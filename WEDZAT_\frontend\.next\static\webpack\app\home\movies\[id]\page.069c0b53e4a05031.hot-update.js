"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./components/VideoInteractionBar.tsx":
/*!********************************************!*\
  !*** ./components/VideoInteractionBar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MdFavorite,MdFavoriteBorder!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst VideoInteractionBar = (param)=>{\n    let { username, uploadDate, viewCount, description, userId, videoId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isCreatingChat, setIsCreatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiring, setIsAdmiring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiringLoading, setIsAdmiringLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiking, setIsLiking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Format view count\n    const formatViewCount = (count)=>{\n        if (count >= 1000000) {\n            return \"\".concat((count / 1000000).toFixed(1), \"M\");\n        } else if (count >= 1000) {\n            return \"\".concat((count / 1000).toFixed(1), \"K\");\n        }\n        return count.toString();\n    };\n    // Handle message button click\n    const handleMessageClick = async ()=>{\n        if (isCreatingChat) return;\n        try {\n            setIsCreatingChat(true);\n            // Get token from localStorage - use userToken as in the chat page\n            const token = localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n            if (!token) {\n                console.error(\"No authentication token found\");\n                alert(\"Please log in to send messages\");\n                setIsCreatingChat(false);\n                return;\n            }\n            // Use the hardcoded URL from the messages page\n            const apiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\";\n            // If userId is 'default' or missing, use a fallback ID for testing\n            // In a real app, you would handle this differently\n            const participantId = !userId || userId === \"default\" ? username.toLowerCase().replace(/\\s+/g, \"_\") + \"_id\" : userId;\n            console.log(\"Creating conversation with user ID: \".concat(participantId));\n            console.log(\"Using API URL: \".concat(apiUrl, \"/conversations\"));\n            console.log(\"Authorization token: \".concat(token.substring(0, 10), \"...\"));\n            try {\n                // Create or get existing conversation\n                console.log(\"Request payload:\", JSON.stringify({\n                    participants: [\n                        participantId\n                    ]\n                }));\n                const response = await fetch(\"\".concat(apiUrl, \"/conversations\"), {\n                    method: \"POST\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        participants: [\n                            participantId\n                        ]\n                    }),\n                    // Add these options to help with CORS issues\n                    mode: \"cors\",\n                    credentials: \"same-origin\"\n                });\n                console.log(\"Response status:\", response.status);\n                console.log(\"Response headers:\", [\n                    ...response.headers.entries()\n                ]);\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"Conversation created/retrieved:\", data);\n                // Handle different response formats\n                let conversationId = null;\n                // Check if the response has a direct conversation_id property\n                if (data.conversation_id) {\n                    conversationId = data.conversation_id;\n                } else if (data.body && typeof data.body === \"string\") {\n                    try {\n                        const bodyData = JSON.parse(data.body);\n                        if (bodyData.conversation_id) {\n                            conversationId = bodyData.conversation_id;\n                            console.log(\"Found conversation ID in body:\", conversationId);\n                        }\n                    } catch (e) {\n                        console.error(\"Error parsing body data:\", e);\n                    }\n                }\n                if (!conversationId) {\n                    console.error(\"No conversation ID found in any format\", data);\n                    throw new Error(\"Invalid response from server\");\n                }\n                // Navigate to the chat page\n                router.push(\"/messages/\".concat(conversationId));\n            } catch (innerError) {\n                console.error(\"Inner fetch error:\", innerError);\n                throw innerError;\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            // Provide more specific error messages based on the error\n            if (error instanceof TypeError && error.message.includes(\"Failed to fetch\")) {\n                alert(\"Network error: Please check your internet connection and try again.\");\n            } else if (error instanceof Error && error.message.includes(\"401\")) {\n                alert(\"Authentication error: Please log in again.\");\n            } else if (error instanceof Error && error.message.includes(\"403\")) {\n                alert(\"Permission denied: You don't have permission to message this user.\");\n            } else {\n                alert(\"Failed to start conversation. Please try again.\");\n            }\n        } finally{\n            setIsCreatingChat(false);\n        }\n    };\n    // Function to navigate to user profile\n    const navigateToUserProfile = ()=>{\n        if (userId) {\n            // Get the token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Navigate to the profile page with the userId\n            router.push(\"/profile/\".concat(userId));\n        }\n    };\n    // Function to handle like/unlike\n    const handleLikeToggle = async ()=>{\n        if (isLiking || !videoId) return;\n        try {\n            setIsLiking(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to like this video');\n                setIsLiking(false);\n                return;\n            }\n            // Optimistically update UI\n            const newLikedState = !isLiked;\n            setIsLiked(newLikedState);\n            // Make API call to Next.js API routes\n            const endpoint = isLiked ? '/api/unlike' : '/api/like';\n            console.log(\"Making API call to: \".concat(endpoint, \" for video: \").concat(videoId));\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: videoId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const responseData = await response.json();\n            console.log('API Success Response:', responseData);\n            // Update user-specific localStorage to persist like status\n            try {\n                // Get current user ID\n                const getCurrentUserId = ()=>{\n                    const userDataStr = localStorage.getItem('userData') || localStorage.getItem('user') || localStorage.getItem('currentUser');\n                    if (userDataStr) {\n                        try {\n                            const userData = JSON.parse(userDataStr);\n                            return userData.user_id || userData.id || userData.userId;\n                        } catch (e) {\n                            console.warn('🎬 Failed to parse user data in handleLikeToggle');\n                        }\n                    }\n                    const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                    if (token) {\n                        try {\n                            const payload = JSON.parse(atob(token.split('.')[1]));\n                            return payload.user_id || payload.id || payload.sub;\n                        } catch (e) {\n                            console.warn('🎬 Failed to decode token in handleLikeToggle');\n                        }\n                    }\n                    return 'anonymous';\n                };\n                const currentUserId = getCurrentUserId();\n                // Determine if this is a glimpse or movie based on current page or content type\n                const currentPath = window.location.pathname;\n                const isGlimpse = currentPath.includes('/glimpses');\n                const storageKey = isGlimpse ? \"likedGlimpses_\".concat(currentUserId) : \"likedMovies_\".concat(currentUserId);\n                const likedData = localStorage.getItem(storageKey);\n                const likedArray = likedData ? JSON.parse(likedData) : [];\n                if (isLiked) {\n                    // Remove from liked items\n                    const updatedLiked = likedArray.filter((id)=>id !== videoId);\n                    localStorage.setItem(storageKey, JSON.stringify(updatedLiked));\n                } else {\n                    // Add to liked items\n                    if (!likedArray.includes(videoId)) {\n                        likedArray.push(videoId);\n                        localStorage.setItem(storageKey, JSON.stringify(likedArray));\n                    }\n                }\n                console.log(\"Updated user-specific \".concat(storageKey, \" in localStorage for user:\"), currentUserId);\n            } catch (storageError) {\n                console.error('Error updating user-specific localStorage:', storageError);\n            }\n            console.log(\"\".concat(isLiked ? 'Unliked' : 'Liked', \" video: \").concat(videoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setIsLiked(!isLiked);\n        } finally{\n            setIsLiking(false);\n        }\n    };\n    // Function to handle admire/unadmire\n    const handleAdmireToggle = async ()=>{\n        if (isAdmiringLoading || !userId) return;\n        try {\n            setIsAdmiringLoading(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to admire this user');\n                setIsAdmiringLoading(false);\n                return;\n            }\n            // Optimistically update UI state immediately for better user experience\n            const newAdmiringState = !isAdmiring;\n            setIsAdmiring(newAdmiringState);\n            // Make API call to follow/unfollow user\n            const endpoint = isAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n            console.log(\"Making request to \".concat(endpoint, \" with user ID: \").concat(userId));\n            try {\n                // Make the API call\n                const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(endpoint, {\n                    target_user_id: userId\n                }, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('API response:', response.data);\n                console.log(\"Successfully \".concat(isAdmiring ? 'unadmired' : 'admired', \" user\"));\n                // Update localStorage with the new state\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    let admiredUsers = JSON.parse(admiredUsersJson);\n                    if (newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                    console.log('Updated admired users in localStorage:', admiredUsers);\n                    // Force a refresh of the following list to ensure it's up to date\n                    const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        }\n                    });\n                    console.log('Updated following list:', followingResponse.data);\n                    // Double-check our state is correct\n                    if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                        const isActuallyFollowing = followingResponse.data.following.some((user)=>user.user_id === userId || user === userId);\n                        if (isActuallyFollowing !== newAdmiringState) {\n                            console.log('State mismatch detected, correcting...');\n                            setIsAdmiring(isActuallyFollowing);\n                            // Update localStorage again with the correct state\n                            admiredUsers = JSON.parse(localStorage.getItem('admiredUsers') || '{}');\n                            if (isActuallyFollowing) {\n                                admiredUsers[userId] = true;\n                            } else {\n                                delete admiredUsers[userId];\n                            }\n                            localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                        }\n                    }\n                } catch (storageError) {\n                    console.error('Error updating localStorage:', storageError);\n                }\n            } catch (apiError) {\n                console.error(\"Error \".concat(isAdmiring ? 'unadmiring' : 'admiring', \" user:\"), apiError);\n                if (apiError.response) {\n                    console.log('Error response data:', apiError.response.data);\n                    console.log('Error response status:', apiError.response.status);\n                    // If the error is that the user is already following/not following, the UI state is already correct\n                    if (apiError.response.status === 400 && apiError.response.data && (apiError.response.data.error === \"Already following this user\" || apiError.response.data.error === \"Not following this user\")) {\n                        console.log('Already in desired state, keeping UI updated');\n                        return;\n                    }\n                }\n                // If there was an error that wasn't just \"already in desired state\", revert the UI\n                setIsAdmiring(!newAdmiringState);\n                // Also update localStorage to match\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (!newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                } catch (storageError) {\n                    console.error('Error updating localStorage after API error:', storageError);\n                }\n            }\n        } catch (error) {\n            console.error('Unexpected error in handleAdmireToggle:', error);\n            // Revert UI state on unexpected errors\n            setIsAdmiring(!isAdmiring);\n        } finally{\n            setIsAdmiringLoading(false);\n        }\n    };\n    // Initialize like status from user-specific localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!videoId) return;\n            try {\n                // Get current user ID\n                const getCurrentUserId = {\n                    \"VideoInteractionBar.useEffect.getCurrentUserId\": ()=>{\n                        const userDataStr = localStorage.getItem('userData') || localStorage.getItem('user') || localStorage.getItem('currentUser');\n                        if (userDataStr) {\n                            try {\n                                const userData = JSON.parse(userDataStr);\n                                return userData.user_id || userData.id || userData.userId;\n                            } catch (e) {\n                                console.warn('🎬 Failed to parse user data in VideoInteractionBar');\n                            }\n                        }\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                        if (token) {\n                            try {\n                                const payload = JSON.parse(atob(token.split('.')[1]));\n                                return payload.user_id || payload.id || payload.sub;\n                            } catch (e) {\n                                console.warn('🎬 Failed to decode token in VideoInteractionBar');\n                            }\n                        }\n                        return 'anonymous';\n                    }\n                }[\"VideoInteractionBar.useEffect.getCurrentUserId\"];\n                const currentUserId = getCurrentUserId();\n                // Check if this video is liked in user-specific localStorage\n                const likedGlimpsesData = localStorage.getItem(\"likedGlimpses_\".concat(currentUserId));\n                const likedMoviesData = localStorage.getItem(\"likedMovies_\".concat(currentUserId));\n                let isVideoLiked = false;\n                // Check glimpses\n                if (likedGlimpsesData) {\n                    const likedGlimpses = JSON.parse(likedGlimpsesData);\n                    if (Array.isArray(likedGlimpses) && likedGlimpses.includes(videoId)) {\n                        isVideoLiked = true;\n                    }\n                }\n                // Check movies\n                if (!isVideoLiked && likedMoviesData) {\n                    const likedMovies = JSON.parse(likedMoviesData);\n                    if (Array.isArray(likedMovies) && likedMovies.includes(videoId)) {\n                        isVideoLiked = true;\n                    }\n                }\n                setIsLiked(isVideoLiked);\n                console.log(\"\\uD83C\\uDFAC VideoInteractionBar: Video \".concat(videoId, \" like status for user \").concat(currentUserId, \":\"), isVideoLiked);\n            } catch (error) {\n                console.error('🎬 Error loading user-specific like status for VideoInteractionBar:', error);\n                setIsLiked(false);\n            }\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        videoId\n    ]);\n    // Check if user is already admiring on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!userId) return;\n            // First check localStorage for cached admiring state\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers');\n                if (admiredUsersJson) {\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (admiredUsers[userId]) {\n                        console.log('Found admiring status in localStorage:', true);\n                        setIsAdmiring(true);\n                    // Continue with API check to verify\n                    }\n                }\n            } catch (storageError) {\n                console.error('Error reading from localStorage:', storageError);\n            }\n            const checkAdmiringStatus = {\n                \"VideoInteractionBar.useEffect.checkAdmiringStatus\": async ()=>{\n                    console.log('Checking admiring status for user ID:', userId);\n                    // Get token from localStorage\n                    const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                    if (!token) {\n                        console.warn('No authentication token found');\n                        return;\n                    }\n                    // Check if this is the current user's content - no need to check admiring status\n                    try {\n                        const tokenParts = token.split('.');\n                        if (tokenParts.length === 3) {\n                            const payload = JSON.parse(atob(tokenParts[1]));\n                            if (payload.user_id === userId) {\n                                console.log('This is the current user\\'s content, skipping admiring status check');\n                                setIsAdmiring(false); // User can't admire themselves\n                                return;\n                            }\n                        }\n                    } catch (tokenError) {\n                        console.log('Could not decode token to check current user, proceeding with API calls');\n                    }\n                    try {\n                        // Direct API call to check following status - most reliable method\n                        const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('Following response:', followingResponse.data);\n                        if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                            // Check if userId is in the following array\n                            const isFollowing = followingResponse.data.following.some({\n                                \"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\": (user)=>user.user_id === userId || user === userId\n                            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\"]);\n                            console.log(\"User \".concat(isFollowing ? 'is' : 'is not', \" in following list:\"), userId);\n                            setIsAdmiring(isFollowing);\n                            // Update localStorage for future reference\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (isFollowing) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                            return;\n                        }\n                    } catch (followingError) {\n                        console.log('Error fetching following list, trying alternative method');\n                    }\n                    // Fallback: Try to get user profile\n                    try {\n                        const profileResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-profile?user_id=\".concat(userId), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('User profile response:', profileResponse.data);\n                        if (profileResponse.data && profileResponse.data.is_following !== undefined) {\n                            console.log('Setting admiring status from profile:', profileResponse.data.is_following);\n                            setIsAdmiring(profileResponse.data.is_following);\n                            // Update localStorage\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (profileResponse.data.is_following) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                        }\n                    } catch (profileError) {\n                        console.log('Error fetching user profile, trying another method');\n                        // Last resort: Try the user-follow-stats endpoint\n                        try {\n                            const statsResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-follow-stats?target_user_id=\".concat(userId), {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token),\n                                    'Content-Type': 'application/json'\n                                }\n                            });\n                            console.log('User follow stats response:', statsResponse.data);\n                            if (statsResponse.data && statsResponse.data.is_following !== undefined) {\n                                setIsAdmiring(statsResponse.data.is_following);\n                                // Update localStorage\n                                try {\n                                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                    const admiredUsers = JSON.parse(admiredUsersJson);\n                                    if (statsResponse.data.is_following) {\n                                        admiredUsers[userId] = true;\n                                    } else {\n                                        delete admiredUsers[userId];\n                                    }\n                                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                } catch (storageError) {\n                                    console.error('Error updating localStorage:', storageError);\n                                }\n                            }\n                        } catch (statsError) {\n                            console.log('All API methods failed to check admiring status, using localStorage fallback');\n                            // Fallback to localStorage if all API calls fail\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                const isAdmiringFromStorage = admiredUsers[userId] === true;\n                                setIsAdmiring(isAdmiringFromStorage);\n                                console.log(\"Using localStorage fallback: \".concat(isAdmiringFromStorage ? 'admiring' : 'not admiring'));\n                            } catch (storageError) {\n                                console.log('localStorage fallback also failed, defaulting to not admiring');\n                                setIsAdmiring(false);\n                            }\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus\"];\n            // Call the API check function\n            checkAdmiringStatus();\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        userId\n    ]);\n    // Add click outside handler to close menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"VideoInteractionBar.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.menu-container')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"VideoInteractionBar.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"VideoInteractionBar.useEffect\"];\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        showMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-white p-2 sm:p-3 md:p-4 rounded-b-xl border border-gray-200 border-t-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 sm:space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLikeToggle,\n                                    disabled: isLiking,\n                                    className: \"flex items-center justify-center rounded-full p-1.5 sm:p-2 transition-colors \".concat(isLiked ? 'bg-red-100 hover:bg-red-200' : 'bg-gray-100 hover:bg-gray-200'),\n                                    title: isLiked ? 'Unlike' : 'Like',\n                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdFavorite, {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        color: \"#B31B1E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdFavoriteBorder, {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        color: \"#666\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    onClick: handleMessageClick,\n                                    disabled: isCreatingChat,\n                                    title: \"Send a message\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    onClick: ()=>{\n                                        if (videoId) {\n                                            // Determine the content type based on current URL\n                                            const currentPath = window.location.pathname;\n                                            let shareUrl = '';\n                                            if (currentPath.includes('/glimpses/')) {\n                                                shareUrl = \"\".concat(window.location.origin, \"/home/<USER>/\").concat(videoId);\n                                            } else if (currentPath.includes('/movies/')) {\n                                                shareUrl = \"\".concat(window.location.origin, \"/home/<USER>/\").concat(videoId);\n                                            } else {\n                                                shareUrl = window.location.href; // fallback to current URL\n                                            }\n                                            navigator.clipboard.writeText(shareUrl).then(()=>{\n                                                console.log('Video link copied to clipboard:', shareUrl);\n                                                alert('Video link copied to clipboard!');\n                                            }).catch((err)=>{\n                                                console.error('Failed to copy video link:', err);\n                                                alert('Failed to copy link');\n                                            });\n                                        }\n                                    },\n                                    title: \"Share Video\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 716,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: formatViewCount(viewCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"views\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative menu-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMenu(!showMenu),\n                                        className: \"p-1.5 rounded-full hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 18,\n                                            className: \"text-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 778,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: \"Report content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 715,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"cursor-pointer\",\n                                        onClick: navigateToUserProfile,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            username: username || \"Anonymous\",\n                                            size: \"md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-black cursor-pointer hover:underline\",\n                                                onClick: navigateToUserProfile,\n                                                children: username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Uploaded \",\n                                                    uploadDate || 'recently'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 800,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMessageClick,\n                                        disabled: isCreatingChat,\n                                        className: \"flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-full text-sm transition-colors\",\n                                        title: \"Send a message\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isCreatingChat ? \"Opening...\" : \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAdmireToggle,\n                                        disabled: isAdmiringLoading,\n                                        className: \"flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors bg-[#B31B1E] text-white hover:bg-red-700\",\n                                        title: isAdmiring ? \"Admiring\" : \"Admire this user\",\n                                        children: isAdmiringLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                isAdmiring ? 'Admiring' : 'Admire',\n                                                !isAdmiring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 35\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 826,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 799,\n                        columnNumber: 9\n                    }, undefined),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black mt-2 text-xs sm:text-sm line-clamp-3 sm:line-clamp-none\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 845,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 798,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n        lineNumber: 714,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoInteractionBar, \"Ub7IOFbhXSoy7hmqI9MF3vyUtUI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = VideoInteractionBar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VideoInteractionBar);\nvar _c;\n$RefreshReg$(_c, \"VideoInteractionBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/VideoInteractionBar.tsx\n"));

/***/ })

});