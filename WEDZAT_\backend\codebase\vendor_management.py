import os
import json
import uuid
import bcrypt
import jwt
from datetime import datetime, timezone, timedelta, date
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database and JWT configuration
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
JWT_SECRET = os.getenv('JWT_SECRET')



def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME,
        cursor_factory=RealDictCursor
    )

# Helper function to make objects JSON serializable
def make_serializable(obj):
    """Convert non-serializable objects to serializable types"""
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, uuid.UUID):
        return str(obj)
    elif isinstance(obj, dict):
        return {k: make_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_serializable(item) for item in obj]
    else:
        return obj

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        token = token.split()[1]
        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

# Create vendor tables if they don't exist
def create_vendor_tables():
    """Create all tables needed for the vendor management"""
    conn = None
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            port=DB_PORT,
            dbname=DB_NAME
        )
        cursor = conn.cursor()

        print("Starting to create vendor tables...")

        # Business Types Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS business_types (
            type_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Business Subtypes Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS business_subtypes (
            subtype_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            business_type_id UUID REFERENCES business_types(type_id) ON DELETE CASCADE,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(business_type_id, name)
        );
        ''')

        # Vendor Profiles Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendor_profiles (
            profile_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE UNIQUE,
            business_name VARCHAR(255) NOT NULL,
            primary_business_type UUID REFERENCES business_types(type_id) NOT NULL,
            secondary_business_types JSONB DEFAULT '[]',
            business_registration_type VARCHAR(50) CHECK (business_registration_type IN ('company', 'freelancer')),
            gst_registered BOOLEAN DEFAULT FALSE,
            gst_number VARCHAR(100),
            business_address TEXT,
            city VARCHAR(100) NOT NULL,
            state VARCHAR(100),
            pin_code VARCHAR(20),
            contact_name VARCHAR(255),
            mobile_number VARCHAR(15) NOT NULL,
            email VARCHAR(100) NOT NULL,
            is_email_verified BOOLEAN DEFAULT FALSE,
            is_phone_verified BOOLEAN DEFAULT FALSE,
            verification_status VARCHAR(50) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Vendor Verification Documents Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendor_verification_documents (
            document_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            vendor_profile_id UUID REFERENCES vendor_profiles(profile_id) ON DELETE CASCADE,
            document_type VARCHAR(100) NOT NULL,
            document_url TEXT NOT NULL,
            verification_status VARCHAR(50) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Vendor Bank Details Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendor_bank_details (
            bank_detail_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            vendor_profile_id UUID REFERENCES vendor_profiles(profile_id) ON DELETE CASCADE UNIQUE,
            bank_name VARCHAR(255) NOT NULL,
            account_holder_name VARCHAR(255) NOT NULL,
            account_number VARCHAR(100) NOT NULL,
            ifsc_code VARCHAR(50) NOT NULL,
            upi_id VARCHAR(100),
            is_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Vendor Verification Codes Table (for OTP and email verification)
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendor_verification_codes (
            code_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            code_type VARCHAR(50) NOT NULL CHECK (code_type IN ('email', 'phone')),
            code VARCHAR(10) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            is_used BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Check if business types exist
        cursor.execute('SELECT COUNT(*) FROM business_types')
        type_count = cursor.fetchone()[0]

        # Insert default business types if they don't exist
        if type_count == 0:
            # Define all business types
            business_types = [
                "Venues",
                "Photography",
                "Videographer",
                "Makeup Artist",
                "Decoration",
                "Caterers",
                "Mehendi",
                "Bridal Outfit",
                "Groom Outfit",
                "Invitations",
                "Music & Dance",
                "Pandits",
                "Jewelry",
                "Dermatologist",
                "Esthetic Dentist",
                "Beauty Grooming",
                "Event Planners",
                "Transportation",
                "Hospitality",
                "Gifts",
                "Astrologers",
                "Honeymoon Planners",
                "Bridal Shower",
                "Baby Shower",
                "Bachelor Party",
                "Wedding Planner",
                "Others"
            ]

            # Dictionary to store business type IDs
            business_type_ids = {}

            for business_type in business_types:
                type_id = str(uuid.uuid4())
                cursor.execute(
                    '''INSERT INTO business_types (type_id, name)
                    VALUES (%s, %s) RETURNING type_id''',
                    (type_id, business_type)
                )
                business_type_ids[business_type] = type_id

            print(f"Inserted {len(business_types)} default business types")

            # Define subtypes for each business type
            business_subtypes = {
                "Venues": [
                    "Kalyana Mandapam",
                    "Banquet Halls",
                    "Small Party/Engagement Halls",
                    "Outdoor Lawn/Garden",
                    "Resort",
                    "4-Star/5-Star Hotel",
                    "Destination Weddings"
                ],
                "Photography": [
                    "Wedding Photography",
                    "Pre-wedding Shoots",
                    "Candid Photography",
                    "Drone Photography",
                    "Traditional Photography"
                ],
                "Videographer": [
                    "Wedding Videography",
                    "Pre-wedding Videos",
                    "Drone Videography",
                    "Traditional Videography",
                    "Cinematic Videography"
                ],
                "Makeup Artist": [
                    "Bridal Makeup",
                    "Party Makeup",
                    "Celebrity Makeup Artist",
                    "Hair Styling",
                    "Nail Art"
                ],
                "Decoration": [
                    "Wedding Decoration",
                    "Stage Decoration",
                    "Floral Decoration",
                    "Lighting Decoration",
                    "Entrance Decoration"
                ],
                "Caterers": [
                    "Vegetarian",
                    "Non-Vegetarian",
                    "Multi-Cuisine",
                    "Regional Cuisine",
                    "International Cuisine"
                ]
            }

            # Insert subtypes
            for business_type, subtypes in business_subtypes.items():
                if business_type in business_type_ids:
                    type_id = business_type_ids[business_type]
                    for subtype in subtypes:
                        cursor.execute(
                            '''INSERT INTO business_subtypes (business_type_id, name)
                            VALUES (%s, %s)''',
                            (type_id, subtype)
                        )
                    print(f"Inserted {len(subtypes)} subtypes for {business_type}")

        # Vendor Services Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendor_services (
            service_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            vendor_profile_id UUID REFERENCES vendor_profiles(profile_id) ON DELETE CASCADE,
            business_type_id UUID REFERENCES business_types(type_id),
            business_subtype_id UUID REFERENCES business_subtypes(subtype_id),
            service_name VARCHAR(255) NOT NULL,
            description TEXT,
            price_range VARCHAR(100),
            location VARCHAR(255),
            city VARCHAR(100),
            state VARCHAR(100),
            pin_code VARCHAR(20),
            capacity INT,
            amenities JSONB DEFAULT '[]',
            images JSONB DEFAULT '[]',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Create indexes for better performance
        cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_vendor_profiles_user_id ON vendor_profiles(user_id);
        CREATE INDEX IF NOT EXISTS idx_vendor_profiles_primary_business_type ON vendor_profiles(primary_business_type);
        CREATE INDEX IF NOT EXISTS idx_business_subtypes_business_type_id ON business_subtypes(business_type_id);
        CREATE INDEX IF NOT EXISTS idx_vendor_services_vendor_profile_id ON vendor_services(vendor_profile_id);
        CREATE INDEX IF NOT EXISTS idx_vendor_services_business_type_id ON vendor_services(business_type_id);
        CREATE INDEX IF NOT EXISTS idx_vendor_services_business_subtype_id ON vendor_services(business_subtype_id);
        ''')

        # Commit the changes
        conn.commit()
        print("Vendor tables created successfully!")
        return True
    except Exception as e:
        # Roll back any failed transactions
        if conn:
            conn.rollback()
        print(f"Database error: {e}")
        return False
    finally:
        # Close connection
        if conn:
            conn.close()


def register_vendor(event):
    """Register a new vendor with all required fields"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        data = json.loads(event['body'])
        name = data.get('name')  # Business name
        email = data.get('email')
        password = data.get('password')
        mobile_number = data.get('mobile_number')
        place = data.get('place')  # City
        primary_business_type = data.get('primary_business_type')
        secondary_business_types = data.get('secondary_business_types', [])

        # Additional fields
        business_registration_type = data.get('business_registration_type')
        gst_registered = data.get('gst_registered', False)
        gst_number = data.get('gst_number')
        business_address = data.get('business_address')
        state = data.get('state')
        pin_code = data.get('pin_code')
        contact_name = data.get('contact_name')

        # Validate required fields
        if not (name and email and password and mobile_number and place and primary_business_type):
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Missing required fields"})
            }

        # Validate secondary business types (max 6)
        if len(secondary_business_types) > 6:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Maximum 6 secondary business types allowed"})
            }

        # Check if email already exists
        cursor.execute('SELECT user_id FROM users WHERE LOWER(email) = LOWER(%s)', (email,))
        existing_email = cursor.fetchone()
        if existing_email:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Email already exists"})
            }

        # Check if mobile number already exists
        if mobile_number:
            cursor.execute('SELECT user_id FROM users WHERE mobile_number = %s', (mobile_number,))
            existing_mobile = cursor.fetchone()
            if existing_mobile:
                return {
                    'statusCode': 400,
                    "headers": {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Mobile number already exists"})
                }

        # Hash the password
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        # Begin transaction
        cursor.execute("BEGIN")

        try:
            # Create user
            cursor.execute(
                '''INSERT INTO users (name, email, password, mobile_number, place, user_type, face_verified, face_image_url)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s) RETURNING user_id''',
                (name, email, hashed_password, mobile_number, place, 'vendor', False, None)
            )
            user_id = cursor.fetchone()['user_id']

            # Verify primary business type exists
            cursor.execute('SELECT type_id FROM business_types WHERE type_id = %s', (primary_business_type,))
            if not cursor.fetchone():
                cursor.execute("ROLLBACK")
                return {
                    'statusCode': 400,
                    "headers": {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Invalid primary business type"})
                }

            # Verify secondary business types exist
            if secondary_business_types:
                placeholders = ', '.join(['%s'] * len(secondary_business_types))
                cursor.execute(f'SELECT COUNT(*) FROM business_types WHERE type_id IN ({placeholders})', secondary_business_types)
                count = cursor.fetchone()['count']
                if count != len(secondary_business_types):
                    cursor.execute("ROLLBACK")
                    return {
                        'statusCode': 400,
                        "headers": {
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Methods": "OPTIONS, POST",
                            "Access-Control-Allow-Headers": "Content-Type, Authorization"
                        },
                        'body': json.dumps({"error": "One or more invalid secondary business types"})
                    }

            # Create vendor profile
            cursor.execute(
                '''INSERT INTO vendor_profiles
                (user_id, business_name, primary_business_type, secondary_business_types,
                business_registration_type, gst_registered, gst_number, business_address,
                city, state, pin_code, contact_name, mobile_number, email)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) RETURNING profile_id''',
                (user_id, name, primary_business_type, json.dumps(secondary_business_types),
                business_registration_type, gst_registered, gst_number, business_address,
                place, state, pin_code, contact_name, mobile_number, email)
            )
            profile_id = cursor.fetchone()['profile_id']

            # Commit transaction
            cursor.execute("COMMIT")

            # Generate JWT token
            token = jwt.encode(
                {'user_id': str(user_id), 'exp': datetime.now(timezone.utc) + timedelta(days=1)},
                JWT_SECRET,
                algorithm='HS256'
            )

            return {
                'statusCode': 201,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "token": token,
                    "user_id": user_id,
                    "profile_id": profile_id,
                    "message": "Vendor registered successfully. Please verify your email and phone."
                })
            }
        except Exception as e:
            cursor.execute("ROLLBACK")
            raise e

    except Exception as e:
        print(f"Error in register_vendor: {str(e)}")
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            conn.close()



def get_business_types(event):
    """Get all available business types"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute(
            '''SELECT type_id, name, description FROM business_types ORDER BY name'''
        )

        business_types = cursor.fetchall()

        return {
            'statusCode': 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"business_types": business_types})
        }
    except Exception as e:
        print(f"Error in get_business_types: {str(e)}")
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_business_subtypes(event):
    """Get all subtypes for a specific business type"""
    try:
        # Get business type ID from query parameters
        query_params = event.get('queryStringParameters', {})
        business_type_id = query_params.get('business_type_id')

        if not business_type_id:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Business type ID is required"})
            }

        conn = get_db_connection()
        cursor = conn.cursor()

        # Get business type name
        cursor.execute(
            '''SELECT name FROM business_types WHERE type_id = %s''',
            (business_type_id,)
        )
        business_type = cursor.fetchone()

        if not business_type:
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Business type not found"})
            }

        # Get subtypes for the business type
        cursor.execute(
            '''SELECT subtype_id, name, description FROM business_subtypes
            WHERE business_type_id = %s ORDER BY name''',
            (business_type_id,)
        )

        subtypes_raw = cursor.fetchall()

        # Convert RealDictRow objects to regular dictionaries and make them serializable
        subtypes = []
        for subtype in subtypes_raw:
            subtype_dict = dict(subtype)
            # Convert UUID to string
            subtype_dict['subtype_id'] = str(subtype_dict['subtype_id'])
            subtypes.append(subtype_dict)

        response_data = {
            "business_type": business_type['name'],
            "subtypes": subtypes
        }

        return {
            'statusCode': 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps(response_data)
        }
    except Exception as e:
        print(f"Error in get_business_subtypes: {str(e)}")
        import traceback
        traceback.print_exc()

        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "error": "Internal server error",
                "message": str(e)
            })
        }
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def add_vendor_service(event):
    """Add a new service for a vendor"""
    try:
        # Validate token
        user_id, error_response = validate_token(event.get('headers', {}))
        if error_response:
            return error_response

        # Check if the request is multipart/form-data (contains file uploads)
        content_type = event.get('headers', {}).get('Content-Type', '')
        is_multipart = content_type and 'multipart/form-data' in content_type

        if is_multipart:
            # Handle multipart form data with file uploads
            # This would require additional processing for the multipart data
            # For AWS Lambda, you would need to use a library like python-lambda-multipart-parser
            # For simplicity, we'll assume the frontend sends base64 encoded images in the JSON payload
            print("Multipart form data detected, but using JSON processing")

        # Parse the request body
        data = json.loads(event['body'])
        business_type_id = data.get('business_type_id')
        business_subtype_name = data.get('business_subtype_name')  # Get the subtype name directly
        service_name = data.get('service_name')
        description = data.get('description')
        price_range = data.get('price_range')
        location = data.get('location')
        city = data.get('city')
        state = data.get('state')
        pin_code = data.get('pin_code')
        capacity = data.get('capacity')
        amenities = data.get('amenities', [])

        # Handle images - can be URLs or base64 encoded images
        images = data.get('images', [])

        # Process any base64 images if present
        base64_images = data.get('base64_images', [])
        if base64_images:
            # In a real implementation, you would:
            # 1. Decode the base64 data
            # 2. Upload to S3 or your storage service
            # 3. Get the URLs and add them to the images array
            # For this example, we'll just add a placeholder note
            for i, img_data in enumerate(base64_images):
                # In a real implementation, replace this with actual S3 upload code
                # and use the returned URL instead of this placeholder
                images.append(f"https://example.com/uploaded-image-{i+1}.jpg")

            print(f"Processed {len(base64_images)} base64 images")

        # Validate required fields
        if not (business_type_id and service_name and business_subtype_name):
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Missing required fields"})
            }

        conn = get_db_connection()
        cursor = conn.cursor()

        # Get vendor profile ID
        cursor.execute(
            '''SELECT profile_id FROM vendor_profiles WHERE user_id = %s''',
            (user_id,)
        )
        vendor_profile = cursor.fetchone()

        if not vendor_profile:
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Vendor profile not found"})
            }

        vendor_profile_id = vendor_profile['profile_id']

        # Verify business type exists
        cursor.execute('SELECT type_id, name FROM business_types WHERE type_id = %s', (business_type_id,))
        business_type = cursor.fetchone()
        if not business_type:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Invalid business type"})
            }

        business_type_name = business_type['name']

        # Create a new column for business_subtype_name if it doesn't exist
        try:
            cursor.execute(
                '''ALTER TABLE vendor_services ADD COLUMN IF NOT EXISTS business_subtype_name VARCHAR(100)'''
            )
            conn.commit()
        except Exception as e:
            print(f"Error adding business_subtype_name column: {str(e)}")
            # Continue even if this fails, as the column might already exist

        # Insert the service with the subtype name directly
        cursor.execute(
            '''INSERT INTO vendor_services
            (vendor_profile_id, business_type_id, business_subtype_name, service_name,
            description, price_range, location, city, state, pin_code, capacity, amenities, images)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) RETURNING service_id''',
            (vendor_profile_id, business_type_id, business_subtype_name, service_name,
            description, price_range, location, city, state, pin_code, capacity,
            json.dumps(amenities), json.dumps(images))
        )

        service_id = cursor.fetchone()['service_id']
        conn.commit()

        # Return a response that includes the business type and subtype names for display
        return {
            'statusCode': 201,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "service_id": service_id,
                "business_type_name": business_type_name,
                "business_subtype_name": business_subtype_name,
                "images": images,  # Return the processed images
                "message": "Service added successfully"
            })
        }
    except Exception as e:
        print(f"Error in add_vendor_service: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "error": f"Internal server error: {str(e)}",
                "details": traceback.format_exc()
            })
        }
    finally:
        if 'conn' in locals() and conn:
            conn.close()


def get_vendor_services(event):
    """Get all services for a vendor"""
    try:
        # Validate token
        user_id, error_response = validate_token(event.get('headers', {}))
        if error_response:
            return error_response

        conn = get_db_connection()
        cursor = conn.cursor()

        # Get vendor profile ID
        cursor.execute(
            '''SELECT profile_id FROM vendor_profiles WHERE user_id = %s''',
            (user_id,)
        )
        vendor_profile = cursor.fetchone()

        if not vendor_profile:
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Vendor profile not found"})
            }

        vendor_profile_id = vendor_profile['profile_id']

        # Get all services for the vendor
        cursor.execute(
            '''SELECT vs.*, bt.name as business_type_name
            FROM vendor_services vs
            JOIN business_types bt ON vs.business_type_id = bt.type_id
            WHERE vs.vendor_profile_id = %s
            ORDER BY vs.created_at DESC''',
            (vendor_profile_id,)
        )

        services = cursor.fetchall()

        # Make everything JSON serializable
        services_list = []
        for service in services:
            service_dict = dict(service)

            # Parse JSON fields
            if 'amenities' in service_dict and service_dict['amenities']:
                try:
                    service_dict['amenities'] = json.loads(service_dict['amenities'])
                except:
                    service_dict['amenities'] = []

            if 'images' in service_dict and service_dict['images']:
                try:
                    service_dict['images'] = json.loads(service_dict['images'])
                except:
                    service_dict['images'] = []

            # Add a default business_subtype_name if not present
            if 'business_subtype_name' not in service_dict or not service_dict['business_subtype_name']:
                service_dict['business_subtype_name'] = "General"

            services_list.append(make_serializable(service_dict))

        return {
            'statusCode': 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"services": services_list})
        }
    except Exception as e:
        print(f"Error in get_vendor_services: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "error": f"Internal server error: {str(e)}",
                "details": traceback.format_exc()
            })
        }
    finally:
        if 'conn' in locals() and conn:
            conn.close()


def update_vendor_service(event):
    """Update an existing vendor service"""
    try:
        # Validate token
        user_id, error_response = validate_token(event.get('headers', {}))
        if error_response:
            return error_response

        data = json.loads(event['body'])
        service_id = data.get('service_id')
        business_type_id = data.get('business_type_id')
        business_subtype_id = data.get('business_subtype_id')
        service_name = data.get('service_name')
        description = data.get('description')
        price_range = data.get('price_range')
        location = data.get('location')
        city = data.get('city')
        state = data.get('state')
        pin_code = data.get('pin_code')
        capacity = data.get('capacity')
        amenities = data.get('amenities')
        images = data.get('images')
        is_active = data.get('is_active')

        # Validate required fields
        if not service_id:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, PUT",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Service ID is required"})
            }

        conn = get_db_connection()
        cursor = conn.cursor()

        # Get vendor profile ID
        cursor.execute(
            '''SELECT profile_id FROM vendor_profiles WHERE user_id = %s''',
            (user_id,)
        )
        vendor_profile = cursor.fetchone()

        if not vendor_profile:
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, PUT",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Vendor profile not found"})
            }

        vendor_profile_id = vendor_profile['profile_id']

        # Check if the service exists and belongs to the vendor
        cursor.execute(
            '''SELECT * FROM vendor_services
            WHERE service_id = %s AND vendor_profile_id = %s''',
            (service_id, vendor_profile_id)
        )

        existing_service = cursor.fetchone()
        if not existing_service:
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, PUT",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Service not found or does not belong to this vendor"})
            }

        # Build update query dynamically based on provided fields
        update_fields = []
        params = []

        if business_type_id is not None:
            update_fields.append("business_type_id = %s")
            params.append(business_type_id)

        if business_subtype_id is not None:
            update_fields.append("business_subtype_id = %s")
            params.append(business_subtype_id)

        if service_name is not None:
            update_fields.append("service_name = %s")
            params.append(service_name)

        if description is not None:
            update_fields.append("description = %s")
            params.append(description)

        if price_range is not None:
            update_fields.append("price_range = %s")
            params.append(price_range)

        if location is not None:
            update_fields.append("location = %s")
            params.append(location)

        if city is not None:
            update_fields.append("city = %s")
            params.append(city)

        if state is not None:
            update_fields.append("state = %s")
            params.append(state)

        if pin_code is not None:
            update_fields.append("pin_code = %s")
            params.append(pin_code)

        if capacity is not None:
            update_fields.append("capacity = %s")
            params.append(capacity)

        if amenities is not None:
            update_fields.append("amenities = %s")
            params.append(json.dumps(amenities))

        if images is not None:
            update_fields.append("images = %s")
            params.append(json.dumps(images))

        if is_active is not None:
            update_fields.append("is_active = %s")
            params.append(is_active)

        update_fields.append("updated_at = CURRENT_TIMESTAMP")

        if not update_fields:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, PUT",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "No fields to update"})
            }

        # Add service_id as the last parameter
        params.append(service_id)

        # Execute update query
        query = f"""UPDATE vendor_services
                  SET {', '.join(update_fields)}
                  WHERE service_id = %s
                  RETURNING service_id"""

        cursor.execute(query, params)
        updated_service = cursor.fetchone()
        conn.commit()

        return {
            'statusCode': 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, PUT",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "service_id": updated_service['service_id'],
                "message": "Service updated successfully"
            })
        }
    except Exception as e:
        print(f"Error in update_vendor_service: {str(e)}")
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, PUT",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            conn.close()


def delete_vendor_service(event):
    """Delete a vendor service"""
    try:
        # Validate token
        user_id, error_response = validate_token(event.get('headers', {}))
        if error_response:
            return error_response

        # Get service ID from query parameters
        query_params = event.get('queryStringParameters', {})
        service_id = query_params.get('service_id')

        if not service_id:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, DELETE",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Service ID is required"})
            }

        conn = get_db_connection()
        cursor = conn.cursor()

        # Get vendor profile ID
        cursor.execute(
            '''SELECT profile_id FROM vendor_profiles WHERE user_id = %s''',
            (user_id,)
        )
        vendor_profile = cursor.fetchone()

        if not vendor_profile:
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, DELETE",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Vendor profile not found"})
            }

        vendor_profile_id = vendor_profile['profile_id']

        # Check if the service exists and belongs to the vendor
        cursor.execute(
            '''SELECT * FROM vendor_services
            WHERE service_id = %s AND vendor_profile_id = %s''',
            (service_id, vendor_profile_id)
        )

        existing_service = cursor.fetchone()
        if not existing_service:
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, DELETE",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Service not found or does not belong to this vendor"})
            }

        # Delete the service
        cursor.execute(
            '''DELETE FROM vendor_services WHERE service_id = %s''',
            (service_id,)
        )

        conn.commit()

        return {
            'statusCode': 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, DELETE",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"message": "Service deleted successfully"})
        }
    except Exception as e:
        print(f"Error in delete_vendor_service: {str(e)}")
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, DELETE",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            conn.close()


def get_vendor_profile(event):
    """Get vendor profile details"""
    try:
        # Validate token
        user_id, error_response = validate_token(event.get('headers', {}))
        if error_response:
            print(f"Token validation failed: {error_response}")
            return error_response

        print(f"Fetching vendor profile for user_id: {user_id}")

        conn = get_db_connection()
        cursor = conn.cursor()

        # First check if the user exists
        cursor.execute('SELECT user_id FROM users WHERE user_id = %s', (user_id,))
        user = cursor.fetchone()
        if not user:
            print(f"User not found with ID: {user_id}")
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "User not found"})
            }

        # Get vendor profile
        query = '''SELECT vp.*, bt.name as primary_business_name
            FROM vendor_profiles vp
            JOIN business_types bt ON vp.primary_business_type = bt.type_id
            WHERE vp.user_id = %s'''

        cursor.execute(query, (user_id,))
        profile = cursor.fetchone()

        if not profile:
            return {
                'statusCode': 404,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Vendor profile not found"})
            }

        # Convert profile to a dictionary
        profile_dict = dict(profile)

        # Handle secondary business types
        if profile_dict.get('secondary_business_types'):
            try:
                # If it's already a string, parse it
                if isinstance(profile_dict['secondary_business_types'], str):
                    secondary_ids = json.loads(profile_dict['secondary_business_types'])
                else:
                    # If it's already a list, use it directly
                    secondary_ids = profile_dict['secondary_business_types']

                if secondary_ids:
                    placeholders = ', '.join(['%s'] * len(secondary_ids))
                    cursor.execute(
                        f'''SELECT type_id, name FROM business_types
                        WHERE type_id IN ({placeholders})''',
                        secondary_ids
                    )
                    secondary_types = cursor.fetchall()
                    profile_dict['secondary_business_types_details'] = [
                        dict(type_info) for type_info in secondary_types
                    ]
            except Exception as e:
                print(f"Error processing secondary business types: {str(e)}")
                profile_dict['secondary_business_types_details'] = []

        # Get bank details
        cursor.execute(
            '''SELECT * FROM vendor_bank_details WHERE vendor_profile_id = %s''',
            (profile_dict['profile_id'],)
        )
        bank_details = cursor.fetchone()
        if bank_details:
            profile_dict['bank_details'] = dict(bank_details)

        # Get verification documents
        cursor.execute(
            '''SELECT document_id, document_type, document_url, verification_status
            FROM vendor_verification_documents
            WHERE vendor_profile_id = %s''',
            (profile_dict['profile_id'],)
        )
        documents = cursor.fetchall()
        if documents:
            profile_dict['documents'] = [dict(doc) for doc in documents]

        # Make everything JSON serializable
        serialized_profile = make_serializable(profile_dict)

        return {
            'statusCode': 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"profile": serialized_profile})
        }

    except Exception as e:
        error_message = f"Error in get_vendor_profile: {str(e)}"
        print(error_message)
        import traceback
        traceback.print_exc()

        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "error": "Internal server error",
                "message": str(e),
                "details": error_message
            })
        }
    finally:
        if conn:
            conn.close()    