"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./components/HomeDashboard/Photos.tsx":
/*!*********************************************!*\
  !*** ./components/HomeDashboard/Photos.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Photos = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [photos, setPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [likedPhotos, setLikedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set());\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // Fallback data if API fails - using empty array\n    const getFallbackPhotos = ()=>[];\n    // Function to fetch photos from the API\n    const fetchPhotos = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching photos for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setPhotos([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.photos) {\n                console.log(\"Loaded \".concat(response.data.photos.length, \" photos for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.photos.length > 0) {\n                    console.log('Sample photo data:', response.data.photos[0]);\n                }\n                // Process the response\n                const processedPhotos = response.data.photos.map((photo)=>{\n                    if (!photo.photo_url) {\n                        console.log(\"Photo missing URL: \".concat(photo.photo_id));\n                    }\n                    return photo;\n                });\n                if (pageNumber === 1) {\n                    setPhotos(processedPhotos);\n                } else {\n                    setPhotos((prev)=>[\n                            ...prev,\n                            ...processedPhotos\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load photos - unexpected response format');\n                setPhotos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load photos');\n            setPhotos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of photos as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Photos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial photos load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchPhotos function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for photos page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"Photos.useEffect\": (response)=>{\n                            console.log('API response received for photos page 1');\n                            if (response.data && response.data.photos) {\n                                setPhotos(response.data.photos);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setPhotos([]);\n                                setError('No photos found');\n                            }\n                        }\n                    }[\"Photos.useEffect\"]).catch({\n                        \"Photos.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load photos');\n                            setPhotos([]);\n                        }\n                    }[\"Photos.useEffect\"]).finally({\n                        \"Photos.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"Photos.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"Photos.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"Photos.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchPhotos(nextPage, false);\n                    }\n                }\n            }[\"Photos.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"Photos.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"Photos.useEffect\"];\n        }\n    }[\"Photos.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a photo\n    const getImageSource = (photo)=>{\n        if (photo.photo_url) {\n            return photo.photo_url;\n        }\n        return '/pics/placeholder.svg';\n    };\n    // Toggle like for photos\n    const toggleLike = async (photoId)=>{\n        try {\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedPhotos.has(photoId);\n            setLikedPhotos((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(photoId);\n                } else {\n                    newLiked.add(photoId);\n                }\n                return newLiked;\n            });\n            // Make API call\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(endpoint, {\n                content_id: photoId,\n                content_type: 'photo'\n            }, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" photo: \").concat(photoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedPhotos((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedPhotos.has(photoId)) {\n                    newLiked.delete(photoId);\n                } else {\n                    newLiked.add(photoId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-black\",\n                        children: \"Photos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading photos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying photos load...');\n                            setError(null);\n                            fetchPhotos(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: 'smooth',\n                    WebkitOverflowScrolling: 'touch',\n                    minHeight: photos.length === 0 && !error ? '220px' : 'auto'\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative\",\n                children: [\n                    photos.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No photos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-4 pb-4 flex-nowrap\",\n                        children: [\n                            photos.map((photo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: '250px',\n                                        height: '200px'\n                                    },\n                                    onClick: ()=>{\n                                        window.location.href = \"/home/<USER>/\".concat(photo.photo_id);\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute top-2 left-2 z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                username: photo.user_name || \"user\",\n                                                size: \"sm\",\n                                                isGradientBorder: true,\n                                                onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: getImageSource(photo),\n                                                alt: photo.photo_name || \"Photo\",\n                                                fill: true,\n                                                sizes: \"(max-width: 640px) 250px, 250px\",\n                                                className: \"object-cover\",\n                                                ...index < 4 ? {\n                                                    priority: true\n                                                } : {\n                                                    loading: 'lazy'\n                                                },\n                                                placeholder: \"blur\" // Show blur placeholder while loading\n                                                ,\n                                                blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                ,\n                                                onError: (e)=>{\n                                                    const imgElement = e.target;\n                                                    if (imgElement) {\n                                                        imgElement.src = '/pics/placeholder.svg';\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-sm font-medium truncate\",\n                                                    children: photo.photo_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-xs flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                                            className: \"jsx-83037452c623c470\" + \" \" + \"cursor-pointer hover:underline\",\n                                                            children: photo.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-83037452c623c470\",\n                                                            children: photo.photo_likes ? \"\".concat(photo.photo_likes, \" likes\") : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(photo.photo_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: 'relative',\n                                    // Add debug outline in development\n                                    outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"photos-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[200px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Photos, \"0D7xLpRSc97qwaKU4HSyafwIrS4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = Photos;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Photos);\nvar _c;\n$RefreshReg$(_c, \"Photos\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Photos.tsx\n"));

/***/ })

});