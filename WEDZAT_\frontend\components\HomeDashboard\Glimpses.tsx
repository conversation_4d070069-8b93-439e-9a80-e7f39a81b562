"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import UserAvatar from "./UserAvatar";
import axios from "../../services/axiosConfig";
import { useRouter } from "next/navigation";
import { useLocation } from "../../contexts/LocationContext";

// Define interface for glimpse video items okay
interface GlimpseVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface ApiResponse {
  glimpses: GlimpseVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

interface GlimpsesProps {
  shouldLoad?: boolean;
}

const GlimpsesSection: React.FC<GlimpsesProps> = ({ shouldLoad = false }) => {
  const router = useRouter();
  const { selectedLocation } = useLocation();
  const [glimpses, setGlimpses] = useState<GlimpseVideo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [initialLoadComplete, setInitialLoadComplete] =
    useState<boolean>(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreTriggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string, event?: React.MouseEvent) => {
    // Stop event propagation to prevent triggering parent click events
    if (event) {
      event.stopPropagation();
    }

    // If we have a userId, navigate to that specific user's profile
    if (userId) {
      router.push(`/profile/${userId}`);
    } else if (username) {
      // For now, just use the username as a parameter
      // In a real app, you might want to fetch the user ID first
      router.push(`/profile/${username}`);
    } else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  // Fallback data if API fails - using empty array to force empty state message
  const getFallbackGlimpses = (): GlimpseVideo[] => [];

  // Function to fetch glimpses data
  const fetchGlimpses = async (pageNumber: number, isInitialLoad: boolean) => {
    try {
      // Set appropriate loading state
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      console.log(`Fetching glimpses for page ${pageNumber}...`);

      // Get token from localStorage
      const token = localStorage.getItem("token");

      if (!token) {
        console.warn("No authentication token found");
        setError("Authentication required");
        return;
      }

      // Build API URL with location parameter if selected
      let apiUrl = `/glimpses?page=${pageNumber}&limit=10`;
      if (selectedLocation) {
        apiUrl += `&location=${encodeURIComponent(selectedLocation)}`;
      }

      // Make API request
      const response = await axios.get<ApiResponse>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      // Process the data
      if (response.data && response.data.glimpses) {
        console.log(`Loaded ${response.data.glimpses.length} glimpses for page ${pageNumber}`);

        // Log the first item for debugging
        if (response.data.glimpses.length > 0) {
          console.log("Sample glimpse data:", response.data.glimpses[0]);
        }

        // Process the response
        const processedGlimpses = response.data.glimpses.map((glimpse) => {
          if (!glimpse.video_thumbnail) {
            console.log(`Glimpse missing thumbnail: ${glimpse.video_id}`);
          }
          return glimpse;
        });

        if (pageNumber === 1) {
          setGlimpses(processedGlimpses);
        } else {
          setGlimpses((prev) => [...prev, ...processedGlimpses]);
        }

        setHasMore(response.data.next_page);
        setPage(pageNumber); // Update the current page
        setError(null); // Clear any previous errors
      } else {
        console.warn("Unexpected response format:", response.data);
        setError("Failed to load glimpses - unexpected response format");
        // Don't clear existing glimpses on error for subsequent pages
        if (pageNumber === 1) {
          setGlimpses([]);
        }
      }
    } catch (err) {
      console.error("API request failed:", err);
      setError("Failed to load glimpses");
      // Don't clear existing glimpses on error for subsequent pages
      if (pageNumber === 1) {
        setGlimpses([]);
      }
    } finally {
      // Always clear loading states
      if (isInitialLoad) {
        setLoading(false);
        console.log("Initial loading complete");
      } else {
        setLoadingMore(false);
        console.log("Loading more complete");
      }
    }
  };

  // Fetch first page of glimpses as soon as the component mounts
  useEffect(() => {
    // Only trigger when shouldLoad changes to true
    if (shouldLoad) {
      console.log("Glimpses component is now visible and ready to load data");

      // Set a flag to track initial load
      setInitialLoadComplete(true);

      // IMMEDIATE API request with no conditions or delays
      console.log("Triggering initial glimpses load IMMEDIATELY...");
      setLoading(true);

      // Skip the fetchGlimpses function and make the API call directly here
      // to eliminate any potential delay or condition that might block it
      const token = localStorage.getItem("token");

      if (token) {
        // Build API URL with location parameter if selected
        let apiUrl = `/glimpses?page=1&limit=10`;
        if (selectedLocation) {
          apiUrl += `&location=${encodeURIComponent(selectedLocation)}`;
        }

        // Make direct API request
        console.log("Making direct API request for glimpses page 1...");
        axios
          .get<ApiResponse>(apiUrl, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })
          .then((response) => {
            console.log("API response received for glimpses page 1");
            if (response.data && response.data.glimpses) {
              setGlimpses(response.data.glimpses);
              setHasMore(response.data.next_page);
              setPage(1);
              setError(null);
            } else {
              setGlimpses([]);
              setError("No glimpses found");
            }
          })
          .catch((err) => {
            console.error("Direct API request failed:", err);
            setError("Failed to load glimpses");
            setGlimpses([]);
          })
          .finally(() => {
            setLoading(false);
            console.log("Initial loading complete");
          });
      } else {
        setLoading(false);
        setError("Authentication required");
      }
    }
  }, [shouldLoad]); // Only depend on shouldLoad

  // Failsafe to ensure content is loaded - only show error after timeout
  useEffect(() => {
    // If we're stuck in loading state for more than 5 seconds, show error
    let timeoutId: NodeJS.Timeout | null = null;

    if (loading && initialLoadComplete) {
      timeoutId = setTimeout(() => {
        console.log("Loading timeout reached - API request may have failed");
        setLoading(false);
        if (glimpses.length === 0) {
          setError(
            "Unable to load glimpses. Please check your network connection."
          );
        }
      }, 10000); // Increased timeout to 10 seconds for slow networks
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [loading, initialLoadComplete, glimpses.length]);

  // Setup Intersection Observer for horizontal lazy loading
  useEffect(() => {
    // Only set up observer after initial load and if there's more content to fetch
    if (!initialLoadComplete || !hasMore) return;

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
      console.log("Disconnected previous intersection observer");
    }

    // Create new observer - similar to the pattern used in Flashes and WeddingVideos
    observerRef.current = new IntersectionObserver(
      (entries) => {
        // If the trigger element is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && !loadingMore && hasMore) {
          console.log("Load more trigger is visible, loading next page...");
          console.log("Intersection ratio:", entries[0].intersectionRatio);
          const nextPage = page + 1;
          fetchGlimpses(nextPage, false);
        }
      },
      // Use the scroll container as the root for the intersection observer
      {
        root: scrollContainerRef.current,
        threshold: 0.1, // Trigger when 10% of the element is visible
        rootMargin: "0px 150px 0px 0px", // Add margin to the right to load earlier
      }
    );

    // Start observing the trigger element
    if (loadMoreTriggerRef.current) {
      observerRef.current.observe(loadMoreTriggerRef.current);
      console.log("Now observing load more trigger element");
    } else {
      console.warn("Load more trigger element not found");
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        console.log("Disconnected intersection observer");
      }
    };
  }, [hasMore, loading, loadingMore, page, initialLoadComplete]);

  // Get appropriate image source for a glimpse
  const getImageSource = (glimpse: GlimpseVideo): string => {
    if (glimpse.video_thumbnail) {
      return glimpse.video_thumbnail;
    }

    // If it's a YouTube video, use the YouTube thumbnail
    if (glimpse.video_url && glimpse.video_url.includes('youtube')) {
      const videoId = getYoutubeId(glimpse.video_url);
      if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
      }
    }

    return "/pics/placeholder.svg";
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Try to extract ID from YouTube URL
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : '';
  };

  return (
    <section className="mb-4 bg-white relative">
      <div className="flex items-center  mb-4 px-2">
        <h2 className="font-inter text-[20px] leading-[18px] tracking-[0%] align-middle font-semibold text-[#000000] ">
          Glimpses
        </h2>
        <a
          href="/home/<USER>"
          className="text-red-500 text-sm font-medium hover:underline z-10 relative ml-auto mr-1"
        >
          See all
        </a>
      </div>

      {/* Loading state for initial load */}
      {loading && (
        <div className="py-10 text-center">
          <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          <span className="text-gray-600">Loading glimpses...</span>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="py-10 text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={() => {
              console.log("Retrying glimpses load...");
              setError(null);
              fetchGlimpses(1, true);
            }}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            Retry
          </button>
        </div>
      )}

      {/* Debug info in development only */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-0 right-0 bg-black/50 text-white text-xs p-2 rounded-bl-md z-50 max-w-[200px]">
          <div>State: {loading ? 'Loading' : error ? 'Error' : glimpses.length === 0 ? 'Empty' : 'Loaded'}</div>
          <div>Count: {glimpses.length}</div>
          <div>Page: {page}</div>
          <div>Has more: {hasMore ? 'Yes' : 'No'}</div>
        </div>
      )} */}

      {/* Horizontal scroll container - show only when data is loaded */}
      {!loading && (
        <div
          className="overflow-x-auto scrollbar-hide relative"
          ref={scrollContainerRef}
          style={{
            scrollBehavior: "smooth",
            WebkitOverflowScrolling: "touch", // For smoother scrolling on iOS
            minHeight: glimpses.length === 0 && !error ? "220px" : "auto",
          }}
        >
          {/* Empty state message when no error */}
          {glimpses.length === 0 && !error && !loading && (
            <div className="flex items-center justify-center h-[220px] w-full">
              <div className="text-gray-400">No glimpses available</div>
            </div>
          )}

          <div className="flex gap-3 pb-4 flex-nowrap">
            {/* Video cards */}
            {glimpses.map((glimpse, index) => (
              <div
                key={`${glimpse.video_id}-${index}`}
                className="flex-shrink-0 rounded-[10px] overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer border border-[#4D0C0D] w-[80vw] sm:w-[40vw] md:w-[30vw] lg:w-[22vw] h-[160px] sm:h-[180px] md:h-[200px] lg:h-[220px] max-w-[365px]"
                style={{
                  background:
                    "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100.01%)",
                }}
              >
                <div
                  className="absolute top-4 left-4 flex items-center space-x-2 z-10"
                  onClick={(e) => navigateToUserProfile(glimpse.user_id, glimpse.user_name, e)}
                >
                  <UserAvatar
                    username={glimpse.user_name || "user"}
                    size="sm"
                    isGradientBorder={true}
                    onClick={() => navigateToUserProfile(glimpse.user_id, glimpse.user_name)}
                  />
                  <span
                    className="text-white text-sm font-medium drop-shadow-md cursor-pointer hover:underline"
                    onClick={(e) => navigateToUserProfile(glimpse.user_id, glimpse.user_name, e)}
                  >
                    {glimpse.user_name || "user"}
                  </span>
                </div>

                {/* Video Thumbnail */}
                <div className="h-full relative">
                  <div
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      zIndex: 1,
                    }}
                  >
                    <div className="relative w-full h-full rounded-[4px] overflow-hidden">
                      <Image
                        src={getImageSource(glimpse)}
                        alt={glimpse.video_name || "Glimpse Video"}
                        fill
                        sizes="(max-width: 640px) 85vw, (max-width: 768px) 45vw, (max-width: 1024px) 30vw, 25vw"
                        className="object-cover"
                        {...(index < 2
                          ? { priority: true }
                          : { loading: "lazy" })}
                        unoptimized={true}
                        placeholder="blur"
                        blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg=="
                        onError={(e) => {
                          const imgElement = e.target as HTMLImageElement;
                          if (imgElement) {
                            imgElement.src = "/pics/placeholder.svg";
                          }
                        }}
                      />
                    </div>
                  </div>

                  {/* Play Button Overlay */}
                  <div
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      zIndex: 2,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                      backgroundColor: "rgba(0, 0, 0, 0.2)",
                    }}
                    onClick={() => {
                      if (glimpse.video_id) {
                        window.location.href = `/home/<USER>/${glimpse.video_id}`;
                      }
                    }}
                  >
                    <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="28"
                        height="28"
                        viewBox="0 0 24 24"
                        fill="white"
                      >
                        <path d="M8 5v14l11-7z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="absolute bottom-0 left-0 right-0 p-2 sm:p-3 md:p-4 bg-gradient-to-t from-black/80 to-transparent text-white">
                  <div className="font-medium text-xs sm:text-sm md:text-base line-clamp-1">
                    {glimpse.video_name}
                  </div>
                  <div className="text-[10px] sm:text-xs">
                    {glimpse.video_views
                      ? `${(glimpse.video_views / 1000).toFixed(1)}K views`
                      : ""}
                    {glimpse.video_likes
                      ? ` • ${(glimpse.video_likes / 1000).toFixed(1)}K likes`
                      : ""}
                  </div>
                </div>
              </div>
            ))}

            {/* Load more trigger element - this is what IntersectionObserver watches */}
            {hasMore && (
              <div
                ref={loadMoreTriggerRef}
                className="flex-shrink-0 w-10 h-full opacity-0"
                style={{
                  position: "relative",
                  // Add debug outline in development
                  outline:
                    process.env.NODE_ENV === "development"
                      ? "1px dashed rgba(255, 0, 0, 0.3)"
                      : "none",
                }}
                aria-hidden="true"
                data-testid="glimpses-load-more-trigger"
              />
            )}

            {/* Loading indicator - only show when loading more */}
            {loadingMore && (
              <div className="flex-shrink-0 flex items-center justify-center min-w-[100px] h-[160px] sm:h-[180px] md:h-[200px] lg:h-[220px]">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="ml-2 text-sm text-gray-600">
                  Loading more...
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* No content state */}
      {!loading && glimpses.length === 0 && !error && (
        <div className="py-10 text-center text-gray-500">
          No glimpses available
        </div>
      )}

      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </section>
  );
};

export default GlimpsesSection;
