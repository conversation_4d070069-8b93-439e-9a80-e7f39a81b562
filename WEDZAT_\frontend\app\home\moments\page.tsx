"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";

// Define interface for story items
interface Story {
  content_id: string;
  content_name: string;
  content_url: string;
  content_description?: string;
  thumbnail_url?: string;
  duration?: number;
  created_at: string;
  user_name: string;
  content_type: 'video' | 'photo';
  is_own_content: boolean;
  viewed?: boolean; // Track if the user has viewed this story
}

interface ApiResponse {
  stories: Story[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

export default function MomentsPage() {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  // Removed right sidebar state

  useEffect(() => setIsClient(true), []);

  // Fetch stories from the API
  useEffect(() => {
    const fetchStories = async () => {
      try {
        setLoading(true);
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          console.warn('No authentication token found');
          setError('Authentication required');
          return;
        }

        const response = await axios.get<ApiResponse>(`/stories?page=${page}&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.stories) {
          console.log('Stories API response:', response.data);

          // Process the stories to ensure thumbnails are properly formatted
          const processedStories = response.data.stories.map(story => {
            // For photos, ensure we're using content_url directly
            // For videos, use thumbnail_url if available, otherwise generate one
            const thumbnailUrl = story.content_type === 'photo'
              ? story.content_url
              : (story.thumbnail_url || getDefaultThumbnail(story));

            return {
              ...story,
              viewed: Math.random() > 0.5, // Random viewed status for demo
              thumbnail_url: thumbnailUrl,
              // Ensure content_url is also set correctly
              content_url: story.content_url || thumbnailUrl
            };
          });

          if (page === 1) {
            setStories(processedStories);
          } else {
            setStories(prev => [...prev, ...processedStories]);
          }

          setHasMore(response.data.next_page);
        } else {
          console.warn('Unexpected API response format:', response.data);
          setError('Failed to load moments');
        }
      } catch (err) {
        console.error('Error fetching stories:', err);
        setError('Failed to load moments');
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, [page]);

  // Reference for the last item in the list
  const lastStoryRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last moment is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.5, rootMargin: '0px 0px 200px 0px' } // Load when item is 50% visible or 200px before it comes into view
    );

    // Get the last item element
    const lastElement = lastStoryRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, stories.length]);

  // Get default thumbnail based on content type
  const getDefaultThumbnail = (story: Story): string => {
    if (story.content_type === 'video') {
      // Try to extract YouTube thumbnail if it's a YouTube video
      if (story.content_url && story.content_url.includes('youtube')) {
        const videoId = getYoutubeId(story.content_url);
        if (videoId) {
          return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        }
      }
      return `/pics/video-placeholder.jpg`;
    } else {
      // For photos, use the content_url directly if available
      return story.content_url || `/pics/placeholder.svg`;
    }
  };

  // Process image URL to handle cloudfront URLs properly
  const processImageUrl = (url: string): string => {
    if (!url) return '/pics/placeholder.svg';

    // If it's already a local URL, return as is
    if (url.startsWith('/')) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
      // console.log('Using CDN URL for story:', url);
      return url;
    }

    // Return the URL as is for other external URLs
    return url;
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Try to extract ID from YouTube URL
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : '';
  };

  // Handle story click
  const handleStoryClick = (storyId: string) => {
    // Find the story
    const story = stories.find(s => s.content_id === storyId);
    if (story) {
      // If it's a video, try to play it
      if (story.content_type === 'video' && story.content_url) {
        console.log(`Opening video: ${story.content_url}`);
        if (story.content_url.includes('youtube')) {
          const videoId = getYoutubeId(story.content_url);
          if (videoId) {
            // Open YouTube video in a new tab
            console.log(`Opening YouTube video: ${videoId}`);
            window.open(`https://www.youtube.com/watch?v=${videoId}`, '_blank');
          }
        } else if (story.content_url.includes('cloudfront.net')) {
          // Open cloudfront video directly
          console.log(`Opening cloudfront video: ${story.content_url}`);
          window.open(story.content_url, '_blank');
        } else {
          // Open the video URL directly
          console.log(`Opening other video: ${story.content_url}`);
          window.open(story.content_url, '_blank');
        }
      } else if (story.content_type === 'photo' && story.content_url) {
        // For photos, open the image in a new tab
        console.log(`Opening photo: ${story.content_url}`);
        window.open(story.content_url, '_blank');
      }
    }

    // Mark story as viewed
    setStories(prevStories =>
      prevStories.map(story =>
        story.content_id === storyId
          ? { ...story, viewed: true }
          : story
      )
    );
  };

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">Moments</h1>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading moments</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {stories.length > 0 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {stories.map((story, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === stories.length - 1;

                  return (
                  <div
                    key={`${story.content_id}-${index}`}
                    // Apply ref to the last item for intersection observer
                    ref={isLastItem ? lastStoryRef : null}
                    className="relative border border-[#B31B1E] rounded-lg overflow-hidden"
                    style={{ width: '100%', maxWidth: '220px', margin: '0 auto' }}
                  >
                    {/* User info at top */}
                    <div className="absolute top-2 left-2 right-2 z-10 flex items-center">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full overflow-hidden relative border border-white">
                          <Image
                            src={`/pics/user-profile.png`}
                            alt={story.user_name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="ml-2">
                          <div className="text-xs font-medium text-white">{story.user_name}</div>
                          <div className="text-xs text-white/80">1.2M Admiring</div>
                        </div>
                      </div>

                      {/* Admire button */}
                      <button className="ml-auto bg-[#B31B1E] text-white text-xs font-medium px-2 py-1 rounded-full">
                        Admire +
                      </button>
                    </div>

                    {/* Main image */}
                    <div className="w-full" style={{ aspectRatio: '3/4' }}>
                      <div className="w-full h-full relative">
                        {(() => {
                          const imageUrl = processImageUrl(story.content_type === 'photo' ? story.content_url : (story.thumbnail_url || story.content_url));
                          return (
                            <Image
                              src={imageUrl}
                              alt={story.content_name || "Story"}
                              fill
                              sizes="(max-width: 640px) 100vw, 220px"
                              className="object-cover"
                              priority={index < 12}
                              unoptimized={true}
                              onError={(e) => {
                                console.error(`Failed to load story image: ${imageUrl}`);
                                const imgElement = e.target as HTMLImageElement;
                                if (imgElement) {
                                  imgElement.src = '/pics/placeholder.svg';
                                }
                              }}
                            />
                          );
                        })()}

                        {/* Gradient overlay for better text visibility */}
                        <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-transparent to-black/60"></div>
                      </div>
                    </div>

                    {/* Interaction buttons at bottom - exactly as in the reference image */}
                    <div className="absolute bottom-0 left-0 right-0 flex justify-around items-center z-10 bg-black/50 py-2 px-2">
                      {/* 3-dots menu */}
                      <button className="text-white opacity-80 hover:opacity-100" onClick={(e) => e.stopPropagation()}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="1"></circle>
                          <circle cx="12" cy="5" r="1"></circle>
                          <circle cx="12" cy="19" r="1"></circle>
                        </svg>
                      </button>

                      {/* Comment */}
                      <button className="text-white opacity-80 hover:opacity-100" onClick={(e) => e.stopPropagation()}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                        </svg>
                      </button>

                      {/* Share */}
                      <button className="text-white opacity-80 hover:opacity-100" onClick={(e) => e.stopPropagation()}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="22" y1="2" x2="11" y2="13"></line>
                          <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                        </svg>
                      </button>

                      {/* Like */}
                      <button className="text-white opacity-80 hover:opacity-100" onClick={(e) => e.stopPropagation()}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                      </button>
                    </div>

                    {/* Click handler div */}
                    <div
                      className="absolute inset-0 cursor-pointer z-5"
                      onClick={() => handleStoryClick(story.content_id)}
                    ></div>
                  </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && stories.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more moments to load</div>
            )}

            {/* No content state */}
            {!loading && stories.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No moments available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
