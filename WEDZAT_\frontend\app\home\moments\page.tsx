"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";

// Define interface for story items
interface Story {
  content_id: string;
  content_name: string;
  content_url: string;
  content_description?: string;
  thumbnail_url?: string;
  duration?: number;
  created_at: string;
  user_name: string;
  content_type: 'video' | 'photo';
  is_own_content: boolean;
  viewed?: boolean; // Track if the user has viewed this story
}

interface ApiResponse {
  stories: Story[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

export default function MomentsPage() {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [likedStories, setLikedStories] = useState<Set<string>>(new Set());
  // Removed right sidebar state

  useEffect(() => setIsClient(true), []);

  // Fetch stories from the API
  useEffect(() => {
    const fetchStories = async () => {
      try {
        setLoading(true);
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          console.warn('No authentication token found');
          setError('Authentication required');
          return;
        }

        const response = await axios.get<ApiResponse>(`/stories?page=${page}&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.stories) {
          console.log('Stories API response:', response.data);

          // Process the stories to ensure thumbnails are properly formatted
          const processedStories = response.data.stories.map(story => {
            // For photos, ensure we're using content_url directly
            // For videos, use thumbnail_url if available, otherwise generate one
            const thumbnailUrl = story.content_type === 'photo'
              ? story.content_url
              : (story.thumbnail_url || getDefaultThumbnail(story));

            return {
              ...story,
              viewed: Math.random() > 0.5, // Random viewed status for demo
              thumbnail_url: thumbnailUrl,
              // Ensure content_url is also set correctly
              content_url: story.content_url || thumbnailUrl
            };
          });

          if (page === 1) {
            setStories(processedStories);
          } else {
            setStories(prev => [...prev, ...processedStories]);
          }

          setHasMore(response.data.next_page);
        } else {
          console.warn('Unexpected API response format:', response.data);
          setError('Failed to load moments');
        }
      } catch (err) {
        console.error('Error fetching stories:', err);
        setError('Failed to load moments');
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, [page]);

  // Reference for the last item in the list
  const lastStoryRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last moment is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.5, rootMargin: '0px 0px 200px 0px' } // Load when item is 50% visible or 200px before it comes into view
    );

    // Get the last item element
    const lastElement = lastStoryRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, stories.length]);

  // Get default thumbnail based on content type
  const getDefaultThumbnail = (story: Story): string => {
    if (story.content_type === 'video') {
      // Try to extract YouTube thumbnail if it's a YouTube video
      if (story.content_url && story.content_url.includes('youtube')) {
        const videoId = getYoutubeId(story.content_url);
        if (videoId) {
          return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        }
      }
      return `/pics/video-placeholder.jpg`;
    } else {
      // For photos, use the content_url directly if available
      return story.content_url || `/pics/placeholder.svg`;
    }
  };

  // Process image URL to handle cloudfront URLs properly
  const processImageUrl = (url: string): string => {
    if (!url) return '/pics/placeholder.svg';

    // If it's already a local URL, return as is
    if (url.startsWith('/')) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
      // console.log('Using CDN URL for story:', url);
      return url;
    }

    // Return the URL as is for other external URLs
    return url;
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Try to extract ID from YouTube URL
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : '';
  };

  // Handle story click
  const handleStoryClick = (storyId: string) => {
    console.log(`Story clicked: ${storyId}`);

    // Mark story as viewed
    setStories(prevStories =>
      prevStories.map(story =>
        story.content_id === storyId
          ? { ...story, viewed: true }
          : story
      )
    );

    // Find the story
    const story = stories.find(s => s.content_id === storyId);
    if (story) {
      // If it's a video, try to play it in a new tab
      if (story.content_type === 'video' && story.content_url) {
        console.log(`Opening video: ${story.content_url}`);
        if (story.content_url.includes('youtube')) {
          const videoId = getYoutubeId(story.content_url);
          if (videoId) {
            // Open YouTube video in a new tab
            console.log(`Opening YouTube video: ${videoId}`);
            window.open(`https://www.youtube.com/watch?v=${videoId}`, '_blank');
          }
        } else if (story.content_url.includes('cloudfront.net')) {
          // Open cloudfront video directly
          console.log(`Opening cloudfront video: ${story.content_url}`);
          window.open(story.content_url, '_blank');
        } else {
          // Open the video URL directly
          console.log(`Opening other video: ${story.content_url}`);
          window.open(story.content_url, '_blank');
        }
      } else if (story.content_type === 'photo' && story.content_url) {
        // For photos, open the image in a new tab
        console.log(`Opening photo: ${story.content_url}`);
        window.open(story.content_url, '_blank');
      }
    }
  };

  // Toggle like for moments/stories
  const toggleLike = async (storyId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Optimistically update UI
      const isCurrentlyLiked = likedStories.has(storyId);
      setLikedStories((prev) => {
        const newLiked = new Set(prev);
        if (isCurrentlyLiked) {
          newLiked.delete(storyId);
        } else {
          newLiked.add(storyId);
        }
        return newLiked;
      });

      // Determine content type based on story
      const story = stories.find(s => s.content_id === storyId);
      const contentType = story?.content_type === 'video' ? 'video' : 'photo';

      // Make API call
      const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';
      await axios.post(endpoint, {
        content_id: storyId,
        content_type: contentType
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log(`${isCurrentlyLiked ? 'Unliked' : 'Liked'} story: ${storyId}`);
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert optimistic update on error
      setLikedStories((prev) => {
        const newLiked = new Set(prev);
        if (likedStories.has(storyId)) {
          newLiked.delete(storyId);
        } else {
          newLiked.add(storyId);
        }
        return newLiked;
      });
    }
  };

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">Moments</h1>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading moments</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {stories.length > 0 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                {stories.map((story, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === stories.length - 1;

                  return (
                  <div
                    key={`${story.content_id}-${index}`}
                    // Apply ref to the last item for intersection observer
                    ref={isLastItem ? lastStoryRef : null}
                    className="flex flex-col items-center cursor-pointer"
                    onClick={() => handleStoryClick(story.content_id)}
                  >
                    <div
                      className={`rounded-full p-1 mb-2 ${
                        !story.viewed
                          ? "bg-gradient-to-tr from-yellow-500 via-red-500 to-purple-600"
                          : "border-2 border-gray-300"
                      }`}
                    >
                      <div className="bg-white p-0.5 rounded-full">
                        <div className="overflow-hidden w-20 h-20 sm:w-24 sm:h-24 rounded-full relative">
                          {/* Display the story content (image or video) */}
                          <div className="w-full h-full relative">
                            {/* Determine the image source based on content type */}
                            {(() => {
                              const imageUrl = processImageUrl(story.content_type === 'photo' ? story.content_url : (story.thumbnail_url || story.content_url));
                              return (
                                <Image
                                  src={imageUrl}
                                  alt={story.content_name || "Story"}
                                  fill
                                  sizes="(max-width: 640px) 100px, 150px"
                                  className="object-cover"
                                  priority={index < 12} // Only prioritize the first twelve images
                                  unoptimized={true} // Skip Next.js image optimization for CDN URLs
                                  onError={(e) => {
                                    console.error(`Failed to load story image: ${imageUrl}`);
                                    // Use placeholder as fallback
                                    const imgElement = e.target as HTMLImageElement;
                                    if (imgElement) {
                                      imgElement.src = '/pics/placeholder.svg';
                                    }
                                  }}
                                />
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    </div>
                    <span className="text-sm font-medium text-center truncate w-full">
                      {story.user_name}
                    </span>
                    <span className="text-xs text-gray-500 truncate w-full text-center">
                      {story.content_name}
                    </span>
                  </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && stories.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more moments to load</div>
            )}

            {/* No content state */}
            {!loading && stories.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No moments available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
