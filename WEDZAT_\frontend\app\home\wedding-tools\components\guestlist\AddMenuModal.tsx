"use client";
import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import axios from 'axios';
import { getAuthToken } from '../../utils';
import { MenuOption } from './GuestList';
import Image from "next/image";

interface AddMenuModalProps {
  onClose: () => void;
  onSave: () => void;
  menu: MenuOption | null;
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
}

const AddMenuModal: React.FC<AddMenuModalProps> = ({
  onClose,
  onSave,
  menu,
  setError,
  setSuccessMessage
}) => {

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  // Initialize form with menu data if editing
  useEffect(() => {
    if (menu) {
      setName(menu.name || '');
      setDescription(menu.description || '');
    }
  }, [menu]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name) {
      setError('Menu name is required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setLoading(false);
        return;
      }

      if (menu) {
        // Update existing menu
        await axios.put(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-menu-option',
          {
            menu_id: menu.menu_id,
            name,
            description
          },
          { headers: { Authorization: `Bearer ${token}` } }
        );

        setSuccessMessage('Menu option updated successfully');
      } else {
        // Add new menu
        await axios.post(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-menu-option',
          {
            name,
            description
          },
          { headers: { Authorization: `Bearer ${token}` } }
        );

        setSuccessMessage('Menu option added successfully');
      }

      onSave();
      onClose();

    } catch (err: any) {
      console.error('Error saving menu option:', err);
      setError(err.response?.data?.error || 'Failed to save menu option');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
      <div
        className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
        style={{
          background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
        }}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
        >
          <X size={20} />
        </button>

        {/* Logo */}
        <div className="flex justify-center pt-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        <div className="px-6 py-4">
          <h3 className="text-2xl font-bold mb-2 text-center" style={{ color: "#B31B1E" }}>
            {menu ? 'Edit Menu Option' : 'Add Menu Option'}
          </h3>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Menu Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Menu Name *
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                required
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                rows={3}
                placeholder="e.g., Vegetarian, Gluten-free, etc."
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#B31B1E] text-white rounded hover:bg-red-700 disabled:bg-red-300"
              disabled={loading}
            >
              {loading ? 'Saving...' : (menu ? 'Update' : 'Add')}
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default AddMenuModal;
