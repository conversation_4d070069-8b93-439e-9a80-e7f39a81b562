// contexts/GlobalUploadManager.tsx
'use client';

import React, { createContext, useState, useContext, ReactNode } from 'react';
import UploadManager from '../components/upload/UploadManager';

interface GlobalUploadContextType {
  isUploadModalOpen: boolean;
  openUploadModal: (initialType?: string) => void;
  closeUploadModal: () => void;
}

const GlobalUploadContext = createContext<GlobalUploadContextType | undefined>(undefined);

export const GlobalUploadProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [initialType, setInitialType] = useState<string | undefined>(undefined);

  const openUploadModal = (type?: string) => {
    setInitialType(type);
    setIsUploadModalOpen(true);
  };

  const closeUploadModal = () => {
    setIsUploadModalOpen(false);
    setInitialType(undefined);
  };

  return (
    <GlobalUploadContext.Provider value={{ isUploadModalOpen, openUploadModal, closeUploadModal }}>
      {children}
      {isUploadModalOpen && <UploadManager onClose={closeUploadModal} initialType={initialType} />}
    </GlobalUploadContext.Provider>
  );
};

export const useGlobalUpload = (): GlobalUploadContextType => {
  const context = useContext(GlobalUploadContext);
  if (!context) {
    throw new Error('useGlobalUpload must be used within a GlobalUploadProvider');
  }
  return context;
};