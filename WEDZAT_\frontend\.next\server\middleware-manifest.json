{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a1b8b59d._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_321a5c3e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|_vercel|api|.*\\..*|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|_vercel|api|.*\\..*|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vXEI+55+u50UGkXILCLrp7mHM2CJivIwNmHc0uy//sY=", "__NEXT_PREVIEW_MODE_ID": "972ee3056a98114d15fec1009a7ad909", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a70b50c4cd679632d4bdb8bcb21350d6fd4da84fbd770a97940bd4a025470859", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6065dd274c21cb483f13bf31be73234ae8999e10eccfec2c7edd387eb1f6ce08"}}}, "sortedMiddleware": ["/"], "functions": {}}