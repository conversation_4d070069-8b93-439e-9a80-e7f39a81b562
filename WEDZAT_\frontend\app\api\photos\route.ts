import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header (same pattern as working like/unlike routes)
    const authHeader = request.headers.get('authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '10';
    const category = searchParams.get('category');
    const user_id = searchParams.get('user_id');

    console.log(`Fetching photos: page=${page}, limit=${limit}, category=${category}, user_id=${user_id}`);

    // Build query parameters for backend API
    const queryParams = new URLSearchParams({
      page,
      limit
    });

    if (category) {
      queryParams.append('category', category);
    }

    if (user_id) {
      queryParams.append('user_id', user_id);
    }

    // Make API call to backend (same pattern as like/unlike routes)
    const response = await axios.get(
      `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/photos?${queryParams.toString()}`,
      {
        headers: {
          'Authorization': authHeader,  // Pass the full header directly
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      }
    );

    console.log('Photos API response received:', response.status);
    const res = NextResponse.json(response.data);
    res.headers.set('Cache-Control', 'public, max-age=60, stale-while-revalidate=120');
    return res;
  } catch (error: any) {
    console.error('Error fetching photos:', error);

    // Detailed error response
    let errorMessage = 'Failed to fetch photos';
    let statusCode = 500;

    if (error.response) {
      console.error('Photos API response error:', error.response.data);
      statusCode = error.response.status;
      errorMessage = `Server error: ${error.response.data?.error || error.response.data?.message || error.message}`;
    } else if (error.request) {
      console.error('Photos API request error:', error.request);
      errorMessage = 'No response received from server';
    } else {
      console.error('Photos API error message:', error.message);
      errorMessage = error.message;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
