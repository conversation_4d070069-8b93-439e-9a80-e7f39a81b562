/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/photos/route";
exports.ids = ["app/api/photos/route"];
exports.modules = {

/***/ "(rsc)/./app/api/photos/route.ts":
/*!*********************************!*\
  !*** ./app/api/photos/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\n\nasync function GET(request) {\n    try {\n        // Get the authorization header (same pattern as working like/unlike routes)\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authorization header is required'\n            }, {\n                status: 401\n            });\n        }\n        // Get query parameters\n        const { searchParams } = new URL(request.url);\n        const page = searchParams.get('page') || '1';\n        const limit = searchParams.get('limit') || '10';\n        const category = searchParams.get('category');\n        const user_id = searchParams.get('user_id');\n        console.log(`Fetching photos: page=${page}, limit=${limit}, category=${category}, user_id=${user_id}`);\n        // Build query parameters for backend API\n        const queryParams = new URLSearchParams({\n            page,\n            limit\n        });\n        if (category) {\n            queryParams.append('category', category);\n        }\n        if (user_id) {\n            queryParams.append('user_id', user_id);\n        }\n        // Make API call to backend (same pattern as like/unlike routes)\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/photos?${queryParams.toString()}`, {\n            headers: {\n                'Authorization': authHeader,\n                'Content-Type': 'application/json'\n            },\n            timeout: 15000\n        });\n        console.log('Photos API response received:', response.status);\n        const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response.data);\n        res.headers.set('Cache-Control', 'public, max-age=60, stale-while-revalidate=120');\n        return res;\n    } catch (error) {\n        console.error('Error fetching photos:', error);\n        // Detailed error response\n        let errorMessage = 'Failed to fetch photos';\n        let statusCode = 500;\n        if (error.response) {\n            console.error('Photos API response error:', error.response.data);\n            statusCode = error.response.status;\n            errorMessage = `Server error: ${error.response.data?.error || error.response.data?.message || error.message}`;\n        } else if (error.request) {\n            console.error('Photos API request error:', error.request);\n            errorMessage = 'No response received from server';\n        } else {\n            console.error('Photos API error message:', error.message);\n            errorMessage = error.message;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage\n        }, {\n            status: statusCode\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/photos/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fphotos%2Froute&page=%2Fapi%2Fphotos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphotos%2Froute.ts&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fphotos%2Froute&page=%2Fapi%2Fphotos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphotos%2Froute.ts&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_app_api_photos_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/photos/route.ts */ \"(rsc)/./app/api/photos/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/photos/route\",\n        pathname: \"/api/photos\",\n        filename: \"route\",\n        bundlePath: \"app/api/photos/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\api\\\\photos\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_app_api_photos_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fphotos%2Froute&page=%2Fapi%2Fphotos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphotos%2Froute.ts&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fphotos%2Froute&page=%2Fapi%2Fphotos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphotos%2Froute.ts&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();