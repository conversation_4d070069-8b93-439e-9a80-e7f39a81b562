"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/login/user-login.tsx":
/*!*****************************************!*\
  !*** ./components/login/user-login.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/api */ \"(app-pages-browser)/./services/api.ts\");\n/* harmony import */ var _barrel_optimize_names_FcGoogle_react_icons_fc__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FcGoogle!=!react-icons/fc */ \"(app-pages-browser)/./node_modules/react-icons/fc/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaApple_FaFacebookF_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FaApple,FaFacebookF!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n// components/login/user-login.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst UserLogin = (param)=>{\n    let { onLogin, onForgotPassword, onSignupClick, onBack, onSocialSignin } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [loginType, setLoginType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mobile\");\n    const [credentials, setCredentials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mobile_number: \"\",\n        email: \"\",\n        password: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mobile_number: \"\",\n        email: \"\",\n        password: \"\"\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [serverError, setServerError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // For debugging\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setCredentials((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n        // Clear server error when typing\n        if (serverError) {\n            setServerError(\"\");\n        }\n    };\n    const validateForm = ()=>{\n        let isValid = true;\n        const newErrors = {\n            mobile_number: \"\",\n            email: \"\",\n            password: \"\"\n        };\n        if (loginType === \"email\") {\n            // Email validation\n            if (!credentials.email) {\n                newErrors.email = \"Email is required\";\n                isValid = false;\n            } else if (!/\\S+@\\S+\\.\\S+/.test(credentials.email)) {\n                newErrors.email = \"Please enter a valid email\";\n                isValid = false;\n            }\n        } else {\n            // Mobile validation\n            if (!credentials.mobile_number) {\n                newErrors.mobile_number = \"Mobile number is required\";\n                isValid = false;\n            } else if (credentials.mobile_number.length < 10) {\n                newErrors.mobile_number = \"Please enter a valid mobile number\";\n                isValid = false;\n            }\n        }\n        // Password validation\n        if (!credentials.password) {\n            newErrors.password = \"Password is required\";\n            isValid = false;\n        }\n        setErrors(newErrors);\n        return isValid;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (validateForm()) {\n            setIsLoading(true);\n            setServerError(\"\");\n            setDebugInfo(\"Login attempt started...\");\n            try {\n                // Call the login API with appropriate credentials\n                let loginParams = {\n                    password: credentials.password\n                };\n                if (loginType === \"email\") {\n                    loginParams.email = credentials.email;\n                } else {\n                    loginParams.mobile_number = credentials.mobile_number;\n                }\n                setDebugInfo((prev)=>prev + \"\\nAttempting login with \".concat(loginType, \": \").concat(loginType === \"email\" ? credentials.email : credentials.mobile_number));\n                // Login with the API\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_4__.authService.login(loginParams);\n                setDebugInfo((prev)=>prev + \"\\nLogin response received: \".concat(JSON.stringify({\n                        success: true,\n                        hasToken: !!(response === null || response === void 0 ? void 0 : response.token),\n                        tokenPreview: (response === null || response === void 0 ? void 0 : response.token) ? \"\".concat(response.token.substring(0, 10), \"...\") : \"none\"\n                    })));\n                // Save token to localStorage with both key names for compatibility\n                if (response === null || response === void 0 ? void 0 : response.token) {\n                    // Store token in multiple places to ensure it's detected\n                    try {\n                        // 1. Store in localStorage (both keys for compatibility)\n                        localStorage.setItem(\"token\", response.token);\n                        localStorage.setItem(\"jwt_token\", response.token);\n                        // 2. Also store as a cookie for middleware to detect\n                        document.cookie = \"token=\".concat(response.token, \"; path=/; max-age=86400; SameSite=Strict\");\n                        // 3. Verify storage was successful\n                        const localToken = localStorage.getItem(\"token\");\n                        const jwtToken = localStorage.getItem(\"jwt_token\");\n                        const cookieSet = document.cookie.includes(\"token=\");\n                        setDebugInfo((prev)=>prev + \"\\nToken storage verification:\\n- localStorage (token): \".concat(localToken ? \"SUCCESS\" : \"FAILED\", \"\\n- localStorage (jwt_token): \").concat(jwtToken ? \"SUCCESS\" : \"FAILED\", \"\\n- cookie: \").concat(cookieSet ? \"SUCCESS\" : \"FAILED\"));\n                        // If using callback approach, call it before redirecting\n                        if (onLogin) {\n                            onLogin(loginParams);\n                            setDebugInfo((prev)=>prev + \"\\nonLogin callback executed\");\n                        }\n                        // Add short delay before redirecting to ensure tokens are saved\n                        setDebugInfo((prev)=>prev + \"\\nPreparing to redirect to home page...\");\n                        setTimeout(()=>{\n                            setDebugInfo((prev)=>prev + \"\\nRedirecting now!\");\n                            router.push(\"/home\");\n                        }, 500);\n                    } catch (storageError) {\n                        console.error(\"Error storing token:\", storageError);\n                        setDebugInfo((prev)=>prev + \"\\nError storing token: \".concat(storageError));\n                        setServerError(\"Error storing authentication token. Please try again.\");\n                    }\n                } else {\n                    // No token in response\n                    setDebugInfo((prev)=>prev + \"\\nNo token in response\");\n                    setServerError(\"Authentication failed: No token received from server\");\n                }\n            } catch (error) {\n                console.error(\"User login error:\", error);\n                setServerError(error.error || \"Invalid credentials. Please try again.\");\n                setDebugInfo((prev)=>prev + \"\\nLogin error: \".concat(JSON.stringify(error)));\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    };\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    const toggleLoginType = (type)=>{\n        setLoginType(type);\n        setErrors({\n            mobile_number: \"\",\n            email: \"\",\n            password: \"\"\n        });\n        setServerError(\"\");\n        setDebugInfo(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full px-6 py-6 rounded-lg overflow-y-auto h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/pics/logo.png\",\n                            alt: \"Wedzat logo\",\n                            width: 40,\n                            height: 40,\n                            className: \"object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"User Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-1 text-gray-600\",\n                            children: \"Welcome back! Please login to continue\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex rounded-full bg-gray-100 p-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>toggleLoginType(\"mobile\"),\n                                className: \"px-4 py-2 rounded-full text-sm font-medium \".concat(loginType === \"mobile\" ? \"bg-red-700 text-white\" : \"text-gray-700 hover:bg-gray-200\"),\n                                children: \"Mobile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>toggleLoginType(\"email\"),\n                                className: \"px-4 py-2 rounded-full text-sm font-medium \".concat(loginType === \"email\" ? \"bg-red-700 text-white\" : \"text-gray-700 hover:bg-gray-200\"),\n                                children: \"Email\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, undefined),\n                serverError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg\",\n                    children: serverError\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        loginType === \"mobile\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"mobile_number\",\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Mobile Number\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"mobile_number\",\n                                    type: \"tel\",\n                                    name: \"mobile_number\",\n                                    placeholder: \"Enter your mobile number\",\n                                    className: \"w-full p-3 border \".concat(errors.mobile_number ? \"border-red-500\" : \"border-gray-300\", \" rounded-lg bg-white\"),\n                                    value: credentials.mobile_number,\n                                    onChange: handleChange,\n                                    disabled: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, undefined),\n                                errors.mobile_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500 text-xs mt-1\",\n                                    children: errors.mobile_number\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"email\",\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Email Address\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"email\",\n                                    type: \"email\",\n                                    name: \"email\",\n                                    placeholder: \"Enter your email\",\n                                    className: \"w-full p-3 border \".concat(errors.email ? \"border-red-500\" : \"border-gray-300\", \" rounded-lg bg-white\"),\n                                    value: credentials.email,\n                                    onChange: handleChange,\n                                    disabled: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, undefined),\n                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500 text-xs mt-1\",\n                                    children: errors.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        onForgotPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: onForgotPassword,\n                                            className: \"text-sm text-red-700 hover:text-red-800\",\n                                            disabled: isLoading,\n                                            children: \"Forgot password?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            type: showPassword ? \"text\" : \"password\",\n                                            name: \"password\",\n                                            placeholder: \"Enter your password\",\n                                            className: \"w-full p-3 border \".concat(errors.password ? \"border-red-500\" : \"border-gray-300\", \" rounded-lg bg-white\"),\n                                            value: credentials.password,\n                                            onChange: handleChange,\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: togglePasswordVisibility,\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 33\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 56\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, undefined),\n                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500 text-xs mt-1\",\n                                    children: errors.password\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6 disabled:bg-red-300\",\n                            disabled: isLoading,\n                            children: isLoading ? \"Logging in...\" : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full border-t border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex justify-center text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 bg-white text-gray-500\",\n                                        children: \"Or continue with\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between gap-4 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onSocialSignin === null || onSocialSignin === void 0 ? void 0 : onSocialSignin(\"Google\"),\n                                    className: \"flex-1 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center hover:bg-gray-50 transition-colors\",\n                                    disabled: isLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FcGoogle_react_icons_fc__WEBPACK_IMPORTED_MODULE_7__.FcGoogle, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onSocialSignin === null || onSocialSignin === void 0 ? void 0 : onSocialSignin(\"Apple\"),\n                                    className: \"flex-1 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center hover:bg-gray-50 transition-colors\",\n                                    disabled: isLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebookF_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaApple, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>onSocialSignin === null || onSocialSignin === void 0 ? void 0 : onSocialSignin(\"Facebook\"),\n                                    className: \"flex-1 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center hover:bg-gray-50 transition-colors\",\n                                    disabled: isLoading,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebookF_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaFacebookF, {\n                                        size: 24,\n                                        color: \"#1877F2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 text-sm\",\n                        children: [\n                            \"Don't have an account?\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onSignupClick,\n                                className: \"text-red-700 font-medium ml-1 hover:text-red-800\",\n                                children: \"Sign up\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\login\\\\user-login.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UserLogin, \"eDfY4rYH43Sbyb3FYrKynV15U/Q=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = UserLogin;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserLogin);\nvar _c;\n$RefreshReg$(_c, \"UserLogin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/login/user-login.tsx\n"));

/***/ })

});