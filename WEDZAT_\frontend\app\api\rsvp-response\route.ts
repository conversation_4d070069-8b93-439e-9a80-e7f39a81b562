// frontend/app/api/rsvp-response/route.ts
import { NextResponse } from 'next/server';
import { sendRsvpConfirmation } from '../../../lib/nodemailer';
import axios from 'axios';

interface RequestBody {
  token: string;
  response: string;
}

interface ResponseData {
  success: boolean;
  message: string;
  guestName?: string;
  response?: string;
}

export async function POST(request: Request): Promise<NextResponse<ResponseData>> {
  try {
    const body: RequestBody = await request.json();
    const { token, response } = body;

    // Validate required fields
    if (!token || !response) {
      return NextResponse.json({
        success: false,
        message: "Token and response are required"
      }, { status: 400 });
    }

    // Validate response value
    if (response !== 'attending' && response !== 'declined') {
      return NextResponse.json({
        success: false,
        message: "Invalid response value. Must be 'attending' or 'declined'"
      }, { status: 400 });
    }

    // Call the backend API to update the guest's attendance status
    const backendResponse = await axios.get(
      `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/respond-invitation?token=${token}&response=${response}`
    );

    // Check if the backend request was successful
    if (backendResponse.status !== 200) {
      return NextResponse.json({
        success: false,
        message: "Failed to update attendance status"
      }, { status: 500 });
    }

    const backendData = backendResponse.data;
    const guestName = backendData.guest_name || 'Guest';
    const coupleName = backendData.couple_name || 'The Couple';
    const guestEmail = backendData.guest_email;

    // If we have the guest's email, send a confirmation email
    if (guestEmail) {
      try {
        await sendRsvpConfirmation(
          guestEmail,
          guestName,
          coupleName,
          response
        );
      } catch (emailError) {
        console.error('Failed to send RSVP confirmation email:', emailError);
        // Continue even if email fails
      }
    }

    return NextResponse.json({
      success: true,
      message: backendData.message || "Thank you for your response!",
      guestName,
      response
    });
  } catch (error) {
    console.error('Error processing RSVP response:', error);

    // For development - provide more detailed error
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({
        success: false,
        message: `Failed to process RSVP: ${error instanceof Error ? error.message : String(error)}`
      }, { status: 500 });
    }

    return NextResponse.json({
      success: false,
      message: "Failed to process your response"
    }, { status: 500 });
  }
}
