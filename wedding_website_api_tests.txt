# Wedding Website API Test Cases

This file contains test cases for all wedding website APIs. Each test case includes the endpoint, method, required headers, request body (if applicable), and expected response.

## Prerequisites
- You need a valid authentication token
- Replace `YOUR_AUTH_TOKEN` with your actual token
- Replace `YOUR_USER_ID` with your actual user ID
- Use a tool like <PERSON><PERSON> or <PERSON> to make the API requests

## Base URL
All endpoints start with: https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test

## Templates API Tests

### 1. Get All Templates
- **Endpoint**: `/tool/website-templates`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "templates": [
      {
        "template_id": "uuid-value",
        "name": "Classic Elegance",
        "thumbnail_url": "https://example.com/thumbnail.jpg",
        "description": "A classic wedding template",
        "default_colors": {},
        "default_fonts": {},
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00"
      }
    ]
  }
  ```

### 2. Get Template Details
- **Endpoint**: `/tool/website-template?template_id=TEMPLATE_ID`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "template": {
      "template_id": "TEMPLATE_ID",
      "name": "Classic Elegance",
      "thumbnail_url": "https://example.com/thumbnail.jpg",
      "description": "A classic wedding template",
      "default_colors": {},
      "default_fonts": {},
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00",
      "page_templates": []
    }
  }
  ```

### 3. Get Page Templates
- **Endpoint**: `/tool/website-page-templates`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "page_templates": [
      {
        "page_template_id": "uuid-value",
        "type": "home",
        "name": "Home Page",
        "thumbnail_url": "https://example.com/thumbnail.jpg",
        "description": "Main home page",
        "default_content": {},
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00"
      }
    ]
  }
  ```

### 4. Create Template (Admin only)
- **Endpoint**: `/tool/create-website-template`
- **Method**: POST
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "name": "New Template",
    "thumbnail_url": "https://example.com/new-thumbnail.jpg",
    "description": "A new wedding template",
    "default_colors": {
      "primary": "#B31B1E",
      "secondary": "#333333",
      "background": "#FFFFFF",
      "text": "#000000"
    },
    "default_fonts": {
      "heading": ["Playfair Display", "Georgia", "serif"],
      "body": ["Roboto", "Arial", "sans-serif"]
    }
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "template": {
      "template_id": "new-uuid-value",
      "name": "New Template",
      "thumbnail_url": "https://example.com/new-thumbnail.jpg",
      "description": "A new wedding template",
      "default_colors": {
        "primary": "#B31B1E",
        "secondary": "#333333",
        "background": "#FFFFFF",
        "text": "#000000"
      },
      "default_fonts": {
        "heading": ["Playfair Display", "Georgia", "serif"],
        "body": ["Roboto", "Arial", "sans-serif"]
      },
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00"
    }
  }
  ```

## Websites API Tests

### 1. Get User Websites
- **Endpoint**: `/tool/user-websites`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "websites": [
      {
        "website_id": "uuid-value",
        "title": "Our Wedding",
        "template_id": "template-uuid",
        "theme": "classic",
        "custom_domain": null,
        "wedding_date": "2024-06-15",
        "wedding_location": "New York, NY",
        "about_couple": "We met five years ago...",
        "design_settings": {},
        "is_published": false,
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00",
        "template_name": "Classic Elegance",
        "template_thumbnail": "https://example.com/thumbnail.jpg"
      }
    ]
  }
  ```

### 2. Get Website Details
- **Endpoint**: `/tool/website?website_id=WEBSITE_ID`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "website": {
      "website_id": "WEBSITE_ID",
      "title": "Our Wedding",
      "template_id": "template-uuid",
      "theme": "classic",
      "custom_domain": null,
      "wedding_date": "2024-06-15",
      "wedding_location": "New York, NY",
      "about_couple": "We met five years ago...",
      "design_settings": {},
      "is_published": false,
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00",
      "template_name": "Classic Elegance",
      "template_thumbnail": "https://example.com/thumbnail.jpg",
      "template_default_colors": {},
      "template_default_fonts": {},
      "pages": [],
      "media": []
    }
  }
  ```

### 3. Create Website
- **Endpoint**: `/tool/create-website`
- **Method**: POST
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "title": "Our Wedding",
    "template_id": "TEMPLATE_ID",
    "wedding_date": "2024-06-15",
    "wedding_location": "New York, NY",
    "about_couple": "We met five years ago..."
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "website": {
      "website_id": "new-uuid-value",
      "title": "Our Wedding",
      "template_id": "TEMPLATE_ID",
      "wedding_date": "2024-06-15",
      "wedding_location": "New York, NY",
      "about_couple": "We met five years ago...",
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00"
    }
  }
  ```

### 4. Update Website
- **Endpoint**: `/tool/update-website`
- **Method**: PUT
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "title": "Our Updated Wedding",
    "wedding_date": "2024-07-15",
    "wedding_location": "Los Angeles, CA",
    "about_couple": "Updated story about us...",
    "is_published": true
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "message": "Website updated successfully",
    "updated_at": "2023-01-02T00:00:00"
  }
  ```

### 5. Delete Website
- **Endpoint**: `/tool/delete-website`
- **Method**: DELETE
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID"
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "message": "Website deleted successfully"
  }
  ```

## Design API Tests

### 1. Get Design Settings
- **Endpoint**: `/tool/website-design?website_id=WEBSITE_ID`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "design_settings": {
      "colors": {
        "primary": "#B31B1E",
        "secondary": "#333333",
        "background": "#FFFFFF",
        "text": "#000000"
      },
      "fonts": {
        "heading": "Playfair Display",
        "body": "Roboto"
      }
    },
    "template_defaults": {
      "default_colors": {
        "primary": "#B31B1E",
        "secondary": "#333333",
        "background": "#FFFFFF",
        "text": "#000000"
      },
      "default_fonts": {
        "heading": ["Playfair Display", "Georgia", "serif"],
        "body": ["Roboto", "Arial", "sans-serif"]
      }
    },
    "available_fonts": {
      "heading": ["Playfair Display", "Montserrat", "Lora"],
      "body": ["Roboto", "Open Sans", "Lato"]
    }
  }
  ```

### 2. Update Design Settings
- **Endpoint**: `/tool/update-website-design`
- **Method**: PUT
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "design_settings": {
      "colors": {
        "primary": "#0066CC",
        "secondary": "#444444",
        "background": "#F5F5F5",
        "text": "#222222"
      },
      "fonts": {
        "heading": "Montserrat",
        "body": "Open Sans"
      }
    }
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "message": "Design settings updated successfully",
    "updated_at": "2023-01-02T00:00:00"
  }
  ```

### 3. Get Fonts
- **Endpoint**: `/tool/website-fonts`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "fonts": {
      "heading": ["Playfair Display", "Montserrat", "Lora", "Merriweather", "Oswald"],
      "body": ["Roboto", "Open Sans", "Lato", "Source Sans Pro", "Nunito"]
    }
  }
  ```

### 4. Get Color Palettes
- **Endpoint**: `/tool/website-color-palettes`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "color_palettes": [
      {
        "name": "Classic Red",
        "colors": {
          "primary": "#B31B1E",
          "secondary": "#333333",
          "background": "#FFFFFF",
          "text": "#000000"
        }
      },
      {
        "name": "Navy Blue",
        "colors": {
          "primary": "#0066CC",
          "secondary": "#444444",
          "background": "#F5F5F5",
          "text": "#222222"
        }
      }
    ]
  }
  ```

## Pages API Tests

### 1. Get Pages
- **Endpoint**: `/tool/website-pages?website_id=WEBSITE_ID`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "pages": [
      {
        "page_id": "uuid-value",
        "title": "Home",
        "slug": "home",
        "type": "home",
        "content": {},
        "order_index": 0,
        "is_visible": true,
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00"
      },
      {
        "page_id": "uuid-value-2",
        "title": "Our Story",
        "slug": "our-story",
        "type": "story",
        "content": {},
        "order_index": 1,
        "is_visible": true,
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00"
      }
    ]
  }
  ```

### 2. Get Page Details
- **Endpoint**: `/tool/website-page?website_id=WEBSITE_ID&page_id=PAGE_ID`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "page": {
      "page_id": "PAGE_ID",
      "title": "Our Story",
      "slug": "our-story",
      "type": "story",
      "content": {
        "heading": "Our Love Story",
        "text": "We met five years ago at a coffee shop...",
        "images": []
      },
      "order_index": 1,
      "is_visible": true,
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00"
    }
  }
  ```

### 3. Create Page
- **Endpoint**: `/tool/create-website-page`
- **Method**: POST
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "title": "Wedding Party",
    "slug": "wedding-party",
    "type": "custom",
    "content": {
      "heading": "Meet Our Wedding Party",
      "text": "These special people will be standing by our side...",
      "members": [
        {
          "name": "Jane Smith",
          "role": "Maid of Honor",
          "photo": "https://example.com/jane.jpg"
        }
      ]
    },
    "order_index": 3,
    "is_visible": true
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "page": {
      "page_id": "new-uuid-value",
      "title": "Wedding Party",
      "slug": "wedding-party",
      "type": "custom",
      "content": {
        "heading": "Meet Our Wedding Party",
        "text": "These special people will be standing by our side...",
        "members": [
          {
            "name": "Jane Smith",
            "role": "Maid of Honor",
            "photo": "https://example.com/jane.jpg"
          }
        ]
      },
      "order_index": 3,
      "is_visible": true,
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00"
    }
  }
  ```

### 4. Update Page
- **Endpoint**: `/tool/update-website-page`
- **Method**: PUT
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "page_id": "PAGE_ID",
    "title": "Updated Wedding Party",
    "content": {
      "heading": "Meet Our Amazing Wedding Party",
      "text": "Updated description of our wedding party...",
      "members": [
        {
          "name": "Jane Smith",
          "role": "Maid of Honor",
          "photo": "https://example.com/jane.jpg"
        },
        {
          "name": "John Doe",
          "role": "Best Man",
          "photo": "https://example.com/john.jpg"
        }
      ]
    },
    "is_visible": true
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "message": "Page updated successfully",
    "updated_at": "2023-01-02T00:00:00"
  }
  ```

### 5. Update Page Order
- **Endpoint**: `/tool/update-website-page-order`
- **Method**: PUT
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "page_order": [
      {"page_id": "page-id-1", "order_index": 0},
      {"page_id": "page-id-2", "order_index": 1},
      {"page_id": "page-id-3", "order_index": 2}
    ]
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "message": "Page order updated successfully"
  }
  ```

### 6. Delete Page
- **Endpoint**: `/tool/delete-website-page`
- **Method**: DELETE
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "page_id": "PAGE_ID"
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "message": "Page deleted successfully"
  }
  ```

## Media API Tests

### 1. Get Media
- **Endpoint**: `/tool/website-media?website_id=WEBSITE_ID`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "media": [
      {
        "media_id": "uuid-value",
        "type": "image",
        "url": "https://example.com/image.jpg",
        "thumbnail_url": "https://example.com/thumbnail.jpg",
        "title": "Engagement Photo",
        "description": "Our engagement photo at the beach",
        "metadata": {
          "width": 1200,
          "height": 800
        },
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00"
      }
    ]
  }
  ```

### 2. Get Media Item
- **Endpoint**: `/tool/website-media-item?website_id=WEBSITE_ID&media_id=MEDIA_ID`
- **Method**: GET
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  ```
- **Expected Response**:
  ```json
  {
    "media": {
      "media_id": "MEDIA_ID",
      "type": "image",
      "url": "https://example.com/image.jpg",
      "thumbnail_url": "https://example.com/thumbnail.jpg",
      "title": "Engagement Photo",
      "description": "Our engagement photo at the beach",
      "metadata": {
        "width": 1200,
        "height": 800
      },
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00"
    }
  }
  ```

### 3. Upload Media
- **Endpoint**: `/tool/upload-website-media`
- **Method**: POST
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "type": "image",
    "url": "https://example.com/new-image.jpg",
    "thumbnail_url": "https://example.com/new-thumbnail.jpg",
    "title": "Wedding Venue",
    "description": "Our beautiful wedding venue",
    "metadata": {
      "width": 1600,
      "height": 1200
    }
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "media": {
      "media_id": "new-uuid-value",
      "type": "image",
      "url": "https://example.com/new-image.jpg",
      "thumbnail_url": "https://example.com/new-thumbnail.jpg",
      "title": "Wedding Venue",
      "description": "Our beautiful wedding venue",
      "metadata": {
        "width": 1600,
        "height": 1200
      },
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00"
    }
  }
  ```

### 4. Update Media
- **Endpoint**: `/tool/update-website-media`
- **Method**: PUT
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "media_id": "MEDIA_ID",
    "title": "Updated Wedding Venue",
    "description": "Our beautiful wedding venue with updated description",
    "metadata": {
      "width": 1600,
      "height": 1200,
      "tags": ["venue", "ceremony"]
    }
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "message": "Media updated successfully",
    "updated_at": "2023-01-02T00:00:00"
  }
  ```

### 5. Delete Media
- **Endpoint**: `/tool/delete-website-media`
- **Method**: DELETE
- **Headers**:
  ```
  Authorization: Bearer YOUR_AUTH_TOKEN
  Content-Type: application/json
  ```
- **Request Body**:
  ```json
  {
    "website_id": "WEBSITE_ID",
    "media_id": "MEDIA_ID"
  }
  ```
- **Expected Response**:
  ```json
  {
    "success": true,
    "message": "Media deleted successfully"
  }
  ```

## Testing Flow

For a complete test of the wedding website functionality, follow this sequence:

1. Get all templates
2. Create a new website using one of the templates
3. Get the website details
4. Update the website details
5. Get the design settings
6. Update the design settings
7. Get all pages for the website
8. Create a new page
9. Update a page
10. Update page order
11. Upload media
12. Get all media
13. Update media
14. Delete media
15. Delete a page
16. Delete the website

This sequence will test all the API endpoints and ensure they're working correctly.
