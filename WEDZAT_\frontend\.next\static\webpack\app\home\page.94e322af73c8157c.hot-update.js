"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./components/HomeDashboard/Flashes.tsx":
/*!**********************************************!*\
  !*** ./components/HomeDashboard/Flashes.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../contexts/LocationContext */ \"(app-pages-browser)/./contexts/LocationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst FlashesSection = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { selectedLocation } = (0,_contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__.useLocation)();\n    const [flashes, setFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // User avatar placeholders (using local images instead of external URLs)\n    const userAvatarPlaceholders = [\n        \"/pics/1stim.jfif\",\n        \"/pics/2ndim.jfif\",\n        \"/pics/jpeg3.jfif\",\n        \"/pics/user-profile.png\",\n        \"/pics/user-profile.png\"\n    ];\n    // Fallback data if API fails - using empty array\n    const getFallbackFlashes = ()=>[];\n    // Function to fetch flashes from the API\n    const fetchFlashes = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching flashes for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setFlashes([]);\n                return;\n            }\n            // Build API URL with location parameter if selected\n            let apiUrl = \"/flashes?page=\".concat(pageNumber, \"&limit=10\");\n            if (selectedLocation) {\n                apiUrl += \"&location=\".concat(encodeURIComponent(selectedLocation));\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(apiUrl, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            console.log('Flashes API response:', response.data);\n            // Process the data\n            if (response.data && response.data.flashes) {\n                console.log(\"Loaded \".concat(response.data.flashes.length, \" flashes for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.flashes.length > 0) {\n                    console.log('Sample flash data:', response.data.flashes[0]);\n                }\n                // Process the response\n                const processedFlashes = response.data.flashes.map((flash)=>{\n                    if (!flash.video_thumbnail) {\n                        console.log(\"Flash missing thumbnail: \".concat(flash.video_id));\n                    }\n                    return flash;\n                });\n                if (pageNumber === 1) {\n                    setFlashes(processedFlashes);\n                } else {\n                    setFlashes((prev)=>[\n                            ...prev,\n                            ...processedFlashes\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load flashes - unexpected response format');\n                setFlashes([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load flashes');\n            setFlashes([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of flashes as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FlashesSection.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Flashes component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial flashes load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchFlashes function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Build API URL with location parameter if selected\n                    let apiUrl = \"/flashes?page=1&limit=10\";\n                    if (selectedLocation) {\n                        apiUrl += \"&location=\".concat(encodeURIComponent(selectedLocation));\n                    }\n                    // Make direct API request\n                    console.log('Making direct API request for flashes page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(apiUrl, {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"FlashesSection.useEffect\": (response)=>{\n                            console.log('API response received for flashes page 1');\n                            if (response.data && response.data.flashes) {\n                                setFlashes(response.data.flashes);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setFlashes([]);\n                                setError('No flashes found');\n                            }\n                        }\n                    }[\"FlashesSection.useEffect\"]).catch({\n                        \"FlashesSection.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load flashes');\n                            setFlashes([]);\n                        }\n                    }[\"FlashesSection.useEffect\"]).finally({\n                        \"FlashesSection.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"FlashesSection.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"FlashesSection.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Failsafe to ensure content is loaded - only show error after timeout\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FlashesSection.useEffect\": ()=>{\n            // If we're stuck in loading state for more than 10 seconds, show error\n            let timeoutId = null;\n            if (loading && initialLoadComplete) {\n                timeoutId = setTimeout({\n                    \"FlashesSection.useEffect\": ()=>{\n                        console.log('Loading timeout reached - API request may have failed');\n                        setLoading(false);\n                        if (flashes.length === 0) {\n                            setError('Unable to load flashes. Please check your network connection.');\n                        }\n                    }\n                }[\"FlashesSection.useEffect\"], 10000); // 10 second timeout for slow networks\n            }\n            return ({\n                \"FlashesSection.useEffect\": ()=>{\n                    if (timeoutId) clearTimeout(timeoutId);\n                }\n            })[\"FlashesSection.useEffect\"];\n        }\n    }[\"FlashesSection.useEffect\"], [\n        loading,\n        initialLoadComplete,\n        flashes.length\n    ]);\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FlashesSection.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"FlashesSection.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchFlashes(nextPage, false);\n                    }\n                }\n            }[\"FlashesSection.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"FlashesSection.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"FlashesSection.useEffect\"];\n        }\n    }[\"FlashesSection.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a flash\n    const getImageSource = (flash)=>{\n        if (flash.video_thumbnail) {\n            return flash.video_thumbnail;\n        }\n        return '/pics/placeholder.svg';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"w-full mb-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-black\",\n                        children: \"Flashes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative ml-auto mr-1\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading flashes...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying flashes load...');\n                            setError(null);\n                            fetchFlashes(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                lineNumber: 331,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: 'smooth',\n                    WebkitOverflowScrolling: 'touch',\n                    minHeight: flashes.length === 0 && !error ? '220px' : 'auto'\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative w-full\",\n                children: [\n                    flashes.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No flashes available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-3 pb-4 flex-nowrap\",\n                        children: [\n                            flashes.map((flash, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>{\n                                        // Open in shorts view with current index\n                                        window.location.href = \"/home/<USER>/shorts?index=\".concat(index);\n                                    },\n                                    style: {\n                                        background: \"linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)\"\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] p-[6px] sm:p-[10px] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                top: \"10px\",\n                                                left: \"10px\",\n                                                zIndex: 2\n                                            },\n                                            onClick: (e)=>navigateToUserProfile(flash.user_id, flash.user_name, e),\n                                            className: \"jsx-83037452c623c470\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                username: flash.user_name || \"user\",\n                                                size: \"sm\",\n                                                isGradientBorder: true,\n                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length],\n                                                onClick: ()=>navigateToUserProfile(flash.user_id, flash.user_name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                top: \"10px\",\n                                                left: \"50px\",\n                                                zIndex: 2,\n                                                color: \"white\",\n                                                textShadow: \"0px 1px 2px rgba(0,0,0,0.8)\"\n                                            },\n                                            onClick: (e)=>navigateToUserProfile(flash.user_id, flash.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"cursor-pointer hover:underline text-sm font-medium\",\n                                            children: flash.user_name || \"user\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                top: 0,\n                                                left: 0,\n                                                right: 0,\n                                                bottom: 0,\n                                                zIndex: 1\n                                            },\n                                            className: \"jsx-83037452c623c470\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: getImageSource(flash),\n                                                    alt: flash.video_name || \"Flash Video\",\n                                                    fill: true,\n                                                    sizes: \"(max-width: 480px) 120px, (max-width: 640px) 140px, (max-width: 768px) 180px, 200px\",\n                                                    className: \"object-cover\",\n                                                    ...index < 2 ? {\n                                                        priority: true\n                                                    } : {\n                                                        loading: 'lazy'\n                                                    },\n                                                    unoptimized: !flash.video_thumbnail,\n                                                    placeholder: \"blur\" // Show blur placeholder while loading\n                                                    ,\n                                                    blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                    ,\n                                                    onError: (e)=>{\n                                                        const imgElement = e.target;\n                                                        if (imgElement) {\n                                                            imgElement.src = '/pics/placeholder.svg';\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"10px\",\n                                                left: \"10px\",\n                                                right: \"10px\",\n                                                zIndex: 2,\n                                                color: \"white\"\n                                            },\n                                            className: \"jsx-83037452c623c470\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: 500\n                                                    },\n                                                    className: \"jsx-83037452c623c470\",\n                                                    children: flash.video_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\"\n                                                    },\n                                                    className: \"jsx-83037452c623c470\",\n                                                    children: [\n                                                        flash.video_views ? \"\".concat((flash.video_views / 1000).toFixed(1), \"K views\") : '',\n                                                        flash.video_likes ? \" • \".concat((flash.video_likes / 1000).toFixed(1), \"K likes\") : ''\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(flash.video_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: 'relative',\n                                    // Add debug outline in development\n                                    outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"flashes-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[220px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                lineNumber: 358,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n        lineNumber: 308,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashesSection, \"89ZtZLKhzbuJQg3T1Sup04Mqw2Q=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__.useLocation\n    ];\n});\n_c = FlashesSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashesSection);\nvar _c;\n$RefreshReg$(_c, \"FlashesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Flashes.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/HomeDashboard/Photos.tsx":
/*!*********************************************!*\
  !*** ./components/HomeDashboard/Photos.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Photos = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [photos, setPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // Fallback data if API fails - using empty array\n    const getFallbackPhotos = ()=>[];\n    // Function to fetch photos from the API\n    const fetchPhotos = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching photos for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setPhotos([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.photos) {\n                console.log(\"Loaded \".concat(response.data.photos.length, \" photos for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.photos.length > 0) {\n                    console.log('Sample photo data:', response.data.photos[0]);\n                }\n                // Process the response\n                const processedPhotos = response.data.photos.map((photo)=>{\n                    if (!photo.photo_url) {\n                        console.log(\"Photo missing URL: \".concat(photo.photo_id));\n                    }\n                    return photo;\n                });\n                if (pageNumber === 1) {\n                    setPhotos(processedPhotos);\n                } else {\n                    setPhotos((prev)=>[\n                            ...prev,\n                            ...processedPhotos\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load photos - unexpected response format');\n                setPhotos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load photos');\n            setPhotos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of photos as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Photos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial photos load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchPhotos function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for photos page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"Photos.useEffect\": (response)=>{\n                            console.log('API response received for photos page 1');\n                            if (response.data && response.data.photos) {\n                                setPhotos(response.data.photos);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setPhotos([]);\n                                setError('No photos found');\n                            }\n                        }\n                    }[\"Photos.useEffect\"]).catch({\n                        \"Photos.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load photos');\n                            setPhotos([]);\n                        }\n                    }[\"Photos.useEffect\"]).finally({\n                        \"Photos.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"Photos.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"Photos.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"Photos.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchPhotos(nextPage, false);\n                    }\n                }\n            }[\"Photos.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"Photos.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"Photos.useEffect\"];\n        }\n    }[\"Photos.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a photo\n    const getImageSource = (photo)=>{\n        if (photo.photo_url) {\n            return photo.photo_url;\n        }\n        return '/pics/placeholder.svg';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-black\",\n                        children: \"Photos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-1 ml-auto\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading photos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying photos load...');\n                            setError(null);\n                            fetchPhotos(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: 'smooth',\n                    WebkitOverflowScrolling: 'touch',\n                    minHeight: photos.length === 0 && !error ? '220px' : 'auto'\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative\",\n                children: [\n                    photos.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No photos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-4 pb-4 flex-nowrap\",\n                        children: [\n                            photos.map((photo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: '250px',\n                                        height: '200px'\n                                    },\n                                    onClick: ()=>{\n                                        window.location.href = \"/home/<USER>/\".concat(photo.photo_id);\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute top-2 left-2 z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                username: photo.user_name || \"user\",\n                                                size: \"sm\",\n                                                isGradientBorder: true,\n                                                onClick: ()=>navigateToUserProfile(photo.user_id, photo.user_name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: getImageSource(photo),\n                                                alt: photo.photo_name || \"Photo\",\n                                                fill: true,\n                                                sizes: \"(max-width: 640px) 250px, 250px\",\n                                                className: \"object-cover\",\n                                                ...index < 4 ? {\n                                                    priority: true\n                                                } : {\n                                                    loading: 'lazy'\n                                                },\n                                                placeholder: \"blur\" // Show blur placeholder while loading\n                                                ,\n                                                blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                ,\n                                                onError: (e)=>{\n                                                    const imgElement = e.target;\n                                                    if (imgElement) {\n                                                        imgElement.src = '/pics/placeholder.svg';\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-sm font-medium truncate\",\n                                                    children: photo.photo_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-xs flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                                            className: \"jsx-83037452c623c470\" + \" \" + \"cursor-pointer hover:underline\",\n                                                            children: photo.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-83037452c623c470\",\n                                                            children: photo.photo_likes ? \"\".concat(photo.photo_likes, \" likes\") : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(photo.photo_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: 'relative',\n                                    // Add debug outline in development\n                                    outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"photos-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[200px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Photos, \"9Jg+DVptG/SwtKabWduqH6CnTuE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = Photos;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Photos);\nvar _c;\n$RefreshReg$(_c, \"Photos\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Photos.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/HomeDashboard/WeddingVideos.tsx":
/*!****************************************************!*\
  !*** ./components/HomeDashboard/WeddingVideos.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var _contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../contexts/LocationContext */ \"(app-pages-browser)/./contexts/LocationContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_MdChevronLeft_MdChevronRight_react_icons_md__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MdChevronLeft,MdChevronRight!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst WeddingVideosSection = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { selectedLocation } = (0,_contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__.useLocation)();\n    const [likedVideos, setLikedVideos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [admiringUsers, setAdmiringUsers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [weddingVideos, setWeddingVideos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Fallback data if API fails - using empty array\n    const getFallbackMovies = ()=>[];\n    // Function to fetch movies from the API\n    const fetchMovies = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching movies for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setWeddingVideos([]);\n                return;\n            }\n            // Build API URL with location parameter if selected\n            let apiUrl = \"/movies?page=\".concat(pageNumber, \"&limit=10\");\n            if (selectedLocation) {\n                apiUrl += \"&location=\".concat(encodeURIComponent(selectedLocation));\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(apiUrl, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.movies) {\n                console.log(\"Loaded \".concat(response.data.movies.length, \" movies for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.movies.length > 0) {\n                    console.log('Sample movie data:', response.data.movies[0]);\n                }\n                // Process the response\n                const processedMovies = response.data.movies.map((movie)=>{\n                    if (!movie.video_thumbnail) {\n                        console.log(\"Movie missing thumbnail: \".concat(movie.video_id));\n                    }\n                    // Ensure user_id is set - in case the API doesn't provide it\n                    if (!movie.user_id && movie.user_name) {\n                        // Create a temporary user_id based on username if not provided\n                        // This is a fallback and should be replaced with actual user IDs from the API\n                        console.log(\"Movie missing user_id, creating temporary one from username: \".concat(movie.user_name));\n                        movie.user_id = \"user-\".concat(movie.user_name.toLowerCase().replace(/\\s+/g, '-'));\n                    }\n                    return movie;\n                });\n                if (pageNumber === 1) {\n                    setWeddingVideos(processedMovies);\n                } else {\n                    setWeddingVideos((prev)=>[\n                            ...prev,\n                            ...processedMovies\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load wedding videos - unexpected response format');\n                setWeddingVideos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load wedding videos');\n            setWeddingVideos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of movies as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Wedding Videos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial movies load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchMovies function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Build API URL with location parameter if selected\n                    let apiUrl = \"/movies?page=1&limit=10\";\n                    if (selectedLocation) {\n                        apiUrl += \"&location=\".concat(encodeURIComponent(selectedLocation));\n                    }\n                    // Make direct API request\n                    console.log('Making direct API request for movies page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(apiUrl, {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"WeddingVideosSection.useEffect\": (response)=>{\n                            console.log('API response received for movies page 1');\n                            if (response.data && response.data.movies) {\n                                setWeddingVideos(response.data.movies);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setWeddingVideos([]);\n                                setError('No wedding videos found');\n                            }\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]).catch({\n                        \"WeddingVideosSection.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load wedding videos');\n                            setWeddingVideos([]);\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]).finally({\n                        \"WeddingVideosSection.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Failsafe to ensure content is loaded - only show error after timeout\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // If we're stuck in loading state for more than 10 seconds, show error\n            let timeoutId = null;\n            if (loading && initialLoadComplete) {\n                timeoutId = setTimeout({\n                    \"WeddingVideosSection.useEffect\": ()=>{\n                        console.log('Loading timeout reached - API request may have failed');\n                        setLoading(false);\n                        if (weddingVideos.length === 0) {\n                            setError('Unable to load wedding videos. Please check your network connection.');\n                        }\n                    }\n                }[\"WeddingVideosSection.useEffect\"], 10000); // 10 second timeout for slow networks\n            }\n            return ({\n                \"WeddingVideosSection.useEffect\": ()=>{\n                    if (timeoutId) clearTimeout(timeoutId);\n                }\n            })[\"WeddingVideosSection.useEffect\"];\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        loading,\n        initialLoadComplete,\n        weddingVideos.length\n    ]);\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"WeddingVideosSection.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchMovies(nextPage, false);\n                    }\n                }\n            }[\"WeddingVideosSection.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"WeddingVideosSection.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"WeddingVideosSection.useEffect\"];\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Load admiring status from localStorage when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers');\n                if (admiredUsersJson) {\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    setAdmiringUsers(admiredUsers);\n                    console.log('Loaded admiring status from localStorage:', admiredUsers);\n                }\n            } catch (error) {\n                console.error('Error loading admiring status from localStorage:', error);\n            }\n        }\n    }[\"WeddingVideosSection.useEffect\"], []);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username)=>{\n        // If we have a userId, make a direct API call to the user profile endpoint\n        if (userId) {\n            // Get the token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Navigate to the profile page with the userId\n            router.push(\"/profile/\".concat(userId));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    const toggleLike = (videoId)=>{\n        setLikedVideos((prev)=>prev.includes(videoId) ? prev.filter((id)=>id !== videoId) : [\n                ...prev,\n                videoId\n            ]);\n    };\n    // Function to handle Admire button click\n    const handleAdmire = async (userId)=>{\n        if (!userId) {\n            console.warn('No user ID provided for Admire action');\n            return;\n        }\n        // Check if already admiring this user\n        const isCurrentlyAdmiring = admiringUsers[userId] || false;\n        try {\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI state\n            setAdmiringUsers((prev)=>({\n                    ...prev,\n                    [userId]: !isCurrentlyAdmiring\n                }));\n            // Make API call to follow/unfollow user\n            const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n            console.log(\"Making request to \".concat(endpoint, \" with user ID: \").concat(userId));\n            try {\n                // Make the API call\n                const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(endpoint, {\n                    target_user_id: userId\n                }, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('API response:', response.data);\n                // Update localStorage with the new state\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (!isCurrentlyAdmiring) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                    console.log('Updated admired users in localStorage:', admiredUsers);\n                } catch (storageError) {\n                    console.error('Error updating localStorage:', storageError);\n                }\n            } catch (apiError) {\n                console.error(\"Error \".concat(isCurrentlyAdmiring ? 'unadmiring' : 'admiring', \" user:\"), apiError);\n                // Revert UI state on error\n                setAdmiringUsers((prev)=>({\n                        ...prev,\n                        [userId]: isCurrentlyAdmiring\n                    }));\n            }\n        } catch (error) {\n            console.error('Unexpected error in handleAdmire:', error);\n            // Revert UI state on unexpected errors\n            setAdmiringUsers((prev)=>({\n                    ...prev,\n                    [userId]: isCurrentlyAdmiring\n                }));\n        }\n    };\n    const handleManualScroll = (direction)=>{\n        if (scrollContainerRef.current) {\n            const scrollAmount = direction === \"left\" ? -694 : 694; // Width + gap\n            scrollContainerRef.current.scrollBy({\n                left: scrollAmount,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Get appropriate image source for a movie\n    const getImageSource = (video)=>{\n        if (video.video_thumbnail) {\n            return video.video_thumbnail;\n        }\n        return '/pics/placeholder.svg';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-[#000000]\",\n                        children: \"MOVIES\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative ml-auto mr-1\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 436,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading wedding videos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 450,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying wedding videos load...');\n                            setError(null);\n                            fetchMovies(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 458,\n                columnNumber: 9\n            }, undefined),\n            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    weddingVideos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No wedding videos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 13\n                    }, undefined),\n                    weddingVideos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleManualScroll(\"left\"),\n                                style: {\n                                    marginLeft: \"-12px\"\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_react_icons_md__WEBPACK_IMPORTED_MODULE_8__.MdChevronLeft, {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: scrollContainerRef,\n                                style: {\n                                    WebkitOverflowScrolling: \"touch\",\n                                    scrollbarWidth: \"none\",\n                                    msOverflowStyle: \"none\",\n                                    scrollBehavior: 'smooth'\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-3 pb-5 flex-nowrap w-full\",\n                                    children: [\n                                        weddingVideos.map((video, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px] max-w-[676px] border-b border-[#DBDBDB] pb-[15px] sm:pb-[21px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-full h-[40px] sm:h-[50px] md:h-[56px] flex justify-between items-center p-[5px] sm:p-[8px_5px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center gap-[10px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        onClick: ()=>navigateToUserProfile(video.user_id, video.user_name),\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-[34px] h-[34px] rounded-[27px] border cursor-pointer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            username: video.user_name || \"user\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>navigateToUserProfile(video.user_id, video.user_name)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-full max-w-[380px] h-[40px] pt-[7px] pr-[50px] sm:pr-[100px] md:pr-[150px] lg:pr-[200px] pb-[10px]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontFamily: \"Inter\",\n                                                                                fontWeight: 600,\n                                                                                fontSize: \"14px\",\n                                                                                lineHeight: \"18px\",\n                                                                                letterSpacing: \"0%\",\n                                                                                verticalAlign: \"middle\",\n                                                                                cursor: \"pointer\"\n                                                                            },\n                                                                            onClick: ()=>navigateToUserProfile(video.user_id, video.user_name),\n                                                                            className: \"jsx-83037452c623c470\" + \" \" + \"hover:underline\",\n                                                                            children: video.user_name || \"user\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 538,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleAdmire(video.user_id),\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center bg-[#B31B1E] text-white px-3 py-1 rounded-full text-sm font-medium hover:bg-red-700 transition-colors\",\n                                                                        children: [\n                                                                            admiringUsers[video.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[video.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-83037452c623c470\" + \" \" + \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                                lineNumber: 565,\n                                                                                columnNumber: 69\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: \"•••\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[180px] sm:h-[250px] md:h-[300px] lg:h-[350px] max-w-[676px] rounded-[10px] border p-[1px] relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full rounded-[10px] overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: getImageSource(video),\n                                                                    alt: video.video_name || \"Wedding Video\",\n                                                                    fill: true,\n                                                                    sizes: \"(max-width: 480px) 300px, (max-width: 640px) 350px, (max-width: 768px) 450px, (max-width: 1024px) 550px, 676px\",\n                                                                    className: \"object-cover\",\n                                                                    ...index < 2 ? {\n                                                                        priority: true\n                                                                    } : {\n                                                                        loading: 'lazy'\n                                                                    },\n                                                                    unoptimized: true,\n                                                                    placeholder: \"blur\" // Show blur placeholder while loading\n                                                                    ,\n                                                                    blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                                    ,\n                                                                    onError: (e)=>{\n                                                                        console.error(\"Failed to load image for video: \".concat(video.video_name));\n                                                                        // Use placeholder as fallback\n                                                                        const imgElement = e.target;\n                                                                        if (imgElement) {\n                                                                            imgElement.src = '/pics/placeholder.svg';\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: \"absolute\",\n                                                                    top: 0,\n                                                                    left: 0,\n                                                                    right: 0,\n                                                                    bottom: 0,\n                                                                    zIndex: 2,\n                                                                    borderRadius: \"10px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"center\",\n                                                                    cursor: \"pointer\",\n                                                                    backgroundColor: \"rgba(0, 0, 0, 0.2)\"\n                                                                },\n                                                                onClick: ()=>{\n                                                                    // Navigate to the movie detail page\n                                                                    if (video.video_id) {\n                                                                        console.log(\"Navigating to movie detail page: \".concat(video.video_id));\n                                                                        window.location.href = \"/home/<USER>/\".concat(video.video_id);\n                                                                    }\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-83037452c623c470\" + \" \" + \"w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"28\",\n                                                                        height: \"28\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"white\",\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M8 5v14l11-7z\",\n                                                                            className: \"jsx-83037452c623c470\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 630,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"                      \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            padding: \"12px 0\",\n                                                            fontSize: \"14px\"\n                                                        },\n                                                        className: \"jsx-83037452c623c470\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-83037452c623c470\",\n                                                            children: video.video_description || video.video_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, \"\".concat(video.video_id, \"-\").concat(index), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 21\n                                            }, undefined)),\n                                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: loadMoreTriggerRef,\n                                            style: {\n                                                position: 'relative',\n                                                // Add debug outline in development\n                                                outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                            },\n                                            \"aria-hidden\": \"true\",\n                                            \"data-testid\": \"wedding-videos-load-more-trigger\",\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                                    children: \"Loading more...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleManualScroll(\"right\"),\n                                style: {\n                                    marginRight: \"-12px\"\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_react_icons_md__WEBPACK_IMPORTED_MODULE_8__.MdChevronRight, {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n        lineNumber: 435,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WeddingVideosSection, \"bX5p5qDDcg7kQLkQFBK7VtUx4QI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__.useLocation\n    ];\n});\n_c = WeddingVideosSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WeddingVideosSection);\nvar _c;\n$RefreshReg$(_c, \"WeddingVideosSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/WeddingVideos.tsx\n"));

/***/ })

});