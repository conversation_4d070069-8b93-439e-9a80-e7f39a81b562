{"node": {"7f147ca738036699110e3c702d45bfd4854b1f734d": {"workers": {"app/home/<USER>/[id]/page": {"moduleId": "[project]/.next-internal/server/app/home/<USER>/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/home/<USER>/[id]/page": "action-browser"}}, "7fde2e55193fcdf0b69fb796d6b5071266b9dbbd9f": {"workers": {"app/home/<USER>/[id]/page": {"moduleId": "[project]/.next-internal/server/app/home/<USER>/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/home/<USER>/[id]/page": "action-browser"}}, "7f4b2866c1c0e9ac214163ee893952ffb5c22f29e9": {"workers": {"app/home/<USER>/[id]/page": {"moduleId": "[project]/.next-internal/server/app/home/<USER>/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/home/<USER>/[id]/page": "action-browser"}}, "7f5025ac8dd733a7d6673872224880481bb2c2602c": {"workers": {"app/home/<USER>/[id]/page": {"moduleId": "[project]/.next-internal/server/app/home/<USER>/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/home/<USER>/[id]/page": "action-browser"}}}, "edge": {}}