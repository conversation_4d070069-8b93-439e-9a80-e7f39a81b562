from codebase.authentication import signup, login, clerk_auth, check_profile, get_user_details, update_user 
from codebase.presigned_uploads import get_presigned_url, complete_upload, verify_face
from codebase.user_stats import follow_user, unfollow_user, get_user_follow_stats
from codebase.video_management import delete_video, get_videos, get_video, search_videos
from codebase.photo_management import delete_photo, get_photos, get_photo, search_photos
from codebase.engagement import like_content, unlike_content, comment_content, get_comments, delete_comment

# Import new homepage APIs
from codebase.homepage_api import check_face_verification, get_stories,get_my_stories, get_flashes, get_glimpses, get_movies, get_photos

# Import new profile APIs
from codebase.profile_api import get_user_stories, get_user_flashes, get_user_glimpses, get_user_movies, get_user_photos

# Import search APIs
from codebase.search_api import search_content, get_search_suggestions

import json
from flask import Flask, request
from flask_cors import CORS
app = Flask(__name__)
CORS(app)

if __name__ == '__main__':
    app.run()

def lambda_handler(event, context):
    try:
        method = event['requestContext']['httpMethod']
        resource_path = event['requestContext']['resourcePath']
        
        # Route to the appropriate function based on method and path
        if method == 'POST':
            # Authentication endpoints
            if resource_path == '/hub/signup':
                return signup(event)
            elif resource_path == '/hub/login':
                return login(event)
            elif resource_path == '/hub/auth-clerk':
                return clerk_auth(event)
            elif resource_path == '/hub/verify-face':
                return verify_face(event)
            
            # Media upload endpoints
            elif resource_path == '/hub/get-upload-url':
                return get_presigned_url(event)
            elif resource_path == '/hub/complete-upload':
                return complete_upload(event)
            
            # User stats endpoints
            elif resource_path == '/hub/follow':
                return follow_user(event)
            elif resource_path == '/hub/unfollow':
                return unfollow_user(event)
            
            # Engagement endpoints
            elif resource_path == '/hub/like':
                return like_content(event)
            elif resource_path == '/hub/unlike':
                return unlike_content(event)
            elif resource_path == '/hub/comment':
                return comment_content(event)
            
            # Not found
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Resource not found"})
                }
            
        elif method == 'GET':
            # User endpoints
            if resource_path == '/hub/check-profile':
                return check_profile(event)
            elif resource_path == '/hub/user-details':
                return get_user_details(event)
            elif resource_path == '/hub/user-stats':
                return get_user_follow_stats(event)
            
            # Home page content endpoints (new)
            elif resource_path == '/hub/check-face-verification':
                return check_face_verification(event)
            elif resource_path == '/hub/stories':
                return get_stories(event)
            elif resource_path == '/hub/mystories':
                return get_my_stories(event)
            elif resource_path == '/hub/flashes':
                return get_flashes(event)
            elif resource_path == '/hub/glimpses':
                return get_glimpses(event)
            elif resource_path == '/hub/movies':
                return get_movies(event)
            elif resource_path == '/hub/photos':
                return get_photos(event)
            
            # Profile page content endpoints (new)
            elif resource_path == '/hub/user-stories':
                return get_user_stories(event)
            elif resource_path == '/hub/user-flashes':
                return get_user_flashes(event)
            elif resource_path == '/hub/user-glimpses':
                return get_user_glimpses(event)
            elif resource_path == '/hub/user-movies':
                return get_user_movies(event)
            elif resource_path == '/hub/user-photos':
                return get_user_photos(event)
            
            # Existing video endpoints
            elif resource_path == '/hub/videos':
                return get_videos(event)
            elif resource_path == '/hub/video/{video_id}':
                return get_video(event)
            elif resource_path == '/hub/search-videos':
                return search_videos(event)
            
            # Existing photo endpoints
            elif resource_path == '/hub/photos':
                return get_photos(event)
            elif resource_path == '/hub/photo/{photo_id}':
                return get_photo(event)
            elif resource_path == '/hub/search-photos':
                return search_photos(event)
            
            # Comment endpoints
            elif resource_path == '/hub/comments':
                return get_comments(event)

            # Search endpoints
            elif resource_path == '/hub/search':
                return search_content(event)
            elif resource_path == '/hub/search-suggestions':
                return get_search_suggestions(event)

            # Not found
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Resource not found"})
                }
            
        elif method == 'PUT':
            # User endpoints
            if resource_path == '/hub/update-user':
                return update_user(event)
            
            # Not found
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Resource not found"})
                }
            
        elif method == 'DELETE':
            # Media deletion endpoints
            if resource_path == '/hub/delete-video':
                return delete_video(event)
            elif resource_path == '/hub/delete-photo':
                return delete_photo(event)
            elif resource_path == '/hub/delete-comment':
                return delete_comment(event)
            
            # Not found
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Resource not found"})
                }
            
        else:
            return {
                'statusCode': 405,
                'body': json.dumps({"error": "Method not allowed"})
            }
            
    except Exception as e:
        print(f"Error in lambda_handler: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }