'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import axios from 'axios';
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  TopNavigation,
  SideNavigation,
  MobileNavigation,
  RightSidebar,
} from "../../../../components/HomeDashboard/Navigation";
import UserAvatar from "../../../../components/HomeDashboard/UserAvatar";
import Photos from "../../../../components/HomeDashboard/Photos";
import DetailPageLazyLoad from "../../../../components/DetailPageLazyLoad";
import { useRef as reactUseRef } from 'react';

// Define interface for photo details - Updated to match backend response
interface PhotoDetails {
  photo_id: string;
  photo_name: string;
  photo_url: string;
  photo_description?: string;
  photo_tags?: string[];
  photo_category?: string;
  created_at?: string;
  user_id?: string;
  username?: string;
  photo_views?: number;
  photo_likes?: number;
  photo_comments?: number;
  liked?: boolean;
  is_own_content?: boolean;
  comments?: PhotoComment[];
}

interface PhotoComment {
  comment_id: string;
  user_id: string;
  username: string;
  comment_text: string;
  created_at: string;
}

// Main component for the photo viewing page
export default function PhotoViewPage() {
  const params = useParams();
  const router = useRouter();
  const photoId = params?.id as string;

  const [photo, setPhoto] = useState<PhotoDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [rightSidebarExpanded, setRightSidebarExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [relatedPhotos, setRelatedPhotos] = useState<any[]>([]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLiking, setIsLiking] = useState(false);
  const imageRef = useRef<HTMLDivElement>(null);

  // Set client-side rendering flag
  useEffect(() => setIsClient(true), []);

  // Fetch photo details when component mounts
  useEffect(() => {
    const fetchPhotoDetails = async () => {
      console.log('=== Starting photo fetch ===');
      console.log('Photo ID:', photoId);

      setLoading(true);
      setError(null);

      try {
        // Get token from localStorage
        const token = localStorage.getItem('token') ||
                     localStorage.getItem('jwt_token') ||
                     localStorage.getItem('wedzat_token');

        console.log('Token found:', token ? 'Yes' : 'No');
        if (token) {
          console.log('Token preview:', token.substring(0, 50) + '...');
          console.log('Token length:', token.length);
          console.log('Token starts with eyJ (JWT):', token.startsWith('eyJ'));
          console.log('Full token:', token);
        }

        if (!token) {
          console.warn('No authentication token found');
          setError('Authentication required. Please log in.');
          setLoading(false);
          return;
        }

        // Store token in a cookie for server components to access
        document.cookie = `token=${token}; path=/; max-age=3600; SameSite=Strict`;

        // First test if the photos list API is working
        console.log('Testing photos list API first...');
        try {
          const photosTestResponse = await axios.get('/api/photos?page=1&limit=1', {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
          console.log('Photos list API test successful:', photosTestResponse.status);
        } catch (photosError: any) {
          console.error('Photos list API test failed:', photosError.response?.data || photosError.message);
          throw new Error('Photos list API is also failing: ' + (photosError.response?.data?.error || photosError.message));
        }

        // Try to get the photo from the photos list first as a workaround
        console.log('Trying to get photo from photos list...');
        let photoFromList = null;
        try {
          const photosResponse = await axios.get('/api/photos?page=1&limit=50', {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });

          if (photosResponse.data && photosResponse.data.photos) {
            photoFromList = photosResponse.data.photos.find((p: any) => p.photo_id === photoId);
            if (photoFromList) {
              console.log('Found photo in photos list:', photoFromList);
              // Add some default values that might be missing
              photoFromList.liked = false;
              photoFromList.comments = [];
            }
          }
        } catch (listError) {
          console.error('Failed to get photo from list:', listError);
        }

        // Try the individual photo API first, but fall back to photos list if it fails
        let response;
        try {
          response = await axios.get(`/api/photo/${photoId}`, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });

          // Check if the response contains an error
          if (response.data?.error) {
            throw new Error(response.data.error);
          }
        } catch (individualError: any) {
          console.error('Individual photo API failed:', {
            error: individualError,
            response: individualError.response?.data,
            message: individualError.message,
            status: individualError.response?.status
          });

          // If individual photo API fails, try to get the photo from the photos list
          if (photoFromList) {
            console.log('Using photo data from photos list as fallback');
            response = { data: photoFromList };
          } else {
            // Try to fetch from photos list API as a last resort
            try {
              console.log('Attempting to fetch photo from photos list API...');
              const photosResponse = await axios.get('/api/photos?page=1&limit=50', {
                headers: {
                  Authorization: `Bearer ${token}`
                }
              });

              const foundPhoto = photosResponse.data?.photos?.find((p: any) => p.photo_id === photoId);
              if (foundPhoto) {
                console.log('Found photo in photos list, using as fallback');
                response = { data: foundPhoto };
              } else {
                throw new Error('Photo not found in photos list');
              }
            } catch (listError: any) {
              console.error('Photos list API also failed:', {
                error: listError,
                response: listError.response?.data,
                message: listError.message,
                status: listError.response?.status
              });
              throw new Error('Failed to load photo: Both individual photo API and photos list API failed');
            }
          }
        }

        if (response.data) {
          if (response.data.error) {
            throw new Error(response.data.error);
          }

          // The backend returns the photo data directly
          const photoData = response.data;

          if (!photoData.photo_id) {
            throw new Error('Invalid photo data received - missing photo_id');
          }

          setPhoto(photoData);
          setIsLiked(photoData.liked || false);
          setLikeCount(photoData.photo_likes || 0);

          // Fetch related photos
          fetchRelatedPhotos(photoData.photo_category, photoData.user_id);
        } else {
          throw new Error('No data received from server');
        }
      } catch (err: any) {
        console.error('Error fetching photo details:', {
          error: err,
          message: err.message,
          response: err.response?.data,
          status: err.response?.status,
          stack: err.stack
        });

        let errorMessage = 'Failed to load photo. Please try again later.';

        if (err.response?.status === 401) {
          errorMessage = 'Authentication failed. Please log in again.';
        } else if (err.response?.status === 404) {
          errorMessage = 'Photo not found.';
        } else if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err.message) {
          errorMessage = err.message;
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (photoId && isClient) {
      fetchPhotoDetails();
    }
  }, [photoId, isClient]);

  // Function to fetch related photos
  const fetchRelatedPhotos = async (category?: string, userId?: string) => {
    try {
      const token = localStorage.getItem('token');

      if (!token) return;

      // Prioritize category if available, otherwise fetch user's photos
      const queryParam = category
        ? `?category=${encodeURIComponent(category)}&limit=6`
        : userId
          ? `?user_id=${encodeURIComponent(userId)}&limit=6`
          : '?limit=6';

      const response = await axios.get(`/api/photos${queryParam}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data && response.data.photos) {
        // Filter out the current photo
        const filtered = response.data.photos.filter(
          (p: any) => p.photo_id !== photoId
        );
        setRelatedPhotos(filtered.slice(0, 5)); // Limit to 5 related photos
      }
    } catch (err) {
      console.error('Error fetching related photos:', err);
    }
  };

  // Function to handle like/unlike
  const handleLikeToggle = async () => {
    if (isLiking) return; // Prevent multiple clicks

    console.log('📸 Individual photo like button clicked for ID:', photoId);
    try {
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('📸 No authentication token found');
        return;
      }

      setIsLiking(true);

      // Optimistically update UI
      const newLikedState = !isLiked;
      setIsLiked(newLikedState);
      setLikeCount(prev => newLikedState ? prev + 1 : prev - 1);

      // Make API request to like/unlike
      const endpoint = newLikedState ? '/api/like' : '/api/unlike';
      console.log('📸 Making API call to:', endpoint);

      const response = await axios.post(endpoint, {
        content_id: photoId,
        content_type: 'photo'
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('📸 API Success Response:', response.data);

      // Update localStorage for persistence
      const likedPhotosData = localStorage.getItem('likedPhotos');
      const likedPhotosArray = likedPhotosData ? JSON.parse(likedPhotosData) : [];

      if (newLikedState) {
        // Add to localStorage
        if (!likedPhotosArray.includes(photoId)) {
          likedPhotosArray.push(photoId);
          localStorage.setItem('likedPhotos', JSON.stringify(likedPhotosArray));
          console.log('📸 Added to localStorage');
        }
      } else {
        // Remove from localStorage
        const updatedArray = likedPhotosArray.filter((id: string) => id !== photoId);
        localStorage.setItem('likedPhotos', JSON.stringify(updatedArray));
        console.log('📸 Removed from localStorage');
      }

      console.log(`📸 Successfully ${newLikedState ? 'liked' : 'unliked'} photo: ${photoId}`);
    } catch (err) {
      console.error('Error toggling like:', err);
      // Revert optimistic update on error
      setIsLiked(!isLiked);
      setLikeCount(prev => isLiked ? prev + 1 : prev - 1);
    } finally {
      setIsLiking(false);
    }
  };

  // Function to toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Function to navigate back
  const goBack = () => {
    router.back();
  };

  // Add this function near your other utility functions
  const getImageSource = (photoUrl: string): string => {
    if (!photoUrl || photoUrl.trim() === '') {
      console.warn('Empty photo URL detected');
      return '/pics/placeholder.svg';
    }
    return photoUrl;
  };

  return (
    <div
      className={
        isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"
      }
    >
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 py-4 pr-4 pl-0 bg-white ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            marginTop: "80px",
            transition: "all 300ms ease-in-out",
            minHeight: "calc(100vh - 80px)",
            paddingBottom: "40px",
            overflowY: "auto",
            overflowX: "hidden",
            marginRight: rightSidebarExpanded ? "320px" : "0",
            paddingRight: "20px",
            paddingLeft: "0",
          }}
        >
          {/* Content Container */}
          <div className="flex flex-col gap-8 max-w-[1100px] w-full pl-2">
            {/* Loading state */}
            {loading && (
              <div className="py-10 text-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading photo...</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center">
                <div className="text-red-500 mb-4">{error}</div>
                <button
                  onClick={goBack}
                  className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                >
                  Go Back
                </button>
              </div>
            )}

            {/* Photo content */}
            {!loading && !error && photo && (
              <div className="flex flex-col w-full">
                {/* Back button */}
                <div className="mb-4">
                  <button
                    onClick={goBack}
                    className="flex items-center text-gray-600 hover:text-red-500"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1"
                    >
                      <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                    Back
                  </button>
                </div>

                {/* Photo title and user info */}
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h1 className="text-2xl font-semibold">{photo.photo_name}</h1>
                    {photo.created_at && (
                      <p className="text-sm text-gray-500 mt-1">
                        {new Date(photo.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center">
                    <UserAvatar
                      username={photo.username || "user"}
                      size="md"
                      isGradientBorder={true}
                    />
                    <span className="ml-2 text-sm font-medium">
                      {photo.username}
                    </span>
                  </div>
                </div>

                {/* Photo container */}
                <div
                  ref={imageRef}
                  className={`relative ${
                    isFullscreen
                      ? "fixed inset-0 z-50 bg-black flex items-center justify-center"
                      : "w-full rounded-lg overflow-hidden shadow-lg"
                  }`}
                >
                  <div
                    className={`relative ${
                      isFullscreen
                        ? "w-full h-full"
                        : "w-full"
                    }`}
                    style={{
                      height: isFullscreen ? "100vh" : "calc(100vh - 300px)",
                      maxHeight: isFullscreen ? "100vh" : "70vh",
                    }}
                  >
                    <Image
                      src={getImageSource(photo.photo_url)}
                      alt={photo.photo_name || "Photo"}
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 80vw, 70vw"
                      className="object-contain"
                      priority
                      unoptimized={true}
                      onError={(e) => {
                        console.error(`Failed to load image: ${photo.photo_url}`);
                        const imgElement = e.target as HTMLImageElement;
                        imgElement.src = "/pics/placeholder.svg";
                      }}
                    />
                  </div>

                  {/* Fullscreen toggle button */}
                  <button
                    onClick={toggleFullscreen}
                    className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                    aria-label={isFullscreen ? "Exit fullscreen" : "View fullscreen"}
                  >
                    {isFullscreen ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="4 14 10 14 10 20"></polyline>
                        <polyline points="20 10 14 10 14 4"></polyline>
                        <line x1="14" y1="10" x2="21" y2="3"></line>
                        <line x1="3" y1="21" x2="10" y2="14"></line>
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="15 3 21 3 21 9"></polyline>
                        <polyline points="9 21 3 21 3 15"></polyline>
                        <line x1="21" y1="3" x2="14" y2="10"></line>
                        <line x1="3" y1="21" x2="10" y2="14"></line>
                      </svg>
                    )}
                  </button>

                  {/* Close button for fullscreen mode */}
                  {isFullscreen && (
                    <button
                      onClick={toggleFullscreen}
                      className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                      aria-label="Close fullscreen"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Photo details and interactions */}
            {!loading && !error && photo && !isFullscreen && (
              <div className="mt-6 flex flex-col gap-6">
                {/* Engagement stats and actions */}
                <div className="flex items-center gap-6 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                    <span>{photo.photo_views || 0} views</span>
                  </div>

                  <button
                    className={`flex items-center gap-1 hover:text-red-500 ${isLiking ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={handleLikeToggle}
                    disabled={isLiking}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill={isLiked ? "currentColor" : "none"}
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className={isLiked ? "text-red-500" : ""}
                    >
                      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                    </svg>
                    <span>{likeCount} likes</span>
                  </button>

                  <div className="flex items-center gap-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                    </svg>
                    <span>{photo.photo_comments || 0} comments</span>
                  </div>
                </div>

                {/* Photo description */}
                {photo.photo_description && photo.photo_description.trim() && (
                  <div className="mt-2">
                    <h3 className="text-lg font-medium mb-1">Description</h3>
                    <p className="text-gray-700">{photo.photo_description}</p>
                  </div>
                )}

                {/* Photo tags */}
                {photo.photo_tags && photo.photo_tags.length > 0 && (
                  <div className="mt-2">
                    <h3 className="text-lg font-medium mb-1">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {photo.photo_tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 rounded-full text-sm text-gray-700"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Comments section */}
                {photo.comments && photo.comments.length > 0 && (
                  <div className="mt-4">
                    <h3 className="text-lg font-medium mb-2">Comments</h3>
                    <div className="space-y-3">
                      {photo.comments.map((comment) => (
                        <div key={comment.comment_id} className="flex gap-3 p-3 bg-gray-50 rounded-lg">
                          <UserAvatar
                            username={comment.username}
                            size="sm"
                            isGradientBorder={false}
                          />
                          <div>
                            <div className="font-medium text-sm">{comment.username}</div>
                            <div className="text-gray-700">{comment.comment_text}</div>
                            <div className="text-xs text-gray-500 mt-1">
                              {new Date(comment.created_at).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Related photos section */}
                {relatedPhotos.length > 0 && (
                  <div className="mt-8">
                    <h3 className="text-lg font-medium mb-4">Related Photos</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                      {relatedPhotos.map((relatedPhoto, index) => (
                        <div
                          key={`${relatedPhoto.photo_id}-${index}`}
                          className="rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer"
                          style={{ height: "160px" }}
                          onClick={() => {
                            window.location.href = `/home/<USER>/${relatedPhoto.photo_id}`;
                          }}
                        >
                          <div className="relative w-full h-full">
                            <Image
                              src={relatedPhoto.photo_url || "/pics/placeholder.svg"}
                              alt={relatedPhoto.photo_name || "Related photo"}
                              fill
                              sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw"
                              className="object-cover"
                              unoptimized={true}
                              onError={(e) => {
                                const imgElement = e.target as HTMLImageElement;
                                if (imgElement) {
                                  imgElement.src = '/pics/placeholder.svg';
                                }
                              }}
                            />
                          </div>
                          <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white">
                            <div className="text-sm font-medium truncate">{relatedPhoto.photo_name}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* More photos section */}
                <div className="mt-8">
                  <h3 className="text-lg font-medium mb-4">More Photos</h3>
                  <DetailPageLazyLoad id="more-photos-section" index={1} rootMargin="0px 0px 200px 0px">
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <Photos shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>
                </div>
              </div>
            )}
          </div>
        </main>

        <RightSidebar
          expanded={rightSidebarExpanded}
          onExpand={() => setRightSidebarExpanded(true)}
          onCollapse={() => setRightSidebarExpanded(false)}
        />
      </div>

      <MobileNavigation />
    </div>
  );
}





function useRef<T>(initialValue: T | null) {
  return reactUseRef<T>(initialValue);
}




