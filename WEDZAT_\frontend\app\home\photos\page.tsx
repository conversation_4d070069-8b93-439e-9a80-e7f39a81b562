"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";

// Define interface for photo items
interface Photo {
  photo_id: string;
  photo_name: string;
  photo_url: string;
  photo_description?: string;
  photo_tags?: string[];
  photo_subtype?: string;
  created_at: string;
  user_name?: string;
  is_own_content?: boolean;
  photo_views?: number;
  photo_likes?: number;
  photo_comments?: number;
}

interface ApiResponse {
  photos: Photo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

export default function PhotosPage() {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [likedPhotos, setLikedPhotos] = useState<Set<string>>(new Set());
  // Removed right sidebar state

  useEffect(() => {
    setIsClient(true);

    // FORCE CLEAR: Remove all like data to fix red button issue
    console.log('📸 FORCE CLEARING all like data to fix red buttons');
    localStorage.removeItem('likedPhotos');

    // Initialize liked photos as empty set
    setLikedPhotos(new Set());

    // Add debug function to window for clearing like states
    if (typeof window !== 'undefined') {
      (window as any).clearPhotoLikes = () => {
        localStorage.removeItem('likedPhotos');
        console.log('📸 Cleared photo like states from localStorage');
        window.location.reload();
      };
    }
  }, []);

  // Process image URL to handle different URL formats (same pattern as working components)
  const processImageUrl = (url: string): string => {
    if (!url) return '/pics/placeholder.svg';

    // If it's already a local URL starting with /, return as is
    if (url.startsWith('/')) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
      return url;
    }

    // For other external URLs (https://, http://), return as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // For relative paths like 'pics/image.png', add leading slash
    if (!url.startsWith('/')) {
      return `/${url}`;
    }

    return url;
  };

  // Fetch photos from the API
  useEffect(() => {
    const fetchPhotos = async () => {
      try {
        setLoading(true);
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          console.warn('No authentication token found');
          setError('Authentication required');
          return;
        }

        const response = await axios.get<ApiResponse>(`/api/photos?page=${page}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.photos) {
          console.log('Photos API response:', response.data);

          if (page === 1) {
            setPhotos(response.data.photos);
          } else {
            setPhotos(prev => [...prev, ...response.data.photos]);
          }

          setHasMore(response.data.next_page);
        } else {
          console.warn('Unexpected API response format:', response.data);
          setError('Failed to load photos');
        }
      } catch (err) {
        console.error('Error fetching photos:', err);
        setError('Failed to load photos');
      } finally {
        setLoading(false);
      }
    };

    fetchPhotos();
  }, [page]);

  // Reference for the last item in the list
  const lastPhotoRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last photo is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.5, rootMargin: '0px 0px 200px 0px' } // Load when item is 50% visible or 200px before it comes into view
    );

    // Get the last item element
    const lastElement = lastPhotoRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, photos.length]);

  // Toggle like for photos
  const toggleLike = async (photoId: string) => {
    console.log('📸 Photo like button clicked for ID:', photoId);
    try {
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('📸 No authentication token found');
        return;
      }

      // Optimistically update UI
      const isCurrentlyLiked = likedPhotos.has(photoId);
      console.log('📸 Current like status:', isCurrentlyLiked);

      setLikedPhotos((prev) => {
        const newLiked = new Set(prev);
        if (isCurrentlyLiked) {
          newLiked.delete(photoId);
        } else {
          newLiked.add(photoId);
        }
        return newLiked;
      });

      // Make API call to Next.js API routes
      const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';
      console.log('📸 Making API call to:', endpoint);

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content_id: photoId,
          content_type: 'photo'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('📸 API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('📸 API Success Response:', responseData);

      // Update localStorage for persistence
      const likedPhotosData = localStorage.getItem('likedPhotos');
      const likedPhotosArray = likedPhotosData ? JSON.parse(likedPhotosData) : [];

      if (isCurrentlyLiked) {
        // Remove from localStorage
        const updatedArray = likedPhotosArray.filter((id: string) => id !== photoId);
        localStorage.setItem('likedPhotos', JSON.stringify(updatedArray));
        console.log('📸 Removed from localStorage');
      } else {
        // Add to localStorage
        if (!likedPhotosArray.includes(photoId)) {
          likedPhotosArray.push(photoId);
          localStorage.setItem('likedPhotos', JSON.stringify(likedPhotosArray));
          console.log('📸 Added to localStorage');
        }
      }

      console.log(`📸 Successfully ${isCurrentlyLiked ? 'unliked' : 'liked'} photo: ${photoId}`);
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert optimistic update on error
      setLikedPhotos((prev) => {
        const newLiked = new Set(prev);
        if (likedPhotos.has(photoId)) {
          newLiked.delete(photoId);
        } else {
          newLiked.add(photoId);
        }
        return newLiked;
      });
    }
  };

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">Photos</h1>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading photos</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {photos.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {photos.map((photo, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === photos.length - 1;

                  return (
                  <div
                    key={`${photo.photo_id}-${index}`}
                    // Apply ref to the last item for intersection observer
                    ref={isLastItem ? lastPhotoRef : null}
                    className="rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer"
                    onClick={() => {
                      window.location.href = `/home/<USER>/${photo.photo_id}`;
                    }}
                  >
                    {/* User avatar */}
                    <div className="absolute top-2 left-2 z-10">
                      <UserAvatar
                        username={photo.user_name || "user"}
                        size="sm"
                        isGradientBorder={true}
                      />
                    </div>

                    {/* Photo */}
                    <div className="relative w-full h-48">
                      <Image
                        src={processImageUrl(photo.photo_url)}
                        alt={photo.photo_name || "Photo"}
                        fill
                        sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw"
                        className="object-cover"
                        priority={index < 10} // Only prioritize the first ten images
                        unoptimized={true} // Always unoptimized for better compatibility
                        onError={(e) => {
                          console.error(`Failed to load photo: ${photo.photo_name}, URL: ${photo.photo_url}`);
                          // Use placeholder as fallback
                          const imgElement = e.target as HTMLImageElement;
                          if (imgElement) {
                            imgElement.src = '/pics/placeholder.svg';
                          }
                        }}
                      />
                    </div>

                    {/* Photo info */}
                    <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white">
                      <div className="text-sm font-medium truncate">{photo.photo_name}</div>
                      <div className="flex items-center justify-between">
                        <div className="text-xs">
                          <div>{photo.user_name}</div>
                          <div>
                            {photo.photo_likes ? `${photo.photo_likes} likes` : ''}
                          </div>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleLike(photo.photo_id);
                          }}
                          className="flex items-center space-x-1 text-white opacity-80 hover:opacity-100 transition-opacity"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill={likedPhotos.has(photo.photo_id) ? "#B31B1E" : "none"}
                            stroke={likedPhotos.has(photo.photo_id) ? "#B31B1E" : "white"}
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && photos.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more photos to load</div>
            )}

            {/* No content state */}
            {!loading && photos.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No photos available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
