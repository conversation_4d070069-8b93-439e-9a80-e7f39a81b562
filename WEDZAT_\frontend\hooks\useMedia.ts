import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { uploadService } from '../services/api';

// Query keys for media
export const mediaKeys = {
  all: ['media'] as const,
  photos: () => [...mediaKeys.all, 'photos'] as const,
  videos: () => [...mediaKeys.all, 'videos'] as const,
  photo: (id: string) => [...mediaKeys.photos(), id] as const,
  video: (id: string) => [...mediaKeys.videos(), id] as const,
};

// Cache media URLs with their access times
const mediaUrlCache = new Map<string, { url: string; accessTime: number }>();

// Function to get cached URL or fetch new one
const getCachedUrl = (url: string): string | null => {
  const cached = mediaUrlCache.get(url);
  if (cached) {
    const now = Date.now();
    // Check if URL is less than 1 hour old
    if (now - cached.accessTime < 60 * 60 * 1000) {
      cached.accessTime = now; // Update access time
      return cached.url;
    }
    mediaUrlCache.delete(url); // Remove expired URL
  }
  return null;
};

// Hook for managing media uploads with caching
export const useMediaUpload = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      file,
      mediaType,
      category,
      title,
      description,
      tags,
      details,
      duration,
      thumbnail,
      onProgress,
    }: {
      file: File;
      mediaType: 'photo' | 'video';
      category: string;
      title: string;
      description?: string;
      tags?: string[];
      details?: Record<string, string>;
      duration?: number;
      thumbnail?: File | null;
      onProgress?: (progress: number) => void;
    }) => {
      const response = await uploadService.handleUpload(
        file,
        mediaType,
        category,
        title,
        description,
        tags,
        details,
        duration,
        thumbnail,
        onProgress
      );

      // Cache the URL if it's available
      if (response.url) {
        mediaUrlCache.set(response.url, {
          url: response.url,
          accessTime: Date.now(),
        });
      }

      return response;
    },
    onSuccess: (data) => {
      // Invalidate relevant queries based on media type
      const mediaType = data.media_type || (data.photo_id ? 'photo' : 'video');
      queryClient.invalidateQueries({
        queryKey: mediaType === 'photo' ? mediaKeys.photos() : mediaKeys.videos(),
      });
    },
  });
};

// Hook for pre-caching media URLs
export const usePreloadMedia = (urls: string[]) => {
  return useQuery({
    queryKey: ['preload', urls],
    queryFn: async () => {
      const results = await Promise.all(
        urls.map(async (url) => {
          // Check cache first
          const cachedUrl = getCachedUrl(url);
          if (cachedUrl) {
            return { url, cached: true };
          }

          try {
            // Preload image or video
            if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
              await new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = resolve;
                img.onerror = reject;
                img.src = url;
              });
            } else if (url.match(/\.(mp4|webm|mov)$/i)) {
              await new Promise((resolve, reject) => {
                const video = document.createElement('video');
                video.preload = 'metadata';
                video.onloadedmetadata = resolve;
                video.onerror = reject;
                video.src = url;
              });
            }

            // Cache the URL
            mediaUrlCache.set(url, {
              url,
              accessTime: Date.now(),
            });

            return { url, cached: false };
          } catch (error) {
            console.error(`Error preloading media: ${url}`, error);
            return { url, error };
          }
        })
      );

      return results;
    },
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 30 * 60 * 1000, // Keep cache for 30 minutes
  });
};

// Hook for getting optimized media URL with caching
export const useMediaUrl = (url: string, options?: { size?: string; quality?: number }) => {
  return useQuery({
    queryKey: ['mediaUrl', url, options],
    queryFn: async () => {
      // Check cache first
      const cachedUrl = getCachedUrl(url);
      if (cachedUrl) {
        return cachedUrl;
      }

      // Add query parameters for optimization if needed
      let optimizedUrl = url;
      if (options) {
        const params = new URLSearchParams();
        if (options.size) params.append('size', options.size);
        if (options.quality) params.append('quality', options.quality.toString());
        optimizedUrl = `${url}?${params.toString()}`;
      }

      // Cache the optimized URL
      mediaUrlCache.set(url, {
        url: optimizedUrl,
        accessTime: Date.now(),
      });

      return optimizedUrl;
    },
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 30 * 60 * 1000, // Keep cache for 30 minutes
  });
}; 