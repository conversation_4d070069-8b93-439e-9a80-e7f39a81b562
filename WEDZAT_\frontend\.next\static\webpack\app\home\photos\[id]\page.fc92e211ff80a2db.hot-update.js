"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./components/HomeDashboard/Photos.tsx":
/*!*********************************************!*\
  !*** ./components/HomeDashboard/Photos.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Photos = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [photos, setPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [likedPhotos, setLikedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set());\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // Fallback data if API fails - using empty array\n    const getFallbackPhotos = ()=>[];\n    // Function to fetch photos from the API\n    const fetchPhotos = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching photos for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setPhotos([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.photos) {\n                console.log(\"Loaded \".concat(response.data.photos.length, \" photos for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.photos.length > 0) {\n                    console.log('Sample photo data:', response.data.photos[0]);\n                }\n                // Process the response\n                const processedPhotos = response.data.photos.map((photo)=>{\n                    if (!photo.photo_url) {\n                        console.log(\"Photo missing URL: \".concat(photo.photo_id));\n                    }\n                    return photo;\n                });\n                if (pageNumber === 1) {\n                    setPhotos(processedPhotos);\n                } else {\n                    setPhotos((prev)=>[\n                            ...prev,\n                            ...processedPhotos\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load photos - unexpected response format');\n                setPhotos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load photos');\n            setPhotos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of photos as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Photos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial photos load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchPhotos function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for photos page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"Photos.useEffect\": (response)=>{\n                            console.log('API response received for photos page 1');\n                            if (response.data && response.data.photos) {\n                                setPhotos(response.data.photos);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setPhotos([]);\n                                setError('No photos found');\n                            }\n                        }\n                    }[\"Photos.useEffect\"]).catch({\n                        \"Photos.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load photos');\n                            setPhotos([]);\n                        }\n                    }[\"Photos.useEffect\"]).finally({\n                        \"Photos.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"Photos.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"Photos.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"Photos.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchPhotos(nextPage, false);\n                    }\n                }\n            }[\"Photos.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"Photos.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"Photos.useEffect\"];\n        }\n    }[\"Photos.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a photo\n    const getImageSource = (photo)=>{\n        if (photo.photo_url) {\n            return photo.photo_url;\n        }\n        return '/pics/placeholder.svg';\n    };\n    // Toggle like for photos\n    const toggleLike = async (photoId)=>{\n        try {\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedPhotos.has(photoId);\n            setLikedPhotos((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(photoId);\n                } else {\n                    newLiked.add(photoId);\n                }\n                return newLiked;\n            });\n            // Make API call\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(endpoint, {\n                content_id: photoId,\n                content_type: 'photo'\n            }, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" photo: \").concat(photoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedPhotos((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedPhotos.has(photoId)) {\n                    newLiked.delete(photoId);\n                } else {\n                    newLiked.add(photoId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-black\",\n                        children: \"Photos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading photos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying photos load...');\n                            setError(null);\n                            fetchPhotos(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: 'smooth',\n                    WebkitOverflowScrolling: 'touch',\n                    minHeight: photos.length === 0 && !error ? '220px' : 'auto'\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative\",\n                children: [\n                    photos.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No photos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-4 pb-4 flex-nowrap\",\n                        children: [\n                            photos.map((photo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: '250px',\n                                        height: '200px'\n                                    },\n                                    onClick: ()=>{\n                                        window.location.href = \"/home/<USER>/\".concat(photo.photo_id);\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute top-2 left-2 z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                username: photo.user_name || \"user\",\n                                                size: \"sm\",\n                                                isGradientBorder: true,\n                                                onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: getImageSource(photo),\n                                                alt: photo.photo_name || \"Photo\",\n                                                fill: true,\n                                                sizes: \"(max-width: 640px) 250px, 250px\",\n                                                className: \"object-cover\",\n                                                ...index < 4 ? {\n                                                    priority: true\n                                                } : {\n                                                    loading: 'lazy'\n                                                },\n                                                placeholder: \"blur\" // Show blur placeholder while loading\n                                                ,\n                                                blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                ,\n                                                onError: (e)=>{\n                                                    const imgElement = e.target;\n                                                    if (imgElement) {\n                                                        imgElement.src = '/pics/placeholder.svg';\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-sm font-medium truncate\",\n                                                    children: photo.photo_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-83037452c623c470\" + \" \" + \"text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                                                    className: \"jsx-83037452c623c470\" + \" \" + \"cursor-pointer hover:underline\",\n                                                                    children: photo.user_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-83037452c623c470\",\n                                                                    children: photo.photo_likes ? \"\".concat(photo.photo_likes, \" likes\") : ''\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                toggleLike(photo.photo_id);\n                                                            },\n                                                            className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center space-x-1 text-white opacity-80 hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: likedPhotos.has(photo.photo_id) ? \"#B31B1E\" : \"none\",\n                                                                stroke: likedPhotos.has(photo.photo_id) ? \"#B31B1E\" : \"white\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\",\n                                                                    className: \"jsx-83037452c623c470\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(photo.photo_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: 'relative',\n                                    // Add debug outline in development\n                                    outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"photos-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[200px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Photos, \"0D7xLpRSc97qwaKU4HSyafwIrS4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = Photos;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Photos);\nvar _c;\n$RefreshReg$(_c, \"Photos\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Photos.tsx\n"));

/***/ })

});