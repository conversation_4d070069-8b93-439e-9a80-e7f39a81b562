"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/shorts/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/shorts/page.tsx":
/*!******************************************!*\
  !*** ./app/home/<USER>/shorts/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashShortsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var _components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/FlashVendorDetails */ \"(app-pages-browser)/./components/FlashVendorDetails.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Navigation */ \"(app-pages-browser)/./components/HomeDashboard/Navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Create a Client component that uses useSearchParams\nfunction FlashShortsContent() {\n    var _flashes_currentIndex, _flashes_currentIndex1, _flashes_currentIndex2, _flashes_currentIndex3, _flashes_currentIndex4, _flashes_currentIndex5, _flashes_currentIndex6, _flashes_currentIndex7;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialIndex = parseInt((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"index\")) || \"0\");\n    const [flashes, setFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialIndex);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedFlashes, setLikedFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [leftSidebarExpanded, setLeftSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightSidebarExpanded, setRightSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showVendorDetails, setShowVendorDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [admiringUsers, setAdmiringUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // Track admiring state\n    const [videoLoading, setVideoLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Refs for video elements\n    const videoRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const youtubeTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // User avatar placeholders - using placeholder.svg which exists\n    const userAvatarPlaceholders = [\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>setIsClient(true)\n    }[\"FlashShortsContent.useEffect\"], []);\n    // Load admiring state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!isClient) return;\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                const admiredUsers = JSON.parse(admiredUsersJson);\n                setAdmiringUsers(admiredUsers);\n            } catch (error) {\n                console.error('Error loading admired users from localStorage:', error);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient\n    ]);\n    // Fetch flashes from the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const fetchFlashes = {\n                \"FlashShortsContent.useEffect.fetchFlashes\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Get token from localStorage\n                        const token = localStorage.getItem(\"token\");\n                        if (!token) {\n                            console.warn(\"No authentication token found\");\n                            setError(\"Authentication required\");\n                            return;\n                        }\n                        const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/flashes?page=\".concat(page, \"&limit=10\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.data && response.data.flashes) {\n                            console.log(\"Flashes API response:\", response.data);\n                            if (page === 1) {\n                                setFlashes(response.data.flashes);\n                                // Initialize loading state for all videos\n                                const initialLoadingState = {};\n                                response.data.flashes.forEach({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                        initialLoadingState[flash.video_id] = true;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                setVideoLoading(initialLoadingState);\n                                // Fetch like status for initial flashes\n                                fetchLikeStatus(response.data.flashes.map({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (f)=>f.video_id\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]));\n                            } else {\n                                setFlashes({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>[\n                                            ...prev,\n                                            ...response.data.flashes\n                                        ]\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Initialize loading state for new videos\n                                setVideoLoading({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>{\n                                        const newState = {\n                                            ...prev\n                                        };\n                                        response.data.flashes.forEach({\n                                            \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                                newState[flash.video_id] = true;\n                                            }\n                                        }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                        return newState;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Fetch like status for new flashes\n                                fetchLikeStatus(response.data.flashes.map({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (f)=>f.video_id\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]));\n                            }\n                            setHasMore(response.data.next_page);\n                        } else {\n                            console.warn(\"Unexpected API response format:\", response.data);\n                            setError(\"Failed to load flashes\");\n                        }\n                    } catch (err) {\n                        console.error(\"Error fetching flashes:\", err);\n                        setError(\"Failed to load flashes\");\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.fetchFlashes\"];\n            fetchFlashes();\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        page\n    ]);\n    // Load more flashes when reaching the end\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (currentIndex >= flashes.length - 2 && hasMore && !loading) {\n                setPage({\n                    \"FlashShortsContent.useEffect\": (prevPage)=>prevPage + 1\n                }[\"FlashShortsContent.useEffect\"]);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length,\n        hasMore,\n        loading\n    ]);\n    // Handle video playback when current index changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (flashes.length === 0 || !isClient) return;\n            // Clear any existing YouTube timer\n            if (youtubeTimerRef.current) {\n                clearTimeout(youtubeTimerRef.current);\n                youtubeTimerRef.current = null;\n            }\n            // Pause all videos\n            Object.values(videoRefs.current).forEach({\n                \"FlashShortsContent.useEffect\": (videoEl)=>{\n                    if (videoEl && !videoEl.paused) {\n                        videoEl.pause();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect\"]);\n            const currentFlash = flashes[currentIndex];\n            if (!currentFlash) return;\n            // Handle YouTube videos with timer-based auto-advance\n            if (isYoutubeVideo(currentFlash) && !isPaused[currentFlash.video_id]) {\n                // Set a timer for YouTube videos (assuming average duration of 30 seconds)\n                // In a real implementation, you might want to use YouTube API to get actual duration\n                youtubeTimerRef.current = setTimeout({\n                    \"FlashShortsContent.useEffect\": ()=>{\n                        console.log(\"YouTube video timer ended: \".concat(currentFlash.video_id, \", auto-advancing to next flash\"));\n                        if (!isPaused[currentFlash.video_id]) {\n                            navigateToNext();\n                        }\n                    }\n                }[\"FlashShortsContent.useEffect\"], 30000); // 30 seconds default duration for YouTube videos\n            } else {\n                // Play current video if not manually paused (for non-YouTube videos)\n                const currentVideoId = currentFlash.video_id;\n                const currentVideo = videoRefs.current[currentVideoId];\n                if (currentVideo && !isPaused[currentVideoId]) {\n                    const playPromise = currentVideo.play();\n                    if (playPromise !== undefined) {\n                        playPromise.catch({\n                            \"FlashShortsContent.useEffect\": (error)=>{\n                                console.error(\"Error playing video:\", error);\n                            }\n                        }[\"FlashShortsContent.useEffect\"]);\n                    }\n                }\n            }\n            // Cleanup function\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes,\n        isClient,\n        isPaused\n    ]);\n    // Handle keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"FlashShortsContent.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") {\n                        navigateToPrevious();\n                    } else if (e.key === \"ArrowDown\" || e.key === \"ArrowRight\") {\n                        navigateToNext();\n                    } else if (e.key === \"Escape\") {\n                        router.push(\"/home/<USER>");\n                    } else if (e.key === \" \" || e.key === \"Spacebar\") {\n                        var _flashes_currentIndex;\n                        // Toggle play/pause on spacebar\n                        togglePlayPause((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleKeyDown\"];\n            window.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle touch events for swiping\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            let startY = 0;\n            // let startTime = 0; // Uncomment if needed for timing-based gestures\n            const handleTouchStart = {\n                \"FlashShortsContent.useEffect.handleTouchStart\": (e)=>{\n                    startY = e.touches[0].clientY;\n                // startTime = Date.now(); // Uncomment if needed for timing-based gestures\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchStart\"];\n            const handleTouchEnd = {\n                \"FlashShortsContent.useEffect.handleTouchEnd\": (e)=>{\n                    const deltaY = e.changedTouches[0].clientY - startY;\n                    // const deltaTime = Date.now() - startTime; // Uncomment if needed for timing-based gestures\n                    // Make touch more responsive by reducing the threshold\n                    if (Math.abs(deltaY) > 30) {\n                        if (deltaY > 0) {\n                            // Swipe down - go to previous\n                            navigateToPrevious();\n                        } else {\n                            // Swipe up - go to next\n                            navigateToNext();\n                        }\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchEnd\"];\n            // Add touchmove handler for more responsive scrolling\n            let lastY = 0;\n            let touchMoveThrottle = false;\n            const handleTouchMove = {\n                \"FlashShortsContent.useEffect.handleTouchMove\": (e)=>{\n                    const currentY = e.touches[0].clientY;\n                    // Only process every few pixels of movement to avoid too many updates\n                    if (!touchMoveThrottle && Math.abs(currentY - lastY) > 20) {\n                        lastY = currentY;\n                        touchMoveThrottle = true;\n                        // Schedule reset of throttle\n                        setTimeout({\n                            \"FlashShortsContent.useEffect.handleTouchMove\": ()=>{\n                                touchMoveThrottle = false;\n                            }\n                        }[\"FlashShortsContent.useEffect.handleTouchMove\"], 100);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchMove\"];\n            const container = containerRef.current;\n            container.addEventListener(\"touchstart\", handleTouchStart);\n            container.addEventListener(\"touchmove\", handleTouchMove, {\n                passive: true\n            });\n            container.addEventListener(\"touchend\", handleTouchEnd);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"touchstart\", handleTouchStart);\n                    container.removeEventListener(\"touchmove\", handleTouchMove);\n                    container.removeEventListener(\"touchend\", handleTouchEnd);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle wheel events for touchpad scrolling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            const handleWheel = {\n                \"FlashShortsContent.useEffect.handleWheel\": (e)=>{\n                    // Debounce the wheel event to prevent too many navigations\n                    if (e.deltaY > 50) {\n                        // Scroll down - go to next\n                        navigateToNext();\n                    } else if (e.deltaY < -50) {\n                        // Scroll up - go to previous\n                        navigateToPrevious();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleWheel\"];\n            const container = containerRef.current;\n            container.addEventListener(\"wheel\", handleWheel, {\n                passive: true\n            });\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"wheel\", handleWheel);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    const navigateToNext = ()=>{\n        if (currentIndex < flashes.length - 1) {\n            setCurrentIndex((prevIndex)=>prevIndex + 1);\n        } else {\n            // When reaching the last video, loop back to the first one\n            setCurrentIndex(0);\n        }\n    };\n    const navigateToPrevious = ()=>{\n        if (currentIndex > 0) {\n            setCurrentIndex((prevIndex)=>prevIndex - 1);\n        }\n    };\n    // Auto-advance to next flash when current video ends\n    const handleVideoEnded = (videoId)=>{\n        console.log(\"Video ended: \".concat(videoId, \", auto-advancing to next flash\"));\n        // Only auto-advance if the video wasn't manually paused\n        if (!isPaused[videoId]) {\n            navigateToNext();\n        }\n    };\n    const toggleLike = async (flashId)=>{\n        console.log('🔥 toggleLike called for flashId:', flashId);\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedFlashes.has(flashId);\n            console.log('🔥 isCurrentlyLiked:', isCurrentlyLiked);\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: flashId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            // Update localStorage to persist like status\n            const likedFlashesData = localStorage.getItem('likedFlashes');\n            const likedFlashesArray = likedFlashesData ? JSON.parse(likedFlashesData) : [];\n            if (isCurrentlyLiked) {\n                // Remove from liked flashes\n                const updatedLikedFlashes = likedFlashesArray.filter((id)=>id !== flashId);\n                localStorage.setItem('likedFlashes', JSON.stringify(updatedLikedFlashes));\n            } else {\n                // Add to liked flashes\n                if (!likedFlashesArray.includes(flashId)) {\n                    likedFlashesArray.push(flashId);\n                    localStorage.setItem('likedFlashes', JSON.stringify(likedFlashesArray));\n                }\n            }\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" flash: \").concat(flashId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedFlashes.has(flashId)) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    // Fetch like status for flashes from localStorage (temporary solution)\n    const fetchLikeStatus = async (flashIds)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token || flashIds.length === 0) return;\n            // Get liked flashes from localStorage as a temporary solution\n            // In a real implementation, this would come from the backend\n            const likedFlashesData = localStorage.getItem('likedFlashes');\n            const likedFlashesArray = likedFlashesData ? JSON.parse(likedFlashesData) : [];\n            // Filter to only include flashes that are currently loaded\n            const currentLikedFlashes = flashIds.filter((id)=>likedFlashesArray.includes(id));\n            console.log('Loaded like status for flashes:', currentLikedFlashes);\n            setLikedFlashes(new Set(currentLikedFlashes));\n        } catch (error) {\n            console.error('Error fetching like status:', error);\n            setLikedFlashes(new Set());\n        }\n    };\n    const togglePlayPause = (videoId)=>{\n        if (!videoId) return;\n        const currentFlash = flashes.find((flash)=>flash.video_id === videoId);\n        if (!currentFlash) return;\n        setIsPaused((prev)=>{\n            const newState = {\n                ...prev\n            };\n            newState[videoId] = !prev[videoId];\n            if (isYoutubeVideo(currentFlash)) {\n                // Handle YouTube video pause/play\n                if (newState[videoId]) {\n                    // Paused - clear the timer\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                } else {\n                    // Resumed - restart the timer\n                    youtubeTimerRef.current = setTimeout(()=>{\n                        console.log(\"YouTube video timer ended: \".concat(videoId, \", auto-advancing to next flash\"));\n                        if (!newState[videoId]) {\n                            navigateToNext();\n                        }\n                    }, 30000); // 30 seconds default duration\n                }\n            } else {\n                // Handle regular video pause/play\n                const videoEl = videoRefs.current[videoId];\n                if (videoEl) {\n                    if (newState[videoId]) {\n                        videoEl.pause();\n                    } else {\n                        videoEl.play().catch((err)=>console.error(\"Error playing video:\", err));\n                    }\n                }\n            }\n            return newState;\n        });\n    };\n    // Extract YouTube video ID from URL\n    const getYoutubeId = (url)=>{\n        if (!url) return \"\";\n        // If it's already just an ID, return it\n        if (url.length < 20 && !url.includes(\"/\")) return url;\n        // Try to extract ID from YouTube URL\n        const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;\n        const match = url.match(regExp);\n        return match && match[2].length === 11 ? match[2] : \"\";\n    };\n    // Get appropriate image source for a flash\n    const getImageSource = (flash)=>{\n        // If we have a thumbnail, use it\n        if (flash.video_thumbnail) {\n            return flash.video_thumbnail;\n        }\n        // If it's a YouTube video, use the YouTube thumbnail\n        if (flash.video_url && flash.video_url.includes(\"youtube\")) {\n            const videoId = getYoutubeId(flash.video_url);\n            if (videoId) {\n                return \"https://img.youtube.com/vi/\".concat(videoId, \"/hqdefault.jpg\");\n            }\n        }\n        // Default fallback - use a local placeholder image\n        return \"/pics/placeholder.svg\";\n    };\n    // Check if the video is from YouTube\n    const isYoutubeVideo = (flash)=>{\n        return typeof flash.video_url === \"string\" && flash.video_url.includes(\"youtube\");\n    };\n    // Format numbers for display (e.g., 1.2K)\n    const formatNumber = (num)=>{\n        if (!num) return \"0\";\n        if (num >= 1000000) {\n            return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        } else if (num >= 1000) {\n            return \"\".concat((num / 1000).toFixed(1), \"K\");\n        }\n        return num.toString();\n    };\n    // Add custom CSS for styling and responsiveness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            // Add a style tag for styling and responsiveness\n            const style = document.createElement(\"style\");\n            style.innerHTML = \"\\n      .shorts-page {\\n        background-color: #f8f8f8;\\n      }\\n\\n      .shorts-container {\\n        background-color: #000;\\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\\n        border-radius: 12px;\\n        overflow: hidden;\\n        position: relative;\\n      }\\n\\n      .shorts-video {\\n        width: 100%;\\n        height: 100%;\\n        object-fit: cover;\\n        background-color: #000;\\n      }\\n\\n      .shorts-controls {\\n        position: absolute;\\n        right: 8px;\\n        bottom: 80px;\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        gap: 16px;\\n        z-index: 20;\\n      }\\n\\n      .shorts-info {\\n        position: absolute;\\n        bottom: 0;\\n        left: 0;\\n        right: 0;\\n        padding: 16px;\\n        background: linear-gradient(transparent, rgba(0,0,0,0.8));\\n        z-index: 10;\\n      }\\n\\n      /* Fixed layout styles for proper centering */\\n      .layout-container {\\n        display: flex;\\n        width: 100%;\\n      }\\n\\n      .left-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .left-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .right-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .right-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .main-content {\\n        flex: 1;\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      /* Mobile responsive styles */\\n      @media (max-width: 768px) {\\n        .shorts-page {\\n          background-color: #000;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n        }\\n\\n        .shorts-container {\\n          width: 100vw !important;\\n          max-width: none !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          max-height: none !important;\\n          border-radius: 0 !important;\\n          border: none !important;\\n          margin: 0 !important;\\n          padding-bottom: 20px !important;\\n        }\\n\\n        .mobile-video-item {\\n          margin-bottom: 10px !important;\\n          border-radius: 8px !important;\\n          overflow: hidden !important;\\n        }\\n\\n        .mobile-nav-buttons {\\n          position: fixed !important;\\n          left: 16px !important;\\n          top: 50% !important;\\n          transform: translateY(-50%) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-nav-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-nav-button:disabled {\\n          background: rgba(255, 255, 255, 0.3) !important;\\n          color: rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-interaction-buttons {\\n          position: fixed !important;\\n          right: 16px !important;\\n          bottom: calc(120px + env(safe-area-inset-bottom, 20px)) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-interaction-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          color: white !important;\\n        }\\n\\n        .mobile-back-button {\\n          position: fixed !important;\\n          top: 20px !important;\\n          left: 16px !important;\\n          z-index: 50 !important;\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-user-info {\\n          position: absolute !important;\\n          top: 20px !important;\\n          left: 80px !important;\\n          right: 16px !important;\\n          z-index: 50 !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 12px !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: space-between !important;\\n        }\\n\\n        .mobile-unlock-vendor {\\n          position: absolute !important;\\n          bottom: calc(20px + env(safe-area-inset-bottom, 10px)) !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n          background: #B31B1E !important;\\n          color: white !important;\\n          border: none !important;\\n          border-radius: 8px !important;\\n          padding: 12px 24px !important;\\n          font-weight: 600 !important;\\n          box-shadow: 0 4px 12px rgba(179, 27, 30, 0.4) !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n        }\\n\\n        .mobile-vendor-details {\\n          position: absolute !important;\\n          bottom: 80px !important;\\n          left: 16px !important;\\n          right: 16px !important;\\n          z-index: 60 !important;\\n          background: rgba(255, 255, 255, 0.95) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 16px !important;\\n          max-height: 50vh !important;\\n          overflow-y: auto !important;\\n          color: black !important;\\n        }\\n\\n        .mobile-progress-indicator {\\n          position: fixed !important;\\n          top: 80px !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n        }\\n\\n        /* Hide desktop navigation buttons on mobile */\\n        .desktop-nav-buttons {\\n          display: none !important;\\n        }\\n\\n        .desktop-interaction-buttons {\\n          display: none !important;\\n        }\\n\\n        /* Ensure main content takes full space on mobile */\\n        .main-content-mobile {\\n          padding: 0 !important;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n          margin: 0 !important;\\n          width: 100vw !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          position: relative !important;\\n        }\\n      }\\n\\n      /* Desktop styles */\\n      @media (min-width: 769px) {\\n        .mobile-nav-buttons,\\n        .mobile-interaction-buttons,\\n        .mobile-back-button,\\n        .mobile-user-info,\\n        .mobile-unlock-vendor,\\n        .mobile-progress-indicator {\\n          display: none !important;\\n        }\\n      }\\n    \";\n            document.head.appendChild(style);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    document.head.removeChild(style);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"opacity-0\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 808,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen w-full shorts-page\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.TopNavigation, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 814,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full h-[calc(100vh-80px)] md:mt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-sidebar \".concat(leftSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.SideNavigation, {\n                            expanded: leftSidebarExpanded,\n                            onExpand: ()=>setLeftSidebarExpanded(true),\n                            onCollapse: ()=>setLeftSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 820,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center px-4 md:px-4 px-0 relative main-content-mobile\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"absolute top-4 left-4 z-50 bg-white rounded-full p-3 text-black shadow-lg hover:bg-gray-200 transition-colors flex items-center justify-center hidden md:flex\",\n                                style: {\n                                    width: \"48px\",\n                                    height: \"48px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"mobile-back-button md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 848,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center w-full md:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-nav-buttons flex flex-col items-center justify-center space-y-4 mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === 0 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === flashes.length - 1 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 869,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-nav-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 886,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: containerRef,\n                                        className: \"shorts-container relative w-full md:w-[400px] h-full md:h-[min(calc(100vh-100px),89vh)]\",\n                                        style: {\n                                            // Desktop styles\n                                            ... true && window.innerWidth >= 768 ? {\n                                                width: \"400px\",\n                                                height: \"min(calc(100vh - 100px), 89vh)\",\n                                                margin: \"0 auto\",\n                                                border: \"2px solid #B31B1E\",\n                                                borderRadius: \"12px\",\n                                                overflow: \"hidden\"\n                                            } : {\n                                                // Mobile styles - full screen with safe area\n                                                width: \"100vw\",\n                                                height: \"calc(100vh - env(safe-area-inset-bottom, 20px))\",\n                                                margin: \"0\",\n                                                border: \"none\",\n                                                borderRadius: \"0\",\n                                                overflow: \"hidden\",\n                                                paddingBottom: \"env(safe-area-inset-bottom, 20px)\"\n                                            }\n                                        },\n                                        children: [\n                                            loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                                children: \"Loading flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 15\n                                            }, this),\n                                            error && !loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-red-500\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 15\n                                            }, this),\n                                            flashes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full w-full transition-transform duration-300 ease-out\",\n                                                style: {\n                                                    transform: \"translateY(-\".concat(currentIndex * 102, \"%)\")\n                                                },\n                                                children: flashes.map((flash, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full w-full flex items-center justify-center relative bg-black \".concat( true && window.innerWidth < 768 ? 'mobile-video-item' : ''),\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(index * 102, \"%\"),\n                                                            left: 0,\n                                                            right: 0,\n                                                            bottom: 0,\n                                                            overflow: \"hidden\",\n                                                            borderRadius: \"8px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-full w-full relative overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 overflow-hidden\",\n                                                                    children: isYoutubeVideo(flash) ? // YouTube iframe for YouTube videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 978,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 979,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 977,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 976,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                                                src: \"https://www.youtube.com/embed/\".concat(getYoutubeId(flash.video_url), \"?autoplay=1&controls=0&rel=0&showinfo=0&mute=0\"),\n                                                                                title: flash.video_name,\n                                                                                className: \"shorts-video\",\n                                                                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                                                                allowFullScreen: true,\n                                                                                onLoad: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 983,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 973,\n                                                                        columnNumber: 27\n                                                                    }, this) : // Video player for Cloudfront videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full overflow-hidden\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1003,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1004,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1002,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1001,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                ref: (el)=>{\n                                                                                    videoRefs.current[flash.video_id] = el;\n                                                                                },\n                                                                                src: flash.video_url,\n                                                                                className: \"shorts-video\",\n                                                                                playsInline: true,\n                                                                                muted: false,\n                                                                                controls: false,\n                                                                                poster: getImageSource(flash),\n                                                                                onLoadStart: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: true\n                                                                                        }));\n                                                                                },\n                                                                                onCanPlay: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onError: ()=>{\n                                                                                    console.error(\"Failed to load video: \".concat(flash.video_name));\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onEnded: ()=>handleVideoEnded(flash.video_id)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1008,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    togglePlayPause(flash.video_id);\n                                                                                },\n                                                                                className: \"absolute inset-0 w-full h-full flex items-center justify-center z-10 group\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(isPaused[flash.video_id] ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\", \" transition-opacity duration-200 bg-black/40 rounded-full p-4 shadow-lg\"),\n                                                                                    children: isPaused[flash.video_id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"white\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                                            points: \"5 3 19 12 5 21 5 3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1057,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1046,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"6\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1071,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"14\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1077,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1060,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1038,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1031,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 998,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 left-4 right-4 z-20 flex items-center bg-black/20 backdrop-blur-sm rounded-lg p-2 hidden md:flex\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1095,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1106,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1107,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1105,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1094,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1171,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1112,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1093,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-user-info md:hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1179,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1190,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1191,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1189,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1178,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1255,\n                                                                                columnNumber: 67\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1177,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-8 left-0 right-0 flex justify-center z-10 hidden md:flex\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-[#B31B1E] text-white text-sm font-medium px-4 py-2 rounded-md flex items-center\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowVendorDetails((prev)=>({\n                                                                                ...prev,\n                                                                                [flash.video_id]: !prev[flash.video_id]\n                                                                            }));\n                                                                    },\n                                                                    children: [\n                                                                        \"Unlock Vendor\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4 ml-1\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1274,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1273,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1261,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mobile-unlock-vendor md:hidden\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    setShowVendorDetails((prev)=>({\n                                                                            ...prev,\n                                                                            [flash.video_id]: !prev[flash.video_id]\n                                                                        }));\n                                                                },\n                                                                children: [\n                                                                    \"Unlock Vendor\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        className: \"h-4 w-4 ml-1\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1293,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1292,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1281,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-20 left-4 right-4 p-4 bg-white/90 rounded-lg text-black max-h-[40%] overflow-y-auto z-30 hidden md:block\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1301,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1300,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === currentIndex && showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-vendor-details md:hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1311,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1310,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(flash.video_id, \"-\").concat(index), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 15\n                                            }, this),\n                                            loading && page > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-20 left-0 right-0 text-center text-white bg-black/50 py-2 mx-auto w-48 rounded-full backdrop-blur-sm z-20\",\n                                                children: \"Loading more flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1324,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-14 left-0 right-0 px-4 z-20 hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1333,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1341,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1331,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1330,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mobile-progress-indicator md:hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1358,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1347,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-interaction-buttons flex flex-col items-center justify-center space-y-6 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100 mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1369,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1370,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1371,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1368,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1367,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 \".concat(likedFlashes.has(((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id) || '') ? 'bg-red-100 hover:bg-red-200' : 'bg-black/20 hover:bg-black/40'),\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        console.log('Desktop like button clicked for flash:', currentFlash.video_id);\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                title: likedFlashes.has(((_flashes_currentIndex1 = flashes[currentIndex]) === null || _flashes_currentIndex1 === void 0 ? void 0 : _flashes_currentIndex1.video_id) || '') ? 'Unlike' : 'Like',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex2 = flashes[currentIndex]) === null || _flashes_currentIndex2 === void 0 ? void 0 : _flashes_currentIndex2.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex3 = flashes[currentIndex]) === null || _flashes_currentIndex3 === void 0 ? void 0 : _flashes_currentIndex3.video_id) || '') ? \"#B31B1E\" : \"white\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1392,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1391,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1376,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1398,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1397,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1406,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1407,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1405,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1404,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-interaction-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash === null || currentFlash === void 0 ? void 0 : currentFlash.user_id) {\n                                                        // Navigate to user profile - you can implement this navigation\n                                                        console.log('Navigate to profile:', currentFlash.user_id);\n                                                    // router.push(`/profile/${currentFlash.user_id}`);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1427,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"7\",\n                                                            r: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1428,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1426,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1415,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 \".concat(likedFlashes.has(((_flashes_currentIndex4 = flashes[currentIndex]) === null || _flashes_currentIndex4 === void 0 ? void 0 : _flashes_currentIndex4.video_id) || '') ? 'bg-red-100 hover:bg-red-200' : 'bg-black/20 hover:bg-black/40'),\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        console.log('Mobile like button clicked for flash:', currentFlash.video_id);\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                title: likedFlashes.has(((_flashes_currentIndex5 = flashes[currentIndex]) === null || _flashes_currentIndex5 === void 0 ? void 0 : _flashes_currentIndex5.video_id) || '') ? 'Unlike' : 'Like',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex6 = flashes[currentIndex]) === null || _flashes_currentIndex6 === void 0 ? void 0 : _flashes_currentIndex6.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex7 = flashes[currentIndex]) === null || _flashes_currentIndex7 === void 0 ? void 0 : _flashes_currentIndex7.video_id) || '') ? \"#B31B1E\" : \"white\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1449,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1448,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1433,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1456,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1455,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1454,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1463,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1464,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1462,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1461,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1471,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1472,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1473,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1470,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1469,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1413,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 833,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-sidebar \".concat(rightSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.RightSidebar, {\n                            expanded: rightSidebarExpanded,\n                            onExpand: ()=>setRightSidebarExpanded(true),\n                            onCollapse: ()=>setRightSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 1486,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 1481,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 818,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 812,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashShortsContent, \"aeQpwdVKjXsnvNtC+OoDi2XkRdk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = FlashShortsContent;\n// Loading fallback component\nfunction FlashShortsLoading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-screen w-full bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-white text-xl\",\n            children: \"Loading flashes...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1501,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1500,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FlashShortsLoading;\n// Main page component with Suspense\nfunction FlashShortsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsLoading, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1509,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1510,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1509,\n        columnNumber: 5\n    }, this);\n}\n_c2 = FlashShortsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FlashShortsContent\");\n$RefreshReg$(_c1, \"FlashShortsLoading\");\n$RefreshReg$(_c2, \"FlashShortsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/shorts/page.tsx\n"));

/***/ })

});