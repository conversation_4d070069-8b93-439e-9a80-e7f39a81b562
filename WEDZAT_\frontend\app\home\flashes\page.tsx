"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";

// Define interface for flash video items
interface FlashVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface ApiResponse {
  flashes: FlashVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

export default function FlashesPage() {
  const [flashes, setFlashes] = useState<FlashVideo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  // Removed right sidebar state

  // User avatar placeholders (using local images instead of external URLs)
  const userAvatarPlaceholders = [
    "/pics/1stim.jfif",
    "/pics/2ndim.jfif",
    "/pics/jpeg3.jfif",
    "/pics/user-profile.png",
    "/pics/user-profile.png",
  ];

  useEffect(() => setIsClient(true), []);

  // Fetch flashes from the API
  useEffect(() => {
    const fetchFlashes = async () => {
      try {
        setLoading(true);
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          console.warn('No authentication token found');
          setError('Authentication required');
          return;
        }

        const response = await axios.get<ApiResponse>(`/flashes?page=${page}&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.flashes) {
          console.log('Flashes API response:', response.data);

          if (page === 1) {
            setFlashes(response.data.flashes);
          } else {
            setFlashes(prev => [...prev, ...response.data.flashes]);
          }

          setHasMore(response.data.next_page);
        } else {
          console.warn('Unexpected API response format:', response.data);
          setError('Failed to load flashes');
        }
      } catch (err) {
        console.error('Error fetching flashes:', err);
        setError('Failed to load flashes');
      } finally {
        setLoading(false);
      }
    };

    fetchFlashes();
  }, [page]);

  // Reference for the last item in the list
  const lastFlashRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last flash is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.5, rootMargin: '0px 0px 200px 0px' } // Load when item is 50% visible or 200px before it comes into view
    );

    // Get the last item element
    const lastElement = lastFlashRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, flashes.length]);

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Try to extract ID from YouTube URL
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : url;
  };

  // Get appropriate image source for a flash
  const getImageSource = (flash: FlashVideo): string => {
    // If we have a thumbnail, use it
    if (flash.video_thumbnail) {
      return flash.video_thumbnail;
    }

    // If it's a YouTube video, use the YouTube thumbnail
    if (flash.video_url && flash.video_url.includes('youtube')) {
      const videoId = getYoutubeId(flash.video_url);
      if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
      }
    }

    // Default fallback - use a local placeholder image
    return '/pics/placeholder.svg';
  };

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">Flashes</h1>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading flashes</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {flashes.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                {flashes.map((flash, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === flashes.length - 1;

                  return (
                  <div
                    key={`${flash.video_id}-${index}`}
                    // Apply ref to the last item for intersection observer
                    ref={isLastItem ? lastFlashRef : null}
                    onClick={() => {
                      // Open in shorts view with current index
                      window.location.href = `/home/<USER>/shorts?index=${index}`;
                    }}
                    style={{
                      width: "100%",
                      height: "308px",
                      position: "relative",
                      borderRadius: "10px",
                      border: "1px solid #B31B1E",
                      padding: "10px",
                      background:
                        "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                      cursor: "pointer",
                      overflow: "hidden",
                      transition: "transform 0.2s ease",
                    }}
                    className="hover:scale-105"
                  >
                    <div
                      style={{
                        position: "absolute",
                        top: "10px",
                        left: "10px",
                        zIndex: 2,
                      }}
                    >
                      <UserAvatar
                        username={flash.user_name || "user"}
                        size="sm"
                        isGradientBorder={true}
                        imageUrl={userAvatarPlaceholders[index % userAvatarPlaceholders.length]}
                      />
                    </div>

                    <div
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        zIndex: 1,
                      }}
                    >
                      <div className="relative w-full h-full">
                        {/* Use Next.js Image component with proper error handling */}
                        <Image
                          src={getImageSource(flash)}
                          alt={flash.video_name || "Flash Video"}
                          fill
                          sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 16vw"
                          className="object-cover"
                          priority={index < 6} // Only prioritize the first six images
                          unoptimized={!flash.video_thumbnail} // Skip optimization for fallback images
                          onError={(e) => {
                            console.error(`Failed to load thumbnail for flash: ${flash.video_name}`);
                            // Use placeholder as fallback
                            const imgElement = e.target as HTMLImageElement;
                            if (imgElement) {
                              imgElement.src = '/pics/placeholder.svg';
                            }
                          }}
                        />
                      </div>
                    </div>

                    {/* Flash info */}
                    <div
                      style={{
                        position: "absolute",
                        bottom: "40px", // Increased to make room for interaction icons
                        left: "10px",
                        right: "10px",
                        zIndex: 2,
                        color: "white",
                      }}
                    >
                      <div style={{ fontSize: "14px", fontWeight: 500 }}>
                        {flash.video_name}
                      </div>
                      <div style={{ fontSize: "12px" }}>
                        {flash.video_views ? `${(flash.video_views / 1000).toFixed(1)}K views` : ''}
                        {flash.video_likes ? ` • ${(flash.video_likes / 1000).toFixed(1)}K likes` : ''}
                      </div>
                    </div>

                    {/* Interaction icons at bottom */}
                    <div
                      style={{
                        position: "absolute",
                        bottom: "0",
                        left: "0",
                        right: "0",
                        zIndex: 3,
                        display: "flex",
                        justifyContent: "space-around",
                        alignItems: "center",
                        padding: "8px 10px",
                        backgroundColor: "rgba(0, 0, 0, 0.5)",
                      }}
                      onClick={(e) => e.stopPropagation()} // Prevent triggering the parent click
                    >
                      {/* 3-dots menu */}
                      <button className="text-white opacity-80 hover:opacity-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="1"></circle>
                          <circle cx="12" cy="5" r="1"></circle>
                          <circle cx="12" cy="19" r="1"></circle>
                        </svg>
                      </button>

                      {/* Comment */}
                      <button className="text-white opacity-80 hover:opacity-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                        </svg>
                      </button>

                      {/* Share */}
                      <button className="text-white opacity-80 hover:opacity-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="22" y1="2" x2="11" y2="13"></line>
                          <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                        </svg>
                      </button>

                      {/* Like */}
                      <button className="text-white opacity-80 hover:opacity-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && flashes.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more flashes to load</div>
            )}

            {/* No content state */}
            {!loading && flashes.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No flashes available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
