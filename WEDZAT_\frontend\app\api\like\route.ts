import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await request.json();
    console.log('Like API - Request body:', body);
    console.log('Like API - Auth header:', authHeader);

    // Validate required fields
    if (!body.content_id || !body.content_type) {
      return NextResponse.json(
        { error: 'content_id and content_type are required' },
        { status: 400 }
      );
    }

    // Forward the request to the backend
    const response = await axios.post(
      'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/like',
      body,
      {
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      }
    );

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('Error in like API:', error);
    
    let errorMessage = 'Failed to like content';
    let statusCode = 500;
    
    if (error.response) {
      statusCode = error.response.status;
      errorMessage = error.response.data?.error || error.response.data?.message || error.message;
    } else if (error.request) {
      errorMessage = 'No response received from server';
    } else {
      errorMessage = error.message;
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
