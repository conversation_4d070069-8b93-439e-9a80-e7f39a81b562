"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { vendorService } from '../../../services/vendor-api';

export default function VendorDashboardPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [vendorProfile, setVendorProfile] = useState<any>(null);
  const [error, setError] = useState('');

  // Check if user is authenticated and is a vendor
  useEffect(() => {
    const checkAuth = async () => {
      // Check authentication on client side only
      if (typeof window !== 'undefined') {
        if (!vendorService.isAuthenticated()) {
          router.push('/login');
          return;
        }

        // Check if user is a vendor
        const isVendor = localStorage.getItem('is_vendor') === 'true';
        if (!isVendor) {
          // If not a vendor, redirect to home
          console.log('User is not a vendor, redirecting to home');
          router.push('/home');
          return;
        }

        // Mark this as a vendor token
        localStorage.setItem('is_vendor', 'true');

        // Also set as cookie for middleware
        document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';
      }

      try {
        // TEMPORARY WORKAROUND: Skip profile fetch if we just registered
        const justRegistered = localStorage.getItem('just_registered');

        if (justRegistered === 'true') {
          // Clear the flag
          localStorage.removeItem('just_registered');

          // Show a message that we're still setting up the profile
          setError('Your vendor profile is being set up. The system needs some time to process your registration.');
          setIsLoading(false);
          return;
        }

        // Fetch vendor profile
        const profile = await vendorService.getVendorProfile();
        setVendorProfile(profile);
        setIsLoading(false);
      } catch (err: any) {
        console.error('Error fetching vendor profile:', err);

        // If we get a 404 (profile not found), it might be because the user just registered
        // and the profile is not yet available. Show a friendly message.
        if (err.status === 404) {
          setError('Your vendor profile is being set up. Please refresh the page in a few moments.');
        } else if (err.status === 500) {
          // For 500 errors, assume it's a temporary issue with the backend
          setError('The server is currently experiencing issues. Your profile will be available soon. Please try again later.');
        } else {
          // Show detailed error for debugging
          const errorMessage = err.error || 'Failed to load vendor profile';
          const errorDetails = err.message || err.details || '';
          setError(`${errorMessage}${errorDetails ? `: ${errorDetails}` : ''}. Please try again later.`);
        }

        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  const handleLogout = () => {
    vendorService.logout();
    router.push('/login');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
            <h1 className="ml-3 text-xl font-semibold text-gray-800">Vendor Dashboard</h1>
          </div>
          <div className="flex space-x-4">
            <button
              onClick={() => router.push('/home')}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition duration-200"
            >
              Go to Home
            </button>
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-red-700 text-white rounded-md hover:bg-red-800 transition duration-200"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {error}
            {error.includes('profile') && (
              <div className="mt-2">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-red-700 text-white rounded-md hover:bg-red-800 transition duration-200"
                >
                  Refresh Page
                </button>
              </div>
            )}
          </div>
        )}

        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-xl font-semibold mb-4">Welcome to Your Vendor Dashboard</h2>
              <p className="text-gray-600">
                This is your vendor dashboard where you can manage your business profile, services, and bookings.
              </p>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-700 text-white rounded-md hover:bg-red-800 transition duration-200 flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
          </div>
        </div>

        {/* Vendor Profile */}
        {vendorProfile ? (
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Business Profile</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-gray-600">Business Name:</p>
                <p className="font-medium">{vendorProfile.business_name}</p>
              </div>
              <div>
                <p className="text-gray-600">Primary Business Type:</p>
                <p className="font-medium">{vendorProfile.primary_business_name}</p>
              </div>
              <div>
                <p className="text-gray-600">Email:</p>
                <p className="font-medium">{vendorProfile.email}</p>
              </div>
              <div>
                <p className="text-gray-600">Phone:</p>
                <p className="font-medium">{vendorProfile.mobile_number}</p>
              </div>
              <div>
                <p className="text-gray-600">City:</p>
                <p className="font-medium">{vendorProfile.city}</p>
              </div>
              <div>
                <p className="text-gray-600">State:</p>
                <p className="font-medium">{vendorProfile.state || 'Not provided'}</p>
              </div>
              <div>
                <p className="text-gray-600">Verification Status:</p>
                <p className={`font-medium ${
                  vendorProfile.verification_status === 'verified'
                    ? 'text-green-600'
                    : vendorProfile.verification_status === 'rejected'
                    ? 'text-red-600'
                    : 'text-yellow-600'
                }`}>
                  {vendorProfile.verification_status.charAt(0).toUpperCase() + vendorProfile.verification_status.slice(1)}
                </p>
              </div>
            </div>
          </div>
        ) : !error ? (
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Business Profile</h2>
            <p className="text-gray-600 mb-4">
              Your profile is being set up. Please wait a moment or refresh the page.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-700 text-white rounded-md hover:bg-red-800 transition duration-200"
            >
              Refresh Page
            </button>
          </div>
        ) : null}

        {/* Dashboard Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Profile Section */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-3">Manage Profile</h3>
            <p className="text-gray-600 mb-4">
              Update your business information, services, and contact details.
            </p>
            <button
              onClick={() => router.push('/vendor/profile')}
              className="w-full px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 transition duration-200"
            >
              Edit Profile
            </button>
          </div>

          {/* Services Section */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-3">Services</h3>
            <p className="text-gray-600 mb-4">
              Add, edit, or remove services that you offer to customers.
            </p>
            <button
              onClick={() => router.push('/vendor/services')}
              className="w-full px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 transition duration-200"
            >
              Manage Services
            </button>
          </div>

          {/* Bookings Section */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-3">Bookings</h3>
            <p className="text-gray-600 mb-4">
              View and manage customer bookings and inquiries.
            </p>
            <button
              onClick={() => router.push('/vendor/bookings')}
              className="w-full px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 transition duration-200"
            >
              View Bookings
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
