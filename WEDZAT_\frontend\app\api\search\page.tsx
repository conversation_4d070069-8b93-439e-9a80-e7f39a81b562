"use client";
import React, { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import {
  TopNavigation,
  SideNavigation,
  RightSidebar,
} from "../../../components/HomeDashboard/Navigation";
import { Search } from "lucide-react";

function SearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams?.get("q") || "";
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [rightSidebarExpanded, setRightSidebarExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setIsClient(true);

    // Simulate search results loading
    setLoading(true);

    // This would be replaced with an actual API call in production
    setTimeout(() => {
      // Mock search results
      const mockResults = [
        {
          id: 1,
          type: "video",
          title: `Wedding video related to "${query}"`,
          thumbnail: "/pics/1stim.jfif",
          author: "Wedding Videographer",
        },
        {
          id: 2,
          type: "vendor",
          title: `Vendor matching "${query}"`,
          thumbnail: "/pics/2ndim.jfif",
          location: "Mumbai, India",
        },
        {
          id: 3,
          type: "idea",
          title: `Wedding idea for "${query}"`,
          thumbnail: "/pics/jpeg3.jfif",
          description: "Creative wedding ideas and inspiration",
        },
      ];

      setSearchResults(mockResults);
      setLoading(false);
    }, 1000);
  }, [query]);

  if (!isClient) {
    return null; // Don't render anything on the server
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Top Navigation */}
      <TopNavigation />

      <div className="flex flex-1">
        {/* Left Sidebar */}
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-6xl mx-auto">
            <div className="mb-8">
              <h1 className="text-2xl font-bold mb-2">
                Search Results for "{query}"
              </h1>
              <div className="relative w-full max-w-xl">
                <form action="/search" method="get">
                  <input
                    type="text"
                    name="q"
                    defaultValue={query}
                    placeholder="Search for Videos / Ideas / Vendor"
                    className="w-full pl-10 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                  <button
                    type="submit"
                    className="absolute right-3 top-2 bg-red-600 text-white p-1 rounded-full"
                  >
                    <Search size={16} />
                  </button>
                </form>
              </div>
            </div>

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
              </div>
            ) : searchResults.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-5xl mb-4">🔍</div>
                <h2 className="text-xl font-semibold mb-2">No results found</h2>
                <p className="text-gray-600">
                  We couldn't find any matches for "{query}". Try different keywords or check your spelling.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {searchResults.map((result) => (
                  <div
                    key={result.id}
                    className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
                  >
                    <div className="relative pb-[56.25%]">
                      <img
                        src={result.thumbnail}
                        alt={result.title}
                        className="absolute inset-0 w-full h-full object-cover"
                      />
                      <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded-full">
                        {result.type}
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-lg mb-1">{result.title}</h3>
                      {result.author && (
                        <p className="text-gray-600 text-sm">{result.author}</p>
                      )}
                      {result.location && (
                        <p className="text-gray-600 text-sm">{result.location}</p>
                      )}
                      {result.description && (
                        <p className="text-gray-600 text-sm mt-2">{result.description}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </main>

        {/* Right Sidebar */}
        <RightSidebar
          expanded={rightSidebarExpanded}
          onExpand={() => setRightSidebarExpanded(true)}
          onCollapse={() => setRightSidebarExpanded(false)}
        />
      </div>
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="flex flex-col min-h-screen bg-white">
        <TopNavigation />
        <div className="flex flex-1 items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
        </div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
}
