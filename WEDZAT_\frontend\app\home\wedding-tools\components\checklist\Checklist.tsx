"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";
import { Plus, Trash, Edit, Check, X, Download, Printer } from "lucide-react";
import { ChecklistItem, isCompleted } from "../../types";
import { getAuthToken } from "../../utils";
import Image from "next/image";

interface ChecklistProps {
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  setLoading: (loading: boolean) => void;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
}

const Checklist: React.FC<ChecklistProps> = ({
  setError,
  setSuccessMessage,
  setLoading,
  loading,
  error,
  successMessage
}) => {
  // Checklist states
  const [checklistItems, setChecklistItems] = useState<ChecklistItem[]>([]);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editTask, setEditTask] = useState<string>("");
  const [editCategory, setEditCategory] = useState<string>("");
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);

  // Modal state
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState<boolean>(false);
  const [newItemTask, setNewItemTask] = useState<string>("");
  const [newItemCategory, setNewItemCategory] = useState<string>("general");
  const [description, setDescription] = useState<string>("");
  const [newCustomCategory, setNewCustomCategory] = useState<string>("");
  const [showCustomCategoryInput, setShowCustomCategoryInput] = useState<boolean>(false);

  // Filter states
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [completedCount, setCompletedCount] = useState<number>(0);
  const [totalCount, setTotalCount] = useState<number>(0);

  // Fetch checklist items from the API
  useEffect(() => {
    fetchChecklistItems();
  }, []);

  const fetchChecklistItems = async () => {
    try {
      setLoading(true);
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setLoading(false);
        return;
      }

      console.log('Fetching checklist items with token:', token);

      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/checklist',
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      console.log('API response for checklist items:', response.data);

      if (response.data && response.data.checklist && Array.isArray(response.data.checklist)) {
        console.log('Found items in response:', response.data.checklist.length);
        const items = response.data.checklist;
        setChecklistItems(items);

        // Update counts
        const total = items.length;
        const completed = items.filter((item: ChecklistItem) => isCompleted(item.status)).length;
        setTotalCount(total);
        setCompletedCount(completed);

        // Get categories from API response
        if (response.data.categories && Array.isArray(response.data.categories)) {
          setAvailableCategories(response.data.categories);
        }

        setError(null); // Clear any previous errors
      } else if (response.data && response.data.checklist === null) {
        // API returned null items, which means no items exist yet
        console.log('API returned null checklist');
        setChecklistItems([]);
        setTotalCount(0);
        setCompletedCount(0);
        setError(null); // Clear any previous errors
      } else if (response.data && response.data.error) {
        // API returned an error message
        console.warn('API returned error:', response.data.error);
        setError(`Failed to load checklist items: ${response.data.error}`);
        setChecklistItems([]);
        setTotalCount(0);
        setCompletedCount(0);
      } else {
        console.warn('Unexpected API response format:', response.data);
        setError('Failed to load checklist items');
        setChecklistItems([]);
        setTotalCount(0);
        setCompletedCount(0);
      }
    } catch (err: any) {
      console.error('Error fetching checklist items:', err);
      // Check if the error is about empty items
      if (err.response && err.response.data && err.response.data.error === "No items found") {
        setChecklistItems([]);
        setError(null); // This is not really an error, just an empty state
      } else {
        setError('Failed to load checklist items');
        setChecklistItems([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Add a new checklist item
  const addChecklistItem = async (task: string, description: string, category: string) => {
    if (!task.trim()) return;

    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      // Create a new item with the required fields
      const newItem = {
        task: task,
        category: category,
        status: 'pending', // Initial status is pending
        description: description // Add description if API supports it
      };

      console.log('Sending new item to API:', newItem);

      const response = await axios.post(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-checklist-item',
        newItem,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      console.log('API response for add item:', response.data);

      if (response.data && response.data.item) {
        // Add the new item to the list
        const updatedItems = [...checklistItems, response.data.item];
        setChecklistItems(updatedItems);

        // Update counts
        setTotalCount(updatedItems.length);
        setCompletedCount(updatedItems.filter((item: ChecklistItem) => isCompleted(item.status)).length);

        setError(null); // Clear any previous errors
        setSuccessMessage('Task added successfully'); // Show success message

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } else {
        console.warn('Unexpected API response format:', response.data);
        setError('Failed to add checklist item');
      }
    } catch (err: any) {
      console.error('Error adding checklist item:', err);
      if (err.response && err.response.data && err.response.data.error) {
        setError(`Failed to add checklist item: ${err.response.data.error}`);
      } else {
        setError('Failed to add checklist item');
      }
      // Don't update UI on failure
    }
  };

  // Toggle checklist item completion status
  const toggleChecklistItem = async (id: string) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      const item = checklistItems.find(item => item.item_id === id);
      if (!item) {
        console.warn('Item not found in checklist:', id);
        return;
      }

      // Create the update payload
      const updatedItem = {
        item_id: id, // Ensure ID is included
        status: isCompleted(item.status) ? 'pending' : 'completed' // Toggle status
      };

      console.log('Sending update to API:', updatedItem);

      const response = await axios.put(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-checklist-item',
        updatedItem,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      console.log('API response for update item:', response.data);

      if (response.data && response.data.item) {
        // Update the item in the list
        const updatedItems = checklistItems.map((item: ChecklistItem) =>
          item.item_id === id ? { ...item, status: isCompleted(item.status) ? 'pending' : 'completed' } : item
        );

        setChecklistItems(updatedItems);

        // Update counts
        setTotalCount(updatedItems.length);
        setCompletedCount(updatedItems.filter((item: ChecklistItem) => isCompleted(item.status)).length);

        setError(null); // Clear any previous errors
        setSuccessMessage(`Task ${isCompleted(item.status) ? 'marked as pending' : 'marked as completed'}`); // Show success message

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } else {
        console.warn('Unexpected API response format:', response.data);
        setError('Failed to update checklist item');
      }
    } catch (err: any) {
      console.error('Error updating checklist item:', err);
      if (err.response && err.response.data && err.response.data.error) {
        setError(`Failed to update checklist item: ${err.response.data.error}`);
      } else {
        setError('Failed to update checklist item');
      }
      // Don't update UI on failure
    }
  };

  // Delete a checklist item
  const deleteChecklistItem = async (id: string) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      if (!id) {
        console.warn('No item ID provided for deletion');
        setError('Item ID is required for deletion');
        return;
      }

      console.log('Deleting item with ID:', id);

      const response = await axios.delete(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-checklist-item',
        {
          headers: {
            Authorization: `Bearer ${token}`
          },
          data: { item_id: id }
        }
      );

      console.log('API response for delete item:', response.data);

      // The API might return different response formats for success
      // Let's check all possible success indicators
      if (
        (response.data && response.data.success) || // Standard success format
        (response.data && response.data.message && response.data.message.includes('success')) || // Message contains 'success'
        (response.status >= 200 && response.status < 300) // HTTP success status code
      ) {
        // Remove the item from the list
        const updatedItems = checklistItems.filter((item: ChecklistItem) => item.item_id !== id);
        setChecklistItems(updatedItems);

        // Update counts
        setTotalCount(updatedItems.length);
        setCompletedCount(updatedItems.filter((item: ChecklistItem) => isCompleted(item.status)).length);

        setError(null); // Clear any previous errors
        setSuccessMessage('Task deleted successfully'); // Show success message

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } else {
        console.warn('Unexpected API response format:', response.data);
        setError('Failed to delete checklist item');
      }
    } catch (err: any) {
      console.error('Error deleting checklist item:', err);
      if (err.response && err.response.data && err.response.data.error) {
        setError(`Failed to delete checklist item: ${err.response.data.error}`);
      } else {
        setError('Failed to delete checklist item');
      }
      // Don't update UI on failure
    }
  };

  // Start editing a checklist item
  const startEditingItem = (item: ChecklistItem) => {
    setEditingItem(item.item_id);
    setEditTask(item.task);
    setEditCategory(item.category);
  };

  // Save edited checklist item
  const saveEditedItem = async (id: string) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      if (!id) {
        console.warn('No item ID provided for update');
        setError('Item ID is required for update');
        setEditingItem(null);
        return;
      }

      const item = checklistItems.find(item => item.item_id === id);
      if (!item) {
        console.warn('Item not found in checklist:', id);
        setEditingItem(null);
        return;
      }

      // Create the update payload with only the necessary fields
      const updatedItem = {
        item_id: id, // Ensure ID is included
        task: editTask,
        category: editCategory
      };

      console.log('Sending edit to API:', updatedItem);

      const response = await axios.put(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-checklist-item',
        updatedItem,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      console.log('API response for edit item:', response.data);

      if (response.data && response.data.item) {
        // Update the item in the list
        const updatedItems = checklistItems.map((item: ChecklistItem) =>
          item.item_id === id ? { ...item, task: editTask, category: editCategory } : item
        );

        setChecklistItems(updatedItems);
        setEditingItem(null);

        // No need to update counts as we're just changing text, not status

        setError(null); // Clear any previous errors
        setSuccessMessage('Task updated successfully'); // Show success message

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } else {
        console.warn('Unexpected API response format:', response.data);
        setError('Failed to update checklist item');
        setEditingItem(null);
      }
    } catch (err: any) {
      console.error('Error updating checklist item:', err);
      if (err.response && err.response.data && err.response.data.error) {
        setError(`Failed to update checklist item: ${err.response.data.error}`);
      } else {
        setError('Failed to update checklist item');
      }
      // Don't update UI on failure
      setEditingItem(null);
    }
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingItem(null);
  };

  // Filter checklist items based on status and category
  const getFilteredItems = () => {
    return checklistItems.filter((item: ChecklistItem) => {
      // Filter by status
      if (statusFilter !== 'all') {
        if (statusFilter === 'completed' && !isCompleted(item.status)) return false;
        if (statusFilter === 'pending' && isCompleted(item.status)) return false;
      }

      // Filter by category (case-insensitive)
      if (categoryFilter !== 'all' && item.category.toLowerCase() !== categoryFilter.toLowerCase()) return false;

      return true;
    });
  };

  // Get all unique categories from items and available categories
  const getCategories = () => {
    const categories = new Set<string>();

    // Add categories from checklist items
    checklistItems.forEach((item: ChecklistItem) => {
      if (item.category) categories.add(item.category.toLowerCase());
    });

    // Add categories from API
    availableCategories.forEach(category => {
      if (category) categories.add(category.toLowerCase());
    });

    return Array.from(categories);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-black">Checklist</h2>
        <div className="flex items-center gap-2">
          <button
            onClick={() => window.print()}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm text-black hover:bg-gray-50 flex items-center gap-1"
          >
            <Printer size={14} />
            <span>Print</span>
          </button>
          <button
            onClick={() => alert('Download functionality would go here')}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm text-black hover:bg-gray-50 flex items-center gap-1"
          >
            <Download size={14} />
            <span>Download</span>
          </button>
        </div>
      </div>

      {/* Status section */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2 text-black">Status</h3>
        <div className="bg-gray-100 p-3 rounded-md">
          <div className="text-[#B31B1E] font-medium mb-1">Completed {completedCount} out of {totalCount}</div>
          <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-[#B31B1E] rounded-full"
              style={{ width: totalCount > 0 ? `${(completedCount / totalCount) * 100}%` : '0%' }}
            ></div>
          </div>
        </div>
      </div>

      {/* Main content with categories and checklist */}
      <div className="flex flex-col md:flex-row gap-6">
        {/* Categories section */}
        <div className="w-full md:w-1/4">
          <h3 className="text-lg font-semibold mb-4 text-black">Category</h3>
          <div className="space-y-2">
            <div
              onClick={() => setCategoryFilter('all')}
              className={`p-2 rounded-md cursor-pointer flex justify-between items-center ${categoryFilter === 'all' ? 'bg-[#B31B1E] text-white' : 'bg-gray-100 text-black hover:bg-gray-200'}`}
            >
              <span>All</span>
              <span className="text-sm">{totalCount}</span>
            </div>

            {getCategories().map(category => (
              <div
                key={category}
                onClick={() => setCategoryFilter(category)}
                className={`p-2 rounded-md cursor-pointer flex justify-between items-center ${categoryFilter === category ? 'bg-[#B31B1E] text-white' : 'bg-gray-100 text-black hover:bg-gray-200'}`}
              >
                <span className="capitalize">{category.charAt(0).toUpperCase() + category.slice(1)}</span>
                <span className="text-sm">
                  {checklistItems.filter(item => item.category.toLowerCase() === category.toLowerCase()).length}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Checklist section */}
        <div className="flex-1">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-black">Checklist</h3>
            <button
              onClick={() => {
                setIsAddTaskModalOpen(true);
                setNewItemTask("");
                setDescription("");
                setNewItemCategory("general");
                setShowCustomCategoryInput(false);
              }}
              className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-[#B31B1E] flex items-center gap-1"
            >
              <Plus size={16} />
              <span>Add New Task</span>
            </button>
          </div>

      {/* Filter options for status */}
      <div className="mb-4 flex flex-wrap gap-2">
        <div className="flex border border-gray-300 rounded-md overflow-hidden">
          <button
            onClick={() => setStatusFilter('all')}
            className={`px-3 py-1 text-sm ${statusFilter === 'all' ? 'bg-[#B31B1E] text-white' : 'bg-white text-black'}`}
          >
            All
          </button>
          <button
            onClick={() => setStatusFilter('pending')}
            className={`px-3 py-1 text-sm ${statusFilter === 'pending' ? 'bg-[#B31B1E] text-white' : 'bg-white text-black'}`}
          >
            Pending
          </button>
          <button
            onClick={() => setStatusFilter('completed')}
            className={`px-3 py-1 text-sm ${statusFilter === 'completed' ? 'bg-[#B31B1E] text-white' : 'bg-white text-black'}`}
          >
            Completed
          </button>
        </div>
      </div>

          {/* Error message */}
          {error && (
            <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {/* Success message */}
          {successMessage && (
            <div className="mb-4 p-2 bg-green-100 text-green-700 rounded-md">
              {successMessage}
            </div>
          )}

          {/* Loading state */}
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#B31B1E]"></div>
            </div>
          ) : (
            /* Checklist items */
            <div className="space-y-2">
              {getFilteredItems().length === 0 ? (
                <p className="text-gray-500 text-center py-4">
                  {checklistItems.length === 0
                    ? "No items in your checklist yet. Add some tasks above!"
                    : "No items match your current filters."}
                </p>
              ) : (
                getFilteredItems().map((item) => (
                  <div
                    key={item.item_id}
                    className={`p-3 border rounded-md flex items-center justify-between ${
                      isCompleted(item.status) ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-300'
                    }`}
                  >
                {editingItem === item.item_id ? (
                  /* Editing mode */
                  <div className="flex-1 flex items-center gap-2">
                    <input
                      type="text"
                      value={editTask}
                      onChange={(e) => setEditTask(e.target.value)}
                      className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black placeholder-gray-700"
                    />
                    <select
                      value={editCategory}
                      onChange={(e) => {
                        if (e.target.value === "custom") {
                          // Handle custom category input
                          const customCategory = prompt("Enter custom category name:");
                          if (customCategory && customCategory.trim()) {
                            setEditCategory(customCategory.trim());
                            if (!availableCategories.includes(customCategory.trim())) {
                              setAvailableCategories([...availableCategories, customCategory.trim()]);
                            }
                          }
                        } else {
                          setEditCategory(e.target.value);
                        }
                      }}
                      className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                    >
                      {/* All categories from API */}
                      {availableCategories.length > 0 ? (
                        availableCategories.map(category => (
                          <option key={category} value={category} className="text-black">
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </option>
                        ))
                      ) : (
                        // Fallback categories if API hasn't loaded yet
                        <>
                          <option value="general" className="text-black">General</option>
                          <option value="planning" className="text-black">Planning</option>
                          <option value="venue" className="text-black">Venue</option>
                          <option value="vendors" className="text-black">Vendors</option>
                          <option value="food & drink" className="text-black">Food & Drink</option>
                          <option value="entertainment" className="text-black">Entertainment</option>
                          <option value="attire" className="text-black">Attire</option>
                          <option value="stationery" className="text-black">Stationery</option>
                          <option value="gifts & favors" className="text-black">Gifts & Favors</option>
                          <option value="ceremony" className="text-black">Ceremony</option>
                          <option value="transportation" className="text-black">Transportation</option>
                          <option value="honeymoon" className="text-black">Honeymoon</option>
                        </>
                      )}

                      {/* Custom category option */}
                      <option value="custom" className="text-black">+ Add Custom Category</option>
                    </select>
                    <button
                      onClick={() => saveEditedItem(item.item_id)}
                      className="p-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <Check size={16} />
                    </button>
                    <button
                      onClick={cancelEditing}
                      className="p-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ) : (
                  /* Display mode */
                  <>
                    <div className="flex items-center gap-3 flex-1">
                      <input
                        type="checkbox"
                        checked={isCompleted(item.status)}
                        onChange={() => toggleChecklistItem(item.item_id)}
                        className="h-5 w-5 text-[#B31B1E] rounded focus:ring-[#B31B1E]"
                      />
                      <div className="flex flex-col">
                        <span className={`text-black ${isCompleted(item.status) ? 'line-through text-gray-500' : ''}`}>
                          {item.task}
                        </span>
                        <span className="text-xs text-gray-500 capitalize">{item.category}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => startEditingItem(item)}
                        className="p-1 text-gray-500 hover:text-[#B31B1E] focus:outline-none"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => deleteChecklistItem(item.item_id)}
                        className="p-1 text-gray-500 hover:text-[#B31B1E] focus:outline-none"
                      >
                        <Trash size={16} />
                      </button>
                    </div>
                  </>
                )}
              </div>
            ))
          )}
            </div>
          )}
        </div>
      </div>

      {/* Add Task Modal */}
      {isAddTaskModalOpen && (
        <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
          <div
            className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
            style={{
              background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
              boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
            }}
          >
            <button
              onClick={() => setIsAddTaskModalOpen(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
            >
              <X size={20} />
            </button>

            {/* Logo */}
            <div className="flex justify-center pt-6">
              <div className="text-red-600">
                <Image
                  src="/pics/logo.png"
                  alt="Wedzat logo"
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
            </div>

            <div className="px-6 py-4">
              <h2 className="text-2xl font-bold mb-2 text-center" style={{ color: "#B31B1E" }}>
                Add New Task
              </h2>
              <p className="text-sm text-center text-gray-600 mb-6">
                Add a task to your wedding checklist
              </p>

              <div className="space-y-4">
                <div>
                  <div className="mb-1">
                    <label htmlFor="taskName" className="text-sm font-medium text-gray-700">
                      Task Name
                    </label>
                  </div>
                  <input
                    id="taskName"
                    type="text"
                    value={newItemTask}
                    onChange={(e) => setNewItemTask(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:border-[#B31B1E]"
                    placeholder="Enter task name"
                  />
                </div>

                <div>
                  <div className="mb-1">
                    <label htmlFor="description" className="text-sm font-medium text-gray-700">
                      Description of task
                    </label>
                  </div>
                  <textarea
                    id="description"
                    value={description || ""}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:border-[#B31B1E]"
                    placeholder="Enter task description"
                    rows={3}
                  />
                </div>

                <div>
                  <div className="mb-1">
                    <label htmlFor="category" className="text-sm font-medium text-gray-700">
                      Category
                    </label>
                  </div>
                  {showCustomCategoryInput ? (
                    <div className="flex items-center">
                      <input
                        type="text"
                        value={newCustomCategory}
                        onChange={(e) => setNewCustomCategory(e.target.value)}
                        className="flex-1 p-3 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:border-[#B31B1E]"
                        placeholder="Enter new category"
                      />
                      <button
                        onClick={() => setShowCustomCategoryInput(false)}
                        className="ml-2 p-2 text-gray-500 hover:text-gray-700"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ) : (
                    <select
                      id="category"
                      value={newItemCategory}
                      onChange={(e) => {
                        if (e.target.value === "custom") {
                          setShowCustomCategoryInput(true);
                          setNewCustomCategory("");
                        } else {
                          setNewItemCategory(e.target.value);
                        }
                      }}
                      className="w-full p-3 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:border-[#B31B1E]"
                    >
                      {/* All categories from API */}
                      {availableCategories.length > 0 ? (
                        availableCategories.map(category => (
                          <option key={category} value={category} className="text-black">
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </option>
                        ))
                      ) : (
                        // Fallback categories if API hasn't loaded yet
                        <>
                          <option value="general" className="text-black">General</option>
                          <option value="planning" className="text-black">Planning</option>
                          <option value="venue" className="text-black">Venue</option>
                          <option value="vendors" className="text-black">Vendors</option>
                          <option value="food & drink" className="text-black">Food & Drink</option>
                          <option value="entertainment" className="text-black">Entertainment</option>
                          <option value="attire" className="text-black">Attire</option>
                          <option value="stationery" className="text-black">Stationery</option>
                          <option value="gifts & favors" className="text-black">Gifts & Favors</option>
                          <option value="ceremony" className="text-black">Ceremony</option>
                          <option value="transportation" className="text-black">Transportation</option>
                          <option value="honeymoon" className="text-black">Honeymoon</option>
                        </>
                      )}

                      {/* Custom category option */}
                      <option value="custom" className="text-black">+ Add Custom Category</option>
                    </select>
                  )}
                </div>

                <button
                  onClick={() => {
                    const finalCategory = showCustomCategoryInput ? newCustomCategory : newItemCategory;
                    addChecklistItem(newItemTask, description || "", finalCategory);
                    setIsAddTaskModalOpen(false);
                  }}
                  disabled={!newItemTask.trim()}
                  className="w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6 disabled:bg-red-300 disabled:cursor-not-allowed"
                >
                  Save Task
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Checklist;
