{"version": 2, "waiters": {"FunctionExists": {"delay": 1, "operation": "GetFunction", "maxAttempts": 20, "acceptors": [{"state": "success", "matcher": "status", "expected": 200}, {"state": "retry", "matcher": "error", "expected": "ResourceNotFoundException"}]}, "FunctionActive": {"delay": 5, "maxAttempts": 60, "operation": "GetFunctionConfiguration", "description": "Waits for the function's State to be Active. This waiter uses GetFunctionConfiguration API. This should be used after new function creation.", "acceptors": [{"state": "success", "matcher": "path", "argument": "State", "expected": "Active"}, {"state": "failure", "matcher": "path", "argument": "State", "expected": "Failed"}, {"state": "retry", "matcher": "path", "argument": "State", "expected": "Pending"}]}, "FunctionUpdated": {"delay": 5, "maxAttempts": 60, "operation": "GetFunctionConfiguration", "description": "Waits for the function's LastUpdateStatus to be Successful. This waiter uses GetFunctionConfiguration API. This should be used after function updates.", "acceptors": [{"state": "success", "matcher": "path", "argument": "LastUpdateStatus", "expected": "Successful"}, {"state": "failure", "matcher": "path", "argument": "LastUpdateStatus", "expected": "Failed"}, {"state": "retry", "matcher": "path", "argument": "LastUpdateStatus", "expected": "InProgress"}]}, "FunctionActiveV2": {"delay": 1, "maxAttempts": 300, "operation": "GetFunction", "description": "Waits for the function's State to be Active. This waiter uses GetFunction API. This should be used after new function creation.", "acceptors": [{"state": "success", "matcher": "path", "argument": "Configuration.State", "expected": "Active"}, {"state": "failure", "matcher": "path", "argument": "Configuration.State", "expected": "Failed"}, {"state": "retry", "matcher": "path", "argument": "Configuration.State", "expected": "Pending"}]}, "FunctionUpdatedV2": {"delay": 1, "maxAttempts": 300, "operation": "GetFunction", "description": "Waits for the function's LastUpdateStatus to be Successful. This waiter uses GetFunction API. This should be used after function updates.", "acceptors": [{"state": "success", "matcher": "path", "argument": "Configuration.LastUpdateStatus", "expected": "Successful"}, {"state": "failure", "matcher": "path", "argument": "Configuration.LastUpdateStatus", "expected": "Failed"}, {"state": "retry", "matcher": "path", "argument": "Configuration.LastUpdateStatus", "expected": "InProgress"}]}, "PublishedVersionActive": {"delay": 5, "maxAttempts": 312, "operation": "GetFunctionConfiguration", "description": "Waits for the published version's State to be Active. This waiter uses GetFunctionConfiguration API. This should be used after new version is published.", "acceptors": [{"state": "success", "matcher": "path", "argument": "State", "expected": "Active"}, {"state": "failure", "matcher": "path", "argument": "State", "expected": "Failed"}, {"state": "retry", "matcher": "path", "argument": "State", "expected": "Pending"}]}}}