import json
import uuid
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from .utils import get_db_connection, validate_token

def get_guest_groups(event):
    """Get all guest groups for a user"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute(
            '''SELECT * FROM wedding_guest_groups
            WHERE user_id = %s
            ORDER BY name ASC''',
            (user_id,)
        )

        groups = cursor.fetchall()

        # If no groups exist, create default ones
        if not groups:
            print("No groups found for user. Creating default groups...")

            # Get default group templates (with NULL user_id)
            cursor.execute(
                '''SELECT * FROM wedding_guest_groups
                WHERE user_id IS NULL
                ORDER BY name ASC'''
            )

            default_templates = cursor.fetchall()

            # Create default groups based on templates
            print(f"Found {len(default_templates)} default templates. Creating user groups...")
            default_groups = []

            if default_templates:
                for template in default_templates:
                    default_groups.append({
                        'id': str(uuid.uuid4()),
                        'name': template['name'],
                        'description': template['description']
                    })
            else:
                # Fallback to hardcoded defaults if no templates exist
                print("No default templates found. Using hardcoded defaults...")
                default_groups = create_default_groups(user_id)

            # Insert the default groups for this user
            if default_groups:
                for group in default_groups:
                    cursor.execute(
                        '''INSERT INTO wedding_guest_groups
                        (group_id, user_id, name, description, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, NOW(), NOW())''',
                        (group['id'], user_id, group['name'], group['description'])
                    )

                conn.commit()
                print(f"Created {len(default_groups)} default groups for user {user_id}")

                # Query again to get the newly created groups
                cursor.execute(
                    '''SELECT * FROM wedding_guest_groups
                    WHERE user_id = %s
                    ORDER BY name ASC''',
                    (user_id,)
                )

                groups = cursor.fetchall()

            # Groups have already been inserted and fetched above

        # Convert date objects to strings for JSON serialization
        serializable_groups = []
        for group in groups:
            group_dict = dict(group)
            for key, value in group_dict.items():
                if isinstance(value, (datetime, date)):
                    group_dict[key] = value.isoformat()
            serializable_groups.append(group_dict)

        # Log the groups for debugging
        print(f"Returning {len(serializable_groups)} groups for user {user_id}:")
        for group in serializable_groups:
            print(f"  - {group['name']} (ID: {group['group_id']})")

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'groups': serializable_groups})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def create_default_groups(user_id, default_groups=None):
    """Create default guest groups for a new user"""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # If default_groups is provided, use those, otherwise create standard ones
        if not default_groups:
            # Default generic groups
            default_groups = [
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Couple',
                    'description': 'The couple getting married'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 1 Family',
                    'description': 'Family members of Partner 1'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 2 Family',
                    'description': 'Family members of Partner 2'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 1 Friends',
                    'description': 'Friends of Partner 1'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 2 Friends',
                    'description': 'Friends of Partner 2'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Mutual Friends',
                    'description': 'Friends shared by the couple'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 1 Colleagues',
                    'description': 'Work colleagues of Partner 1'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 2 Colleagues',
                    'description': 'Work colleagues of Partner 2'
                }
            ]

        # Insert the default groups
        created_groups = []
        for group in default_groups:
            cursor.execute(
                '''INSERT INTO wedding_guest_groups
                (group_id, user_id, name, description, created_at, updated_at)
                VALUES (%s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (group['id'], user_id, group['name'], group['description'])
            )

            new_group = cursor.fetchone()
            group_dict = dict(new_group)

            # Convert date objects to strings
            for key, value in group_dict.items():
                if isinstance(value, (datetime, date)):
                    group_dict[key] = value.isoformat()

            created_groups.append(group_dict)

        conn.commit()
        return created_groups
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error creating default groups: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def create_guest_group(event):
    """Create a new guest group"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event['body'])
        name = data.get('name')
        description = data.get('description', '')

        # Check if this is a request to create default groups
        is_first_time = data.get('is_first_time', False)

        if is_first_time:
            # This is now handled automatically by get_guest_groups
            # Just return the existing groups
            return get_guest_groups(event)

        # Regular group creation
        if not name:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Group name is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            group_id = str(uuid.uuid4())

            cursor.execute(
                '''INSERT INTO wedding_guest_groups
                (group_id, user_id, name, description, created_at, updated_at)
                VALUES (%s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (group_id, user_id, name, description)
            )

            new_group = cursor.fetchone()

            # Convert to a regular Python dict
            group_dict = dict(new_group)

            # Convert date objects to strings
            for key, value in group_dict.items():
                if isinstance(value, (datetime, date)):
                    group_dict[key] = value.isoformat()

            conn.commit()

            # Log the new group for debugging
            print(f"Created new group: {name} (ID: {group_id}) for user {user_id}")

            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'group': group_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def update_guest_group(event):
    """Update a guest group"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event['body'])
        group_id = data.get('group_id')
        name = data.get('name')
        description = data.get('description')

        if not group_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Group ID is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Check if the group belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_guest_groups
                WHERE group_id = %s AND user_id = %s''',
                (group_id, user_id)
            )

            existing_group = cursor.fetchone()
            if not existing_group:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Group not found'})
                }

            # Update fields
            cursor.execute(
                '''UPDATE wedding_guest_groups
                SET name = COALESCE(%s, name),
                    description = COALESCE(%s, description),
                    updated_at = NOW()
                WHERE group_id = %s AND user_id = %s
                RETURNING *''',
                (name, description, group_id, user_id)
            )

            updated_group = cursor.fetchone()

            # Convert to a regular Python dict
            group_dict = dict(updated_group)

            # Convert date objects to strings
            for key, value in group_dict.items():
                if isinstance(value, (datetime, date)):
                    group_dict[key] = value.isoformat()

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'group': group_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def delete_guest_group(event):
    """Delete a guest group"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event.get('body', '{}'))
        group_id = data.get('group_id')

        if not group_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Group ID is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if the group belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_guest_groups
                WHERE group_id = %s AND user_id = %s''',
                (group_id, user_id)
            )

            existing_group = cursor.fetchone()
            if not existing_group:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Group not found'})
                }

            # Delete the group (cascade will delete all guests in this group)
            cursor.execute(
                '''DELETE FROM wedding_guest_groups
                WHERE group_id = %s AND user_id = %s''',
                (group_id, user_id)
            )

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'message': 'Group deleted successfully'})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
