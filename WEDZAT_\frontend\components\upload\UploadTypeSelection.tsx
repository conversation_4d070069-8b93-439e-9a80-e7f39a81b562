// components/upload/UploadTypeSelection.tsx
'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { X } from 'lucide-react';

interface UploadTypeSelectionProps {
  onNext: (type: string) => void;
  onClose: () => void;
}

const UploadTypeSelection: React.FC<UploadTypeSelectionProps> = ({
  onNext,
  onClose
}) => {
  const [selectedType, setSelectedType] = useState<string | null>(null);

  const handleTypeSelect = (type: string) => {
    setSelectedType(type);
  };

  const handleNext = () => {
    if (selectedType) {
      onNext(selectedType);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-2 sm:p-4 bg-black/50 backdrop-blur-sm">
      <div className="bg-[#FFF7E8] rounded-2xl w-full max-w-[95%] sm:max-w-[90%] md:max-w-[80%] lg:max-w-[60%] xl:max-w-[50%] p-3 sm:p-6 relative overflow-y-auto max-h-[90vh]">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-2 sm:top-4 right-2 sm:right-4 text-gray-800 hover:text-red-600"
        >
          <X size={20} className="sm:w-6 sm:h-6" />
        </button>

        {/* Header */}
        <div className="flex items-center mb-4 sm:mb-6">
          <div className="flex items-center mr-2">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={28}
              height={28}
              className="object-cover sm:w-8 sm:h-8"
            />
          </div>
          <h2 className="text-lg sm:text-xl font-bold">Create New</h2>
          <div className="ml-2">
            <Image
              src="/pics/umoments.png"
              alt="Moments"
              width={18}
              height={18}
              className="object-cover sm:w-5 sm:h-5"
            />
          </div>
        </div>

        <p className="text-sm sm:text-base mb-4 sm:mb-6">Upload What is Relevant to you</p>

        {/* Upload type options */}
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6 border border-blue-200 border-dashed p-2 sm:p-4 rounded-lg">
          <div
            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer
              ${selectedType === 'flashes' ? 'border-red-600' : 'border-gray-200'}
            `}
            onClick={() => handleTypeSelect('flashes')}
          >
            <div className="flex justify-center mb-1 sm:mb-2">
              <Image
                src="/pics/uflashes.png"
                alt="Flashes"
                width={32}
                height={32}
                className="object-contain w-7 h-7 sm:w-10 sm:h-10"
              />
            </div>
            <div className="flex items-center justify-center">
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center
                ${selectedType === 'flashes' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedType === 'flashes' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-xs sm:text-sm font-medium">Flashes</span>
            </div>
          </div>

          <div
            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer
              ${selectedType === 'glimpses' ? 'border-red-600' : 'border-gray-200'}
            `}
            onClick={() => handleTypeSelect('glimpses')}
          >
            <div className="flex justify-center mb-1 sm:mb-2">
              <Image
                src="/pics/uglimpses.png"
                alt="Glimpses"
                width={32}
                height={32}
                className="object-contain w-7 h-7 sm:w-10 sm:h-10"
              />
            </div>
            <div className="flex items-center justify-center">
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center
                ${selectedType === 'glimpses' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedType === 'glimpses' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-xs sm:text-sm font-medium">Glimpses</span>
            </div>
          </div>

          <div
            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer
              ${selectedType === 'movies' ? 'border-red-600' : 'border-gray-200'}
            `}
            onClick={() => handleTypeSelect('movies')}
          >
            <div className="flex justify-center mb-1 sm:mb-2">
              <Image
                src="/pics/umovies.png"
                alt="Movies"
                width={32}
                height={32}
                className="object-contain w-7 h-7 sm:w-10 sm:h-10"
              />
            </div>
            <div className="flex items-center justify-center">
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center
                ${selectedType === 'movies' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedType === 'movies' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-xs sm:text-sm font-medium">Movies</span>
            </div>
          </div>

          <div
            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer
              ${selectedType === 'photos' ? 'border-red-600' : 'border-gray-200'}
            `}
            onClick={() => handleTypeSelect('photos')}
          >
            <div className="flex justify-center mb-1 sm:mb-2">
              <Image
                src="/pics/uphotos.png"
                alt="Photos"
                width={32}
                height={32}
                className="object-contain w-7 h-7 sm:w-10 sm:h-10"
              />
            </div>
            <div className="flex items-center justify-center">
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center
                ${selectedType === 'photos' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedType === 'photos' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-xs sm:text-sm font-medium">Photos</span>
            </div>
          </div>

          <div
            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer
              ${selectedType === 'live' ? 'border-red-600' : 'border-gray-200'}
            `}
            onClick={() => handleTypeSelect('live')}
          >
            <div className="flex justify-center mb-1 sm:mb-2">
              <Image
                src="/pics/ulive.png"
                alt="Live"
                width={32}
                height={32}
                className="object-contain w-7 h-7 sm:w-10 sm:h-10"
              />
            </div>
            <div className="flex items-center justify-center">
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center
                ${selectedType === 'live' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedType === 'live' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-xs sm:text-sm font-medium">Live</span>
            </div>
          </div>

          <div
            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer
              ${selectedType === 'moments' ? 'border-red-600' : 'border-gray-200'}
            `}
            onClick={() => handleTypeSelect('moments')}
          >
            <div className="flex justify-center mb-1 sm:mb-2">
              <Image
                src="/pics/umoments.png"
                alt="Moments"
                width={32}
                height={32}
                className="object-contain w-7 h-7 sm:w-10 sm:h-10"
              />
            </div>
            <div className="flex items-center justify-center">
              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center
                ${selectedType === 'moments' ? 'border-red-600' : 'border-gray-300'}
              `}>
                {selectedType === 'moments' && (
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full"></div>
                )}
              </div>
              <span className="text-xs sm:text-sm font-medium">Moments</span>
            </div>
          </div>
        </div>

        {/* Next button */}
        <div className="flex justify-center">
          <button
            onClick={handleNext}
            disabled={!selectedType}
            className={`flex items-center justify-center px-4 sm:px-6 py-1.5 sm:py-2 rounded-md text-sm sm:text-base ${selectedType
              ? 'bg-red-600 text-white hover:bg-red-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              } transition duration-200`}
          >
            Next
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 sm:h-5 sm:w-5 ml-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default UploadTypeSelection;