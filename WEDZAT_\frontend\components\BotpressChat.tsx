"use client";

import React, { useEffect } from 'react';

interface BotpressChatProps {
  botId?: string;
  hostUrl?: string;
  messagingUrl?: string;
  clientId?: string;
}

const BotpressChat: React.FC<BotpressChatProps> = ({
  botId = "8AYDGFUF",
  hostUrl = "https://cdn.botpress.cloud/webchat/v2.3",
  messagingUrl = "https://messaging.botpress.cloud",
  clientId = "8AYDGFUF-8AYDGFUF",
}) => {
  useEffect(() => {
    // This is a simple component that doesn't do anything
    // The actual Botpress initialization is handled in app/layout.tsx
    console.log('BotpressChat component mounted');

    return () => {
      console.log('BotpressChat component unmounted');
    };
  }, []);

  // This component doesn't render anything visible
  return null;
};

export default BotpressChat;
