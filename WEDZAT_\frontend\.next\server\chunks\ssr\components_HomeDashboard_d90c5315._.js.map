{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/HomeDashboard/UserAvatar.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Image from 'next/image';\r\nimport { User } from 'lucide-react';\r\n\r\ninterface UserAvatarProps {\r\n    username?: string;\r\n    size?: 'xs' | 'sm' | 'md' | 'lg';\r\n    showUsername?: boolean;\r\n    isGradientBorder?: boolean;\r\n    imageUrl?: string;\r\n    onClick?: () => void;\r\n}\r\n\r\nconst UserAvatar: React.FC<UserAvatarProps> = ({\r\n    username,\r\n    size = 'md',\r\n    showUsername = false,\r\n    isGradientBorder = false,\r\n    imageUrl,\r\n    onClick\r\n}) => {\r\n    // Size mappings\r\n    const sizeClasses = {\r\n        xs: 'w-4 h-4',\r\n        sm: 'w-8 h-8',\r\n        md: 'w-12 h-12',\r\n        lg: 'w-16 h-16'\r\n    };\r\n\r\n    // Text size based on avatar size\r\n    const textSizes = {\r\n        xs: 'text-[8px]',\r\n        sm: 'text-xs',\r\n        md: 'text-sm',\r\n        lg: 'text-base'\r\n    };\r\n    console.log(\"img url\", imageUrl);\r\n    return (\r\n        <div className=\"flex flex-col items-center\">\r\n            <div\r\n                className={`${sizeClasses[size]} rounded-full overflow-hidden ${isGradientBorder ? 'p-0.5 bg-gradient-to-br from-purple-400 to-pink-500' : ''\r\n                    } ${onClick ? 'cursor-pointer hover:opacity-90' : ''}`}\r\n                onClick={onClick}\r\n            >\r\n                <div className={`${isGradientBorder ? 'bg-white rounded-full w-full h-full' : ''} flex items-center justify-center`}>\r\n                    {imageUrl ? (\r\n                        <div className=\"relative w-full h-full\">\r\n                            {imageUrl.startsWith('data:') || !imageUrl.startsWith('/') ? (\r\n                                // For base64 images or absolute URLs\r\n                                <img\r\n                                    src={imageUrl}\r\n                                    alt={username || 'User'}\r\n                                    className=\"w-full h-full object-cover rounded-full\"\r\n                                    onError={(e) => {\r\n                                        console.error(`Failed to load avatar image: ${imageUrl}`);\r\n                                        const imgElement = e.target as HTMLImageElement;\r\n                                        if (imgElement) {\r\n                                            imgElement.src = '/pics/placeholder.svg';\r\n                                        }\r\n                                    }}\r\n                                />\r\n                            ) : (\r\n                                // For Next.js Image component (only for relative URLs)\r\n                                <Image\r\n                                    src={imageUrl}\r\n                                    alt={username || 'User'}\r\n                                    fill\r\n                                    sizes={size === 'xs' ? '16px' : size === 'sm' ? '32px' : size === 'md' ? '48px' : '64px'}\r\n                                    className=\"object-cover rounded-full\"\r\n                                    unoptimized={imageUrl.startsWith('/pics/') || imageUrl.includes('cloudfront.net')} // Skip optimization for local images and cloudfront URLs\r\n                                    onError={() => {\r\n                                        console.error(`Failed to load avatar image: ${imageUrl}`);\r\n                                        // Error handling is managed by the fallback UI\r\n                                    }}\r\n                                />\r\n                            )}\r\n                        </div>\r\n                    ) : (\r\n                        <div className={`${sizeClasses[size]} bg-gray-200 rounded-full flex items-center justify-center`}>\r\n                            <User className=\"text-gray-500\" size={size === 'sm' ? 16 : size === 'md' ? 24 : 32} />\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            </div>\r\n\r\n            {showUsername && username && (\r\n                <span className={`${textSizes[size]} mt-1 text-center truncate max-w-full`}>\r\n                    {username}\r\n                </span>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default UserAvatar;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAWA,MAAM,aAAwC,CAAC,EAC3C,QAAQ,EACR,OAAO,IAAI,EACX,eAAe,KAAK,EACpB,mBAAmB,KAAK,EACxB,QAAQ,EACR,OAAO,EACV;IACG,gBAAgB;IAChB,MAAM,cAAc;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IAEA,iCAAiC;IACjC,MAAM,YAAY;QACd,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IACA,QAAQ,GAAG,CAAC,WAAW;IACvB,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBACG,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,8BAA8B,EAAE,mBAAmB,wDAAwD,GACtI,CAAC,EAAE,UAAU,oCAAoC,IAAI;gBAC1D,SAAS;0BAET,cAAA,8OAAC;oBAAI,WAAW,GAAG,mBAAmB,wCAAwC,GAAG,iCAAiC,CAAC;8BAC9G,yBACG,8OAAC;wBAAI,WAAU;kCACV,SAAS,UAAU,CAAC,YAAY,CAAC,SAAS,UAAU,CAAC,OAClD,qCAAqC;sCACrC,8OAAC;4BACG,KAAK;4BACL,KAAK,YAAY;4BACjB,WAAU;4BACV,SAAS,CAAC;gCACN,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,UAAU;gCACxD,MAAM,aAAa,EAAE,MAAM;gCAC3B,IAAI,YAAY;oCACZ,WAAW,GAAG,GAAG;gCACrB;4BACJ;;;;;mCAGJ,uDAAuD;sCACvD,8OAAC,6HAAA,CAAA,UAAK;4BACF,KAAK;4BACL,KAAK,YAAY;4BACjB,IAAI;4BACJ,OAAO,SAAS,OAAO,SAAS,SAAS,OAAO,SAAS,SAAS,OAAO,SAAS;4BAClF,WAAU;4BACV,aAAa,SAAS,UAAU,CAAC,aAAa,SAAS,QAAQ,CAAC;4BAChE,SAAS;gCACL,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,UAAU;4BACxD,+CAA+C;4BACnD;;;;;;;;;;6CAKZ,8OAAC;wBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,0DAA0D,CAAC;kCAC5F,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;4BAAgB,MAAM,SAAS,OAAO,KAAK,SAAS,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;YAM/F,gBAAgB,0BACb,8OAAC;gBAAK,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,qCAAqC,CAAC;0BACrE;;;;;;;;;;;;AAKrB;uCAEe", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/HomeDashboard/Navigation.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport {\r\n  MessageCircle,\r\n  Search,\r\n  Home,\r\n  Users,\r\n  Bell,\r\n  Settings,\r\n  User,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  ShoppingBag,\r\n  Calendar,\r\n  Video,\r\n  Mail,\r\n  LogOut,\r\n  Menu,\r\n  X,\r\n  Upload,\r\n  MapPin,\r\n  Info,\r\n  ChevronDown,\r\n  Loader,\r\n} from \"lucide-react\";\r\n// import { useRouter } from 'next/navigation';\r\nimport UserAvatar from \"./UserAvatar\";\r\nimport { useGlobalUpload } from \"../../contexts/GlobalUploadManager\";\r\nimport { useAuth } from \"../../contexts/AuthContext\";\r\nimport axios from \"../../services/axiosConfig\";\r\nimport BotpressScripts from \"../BotpressScripts\";\r\n\r\n// Navigation Components\r\ninterface NavItemProps {\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  active?: boolean;\r\n  onClick?: () => void;\r\n  badge?: number;\r\n}\r\ninterface City {\r\n  id: number;\r\n  name: string;\r\n  region?: string;\r\n  country?: string;\r\n  description?: string;\r\n}\r\n\r\nconst NavItem: React.FC<NavItemProps> = ({\r\n  icon,\r\n  label,\r\n  active,\r\n  onClick,\r\n  badge,\r\n}) => {\r\n  return (\r\n    <li className=\"mb-6\">\r\n      <a\r\n        href=\"#\"\r\n        className={`flex items-center ${active\r\n          ? \"bg-red-600 text-white\"\r\n          : \"text-gray-700 hover:bg-red-50 hover:text-red-700\"\r\n          } p-3 rounded-lg transition-colors duration-150`}\r\n        onClick={onClick}\r\n      >\r\n        <div className=\"flex items-center justify-center w-10 h-10\">{icon}</div>\r\n        {label && <span className=\"ml-3 whitespace-nowrap\">{label}</span>}\r\n        {badge && badge > 0 && (\r\n          <span className=\"ml-auto bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n            {badge}\r\n          </span>\r\n        )}\r\n      </a>\r\n    </li>\r\n  );\r\n};\r\n\r\n// Top Navigation Component\r\nexport const TopNavigation: React.FC = () => {\r\n  const router = useRouter();\r\n  const { openUploadModal } = useGlobalUpload();\r\n  const { userProfile } = useAuth();\r\n  const [searchOpen, setSearchOpen] = useState(false);\r\n  const [cityDropdownOpen, setCityDropdownOpen] = useState(false);\r\n  const [selectedCity, setSelectedCity] = useState(\"\");\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [cities, setCities] = useState<City[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const searchTimeout = useRef<NodeJS.Timeout | null>(null);\r\n  // const router = useRouter();\r\n\r\n  // State for user avatar\r\n  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(undefined);\r\n  const [userName, setUserName] = useState<string>('User');\r\n\r\n  // Directly fetch user profile data from API\r\n  useEffect(() => {\r\n    const fetchUserProfile = async () => {\r\n      try {\r\n        // Get token from localStorage\r\n        const token = localStorage.getItem('token') ||\r\n          localStorage.getItem('jwt_token') ||\r\n          localStorage.getItem('wedzat_token');\r\n\r\n        if (!token) {\r\n          console.warn('No token found in localStorage');\r\n          return;\r\n        }\r\n\r\n        // Make API call to get user profile\r\n        const response = await axios.get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user', {\r\n          headers: { Authorization: `Bearer ${token}` }\r\n        });\r\n\r\n        console.log('User profile from API:', response.data);\r\n\r\n        // Set avatar URL and user name\r\n        if (response.data && response.data.user_avatar) {\r\n          setAvatarUrl(response.data.user_avatar);\r\n          localStorage.setItem('user_avatar', response.data.user_avatar);\r\n        }\r\n\r\n        if (response.data && response.data.name) {\r\n          setUserName(response.data.name);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching user profile:', error);\r\n\r\n        // Try to get avatar from localStorage as fallback\r\n        const storedAvatar = localStorage.getItem('user_avatar');\r\n        if (storedAvatar) {\r\n          console.log('Using avatar from localStorage');\r\n          setAvatarUrl(storedAvatar);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Call the function immediately\r\n    fetchUserProfile();\r\n\r\n    // Set up a refresh interval\r\n    const refreshInterval = setInterval(fetchUserProfile, 30000); // Refresh every 30 seconds\r\n\r\n    // Also use userProfile from AuthContext if available\r\n    if (userProfile) {\r\n      console.log('User profile in Navigation from AuthContext:', userProfile);\r\n      if (userProfile.user_avatar) {\r\n        setAvatarUrl(userProfile.user_avatar);\r\n      }\r\n      if (userProfile.name) {\r\n        setUserName(userProfile.name);\r\n      }\r\n    }\r\n\r\n    // Clean up the interval on component unmount\r\n    return () => clearInterval(refreshInterval);\r\n  }, [userProfile]);\r\n\r\n  // Default cities to show when no search is performed\r\n  const defaultCities: City[] = [\r\n    { id: 1, name: \"Mumbai\", description: \"Financial Capital\" },\r\n    { id: 2, name: \"Delhi\", description: \"National Capital\" },\r\n    { id: 3, name: \"Bangalore\", description: \"IT Hub\" },\r\n    { id: 4, name: \"Hyderabad\", description: \"Pearl City\" },\r\n    { id: 5, name: \"Chennai\", description: \"Gateway of South India\" },\r\n    { id: 6, name: \"Kolkata\", description: \"City of Joy\" },\r\n    { id: 7, name: \"Ahmedabad\", description: \"Manchester of India\" },\r\n    { id: 8, name: \"Pune\", description: \"Oxford of the East\" },\r\n  ];\r\n\r\n  // Function to search cities using API\r\n  const searchCities = async (query: string) => {\r\n    if (!query) {\r\n      setCities(defaultCities);\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Using the GeoDB Cities API - you'll need to replace 'YOUR_RAPIDAPI_KEY'\r\n      const response = await fetch(\r\n        `https://wft-geo-db.p.rapidapi.com/v1/geo/cities?namePrefix=${query}&limit=10&countryIds=IN`,\r\n        {\r\n          method: \"GET\",\r\n          headers: {\r\n            \"X-RapidAPI-Key\":\r\n              \"**************************************************\", // Replace with your actual API key\r\n            \"X-RapidAPI-Host\": \"wft-geo-db.p.rapidapi.com\",\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        // Silently fall back to default cities without showing an error\r\n        console.warn(\"Failed to fetch cities from API, using default cities\");\r\n        setCities(defaultCities);\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      // Transform API response to match our City interface\r\n      const formattedCities: City[] = data.data.map((city: any) => ({\r\n        id: city.id,\r\n        name: city.name,\r\n        region: city.region,\r\n        country: city.country,\r\n        description: city.region || \"India\",\r\n      }));\r\n\r\n      // If we got results, use them - don't fall back to default cities when API returns empty\r\n      setCities(formattedCities);\r\n    } catch (err) {\r\n      // Silently log error and fall back to default cities without showing error message\r\n      console.warn(\"Error fetching cities, using default cities:\", err);\r\n      setCities(defaultCities);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle search input change with debounce\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setSearchTerm(value);\r\n\r\n    // Clear any existing timeout\r\n    if (searchTimeout.current) {\r\n      clearTimeout(searchTimeout.current);\r\n    }\r\n\r\n    // Set a new timeout to debounce API calls\r\n    searchTimeout.current = setTimeout(() => {\r\n      searchCities(value);\r\n    }, 500); // 500ms debounce\r\n  };\r\n\r\n  // Initialize with default cities\r\n  useEffect(() => {\r\n    setCities(defaultCities);\r\n\r\n    // Cleanup function to clear timeout\r\n    return () => {\r\n      if (searchTimeout.current) {\r\n        clearTimeout(searchTimeout.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const handleSelectCity = (cityName: string) => {\r\n    setSelectedCity(cityName);\r\n    setCityDropdownOpen(false);\r\n    setSearchTerm(\"\"); // Clear search term after selection\r\n  };\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const dropdown = document.getElementById(\"city-dropdown\");\r\n      const button = document.getElementById(\"city-dropdown-button\");\r\n      if (\r\n        dropdown &&\r\n        button &&\r\n        !dropdown.contains(event.target as Node) &&\r\n        !button.contains(event.target as Node)\r\n      ) {\r\n        setCityDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white shadow-sm\">\r\n      <div className=\"max-w-full px-4 flex items-center justify-between h-20 border-b\">\r\n        {/* Logo - Updated to use image with navigation to home */}\r\n        <div className=\"flex items-center cursor-pointer\" onClick={() => router.push('/home')}>\r\n          <img\r\n            src=\"/pics/Logo Horizontal.png\"\r\n            alt=\"WEDZAT\"\r\n            className=\"h-13 object-contain\"\r\n          />\r\n        </div>\r\n\r\n        {/* Search bar - Desktop */}\r\n        <div className=\"hidden md:flex items-center space-x-4 flex-1 max-w-xl mx-4\">\r\n          <div className=\"relative w-full\">\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Search for Videos / Ideas / Vendor\"\r\n              className=\"w-full pl-10 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 font-inter font-medium text-[14px] leading-[100%] tracking-[0%] text-black placeholder:text-black\"\r\n            />\r\n            <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\r\n            <div className=\"absolute right-3 top-2.5\">\r\n              <svg\r\n                width=\"20\"\r\n                height=\"20\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"2\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                className=\"text-gray-400\"\r\n              >\r\n                <path d=\"M3 6h18M3 12h18M3 18h18\"></path>\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile search toggle */}\r\n        <button\r\n          className=\"md:hidden p-2 rounded-full hover:bg-gray-100\"\r\n          onClick={() => setSearchOpen(!searchOpen)}\r\n        >\r\n          <Search size={24} className=\"text-gray-700\" />\r\n        </button>\r\n\r\n        {/* Action buttons */}\r\n        <div className=\"flex items-center space-x-4\">\r\n          <button\r\n            className=\"bg-black px-4 py-2 rounded-full flex items-center space-x-2 hidden md:flex\"\r\n            onClick={openUploadModal}\r\n          >\r\n            <Upload size={18} style={{ color: \"white\" }} />\r\n            <span style={{ color: \"white\" }}>Upload</span>\r\n          </button>\r\n          <button\r\n            className=\"p-2 rounded-full hover:bg-gray-100 relative\"\r\n            onClick={() => router.push(\"/messages\")}\r\n          >\r\n            <MessageCircle size={24} className=\"text-gray-700\" />\r\n          </button>\r\n          <button className=\"p-2 rounded-full hover:bg-gray-100 relative\">\r\n            <Bell size={24} className=\"text-gray-700\" />\r\n            <span className=\"absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-white text-xs flex items-center justify-center\">\r\n              1\r\n            </span>\r\n          </button>\r\n          <div className=\"hidden md:flex items-center space-x-2 relative\">\r\n            <button\r\n              id=\"city-dropdown-button\"\r\n              className=\"flex items-center space-x-2 p-1 rounded-lg border border-gray-300 px-3 hover:bg-gray-50\"\r\n              onClick={() => setCityDropdownOpen(!cityDropdownOpen)}\r\n            >\r\n              <MapPin size={18} className=\"text-gray-700\" />\r\n              <span className=\"text-black font-medium\">\r\n                {selectedCity || \"Select City\"}\r\n              </span>\r\n              <ChevronDown size={16} className=\"text-gray-700\" />\r\n            </button>\r\n\r\n            {/* City Selection Dropdown - Updated with search */}\r\n            {cityDropdownOpen && (\r\n              <div\r\n                id=\"city-dropdown\"\r\n                className=\"absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\"\r\n              >\r\n                <div className=\"p-4 border-b border-gray-200\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-800\">\r\n                    Select Your City\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-500 mt-1\">\r\n                    Search for cities in India\r\n                  </p>\r\n\r\n                  {/* Search input for cities */}\r\n                  <div className=\"mt-2 relative\">\r\n                    <input\r\n                      type=\"text\"\r\n                      value={searchTerm}\r\n                      onChange={handleSearchChange}\r\n                      placeholder=\"Search cities...\"\r\n                      className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\r\n                    />\r\n                    {isLoading && (\r\n                      <Loader\r\n                        size={18}\r\n                        className=\"absolute right-2 top-2.5 animate-spin text-gray-400\"\r\n                      />\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"max-h-96 overflow-y-auto py-2\">\r\n                  {cities.length === 0 && !isLoading ? (\r\n                    <div className=\"px-4 py-3 text-gray-500\">\r\n                      No cities found\r\n                    </div>\r\n                  ) : (\r\n                    cities.map((city) => (\r\n                      <div\r\n                        key={city.id}\r\n                        className=\"px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150 border-b border-gray-100 last:border-b-0\"\r\n                        onClick={() => handleSelectCity(city.name)}\r\n                      >\r\n                        <div className=\"flex flex-col\">\r\n                          <span className=\"font-medium text-gray-800\">\r\n                            {city.name}\r\n                          </span>\r\n                          <span className=\"text-sm text-gray-500\">\r\n                            {city.description ||\r\n                              (city.region\r\n                                ? `${city.region}, ${city.country || \"India\"}`\r\n                                : \"India\")}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"p-3 border-t border-gray-200 bg-gray-50\">\r\n                  <button\r\n                    className=\"w-full bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg py-2 transition-colors duration-150\"\r\n                    onClick={() => setCityDropdownOpen(false)}\r\n                  >\r\n                    Close\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <button className=\"flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100\"\r\n            onClick={() => router.push('/userProfile')}  // Add this onClick handler\r\n          >\r\n            <UserAvatar\r\n              size=\"sm\"\r\n              imageUrl={avatarUrl}\r\n              username={userName || userProfile?.name}\r\n            />\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile search bar */}\r\n      {searchOpen && (\r\n        <div className=\"md:hidden p-2 bg-white border-t border-gray-200\">\r\n          <div className=\"relative\">\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Search for Videos / Ideas / Vendor\"\r\n              className=\"w-full pl-10 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500\"\r\n              autoFocus\r\n            />\r\n            <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\r\n            <button\r\n              className=\"absolute right-3 top-2.5\"\r\n              onClick={() => setSearchOpen(false)}\r\n            >\r\n              <X size={20} className=\"text-gray-500\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Mobile City Selection Modal - Updated with search */}\r\n      {cityDropdownOpen && (\r\n        <div className=\"md:hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center\">\r\n          <div className=\"bg-white rounded-lg w-11/12 max-w-md max-h-[80vh] overflow-hidden\">\r\n            <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\r\n              <h3 className=\"text-lg font-semibold text-gray-800\">\r\n                Select Your City\r\n              </h3>\r\n              <button onClick={() => setCityDropdownOpen(false)}>\r\n                <X size={20} className=\"text-gray-500\" />\r\n              </button>\r\n            </div>\r\n\r\n            {/* Search input for mobile */}\r\n            <div className=\"p-4 border-b border-gray-200\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={handleSearchChange}\r\n                  placeholder=\"Search cities...\"\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\"\r\n                />\r\n                {isLoading && (\r\n                  <Loader\r\n                    size={18}\r\n                    className=\"absolute right-2 top-2.5 animate-spin text-gray-400\"\r\n                  />\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-2 overflow-y-auto max-h-[60vh]\">\r\n              {cities.length === 0 && !isLoading ? (\r\n                <div className=\"px-4 py-3 text-gray-500\">No cities found</div>\r\n              ) : (\r\n                cities.map((city) => (\r\n                  <div\r\n                    key={city.id}\r\n                    className=\"p-3 hover:bg-gray-50 cursor-pointer transition-colors border-b border-gray-100\"\r\n                    onClick={() => handleSelectCity(city.name)}\r\n                  >\r\n                    <div className=\"flex flex-col\">\r\n                      <span className=\"font-medium text-gray-800\">\r\n                        {city.name}\r\n                      </span>\r\n                      <span className=\"text-sm text-gray-500\">\r\n                        {city.description ||\r\n                          (city.region\r\n                            ? `${city.region}, ${city.country || \"India\"}`\r\n                            : \"India\")}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\r\n              <button\r\n                className=\"w-full bg-red-600 text-white rounded-lg py-3 font-medium\"\r\n                onClick={() => setCityDropdownOpen(false)}\r\n              >\r\n                Close\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </header>\r\n  );\r\n};\r\n// Update SideNavigation to accept and use props\r\ninterface SideNavigationProps {\r\n  expanded: boolean;\r\n  onExpand: () => void;\r\n  onCollapse: () => void;\r\n}\r\n\r\nexport const SideNavigation: React.FC<SideNavigationProps> = ({\r\n  expanded,\r\n  onExpand,\r\n  onCollapse,\r\n}) => {\r\n  const [activePage, setActivePage] = useState(\"home\");\r\n\r\n  // Use your existing navItems array with all the SVGs\r\n  const navItems = [\r\n    // Info item with all SVGs (defaultSvg, activeSvg, expandedDefaultSvg, expandedActiveSvg)\r\n    // Home item with all SVGs\r\n    // Vendor item with all SVGs\r\n    // Other items...\r\n    {\r\n      id: \"info\",\r\n      label: \"Info\",\r\n      defaultSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"url(#paint0_linear_159_59427)\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M30.7503 13.6875H23.167C22.7228 13.6875 22.3545 13.3192 22.3545 12.875C22.3545 12.4308 22.7228 12.0625 23.167 12.0625H30.7503C31.1945 12.0625 31.5628 12.4308 31.5628 12.875C31.5628 13.3192 31.1945 13.6875 30.7503 13.6875Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.7503 19.1042H23.167C22.7228 19.1042 22.3545 18.7359 22.3545 18.2917C22.3545 17.8476 22.7228 17.4792 23.167 17.4792H30.7503C31.1945 17.4792 31.5628 17.8476 31.5628 18.2917C31.5628 18.7359 31.1945 19.1042 30.7503 19.1042Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.75 24.5208H11.25C10.8058 24.5208 10.4375 24.1524 10.4375 23.7083C10.4375 23.2641 10.8058 22.8958 11.25 22.8958H30.75C31.1942 22.8958 31.5625 23.2641 31.5625 23.7083C31.5625 24.1524 31.1942 24.5208 30.75 24.5208Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.75 29.9375H11.25C10.8058 29.9375 10.4375 29.5692 10.4375 29.125C10.4375 28.6808 10.8058 28.3125 11.25 28.3125H30.75C31.1942 28.3125 31.5625 28.6808 31.5625 29.125C31.5625 29.5692 31.1942 29.9375 30.75 29.9375Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.1348 19.3701C13.2031 19.3701 11.0039 18.2651 10.3323 16.2393C10.0614 15.4376 10.1264 14.5168 10.5056 13.791C10.8414 13.141 11.4048 12.6643 12.0981 12.4476C12.8023 12.231 13.5498 12.3285 14.1348 12.686C14.7198 12.3285 15.4673 12.231 16.1823 12.4476C16.8756 12.6643 17.4389 13.141 17.7748 13.791C18.1539 14.5168 18.2189 15.4376 17.9481 16.2393C17.2656 18.2976 14.9689 19.3701 14.1348 19.3701ZM11.8706 15.7193C12.3256 17.0843 13.8748 17.7126 14.1564 17.7451C14.4706 17.7126 15.9873 17.0085 16.4098 15.7301C16.5398 15.3401 16.5073 14.8743 16.3339 14.5385C16.1931 14.2676 15.9873 14.0943 15.7056 14.0076C15.4023 13.8993 14.9906 13.9535 14.8064 14.2243C14.6548 14.441 14.4164 14.571 14.1564 14.5818C13.8964 14.5818 13.6473 14.4626 13.4848 14.2568C13.2573 13.9643 12.8564 13.921 12.5639 14.0076C12.2823 14.0943 12.0764 14.2676 11.9356 14.5385C11.7731 14.8743 11.7406 15.3293 11.8706 15.7193Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_159_59427\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      activeSvg: (\r\n        // Your active SVG for info in collapsed state\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"#B31B1E\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M30.7503 13.6875H23.167C22.7228 13.6875 22.3545 13.3192 22.3545 12.875C22.3545 12.4308 22.7228 12.0625 23.167 12.0625H30.7503C31.1945 12.0625 31.5628 12.4308 31.5628 12.875C31.5628 13.3192 31.1945 13.6875 30.7503 13.6875Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.7503 19.1042H23.167C22.7228 19.1042 22.3545 18.7359 22.3545 18.2917C22.3545 17.8476 22.7228 17.4792 23.167 17.4792H30.7503C31.1945 17.4792 31.5628 17.8476 31.5628 18.2917C31.5628 18.7359 31.1945 19.1042 30.7503 19.1042Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.75 24.5208H11.25C10.8058 24.5208 10.4375 24.1524 10.4375 23.7083C10.4375 23.2641 10.8058 22.8958 11.25 22.8958H30.75C31.1942 22.8958 31.5625 23.2641 31.5625 23.7083C31.5625 24.1524 31.1942 24.5208 30.75 24.5208Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.75 29.9375H11.25C10.8058 29.9375 10.4375 29.5692 10.4375 29.125C10.4375 28.6808 10.8058 28.3125 11.25 28.3125H30.75C31.1942 28.3125 31.5625 28.6808 31.5625 29.125C31.5625 29.5692 31.1942 29.9375 30.75 29.9375Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.1348 19.3701C13.2031 19.3701 11.0039 18.2651 10.3323 16.2393C10.0614 15.4376 10.1264 14.5168 10.5056 13.791C10.8414 13.141 11.4048 12.6643 12.0981 12.4476C12.8023 12.231 13.5498 12.3285 14.1348 12.686C14.7198 12.3285 15.4673 12.231 16.1823 12.4476C16.8756 12.6643 17.4389 13.141 17.7748 13.791C18.1539 14.5168 18.2189 15.4376 17.9481 16.2393C17.2656 18.2976 14.9689 19.3701 14.1348 19.3701ZM11.8706 15.7193C12.3256 17.0843 13.8748 17.7126 14.1564 17.7451C14.4706 17.7126 15.9873 17.0085 16.4098 15.7301C16.5398 15.3401 16.5073 14.8743 16.3339 14.5385C16.1931 14.2676 15.9873 14.0943 15.7056 14.0076C15.4023 13.8993 14.9906 13.9535 14.8064 14.2243C14.6548 14.441 14.4164 14.571 14.1564 14.5818C13.8964 14.5818 13.6473 14.4626 13.4848 14.2568C13.2573 13.9643 12.8564 13.921 12.5639 14.0076C12.2823 14.0943 12.0764 14.2676 11.9356 14.5385C11.7731 14.8743 11.7406 15.3293 11.8706 15.7193Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedDefaultSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M25 5.25H18C17.59 5.25 17.25 4.91 17.25 4.5C17.25 4.09 17.59 3.75 18 3.75H25C25.41 3.75 25.75 4.09 25.75 4.5C25.75 4.91 25.41 5.25 25 5.25Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M25 10.25H18C17.59 10.25 17.25 9.91 17.25 9.5C17.25 9.09 17.59 8.75 18 8.75H25C25.41 8.75 25.75 9.09 25.75 9.5C25.75 9.91 25.41 10.25 25 10.25Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M25 15.25H7C6.59 15.25 6.25 14.91 6.25 14.5C6.25 14.09 6.59 13.75 7 13.75H25C25.41 13.75 25.75 14.09 25.75 14.5C25.75 14.91 25.41 15.25 25 15.25Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M25 20.25H7C6.59 20.25 6.25 19.91 6.25 19.5C6.25 19.09 6.59 18.75 7 18.75H25C25.41 18.75 25.75 19.09 25.75 19.5C25.75 19.91 25.41 20.25 25 20.25Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9.66255 10.4956C8.80255 10.4956 6.77255 9.4756 6.15255 7.6056C5.90255 6.8656 5.96255 6.0156 6.31255 5.3456C6.62255 4.7456 7.14255 4.3056 7.78255 4.1056C8.43255 3.9056 9.12255 3.9956 9.66255 4.3256C10.2025 3.9956 10.8925 3.9056 11.5525 4.1056C12.1925 4.3056 12.7125 4.7456 13.0225 5.3456C13.3725 6.0156 13.4325 6.8656 13.1825 7.6056C12.5525 9.5056 10.4325 10.4956 9.66255 10.4956ZM7.57255 7.1256C7.99255 8.3856 9.42255 8.9656 9.68255 8.9956C9.97255 8.9656 11.3725 8.3156 11.7625 7.1356C11.8825 6.7756 11.8525 6.3456 11.6925 6.0356C11.5625 5.7856 11.3725 5.6256 11.1125 5.5456C10.8325 5.4456 10.4525 5.4956 10.2825 5.7456C10.1425 5.9456 9.92255 6.0656 9.68255 6.0756C9.44255 6.0756 9.21255 5.9656 9.06255 5.7756C8.85255 5.5056 8.48255 5.4656 8.21255 5.5456C7.95255 5.6256 7.76255 5.7856 7.63255 6.0356C7.48255 6.3456 7.45255 6.7656 7.57255 7.1256Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M40.6548 7.31818V17.5H39.1186V7.31818H40.6548ZM44.2592 12.9659V17.5H42.7727V9.86364H44.1996V11.1065H44.294C44.4697 10.7022 44.7448 10.3774 45.1193 10.1321C45.4972 9.88684 45.9728 9.7642 46.5462 9.7642C47.0665 9.7642 47.5223 9.87358 47.9134 10.0923C48.3045 10.3078 48.6077 10.6293 48.8232 11.0568C49.0386 11.4844 49.1463 12.013 49.1463 12.6428V17.5H47.6598V12.8217C47.6598 12.2682 47.5156 11.8357 47.2273 11.5241C46.9389 11.2093 46.5429 11.0518 46.0391 11.0518C45.6944 11.0518 45.3878 11.1264 45.1193 11.2756C44.8542 11.4247 44.6437 11.6435 44.4879 11.9318C44.3355 12.2169 44.2592 12.5616 44.2592 12.9659ZM54.7741 9.86364V11.0568H50.4588V9.86364H54.7741ZM51.642 17.5V8.97869C51.642 8.50142 51.7464 8.10535 51.9553 7.79048C52.1641 7.4723 52.4408 7.23532 52.7855 7.07955C53.1302 6.92045 53.5047 6.84091 53.9091 6.84091C54.2074 6.84091 54.4626 6.86577 54.6747 6.91548C54.8868 6.96188 55.0443 7.00497 55.147 7.04474L54.799 8.24787C54.7294 8.22798 54.6399 8.20478 54.5305 8.17827C54.4212 8.14844 54.2886 8.13352 54.1328 8.13352C53.7715 8.13352 53.513 8.22301 53.3572 8.40199C53.2048 8.58097 53.1286 8.83949 53.1286 9.17756V17.5H51.642ZM59.2237 17.6541C58.5078 17.6541 57.883 17.4901 57.3494 17.1619C56.8158 16.8338 56.4015 16.3748 56.1065 15.7848C55.8116 15.1948 55.6641 14.5054 55.6641 13.7166C55.6641 12.9245 55.8116 12.2318 56.1065 11.6385C56.4015 11.0452 56.8158 10.5845 57.3494 10.2564C57.883 9.92827 58.5078 9.7642 59.2237 9.7642C59.9396 9.7642 60.5644 9.92827 61.098 10.2564C61.6316 10.5845 62.0459 11.0452 62.3409 11.6385C62.6359 12.2318 62.7834 12.9245 62.7834 13.7166C62.7834 14.5054 62.6359 15.1948 62.3409 15.7848C62.0459 16.3748 61.6316 16.8338 61.098 17.1619C60.5644 17.4901 59.9396 17.6541 59.2237 17.6541ZM59.2287 16.4062C59.6927 16.4062 60.0772 16.2836 60.3821 16.0384C60.687 15.7931 60.9124 15.4666 61.0582 15.0589C61.2074 14.6513 61.282 14.2022 61.282 13.7116C61.282 13.2244 61.2074 12.777 61.0582 12.3693C60.9124 11.9583 60.687 11.6286 60.3821 11.38C60.0772 11.1314 59.6927 11.0071 59.2287 11.0071C58.7614 11.0071 58.3736 11.1314 58.0653 11.38C57.7604 11.6286 57.5334 11.9583 57.3842 12.3693C57.2384 12.777 57.1655 13.2244 57.1655 13.7116C57.1655 14.2022 57.2384 14.6513 57.3842 15.0589C57.5334 15.4666 57.7604 15.7931 58.0653 16.0384C58.3736 16.2836 58.7614 16.4062 59.2287 16.4062Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedActiveSvg: (\r\n        // Your active SVG for info in expanded state (with text)\r\n\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M25 5.25H18C17.59 5.25 17.25 4.91 17.25 4.5C17.25 4.09 17.59 3.75 18 3.75H25C25.41 3.75 25.75 4.09 25.75 4.5C25.75 4.91 25.41 5.25 25 5.25Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M25 10.25H18C17.59 10.25 17.25 9.91 17.25 9.5C17.25 9.09 17.59 8.75 18 8.75H25C25.41 8.75 25.75 9.09 25.75 9.5C25.75 9.91 25.41 10.25 25 10.25Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M25 15.25H7C6.59 15.25 6.25 14.91 6.25 14.5C6.25 14.09 6.59 13.75 7 13.75H25C25.41 13.75 25.75 14.09 25.75 14.5C25.75 14.91 25.41 15.25 25 15.25Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M25 20.25H7C6.59 20.25 6.25 19.91 6.25 19.5C6.25 19.09 6.59 18.75 7 18.75H25C25.41 18.75 25.75 19.09 25.75 19.5C25.75 19.91 25.41 20.25 25 20.25Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9.66255 10.4956C8.80255 10.4956 6.77255 9.4756 6.15255 7.6056C5.90255 6.8656 5.96255 6.0156 6.31255 5.3456C6.62255 4.7456 7.14255 4.3056 7.78255 4.1056C8.43255 3.9056 9.12255 3.9956 9.66255 4.3256C10.2025 3.9956 10.8925 3.9056 11.5525 4.1056C12.1925 4.3056 12.7125 4.7456 13.0225 5.3456C13.3725 6.0156 13.4325 6.8656 13.1825 7.6056C12.5525 9.5056 10.4325 10.4956 9.66255 10.4956ZM7.57255 7.1256C7.99255 8.3856 9.42255 8.9656 9.68255 8.9956C9.97255 8.9656 11.3725 8.3156 11.7625 7.1356C11.8825 6.7756 11.8525 6.3456 11.6925 6.0356C11.5625 5.7856 11.3725 5.6256 11.1125 5.5456C10.8325 5.4456 10.4525 5.4956 10.2825 5.7456C10.1425 5.9456 9.92255 6.0656 9.68255 6.0756C9.44255 6.0756 9.21255 5.9656 9.06255 5.7756C8.85255 5.5056 8.48255 5.4656 8.21255 5.5456C7.95255 5.6256 7.76255 5.7856 7.63255 6.0356C7.48255 6.3456 7.45255 6.7656 7.57255 7.1256Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M40.6548 7.31818V17.5H39.1186V7.31818H40.6548ZM44.2592 12.9659V17.5H42.7727V9.86364H44.1996V11.1065H44.294C44.4697 10.7022 44.7448 10.3774 45.1193 10.1321C45.4972 9.88684 45.9728 9.7642 46.5462 9.7642C47.0665 9.7642 47.5223 9.87358 47.9134 10.0923C48.3045 10.3078 48.6077 10.6293 48.8232 11.0568C49.0386 11.4844 49.1463 12.013 49.1463 12.6428V17.5H47.6598V12.8217C47.6598 12.2682 47.5156 11.8357 47.2273 11.5241C46.9389 11.2093 46.5429 11.0518 46.0391 11.0518C45.6944 11.0518 45.3878 11.1264 45.1193 11.2756C44.8542 11.4247 44.6437 11.6435 44.4879 11.9318C44.3355 12.2169 44.2592 12.5616 44.2592 12.9659ZM54.7741 9.86364V11.0568H50.4588V9.86364H54.7741ZM51.642 17.5V8.97869C51.642 8.50142 51.7464 8.10535 51.9553 7.79048C52.1641 7.4723 52.4408 7.23532 52.7855 7.07955C53.1302 6.92045 53.5047 6.84091 53.9091 6.84091C54.2074 6.84091 54.4626 6.86577 54.6747 6.91548C54.8868 6.96188 55.0443 7.00497 55.147 7.04474L54.799 8.24787C54.7294 8.22798 54.6399 8.20478 54.5305 8.17827C54.4212 8.14844 54.2886 8.13352 54.1328 8.13352C53.7715 8.13352 53.513 8.22301 53.3572 8.40199C53.2048 8.58097 53.1286 8.83949 53.1286 9.17756V17.5H51.642ZM59.2237 17.6541C58.5078 17.6541 57.883 17.4901 57.3494 17.1619C56.8158 16.8338 56.4015 16.3748 56.1065 15.7848C55.8116 15.1948 55.6641 14.5054 55.6641 13.7166C55.6641 12.9245 55.8116 12.2318 56.1065 11.6385C56.4015 11.0452 56.8158 10.5845 57.3494 10.2564C57.883 9.92827 58.5078 9.7642 59.2237 9.7642C59.9396 9.7642 60.5644 9.92827 61.098 10.2564C61.6316 10.5845 62.0459 11.0452 62.3409 11.6385C62.6359 12.2318 62.7834 12.9245 62.7834 13.7166C62.7834 14.5054 62.6359 15.1948 62.3409 15.7848C62.0459 16.3748 61.6316 16.8338 61.098 17.1619C60.5644 17.4901 59.9396 17.6541 59.2237 17.6541ZM59.2287 16.4062C59.6927 16.4062 60.0772 16.2836 60.3821 16.0384C60.687 15.7931 60.9124 15.4666 61.0582 15.0589C61.2074 14.6513 61.282 14.2022 61.282 13.7116C61.282 13.2244 61.2074 12.777 61.0582 12.3693C60.9124 11.9583 60.687 11.6286 60.3821 11.38C60.0772 11.1314 59.6927 11.0071 59.2287 11.0071C58.7614 11.0071 58.3736 11.1314 58.0653 11.38C57.7604 11.6286 57.5334 11.9583 57.3842 12.3693C57.2384 12.777 57.1655 13.2244 57.1655 13.7116C57.1655 14.2022 57.2384 14.6513 57.3842 15.0589C57.5334 15.4666 57.7604 15.7931 58.0653 16.0384C58.3736 16.2836 58.7614 16.4062 59.2287 16.4062Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      hasOwnBackground: false, // Set to true if your SVG has its own background\r\n    },\r\n    {\r\n      id: \"home\",\r\n      label: \"Home\",\r\n      defaultSvg: (\r\n        <svg\r\n          width=\"46\"\r\n          height=\"46\"\r\n          viewBox=\"0 0 46 46\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"45\"\r\n            height=\"45\"\r\n            rx=\"22.5\"\r\n            fill=\"#B31B1E\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"45\"\r\n            height=\"45\"\r\n            rx=\"22.5\"\r\n            stroke=\"#4D0C0D\"\r\n          />\r\n          <path\r\n            d=\"M29.2728 34.6459H16.7278C13.7595 34.6459 11.3545 32.2301 11.3545 29.2617V21.2342C11.3545 19.7609 12.2645 17.9084 13.4345 16.9984L19.2737 12.4484C21.0287 11.0834 23.8345 11.0184 25.6545 12.2967L32.3495 16.9876C33.6387 17.8867 34.6462 19.8151 34.6462 21.3859V29.2726C34.6462 32.2301 32.2412 34.6459 29.2728 34.6459ZM20.2703 13.7267L14.4312 18.2767C13.662 18.8834 12.9795 20.2592 12.9795 21.2342V29.2617C12.9795 31.3309 14.6587 33.0209 16.7278 33.0209H29.2728C31.342 33.0209 33.0212 31.3417 33.0212 29.2726V21.3859C33.0212 20.3459 32.2737 18.9051 31.4178 18.3201L24.7228 13.6292C23.4878 12.7626 21.4512 12.8059 20.2703 13.7267Z\"\r\n            fill=\"white\"\r\n          />\r\n          <path\r\n            d=\"M23 30.3125C22.5558 30.3125 22.1875 29.9442 22.1875 29.5V26.25C22.1875 25.8058 22.5558 25.4375 23 25.4375C23.4442 25.4375 23.8125 25.8058 23.8125 26.25V29.5C23.8125 29.9442 23.4442 30.3125 23 30.3125Z\"\r\n            fill=\"white\"\r\n          />\r\n        </svg>\r\n      ),\r\n      activeSvg: (\r\n        <svg\r\n          width=\"46\"\r\n          height=\"46\"\r\n          viewBox=\"0 0 46 46\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"45\"\r\n            height=\"45\"\r\n            rx=\"22.5\"\r\n            fill=\"#B31B1E\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"45\"\r\n            height=\"45\"\r\n            rx=\"22.5\"\r\n            stroke=\"#4D0C0D\"\r\n          />\r\n          <path\r\n            d=\"M29.2728 34.6459H16.7278C13.7595 34.6459 11.3545 32.2301 11.3545 29.2617V21.2342C11.3545 19.7609 12.2645 17.9084 13.4345 16.9984L19.2737 12.4484C21.0287 11.0834 23.8345 11.0184 25.6545 12.2967L32.3495 16.9876C33.6387 17.8867 34.6462 19.8151 34.6462 21.3859V29.2726C34.6462 32.2301 32.2412 34.6459 29.2728 34.6459ZM20.2703 13.7267L14.4312 18.2767C13.662 18.8834 12.9795 20.2592 12.9795 21.2342V29.2617C12.9795 31.3309 14.6587 33.0209 16.7278 33.0209H29.2728C31.342 33.0209 33.0212 31.3417 33.0212 29.2726V21.3859C33.0212 20.3459 32.2737 18.9051 31.4178 18.3201L24.7228 13.6292C23.4878 12.7626 21.4512 12.8059 20.2703 13.7267Z\"\r\n            fill=\"white\"\r\n          />\r\n          <path\r\n            d=\"M23 30.3125C22.5558 30.3125 22.1875 29.9442 22.1875 29.5V26.25C22.1875 25.8058 22.5558 25.4375 23 25.4375C23.4442 25.4375 23.8125 25.8058 23.8125 26.25V29.5C23.8125 29.9442 23.4442 30.3125 23 30.3125Z\"\r\n            fill=\"white\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedDefaultSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"36\"\r\n          viewBox=\"0 0 128 36\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect width=\"128\" height=\"36\" rx=\"7\" fill=\"#B31B1E\" />\r\n          <path\r\n            d=\"M21.79 28.75H10.21C7.47 28.75 5.25 26.52 5.25 23.78V16.37C5.25 15.01 6.09 13.3 7.17 12.46L12.56 8.25997C14.18 6.99997 16.77 6.93997 18.45 8.11997L24.63 12.45C25.82 13.28 26.75 15.06 26.75 16.51V23.79C26.75 26.52 24.53 28.75 21.79 28.75ZM13.48 9.43997L8.09 13.64C7.38 14.2 6.75 15.47 6.75 16.37V23.78C6.75 25.69 8.3 27.25 10.21 27.25H21.79C23.7 27.25 25.25 25.7 25.25 23.79V16.51C25.25 15.55 24.56 14.22 23.77 13.68L17.59 9.34997C16.45 8.54997 14.57 8.58997 13.48 9.43997Z\"\r\n            fill=\"white\"\r\n          />\r\n          <path\r\n            d=\"M16 24.75C15.59 24.75 15.25 24.41 15.25 24V21C15.25 20.59 15.59 20.25 16 20.25C16.41 20.25 16.75 20.59 16.75 21V24C16.75 24.41 16.41 24.75 16 24.75Z\"\r\n            fill=\"white\"\r\n          />\r\n          <path\r\n            d=\"M38.8849 23.5V13.3182H41.0376V17.5192H45.4077V13.3182H47.5554V23.5H45.4077V19.294H41.0376V23.5H38.8849ZM52.7408 23.6491C51.9685 23.6491 51.3007 23.4851 50.7372 23.157C50.1771 22.8255 49.7446 22.3648 49.4396 21.7749C49.1347 21.1816 48.9822 20.4938 48.9822 19.7116C48.9822 18.9228 49.1347 18.2334 49.4396 17.6435C49.7446 17.0502 50.1771 16.5895 50.7372 16.2614C51.3007 15.9299 51.9685 15.7642 52.7408 15.7642C53.513 15.7642 54.1792 15.9299 54.7393 16.2614C55.3028 16.5895 55.737 17.0502 56.0419 17.6435C56.3468 18.2334 56.4993 18.9228 56.4993 19.7116C56.4993 20.4938 56.3468 21.1816 56.0419 21.7749C55.737 22.3648 55.3028 22.8255 54.7393 23.157C54.1792 23.4851 53.513 23.6491 52.7408 23.6491ZM52.7507 22.0085C53.102 22.0085 53.3954 21.9091 53.6307 21.7102C53.866 21.508 54.0433 21.233 54.1626 20.8849C54.2853 20.5369 54.3466 20.1409 54.3466 19.6967C54.3466 19.2526 54.2853 18.8565 54.1626 18.5085C54.0433 18.1605 53.866 17.8854 53.6307 17.6832C53.3954 17.4811 53.102 17.38 52.7507 17.38C52.3961 17.38 52.0978 17.4811 51.8558 17.6832C51.6172 17.8854 51.4366 18.1605 51.3139 18.5085C51.1946 18.8565 51.1349 19.2526 51.1349 19.6967C51.1349 20.1409 51.1946 20.5369 51.3139 20.8849C51.4366 21.233 51.6172 21.508 51.8558 21.7102C52.0978 21.9091 52.3961 22.0085 52.7507 22.0085ZM57.8764 23.5V15.8636H59.8949V17.2109H59.9844C60.1435 16.7635 60.4086 16.4105 60.7798 16.152C61.151 15.8935 61.5952 15.7642 62.1122 15.7642C62.6359 15.7642 63.0817 15.8951 63.4496 16.157C63.8175 16.4155 64.0627 16.7668 64.1854 17.2109H64.2649C64.4207 16.7734 64.7024 16.4238 65.1101 16.1619C65.5211 15.8968 66.0066 15.7642 66.5668 15.7642C67.2794 15.7642 67.8577 15.9912 68.3018 16.4453C68.7493 16.8961 68.973 17.5357 68.973 18.3643V23.5H66.8601V18.782C66.8601 18.3577 66.7474 18.0395 66.522 17.8274C66.2966 17.6153 66.0149 17.5092 65.6768 17.5092C65.2924 17.5092 64.9924 17.6319 64.777 17.8771C64.5616 18.1191 64.4538 18.4389 64.4538 18.8366V23.5H62.4006V18.7372C62.4006 18.3627 62.2929 18.0644 62.0774 17.8423C61.8653 17.6203 61.5852 17.5092 61.2372 17.5092C61.0019 17.5092 60.7898 17.5689 60.6009 17.6882C60.4152 17.8042 60.2678 17.9683 60.1584 18.1804C60.049 18.3892 59.9943 18.6345 59.9943 18.9162V23.5H57.8764ZM74.1261 23.6491C73.3406 23.6491 72.6644 23.4901 72.0977 23.1719C71.5342 22.8504 71.1 22.3963 70.7951 21.8097C70.4902 21.2197 70.3377 20.522 70.3377 19.7166C70.3377 18.9311 70.4902 18.2417 70.7951 17.6484C71.1 17.0552 71.5292 16.5928 72.0827 16.2614C72.6396 15.9299 73.2925 15.7642 74.0415 15.7642C74.5453 15.7642 75.0143 15.8454 75.4485 16.0078C75.886 16.1669 76.2672 16.4072 76.592 16.7287C76.9201 17.0502 77.1753 17.4545 77.3576 17.9418C77.5399 18.4257 77.631 18.9924 77.631 19.642V20.2237H71.1829V18.9112H75.6374C75.6374 18.6063 75.5711 18.3362 75.4386 18.1009C75.306 17.8655 75.122 17.6816 74.8867 17.549C74.6547 17.4131 74.3846 17.3452 74.0763 17.3452C73.7549 17.3452 73.4698 17.4197 73.2212 17.5689C72.976 17.7147 72.7837 17.9119 72.6445 18.1605C72.5053 18.4058 72.4341 18.6792 72.4308 18.9808V20.2287C72.4308 20.6065 72.5004 20.933 72.6396 21.2081C72.7821 21.4832 72.9826 21.6953 73.2411 21.8445C73.4996 21.9936 73.8062 22.0682 74.1609 22.0682C74.3962 22.0682 74.6116 22.035 74.8072 21.9688C75.0027 21.9025 75.1701 21.803 75.3093 21.6705C75.4485 21.5379 75.5546 21.3755 75.6275 21.1832L77.5863 21.3125C77.4869 21.7831 77.283 22.1941 76.9748 22.5455C76.6699 22.8935 76.2754 23.1652 75.7915 23.3608C75.311 23.553 74.7558 23.6491 74.1261 23.6491Z\"\r\n            fill=\"white\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedActiveSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"36\"\r\n          viewBox=\"0 0 128 36\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect width=\"128\" height=\"36\" rx=\"7\" fill=\"#B31B1E\" />\r\n          <path\r\n            d=\"M21.79 28.75H10.21C7.47 28.75 5.25 26.52 5.25 23.78V16.37C5.25 15.01 6.09 13.3 7.17 12.46L12.56 8.25997C14.18 6.99997 16.77 6.93997 18.45 8.11997L24.63 12.45C25.82 13.28 26.75 15.06 26.75 16.51V23.79C26.75 26.52 24.53 28.75 21.79 28.75ZM13.48 9.43997L8.09 13.64C7.38 14.2 6.75 15.47 6.75 16.37V23.78C6.75 25.69 8.3 27.25 10.21 27.25H21.79C23.7 27.25 25.25 25.7 25.25 23.79V16.51C25.25 15.55 24.56 14.22 23.77 13.68L17.59 9.34997C16.45 8.54997 14.57 8.58997 13.48 9.43997Z\"\r\n            fill=\"white\"\r\n          />\r\n          <path\r\n            d=\"M16 24.75C15.59 24.75 15.25 24.41 15.25 24V21C15.25 20.59 15.59 20.25 16 20.25C16.41 20.25 16.75 20.59 16.75 21V24C16.75 24.41 16.41 24.75 16 24.75Z\"\r\n            fill=\"white\"\r\n          />\r\n          <path\r\n            d=\"M38.8849 23.5V13.3182H41.0376V17.5192H45.4077V13.3182H47.5554V23.5H45.4077V19.294H41.0376V23.5H38.8849ZM52.7408 23.6491C51.9685 23.6491 51.3007 23.4851 50.7372 23.157C50.1771 22.8255 49.7446 22.3648 49.4396 21.7749C49.1347 21.1816 48.9822 20.4938 48.9822 19.7116C48.9822 18.9228 49.1347 18.2334 49.4396 17.6435C49.7446 17.0502 50.1771 16.5895 50.7372 16.2614C51.3007 15.9299 51.9685 15.7642 52.7408 15.7642C53.513 15.7642 54.1792 15.9299 54.7393 16.2614C55.3028 16.5895 55.737 17.0502 56.0419 17.6435C56.3468 18.2334 56.4993 18.9228 56.4993 19.7116C56.4993 20.4938 56.3468 21.1816 56.0419 21.7749C55.737 22.3648 55.3028 22.8255 54.7393 23.157C54.1792 23.4851 53.513 23.6491 52.7408 23.6491ZM52.7507 22.0085C53.102 22.0085 53.3954 21.9091 53.6307 21.7102C53.866 21.508 54.0433 21.233 54.1626 20.8849C54.2853 20.5369 54.3466 20.1409 54.3466 19.6967C54.3466 19.2526 54.2853 18.8565 54.1626 18.5085C54.0433 18.1605 53.866 17.8854 53.6307 17.6832C53.3954 17.4811 53.102 17.38 52.7507 17.38C52.3961 17.38 52.0978 17.4811 51.8558 17.6832C51.6172 17.8854 51.4366 18.1605 51.3139 18.5085C51.1946 18.8565 51.1349 19.2526 51.1349 19.6967C51.1349 20.1409 51.1946 20.5369 51.3139 20.8849C51.4366 21.233 51.6172 21.508 51.8558 21.7102C52.0978 21.9091 52.3961 22.0085 52.7507 22.0085ZM57.8764 23.5V15.8636H59.8949V17.2109H59.9844C60.1435 16.7635 60.4086 16.4105 60.7798 16.152C61.151 15.8935 61.5952 15.7642 62.1122 15.7642C62.6359 15.7642 63.0817 15.8951 63.4496 16.157C63.8175 16.4155 64.0627 16.7668 64.1854 17.2109H64.2649C64.4207 16.7734 64.7024 16.4238 65.1101 16.1619C65.5211 15.8968 66.0066 15.7642 66.5668 15.7642C67.2794 15.7642 67.8577 15.9912 68.3018 16.4453C68.7493 16.8961 68.973 17.5357 68.973 18.3643V23.5H66.8601V18.782C66.8601 18.3577 66.7474 18.0395 66.522 17.8274C66.2966 17.6153 66.0149 17.5092 65.6768 17.5092C65.2924 17.5092 64.9924 17.6319 64.777 17.8771C64.5616 18.1191 64.4538 18.4389 64.4538 18.8366V23.5H62.4006V18.7372C62.4006 18.3627 62.2929 18.0644 62.0774 17.8423C61.8653 17.6203 61.5852 17.5092 61.2372 17.5092C61.0019 17.5092 60.7898 17.5689 60.6009 17.6882C60.4152 17.8042 60.2678 17.9683 60.1584 18.1804C60.049 18.3892 59.9943 18.6345 59.9943 18.9162V23.5H57.8764ZM74.1261 23.6491C73.3406 23.6491 72.6644 23.4901 72.0977 23.1719C71.5342 22.8504 71.1 22.3963 70.7951 21.8097C70.4902 21.2197 70.3377 20.522 70.3377 19.7166C70.3377 18.9311 70.4902 18.2417 70.7951 17.6484C71.1 17.0552 71.5292 16.5928 72.0827 16.2614C72.6396 15.9299 73.2925 15.7642 74.0415 15.7642C74.5453 15.7642 75.0143 15.8454 75.4485 16.0078C75.886 16.1669 76.2672 16.4072 76.592 16.7287C76.9201 17.0502 77.1753 17.4545 77.3576 17.9418C77.5399 18.4257 77.631 18.9924 77.631 19.642V20.2237H71.1829V18.9112H75.6374C75.6374 18.6063 75.5711 18.3362 75.4386 18.1009C75.306 17.8655 75.122 17.6816 74.8867 17.549C74.6547 17.4131 74.3846 17.3452 74.0763 17.3452C73.7549 17.3452 73.4698 17.4197 73.2212 17.5689C72.976 17.7147 72.7837 17.9119 72.6445 18.1605C72.5053 18.4058 72.4341 18.6792 72.4308 18.9808V20.2287C72.4308 20.6065 72.5004 20.933 72.6396 21.2081C72.7821 21.4832 72.9826 21.6953 73.2411 21.8445C73.4996 21.9936 73.8062 22.0682 74.1609 22.0682C74.3962 22.0682 74.6116 22.035 74.8072 21.9688C75.0027 21.9025 75.1701 21.803 75.3093 21.6705C75.4485 21.5379 75.5546 21.3755 75.6275 21.1832L77.5863 21.3125C77.4869 21.7831 77.283 22.1941 76.9748 22.5455C76.6699 22.8935 76.2754 23.1652 75.7915 23.3608C75.311 23.553 74.7558 23.6491 74.1261 23.6491Z\"\r\n            fill=\"white\"\r\n          />\r\n        </svg>\r\n      ),\r\n      // Note: You may not need expanded SVGs for all items\r\n      // Only include these if you have special versions for expanded state\r\n      hasOwnBackground: false, // Set to false if your SVG doesn't have its own background\r\n    },\r\n    {\r\n      id: \"vendor\",\r\n      label: \"Vendor\",\r\n      defaultSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"url(#paint0_linear_159_59439)\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M23.9462 32.6458H18.1071C17.4138 32.6458 16.8071 32.6024 16.2546 32.5266C15.8104 32.4616 15.4963 32.05 15.5613 31.6058C15.6263 31.1616 16.0271 30.8366 16.4821 30.9124C16.9588 30.9774 17.4896 31.0099 18.0963 31.0099H23.9355C28.3663 31.0099 29.9371 29.439 29.9371 25.0082V20.144C29.9371 19.6999 30.3054 19.3315 30.7496 19.3315C31.1938 19.3315 31.5621 19.6999 31.5621 20.144V25.0082C31.5729 30.3599 29.2871 32.6458 23.9462 32.6458Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M11.293 25.6259C10.8488 25.6259 10.4805 25.2576 10.4805 24.8134V20.155C10.4805 19.7109 10.8488 19.3425 11.293 19.3425C11.7371 19.3425 12.1055 19.7109 12.1055 20.155V24.8134C12.1055 25.2576 11.7371 25.6259 11.293 25.6259Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M21.0322 21.8126C19.8405 21.8126 18.7571 21.3468 17.988 20.4909C17.2188 19.6351 16.8613 18.5192 16.9805 17.3276L17.7064 10.091C17.7497 9.67931 18.0964 9.35425 18.5189 9.35425H23.578C24.0005 9.35425 24.3472 9.66847 24.3905 10.091L25.1163 17.3276C25.2355 18.5192 24.878 19.6351 24.1088 20.4909C23.3072 21.3468 22.2239 21.8126 21.0322 21.8126ZM19.2338 10.9792L18.5839 17.4901C18.508 18.2159 18.7247 18.8984 19.1797 19.3968C20.1005 20.4151 21.9421 20.4151 22.863 19.3968C23.318 18.8876 23.5347 18.2051 23.4589 17.4901L22.8088 10.9792H19.2338Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M27.8575 21.8126C25.6583 21.8126 23.6975 20.0359 23.47 17.8475L22.7116 10.2534C22.69 10.0259 22.7658 9.79841 22.9175 9.62508C23.0692 9.45175 23.2858 9.35425 23.5241 9.35425H26.8283C30.0133 9.35425 31.4974 10.6867 31.9416 13.9584L32.245 16.9701C32.375 18.2484 31.985 19.4618 31.1508 20.3826C30.3166 21.3035 29.1466 21.8126 27.8575 21.8126ZM24.4233 10.9792L25.095 17.685C25.2358 19.0392 26.5033 20.1876 27.8575 20.1876C28.6808 20.1876 29.4175 19.8735 29.9483 19.2993C30.4683 18.7251 30.7066 17.956 30.6308 17.1326L30.3274 14.1535C29.9916 11.7051 29.2008 10.9792 26.8283 10.9792H24.4233Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.1415 21.8126C12.8523 21.8126 11.6824 21.3035 10.8482 20.3826C10.014 19.4618 9.62402 18.2484 9.75402 16.9701L10.0574 13.9909C10.5124 10.6868 11.9965 9.35425 15.1815 9.35425H18.4857C18.7132 9.35425 18.9298 9.45175 19.0923 9.62508C19.244 9.79841 19.3198 10.0259 19.2982 10.2534L18.5398 17.8475C18.3015 20.0359 16.3298 21.8126 14.1415 21.8126ZM15.1707 10.9792C12.7982 10.9792 12.0074 11.6942 11.6607 14.175L11.3682 17.1218C11.2815 17.9451 11.5307 18.7143 12.0507 19.2885C12.5707 19.8626 13.3182 20.1767 14.1415 20.1767C15.5065 20.1767 16.774 19.0285 16.904 17.6743L17.5757 10.9684H15.1707V10.9792Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M13.4163 33.7294C11.8997 33.7294 10.4805 33.0685 9.50552 31.9093C9.49469 31.8985 9.45134 31.8552 9.41884 31.801C9.25634 31.606 9.1155 31.4111 9.00717 31.2053C8.5305 30.4361 8.27051 29.526 8.27051 28.5727C8.27051 26.991 8.97471 25.5286 10.1989 24.5536C10.4155 24.3802 10.6538 24.2177 10.9138 24.0768C11.6613 23.6543 12.5388 23.416 13.4163 23.416C14.673 23.416 15.8322 23.8494 16.7639 24.6619C16.883 24.7485 17.0455 24.911 17.1863 25.0627C18.0638 26.0269 18.5513 27.2618 18.5513 28.551C18.5513 29.4935 18.2913 30.4144 17.8038 31.2053C17.5655 31.6169 17.273 31.9744 16.948 32.2561C16.883 32.3211 16.818 32.3861 16.7422 32.4403C15.8863 33.2744 14.6838 33.7294 13.4163 33.7294ZM10.6863 30.7936C10.7188 30.8261 10.773 30.8801 10.8163 30.956C11.4121 31.6601 12.3872 32.1152 13.4163 32.1152C14.2722 32.1152 15.0955 31.801 15.7238 31.2377C15.7563 31.1943 15.7997 31.151 15.8538 31.1185C16.0705 30.9343 16.2547 30.696 16.428 30.4143C16.7639 29.8618 16.948 29.2444 16.948 28.5944C16.948 27.7169 16.6122 26.8611 16.0055 26.2003C15.9297 26.1136 15.8538 26.0268 15.7563 25.9618C14.5863 24.9543 12.983 24.8028 11.7047 25.5286C11.5313 25.6261 11.3797 25.7235 11.2388 25.8427C10.3938 26.5143 9.90635 27.511 9.90635 28.5944C9.90635 29.2335 10.0797 29.8619 10.4155 30.4036C10.5022 30.5553 10.5888 30.6744 10.6863 30.7827C10.6755 30.7827 10.6863 30.7827 10.6863 30.7936Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M15.0409 29.3743H11.8018C11.3576 29.3743 10.9893 29.0059 10.9893 28.5618C10.9893 28.1176 11.3576 27.7493 11.8018 27.7493H15.0409C15.4851 27.7493 15.8534 28.1176 15.8534 28.5618C15.8534 29.0059 15.4851 29.3743 15.0409 29.3743Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M13.417 31.0316C12.9728 31.0316 12.6045 30.6633 12.6045 30.2191V26.98C12.6045 26.5358 12.9728 26.1675 13.417 26.1675C13.8612 26.1675 14.2295 26.5358 14.2295 26.98V30.2191C14.2295 30.6741 13.8612 31.0316 13.417 31.0316Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_159_59439\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      activeSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"#B31B1E\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M23.9462 32.6458H18.1071C17.4138 32.6458 16.8071 32.6024 16.2546 32.5266C15.8104 32.4616 15.4963 32.05 15.5613 31.6058C15.6263 31.1616 16.0271 30.8366 16.4821 30.9124C16.9588 30.9774 17.4896 31.0099 18.0963 31.0099H23.9355C28.3663 31.0099 29.9371 29.439 29.9371 25.0082V20.144C29.9371 19.6999 30.3054 19.3315 30.7496 19.3315C31.1938 19.3315 31.5621 19.6999 31.5621 20.144V25.0082C31.5729 30.3599 29.2871 32.6458 23.9462 32.6458Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M11.293 25.6259C10.8488 25.6259 10.4805 25.2576 10.4805 24.8134V20.155C10.4805 19.7109 10.8488 19.3425 11.293 19.3425C11.7371 19.3425 12.1055 19.7109 12.1055 20.155V24.8134C12.1055 25.2576 11.7371 25.6259 11.293 25.6259Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M21.0322 21.8126C19.8405 21.8126 18.7571 21.3468 17.988 20.4909C17.2188 19.6351 16.8613 18.5192 16.9805 17.3276L17.7064 10.091C17.7497 9.67931 18.0964 9.35425 18.5189 9.35425H23.578C24.0005 9.35425 24.3472 9.66847 24.3905 10.091L25.1163 17.3276C25.2355 18.5192 24.878 19.6351 24.1088 20.4909C23.3072 21.3468 22.2239 21.8126 21.0322 21.8126ZM19.2338 10.9792L18.5839 17.4901C18.508 18.2159 18.7247 18.8984 19.1797 19.3968C20.1005 20.4151 21.9421 20.4151 22.863 19.3968C23.318 18.8876 23.5347 18.2051 23.4589 17.4901L22.8088 10.9792H19.2338Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M27.8575 21.8126C25.6583 21.8126 23.6975 20.0359 23.47 17.8475L22.7116 10.2534C22.69 10.0259 22.7658 9.79841 22.9175 9.62508C23.0692 9.45175 23.2858 9.35425 23.5241 9.35425H26.8283C30.0133 9.35425 31.4974 10.6867 31.9416 13.9584L32.245 16.9701C32.375 18.2484 31.985 19.4618 31.1508 20.3826C30.3166 21.3035 29.1466 21.8126 27.8575 21.8126ZM24.4233 10.9792L25.095 17.685C25.2358 19.0392 26.5033 20.1876 27.8575 20.1876C28.6808 20.1876 29.4175 19.8735 29.9483 19.2993C30.4683 18.7251 30.7066 17.956 30.6308 17.1326L30.3274 14.1535C29.9916 11.7051 29.2008 10.9792 26.8283 10.9792H24.4233Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.1415 21.8126C12.8523 21.8126 11.6824 21.3035 10.8482 20.3826C10.014 19.4618 9.62402 18.2484 9.75402 16.9701L10.0574 13.9909C10.5124 10.6868 11.9965 9.35425 15.1815 9.35425H18.4857C18.7132 9.35425 18.9298 9.45175 19.0923 9.62508C19.244 9.79841 19.3198 10.0259 19.2982 10.2534L18.5398 17.8475C18.3015 20.0359 16.3298 21.8126 14.1415 21.8126ZM15.1707 10.9792C12.7982 10.9792 12.0074 11.6942 11.6607 14.175L11.3682 17.1218C11.2815 17.9451 11.5307 18.7143 12.0507 19.2885C12.5707 19.8626 13.3182 20.1767 14.1415 20.1767C15.5065 20.1767 16.774 19.0285 16.904 17.6743L17.5757 10.9684H15.1707V10.9792Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M13.4163 33.7294C11.8997 33.7294 10.4805 33.0685 9.50552 31.9093C9.49469 31.8985 9.45134 31.8552 9.41884 31.801C9.25634 31.606 9.1155 31.4111 9.00717 31.2053C8.5305 30.4361 8.27051 29.526 8.27051 28.5727C8.27051 26.991 8.97471 25.5286 10.1989 24.5536C10.4155 24.3802 10.6538 24.2177 10.9138 24.0768C11.6613 23.6543 12.5388 23.416 13.4163 23.416C14.673 23.416 15.8322 23.8494 16.7639 24.6619C16.883 24.7485 17.0455 24.911 17.1863 25.0627C18.0638 26.0269 18.5513 27.2618 18.5513 28.551C18.5513 29.4935 18.2913 30.4144 17.8038 31.2053C17.5655 31.6169 17.273 31.9744 16.948 32.2561C16.883 32.3211 16.818 32.3861 16.7422 32.4403C15.8863 33.2744 14.6838 33.7294 13.4163 33.7294ZM10.6863 30.7936C10.7188 30.8261 10.773 30.8801 10.8163 30.956C11.4121 31.6601 12.3872 32.1152 13.4163 32.1152C14.2722 32.1152 15.0955 31.801 15.7238 31.2377C15.7563 31.1943 15.7997 31.151 15.8538 31.1185C16.0705 30.9343 16.2547 30.696 16.428 30.4143C16.7639 29.8618 16.948 29.2444 16.948 28.5944C16.948 27.7169 16.6122 26.8611 16.0055 26.2003C15.9297 26.1136 15.8538 26.0268 15.7563 25.9618C14.5863 24.9543 12.983 24.8028 11.7047 25.5286C11.5313 25.6261 11.3797 25.7235 11.2388 25.8427C10.3938 26.5143 9.90635 27.511 9.90635 28.5944C9.90635 29.2335 10.0797 29.8619 10.4155 30.4036C10.5022 30.5553 10.5888 30.6744 10.6863 30.7827C10.6755 30.7827 10.6863 30.7827 10.6863 30.7936Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M15.0409 29.3743H11.8018C11.3576 29.3743 10.9893 29.0059 10.9893 28.5618C10.9893 28.1176 11.3576 27.7493 11.8018 27.7493H15.0409C15.4851 27.7493 15.8534 28.1176 15.8534 28.5618C15.8534 29.0059 15.4851 29.3743 15.0409 29.3743Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M13.417 31.0316C12.9728 31.0316 12.6045 30.6633 12.6045 30.2191V26.98C12.6045 26.5358 12.9728 26.1675 13.417 26.1675C13.8612 26.1675 14.2295 26.5358 14.2295 26.98V30.2191C14.2295 30.6741 13.8612 31.0316 13.417 31.0316Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedDefaultSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M18.7205 22.75H13.3306C12.6906 22.75 12.1305 22.71 11.6205 22.64C11.2105 22.58 10.9205 22.2 10.9805 21.79C11.0405 21.38 11.4106 21.08 11.8306 21.15C12.2706 21.21 12.7605 21.24 13.3205 21.24H18.7106C22.8006 21.24 24.2505 19.79 24.2505 15.7V11.21C24.2505 10.8 24.5905 10.46 25.0005 10.46C25.4105 10.46 25.7505 10.8 25.7505 11.21V15.7C25.7605 20.64 23.6505 22.75 18.7205 22.75Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M7.04004 16.27C6.63004 16.27 6.29004 15.93 6.29004 15.52V11.22C6.29004 10.81 6.63004 10.47 7.04004 10.47C7.45004 10.47 7.79004 10.81 7.79004 11.22V15.52C7.79004 15.93 7.45004 16.27 7.04004 16.27Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M16.0303 12.75C14.9303 12.75 13.9303 12.32 13.2203 11.53C12.5103 10.74 12.1803 9.70999 12.2903 8.60999L12.9603 1.93005C13.0003 1.55005 13.3203 1.25 13.7103 1.25H18.3803C18.7703 1.25 19.0903 1.54005 19.1303 1.93005L19.8003 8.60999C19.9103 9.70999 19.5803 10.74 18.8703 11.53C18.1303 12.32 17.1303 12.75 16.0303 12.75ZM14.3703 2.75L13.7703 8.76001C13.7003 9.43001 13.9003 10.06 14.3203 10.52C15.1703 11.46 16.8703 11.46 17.7203 10.52C18.1403 10.05 18.3403 9.42001 18.2703 8.76001L17.6703 2.75H14.3703Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M22.3295 12.75C20.2995 12.75 18.4895 11.11 18.2795 9.08997L17.5795 2.07996C17.5595 1.86996 17.6295 1.66 17.7695 1.5C17.9095 1.34 18.1095 1.25 18.3295 1.25H21.3795C24.3195 1.25 25.6895 2.48 26.0995 5.5L26.3795 8.28003C26.4995 9.46003 26.1395 10.5801 25.3695 11.4301C24.5995 12.2801 23.5195 12.75 22.3295 12.75ZM19.1595 2.75L19.7795 8.93994C19.9095 10.1899 21.0795 11.25 22.3295 11.25C23.0895 11.25 23.7695 10.9601 24.2595 10.4301C24.7395 9.90005 24.9595 9.19005 24.8895 8.43005L24.6095 5.68005C24.2995 3.42005 23.5695 2.75 21.3795 2.75H19.1595Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9.67028 12.75C8.48028 12.75 7.4003 12.2801 6.6303 11.4301C5.8603 10.5801 5.50029 9.46003 5.62029 8.28003L5.90032 5.53003C6.32032 2.48003 7.6903 1.25 10.6303 1.25H13.6803C13.8903 1.25 14.0903 1.34 14.2403 1.5C14.3803 1.66 14.4503 1.86996 14.4303 2.07996L13.7303 9.08997C13.5103 11.11 11.6903 12.75 9.67028 12.75ZM10.6203 2.75C8.43029 2.75 7.7003 3.40995 7.3803 5.69995L7.11028 8.42004C7.03028 9.18004 7.26029 9.89004 7.74029 10.42C8.22029 10.95 8.91028 11.24 9.67028 11.24C10.9303 11.24 12.1003 10.1801 12.2203 8.93005L12.8403 2.73999H10.6203V2.75Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9 23.75C7.6 23.75 6.29001 23.1399 5.39001 22.0699C5.38001 22.0599 5.34 22.02 5.31 21.97C5.16 21.79 5.02999 21.61 4.92999 21.42C4.48999 20.71 4.25 19.87 4.25 18.99C4.25 17.53 4.90003 16.18 6.03003 15.28C6.23003 15.12 6.45 14.97 6.69 14.84C7.38 14.45 8.19 14.23 9 14.23C10.16 14.23 11.23 14.63 12.09 15.38C12.2 15.46 12.35 15.61 12.48 15.75C13.29 16.64 13.74 17.78 13.74 18.97C13.74 19.84 13.5 20.69 13.05 21.42C12.83 21.8 12.56 22.13 12.26 22.39C12.2 22.45 12.14 22.5101 12.07 22.5601C11.28 23.3301 10.17 23.75 9 23.75ZM6.47998 21.04C6.50998 21.07 6.55998 21.1199 6.59998 21.1899C7.14998 21.8399 8.05 22.26 9 22.26C9.79 22.26 10.55 21.97 11.13 21.45C11.16 21.41 11.2 21.37 11.25 21.34C11.45 21.17 11.62 20.9499 11.78 20.6899C12.09 20.1799 12.26 19.61 12.26 19.01C12.26 18.2 11.95 17.41 11.39 16.8C11.32 16.72 11.25 16.64 11.16 16.58C10.08 15.65 8.59998 15.5101 7.41998 16.1801C7.25998 16.2701 7.11999 16.36 6.98999 16.47C6.20999 17.09 5.76001 18.01 5.76001 19.01C5.76001 19.6 5.91998 20.1801 6.22998 20.6801C6.30998 20.8201 6.38998 20.93 6.47998 21.03C6.46998 21.03 6.47998 21.03 6.47998 21.04Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M10.4998 19.73H7.50977C7.09977 19.73 6.75977 19.39 6.75977 18.98C6.75977 18.57 7.09977 18.23 7.50977 18.23H10.4998C10.9098 18.23 11.2498 18.57 11.2498 18.98C11.2498 19.39 10.9098 19.73 10.4998 19.73Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9 21.26C8.59 21.26 8.25 20.92 8.25 20.51V17.52C8.25 17.11 8.59 16.77 9 16.77C9.41 16.77 9.75 17.11 9.75 17.52V20.51C9.75 20.93 9.41 21.26 9 21.26Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M40.0085 7.31818L42.8423 15.6108H42.9567L45.7905 7.31818H47.451L43.7869 17.5H42.0121L38.348 7.31818H40.0085ZM51.3276 17.6541C50.5752 17.6541 49.9273 17.4934 49.3837 17.1719C48.8435 16.8471 48.4258 16.3913 48.1309 15.8047C47.8392 15.2147 47.6934 14.5237 47.6934 13.7315C47.6934 12.9493 47.8392 12.2599 48.1309 11.6634C48.4258 11.0668 48.8368 10.6011 49.3638 10.2663C49.8941 9.93158 50.5139 9.7642 51.2232 9.7642C51.6541 9.7642 52.0717 9.83546 52.476 9.97798C52.8804 10.1205 53.2433 10.3442 53.5648 10.6491C53.8863 10.9541 54.1399 11.3501 54.3255 11.8374C54.5111 12.3213 54.6039 12.9096 54.6039 13.6023V14.1293H48.5336V13.0156H53.1472C53.1472 12.6245 53.0676 12.2782 52.9086 11.9766C52.7495 11.6716 52.5257 11.4313 52.2374 11.2557C51.9524 11.08 51.6176 10.9922 51.2331 10.9922C50.8155 10.9922 50.4509 11.0949 50.1394 11.3004C49.8311 11.5026 49.5925 11.7678 49.4235 12.0959C49.2578 12.4207 49.1749 12.7737 49.1749 13.1548V14.0249C49.1749 14.5353 49.2644 14.9695 49.4434 15.3274C49.6257 15.6854 49.8792 15.9588 50.204 16.1477C50.5288 16.3333 50.9083 16.4261 51.3425 16.4261C51.6242 16.4261 51.8811 16.3864 52.1131 16.3068C52.3451 16.224 52.5456 16.1013 52.7147 15.9389C52.8837 15.7765 53.013 15.576 53.1025 15.3374L54.5094 15.5909C54.3967 16.0052 54.1945 16.3681 53.9029 16.6797C53.6145 16.9879 53.2516 17.2282 52.8141 17.4006C52.3799 17.5696 51.8844 17.6541 51.3276 17.6541ZM57.7397 12.9659V17.5H56.2532V9.86364H57.68V11.1065H57.7745C57.9502 10.7022 58.2253 10.3774 58.5998 10.1321C58.9776 9.88684 59.4532 9.7642 60.0266 9.7642C60.547 9.7642 61.0027 9.87358 61.3938 10.0923C61.7849 10.3078 62.0882 10.6293 62.3036 11.0568C62.5191 11.4844 62.6268 12.013 62.6268 12.6428V17.5H61.1403V12.8217C61.1403 12.2682 60.9961 11.8357 60.7077 11.5241C60.4194 11.2093 60.0233 11.0518 59.5195 11.0518C59.1748 11.0518 58.8683 11.1264 58.5998 11.2756C58.3346 11.4247 58.1242 11.6435 57.9684 11.9318C57.8159 12.2169 57.7397 12.5616 57.7397 12.9659ZM67.479 17.6491C66.8626 17.6491 66.3124 17.4917 65.8285 17.1768C65.3479 16.8587 64.9701 16.4062 64.695 15.8196C64.4232 15.2296 64.2873 14.522 64.2873 13.6967C64.2873 12.8714 64.4248 12.1655 64.6999 11.5788C64.9783 10.9922 65.3595 10.5431 65.8434 10.2315C66.3273 9.91998 66.8758 9.7642 67.489 9.7642C67.9629 9.7642 68.3441 9.84375 68.6325 10.0028C68.9241 10.1586 69.1495 10.3409 69.3086 10.5497C69.471 10.7585 69.5969 10.9425 69.6864 11.1016H69.7759V7.31818H71.2624V17.5H69.8107V16.3118H69.6864C69.5969 16.4742 69.4677 16.6598 69.2987 16.8686C69.1329 17.0774 68.9042 17.2597 68.6126 17.4155C68.3209 17.5713 67.9431 17.6491 67.479 17.6491ZM67.8072 16.3814C68.2347 16.3814 68.596 16.2687 68.891 16.0433C69.1893 15.8146 69.4147 15.4981 69.5671 15.0938C69.7229 14.6894 69.8008 14.2187 69.8008 13.6818C69.8008 13.1515 69.7246 12.6875 69.5721 12.2898C69.4196 11.892 69.1959 11.5821 68.9009 11.3601C68.6059 11.138 68.2414 11.027 67.8072 11.027C67.3597 11.027 66.9869 11.143 66.6886 11.375C66.3903 11.607 66.1649 11.9235 66.0124 12.3246C65.8633 12.7256 65.7887 13.178 65.7887 13.6818C65.7887 14.1922 65.8649 14.6513 66.0174 15.0589C66.1699 15.4666 66.3952 15.7898 66.6935 16.0284C66.9951 16.2637 67.3664 16.3814 67.8072 16.3814ZM76.6007 17.6541C75.8848 17.6541 75.26 17.4901 74.7264 17.1619C74.1928 16.8338 73.7785 16.3748 73.4835 15.7848C73.1885 15.1948 73.041 14.5054 73.041 13.7166C73.041 12.9245 73.1885 12.2318 73.4835 11.6385C73.7785 11.0452 74.1928 10.5845 74.7264 10.2564C75.26 9.92827 75.8848 9.7642 76.6007 9.7642C77.3166 9.7642 77.9413 9.92827 78.475 10.2564C79.0086 10.5845 79.4229 11.0452 79.7179 11.6385C80.0128 12.2318 80.1603 12.9245 80.1603 13.7166C80.1603 14.5054 80.0128 15.1948 79.7179 15.7848C79.4229 16.3748 79.0086 16.8338 78.475 17.1619C77.9413 17.4901 77.3166 17.6541 76.6007 17.6541ZM76.6056 16.4062C77.0697 16.4062 77.4541 16.2836 77.7591 16.0384C78.064 15.7931 78.2894 15.4666 78.4352 15.0589C78.5843 14.6513 78.6589 14.2022 78.6589 13.7116C78.6589 13.2244 78.5843 12.777 78.4352 12.3693C78.2894 11.9583 78.064 11.6286 77.7591 11.38C77.4541 11.1314 77.0697 11.0071 76.6056 11.0071C76.1383 11.0071 75.7505 11.1314 75.4423 11.38C75.1374 11.6286 74.9103 11.9583 74.7612 12.3693C74.6154 12.777 74.5424 13.2244 74.5424 13.7116C74.5424 14.2022 74.6154 14.6513 74.7612 15.0589C74.9103 15.4666 75.1374 15.7931 75.4423 16.0384C75.7505 16.2836 76.1383 16.4062 76.6056 16.4062ZM81.8196 17.5V9.86364H83.2564V11.0767H83.3359C83.4751 10.6657 83.7204 10.3426 84.0717 10.1072C84.4264 9.86861 84.8274 9.74929 85.2749 9.74929C85.3677 9.74929 85.477 9.7526 85.603 9.75923C85.7322 9.76586 85.8333 9.77415 85.9062 9.78409V11.206C85.8466 11.1894 85.7405 11.1712 85.5881 11.1513C85.4356 11.1281 85.2831 11.1165 85.1307 11.1165C84.7794 11.1165 84.4661 11.1911 84.1911 11.3402C83.9193 11.486 83.7038 11.6899 83.5447 11.9517C83.3857 12.2102 83.3061 12.5052 83.3061 12.8366V17.5H81.8196Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedActiveSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M18.7205 22.75H13.3306C12.6906 22.75 12.1305 22.71 11.6205 22.64C11.2105 22.58 10.9205 22.2 10.9805 21.79C11.0405 21.38 11.4106 21.08 11.8306 21.15C12.2706 21.21 12.7605 21.24 13.3205 21.24H18.7106C22.8006 21.24 24.2505 19.79 24.2505 15.7V11.21C24.2505 10.8 24.5905 10.46 25.0005 10.46C25.4105 10.46 25.7505 10.8 25.7505 11.21V15.7C25.7605 20.64 23.6505 22.75 18.7205 22.75Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M7.04004 16.27C6.63004 16.27 6.29004 15.93 6.29004 15.52V11.22C6.29004 10.81 6.63004 10.47 7.04004 10.47C7.45004 10.47 7.79004 10.81 7.79004 11.22V15.52C7.79004 15.93 7.45004 16.27 7.04004 16.27Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M16.0303 12.75C14.9303 12.75 13.9303 12.32 13.2203 11.53C12.5103 10.74 12.1803 9.70999 12.2903 8.60999L12.9603 1.93005C13.0003 1.55005 13.3203 1.25 13.7103 1.25H18.3803C18.7703 1.25 19.0903 1.54005 19.1303 1.93005L19.8003 8.60999C19.9103 9.70999 19.5803 10.74 18.8703 11.53C18.1303 12.32 17.1303 12.75 16.0303 12.75ZM14.3703 2.75L13.7703 8.76001C13.7003 9.43001 13.9003 10.06 14.3203 10.52C15.1703 11.46 16.8703 11.46 17.7203 10.52C18.1403 10.05 18.3403 9.42001 18.2703 8.76001L17.6703 2.75H14.3703Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M22.3295 12.75C20.2995 12.75 18.4895 11.11 18.2795 9.08997L17.5795 2.07996C17.5595 1.86996 17.6295 1.66 17.7695 1.5C17.9095 1.34 18.1095 1.25 18.3295 1.25H21.3795C24.3195 1.25 25.6895 2.48 26.0995 5.5L26.3795 8.28003C26.4995 9.46003 26.1395 10.5801 25.3695 11.4301C24.5995 12.2801 23.5195 12.75 22.3295 12.75ZM19.1595 2.75L19.7795 8.93994C19.9095 10.1899 21.0795 11.25 22.3295 11.25C23.0895 11.25 23.7695 10.9601 24.2595 10.4301C24.7395 9.90005 24.9595 9.19005 24.8895 8.43005L24.6095 5.68005C24.2995 3.42005 23.5695 2.75 21.3795 2.75H19.1595Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9.67028 12.75C8.48028 12.75 7.4003 12.2801 6.6303 11.4301C5.8603 10.5801 5.50029 9.46003 5.62029 8.28003L5.90032 5.53003C6.32032 2.48003 7.6903 1.25 10.6303 1.25H13.6803C13.8903 1.25 14.0903 1.34 14.2403 1.5C14.3803 1.66 14.4503 1.86996 14.4303 2.07996L13.7303 9.08997C13.5103 11.11 11.6903 12.75 9.67028 12.75ZM10.6203 2.75C8.43029 2.75 7.7003 3.40995 7.3803 5.69995L7.11028 8.42004C7.03028 9.18004 7.26029 9.89004 7.74029 10.42C8.22029 10.95 8.91028 11.24 9.67028 11.24C10.9303 11.24 12.1003 10.1801 12.2203 8.93005L12.8403 2.73999H10.6203V2.75Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9 23.75C7.6 23.75 6.29001 23.1399 5.39001 22.0699C5.38001 22.0599 5.34 22.02 5.31 21.97C5.16 21.79 5.02999 21.61 4.92999 21.42C4.48999 20.71 4.25 19.87 4.25 18.99C4.25 17.53 4.90003 16.18 6.03003 15.28C6.23003 15.12 6.45 14.97 6.69 14.84C7.38 14.45 8.19 14.23 9 14.23C10.16 14.23 11.23 14.63 12.09 15.38C12.2 15.46 12.35 15.61 12.48 15.75C13.29 16.64 13.74 17.78 13.74 18.97C13.74 19.84 13.5 20.69 13.05 21.42C12.83 21.8 12.56 22.13 12.26 22.39C12.2 22.45 12.14 22.5101 12.07 22.5601C11.28 23.3301 10.17 23.75 9 23.75ZM6.47998 21.04C6.50998 21.07 6.55998 21.1199 6.59998 21.1899C7.14998 21.8399 8.05 22.26 9 22.26C9.79 22.26 10.55 21.97 11.13 21.45C11.16 21.41 11.2 21.37 11.25 21.34C11.45 21.17 11.62 20.9499 11.78 20.6899C12.09 20.1799 12.26 19.61 12.26 19.01C12.26 18.2 11.95 17.41 11.39 16.8C11.32 16.72 11.25 16.64 11.16 16.58C10.08 15.65 8.59998 15.5101 7.41998 16.1801C7.25998 16.2701 7.11999 16.36 6.98999 16.47C6.20999 17.09 5.76001 18.01 5.76001 19.01C5.76001 19.6 5.91998 20.1801 6.22998 20.6801C6.30998 20.8201 6.38998 20.93 6.47998 21.03C6.46998 21.03 6.47998 21.03 6.47998 21.04Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M10.4998 19.73H7.50977C7.09977 19.73 6.75977 19.39 6.75977 18.98C6.75977 18.57 7.09977 18.23 7.50977 18.23H10.4998C10.9098 18.23 11.2498 18.57 11.2498 18.98C11.2498 19.39 10.9098 19.73 10.4998 19.73Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9 21.26C8.59 21.26 8.25 20.92 8.25 20.51V17.52C8.25 17.11 8.59 16.77 9 16.77C9.41 16.77 9.75 17.11 9.75 17.52V20.51C9.75 20.93 9.41 21.26 9 21.26Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M40.0085 7.31818L42.8423 15.6108H42.9567L45.7905 7.31818H47.451L43.7869 17.5H42.0121L38.348 7.31818H40.0085ZM51.3276 17.6541C50.5752 17.6541 49.9273 17.4934 49.3837 17.1719C48.8435 16.8471 48.4258 16.3913 48.1309 15.8047C47.8392 15.2147 47.6934 14.5237 47.6934 13.7315C47.6934 12.9493 47.8392 12.2599 48.1309 11.6634C48.4258 11.0668 48.8368 10.6011 49.3638 10.2663C49.8941 9.93158 50.5139 9.7642 51.2232 9.7642C51.6541 9.7642 52.0717 9.83546 52.476 9.97798C52.8804 10.1205 53.2433 10.3442 53.5648 10.6491C53.8863 10.9541 54.1399 11.3501 54.3255 11.8374C54.5111 12.3213 54.6039 12.9096 54.6039 13.6023V14.1293H48.5336V13.0156H53.1472C53.1472 12.6245 53.0676 12.2782 52.9086 11.9766C52.7495 11.6716 52.5257 11.4313 52.2374 11.2557C51.9524 11.08 51.6176 10.9922 51.2331 10.9922C50.8155 10.9922 50.4509 11.0949 50.1394 11.3004C49.8311 11.5026 49.5925 11.7678 49.4235 12.0959C49.2578 12.4207 49.1749 12.7737 49.1749 13.1548V14.0249C49.1749 14.5353 49.2644 14.9695 49.4434 15.3274C49.6257 15.6854 49.8792 15.9588 50.204 16.1477C50.5288 16.3333 50.9083 16.4261 51.3425 16.4261C51.6242 16.4261 51.8811 16.3864 52.1131 16.3068C52.3451 16.224 52.5456 16.1013 52.7147 15.9389C52.8837 15.7765 53.013 15.576 53.1025 15.3374L54.5094 15.5909C54.3967 16.0052 54.1945 16.3681 53.9029 16.6797C53.6145 16.9879 53.2516 17.2282 52.8141 17.4006C52.3799 17.5696 51.8844 17.6541 51.3276 17.6541ZM57.7397 12.9659V17.5H56.2532V9.86364H57.68V11.1065H57.7745C57.9502 10.7022 58.2253 10.3774 58.5998 10.1321C58.9776 9.88684 59.4532 9.7642 60.0266 9.7642C60.547 9.7642 61.0027 9.87358 61.3938 10.0923C61.7849 10.3078 62.0882 10.6293 62.3036 11.0568C62.5191 11.4844 62.6268 12.013 62.6268 12.6428V17.5H61.1403V12.8217C61.1403 12.2682 60.9961 11.8357 60.7077 11.5241C60.4194 11.2093 60.0233 11.0518 59.5195 11.0518C59.1748 11.0518 58.8683 11.1264 58.5998 11.2756C58.3346 11.4247 58.1242 11.6435 57.9684 11.9318C57.8159 12.2169 57.7397 12.5616 57.7397 12.9659ZM67.479 17.6491C66.8626 17.6491 66.3124 17.4917 65.8285 17.1768C65.3479 16.8587 64.9701 16.4062 64.695 15.8196C64.4232 15.2296 64.2873 14.522 64.2873 13.6967C64.2873 12.8714 64.4248 12.1655 64.6999 11.5788C64.9783 10.9922 65.3595 10.5431 65.8434 10.2315C66.3273 9.91998 66.8758 9.7642 67.489 9.7642C67.9629 9.7642 68.3441 9.84375 68.6325 10.0028C68.9241 10.1586 69.1495 10.3409 69.3086 10.5497C69.471 10.7585 69.5969 10.9425 69.6864 11.1016H69.7759V7.31818H71.2624V17.5H69.8107V16.3118H69.6864C69.5969 16.4742 69.4677 16.6598 69.2987 16.8686C69.1329 17.0774 68.9042 17.2597 68.6126 17.4155C68.3209 17.5713 67.9431 17.6491 67.479 17.6491ZM67.8072 16.3814C68.2347 16.3814 68.596 16.2687 68.891 16.0433C69.1893 15.8146 69.4147 15.4981 69.5671 15.0938C69.7229 14.6894 69.8008 14.2187 69.8008 13.6818C69.8008 13.1515 69.7246 12.6875 69.5721 12.2898C69.4196 11.892 69.1959 11.5821 68.9009 11.3601C68.6059 11.138 68.2414 11.027 67.8072 11.027C67.3597 11.027 66.9869 11.143 66.6886 11.375C66.3903 11.607 66.1649 11.9235 66.0124 12.3246C65.8633 12.7256 65.7887 13.178 65.7887 13.6818C65.7887 14.1922 65.8649 14.6513 66.0174 15.0589C66.1699 15.4666 66.3952 15.7898 66.6935 16.0284C66.9951 16.2637 67.3664 16.3814 67.8072 16.3814ZM76.6007 17.6541C75.8848 17.6541 75.26 17.4901 74.7264 17.1619C74.1928 16.8338 73.7785 16.3748 73.4835 15.7848C73.1885 15.1948 73.041 14.5054 73.041 13.7166C73.041 12.9245 73.1885 12.2318 73.4835 11.6385C73.7785 11.0452 74.1928 10.5845 74.7264 10.2564C75.26 9.92827 75.8848 9.7642 76.6007 9.7642C77.3166 9.7642 77.9413 9.92827 78.475 10.2564C79.0086 10.5845 79.4229 11.0452 79.7179 11.6385C80.0128 12.2318 80.1603 12.9245 80.1603 13.7166C80.1603 14.5054 80.0128 15.1948 79.7179 15.7848C79.4229 16.3748 79.0086 16.8338 78.475 17.1619C77.9413 17.4901 77.3166 17.6541 76.6007 17.6541ZM76.6056 16.4062C77.0697 16.4062 77.4541 16.2836 77.7591 16.0384C78.064 15.7931 78.2894 15.4666 78.4352 15.0589C78.5843 14.6513 78.6589 14.2022 78.6589 13.7116C78.6589 13.2244 78.5843 12.777 78.4352 12.3693C78.2894 11.9583 78.064 11.6286 77.7591 11.38C77.4541 11.1314 77.0697 11.0071 76.6056 11.0071C76.1383 11.0071 75.7505 11.1314 75.4423 11.38C75.1374 11.6286 74.9103 11.9583 74.7612 12.3693C74.6154 12.777 74.5424 13.2244 74.5424 13.7116C74.5424 14.2022 74.6154 14.6513 74.7612 15.0589C74.9103 15.4666 75.1374 15.7931 75.4423 16.0384C75.7505 16.2836 76.1383 16.4062 76.6056 16.4062ZM81.8196 17.5V9.86364H83.2564V11.0767H83.3359C83.4751 10.6657 83.7204 10.3426 84.0717 10.1072C84.4264 9.86861 84.8274 9.74929 85.2749 9.74929C85.3677 9.74929 85.477 9.7526 85.603 9.75923C85.7322 9.76586 85.8333 9.77415 85.9062 9.78409V11.206C85.8466 11.1894 85.7405 11.1712 85.5881 11.1513C85.4356 11.1281 85.2831 11.1165 85.1307 11.1165C84.7794 11.1165 84.4661 11.1911 84.1911 11.3402C83.9193 11.486 83.7038 11.6899 83.5447 11.9517C83.3857 12.2102 83.3061 12.5052 83.3061 12.8366V17.5H81.8196Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      hasOwnBackground: true, // Adjust based on your SVG\r\n    },\r\n    {\r\n      id: \"flashes\",\r\n      label: \"flashes\",\r\n      defaultSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"url(#paint0_linear_159_59445)\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M20.458 31.8333C14.1832 31.8333 9.08301 26.733 9.08301 20.4583C9.08301 14.1835 14.1832 9.08325 20.458 9.08325C26.7328 9.08325 31.833 14.1835 31.833 20.4583C31.833 21.9396 31.5579 23.3576 31.0077 24.6908C30.9124 24.913 30.7325 25.0823 30.4997 25.1458C30.267 25.2199 30.0236 25.1776 29.8225 25.04C28.5739 24.1829 26.8915 24.2464 25.7064 25.1776C24.8916 25.823 24.4154 26.8071 24.4154 27.8546C24.4154 28.4789 24.5847 29.0926 24.9128 29.6217C24.9445 29.6746 24.9657 29.7064 24.9974 29.7382C25.1667 29.9286 25.2302 30.1931 25.1773 30.4471C25.1244 30.701 24.9445 30.9021 24.7012 30.9973C23.3573 31.5582 21.9288 31.8333 20.458 31.8333ZM20.458 10.6705C15.0615 10.6705 10.6702 15.0617 10.6702 20.4583C10.6702 25.8548 15.0615 30.246 20.458 30.246C21.4103 30.246 22.3415 30.1085 23.2409 29.8334C22.9764 29.2196 22.8388 28.553 22.8388 27.8652C22.8388 26.3203 23.5266 24.8918 24.7223 23.9395C26.1614 22.7967 28.1824 22.5322 29.8331 23.2412C30.0976 22.3523 30.2352 21.4106 30.2352 20.4477C30.2458 15.0617 25.8545 10.6705 20.458 10.6705Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M19.7861 25.4092C19.3636 25.4092 18.9628 25.3117 18.6161 25.1059C17.8144 24.64 17.3486 23.6976 17.3486 22.5059V19.4942C17.3486 18.3025 17.8144 17.3601 18.6161 16.8942C19.4286 16.4284 20.4795 16.5043 21.5086 17.1001L24.1086 18.6059C25.1378 19.2017 25.7228 20.0792 25.7228 21.0001C25.7228 21.9209 25.1378 22.8092 24.1086 23.3942L21.5086 24.9C20.9236 25.2359 20.3386 25.4092 19.7861 25.4092ZM19.797 18.2159C19.6561 18.2159 19.5369 18.2484 19.4286 18.3026C19.1361 18.4759 18.9736 18.9092 18.9736 19.4942V22.5059C18.9736 23.0909 19.1361 23.535 19.4286 23.6975C19.7211 23.86 20.1761 23.795 20.6961 23.4917L23.2961 21.9859C23.8053 21.6934 24.0978 21.325 24.0978 20.9892C24.0978 20.6534 23.8053 20.2959 23.2961 19.9926L20.6961 18.4867C20.3603 18.3134 20.057 18.2159 19.797 18.2159Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M29.3278 32.6532C28.3853 32.6532 26.1753 31.4941 25.4928 29.3816C25.027 27.9191 25.5687 26.0016 27.2587 25.4599C27.9737 25.2324 28.7212 25.3299 29.317 25.6982C29.902 25.3299 30.6712 25.2216 31.397 25.4599C33.087 26.0016 33.6287 27.9191 33.1628 29.3816C32.4803 31.5374 30.162 32.6532 29.3278 32.6532ZM27.042 28.8832C27.5078 30.3241 29.0678 30.9957 29.3495 31.0282C29.6637 30.9957 31.1912 30.2374 31.6137 28.8832C31.8412 28.1574 31.6137 27.2366 30.8987 26.9982C30.5953 26.9007 30.1945 26.9549 30.0103 27.2366C29.8587 27.4641 29.6095 27.6049 29.3495 27.6049C29.1003 27.6049 28.8295 27.4857 28.6778 27.2691C28.4503 26.9549 28.0495 26.9116 27.7678 26.9982C27.042 27.2366 26.8145 28.1574 27.042 28.8832Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_159_59445\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      activeSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"#B31B1E\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M20.458 31.8333C14.1832 31.8333 9.08301 26.733 9.08301 20.4583C9.08301 14.1835 14.1832 9.08325 20.458 9.08325C26.7328 9.08325 31.833 14.1835 31.833 20.4583C31.833 21.9396 31.5579 23.3576 31.0077 24.6908C30.9124 24.913 30.7325 25.0823 30.4997 25.1458C30.267 25.2199 30.0236 25.1776 29.8225 25.04C28.5739 24.1829 26.8915 24.2464 25.7064 25.1776C24.8916 25.823 24.4154 26.8071 24.4154 27.8546C24.4154 28.4789 24.5847 29.0926 24.9128 29.6217C24.9445 29.6746 24.9657 29.7064 24.9974 29.7382C25.1667 29.9286 25.2302 30.1931 25.1773 30.4471C25.1244 30.701 24.9445 30.9021 24.7012 30.9973C23.3573 31.5582 21.9288 31.8333 20.458 31.8333ZM20.458 10.6705C15.0615 10.6705 10.6702 15.0617 10.6702 20.4583C10.6702 25.8548 15.0615 30.246 20.458 30.246C21.4103 30.246 22.3415 30.1085 23.2409 29.8334C22.9764 29.2196 22.8388 28.553 22.8388 27.8652C22.8388 26.3203 23.5266 24.8918 24.7223 23.9395C26.1614 22.7967 28.1824 22.5322 29.8331 23.2412C30.0976 22.3523 30.2352 21.4106 30.2352 20.4477C30.2458 15.0617 25.8545 10.6705 20.458 10.6705Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M19.7861 25.4092C19.3636 25.4092 18.9628 25.3117 18.6161 25.1059C17.8144 24.64 17.3486 23.6976 17.3486 22.5059V19.4942C17.3486 18.3025 17.8144 17.3601 18.6161 16.8942C19.4286 16.4284 20.4795 16.5043 21.5086 17.1001L24.1086 18.6059C25.1378 19.2017 25.7228 20.0792 25.7228 21.0001C25.7228 21.9209 25.1378 22.8092 24.1086 23.3942L21.5086 24.9C20.9236 25.2359 20.3386 25.4092 19.7861 25.4092ZM19.797 18.2159C19.6561 18.2159 19.5369 18.2484 19.4286 18.3026C19.1361 18.4759 18.9736 18.9092 18.9736 19.4942V22.5059C18.9736 23.0909 19.1361 23.535 19.4286 23.6975C19.7211 23.86 20.1761 23.795 20.6961 23.4917L23.2961 21.9859C23.8053 21.6934 24.0978 21.325 24.0978 20.9892C24.0978 20.6534 23.8053 20.2959 23.2961 19.9926L20.6961 18.4867C20.3603 18.3134 20.057 18.2159 19.797 18.2159Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M29.3278 32.6532C28.3853 32.6532 26.1753 31.4941 25.4928 29.3816C25.027 27.9191 25.5687 26.0016 27.2587 25.4599C27.9737 25.2324 28.7212 25.3299 29.317 25.6982C29.902 25.3299 30.6712 25.2216 31.397 25.4599C33.087 26.0016 33.6287 27.9191 33.1628 29.3816C32.4803 31.5374 30.162 32.6532 29.3278 32.6532ZM27.042 28.8832C27.5078 30.3241 29.0678 30.9957 29.3495 31.0282C29.6637 30.9957 31.1912 30.2374 31.6137 28.8832C31.8412 28.1574 31.6137 27.2366 30.8987 26.9982C30.5953 26.9007 30.1945 26.9549 30.0103 27.2366C29.8587 27.4641 29.6095 27.6049 29.3495 27.6049C29.1003 27.6049 28.8295 27.4857 28.6778 27.2691C28.4503 26.9549 28.0495 26.9116 27.7678 26.9982C27.042 27.2366 26.8145 28.1574 27.042 28.8832Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_159_59445\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      expandedDefaultSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M15.5 22C9.70791 22 5 17.2921 5 11.5C5 5.70791 9.70791 1 15.5 1C21.2921 1 26 5.70791 26 11.5C26 12.8674 25.746 14.1763 25.2381 15.407C25.1502 15.6121 24.9842 15.7684 24.7693 15.827C24.5544 15.8953 24.3298 15.8563 24.1442 15.7293C22.9916 14.9382 21.4386 14.9968 20.3446 15.8563C19.5925 16.4521 19.153 17.3605 19.153 18.3274C19.153 18.9037 19.3093 19.4702 19.6121 19.9586C19.6414 20.0074 19.6609 20.0368 19.6902 20.0661C19.8465 20.2419 19.9051 20.486 19.8563 20.7205C19.8074 20.9549 19.6414 21.1405 19.4168 21.2284C18.1763 21.7461 16.8577 22 15.5 22ZM15.5 2.46512C10.5186 2.46512 6.46512 6.5186 6.46512 11.5C6.46512 16.4814 10.5186 20.5349 15.5 20.5349C16.3791 20.5349 17.2386 20.4079 18.0688 20.1539C17.8247 19.5874 17.6977 18.9721 17.6977 18.3372C17.6977 16.9112 18.3326 15.5925 19.4363 14.7135C20.7646 13.6586 22.6302 13.4144 24.1539 14.0688C24.3981 13.2484 24.5251 12.3791 24.5251 11.4902C24.5349 6.51859 20.4814 2.46512 15.5 2.46512Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.8799 16.0701C14.4899 16.0701 14.1199 15.9801 13.7999 15.7901C13.0599 15.3601 12.6299 14.4901 12.6299 13.3901V10.6101C12.6299 9.51008 13.0599 8.64011 13.7999 8.21011C14.5499 7.78011 15.5199 7.85011 16.4699 8.40011L18.8699 9.79007C19.8199 10.3401 20.3599 11.1501 20.3599 12.0001C20.3599 12.8501 19.8199 13.6701 18.8699 14.2101L16.4699 15.6001C15.9299 15.9101 15.3899 16.0701 14.8799 16.0701ZM14.8899 9.43008C14.7599 9.43008 14.6499 9.4601 14.5499 9.5101C14.2799 9.6701 14.1299 10.0701 14.1299 10.6101V13.3901C14.1299 13.9301 14.2799 14.3401 14.5499 14.4901C14.8199 14.6401 15.2399 14.5801 15.7199 14.3001L18.1199 12.9101C18.5899 12.6401 18.8599 12.3001 18.8599 11.9901C18.8599 11.6801 18.5899 11.3501 18.1199 11.0701L15.7199 9.68008C15.4099 9.52008 15.1299 9.43008 14.8899 9.43008Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M23.6875 22.7569C22.8175 22.7569 20.7775 21.6869 20.1475 19.7369C19.7175 18.3869 20.2175 16.6169 21.7775 16.1169C22.4375 15.9069 23.1275 15.9969 23.6775 16.3369C24.2175 15.9969 24.9275 15.8969 25.5975 16.1169C27.1575 16.6169 27.6575 18.3869 27.2275 19.7369C26.5975 21.7269 24.4575 22.7569 23.6875 22.7569ZM21.5775 19.2769C22.0075 20.6069 23.4475 21.2269 23.7075 21.2569C23.9975 21.2269 25.4075 20.5269 25.7975 19.2769C26.0075 18.6069 25.7975 17.7569 25.1375 17.5369C24.8575 17.4469 24.4875 17.4969 24.3175 17.7569C24.1775 17.9669 23.9475 18.0969 23.7075 18.0969C23.4775 18.0969 23.2275 17.9869 23.0875 17.7869C22.8775 17.4969 22.5075 17.4569 22.2475 17.5369C21.5775 17.7569 21.3675 18.6069 21.5775 19.2769Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M39.1186 17.5V7.31818H45.4325V8.64062H40.6548V11.7429H44.9801V13.0604H40.6548V17.5H39.1186ZM48.6889 7.31818V17.5H47.2024V7.31818H48.6889ZM52.911 17.669C52.4271 17.669 51.9896 17.5795 51.5985 17.4006C51.2074 17.2183 50.8975 16.9548 50.6689 16.6101C50.4435 16.2654 50.3308 15.8428 50.3308 15.3423C50.3308 14.9115 50.4136 14.5568 50.5794 14.2784C50.7451 14 50.9688 13.7796 51.2505 13.6172C51.5323 13.4548 51.8471 13.3321 52.1951 13.2493C52.5431 13.1664 52.8978 13.1035 53.2591 13.0604C53.7164 13.0073 54.0877 12.9643 54.3727 12.9311C54.6577 12.8946 54.8649 12.8366 54.9941 12.7571C55.1234 12.6776 55.188 12.5483 55.188 12.3693V12.3345C55.188 11.9003 55.0654 11.5639 54.8201 11.3253C54.5782 11.0866 54.2169 10.9673 53.7363 10.9673C53.2359 10.9673 52.8414 11.0784 52.5531 11.3004C52.2681 11.5192 52.0708 11.7628 51.9615 12.0312L50.5645 11.7131C50.7302 11.2491 50.9721 10.8745 51.2903 10.5895C51.6118 10.3011 51.9814 10.0923 52.399 9.96307C52.8166 9.83049 53.2557 9.7642 53.7164 9.7642C54.0214 9.7642 54.3445 9.80066 54.6859 9.87358C55.0306 9.94318 55.3521 10.0724 55.6504 10.2614C55.952 10.4503 56.1989 10.7204 56.3912 11.0717C56.5834 11.4197 56.6795 11.8722 56.6795 12.429V17.5H55.2278V16.456H55.1681C55.072 16.6482 54.9279 16.8371 54.7356 17.0227C54.5434 17.2083 54.2965 17.3625 53.9949 17.4851C53.6932 17.6077 53.332 17.669 52.911 17.669ZM53.2342 16.4759C53.6452 16.4759 53.9965 16.3946 54.2882 16.2322C54.5832 16.0698 54.8069 15.8577 54.9593 15.5959C55.1151 15.3307 55.193 15.0473 55.193 14.7457V13.7614C55.14 13.8144 55.0372 13.8641 54.8848 13.9105C54.7356 13.9536 54.5649 13.9917 54.3727 14.0249C54.1805 14.0547 53.9932 14.0829 53.8109 14.1094C53.6286 14.1326 53.4761 14.1525 53.3535 14.169C53.0652 14.2055 52.8017 14.2668 52.563 14.353C52.3277 14.4392 52.1388 14.5634 51.9963 14.7259C51.8571 14.8849 51.7875 15.0971 51.7875 15.3622C51.7875 15.7301 51.9234 16.0085 52.1951 16.1974C52.4669 16.383 52.8133 16.4759 53.2342 16.4759ZM64.3817 11.728L63.0344 11.9666C62.9781 11.7943 62.8886 11.6302 62.766 11.4744C62.6467 11.3187 62.4843 11.1911 62.2788 11.0916C62.0733 10.9922 61.8164 10.9425 61.5082 10.9425C61.0872 10.9425 60.7359 11.0369 60.4542 11.2259C60.1725 11.4115 60.0316 11.6518 60.0316 11.9467C60.0316 12.2019 60.1261 12.4074 60.315 12.5632C60.5039 12.719 60.8088 12.8466 61.2298 12.946L62.4428 13.2244C63.1455 13.3868 63.6692 13.6371 64.0138 13.9751C64.3585 14.3132 64.5309 14.7524 64.5309 15.2926C64.5309 15.75 64.3983 16.1577 64.1332 16.5156C63.8713 16.8703 63.5051 17.1487 63.0344 17.3509C62.5671 17.553 62.0252 17.6541 61.4087 17.6541C60.5536 17.6541 59.8559 17.4718 59.3157 17.1072C58.7754 16.7393 58.444 16.2173 58.3214 15.5412L59.7582 15.3224C59.8477 15.697 60.0316 15.9804 60.31 16.1726C60.5884 16.3615 60.9513 16.456 61.3988 16.456C61.886 16.456 62.2754 16.3549 62.5671 16.1527C62.8588 15.9472 63.0046 15.697 63.0046 15.402C63.0046 15.1634 62.9151 14.9628 62.7362 14.8004C62.5605 14.638 62.2904 14.5154 61.9258 14.4325L60.6332 14.1491C59.9206 13.9867 59.3936 13.7282 59.0522 13.3736C58.7141 13.0189 58.5451 12.5698 58.5451 12.0263C58.5451 11.5755 58.671 11.1811 58.9229 10.843C59.1748 10.505 59.5228 10.2415 59.967 10.0526C60.4111 9.86032 60.9199 9.7642 61.4933 9.7642C62.3185 9.7642 62.9682 9.94318 63.4421 10.3011C63.9161 10.6558 64.2293 11.1314 64.3817 11.728ZM67.6518 12.9659V17.5H66.1653V7.31818H67.6319V11.1065H67.7264C67.9054 10.6955 68.1788 10.3691 68.5467 10.1271C68.9146 9.88518 69.3952 9.7642 69.9885 9.7642C70.5121 9.7642 70.9695 9.87192 71.3606 10.0874C71.755 10.3028 72.06 10.6243 72.2754 11.0518C72.4941 11.4761 72.6035 12.0064 72.6035 12.6428V17.5H71.117V12.8217C71.117 12.2616 70.9728 11.8274 70.6845 11.5192C70.3961 11.2076 69.9951 11.0518 69.4814 11.0518C69.13 11.0518 68.8152 11.1264 68.5368 11.2756C68.2617 11.4247 68.0446 11.6435 67.8855 11.9318C67.7297 12.2169 67.6518 12.5616 67.6518 12.9659ZM77.8784 17.6541C77.126 17.6541 76.478 17.4934 75.9345 17.1719C75.3942 16.8471 74.9766 16.3913 74.6816 15.8047C74.39 15.2147 74.2441 14.5237 74.2441 13.7315C74.2441 12.9493 74.39 12.2599 74.6816 11.6634C74.9766 11.0668 75.3876 10.6011 75.9146 10.2663C76.4449 9.93158 77.0647 9.7642 77.774 9.7642C78.2048 9.7642 78.6225 9.83546 79.0268 9.97798C79.4312 10.1205 79.7941 10.3442 80.1156 10.6491C80.4371 10.9541 80.6906 11.3501 80.8762 11.8374C81.0618 12.3213 81.1547 12.9096 81.1547 13.6023V14.1293H75.0843V13.0156H79.698C79.698 12.6245 79.6184 12.2782 79.4593 11.9766C79.3002 11.6716 79.0765 11.4313 78.7882 11.2557C78.5031 11.08 78.1684 10.9922 77.7839 10.9922C77.3663 10.9922 77.0017 11.0949 76.6902 11.3004C76.3819 11.5026 76.1433 11.7678 75.9743 12.0959C75.8085 12.4207 75.7257 12.7737 75.7257 13.1548V14.0249C75.7257 14.5353 75.8152 14.9695 75.9941 15.3274C76.1764 15.6854 76.43 15.9588 76.7548 16.1477C77.0796 16.3333 77.4591 16.4261 77.8933 16.4261C78.175 16.4261 78.4319 16.3864 78.6639 16.3068C78.8959 16.224 79.0964 16.1013 79.2654 15.9389C79.4345 15.7765 79.5637 15.576 79.6532 15.3374L81.0602 15.5909C80.9475 16.0052 80.7453 16.3681 80.4537 16.6797C80.1653 16.9879 79.8024 17.2282 79.3649 17.4006C78.9307 17.5696 78.4352 17.6541 77.8784 17.6541ZM88.5263 11.728L87.179 11.9666C87.1226 11.7943 87.0331 11.6302 86.9105 11.4744C86.7912 11.3187 86.6288 11.1911 86.4233 11.0916C86.2178 10.9922 85.9609 10.9425 85.6527 10.9425C85.2318 10.9425 84.8804 11.0369 84.5987 11.2259C84.317 11.4115 84.1761 11.6518 84.1761 11.9467C84.1761 12.2019 84.2706 12.4074 84.4595 12.5632C84.6484 12.719 84.9534 12.8466 85.3743 12.946L86.5874 13.2244C87.29 13.3868 87.8137 13.6371 88.1584 13.9751C88.5031 14.3132 88.6754 14.7524 88.6754 15.2926C88.6754 15.75 88.5429 16.1577 88.2777 16.5156C88.0159 16.8703 87.6496 17.1487 87.179 17.3509C86.7116 17.553 86.1697 17.6541 85.5533 17.6541C84.6982 17.6541 84.0005 17.4718 83.4602 17.1072C82.92 16.7393 82.5885 16.2173 82.4659 15.5412L83.9027 15.3224C83.9922 15.697 84.1761 15.9804 84.4545 16.1726C84.733 16.3615 85.0959 16.456 85.5433 16.456C86.0305 16.456 86.42 16.3549 86.7116 16.1527C87.0033 15.9472 87.1491 15.697 87.1491 15.402C87.1491 15.1634 87.0597 14.9628 86.8807 14.8004C86.705 14.638 86.4349 14.5154 86.0703 14.4325L84.7777 14.1491C84.0651 13.9867 83.5381 13.7282 83.1967 13.3736C82.8587 13.0189 82.6896 12.5698 82.6896 12.0263C82.6896 11.5755 82.8156 11.1811 83.0675 10.843C83.3194 10.505 83.6674 10.2415 84.1115 10.0526C84.5556 9.86032 85.0644 9.7642 85.6378 9.7642C86.4631 9.7642 87.1127 9.94318 87.5866 10.3011C88.0606 10.6558 88.3738 11.1314 88.5263 11.728Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedActiveSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M15.5 22C9.70791 22 5 17.2921 5 11.5C5 5.70791 9.70791 1 15.5 1C21.2921 1 26 5.70791 26 11.5C26 12.8674 25.746 14.1763 25.2381 15.407C25.1502 15.6121 24.9842 15.7684 24.7693 15.827C24.5544 15.8953 24.3298 15.8563 24.1442 15.7293C22.9916 14.9382 21.4386 14.9968 20.3446 15.8563C19.5925 16.4521 19.153 17.3605 19.153 18.3274C19.153 18.9037 19.3093 19.4702 19.6121 19.9586C19.6414 20.0074 19.6609 20.0368 19.6902 20.0661C19.8465 20.2419 19.9051 20.486 19.8563 20.7205C19.8074 20.9549 19.6414 21.1405 19.4168 21.2284C18.1763 21.7461 16.8577 22 15.5 22ZM15.5 2.46512C10.5186 2.46512 6.46512 6.5186 6.46512 11.5C6.46512 16.4814 10.5186 20.5349 15.5 20.5349C16.3791 20.5349 17.2386 20.4079 18.0688 20.1539C17.8247 19.5874 17.6977 18.9721 17.6977 18.3372C17.6977 16.9112 18.3326 15.5925 19.4363 14.7135C20.7646 13.6586 22.6302 13.4144 24.1539 14.0688C24.3981 13.2484 24.5251 12.3791 24.5251 11.4902C24.5349 6.51859 20.4814 2.46512 15.5 2.46512Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.8799 16.0701C14.4899 16.0701 14.1199 15.9801 13.7999 15.7901C13.0599 15.3601 12.6299 14.4901 12.6299 13.3901V10.6101C12.6299 9.51008 13.0599 8.64011 13.7999 8.21011C14.5499 7.78011 15.5199 7.85011 16.4699 8.40011L18.8699 9.79007C19.8199 10.3401 20.3599 11.1501 20.3599 12.0001C20.3599 12.8501 19.8199 13.6701 18.8699 14.2101L16.4699 15.6001C15.9299 15.9101 15.3899 16.0701 14.8799 16.0701ZM14.8899 9.43008C14.7599 9.43008 14.6499 9.4601 14.5499 9.5101C14.2799 9.6701 14.1299 10.0701 14.1299 10.6101V13.3901C14.1299 13.9301 14.2799 14.3401 14.5499 14.4901C14.8199 14.6401 15.2399 14.5801 15.7199 14.3001L18.1199 12.9101C18.5899 12.6401 18.8599 12.3001 18.8599 11.9901C18.8599 11.6801 18.5899 11.3501 18.1199 11.0701L15.7199 9.68008C15.4099 9.52008 15.1299 9.43008 14.8899 9.43008Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M23.6875 22.7569C22.8175 22.7569 20.7775 21.6869 20.1475 19.7369C19.7175 18.3869 20.2175 16.6169 21.7775 16.1169C22.4375 15.9069 23.1275 15.9969 23.6775 16.3369C24.2175 15.9969 24.9275 15.8969 25.5975 16.1169C27.1575 16.6169 27.6575 18.3869 27.2275 19.7369C26.5975 21.7269 24.4575 22.7569 23.6875 22.7569ZM21.5775 19.2769C22.0075 20.6069 23.4475 21.2269 23.7075 21.2569C23.9975 21.2269 25.4075 20.5269 25.7975 19.2769C26.0075 18.6069 25.7975 17.7569 25.1375 17.5369C24.8575 17.4469 24.4875 17.4969 24.3175 17.7569C24.1775 17.9669 23.9475 18.0969 23.7075 18.0969C23.4775 18.0969 23.2275 17.9869 23.0875 17.7869C22.8775 17.4969 22.5075 17.4569 22.2475 17.5369C21.5775 17.7569 21.3675 18.6069 21.5775 19.2769Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M39.1186 17.5V7.31818H45.4325V8.64062H40.6548V11.7429H44.9801V13.0604H40.6548V17.5H39.1186ZM48.6889 7.31818V17.5H47.2024V7.31818H48.6889ZM52.911 17.669C52.4271 17.669 51.9896 17.5795 51.5985 17.4006C51.2074 17.2183 50.8975 16.9548 50.6689 16.6101C50.4435 16.2654 50.3308 15.8428 50.3308 15.3423C50.3308 14.9115 50.4136 14.5568 50.5794 14.2784C50.7451 14 50.9688 13.7796 51.2505 13.6172C51.5323 13.4548 51.8471 13.3321 52.1951 13.2493C52.5431 13.1664 52.8978 13.1035 53.2591 13.0604C53.7164 13.0073 54.0877 12.9643 54.3727 12.9311C54.6577 12.8946 54.8649 12.8366 54.9941 12.7571C55.1234 12.6776 55.188 12.5483 55.188 12.3693V12.3345C55.188 11.9003 55.0654 11.5639 54.8201 11.3253C54.5782 11.0866 54.2169 10.9673 53.7363 10.9673C53.2359 10.9673 52.8414 11.0784 52.5531 11.3004C52.2681 11.5192 52.0708 11.7628 51.9615 12.0312L50.5645 11.7131C50.7302 11.2491 50.9721 10.8745 51.2903 10.5895C51.6118 10.3011 51.9814 10.0923 52.399 9.96307C52.8166 9.83049 53.2557 9.7642 53.7164 9.7642C54.0214 9.7642 54.3445 9.80066 54.6859 9.87358C55.0306 9.94318 55.3521 10.0724 55.6504 10.2614C55.952 10.4503 56.1989 10.7204 56.3912 11.0717C56.5834 11.4197 56.6795 11.8722 56.6795 12.429V17.5H55.2278V16.456H55.1681C55.072 16.6482 54.9279 16.8371 54.7356 17.0227C54.5434 17.2083 54.2965 17.3625 53.9949 17.4851C53.6932 17.6077 53.332 17.669 52.911 17.669ZM53.2342 16.4759C53.6452 16.4759 53.9965 16.3946 54.2882 16.2322C54.5832 16.0698 54.8069 15.8577 54.9593 15.5959C55.1151 15.3307 55.193 15.0473 55.193 14.7457V13.7614C55.14 13.8144 55.0372 13.8641 54.8848 13.9105C54.7356 13.9536 54.5649 13.9917 54.3727 14.0249C54.1805 14.0547 53.9932 14.0829 53.8109 14.1094C53.6286 14.1326 53.4761 14.1525 53.3535 14.169C53.0652 14.2055 52.8017 14.2668 52.563 14.353C52.3277 14.4392 52.1388 14.5634 51.9963 14.7259C51.8571 14.8849 51.7875 15.0971 51.7875 15.3622C51.7875 15.7301 51.9234 16.0085 52.1951 16.1974C52.4669 16.383 52.8133 16.4759 53.2342 16.4759ZM64.3817 11.728L63.0344 11.9666C62.9781 11.7943 62.8886 11.6302 62.766 11.4744C62.6467 11.3187 62.4843 11.1911 62.2788 11.0916C62.0733 10.9922 61.8164 10.9425 61.5082 10.9425C61.0872 10.9425 60.7359 11.0369 60.4542 11.2259C60.1725 11.4115 60.0316 11.6518 60.0316 11.9467C60.0316 12.2019 60.1261 12.4074 60.315 12.5632C60.5039 12.719 60.8088 12.8466 61.2298 12.946L62.4428 13.2244C63.1455 13.3868 63.6692 13.6371 64.0138 13.9751C64.3585 14.3132 64.5309 14.7524 64.5309 15.2926C64.5309 15.75 64.3983 16.1577 64.1332 16.5156C63.8713 16.8703 63.5051 17.1487 63.0344 17.3509C62.5671 17.553 62.0252 17.6541 61.4087 17.6541C60.5536 17.6541 59.8559 17.4718 59.3157 17.1072C58.7754 16.7393 58.444 16.2173 58.3214 15.5412L59.7582 15.3224C59.8477 15.697 60.0316 15.9804 60.31 16.1726C60.5884 16.3615 60.9513 16.456 61.3988 16.456C61.886 16.456 62.2754 16.3549 62.5671 16.1527C62.8588 15.9472 63.0046 15.697 63.0046 15.402C63.0046 15.1634 62.9151 14.9628 62.7362 14.8004C62.5605 14.638 62.2904 14.5154 61.9258 14.4325L60.6332 14.1491C59.9206 13.9867 59.3936 13.7282 59.0522 13.3736C58.7141 13.0189 58.5451 12.5698 58.5451 12.0263C58.5451 11.5755 58.671 11.1811 58.9229 10.843C59.1748 10.505 59.5228 10.2415 59.967 10.0526C60.4111 9.86032 60.9199 9.7642 61.4933 9.7642C62.3185 9.7642 62.9682 9.94318 63.4421 10.3011C63.9161 10.6558 64.2293 11.1314 64.3817 11.728ZM67.6518 12.9659V17.5H66.1653V7.31818H67.6319V11.1065H67.7264C67.9054 10.6955 68.1788 10.3691 68.5467 10.1271C68.9146 9.88518 69.3952 9.7642 69.9885 9.7642C70.5121 9.7642 70.9695 9.87192 71.3606 10.0874C71.755 10.3028 72.06 10.6243 72.2754 11.0518C72.4941 11.4761 72.6035 12.0064 72.6035 12.6428V17.5H71.117V12.8217C71.117 12.2616 70.9728 11.8274 70.6845 11.5192C70.3961 11.2076 69.9951 11.0518 69.4814 11.0518C69.13 11.0518 68.8152 11.1264 68.5368 11.2756C68.2617 11.4247 68.0446 11.6435 67.8855 11.9318C67.7297 12.2169 67.6518 12.5616 67.6518 12.9659ZM77.8784 17.6541C77.126 17.6541 76.478 17.4934 75.9345 17.1719C75.3942 16.8471 74.9766 16.3913 74.6816 15.8047C74.39 15.2147 74.2441 14.5237 74.2441 13.7315C74.2441 12.9493 74.39 12.2599 74.6816 11.6634C74.9766 11.0668 75.3876 10.6011 75.9146 10.2663C76.4449 9.93158 77.0647 9.7642 77.774 9.7642C78.2048 9.7642 78.6225 9.83546 79.0268 9.97798C79.4312 10.1205 79.7941 10.3442 80.1156 10.6491C80.4371 10.9541 80.6906 11.3501 80.8762 11.8374C81.0618 12.3213 81.1547 12.9096 81.1547 13.6023V14.1293H75.0843V13.0156H79.698C79.698 12.6245 79.6184 12.2782 79.4593 11.9766C79.3002 11.6716 79.0765 11.4313 78.7882 11.2557C78.5031 11.08 78.1684 10.9922 77.7839 10.9922C77.3663 10.9922 77.0017 11.0949 76.6902 11.3004C76.3819 11.5026 76.1433 11.7678 75.9743 12.0959C75.8085 12.4207 75.7257 12.7737 75.7257 13.1548V14.0249C75.7257 14.5353 75.8152 14.9695 75.9941 15.3274C76.1764 15.6854 76.43 15.9588 76.7548 16.1477C77.0796 16.3333 77.4591 16.4261 77.8933 16.4261C78.175 16.4261 78.4319 16.3864 78.6639 16.3068C78.8959 16.224 79.0964 16.1013 79.2654 15.9389C79.4345 15.7765 79.5637 15.576 79.6532 15.3374L81.0602 15.5909C80.9475 16.0052 80.7453 16.3681 80.4537 16.6797C80.1653 16.9879 79.8024 17.2282 79.3649 17.4006C78.9307 17.5696 78.4352 17.6541 77.8784 17.6541ZM88.5263 11.728L87.179 11.9666C87.1226 11.7943 87.0331 11.6302 86.9105 11.4744C86.7912 11.3187 86.6288 11.1911 86.4233 11.0916C86.2178 10.9922 85.9609 10.9425 85.6527 10.9425C85.2318 10.9425 84.8804 11.0369 84.5987 11.2259C84.317 11.4115 84.1761 11.6518 84.1761 11.9467C84.1761 12.2019 84.2706 12.4074 84.4595 12.5632C84.6484 12.719 84.9534 12.8466 85.3743 12.946L86.5874 13.2244C87.29 13.3868 87.8137 13.6371 88.1584 13.9751C88.5031 14.3132 88.6754 14.7524 88.6754 15.2926C88.6754 15.75 88.5429 16.1577 88.2777 16.5156C88.0159 16.8703 87.6496 17.1487 87.179 17.3509C86.7116 17.553 86.1697 17.6541 85.5533 17.6541C84.6982 17.6541 84.0005 17.4718 83.4602 17.1072C82.92 16.7393 82.5885 16.2173 82.4659 15.5412L83.9027 15.3224C83.9922 15.697 84.1761 15.9804 84.4545 16.1726C84.733 16.3615 85.0959 16.456 85.5433 16.456C86.0305 16.456 86.42 16.3549 86.7116 16.1527C87.0033 15.9472 87.1491 15.697 87.1491 15.402C87.1491 15.1634 87.0597 14.9628 86.8807 14.8004C86.705 14.638 86.4349 14.5154 86.0703 14.4325L84.7777 14.1491C84.0651 13.9867 83.5381 13.7282 83.1967 13.3736C82.8587 13.0189 82.6896 12.5698 82.6896 12.0263C82.6896 11.5755 82.8156 11.1811 83.0675 10.843C83.3194 10.505 83.6674 10.2415 84.1115 10.0526C84.5556 9.86032 85.0644 9.7642 85.6378 9.7642C86.4631 9.7642 87.1127 9.94318 87.5866 10.3011C88.0606 10.6558 88.3738 11.1314 88.5263 11.728Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      hasOwnBackground: true,\r\n    },\r\n    {\r\n      id: \"My Weddings\",\r\n      label: \"My Weddings\",\r\n      defaultSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"url(#paint0_linear_261_25118)\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M29.0614 30.8129C28.4759 30.8112 27.9111 31.0286 27.478 31.4225C27.0449 31.0286 26.48 30.8112 25.8946 30.8129C25.3471 30.8127 24.8163 31.0018 24.3922 31.3481C23.968 31.6943 23.6765 32.1764 23.567 32.7129C23.5566 32.7649 23.5566 32.8184 23.5669 32.8704C23.5772 32.9224 23.5976 32.9719 23.627 33.016C23.6564 33.0601 23.6942 33.098 23.7383 33.1275C23.7823 33.157 23.8317 33.1775 23.8837 33.1879C23.9357 33.1983 23.9892 33.1984 24.0412 33.1881C24.0932 33.1778 24.1427 33.1574 24.1868 33.128C24.2309 33.0986 24.2688 33.0608 24.2983 33.0167C24.3278 32.9727 24.3483 32.9232 24.3587 32.8712C24.4312 32.5163 24.6232 32.1968 24.9027 31.9663C25.1822 31.7358 25.5323 31.6081 25.8946 31.6045C26.1411 31.6055 26.3839 31.6639 26.6039 31.7752C26.8238 31.8866 27.0146 32.0477 27.1613 32.2458C27.1982 32.295 27.246 32.3349 27.301 32.3624C27.3559 32.3898 27.4165 32.4042 27.478 32.4042C27.5394 32.4042 27.6 32.3898 27.655 32.3624C27.71 32.3349 27.7578 32.295 27.7947 32.2458C27.9413 32.0477 28.1322 31.8866 28.3521 31.7752C28.572 31.6639 28.8149 31.6055 29.0614 31.6045C29.4264 31.6045 29.7802 31.7305 30.063 31.9613C30.3457 32.1922 30.5401 32.5136 30.6131 32.8712C30.6316 32.962 30.6813 33.0434 30.7536 33.1013C30.826 33.1591 30.9163 33.1898 31.0089 33.1879H31.0881C31.139 33.1775 31.1874 33.1572 31.2305 33.1281C31.2736 33.099 31.3105 33.0617 31.3392 33.0183C31.3679 32.975 31.3877 32.9264 31.3976 32.8753C31.4075 32.8243 31.4072 32.7718 31.3968 32.7208C31.2887 32.1816 30.9967 31.6966 30.5708 31.3486C30.1448 31.0006 29.6113 30.8113 29.0614 30.8129Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.6448 28.4379H29.4572V27.3454C29.8276 27.0489 30.1265 26.673 30.3321 26.2455C30.5376 25.8179 30.6445 25.3497 30.6448 24.8753V22.8961C30.6452 22.797 30.6084 22.7014 30.5417 22.6281C30.475 22.5548 30.3833 22.5092 30.2846 22.5002C29.9268 22.466 29.5875 22.3251 29.3108 22.0957C29.0341 21.8663 28.8328 21.559 28.7329 21.2138C28.7089 21.1281 28.6568 21.0531 28.585 21.0006C28.5132 20.9482 28.4259 20.9215 28.337 20.9248H28.2222C28.1531 20.9252 28.0854 20.9437 28.0256 20.9784C27.9659 21.0131 27.9163 21.0629 27.8818 21.1227C27.639 21.54 27.2914 21.8867 26.8734 22.1283C26.4555 22.3699 25.9816 22.4981 25.4988 22.5002C25.265 22.4987 25.0323 22.4681 24.8061 22.4092C24.7465 22.3938 24.6842 22.3925 24.6241 22.4053C24.564 22.4182 24.5077 22.4449 24.4596 22.4833C24.4116 22.5217 24.3732 22.5708 24.3475 22.6267C24.3217 22.6825 24.3093 22.7436 24.3113 22.805V24.8753C24.3116 25.3497 24.4184 25.8179 24.624 26.2455C24.8295 26.673 25.1285 27.0489 25.4988 27.3454V28.4379H24.3113C23.5764 28.4379 22.8716 28.7298 22.3519 29.2495C21.8323 29.7691 21.5404 30.4739 21.5404 31.2088C21.5404 30.4739 21.2484 29.7691 20.7288 29.2495C20.2091 28.7298 19.5043 28.4379 18.7695 28.4379H17.5819V27.3454C17.9522 27.0489 18.2512 26.673 18.4567 26.2455C18.6623 25.8179 18.7692 25.3497 18.7695 24.8753V24.8357C19.2165 24.7445 19.6183 24.5016 19.9068 24.1481C20.1954 23.7947 20.3529 23.3524 20.3528 22.8961V20.1252C20.3528 19.9152 20.2694 19.7138 20.121 19.5654C19.9725 19.4169 19.7711 19.3335 19.5611 19.3335H12.8318C12.3069 19.3335 11.8034 19.542 11.4323 19.9132C11.0611 20.2844 10.8526 20.7878 10.8526 21.3127V22.8961C10.8526 23.0011 10.8943 23.1018 10.9685 23.176C11.0427 23.2502 11.1434 23.2919 11.2484 23.2919C11.3534 23.2919 11.4541 23.2502 11.5283 23.176C11.6026 23.1018 11.6443 23.0011 11.6443 22.8961V21.3127C11.6443 20.9978 11.7694 20.6957 11.9921 20.473C12.2148 20.2503 12.5168 20.1252 12.8318 20.1252H19.5611V22.8961C19.5605 23.1411 19.484 23.3799 19.3423 23.5797C19.2005 23.7796 19.0005 23.9307 18.7695 24.0124V22.1044C18.7696 22.05 18.7585 21.9962 18.7369 21.9462C18.7153 21.8963 18.6836 21.8514 18.6439 21.8142C18.6042 21.7771 18.5572 21.7485 18.5059 21.7304C18.4546 21.7122 18.4002 21.7048 18.3459 21.7086L16.7625 21.8234C16.7105 21.827 16.6598 21.8408 16.6132 21.8641C16.5665 21.8873 16.5249 21.9196 16.4907 21.9589C16.4566 21.9982 16.4304 22.0439 16.4139 22.0933C16.3974 22.1427 16.3908 22.1949 16.3944 22.2469C16.398 22.2989 16.4119 22.3496 16.4351 22.3963C16.4584 22.4429 16.4906 22.4845 16.5299 22.5187C16.5693 22.5529 16.6149 22.579 16.6644 22.5955C16.7138 22.612 16.766 22.6187 16.8179 22.615L17.9778 22.528V24.8753C17.9778 25.5052 17.7275 26.1093 17.2821 26.5547C16.8367 27.0002 16.2326 27.2504 15.6027 27.2504C14.9728 27.2504 14.3687 27.0002 13.9233 26.5547C13.4779 26.1093 13.2276 25.5052 13.2276 24.8753V22.8684L14.8387 22.7536C14.8908 22.7501 14.9416 22.7363 14.9883 22.7131C15.035 22.6899 15.0766 22.6577 15.1108 22.6183C15.145 22.579 15.1711 22.5332 15.1876 22.4838C15.2041 22.4343 15.2106 22.382 15.2069 22.33C15.1988 22.2257 15.1497 22.1287 15.0703 22.0605C14.9909 21.9922 14.8877 21.9582 14.7833 21.9659L12.8041 22.1044C12.704 22.1114 12.6103 22.1562 12.5419 22.2297C12.4736 22.3032 12.4357 22.3999 12.4359 22.5002V24.8753C12.4362 25.3497 12.5431 25.8179 12.7487 26.2455C12.9542 26.673 13.2532 27.0489 13.6235 27.3454V28.4379H12.4359C11.7011 28.4379 10.9963 28.7298 10.4766 29.2495C9.95697 29.7691 9.66504 30.4739 9.66504 31.2088V32.7922C9.66504 32.8972 9.70674 32.9979 9.78098 33.0721C9.85521 33.1463 9.9559 33.188 10.0609 33.188C10.1659 33.188 10.2666 33.1463 10.3408 33.0721C10.415 32.9979 10.4567 32.8972 10.4567 32.7922V31.2088C10.4592 30.7309 10.6346 30.2701 10.9503 29.9114C11.2661 29.5527 11.701 29.3204 12.1747 29.2573L13.576 32.9347C13.6047 33.0091 13.6552 33.0731 13.7209 33.1183C13.7866 33.1635 13.8644 33.1878 13.9441 33.188C13.993 33.1885 14.0415 33.179 14.0866 33.1603C14.1841 33.1223 14.2625 33.0472 14.3048 32.9516C14.3471 32.8559 14.3497 32.7474 14.3123 32.6497L13.9164 31.5888C13.9498 31.5991 13.9844 31.6045 14.0193 31.6047C14.0813 31.6043 14.1423 31.5894 14.1975 31.5611L15.6027 30.8605L17.0079 31.5611C17.0631 31.5894 17.1241 31.6043 17.1861 31.6047C17.2228 31.6104 17.2602 31.6104 17.2969 31.6047L16.9011 32.6655C16.8636 32.7632 16.8663 32.8717 16.9085 32.9674C16.9508 33.0631 17.0293 33.1381 17.1267 33.1762C17.1719 33.1949 17.2203 33.2043 17.2692 33.2039C17.3496 33.2044 17.4283 33.1805 17.4948 33.1352C17.5612 33.09 17.6124 33.0256 17.6413 32.9505L19.0386 29.2731C19.5082 29.3376 19.9391 29.5684 20.2529 29.9236C20.5667 30.2788 20.7426 30.7349 20.7487 31.2088V32.7922C20.7487 32.8972 20.7904 32.9979 20.8646 33.0721C20.9389 33.1463 21.0395 33.188 21.1445 33.188C21.2495 33.188 21.3502 33.1463 21.4244 33.0721C21.4987 32.9979 21.5404 32.8972 21.5404 32.7922C21.5404 32.8972 21.5821 32.9979 21.6563 33.0721C21.7305 33.1463 21.8312 33.188 21.9362 33.188C22.0412 33.188 22.1419 33.1463 22.2161 33.0721C22.2903 32.9979 22.3321 32.8972 22.3321 32.7922V31.2088C22.3321 30.6839 22.5406 30.1805 22.9118 29.8093C23.2829 29.4381 23.7864 29.2296 24.3113 29.2296H25.8947C25.9996 29.2296 26.1003 29.1879 26.1746 29.1137C26.2488 29.0394 26.2905 28.9387 26.2905 28.8338V27.8085C27.0515 28.12 27.9045 28.12 28.6656 27.8085V28.8338C28.6656 28.9387 28.7073 29.0394 28.7815 29.1137C28.8557 29.1879 28.9564 29.2296 29.0614 29.2296H30.6448C31.1697 29.2296 31.6731 29.4381 32.0443 29.8093C32.4155 30.1805 32.624 30.6839 32.624 31.2088V32.7922C32.624 32.8972 32.6657 32.9979 32.7399 33.0721C32.8142 33.1463 32.9149 33.188 33.0198 33.188C33.1248 33.188 33.2255 33.1463 33.2998 33.0721C33.374 32.9979 33.4157 32.8972 33.4157 32.7922V31.2088C33.4157 30.4739 33.1238 29.7691 32.6041 29.2495C32.0845 28.7298 31.3797 28.4379 30.6448 28.4379ZM14.4152 30.5676V30.2667L14.716 30.4171L14.4152 30.5676ZM16.7902 30.5676L16.4894 30.4171L16.7902 30.2667V30.5676ZM17.5819 30.8407V29.6254C17.5817 29.558 17.5643 29.4917 17.5312 29.4328C17.4982 29.374 17.4507 29.3246 17.3932 29.2893C17.3356 29.254 17.2701 29.234 17.2026 29.2311C17.1352 29.2283 17.0682 29.2428 17.0079 29.2731L15.6027 29.9738L14.1975 29.2731C14.1372 29.2428 14.0702 29.2283 14.0028 29.2311C13.9353 29.234 13.8698 29.254 13.8123 29.2893C13.7547 29.3246 13.7072 29.374 13.6742 29.4328C13.6411 29.4917 13.6237 29.558 13.6235 29.6254V30.8407L13.0099 29.2296H14.0193C14.1243 29.2296 14.225 29.1879 14.2992 29.1137C14.3735 29.0394 14.4152 28.9387 14.4152 28.8338V27.8085C15.1762 28.12 16.0292 28.12 16.7902 27.8085V28.4379H16.3944C16.2894 28.4379 16.1887 28.4796 16.1145 28.5539C16.0403 28.6281 15.9985 28.7288 15.9985 28.8338C15.9985 28.9387 16.0403 29.0394 16.1145 29.1137C16.1887 29.1879 16.2894 29.2296 16.3944 29.2296H18.1955L17.5819 30.8407ZM25.103 24.8753V23.2682C25.6868 23.3351 26.2782 23.2555 26.8235 23.0364C27.3688 22.8173 27.8509 22.4657 28.2262 22.0134C28.4041 22.3101 28.6397 22.5681 28.9192 22.7721C29.1987 22.9761 29.5162 23.1219 29.8531 23.2009V24.8753C29.8531 25.5052 29.6029 26.1093 29.1575 26.5547C28.712 27.0002 28.1079 27.2504 27.478 27.2504C26.8481 27.2504 26.244 27.0002 25.7986 26.5547C25.3532 26.1093 25.103 25.5052 25.103 24.8753Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M22.332 23.292V26.4587C22.332 26.5637 22.3737 26.6644 22.448 26.7386C22.5222 26.8129 22.6229 26.8546 22.7279 26.8546C22.8329 26.8546 22.9335 26.8129 23.0078 26.7386C23.082 26.6644 23.1237 26.5637 23.1237 26.4587V23.292C23.1237 22.1371 23.5825 21.0296 24.3991 20.213C25.2156 19.3964 26.3232 18.9377 27.478 18.9377C28.6328 18.9377 29.7404 19.3964 30.557 20.213C31.3735 21.0296 31.8323 22.1371 31.8323 23.292V26.4587C31.8323 26.5637 31.874 26.6644 31.9482 26.7386C32.0225 26.8129 32.1232 26.8546 32.2281 26.8546C32.3331 26.8546 32.4338 26.8129 32.508 26.7386C32.5823 26.6644 32.624 26.5637 32.624 26.4587V23.292C32.624 21.9272 32.0818 20.6183 31.1168 19.6532C30.1517 18.6882 28.8428 18.146 27.478 18.146C26.1132 18.146 24.8043 18.6882 23.8393 19.6532C22.8742 20.6183 22.332 21.9272 22.332 23.292Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M26.2904 24.8754C26.509 24.8754 26.6862 24.6982 26.6862 24.4796C26.6862 24.261 26.509 24.0837 26.2904 24.0837C26.0718 24.0837 25.8945 24.261 25.8945 24.4796C25.8945 24.6982 26.0718 24.8754 26.2904 24.8754Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M28.6664 24.8754C28.885 24.8754 29.0622 24.6982 29.0622 24.4796C29.0622 24.261 28.885 24.0837 28.6664 24.0837C28.4477 24.0837 28.2705 24.261 28.2705 24.4796C28.2705 24.6982 28.4477 24.8754 28.6664 24.8754Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.4154 24.4794C14.634 24.4794 14.8112 24.3022 14.8112 24.0836C14.8112 23.865 14.634 23.6877 14.4154 23.6877C14.1968 23.6877 14.0195 23.865 14.0195 24.0836C14.0195 24.3022 14.1968 24.4794 14.4154 24.4794Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M16.7904 24.4794C17.009 24.4794 17.1862 24.3022 17.1862 24.0836C17.1862 23.865 17.009 23.6877 16.7904 23.6877C16.5718 23.6877 16.3945 23.865 16.3945 24.0836C16.3945 24.3022 16.5718 24.4794 16.7904 24.4794Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M21.634 16.1249C21.5006 16.1249 21.3672 16.1027 21.2486 16.0656C20.2702 15.7321 17.75 14.257 17.75 11.4997C17.75 10.1655 18.8322 9.08325 20.159 9.08325C20.7001 9.08325 21.2189 9.26856 21.6414 9.5947C22.0639 9.26856 22.5828 9.08325 23.1239 9.08325C24.4507 9.08325 25.5329 10.1655 25.5329 11.4997C25.5329 14.257 23.0053 15.7321 22.0343 16.0656C21.9009 16.1027 21.7674 16.1249 21.634 16.1249ZM20.1516 10.1951C19.44 10.1951 18.8544 10.7807 18.8544 11.4997C18.8544 13.5973 20.8928 14.7685 21.6044 15.0131C21.6118 15.0131 21.6414 15.0131 21.6637 15.0131C22.3827 14.7611 24.4062 13.5899 24.4062 11.5071C24.4062 10.7881 23.8281 10.2025 23.1091 10.2025C22.7014 10.2025 22.3234 10.3952 22.0713 10.7214C21.8638 11.003 21.3894 11.003 21.1819 10.7214C20.9447 10.3878 20.5667 10.1951 20.1516 10.1951Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M29.0143 15.5716C28.9251 15.5477 28.8398 15.5089 28.7672 15.4629C28.1726 15.0645 26.7517 13.6265 27.2457 11.7826C27.4848 10.8904 28.4024 10.3607 29.2896 10.5984C29.6515 10.6954 29.9653 10.9122 30.1893 11.206C30.5303 11.0637 30.9105 11.0327 31.2723 11.1297C32.1596 11.3674 32.6894 12.285 32.4503 13.1772C31.9562 15.0211 30.0017 15.5546 29.2926 15.6036C29.1967 15.6045 29.1035 15.5955 29.0143 15.5716ZM29.0855 11.3406C28.6096 11.2131 28.1131 11.4997 27.9843 11.9805C27.6084 13.3833 28.7617 14.5317 29.1937 14.8228C29.1986 14.8241 29.2185 14.8294 29.2333 14.8334C29.7593 14.7937 31.3223 14.3731 31.6955 12.9803C31.8244 12.4995 31.5427 12.0043 31.0619 11.8755C30.7892 11.8024 30.5019 11.8636 30.275 12.0365C30.0857 12.1877 29.7685 12.1027 29.6802 11.8771C29.5813 11.6116 29.363 11.415 29.0855 11.3406Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M13.8492 15.7382C13.7692 15.7844 13.6815 15.8173 13.5976 15.8361C12.8955 15.9748 10.8739 15.9628 9.91946 14.3096C9.45762 13.5097 9.73184 12.4862 10.5273 12.027C10.8518 11.8397 11.227 11.7712 11.5932 11.8204C11.7336 11.4787 11.9805 11.1879 12.305 11.0006C13.1005 10.5414 14.1239 10.8156 14.5857 11.6155C15.5402 13.2687 14.5354 15.028 14.0687 15.5641C14.0015 15.6325 13.9292 15.6921 13.8492 15.7382ZM10.9078 12.6961C10.4811 12.9425 10.3327 13.4962 10.5816 13.9273C11.3077 15.185 12.9352 15.1816 13.4466 15.0819C13.451 15.0793 13.4688 15.0691 13.4821 15.0614C13.826 14.6614 14.6338 13.2588 13.9128 12.01C13.6639 11.5789 13.1146 11.428 12.6835 11.6768C12.4391 11.818 12.2791 12.0644 12.2409 12.3471C12.214 12.5879 11.9296 12.7521 11.7077 12.655C11.45 12.5372 11.1566 12.5525 10.9078 12.6961Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_261_25118\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      activeSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"#B31B1E\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M29.0614 30.8129C28.4759 30.8112 27.9111 31.0286 27.478 31.4225C27.0449 31.0286 26.48 30.8112 25.8946 30.8129C25.3471 30.8127 24.8163 31.0018 24.3922 31.3481C23.968 31.6943 23.6765 32.1764 23.567 32.7129C23.5566 32.7649 23.5566 32.8184 23.5669 32.8704C23.5772 32.9224 23.5976 32.9719 23.627 33.016C23.6564 33.0601 23.6942 33.098 23.7383 33.1275C23.7823 33.157 23.8317 33.1775 23.8837 33.1879C23.9357 33.1983 23.9892 33.1984 24.0412 33.1881C24.0932 33.1778 24.1427 33.1574 24.1868 33.128C24.2309 33.0986 24.2688 33.0608 24.2983 33.0167C24.3278 32.9727 24.3483 32.9232 24.3587 32.8712C24.4312 32.5163 24.6232 32.1968 24.9027 31.9663C25.1822 31.7358 25.5323 31.6081 25.8946 31.6045C26.1411 31.6055 26.3839 31.6639 26.6039 31.7752C26.8238 31.8866 27.0146 32.0477 27.1613 32.2458C27.1982 32.295 27.246 32.3349 27.301 32.3624C27.3559 32.3898 27.4165 32.4042 27.478 32.4042C27.5394 32.4042 27.6 32.3898 27.655 32.3624C27.71 32.3349 27.7578 32.295 27.7947 32.2458C27.9413 32.0477 28.1322 31.8866 28.3521 31.7752C28.572 31.6639 28.8149 31.6055 29.0614 31.6045C29.4264 31.6045 29.7802 31.7305 30.063 31.9613C30.3457 32.1922 30.5401 32.5136 30.6131 32.8712C30.6316 32.962 30.6813 33.0434 30.7536 33.1013C30.826 33.1591 30.9163 33.1898 31.0089 33.1879H31.0881C31.139 33.1775 31.1874 33.1572 31.2305 33.1281C31.2736 33.099 31.3105 33.0617 31.3392 33.0183C31.3679 32.975 31.3877 32.9264 31.3976 32.8753C31.4075 32.8243 31.4072 32.7718 31.3968 32.7208C31.2887 32.1816 30.9967 31.6966 30.5708 31.3486C30.1448 31.0006 29.6113 30.8113 29.0614 30.8129Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.6448 28.4379H29.4572V27.3454C29.8276 27.0489 30.1265 26.673 30.3321 26.2455C30.5376 25.8179 30.6445 25.3497 30.6448 24.8753V22.8961C30.6452 22.797 30.6084 22.7014 30.5417 22.6281C30.475 22.5548 30.3833 22.5092 30.2846 22.5002C29.9268 22.466 29.5875 22.3251 29.3108 22.0957C29.0341 21.8663 28.8328 21.559 28.7329 21.2138C28.7089 21.1281 28.6568 21.0531 28.585 21.0006C28.5132 20.9482 28.4259 20.9215 28.337 20.9248H28.2222C28.1531 20.9252 28.0854 20.9437 28.0256 20.9784C27.9659 21.0131 27.9163 21.0629 27.8818 21.1227C27.639 21.54 27.2914 21.8867 26.8734 22.1283C26.4555 22.3699 25.9816 22.4981 25.4988 22.5002C25.265 22.4987 25.0323 22.4681 24.8061 22.4092C24.7465 22.3938 24.6842 22.3925 24.6241 22.4053C24.564 22.4182 24.5077 22.4449 24.4596 22.4833C24.4116 22.5217 24.3732 22.5708 24.3475 22.6267C24.3217 22.6825 24.3093 22.7436 24.3113 22.805V24.8753C24.3116 25.3497 24.4184 25.8179 24.624 26.2455C24.8295 26.673 25.1285 27.0489 25.4988 27.3454V28.4379H24.3113C23.5764 28.4379 22.8716 28.7298 22.3519 29.2495C21.8323 29.7691 21.5404 30.4739 21.5404 31.2088C21.5404 30.4739 21.2484 29.7691 20.7288 29.2495C20.2091 28.7298 19.5043 28.4379 18.7695 28.4379H17.5819V27.3454C17.9522 27.0489 18.2512 26.673 18.4567 26.2455C18.6623 25.8179 18.7692 25.3497 18.7695 24.8753V24.8357C19.2165 24.7445 19.6183 24.5016 19.9068 24.1481C20.1954 23.7947 20.3529 23.3524 20.3528 22.8961V20.1252C20.3528 19.9152 20.2694 19.7138 20.121 19.5654C19.9725 19.4169 19.7711 19.3335 19.5611 19.3335H12.8318C12.3069 19.3335 11.8034 19.542 11.4323 19.9132C11.0611 20.2844 10.8526 20.7878 10.8526 21.3127V22.8961C10.8526 23.0011 10.8943 23.1018 10.9685 23.176C11.0427 23.2502 11.1434 23.2919 11.2484 23.2919C11.3534 23.2919 11.4541 23.2502 11.5283 23.176C11.6026 23.1018 11.6443 23.0011 11.6443 22.8961V21.3127C11.6443 20.9978 11.7694 20.6957 11.9921 20.473C12.2148 20.2503 12.5168 20.1252 12.8318 20.1252H19.5611V22.8961C19.5605 23.1411 19.484 23.3799 19.3423 23.5797C19.2005 23.7796 19.0005 23.9307 18.7695 24.0124V22.1044C18.7696 22.05 18.7585 21.9962 18.7369 21.9462C18.7153 21.8963 18.6836 21.8514 18.6439 21.8142C18.6042 21.7771 18.5572 21.7485 18.5059 21.7304C18.4546 21.7122 18.4002 21.7048 18.3459 21.7086L16.7625 21.8234C16.7105 21.827 16.6598 21.8408 16.6132 21.8641C16.5665 21.8873 16.5249 21.9196 16.4907 21.9589C16.4566 21.9982 16.4304 22.0439 16.4139 22.0933C16.3974 22.1427 16.3908 22.1949 16.3944 22.2469C16.398 22.2989 16.4119 22.3496 16.4351 22.3963C16.4584 22.4429 16.4906 22.4845 16.5299 22.5187C16.5693 22.5529 16.6149 22.579 16.6644 22.5955C16.7138 22.612 16.766 22.6187 16.8179 22.615L17.9778 22.528V24.8753C17.9778 25.5052 17.7275 26.1093 17.2821 26.5547C16.8367 27.0002 16.2326 27.2504 15.6027 27.2504C14.9728 27.2504 14.3687 27.0002 13.9233 26.5547C13.4779 26.1093 13.2276 25.5052 13.2276 24.8753V22.8684L14.8387 22.7536C14.8908 22.7501 14.9416 22.7363 14.9883 22.7131C15.035 22.6899 15.0766 22.6577 15.1108 22.6183C15.145 22.579 15.1711 22.5332 15.1876 22.4838C15.2041 22.4343 15.2106 22.382 15.2069 22.33C15.1988 22.2257 15.1497 22.1287 15.0703 22.0605C14.9909 21.9922 14.8877 21.9582 14.7833 21.9659L12.8041 22.1044C12.704 22.1114 12.6103 22.1562 12.5419 22.2297C12.4736 22.3032 12.4357 22.3999 12.4359 22.5002V24.8753C12.4362 25.3497 12.5431 25.8179 12.7487 26.2455C12.9542 26.673 13.2532 27.0489 13.6235 27.3454V28.4379H12.4359C11.7011 28.4379 10.9963 28.7298 10.4766 29.2495C9.95697 29.7691 9.66504 30.4739 9.66504 31.2088V32.7922C9.66504 32.8972 9.70674 32.9979 9.78098 33.0721C9.85521 33.1463 9.9559 33.188 10.0609 33.188C10.1659 33.188 10.2666 33.1463 10.3408 33.0721C10.415 32.9979 10.4567 32.8972 10.4567 32.7922V31.2088C10.4592 30.7309 10.6346 30.2701 10.9503 29.9114C11.2661 29.5527 11.701 29.3204 12.1747 29.2573L13.576 32.9347C13.6047 33.0091 13.6552 33.0731 13.7209 33.1183C13.7866 33.1635 13.8644 33.1878 13.9441 33.188C13.993 33.1885 14.0415 33.179 14.0866 33.1603C14.1841 33.1223 14.2625 33.0472 14.3048 32.9516C14.3471 32.8559 14.3497 32.7474 14.3123 32.6497L13.9164 31.5888C13.9498 31.5991 13.9844 31.6045 14.0193 31.6047C14.0813 31.6043 14.1423 31.5894 14.1975 31.5611L15.6027 30.8605L17.0079 31.5611C17.0631 31.5894 17.1241 31.6043 17.1861 31.6047C17.2228 31.6104 17.2602 31.6104 17.2969 31.6047L16.9011 32.6655C16.8636 32.7632 16.8663 32.8717 16.9085 32.9674C16.9508 33.0631 17.0293 33.1381 17.1267 33.1762C17.1719 33.1949 17.2203 33.2043 17.2692 33.2039C17.3496 33.2044 17.4283 33.1805 17.4948 33.1352C17.5612 33.09 17.6124 33.0256 17.6413 32.9505L19.0386 29.2731C19.5082 29.3376 19.9391 29.5684 20.2529 29.9236C20.5667 30.2788 20.7426 30.7349 20.7487 31.2088V32.7922C20.7487 32.8972 20.7904 32.9979 20.8646 33.0721C20.9389 33.1463 21.0395 33.188 21.1445 33.188C21.2495 33.188 21.3502 33.1463 21.4244 33.0721C21.4987 32.9979 21.5404 32.8972 21.5404 32.7922C21.5404 32.8972 21.5821 32.9979 21.6563 33.0721C21.7305 33.1463 21.8312 33.188 21.9362 33.188C22.0412 33.188 22.1419 33.1463 22.2161 33.0721C22.2903 32.9979 22.3321 32.8972 22.3321 32.7922V31.2088C22.3321 30.6839 22.5406 30.1805 22.9118 29.8093C23.2829 29.4381 23.7864 29.2296 24.3113 29.2296H25.8947C25.9996 29.2296 26.1003 29.1879 26.1746 29.1137C26.2488 29.0394 26.2905 28.9387 26.2905 28.8338V27.8085C27.0515 28.12 27.9045 28.12 28.6656 27.8085V28.8338C28.6656 28.9387 28.7073 29.0394 28.7815 29.1137C28.8557 29.1879 28.9564 29.2296 29.0614 29.2296H30.6448C31.1697 29.2296 31.6731 29.4381 32.0443 29.8093C32.4155 30.1805 32.624 30.6839 32.624 31.2088V32.7922C32.624 32.8972 32.6657 32.9979 32.7399 33.0721C32.8142 33.1463 32.9149 33.188 33.0198 33.188C33.1248 33.188 33.2255 33.1463 33.2998 33.0721C33.374 32.9979 33.4157 32.8972 33.4157 32.7922V31.2088C33.4157 30.4739 33.1238 29.7691 32.6041 29.2495C32.0845 28.7298 31.3797 28.4379 30.6448 28.4379ZM14.4152 30.5676V30.2667L14.716 30.4171L14.4152 30.5676ZM16.7902 30.5676L16.4894 30.4171L16.7902 30.2667V30.5676ZM17.5819 30.8407V29.6254C17.5817 29.558 17.5643 29.4917 17.5312 29.4328C17.4982 29.374 17.4507 29.3246 17.3932 29.2893C17.3356 29.254 17.2701 29.234 17.2026 29.2311C17.1352 29.2283 17.0682 29.2428 17.0079 29.2731L15.6027 29.9738L14.1975 29.2731C14.1372 29.2428 14.0702 29.2283 14.0028 29.2311C13.9353 29.234 13.8698 29.254 13.8123 29.2893C13.7547 29.3246 13.7072 29.374 13.6742 29.4328C13.6411 29.4917 13.6237 29.558 13.6235 29.6254V30.8407L13.0099 29.2296H14.0193C14.1243 29.2296 14.225 29.1879 14.2992 29.1137C14.3735 29.0394 14.4152 28.9387 14.4152 28.8338V27.8085C15.1762 28.12 16.0292 28.12 16.7902 27.8085V28.4379H16.3944C16.2894 28.4379 16.1887 28.4796 16.1145 28.5539C16.0403 28.6281 15.9985 28.7288 15.9985 28.8338C15.9985 28.9387 16.0403 29.0394 16.1145 29.1137C16.1887 29.1879 16.2894 29.2296 16.3944 29.2296H18.1955L17.5819 30.8407ZM25.103 24.8753V23.2682C25.6868 23.3351 26.2782 23.2555 26.8235 23.0364C27.3688 22.8173 27.8509 22.4657 28.2262 22.0134C28.4041 22.3101 28.6397 22.5681 28.9192 22.7721C29.1987 22.9761 29.5162 23.1219 29.8531 23.2009V24.8753C29.8531 25.5052 29.6029 26.1093 29.1575 26.5547C28.712 27.0002 28.1079 27.2504 27.478 27.2504C26.8481 27.2504 26.244 27.0002 25.7986 26.5547C25.3532 26.1093 25.103 25.5052 25.103 24.8753Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M22.332 23.292V26.4587C22.332 26.5637 22.3737 26.6644 22.448 26.7386C22.5222 26.8129 22.6229 26.8546 22.7279 26.8546C22.8329 26.8546 22.9335 26.8129 23.0078 26.7386C23.082 26.6644 23.1237 26.5637 23.1237 26.4587V23.292C23.1237 22.1371 23.5825 21.0296 24.3991 20.213C25.2156 19.3964 26.3232 18.9377 27.478 18.9377C28.6328 18.9377 29.7404 19.3964 30.557 20.213C31.3735 21.0296 31.8323 22.1371 31.8323 23.292V26.4587C31.8323 26.5637 31.874 26.6644 31.9482 26.7386C32.0225 26.8129 32.1232 26.8546 32.2281 26.8546C32.3331 26.8546 32.4338 26.8129 32.508 26.7386C32.5823 26.6644 32.624 26.5637 32.624 26.4587V23.292C32.624 21.9272 32.0818 20.6183 31.1168 19.6532C30.1517 18.6882 28.8428 18.146 27.478 18.146C26.1132 18.146 24.8043 18.6882 23.8393 19.6532C22.8742 20.6183 22.332 21.9272 22.332 23.292Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M26.2904 24.8754C26.509 24.8754 26.6862 24.6982 26.6862 24.4796C26.6862 24.261 26.509 24.0837 26.2904 24.0837C26.0718 24.0837 25.8945 24.261 25.8945 24.4796C25.8945 24.6982 26.0718 24.8754 26.2904 24.8754Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M28.6664 24.8754C28.885 24.8754 29.0622 24.6982 29.0622 24.4796C29.0622 24.261 28.885 24.0837 28.6664 24.0837C28.4477 24.0837 28.2705 24.261 28.2705 24.4796C28.2705 24.6982 28.4477 24.8754 28.6664 24.8754Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.4154 24.4794C14.634 24.4794 14.8112 24.3022 14.8112 24.0836C14.8112 23.865 14.634 23.6877 14.4154 23.6877C14.1968 23.6877 14.0195 23.865 14.0195 24.0836C14.0195 24.3022 14.1968 24.4794 14.4154 24.4794Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M16.7904 24.4794C17.009 24.4794 17.1862 24.3022 17.1862 24.0836C17.1862 23.865 17.009 23.6877 16.7904 23.6877C16.5718 23.6877 16.3945 23.865 16.3945 24.0836C16.3945 24.3022 16.5718 24.4794 16.7904 24.4794Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M21.634 16.1249C21.5006 16.1249 21.3672 16.1027 21.2486 16.0656C20.2702 15.7321 17.75 14.257 17.75 11.4997C17.75 10.1655 18.8322 9.08325 20.159 9.08325C20.7001 9.08325 21.2189 9.26856 21.6414 9.5947C22.0639 9.26856 22.5828 9.08325 23.1239 9.08325C24.4507 9.08325 25.5329 10.1655 25.5329 11.4997C25.5329 14.257 23.0053 15.7321 22.0343 16.0656C21.9009 16.1027 21.7674 16.1249 21.634 16.1249ZM20.1516 10.1951C19.44 10.1951 18.8544 10.7807 18.8544 11.4997C18.8544 13.5973 20.8928 14.7685 21.6044 15.0131C21.6118 15.0131 21.6414 15.0131 21.6637 15.0131C22.3827 14.7611 24.4062 13.5899 24.4062 11.5071C24.4062 10.7881 23.8281 10.2025 23.1091 10.2025C22.7014 10.2025 22.3234 10.3952 22.0713 10.7214C21.8638 11.003 21.3894 11.003 21.1819 10.7214C20.9447 10.3878 20.5667 10.1951 20.1516 10.1951Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M29.0143 15.5716C28.9251 15.5477 28.8398 15.5089 28.7672 15.4629C28.1726 15.0645 26.7517 13.6265 27.2457 11.7826C27.4848 10.8904 28.4024 10.3607 29.2896 10.5984C29.6515 10.6954 29.9653 10.9122 30.1893 11.206C30.5303 11.0637 30.9105 11.0327 31.2723 11.1297C32.1596 11.3674 32.6894 12.285 32.4503 13.1772C31.9562 15.0211 30.0017 15.5546 29.2926 15.6036C29.1967 15.6045 29.1035 15.5955 29.0143 15.5716ZM29.0855 11.3406C28.6096 11.2131 28.1131 11.4997 27.9843 11.9805C27.6084 13.3833 28.7617 14.5317 29.1937 14.8228C29.1986 14.8241 29.2185 14.8294 29.2333 14.8334C29.7593 14.7937 31.3223 14.3731 31.6955 12.9803C31.8244 12.4995 31.5427 12.0043 31.0619 11.8755C30.7892 11.8024 30.5019 11.8636 30.275 12.0365C30.0857 12.1877 29.7685 12.1027 29.6802 11.8771C29.5813 11.6116 29.363 11.415 29.0855 11.3406Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M13.8492 15.7382C13.7692 15.7844 13.6815 15.8173 13.5976 15.8361C12.8955 15.9748 10.8739 15.9628 9.91946 14.3096C9.45762 13.5097 9.73184 12.4862 10.5273 12.027C10.8518 11.8397 11.227 11.7712 11.5932 11.8204C11.7336 11.4787 11.9805 11.1879 12.305 11.0006C13.1005 10.5414 14.1239 10.8156 14.5857 11.6155C15.5402 13.2687 14.5354 15.028 14.0687 15.5641C14.0015 15.6325 13.9292 15.6921 13.8492 15.7382ZM10.9078 12.6961C10.4811 12.9425 10.3327 13.4962 10.5816 13.9273C11.3077 15.185 12.9352 15.1816 13.4466 15.0819C13.451 15.0793 13.4688 15.0691 13.4821 15.0614C13.826 14.6614 14.6338 13.2588 13.9128 12.01C13.6639 11.5789 13.1146 11.428 12.6835 11.6768C12.4391 11.818 12.2791 12.0644 12.2409 12.3471C12.214 12.5879 11.9296 12.7521 11.7077 12.655C11.45 12.5372 11.1566 12.5525 10.9078 12.6961Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_261_25118\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      expandedDefaultSvg: (\r\n        <svg\r\n          width=\"129\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 129 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M23.4413 21.058C22.9009 21.0564 22.3796 21.2572 21.9797 21.6207C21.5799 21.2572 21.0585 21.0564 20.5182 21.058C20.0128 21.0579 19.5228 21.2324 19.1313 21.552C18.7398 21.8717 18.4707 22.3167 18.3696 22.8119C18.3601 22.8599 18.36 22.9093 18.3695 22.9573C18.379 23.0053 18.3979 23.051 18.425 23.0917C18.4521 23.1324 18.487 23.1674 18.5277 23.1946C18.5684 23.2218 18.614 23.2408 18.662 23.2504C18.7099 23.26 18.7594 23.26 18.8074 23.2505C18.8554 23.241 18.901 23.2222 18.9417 23.195C18.9825 23.1679 19.0174 23.133 19.0447 23.0923C19.0719 23.0517 19.0908 23.0061 19.1004 22.9581C19.1673 22.6304 19.3445 22.3355 19.6025 22.1228C19.8606 21.91 20.1837 21.7921 20.5182 21.7888C20.7457 21.7897 20.9699 21.8436 21.1729 21.9464C21.3759 22.0491 21.5521 22.1979 21.6874 22.3808C21.7215 22.4261 21.7656 22.463 21.8163 22.4883C21.8671 22.5137 21.923 22.5269 21.9797 22.5269C22.0365 22.5269 22.0924 22.5137 22.1432 22.4883C22.1939 22.463 22.238 22.4261 22.2721 22.3808C22.4074 22.1979 22.5836 22.0491 22.7866 21.9464C22.9896 21.8436 23.2138 21.7897 23.4413 21.7888C23.7783 21.7887 24.1049 21.9051 24.3659 22.1182C24.6269 22.3312 24.8063 22.6279 24.8737 22.9581C24.8908 23.0419 24.9367 23.117 25.0034 23.1704C25.0702 23.2238 25.1536 23.2521 25.2391 23.2504H25.3121C25.3592 23.2408 25.4039 23.222 25.4436 23.1952C25.4834 23.1683 25.5175 23.1339 25.544 23.0939C25.5704 23.0538 25.5888 23.009 25.5979 22.9618C25.607 22.9147 25.6068 22.8663 25.5972 22.8192C25.4973 22.3215 25.2278 21.8738 24.8346 21.5526C24.4415 21.2314 23.949 21.0566 23.4413 21.058Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M24.903 18.8657H23.8068V17.8573C24.1487 17.5836 24.4246 17.2366 24.6144 16.842C24.8041 16.4473 24.9028 16.0151 24.903 15.5772V13.7502C24.9034 13.6588 24.8694 13.5705 24.8079 13.5028C24.7463 13.4352 24.6616 13.3931 24.5705 13.3848C24.2403 13.3532 23.9271 13.2231 23.6717 13.0114C23.4163 12.7996 23.2304 12.516 23.1382 12.1973C23.1161 12.1183 23.068 12.049 23.0017 12.0006C22.9354 11.9522 22.8548 11.9275 22.7728 11.9306H22.6668C22.603 11.9309 22.5405 11.948 22.4854 11.9801C22.4302 12.0121 22.3845 12.058 22.3526 12.1133C22.1285 12.4985 21.8076 12.8184 21.4218 13.0415C21.036 13.2645 20.5985 13.3829 20.1529 13.3848C19.9371 13.3834 19.7223 13.3552 19.5135 13.3008C19.4585 13.2866 19.401 13.2853 19.3455 13.2972C19.29 13.3091 19.238 13.3337 19.1937 13.3692C19.1493 13.4047 19.1139 13.45 19.0901 13.5015C19.0663 13.5531 19.0549 13.6094 19.0567 13.6662V15.5772C19.057 16.0151 19.1556 16.4473 19.3454 16.842C19.5351 17.2366 19.8111 17.5836 20.1529 17.8573V18.8657H19.0567C18.3784 18.8657 17.7278 19.1352 17.2481 19.6149C16.7684 20.0946 16.4989 20.7451 16.4989 21.4235C16.4989 20.7451 16.2295 20.0946 15.7498 19.6149C15.2701 19.1352 14.6195 18.8657 13.9412 18.8657H12.845V17.8573C13.1868 17.5836 13.4628 17.2366 13.6525 16.842C13.8423 16.4473 13.9409 16.0151 13.9412 15.5772V15.5407C14.3538 15.4564 14.7247 15.2322 14.9911 14.9059C15.2574 14.5797 15.4029 14.1714 15.4028 13.7502V11.1925C15.4028 10.9986 15.3258 10.8128 15.1887 10.6757C15.0517 10.5387 14.8658 10.4617 14.672 10.4617H8.46027C7.97572 10.4617 7.51103 10.6542 7.1684 10.9968C6.82578 11.3394 6.63329 11.8041 6.63329 12.2886V13.7502C6.63329 13.8471 6.67179 13.9401 6.74031 14.0086C6.80884 14.0771 6.90178 14.1156 6.99869 14.1156C7.0956 14.1156 7.18854 14.0771 7.25706 14.0086C7.32559 13.9401 7.36408 13.8471 7.36408 13.7502V12.2886C7.36408 11.9979 7.47957 11.7191 7.68515 11.5135C7.89072 11.3079 8.16954 11.1925 8.46027 11.1925H14.672V13.7502C14.6713 13.9764 14.6008 14.1968 14.4699 14.3813C14.3391 14.5658 14.1544 14.7052 13.9412 14.7806V13.0194C13.9413 12.9692 13.9311 12.9195 13.9111 12.8734C13.8912 12.8273 13.862 12.7859 13.8253 12.7516C13.7886 12.7173 13.7452 12.6909 13.6979 12.6742C13.6506 12.6574 13.6003 12.6505 13.5502 12.654L12.0886 12.76C12.0407 12.7634 11.9938 12.7761 11.9508 12.7976C11.9077 12.8191 11.8693 12.8488 11.8378 12.8851C11.8062 12.9214 11.7821 12.9636 11.7668 13.0092C11.7516 13.0548 11.7455 13.103 11.7488 13.151C11.7522 13.199 11.765 13.2458 11.7864 13.2889C11.8079 13.3319 11.8376 13.3703 11.8739 13.4019C11.9102 13.4334 11.9524 13.4575 11.998 13.4728C12.0436 13.488 12.0918 13.4942 12.1398 13.4908L13.2104 13.4104V15.5772C13.2104 16.1586 12.9794 16.7163 12.5683 17.1274C12.1571 17.5386 11.5995 17.7696 11.018 17.7696C10.4366 17.7696 9.87894 17.5386 9.46779 17.1274C9.05664 16.7163 8.82566 16.1586 8.82566 15.5772V13.7246L10.3128 13.6187C10.3608 13.6154 10.4078 13.6027 10.4509 13.5813C10.494 13.5599 10.5324 13.5301 10.564 13.4938C10.5956 13.4575 10.6196 13.4153 10.6349 13.3696C10.6501 13.3239 10.6561 13.2757 10.6526 13.2277C10.6452 13.1314 10.5998 13.0419 10.5266 12.9789C10.4533 12.9159 10.358 12.8845 10.2617 12.8915L8.43469 13.0194C8.34228 13.0259 8.25578 13.0673 8.1927 13.1351C8.12961 13.2029 8.09464 13.2922 8.09487 13.3848V15.5772C8.09515 16.0151 8.19379 16.4473 8.38353 16.842C8.57326 17.2366 8.84923 17.5836 9.19106 17.8573V18.8657H8.09487C7.41651 18.8657 6.76593 19.1352 6.28626 19.6149C5.80659 20.0946 5.53711 20.7451 5.53711 21.4235V22.8851C5.53711 22.982 5.57561 23.0749 5.64413 23.1435C5.71266 23.212 5.8056 23.2505 5.9025 23.2505C5.99941 23.2505 6.09235 23.212 6.16088 23.1435C6.2294 23.0749 6.2679 22.982 6.2679 22.8851V21.4235C6.27022 20.9824 6.43205 20.557 6.72353 20.2259C7.01501 19.8948 7.41645 19.6803 7.85371 19.6221L9.14721 23.0166C9.17371 23.0853 9.22033 23.1444 9.28096 23.1861C9.3416 23.2278 9.41342 23.2503 9.48703 23.2505C9.53215 23.2509 9.57688 23.2422 9.61857 23.2249C9.70852 23.1898 9.78092 23.1205 9.81995 23.0322C9.85897 22.9439 9.86145 22.8437 9.82684 22.7535L9.46145 21.7743C9.49223 21.7838 9.52424 21.7887 9.55645 21.7889C9.61365 21.7886 9.66997 21.7748 9.72088 21.7487L11.018 21.102L12.3152 21.7487C12.3661 21.7748 12.4224 21.7886 12.4796 21.7889C12.5135 21.7942 12.548 21.7942 12.5819 21.7889L12.2165 22.7682C12.1819 22.8583 12.1844 22.9585 12.2234 23.0468C12.2624 23.1351 12.3348 23.2044 12.4248 23.2395C12.4665 23.2568 12.5112 23.2655 12.5563 23.2651C12.6306 23.2656 12.7032 23.2435 12.7646 23.2017C12.8259 23.16 12.8731 23.1005 12.8998 23.0312L14.1897 19.6367C14.6231 19.6962 15.0209 19.9093 15.3105 20.2372C15.6002 20.565 15.7626 20.986 15.7682 21.4235V22.8851C15.7682 22.982 15.8067 23.0749 15.8752 23.1435C15.9437 23.212 16.0366 23.2505 16.1336 23.2505C16.2305 23.2505 16.3234 23.212 16.3919 23.1435C16.4605 23.0749 16.4989 22.982 16.4989 22.8851C16.4989 22.982 16.5374 23.0749 16.606 23.1435C16.6745 23.212 16.7674 23.2505 16.8643 23.2505C16.9613 23.2505 17.0542 23.212 17.1227 23.1435C17.1912 23.0749 17.2297 22.982 17.2297 22.8851V21.4235C17.2297 20.939 17.4222 20.4743 17.7648 20.1316C18.1075 19.789 18.5722 19.5965 19.0567 19.5965H20.5183C20.6152 19.5965 20.7081 19.558 20.7767 19.4895C20.8452 19.421 20.8837 19.328 20.8837 19.2311V18.2848C21.5862 18.5723 22.3735 18.5723 23.0761 18.2848V19.2311C23.0761 19.328 23.1145 19.421 23.1831 19.4895C23.2516 19.558 23.3445 19.5965 23.4414 19.5965H24.903C25.3876 19.5965 25.8523 19.789 26.1949 20.1316C26.5375 20.4743 26.73 20.939 26.73 21.4235V22.8851C26.73 22.982 26.7685 23.0749 26.837 23.1435C26.9055 23.212 26.9985 23.2505 27.0954 23.2505C27.1923 23.2505 27.2852 23.212 27.3538 23.1435C27.4223 23.0749 27.4608 22.982 27.4608 22.8851V21.4235C27.4608 20.7451 27.1913 20.0946 26.7116 19.6149C26.232 19.1352 25.5814 18.8657 24.903 18.8657ZM9.92184 20.8316V20.5539L10.1995 20.6927L9.92184 20.8316ZM12.1142 20.8316L11.8365 20.6927L12.1142 20.5539V20.8316ZM12.845 21.0837V19.9619C12.8448 19.8996 12.8287 19.8384 12.7982 19.7841C12.7677 19.7298 12.7238 19.6842 12.6708 19.6516C12.6177 19.619 12.5571 19.6006 12.4949 19.598C12.4327 19.5954 12.3708 19.6087 12.3152 19.6367L11.018 20.2835L9.72088 19.6367C9.66525 19.6087 9.60339 19.5954 9.54116 19.598C9.47892 19.6006 9.41839 19.619 9.36531 19.6516C9.31222 19.6842 9.26835 19.7298 9.23785 19.7841C9.20735 19.8384 9.19124 19.8996 9.19106 19.9619V21.0837L8.62469 19.5965H9.55645C9.65336 19.5965 9.7463 19.558 9.81482 19.4895C9.88335 19.421 9.92184 19.328 9.92184 19.2311V18.2848C10.6244 18.5723 11.4117 18.5723 12.1142 18.2848V18.8657H11.7488C11.6519 18.8657 11.559 18.9042 11.4904 18.9728C11.4219 19.0413 11.3834 19.1342 11.3834 19.2311C11.3834 19.328 11.4219 19.421 11.4904 19.4895C11.559 19.558 11.6519 19.5965 11.7488 19.5965H13.4114L12.845 21.0837ZM19.7875 15.5772V14.0937C20.3265 14.1555 20.8723 14.0819 21.3757 13.8797C21.8791 13.6775 22.3241 13.3529 22.6705 12.9354C22.8347 13.2093 23.0522 13.4475 23.3102 13.6358C23.5681 13.8241 23.8613 13.9587 24.1722 14.0316V15.5772C24.1722 16.1586 23.9413 16.7163 23.5301 17.1274C23.119 17.5386 22.5613 17.7696 21.9799 17.7696C21.3984 17.7696 20.8408 17.5386 20.4296 17.1274C20.0185 16.7163 19.7875 16.1586 19.7875 15.5772Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M17.2295 14.1156V17.0388C17.2295 17.1357 17.268 17.2286 17.3365 17.2971C17.405 17.3657 17.498 17.4042 17.5949 17.4042C17.6918 17.4042 17.7847 17.3657 17.8533 17.2971C17.9218 17.2286 17.9603 17.1357 17.9603 17.0388V14.1156C17.9603 13.0496 18.3837 12.0273 19.1375 11.2735C19.8913 10.5197 20.9136 10.0963 21.9796 10.0963C23.0456 10.0963 24.068 10.5197 24.8217 11.2735C25.5755 12.0273 25.999 13.0496 25.999 14.1156V17.0388C25.999 17.1357 26.0375 17.2286 26.106 17.2971C26.1745 17.3657 26.2674 17.4042 26.3644 17.4042C26.4613 17.4042 26.5542 17.3657 26.6227 17.2971C26.6913 17.2286 26.7298 17.1357 26.7298 17.0388V14.1156C26.7298 12.8558 26.2293 11.6476 25.3385 10.7568C24.4476 9.86594 23.2394 9.36548 21.9796 9.36548C20.7198 9.36548 19.5116 9.86594 18.6208 10.7568C17.73 11.6476 17.2295 12.8558 17.2295 14.1156Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M20.883 15.5772C21.0848 15.5772 21.2484 15.4136 21.2484 15.2118C21.2484 15.01 21.0848 14.8464 20.883 14.8464C20.6812 14.8464 20.5176 15.01 20.5176 15.2118C20.5176 15.4136 20.6812 15.5772 20.883 15.5772Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M23.0763 15.5772C23.2781 15.5772 23.4417 15.4136 23.4417 15.2118C23.4417 15.01 23.2781 14.8464 23.0763 14.8464C22.8745 14.8464 22.7109 15.01 22.7109 15.2118C22.7109 15.4136 22.8745 15.5772 23.0763 15.5772Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9.92204 15.2117C10.1238 15.2117 10.2874 15.0482 10.2874 14.8464C10.2874 14.6445 10.1238 14.481 9.92204 14.481C9.72023 14.481 9.55664 14.6445 9.55664 14.8464C9.55664 15.0482 9.72023 15.2117 9.92204 15.2117Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M12.1144 15.2117C12.3162 15.2117 12.4798 15.0482 12.4798 14.8464C12.4798 14.6445 12.3162 14.481 12.1144 14.481C11.9126 14.481 11.749 14.6445 11.749 14.8464C11.749 15.0482 11.9126 15.2117 12.1144 15.2117Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M16.5853 7.5C16.4621 7.5 16.3389 7.47946 16.2295 7.44525C15.3263 7.13736 13 5.7758 13 3.23053C13 1.99895 13.9989 1 15.2237 1C15.7232 1 16.2021 1.17105 16.5921 1.47211C16.9821 1.17105 17.4611 1 17.9605 1C19.1853 1 20.1842 1.99895 20.1842 3.23053C20.1842 5.7758 17.8511 7.13736 16.9547 7.44525C16.8316 7.47946 16.7084 7.5 16.5853 7.5ZM15.2168 2.02632C14.56 2.02632 14.0195 2.56685 14.0195 3.23053C14.0195 5.16685 15.901 6.2479 16.5579 6.47368C16.5647 6.47368 16.5921 6.47368 16.6126 6.47368C17.2763 6.24105 19.1442 5.16001 19.1442 3.23738C19.1442 2.5737 18.6105 2.03316 17.9468 2.03316C17.5705 2.03316 17.2216 2.21107 16.9889 2.51212C16.7974 2.77212 16.3595 2.77212 16.1679 2.51212C15.949 2.20423 15.6 2.02632 15.2168 2.02632Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M23.3979 6.98908C23.3155 6.96701 23.2368 6.93121 23.1698 6.88872C22.621 6.521 21.3093 5.19366 21.7654 3.4916C21.986 2.66802 22.8331 2.17899 23.6521 2.39845C23.9861 2.48794 24.2757 2.68815 24.4826 2.95935C24.7973 2.82791 25.1482 2.79934 25.4822 2.88884C26.3012 3.10829 26.7903 3.9553 26.5696 4.77888C26.1135 6.48094 24.3093 6.97338 23.6548 7.01867C23.5663 7.01948 23.4802 7.01115 23.3979 6.98908ZM23.4636 3.08353C23.0243 2.96584 22.566 3.23045 22.4471 3.67427C22.1001 4.96912 23.1647 6.02918 23.5635 6.29786C23.568 6.29909 23.5864 6.30399 23.6001 6.30767C24.0856 6.27103 25.5284 5.88281 25.8729 4.59711C25.9918 4.15329 25.7318 3.6962 25.2879 3.57728C25.0363 3.50985 24.7711 3.56629 24.5616 3.72593C24.3869 3.86547 24.094 3.78701 24.0125 3.57881C23.9213 3.33369 23.7198 3.15219 23.4636 3.08353Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9.39956 7.14288C9.32572 7.18551 9.24478 7.21583 9.1673 7.23321C8.51923 7.36124 6.65316 7.35017 5.77211 5.82415C5.34579 5.08575 5.59892 4.14103 6.33322 3.71708C6.63269 3.54418 6.97905 3.48095 7.31709 3.52645C7.44671 3.21095 7.67465 2.94261 7.97411 2.76971C8.70841 2.34576 9.65313 2.5989 10.0794 3.3373C10.9605 4.86333 10.0329 6.4873 9.60213 6.98216C9.54014 7.0453 9.4734 7.10025 9.39956 7.14288ZM6.68438 4.33478C6.29057 4.56215 6.1536 5.07334 6.38334 5.47125C7.0536 6.63218 8.55592 6.62902 9.02789 6.53702C9.03199 6.53465 9.0484 6.52518 9.06071 6.51807C9.3781 6.14886 10.1238 4.85414 9.45827 3.70141C9.22853 3.3035 8.72145 3.16416 8.32354 3.39389C8.09791 3.52416 7.95028 3.75161 7.91502 4.01263C7.89016 4.23483 7.62762 4.38641 7.42276 4.29684C7.18491 4.18803 6.9141 4.20215 6.68438 4.33478Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M39.1186 7.31818H40.983L44.2244 15.233H44.3438L47.5852 7.31818H49.4496V17.5H47.9879V10.1321H47.8935L44.8906 17.4851H43.6776L40.6747 10.1271H40.5803V17.5H39.1186V7.31818ZM52.5034 20.3636C52.2813 20.3636 52.0791 20.3454 51.8968 20.3089C51.7145 20.2758 51.5787 20.2393 51.4892 20.1996L51.8471 18.9815C52.1189 19.0545 52.3609 19.0859 52.573 19.076C52.7851 19.0661 52.9724 18.9865 53.1348 18.8374C53.3005 18.6882 53.4463 18.4446 53.5723 18.1065L53.7562 17.5994L50.9622 9.86364H52.5531L54.487 15.7898H54.5666L56.5005 9.86364H58.0964L54.9494 18.5192C54.8036 18.9169 54.618 19.2533 54.3926 19.5284C54.1672 19.8068 53.8987 20.0156 53.5872 20.1548C53.2756 20.294 52.9144 20.3636 52.5034 20.3636ZM65.3114 17.5L62.4876 7.31818H64.1033L66.087 15.2031H66.1815L68.2447 7.31818H69.8455L71.9087 15.2081H72.0032L73.9819 7.31818H75.6026L72.7738 17.5H71.2276L69.0849 9.87358H69.0053L66.8626 17.5H65.3114ZM79.4233 17.6541C78.6709 17.6541 78.023 17.4934 77.4794 17.1719C76.9392 16.8471 76.5215 16.3913 76.2266 15.8047C75.9349 15.2147 75.7891 14.5237 75.7891 13.7315C75.7891 12.9493 75.9349 12.2599 76.2266 11.6634C76.5215 11.0668 76.9325 10.6011 77.4595 10.2663C77.9898 9.93158 78.6096 9.7642 79.3189 9.7642C79.7498 9.7642 80.1674 9.83546 80.5717 9.97798C80.9761 10.1205 81.339 10.3442 81.6605 10.6491C81.982 10.9541 82.2356 11.3501 82.4212 11.8374C82.6068 12.3213 82.6996 12.9096 82.6996 13.6023V14.1293H76.6293V13.0156H81.2429C81.2429 12.6245 81.1634 12.2782 81.0043 11.9766C80.8452 11.6716 80.6214 11.4313 80.3331 11.2557C80.0481 11.08 79.7133 10.9922 79.3288 10.9922C78.9112 10.9922 78.5466 11.0949 78.2351 11.3004C77.9268 11.5026 77.6882 11.7678 77.5192 12.0959C77.3535 12.4207 77.2706 12.7737 77.2706 13.1548V14.0249C77.2706 14.5353 77.3601 14.9695 77.5391 15.3274C77.7214 15.6854 77.9749 15.9588 78.2997 16.1477C78.6245 16.3333 79.004 16.4261 79.4382 16.4261C79.7199 16.4261 79.9768 16.3864 80.2088 16.3068C80.4408 16.224 80.6413 16.1013 80.8104 15.9389C80.9794 15.7765 81.1087 15.576 81.1982 15.3374L82.6051 15.5909C82.4924 16.0052 82.2902 16.3681 81.9986 16.6797C81.7102 16.9879 81.3473 17.2282 80.9098 17.4006C80.4756 17.5696 79.9801 17.6541 79.4233 17.6541ZM87.2076 17.6491C86.5911 17.6491 86.0409 17.4917 85.557 17.1768C85.0764 16.8587 84.6986 16.4062 84.4235 15.8196C84.1517 15.2296 84.0158 14.522 84.0158 13.6967C84.0158 12.8714 84.1533 12.1655 84.4284 11.5788C84.7069 10.9922 85.088 10.5431 85.5719 10.2315C86.0558 9.91998 86.6043 9.7642 87.2175 9.7642C87.6915 9.7642 88.0726 9.84375 88.361 10.0028C88.6526 10.1586 88.878 10.3409 89.0371 10.5497C89.1995 10.7585 89.3255 10.9425 89.415 11.1016H89.5044V7.31818H90.9909V17.5H89.5392V16.3118H89.415C89.3255 16.4742 89.1962 16.6598 89.0272 16.8686C88.8614 17.0774 88.6328 17.2597 88.3411 17.4155C88.0494 17.5713 87.6716 17.6491 87.2076 17.6491ZM87.5357 16.3814C87.9632 16.3814 88.3245 16.2687 88.6195 16.0433C88.9178 15.8146 89.1432 15.4981 89.2956 15.0938C89.4514 14.6894 89.5293 14.2187 89.5293 13.6818C89.5293 13.1515 89.4531 12.6875 89.3006 12.2898C89.1481 11.892 88.9244 11.5821 88.6294 11.3601C88.3345 11.138 87.9699 11.027 87.5357 11.027C87.0882 11.027 86.7154 11.143 86.4171 11.375C86.1188 11.607 85.8934 11.9235 85.7409 12.3246C85.5918 12.7256 85.5172 13.178 85.5172 13.6818C85.5172 14.1922 85.5935 14.6513 85.7459 15.0589C85.8984 15.4666 86.1238 15.7898 86.4221 16.0284C86.7237 16.2637 87.0949 16.3814 87.5357 16.3814ZM95.9712 17.6491C95.3548 17.6491 94.8046 17.4917 94.3207 17.1768C93.8401 16.8587 93.4622 16.4062 93.1871 15.8196C92.9154 15.2296 92.7795 14.522 92.7795 13.6967C92.7795 12.8714 92.917 12.1655 93.1921 11.5788C93.4705 10.9922 93.8517 10.5431 94.3356 10.2315C94.8195 9.91998 95.368 9.7642 95.9812 9.7642C96.4551 9.7642 96.8363 9.84375 97.1246 10.0028C97.4163 10.1586 97.6417 10.3409 97.8008 10.5497C97.9632 10.7585 98.0891 10.9425 98.1786 11.1016H98.2681V7.31818H99.7546V17.5H98.3029V16.3118H98.1786C98.0891 16.4742 97.9599 16.6598 97.7908 16.8686C97.6251 17.0774 97.3964 17.2597 97.1048 17.4155C96.8131 17.5713 96.4353 17.6491 95.9712 17.6491ZM96.2994 16.3814C96.7269 16.3814 97.0882 16.2687 97.3832 16.0433C97.6815 15.8146 97.9068 15.4981 98.0593 15.0938C98.2151 14.6894 98.293 14.2187 98.293 13.6818C98.293 13.1515 98.2167 12.6875 98.0643 12.2898C97.9118 11.892 97.6881 11.5821 97.3931 11.3601C97.0981 11.138 96.7335 11.027 96.2994 11.027C95.8519 11.027 95.479 11.143 95.1808 11.375C94.8825 11.607 94.6571 11.9235 94.5046 12.3246C94.3555 12.7256 94.2809 13.178 94.2809 13.6818C94.2809 14.1922 94.3571 14.6513 94.5096 15.0589C94.6621 15.4666 94.8874 15.7898 95.1857 16.0284C95.4873 16.2637 95.8585 16.3814 96.2994 16.3814ZM101.876 17.5V9.86364H103.363V17.5H101.876ZM102.627 8.68537C102.368 8.68537 102.146 8.59919 101.961 8.42685C101.778 8.25118 101.687 8.04238 101.687 7.80043C101.687 7.55516 101.778 7.34635 101.961 7.17401C102.146 6.99834 102.368 6.91051 102.627 6.91051C102.885 6.91051 103.106 6.99834 103.288 7.17401C103.474 7.34635 103.567 7.55516 103.567 7.80043C103.567 8.04238 103.474 8.25118 103.288 8.42685C103.106 8.59919 102.885 8.68537 102.627 8.68537ZM106.849 12.9659V17.5H105.363V9.86364H106.789V11.1065H106.884C107.06 10.7022 107.335 10.3774 107.709 10.1321C108.087 9.88684 108.563 9.7642 109.136 9.7642C109.656 9.7642 110.112 9.87358 110.503 10.0923C110.894 10.3078 111.198 10.6293 111.413 11.0568C111.628 11.4844 111.736 12.013 111.736 12.6428V17.5H110.25V12.8217C110.25 12.2682 110.105 11.8357 109.817 11.5241C109.529 11.2093 109.133 11.0518 108.629 11.0518C108.284 11.0518 107.978 11.1264 107.709 11.2756C107.444 11.4247 107.234 11.6435 107.078 11.9318C106.925 12.2169 106.849 12.5616 106.849 12.9659ZM116.931 20.5227C116.325 20.5227 115.803 20.4432 115.365 20.2841C114.931 20.125 114.577 19.9145 114.301 19.6527C114.026 19.3909 113.821 19.1042 113.685 18.7926L114.963 18.2656C115.052 18.4115 115.172 18.5656 115.321 18.728C115.473 18.8937 115.679 19.0346 115.937 19.1506C116.199 19.2666 116.535 19.3246 116.946 19.3246C117.51 19.3246 117.975 19.187 118.343 18.9119C118.711 18.6402 118.895 18.206 118.895 17.6094V16.108H118.801C118.711 16.2704 118.582 16.451 118.413 16.6499C118.247 16.8487 118.019 17.0211 117.727 17.1669C117.435 17.3127 117.056 17.3857 116.588 17.3857C115.985 17.3857 115.442 17.2448 114.958 16.9631C114.477 16.678 114.096 16.2588 113.814 15.7053C113.536 15.1484 113.397 14.464 113.397 13.652C113.397 12.84 113.534 12.1439 113.809 11.5639C114.088 10.9839 114.469 10.5398 114.953 10.2315C115.437 9.91998 115.985 9.7642 116.598 9.7642C117.072 9.7642 117.455 9.84375 117.747 10.0028C118.038 10.1586 118.266 10.3409 118.428 10.5497C118.594 10.7585 118.721 10.9425 118.811 11.1016H118.92V9.86364H120.377V17.669C120.377 18.3253 120.224 18.8639 119.919 19.2848C119.614 19.7057 119.202 20.0173 118.681 20.2195C118.164 20.4216 117.581 20.5227 116.931 20.5227ZM116.917 16.1527C117.344 16.1527 117.705 16.0533 118 15.8544C118.299 15.6522 118.524 15.3639 118.676 14.9893C118.832 14.6115 118.91 14.1591 118.91 13.6321C118.91 13.1184 118.834 12.666 118.681 12.2749C118.529 11.8838 118.305 11.5788 118.01 11.3601C117.715 11.138 117.351 11.027 116.917 11.027C116.469 11.027 116.096 11.143 115.798 11.375C115.5 11.6037 115.274 11.9152 115.122 12.3097C114.973 12.7041 114.898 13.1449 114.898 13.6321C114.898 14.1326 114.974 14.5717 115.127 14.9496C115.279 15.3274 115.505 15.6224 115.803 15.8345C116.105 16.0466 116.476 16.1527 116.917 16.1527ZM128.093 11.728L126.745 11.9666C126.689 11.7943 126.6 11.6302 126.477 11.4744C126.358 11.3187 126.195 11.1911 125.99 11.0916C125.784 10.9922 125.527 10.9425 125.219 10.9425C124.798 10.9425 124.447 11.0369 124.165 11.2259C123.883 11.4115 123.743 11.6518 123.743 11.9467C123.743 12.2019 123.837 12.4074 124.026 12.5632C124.215 12.719 124.52 12.8466 124.941 12.946L126.154 13.2244C126.856 13.3868 127.38 13.6371 127.725 13.9751C128.069 14.3132 128.242 14.7524 128.242 15.2926C128.242 15.75 128.109 16.1577 127.844 16.5156C127.582 16.8703 127.216 17.1487 126.745 17.3509C126.278 17.553 125.736 17.6541 125.12 17.6541C124.265 17.6541 123.567 17.4718 123.027 17.1072C122.486 16.7393 122.155 16.2173 122.032 15.5412L123.469 15.3224C123.559 15.697 123.743 15.9804 124.021 16.1726C124.299 16.3615 124.662 16.456 125.11 16.456C125.597 16.456 125.986 16.3549 126.278 16.1527C126.57 15.9472 126.716 15.697 126.716 15.402C126.716 15.1634 126.626 14.9628 126.447 14.8004C126.271 14.638 126.001 14.5154 125.637 14.4325L124.344 14.1491C123.632 13.9867 123.105 13.7282 122.763 13.3736C122.425 13.0189 122.256 12.5698 122.256 12.0263C122.256 11.5755 122.382 11.1811 122.634 10.843C122.886 10.505 123.234 10.2415 123.678 10.0526C124.122 9.86032 124.631 9.7642 125.204 9.7642C126.029 9.7642 126.679 9.94318 127.153 10.3011C127.627 10.6558 127.94 11.1314 128.093 11.728Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedActiveSvg: (\r\n        <svg\r\n          width=\"129\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 129 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M23.4413 21.058C22.9009 21.0564 22.3796 21.2572 21.9797 21.6207C21.5799 21.2572 21.0585 21.0564 20.5182 21.058C20.0128 21.0579 19.5228 21.2324 19.1313 21.552C18.7398 21.8717 18.4707 22.3167 18.3696 22.8119C18.3601 22.8599 18.36 22.9093 18.3695 22.9573C18.379 23.0053 18.3979 23.051 18.425 23.0917C18.4521 23.1324 18.487 23.1674 18.5277 23.1946C18.5684 23.2218 18.614 23.2408 18.662 23.2504C18.7099 23.26 18.7594 23.26 18.8074 23.2505C18.8554 23.241 18.901 23.2222 18.9417 23.195C18.9825 23.1679 19.0174 23.133 19.0447 23.0923C19.0719 23.0517 19.0908 23.0061 19.1004 22.9581C19.1673 22.6304 19.3445 22.3355 19.6025 22.1228C19.8606 21.91 20.1837 21.7921 20.5182 21.7888C20.7457 21.7897 20.9699 21.8436 21.1729 21.9464C21.3759 22.0491 21.5521 22.1979 21.6874 22.3808C21.7215 22.4261 21.7656 22.463 21.8163 22.4883C21.8671 22.5137 21.923 22.5269 21.9797 22.5269C22.0365 22.5269 22.0924 22.5137 22.1432 22.4883C22.1939 22.463 22.238 22.4261 22.2721 22.3808C22.4074 22.1979 22.5836 22.0491 22.7866 21.9464C22.9896 21.8436 23.2138 21.7897 23.4413 21.7888C23.7783 21.7887 24.1049 21.9051 24.3659 22.1182C24.6269 22.3312 24.8063 22.6279 24.8737 22.9581C24.8908 23.0419 24.9367 23.117 25.0034 23.1704C25.0702 23.2238 25.1536 23.2521 25.2391 23.2504H25.3121C25.3592 23.2408 25.4039 23.222 25.4436 23.1952C25.4834 23.1683 25.5175 23.1339 25.544 23.0939C25.5704 23.0538 25.5888 23.009 25.5979 22.9618C25.607 22.9147 25.6068 22.8663 25.5972 22.8192C25.4973 22.3215 25.2278 21.8738 24.8346 21.5526C24.4415 21.2314 23.949 21.0566 23.4413 21.058Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M24.903 18.8657H23.8068V17.8573C24.1487 17.5836 24.4246 17.2366 24.6144 16.842C24.8041 16.4473 24.9028 16.0151 24.903 15.5772V13.7502C24.9034 13.6588 24.8694 13.5705 24.8079 13.5028C24.7463 13.4352 24.6616 13.3931 24.5705 13.3848C24.2403 13.3532 23.9271 13.2231 23.6717 13.0114C23.4163 12.7996 23.2304 12.516 23.1382 12.1973C23.1161 12.1183 23.068 12.049 23.0017 12.0006C22.9354 11.9522 22.8548 11.9275 22.7728 11.9306H22.6668C22.603 11.9309 22.5405 11.948 22.4854 11.9801C22.4302 12.0121 22.3845 12.058 22.3526 12.1133C22.1285 12.4985 21.8076 12.8184 21.4218 13.0415C21.036 13.2645 20.5985 13.3829 20.1529 13.3848C19.9371 13.3834 19.7223 13.3552 19.5135 13.3008C19.4585 13.2866 19.401 13.2853 19.3455 13.2972C19.29 13.3091 19.238 13.3337 19.1937 13.3692C19.1493 13.4047 19.1139 13.45 19.0901 13.5015C19.0663 13.5531 19.0549 13.6094 19.0567 13.6662V15.5772C19.057 16.0151 19.1556 16.4473 19.3454 16.842C19.5351 17.2366 19.8111 17.5836 20.1529 17.8573V18.8657H19.0567C18.3784 18.8657 17.7278 19.1352 17.2481 19.6149C16.7684 20.0946 16.4989 20.7451 16.4989 21.4235C16.4989 20.7451 16.2295 20.0946 15.7498 19.6149C15.2701 19.1352 14.6195 18.8657 13.9412 18.8657H12.845V17.8573C13.1868 17.5836 13.4628 17.2366 13.6525 16.842C13.8423 16.4473 13.9409 16.0151 13.9412 15.5772V15.5407C14.3538 15.4564 14.7247 15.2322 14.9911 14.9059C15.2574 14.5797 15.4029 14.1714 15.4028 13.7502V11.1925C15.4028 10.9986 15.3258 10.8128 15.1887 10.6757C15.0517 10.5387 14.8658 10.4617 14.672 10.4617H8.46027C7.97572 10.4617 7.51103 10.6542 7.1684 10.9968C6.82578 11.3394 6.63329 11.8041 6.63329 12.2886V13.7502C6.63329 13.8471 6.67179 13.9401 6.74031 14.0086C6.80884 14.0771 6.90178 14.1156 6.99869 14.1156C7.0956 14.1156 7.18854 14.0771 7.25706 14.0086C7.32559 13.9401 7.36408 13.8471 7.36408 13.7502V12.2886C7.36408 11.9979 7.47957 11.7191 7.68515 11.5135C7.89072 11.3079 8.16954 11.1925 8.46027 11.1925H14.672V13.7502C14.6713 13.9764 14.6008 14.1968 14.4699 14.3813C14.3391 14.5658 14.1544 14.7052 13.9412 14.7806V13.0194C13.9413 12.9692 13.9311 12.9195 13.9111 12.8734C13.8912 12.8273 13.862 12.7859 13.8253 12.7516C13.7886 12.7173 13.7452 12.6909 13.6979 12.6742C13.6506 12.6574 13.6003 12.6505 13.5502 12.654L12.0886 12.76C12.0407 12.7634 11.9938 12.7761 11.9508 12.7976C11.9077 12.8191 11.8693 12.8488 11.8378 12.8851C11.8062 12.9214 11.7821 12.9636 11.7668 13.0092C11.7516 13.0548 11.7455 13.103 11.7488 13.151C11.7522 13.199 11.765 13.2458 11.7864 13.2889C11.8079 13.3319 11.8376 13.3703 11.8739 13.4019C11.9102 13.4334 11.9524 13.4575 11.998 13.4728C12.0436 13.488 12.0918 13.4942 12.1398 13.4908L13.2104 13.4104V15.5772C13.2104 16.1586 12.9794 16.7163 12.5683 17.1274C12.1571 17.5386 11.5995 17.7696 11.018 17.7696C10.4366 17.7696 9.87894 17.5386 9.46779 17.1274C9.05664 16.7163 8.82566 16.1586 8.82566 15.5772V13.7246L10.3128 13.6187C10.3608 13.6154 10.4078 13.6027 10.4509 13.5813C10.494 13.5599 10.5324 13.5301 10.564 13.4938C10.5956 13.4575 10.6196 13.4153 10.6349 13.3696C10.6501 13.3239 10.6561 13.2757 10.6526 13.2277C10.6452 13.1314 10.5998 13.0419 10.5266 12.9789C10.4533 12.9159 10.358 12.8845 10.2617 12.8915L8.43469 13.0194C8.34228 13.0259 8.25578 13.0673 8.1927 13.1351C8.12961 13.2029 8.09464 13.2922 8.09487 13.3848V15.5772C8.09515 16.0151 8.19379 16.4473 8.38353 16.842C8.57326 17.2366 8.84923 17.5836 9.19106 17.8573V18.8657H8.09487C7.41651 18.8657 6.76593 19.1352 6.28626 19.6149C5.80659 20.0946 5.53711 20.7451 5.53711 21.4235V22.8851C5.53711 22.982 5.57561 23.0749 5.64413 23.1435C5.71266 23.212 5.8056 23.2505 5.9025 23.2505C5.99941 23.2505 6.09235 23.212 6.16088 23.1435C6.2294 23.0749 6.2679 22.982 6.2679 22.8851V21.4235C6.27022 20.9824 6.43205 20.557 6.72353 20.2259C7.01501 19.8948 7.41645 19.6803 7.85371 19.6221L9.14721 23.0166C9.17371 23.0853 9.22033 23.1444 9.28096 23.1861C9.3416 23.2278 9.41342 23.2503 9.48703 23.2505C9.53215 23.2509 9.57688 23.2422 9.61857 23.2249C9.70852 23.1898 9.78092 23.1205 9.81995 23.0322C9.85897 22.9439 9.86145 22.8437 9.82684 22.7535L9.46145 21.7743C9.49223 21.7838 9.52424 21.7887 9.55645 21.7889C9.61365 21.7886 9.66997 21.7748 9.72088 21.7487L11.018 21.102L12.3152 21.7487C12.3661 21.7748 12.4224 21.7886 12.4796 21.7889C12.5135 21.7942 12.548 21.7942 12.5819 21.7889L12.2165 22.7682C12.1819 22.8583 12.1844 22.9585 12.2234 23.0468C12.2624 23.1351 12.3348 23.2044 12.4248 23.2395C12.4665 23.2568 12.5112 23.2655 12.5563 23.2651C12.6306 23.2656 12.7032 23.2435 12.7646 23.2017C12.8259 23.16 12.8731 23.1005 12.8998 23.0312L14.1897 19.6367C14.6231 19.6962 15.0209 19.9093 15.3105 20.2372C15.6002 20.565 15.7626 20.986 15.7682 21.4235V22.8851C15.7682 22.982 15.8067 23.0749 15.8752 23.1435C15.9437 23.212 16.0366 23.2505 16.1336 23.2505C16.2305 23.2505 16.3234 23.212 16.3919 23.1435C16.4605 23.0749 16.4989 22.982 16.4989 22.8851C16.4989 22.982 16.5374 23.0749 16.606 23.1435C16.6745 23.212 16.7674 23.2505 16.8643 23.2505C16.9613 23.2505 17.0542 23.212 17.1227 23.1435C17.1912 23.0749 17.2297 22.982 17.2297 22.8851V21.4235C17.2297 20.939 17.4222 20.4743 17.7648 20.1316C18.1075 19.789 18.5722 19.5965 19.0567 19.5965H20.5183C20.6152 19.5965 20.7081 19.558 20.7767 19.4895C20.8452 19.421 20.8837 19.328 20.8837 19.2311V18.2848C21.5862 18.5723 22.3735 18.5723 23.0761 18.2848V19.2311C23.0761 19.328 23.1145 19.421 23.1831 19.4895C23.2516 19.558 23.3445 19.5965 23.4414 19.5965H24.903C25.3876 19.5965 25.8523 19.789 26.1949 20.1316C26.5375 20.4743 26.73 20.939 26.73 21.4235V22.8851C26.73 22.982 26.7685 23.0749 26.837 23.1435C26.9055 23.212 26.9985 23.2505 27.0954 23.2505C27.1923 23.2505 27.2852 23.212 27.3538 23.1435C27.4223 23.0749 27.4608 22.982 27.4608 22.8851V21.4235C27.4608 20.7451 27.1913 20.0946 26.7116 19.6149C26.232 19.1352 25.5814 18.8657 24.903 18.8657ZM9.92184 20.8316V20.5539L10.1995 20.6927L9.92184 20.8316ZM12.1142 20.8316L11.8365 20.6927L12.1142 20.5539V20.8316ZM12.845 21.0837V19.9619C12.8448 19.8996 12.8287 19.8384 12.7982 19.7841C12.7677 19.7298 12.7238 19.6842 12.6708 19.6516C12.6177 19.619 12.5571 19.6006 12.4949 19.598C12.4327 19.5954 12.3708 19.6087 12.3152 19.6367L11.018 20.2835L9.72088 19.6367C9.66525 19.6087 9.60339 19.5954 9.54116 19.598C9.47892 19.6006 9.41839 19.619 9.36531 19.6516C9.31222 19.6842 9.26835 19.7298 9.23785 19.7841C9.20735 19.8384 9.19124 19.8996 9.19106 19.9619V21.0837L8.62469 19.5965H9.55645C9.65336 19.5965 9.7463 19.558 9.81482 19.4895C9.88335 19.421 9.92184 19.328 9.92184 19.2311V18.2848C10.6244 18.5723 11.4117 18.5723 12.1142 18.2848V18.8657H11.7488C11.6519 18.8657 11.559 18.9042 11.4904 18.9728C11.4219 19.0413 11.3834 19.1342 11.3834 19.2311C11.3834 19.328 11.4219 19.421 11.4904 19.4895C11.559 19.558 11.6519 19.5965 11.7488 19.5965H13.4114L12.845 21.0837ZM19.7875 15.5772V14.0937C20.3265 14.1555 20.8723 14.0819 21.3757 13.8797C21.8791 13.6775 22.3241 13.3529 22.6705 12.9354C22.8347 13.2093 23.0522 13.4475 23.3102 13.6358C23.5681 13.8241 23.8613 13.9587 24.1722 14.0316V15.5772C24.1722 16.1586 23.9413 16.7163 23.5301 17.1274C23.119 17.5386 22.5613 17.7696 21.9799 17.7696C21.3984 17.7696 20.8408 17.5386 20.4296 17.1274C20.0185 16.7163 19.7875 16.1586 19.7875 15.5772Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M17.2295 14.1156V17.0388C17.2295 17.1357 17.268 17.2286 17.3365 17.2971C17.405 17.3657 17.498 17.4042 17.5949 17.4042C17.6918 17.4042 17.7847 17.3657 17.8533 17.2971C17.9218 17.2286 17.9603 17.1357 17.9603 17.0388V14.1156C17.9603 13.0496 18.3837 12.0273 19.1375 11.2735C19.8913 10.5197 20.9136 10.0963 21.9796 10.0963C23.0456 10.0963 24.068 10.5197 24.8217 11.2735C25.5755 12.0273 25.999 13.0496 25.999 14.1156V17.0388C25.999 17.1357 26.0375 17.2286 26.106 17.2971C26.1745 17.3657 26.2674 17.4042 26.3644 17.4042C26.4613 17.4042 26.5542 17.3657 26.6227 17.2971C26.6913 17.2286 26.7298 17.1357 26.7298 17.0388V14.1156C26.7298 12.8558 26.2293 11.6476 25.3385 10.7568C24.4476 9.86594 23.2394 9.36548 21.9796 9.36548C20.7198 9.36548 19.5116 9.86594 18.6208 10.7568C17.73 11.6476 17.2295 12.8558 17.2295 14.1156Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M20.883 15.5772C21.0848 15.5772 21.2484 15.4136 21.2484 15.2118C21.2484 15.01 21.0848 14.8464 20.883 14.8464C20.6812 14.8464 20.5176 15.01 20.5176 15.2118C20.5176 15.4136 20.6812 15.5772 20.883 15.5772Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M23.0763 15.5772C23.2781 15.5772 23.4417 15.4136 23.4417 15.2118C23.4417 15.01 23.2781 14.8464 23.0763 14.8464C22.8745 14.8464 22.7109 15.01 22.7109 15.2118C22.7109 15.4136 22.8745 15.5772 23.0763 15.5772Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9.92204 15.2117C10.1238 15.2117 10.2874 15.0482 10.2874 14.8464C10.2874 14.6445 10.1238 14.481 9.92204 14.481C9.72023 14.481 9.55664 14.6445 9.55664 14.8464C9.55664 15.0482 9.72023 15.2117 9.92204 15.2117Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M12.1144 15.2117C12.3162 15.2117 12.4798 15.0482 12.4798 14.8464C12.4798 14.6445 12.3162 14.481 12.1144 14.481C11.9126 14.481 11.749 14.6445 11.749 14.8464C11.749 15.0482 11.9126 15.2117 12.1144 15.2117Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M16.5853 7.5C16.4621 7.5 16.3389 7.47946 16.2295 7.44525C15.3263 7.13736 13 5.7758 13 3.23053C13 1.99895 13.9989 1 15.2237 1C15.7232 1 16.2021 1.17105 16.5921 1.47211C16.9821 1.17105 17.4611 1 17.9605 1C19.1853 1 20.1842 1.99895 20.1842 3.23053C20.1842 5.7758 17.8511 7.13736 16.9547 7.44525C16.8316 7.47946 16.7084 7.5 16.5853 7.5ZM15.2168 2.02632C14.56 2.02632 14.0195 2.56685 14.0195 3.23053C14.0195 5.16685 15.901 6.2479 16.5579 6.47368C16.5647 6.47368 16.5921 6.47368 16.6126 6.47368C17.2763 6.24105 19.1442 5.16001 19.1442 3.23738C19.1442 2.5737 18.6105 2.03316 17.9468 2.03316C17.5705 2.03316 17.2216 2.21107 16.9889 2.51212C16.7974 2.77212 16.3595 2.77212 16.1679 2.51212C15.949 2.20423 15.6 2.02632 15.2168 2.02632Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M23.3979 6.98908C23.3155 6.96701 23.2368 6.93121 23.1698 6.88872C22.621 6.521 21.3093 5.19366 21.7654 3.4916C21.986 2.66802 22.8331 2.17899 23.6521 2.39845C23.9861 2.48794 24.2757 2.68815 24.4826 2.95935C24.7973 2.82791 25.1482 2.79934 25.4822 2.88884C26.3012 3.10829 26.7903 3.9553 26.5696 4.77888C26.1135 6.48094 24.3093 6.97338 23.6548 7.01867C23.5663 7.01948 23.4802 7.01115 23.3979 6.98908ZM23.4636 3.08353C23.0243 2.96584 22.566 3.23045 22.4471 3.67427C22.1001 4.96912 23.1647 6.02918 23.5635 6.29786C23.568 6.29909 23.5864 6.30399 23.6001 6.30767C24.0856 6.27103 25.5284 5.88281 25.8729 4.59711C25.9918 4.15329 25.7318 3.6962 25.2879 3.57728C25.0363 3.50985 24.7711 3.56629 24.5616 3.72593C24.3869 3.86547 24.094 3.78701 24.0125 3.57881C23.9213 3.33369 23.7198 3.15219 23.4636 3.08353Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M9.39956 7.14288C9.32572 7.18551 9.24478 7.21583 9.1673 7.23321C8.51923 7.36124 6.65316 7.35017 5.77211 5.82415C5.34579 5.08575 5.59892 4.14103 6.33322 3.71708C6.63269 3.54418 6.97905 3.48095 7.31709 3.52645C7.44671 3.21095 7.67465 2.94261 7.97411 2.76971C8.70841 2.34576 9.65313 2.5989 10.0794 3.3373C10.9605 4.86333 10.0329 6.4873 9.60213 6.98216C9.54014 7.0453 9.4734 7.10025 9.39956 7.14288ZM6.68438 4.33478C6.29057 4.56215 6.1536 5.07334 6.38334 5.47125C7.0536 6.63218 8.55592 6.62902 9.02789 6.53702C9.03199 6.53465 9.0484 6.52518 9.06071 6.51807C9.3781 6.14886 10.1238 4.85414 9.45827 3.70141C9.22853 3.3035 8.72145 3.16416 8.32354 3.39389C8.09791 3.52416 7.95028 3.75161 7.91502 4.01263C7.89016 4.23483 7.62762 4.38641 7.42276 4.29684C7.18491 4.18803 6.9141 4.20215 6.68438 4.33478Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M39.1186 7.31818H40.983L44.2244 15.233H44.3438L47.5852 7.31818H49.4496V17.5H47.9879V10.1321H47.8935L44.8906 17.4851H43.6776L40.6747 10.1271H40.5803V17.5H39.1186V7.31818ZM52.5034 20.3636C52.2813 20.3636 52.0791 20.3454 51.8968 20.3089C51.7145 20.2758 51.5787 20.2393 51.4892 20.1996L51.8471 18.9815C52.1189 19.0545 52.3609 19.0859 52.573 19.076C52.7851 19.0661 52.9724 18.9865 53.1348 18.8374C53.3005 18.6882 53.4463 18.4446 53.5723 18.1065L53.7562 17.5994L50.9622 9.86364H52.5531L54.487 15.7898H54.5666L56.5005 9.86364H58.0964L54.9494 18.5192C54.8036 18.9169 54.618 19.2533 54.3926 19.5284C54.1672 19.8068 53.8987 20.0156 53.5872 20.1548C53.2756 20.294 52.9144 20.3636 52.5034 20.3636ZM65.3114 17.5L62.4876 7.31818H64.1033L66.087 15.2031H66.1815L68.2447 7.31818H69.8455L71.9087 15.2081H72.0032L73.9819 7.31818H75.6026L72.7738 17.5H71.2276L69.0849 9.87358H69.0053L66.8626 17.5H65.3114ZM79.4233 17.6541C78.6709 17.6541 78.023 17.4934 77.4794 17.1719C76.9392 16.8471 76.5215 16.3913 76.2266 15.8047C75.9349 15.2147 75.7891 14.5237 75.7891 13.7315C75.7891 12.9493 75.9349 12.2599 76.2266 11.6634C76.5215 11.0668 76.9325 10.6011 77.4595 10.2663C77.9898 9.93158 78.6096 9.7642 79.3189 9.7642C79.7498 9.7642 80.1674 9.83546 80.5717 9.97798C80.9761 10.1205 81.339 10.3442 81.6605 10.6491C81.982 10.9541 82.2356 11.3501 82.4212 11.8374C82.6068 12.3213 82.6996 12.9096 82.6996 13.6023V14.1293H76.6293V13.0156H81.2429C81.2429 12.6245 81.1634 12.2782 81.0043 11.9766C80.8452 11.6716 80.6214 11.4313 80.3331 11.2557C80.0481 11.08 79.7133 10.9922 79.3288 10.9922C78.9112 10.9922 78.5466 11.0949 78.2351 11.3004C77.9268 11.5026 77.6882 11.7678 77.5192 12.0959C77.3535 12.4207 77.2706 12.7737 77.2706 13.1548V14.0249C77.2706 14.5353 77.3601 14.9695 77.5391 15.3274C77.7214 15.6854 77.9749 15.9588 78.2997 16.1477C78.6245 16.3333 79.004 16.4261 79.4382 16.4261C79.7199 16.4261 79.9768 16.3864 80.2088 16.3068C80.4408 16.224 80.6413 16.1013 80.8104 15.9389C80.9794 15.7765 81.1087 15.576 81.1982 15.3374L82.6051 15.5909C82.4924 16.0052 82.2902 16.3681 81.9986 16.6797C81.7102 16.9879 81.3473 17.2282 80.9098 17.4006C80.4756 17.5696 79.9801 17.6541 79.4233 17.6541ZM87.2076 17.6491C86.5911 17.6491 86.0409 17.4917 85.557 17.1768C85.0764 16.8587 84.6986 16.4062 84.4235 15.8196C84.1517 15.2296 84.0158 14.522 84.0158 13.6967C84.0158 12.8714 84.1533 12.1655 84.4284 11.5788C84.7069 10.9922 85.088 10.5431 85.5719 10.2315C86.0558 9.91998 86.6043 9.7642 87.2175 9.7642C87.6915 9.7642 88.0726 9.84375 88.361 10.0028C88.6526 10.1586 88.878 10.3409 89.0371 10.5497C89.1995 10.7585 89.3255 10.9425 89.415 11.1016H89.5044V7.31818H90.9909V17.5H89.5392V16.3118H89.415C89.3255 16.4742 89.1962 16.6598 89.0272 16.8686C88.8614 17.0774 88.6328 17.2597 88.3411 17.4155C88.0494 17.5713 87.6716 17.6491 87.2076 17.6491ZM87.5357 16.3814C87.9632 16.3814 88.3245 16.2687 88.6195 16.0433C88.9178 15.8146 89.1432 15.4981 89.2956 15.0938C89.4514 14.6894 89.5293 14.2187 89.5293 13.6818C89.5293 13.1515 89.4531 12.6875 89.3006 12.2898C89.1481 11.892 88.9244 11.5821 88.6294 11.3601C88.3345 11.138 87.9699 11.027 87.5357 11.027C87.0882 11.027 86.7154 11.143 86.4171 11.375C86.1188 11.607 85.8934 11.9235 85.7409 12.3246C85.5918 12.7256 85.5172 13.178 85.5172 13.6818C85.5172 14.1922 85.5935 14.6513 85.7459 15.0589C85.8984 15.4666 86.1238 15.7898 86.4221 16.0284C86.7237 16.2637 87.0949 16.3814 87.5357 16.3814ZM95.9712 17.6491C95.3548 17.6491 94.8046 17.4917 94.3207 17.1768C93.8401 16.8587 93.4622 16.4062 93.1871 15.8196C92.9154 15.2296 92.7795 14.522 92.7795 13.6967C92.7795 12.8714 92.917 12.1655 93.1921 11.5788C93.4705 10.9922 93.8517 10.5431 94.3356 10.2315C94.8195 9.91998 95.368 9.7642 95.9812 9.7642C96.4551 9.7642 96.8363 9.84375 97.1246 10.0028C97.4163 10.1586 97.6417 10.3409 97.8008 10.5497C97.9632 10.7585 98.0891 10.9425 98.1786 11.1016H98.2681V7.31818H99.7546V17.5H98.3029V16.3118H98.1786C98.0891 16.4742 97.9599 16.6598 97.7908 16.8686C97.6251 17.0774 97.3964 17.2597 97.1048 17.4155C96.8131 17.5713 96.4353 17.6491 95.9712 17.6491ZM96.2994 16.3814C96.7269 16.3814 97.0882 16.2687 97.3832 16.0433C97.6815 15.8146 97.9068 15.4981 98.0593 15.0938C98.2151 14.6894 98.293 14.2187 98.293 13.6818C98.293 13.1515 98.2167 12.6875 98.0643 12.2898C97.9118 11.892 97.6881 11.5821 97.3931 11.3601C97.0981 11.138 96.7335 11.027 96.2994 11.027C95.8519 11.027 95.479 11.143 95.1808 11.375C94.8825 11.607 94.6571 11.9235 94.5046 12.3246C94.3555 12.7256 94.2809 13.178 94.2809 13.6818C94.2809 14.1922 94.3571 14.6513 94.5096 15.0589C94.6621 15.4666 94.8874 15.7898 95.1857 16.0284C95.4873 16.2637 95.8585 16.3814 96.2994 16.3814ZM101.876 17.5V9.86364H103.363V17.5H101.876ZM102.627 8.68537C102.368 8.68537 102.146 8.59919 101.961 8.42685C101.778 8.25118 101.687 8.04238 101.687 7.80043C101.687 7.55516 101.778 7.34635 101.961 7.17401C102.146 6.99834 102.368 6.91051 102.627 6.91051C102.885 6.91051 103.106 6.99834 103.288 7.17401C103.474 7.34635 103.567 7.55516 103.567 7.80043C103.567 8.04238 103.474 8.25118 103.288 8.42685C103.106 8.59919 102.885 8.68537 102.627 8.68537ZM106.849 12.9659V17.5H105.363V9.86364H106.789V11.1065H106.884C107.06 10.7022 107.335 10.3774 107.709 10.1321C108.087 9.88684 108.563 9.7642 109.136 9.7642C109.656 9.7642 110.112 9.87358 110.503 10.0923C110.894 10.3078 111.198 10.6293 111.413 11.0568C111.628 11.4844 111.736 12.013 111.736 12.6428V17.5H110.25V12.8217C110.25 12.2682 110.105 11.8357 109.817 11.5241C109.529 11.2093 109.133 11.0518 108.629 11.0518C108.284 11.0518 107.978 11.1264 107.709 11.2756C107.444 11.4247 107.234 11.6435 107.078 11.9318C106.925 12.2169 106.849 12.5616 106.849 12.9659ZM116.931 20.5227C116.325 20.5227 115.803 20.4432 115.365 20.2841C114.931 20.125 114.577 19.9145 114.301 19.6527C114.026 19.3909 113.821 19.1042 113.685 18.7926L114.963 18.2656C115.052 18.4115 115.172 18.5656 115.321 18.728C115.473 18.8937 115.679 19.0346 115.937 19.1506C116.199 19.2666 116.535 19.3246 116.946 19.3246C117.51 19.3246 117.975 19.187 118.343 18.9119C118.711 18.6402 118.895 18.206 118.895 17.6094V16.108H118.801C118.711 16.2704 118.582 16.451 118.413 16.6499C118.247 16.8487 118.019 17.0211 117.727 17.1669C117.435 17.3127 117.056 17.3857 116.588 17.3857C115.985 17.3857 115.442 17.2448 114.958 16.9631C114.477 16.678 114.096 16.2588 113.814 15.7053C113.536 15.1484 113.397 14.464 113.397 13.652C113.397 12.84 113.534 12.1439 113.809 11.5639C114.088 10.9839 114.469 10.5398 114.953 10.2315C115.437 9.91998 115.985 9.7642 116.598 9.7642C117.072 9.7642 117.455 9.84375 117.747 10.0028C118.038 10.1586 118.266 10.3409 118.428 10.5497C118.594 10.7585 118.721 10.9425 118.811 11.1016H118.92V9.86364H120.377V17.669C120.377 18.3253 120.224 18.8639 119.919 19.2848C119.614 19.7057 119.202 20.0173 118.681 20.2195C118.164 20.4216 117.581 20.5227 116.931 20.5227ZM116.917 16.1527C117.344 16.1527 117.705 16.0533 118 15.8544C118.299 15.6522 118.524 15.3639 118.676 14.9893C118.832 14.6115 118.91 14.1591 118.91 13.6321C118.91 13.1184 118.834 12.666 118.681 12.2749C118.529 11.8838 118.305 11.5788 118.01 11.3601C117.715 11.138 117.351 11.027 116.917 11.027C116.469 11.027 116.096 11.143 115.798 11.375C115.5 11.6037 115.274 11.9152 115.122 12.3097C114.973 12.7041 114.898 13.1449 114.898 13.6321C114.898 14.1326 114.974 14.5717 115.127 14.9496C115.279 15.3274 115.505 15.6224 115.803 15.8345C116.105 16.0466 116.476 16.1527 116.917 16.1527ZM128.093 11.728L126.745 11.9666C126.689 11.7943 126.6 11.6302 126.477 11.4744C126.358 11.3187 126.195 11.1911 125.99 11.0916C125.784 10.9922 125.527 10.9425 125.219 10.9425C124.798 10.9425 124.447 11.0369 124.165 11.2259C123.883 11.4115 123.743 11.6518 123.743 11.9467C123.743 12.2019 123.837 12.4074 124.026 12.5632C124.215 12.719 124.52 12.8466 124.941 12.946L126.154 13.2244C126.856 13.3868 127.38 13.6371 127.725 13.9751C128.069 14.3132 128.242 14.7524 128.242 15.2926C128.242 15.75 128.109 16.1577 127.844 16.5156C127.582 16.8703 127.216 17.1487 126.745 17.3509C126.278 17.553 125.736 17.6541 125.12 17.6541C124.265 17.6541 123.567 17.4718 123.027 17.1072C122.486 16.7393 122.155 16.2173 122.032 15.5412L123.469 15.3224C123.559 15.697 123.743 15.9804 124.021 16.1726C124.299 16.3615 124.662 16.456 125.11 16.456C125.597 16.456 125.986 16.3549 126.278 16.1527C126.57 15.9472 126.716 15.697 126.716 15.402C126.716 15.1634 126.626 14.9628 126.447 14.8004C126.271 14.638 126.001 14.5154 125.637 14.4325L124.344 14.1491C123.632 13.9867 123.105 13.7282 122.763 13.3736C122.425 13.0189 122.256 12.5698 122.256 12.0263C122.256 11.5755 122.382 11.1811 122.634 10.843C122.886 10.505 123.234 10.2415 123.678 10.0526C124.122 9.86032 124.631 9.7642 125.204 9.7642C126.029 9.7642 126.679 9.94318 127.153 10.3011C127.627 10.6558 127.94 11.1314 128.093 11.728Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      hasOwnBackground: true,\r\n    },\r\n    {\r\n      id: \"Wedzat Live\",\r\n      label: \"Wedzat Live\",\r\n      defaultSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"url(#paint0_linear_261_24994)\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M21.1486 24.8459C20.9946 24.8459 20.8407 24.8203 20.7038 24.7775C19.5749 24.3926 16.667 22.6907 16.667 19.5091C16.667 17.9696 17.9157 16.7209 19.4466 16.7209C20.0709 16.7209 20.6696 16.9348 21.1571 17.3111C21.6446 16.9348 22.2433 16.7209 22.8676 16.7209C24.3986 16.7209 25.6473 17.9696 25.6473 19.5091C25.6473 22.6907 22.7308 24.3926 21.6104 24.7775C21.4565 24.8203 21.3025 24.8459 21.1486 24.8459ZM19.438 18.0038C18.617 18.0038 17.9413 18.6795 17.9413 19.5091C17.9413 21.9295 20.2933 23.2808 21.1143 23.5631C21.1229 23.5631 21.1571 23.5631 21.1828 23.5631C22.0124 23.2723 24.3472 21.921 24.3472 19.5177C24.3472 18.6881 23.6801 18.0124 22.8505 18.0124C22.3801 18.0124 21.944 18.2348 21.6532 18.6111C21.4137 18.9361 20.8663 18.9361 20.6269 18.6111C20.3532 18.2262 19.917 18.0038 19.438 18.0038Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.7503 28.3126C30.577 28.3126 30.4145 28.2584 30.2628 28.1501C29.9053 27.8792 29.8295 27.37 30.1003 27.0125C31.4111 25.2684 32.1045 23.1884 32.1045 21.0001C32.1045 18.8117 31.4111 16.7317 30.1003 14.9876C29.8295 14.6301 29.9053 14.1209 30.2628 13.8501C30.6203 13.5792 31.1295 13.655 31.4003 14.0125C32.9278 16.0384 33.7295 18.4542 33.7295 21.0001C33.7295 23.5459 32.9278 25.9617 31.4003 27.9876C31.2378 28.2042 30.9995 28.3126 30.7503 28.3126Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M11.2497 28.3126C11.0005 28.3126 10.7622 28.2042 10.5997 27.9876C9.07217 25.9617 8.27051 23.5459 8.27051 21.0001C8.27051 18.4542 9.07217 16.0384 10.5997 14.0125C10.8705 13.655 11.3797 13.5792 11.7372 13.8501C12.0947 14.1209 12.1705 14.6301 11.8997 14.9876C10.5888 16.7317 9.89551 18.8117 9.89551 21.0001C9.89551 23.1884 10.5888 25.2684 11.8997 27.0125C12.1705 27.37 12.0947 27.8792 11.7372 28.1501C11.5964 28.2584 11.423 28.3126 11.2497 28.3126Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M27.2835 25.7124C27.1101 25.7124 26.9477 25.6583 26.796 25.5499C26.4385 25.2791 26.3627 24.7699 26.6335 24.4124C27.381 23.4266 27.771 22.2457 27.771 20.9999C27.771 19.7541 27.381 18.5732 26.6335 17.5874C26.3627 17.2299 26.4385 16.7207 26.796 16.4499C27.1535 16.1791 27.6626 16.2549 27.9335 16.6124C28.8868 17.8908 29.396 19.4074 29.396 20.9999C29.396 22.5924 28.8868 24.1199 27.9335 25.3874C27.771 25.6041 27.5326 25.7124 27.2835 25.7124Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.717 25.7124C14.4678 25.7124 14.2295 25.6041 14.067 25.3874C13.1137 24.1091 12.6045 22.5924 12.6045 20.9999C12.6045 19.4074 13.1137 17.8799 14.067 16.6124C14.3378 16.2549 14.847 16.1791 15.2045 16.4499C15.562 16.7207 15.6378 17.2299 15.367 17.5874C14.6195 18.5732 14.2295 19.7541 14.2295 20.9999C14.2295 22.2457 14.6195 23.4266 15.367 24.4124C15.6378 24.7699 15.562 25.2791 15.2045 25.5499C15.0637 25.6583 14.8903 25.7124 14.717 25.7124Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_261_24994\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      activeSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"#B31B1E\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <path\r\n            d=\"M21.1486 24.8459C20.9946 24.8459 20.8407 24.8203 20.7038 24.7775C19.5749 24.3926 16.667 22.6907 16.667 19.5091C16.667 17.9696 17.9157 16.7209 19.4466 16.7209C20.0709 16.7209 20.6696 16.9348 21.1571 17.3111C21.6446 16.9348 22.2433 16.7209 22.8676 16.7209C24.3986 16.7209 25.6473 17.9696 25.6473 19.5091C25.6473 22.6907 22.7308 24.3926 21.6104 24.7775C21.4565 24.8203 21.3025 24.8459 21.1486 24.8459ZM19.438 18.0038C18.617 18.0038 17.9413 18.6795 17.9413 19.5091C17.9413 21.9295 20.2933 23.2808 21.1143 23.5631C21.1229 23.5631 21.1571 23.5631 21.1828 23.5631C22.0124 23.2723 24.3472 21.921 24.3472 19.5177C24.3472 18.6881 23.6801 18.0124 22.8505 18.0124C22.3801 18.0124 21.944 18.2348 21.6532 18.6111C21.4137 18.9361 20.8663 18.9361 20.6269 18.6111C20.3532 18.2262 19.917 18.0038 19.438 18.0038Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M30.7503 28.3126C30.577 28.3126 30.4145 28.2584 30.2628 28.1501C29.9053 27.8792 29.8295 27.37 30.1003 27.0125C31.4111 25.2684 32.1045 23.1884 32.1045 21.0001C32.1045 18.8117 31.4111 16.7317 30.1003 14.9876C29.8295 14.6301 29.9053 14.1209 30.2628 13.8501C30.6203 13.5792 31.1295 13.655 31.4003 14.0125C32.9278 16.0384 33.7295 18.4542 33.7295 21.0001C33.7295 23.5459 32.9278 25.9617 31.4003 27.9876C31.2378 28.2042 30.9995 28.3126 30.7503 28.3126Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M11.2497 28.3126C11.0005 28.3126 10.7622 28.2042 10.5997 27.9876C9.07217 25.9617 8.27051 23.5459 8.27051 21.0001C8.27051 18.4542 9.07217 16.0384 10.5997 14.0125C10.8705 13.655 11.3797 13.5792 11.7372 13.8501C12.0947 14.1209 12.1705 14.6301 11.8997 14.9876C10.5888 16.7317 9.89551 18.8117 9.89551 21.0001C9.89551 23.1884 10.5888 25.2684 11.8997 27.0125C12.1705 27.37 12.0947 27.8792 11.7372 28.1501C11.5964 28.2584 11.423 28.3126 11.2497 28.3126Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M27.2835 25.7124C27.1101 25.7124 26.9477 25.6583 26.796 25.5499C26.4385 25.2791 26.3627 24.7699 26.6335 24.4124C27.381 23.4266 27.771 22.2457 27.771 20.9999C27.771 19.7541 27.381 18.5732 26.6335 17.5874C26.3627 17.2299 26.4385 16.7207 26.796 16.4499C27.1535 16.1791 27.6626 16.2549 27.9335 16.6124C28.8868 17.8908 29.396 19.4074 29.396 20.9999C29.396 22.5924 28.8868 24.1199 27.9335 25.3874C27.771 25.6041 27.5326 25.7124 27.2835 25.7124Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M14.717 25.7124C14.4678 25.7124 14.2295 25.6041 14.067 25.3874C13.1137 24.1091 12.6045 22.5924 12.6045 20.9999C12.6045 19.4074 13.1137 17.8799 14.067 16.6124C14.3378 16.2549 14.847 16.1791 15.2045 16.4499C15.562 16.7207 15.6378 17.2299 15.367 17.5874C14.6195 18.5732 14.2295 19.7541 14.2295 20.9999C14.2295 22.2457 14.6195 23.4266 15.367 24.4124C15.6378 24.7699 15.562 25.2791 15.2045 25.5499C15.0637 25.6583 14.8903 25.7124 14.717 25.7124Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_261_24994\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      expandedDefaultSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M15.9995 13.74C15.2195 13.74 14.4795 13.44 13.9095 12.88C13.1795 12.15 12.8895 11.1 13.1295 10.07C13.3795 9.02001 14.2195 8.17999 15.2695 7.92999C16.2895 7.67999 17.3395 7.97002 18.0795 8.71002C18.8195 9.45002 19.0995 10.49 18.8595 11.52C18.6095 12.57 17.7695 13.41 16.7195 13.66C16.4795 13.71 16.2395 13.74 15.9995 13.74ZM15.9995 9.34003C15.8795 9.34003 15.7495 9.36001 15.6295 9.39001C15.1295 9.51001 14.7195 9.91998 14.5995 10.42C14.4695 10.94 14.6095 11.45 14.9795 11.81C15.3495 12.18 15.8495 12.31 16.3795 12.19C16.8795 12.07 17.2895 11.66 17.4095 11.16C17.5395 10.64 17.3995 10.13 17.0295 9.77002C16.7495 9.49002 16.3895 9.34003 15.9995 9.34003Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M10.02 17.97C9.83996 17.97 9.64995 17.9 9.50995 17.77C7.56995 15.97 6.45996 13.43 6.45996 10.79C6.45996 5.52998 10.74 1.25 16 1.25C21.26 1.25 25.5399 5.52998 25.5399 10.79C25.5399 13.45 24.47 15.91 22.53 17.74C22.23 18.02 21.75 18.01 21.47 17.71C21.19 17.41 21.2 16.93 21.5 16.65C23.14 15.11 24.0399 13.04 24.0399 10.8C24.0399 6.36999 20.43 2.76001 16 2.76001C11.57 2.76001 7.95996 6.36999 7.95996 10.8C7.95996 13.06 8.86995 15.14 10.53 16.68C10.83 16.96 10.85 17.44 10.57 17.74C10.42 17.89 10.22 17.97 10.02 17.97Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M19.9995 15.3C19.8195 15.3 19.6295 15.23 19.4895 15.1C19.1895 14.82 19.1695 14.34 19.4595 14.04C20.2895 13.16 20.7495 12 20.7495 10.8C20.7495 8.18005 18.6195 6.06006 16.0095 6.06006C13.3995 6.06006 11.2695 8.19005 11.2695 10.8C11.2695 12.01 11.7295 13.16 12.5595 14.04C12.8395 14.34 12.8295 14.82 12.5295 15.1C12.2295 15.38 11.7495 15.3701 11.4695 15.0701C10.3795 13.9101 9.76953 12.39 9.76953 10.8C9.76953 7.36005 12.5695 4.56006 16.0095 4.56006C19.4495 4.56006 22.2495 7.36005 22.2495 10.8C22.2495 12.39 21.6495 13.9101 20.5495 15.0701C20.3995 15.2201 20.1995 15.3 19.9995 15.3Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M16.1368 23.5C15.9947 23.5 15.8526 23.4763 15.7263 23.4368C14.6842 23.0816 12 21.5105 12 18.5737C12 17.1526 13.1526 16 14.5658 16C15.1421 16 15.6947 16.1974 16.1447 16.5447C16.5947 16.1974 17.1474 16 17.7237 16C19.1368 16 20.2895 17.1526 20.2895 18.5737C20.2895 21.5105 17.5974 23.0816 16.5632 23.4368C16.4211 23.4763 16.2789 23.5 16.1368 23.5ZM14.5579 17.1842C13.8 17.1842 13.1763 17.8079 13.1763 18.5737C13.1763 20.8079 15.3474 22.0553 16.1052 22.3158C16.1131 22.3158 16.1447 22.3158 16.1684 22.3158C16.9342 22.0474 19.0895 20.8 19.0895 18.5816C19.0895 17.8158 18.4737 17.1921 17.7079 17.1921C17.2737 17.1921 16.871 17.3974 16.6026 17.7448C16.3816 18.0448 15.8763 18.0448 15.6553 17.7448C15.4026 17.3895 15 17.1842 14.5579 17.1842Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M41.1122 17.5L38.2884 7.31818H39.9041L41.8878 15.2031H41.9822L44.0455 7.31818H45.6463L47.7095 15.2081H47.804L49.7827 7.31818H51.4034L48.5746 17.5H47.0284L44.8857 9.87358H44.8061L42.6634 17.5H41.1122ZM55.2241 17.6541C54.4717 17.6541 53.8237 17.4934 53.2802 17.1719C52.7399 16.8471 52.3223 16.3913 52.0273 15.8047C51.7357 15.2147 51.5898 14.5237 51.5898 13.7315C51.5898 12.9493 51.7357 12.2599 52.0273 11.6634C52.3223 11.0668 52.7333 10.6011 53.2603 10.2663C53.7906 9.93158 54.4104 9.7642 55.1197 9.7642C55.5505 9.7642 55.9682 9.83546 56.3725 9.97798C56.7769 10.1205 57.1398 10.3442 57.4613 10.6491C57.7828 10.9541 58.0363 11.3501 58.2219 11.8374C58.4076 12.3213 58.5004 12.9096 58.5004 13.6023V14.1293H52.43V13.0156H57.0437C57.0437 12.6245 56.9641 12.2782 56.805 11.9766C56.646 11.6716 56.4222 11.4313 56.1339 11.2557C55.8488 11.08 55.5141 10.9922 55.1296 10.9922C54.712 10.9922 54.3474 11.0949 54.0359 11.3004C53.7276 11.5026 53.489 11.7678 53.32 12.0959C53.1542 12.4207 53.0714 12.7737 53.0714 13.1548V14.0249C53.0714 14.5353 53.1609 14.9695 53.3398 15.3274C53.5221 15.6854 53.7757 15.9588 54.1005 16.1477C54.4253 16.3333 54.8048 16.4261 55.239 16.4261C55.5207 16.4261 55.7776 16.3864 56.0096 16.3068C56.2416 16.224 56.4421 16.1013 56.6112 15.9389C56.7802 15.7765 56.9094 15.576 56.9989 15.3374L58.4059 15.5909C58.2932 16.0052 58.091 16.3681 57.7994 16.6797C57.511 16.9879 57.1481 17.2282 56.7106 17.4006C56.2764 17.5696 55.7809 17.6541 55.2241 17.6541ZM63.0083 17.6491C62.3919 17.6491 61.8417 17.4917 61.3578 17.1768C60.8772 16.8587 60.4993 16.4062 60.2243 15.8196C59.9525 15.2296 59.8166 14.522 59.8166 13.6967C59.8166 12.8714 59.9541 12.1655 60.2292 11.5788C60.5076 10.9922 60.8888 10.5431 61.3727 10.2315C61.8566 9.91998 62.4051 9.7642 63.0183 9.7642C63.4922 9.7642 63.8734 9.84375 64.1618 10.0028C64.4534 10.1586 64.6788 10.3409 64.8379 10.5497C65.0003 10.7585 65.1262 10.9425 65.2157 11.1016H65.3052V7.31818H66.7917V17.5H65.34V16.3118H65.2157C65.1262 16.4742 64.997 16.6598 64.8279 16.8686C64.6622 17.0774 64.4335 17.2597 64.1419 17.4155C63.8502 17.5713 63.4724 17.6491 63.0083 17.6491ZM63.3365 16.3814C63.764 16.3814 64.1253 16.2687 64.4203 16.0433C64.7186 15.8146 64.944 15.4981 65.0964 15.0938C65.2522 14.6894 65.3301 14.2187 65.3301 13.6818C65.3301 13.1515 65.2538 12.6875 65.1014 12.2898C64.9489 11.892 64.7252 11.5821 64.4302 11.3601C64.1352 11.138 63.7707 11.027 63.3365 11.027C62.889 11.027 62.5162 11.143 62.2179 11.375C61.9196 11.607 61.6942 11.9235 61.5417 12.3246C61.3926 12.7256 61.318 13.178 61.318 13.6818C61.318 14.1922 61.3942 14.6513 61.5467 15.0589C61.6992 15.4666 61.9245 15.7898 62.2228 16.0284C62.5244 16.2637 62.8957 16.3814 63.3365 16.3814ZM68.7543 17.5V16.4808L72.8906 11.2259V11.1562H68.8885V9.86364H74.745V10.9474L70.7678 16.1378V16.2074H74.8842V17.5H68.7543ZM78.8603 17.669C78.3764 17.669 77.9389 17.5795 77.5478 17.4006C77.1567 17.2183 76.8468 16.9548 76.6181 16.6101C76.3927 16.2654 76.28 15.8428 76.28 15.3423C76.28 14.9115 76.3629 14.5568 76.5286 14.2784C76.6943 14 76.918 13.7796 77.1998 13.6172C77.4815 13.4548 77.7963 13.3321 78.1444 13.2493C78.4924 13.1664 78.847 13.1035 79.2083 13.0604C79.6657 13.0073 80.0369 12.9643 80.3219 12.9311C80.6069 12.8946 80.8141 12.8366 80.9434 12.7571C81.0726 12.6776 81.1373 12.5483 81.1373 12.3693V12.3345C81.1373 11.9003 81.0146 11.5639 80.7694 11.3253C80.5274 11.0866 80.1661 10.9673 79.6855 10.9673C79.1851 10.9673 78.7907 11.0784 78.5023 11.3004C78.2173 11.5192 78.0201 11.7628 77.9107 12.0312L76.5137 11.7131C76.6794 11.2491 76.9213 10.8745 77.2395 10.5895C77.561 10.3011 77.9306 10.0923 78.3482 9.96307C78.7658 9.83049 79.205 9.7642 79.6657 9.7642C79.9706 9.7642 80.2937 9.80066 80.6351 9.87358C80.9798 9.94318 81.3013 10.0724 81.5996 10.2614C81.9012 10.4503 82.1481 10.7204 82.3404 11.0717C82.5326 11.4197 82.6287 11.8722 82.6287 12.429V17.5H81.177V16.456H81.1174C81.0212 16.6482 80.8771 16.8371 80.6848 17.0227C80.4926 17.2083 80.2457 17.3625 79.9441 17.4851C79.6425 17.6077 79.2812 17.669 78.8603 17.669ZM79.1834 16.4759C79.5944 16.4759 79.9457 16.3946 80.2374 16.2322C80.5324 16.0698 80.7561 15.8577 80.9086 15.5959C81.0643 15.3307 81.1422 15.0473 81.1422 14.7457V13.7614C81.0892 13.8144 80.9864 13.8641 80.834 13.9105C80.6848 13.9536 80.5141 13.9917 80.3219 14.0249C80.1297 14.0547 79.9424 14.0829 79.7601 14.1094C79.5778 14.1326 79.4254 14.1525 79.3027 14.169C79.0144 14.2055 78.7509 14.2668 78.5123 14.353C78.2769 14.4392 78.088 14.5634 77.9455 14.7259C77.8063 14.8849 77.7367 15.0971 77.7367 15.3622C77.7367 15.7301 77.8726 16.0085 78.1444 16.1974C78.4161 16.383 78.7625 16.4759 79.1834 16.4759ZM88.1733 9.86364V11.0568H84.0021V9.86364H88.1733ZM85.1207 8.03409H86.6072V15.2578C86.6072 15.5462 86.6503 15.7633 86.7365 15.9091C86.8227 16.0516 86.9337 16.1494 87.0696 16.2024C87.2088 16.2521 87.3596 16.277 87.522 16.277C87.6413 16.277 87.7457 16.2687 87.8352 16.2521C87.9247 16.2356 87.9943 16.2223 88.044 16.2124L88.3125 17.4403C88.2263 17.4735 88.1037 17.5066 87.9446 17.5398C87.7855 17.5762 87.5866 17.5961 87.348 17.5994C86.9569 17.6061 86.5923 17.5365 86.2543 17.3906C85.9162 17.2448 85.6428 17.0194 85.4339 16.7145C85.2251 16.4096 85.1207 16.0268 85.1207 15.5661V8.03409ZM93.642 17.5V7.31818H95.1783V16.1776H99.7919V17.5H93.642ZM101.425 17.5V9.86364H102.912V17.5H101.425ZM102.176 8.68537C101.917 8.68537 101.695 8.59919 101.51 8.42685C101.327 8.25118 101.236 8.04238 101.236 7.80043C101.236 7.55516 101.327 7.34635 101.51 7.17401C101.695 6.99834 101.917 6.91051 102.176 6.91051C102.434 6.91051 102.655 6.99834 102.837 7.17401C103.023 7.34635 103.115 7.55516 103.115 7.80043C103.115 8.04238 103.023 8.25118 102.837 8.42685C102.655 8.59919 102.434 8.68537 102.176 8.68537ZM111.444 9.86364L108.675 17.5H107.084L104.31 9.86364H105.906L107.84 15.7401H107.919L109.848 9.86364H111.444ZM115.873 17.6541C115.12 17.6541 114.472 17.4934 113.929 17.1719C113.388 16.8471 112.971 16.3913 112.676 15.8047C112.384 15.2147 112.238 14.5237 112.238 13.7315C112.238 12.9493 112.384 12.2599 112.676 11.6634C112.971 11.0668 113.382 10.6011 113.909 10.2663C114.439 9.93158 115.059 9.7642 115.768 9.7642C116.199 9.7642 116.617 9.83546 117.021 9.97798C117.425 10.1205 117.788 10.3442 118.11 10.6491C118.431 10.9541 118.685 11.3501 118.87 11.8374C119.056 12.3213 119.149 12.9096 119.149 13.6023V14.1293H113.078V13.0156H117.692C117.692 12.6245 117.613 12.2782 117.453 11.9766C117.294 11.6716 117.071 11.4313 116.782 11.2557C116.497 11.08 116.163 10.9922 115.778 10.9922C115.36 10.9922 114.996 11.0949 114.684 11.3004C114.376 11.5026 114.137 11.7678 113.968 12.0959C113.803 12.4207 113.72 12.7737 113.72 13.1548V14.0249C113.72 14.5353 113.809 14.9695 113.988 15.3274C114.171 15.6854 114.424 15.9588 114.749 16.1477C115.074 16.3333 115.453 16.4261 115.887 16.4261C116.169 16.4261 116.426 16.3864 116.658 16.3068C116.89 16.224 117.091 16.1013 117.26 15.9389C117.429 15.7765 117.558 15.576 117.647 15.3374L119.054 15.5909C118.942 16.0052 118.739 16.3681 118.448 16.6797C118.159 16.9879 117.797 17.2282 117.359 17.4006C116.925 17.5696 116.429 17.6541 115.873 17.6541Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      expandedActiveSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M15.9995 13.74C15.2195 13.74 14.4795 13.44 13.9095 12.88C13.1795 12.15 12.8895 11.1 13.1295 10.07C13.3795 9.02001 14.2195 8.17999 15.2695 7.92999C16.2895 7.67999 17.3395 7.97002 18.0795 8.71002C18.8195 9.45002 19.0995 10.49 18.8595 11.52C18.6095 12.57 17.7695 13.41 16.7195 13.66C16.4795 13.71 16.2395 13.74 15.9995 13.74ZM15.9995 9.34003C15.8795 9.34003 15.7495 9.36001 15.6295 9.39001C15.1295 9.51001 14.7195 9.91998 14.5995 10.42C14.4695 10.94 14.6095 11.45 14.9795 11.81C15.3495 12.18 15.8495 12.31 16.3795 12.19C16.8795 12.07 17.2895 11.66 17.4095 11.16C17.5395 10.64 17.3995 10.13 17.0295 9.77002C16.7495 9.49002 16.3895 9.34003 15.9995 9.34003Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M10.02 17.97C9.83996 17.97 9.64995 17.9 9.50995 17.77C7.56995 15.97 6.45996 13.43 6.45996 10.79C6.45996 5.52998 10.74 1.25 16 1.25C21.26 1.25 25.5399 5.52998 25.5399 10.79C25.5399 13.45 24.47 15.91 22.53 17.74C22.23 18.02 21.75 18.01 21.47 17.71C21.19 17.41 21.2 16.93 21.5 16.65C23.14 15.11 24.0399 13.04 24.0399 10.8C24.0399 6.36999 20.43 2.76001 16 2.76001C11.57 2.76001 7.95996 6.36999 7.95996 10.8C7.95996 13.06 8.86995 15.14 10.53 16.68C10.83 16.96 10.85 17.44 10.57 17.74C10.42 17.89 10.22 17.97 10.02 17.97Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M19.9995 15.3C19.8195 15.3 19.6295 15.23 19.4895 15.1C19.1895 14.82 19.1695 14.34 19.4595 14.04C20.2895 13.16 20.7495 12 20.7495 10.8C20.7495 8.18005 18.6195 6.06006 16.0095 6.06006C13.3995 6.06006 11.2695 8.19005 11.2695 10.8C11.2695 12.01 11.7295 13.16 12.5595 14.04C12.8395 14.34 12.8295 14.82 12.5295 15.1C12.2295 15.38 11.7495 15.3701 11.4695 15.0701C10.3795 13.9101 9.76953 12.39 9.76953 10.8C9.76953 7.36005 12.5695 4.56006 16.0095 4.56006C19.4495 4.56006 22.2495 7.36005 22.2495 10.8C22.2495 12.39 21.6495 13.9101 20.5495 15.0701C20.3995 15.2201 20.1995 15.3 19.9995 15.3Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M16.1368 23.5C15.9947 23.5 15.8526 23.4763 15.7263 23.4368C14.6842 23.0816 12 21.5105 12 18.5737C12 17.1526 13.1526 16 14.5658 16C15.1421 16 15.6947 16.1974 16.1447 16.5447C16.5947 16.1974 17.1474 16 17.7237 16C19.1368 16 20.2895 17.1526 20.2895 18.5737C20.2895 21.5105 17.5974 23.0816 16.5632 23.4368C16.4211 23.4763 16.2789 23.5 16.1368 23.5ZM14.5579 17.1842C13.8 17.1842 13.1763 17.8079 13.1763 18.5737C13.1763 20.8079 15.3474 22.0553 16.1052 22.3158C16.1131 22.3158 16.1447 22.3158 16.1684 22.3158C16.9342 22.0474 19.0895 20.8 19.0895 18.5816C19.0895 17.8158 18.4737 17.1921 17.7079 17.1921C17.2737 17.1921 16.871 17.3974 16.6026 17.7448C16.3816 18.0448 15.8763 18.0448 15.6553 17.7448C15.4026 17.3895 15 17.1842 14.5579 17.1842Z\"\r\n            fill=\"#292D32\"\r\n          />\r\n          <path\r\n            d=\"M41.1122 17.5L38.2884 7.31818H39.9041L41.8878 15.2031H41.9822L44.0455 7.31818H45.6463L47.7095 15.2081H47.804L49.7827 7.31818H51.4034L48.5746 17.5H47.0284L44.8857 9.87358H44.8061L42.6634 17.5H41.1122ZM55.2241 17.6541C54.4717 17.6541 53.8237 17.4934 53.2802 17.1719C52.7399 16.8471 52.3223 16.3913 52.0273 15.8047C51.7357 15.2147 51.5898 14.5237 51.5898 13.7315C51.5898 12.9493 51.7357 12.2599 52.0273 11.6634C52.3223 11.0668 52.7333 10.6011 53.2603 10.2663C53.7906 9.93158 54.4104 9.7642 55.1197 9.7642C55.5505 9.7642 55.9682 9.83546 56.3725 9.97798C56.7769 10.1205 57.1398 10.3442 57.4613 10.6491C57.7828 10.9541 58.0363 11.3501 58.2219 11.8374C58.4076 12.3213 58.5004 12.9096 58.5004 13.6023V14.1293H52.43V13.0156H57.0437C57.0437 12.6245 56.9641 12.2782 56.805 11.9766C56.646 11.6716 56.4222 11.4313 56.1339 11.2557C55.8488 11.08 55.5141 10.9922 55.1296 10.9922C54.712 10.9922 54.3474 11.0949 54.0359 11.3004C53.7276 11.5026 53.489 11.7678 53.32 12.0959C53.1542 12.4207 53.0714 12.7737 53.0714 13.1548V14.0249C53.0714 14.5353 53.1609 14.9695 53.3398 15.3274C53.5221 15.6854 53.7757 15.9588 54.1005 16.1477C54.4253 16.3333 54.8048 16.4261 55.239 16.4261C55.5207 16.4261 55.7776 16.3864 56.0096 16.3068C56.2416 16.224 56.4421 16.1013 56.6112 15.9389C56.7802 15.7765 56.9094 15.576 56.9989 15.3374L58.4059 15.5909C58.2932 16.0052 58.091 16.3681 57.7994 16.6797C57.511 16.9879 57.1481 17.2282 56.7106 17.4006C56.2764 17.5696 55.7809 17.6541 55.2241 17.6541ZM63.0083 17.6491C62.3919 17.6491 61.8417 17.4917 61.3578 17.1768C60.8772 16.8587 60.4993 16.4062 60.2243 15.8196C59.9525 15.2296 59.8166 14.522 59.8166 13.6967C59.8166 12.8714 59.9541 12.1655 60.2292 11.5788C60.5076 10.9922 60.8888 10.5431 61.3727 10.2315C61.8566 9.91998 62.4051 9.7642 63.0183 9.7642C63.4922 9.7642 63.8734 9.84375 64.1618 10.0028C64.4534 10.1586 64.6788 10.3409 64.8379 10.5497C65.0003 10.7585 65.1262 10.9425 65.2157 11.1016H65.3052V7.31818H66.7917V17.5H65.34V16.3118H65.2157C65.1262 16.4742 64.997 16.6598 64.8279 16.8686C64.6622 17.0774 64.4335 17.2597 64.1419 17.4155C63.8502 17.5713 63.4724 17.6491 63.0083 17.6491ZM63.3365 16.3814C63.764 16.3814 64.1253 16.2687 64.4203 16.0433C64.7186 15.8146 64.944 15.4981 65.0964 15.0938C65.2522 14.6894 65.3301 14.2187 65.3301 13.6818C65.3301 13.1515 65.2538 12.6875 65.1014 12.2898C64.9489 11.892 64.7252 11.5821 64.4302 11.3601C64.1352 11.138 63.7707 11.027 63.3365 11.027C62.889 11.027 62.5162 11.143 62.2179 11.375C61.9196 11.607 61.6942 11.9235 61.5417 12.3246C61.3926 12.7256 61.318 13.178 61.318 13.6818C61.318 14.1922 61.3942 14.6513 61.5467 15.0589C61.6992 15.4666 61.9245 15.7898 62.2228 16.0284C62.5244 16.2637 62.8957 16.3814 63.3365 16.3814ZM68.7543 17.5V16.4808L72.8906 11.2259V11.1562H68.8885V9.86364H74.745V10.9474L70.7678 16.1378V16.2074H74.8842V17.5H68.7543ZM78.8603 17.669C78.3764 17.669 77.9389 17.5795 77.5478 17.4006C77.1567 17.2183 76.8468 16.9548 76.6181 16.6101C76.3927 16.2654 76.28 15.8428 76.28 15.3423C76.28 14.9115 76.3629 14.5568 76.5286 14.2784C76.6943 14 76.918 13.7796 77.1998 13.6172C77.4815 13.4548 77.7963 13.3321 78.1444 13.2493C78.4924 13.1664 78.847 13.1035 79.2083 13.0604C79.6657 13.0073 80.0369 12.9643 80.3219 12.9311C80.6069 12.8946 80.8141 12.8366 80.9434 12.7571C81.0726 12.6776 81.1373 12.5483 81.1373 12.3693V12.3345C81.1373 11.9003 81.0146 11.5639 80.7694 11.3253C80.5274 11.0866 80.1661 10.9673 79.6855 10.9673C79.1851 10.9673 78.7907 11.0784 78.5023 11.3004C78.2173 11.5192 78.0201 11.7628 77.9107 12.0312L76.5137 11.7131C76.6794 11.2491 76.9213 10.8745 77.2395 10.5895C77.561 10.3011 77.9306 10.0923 78.3482 9.96307C78.7658 9.83049 79.205 9.7642 79.6657 9.7642C79.9706 9.7642 80.2937 9.80066 80.6351 9.87358C80.9798 9.94318 81.3013 10.0724 81.5996 10.2614C81.9012 10.4503 82.1481 10.7204 82.3404 11.0717C82.5326 11.4197 82.6287 11.8722 82.6287 12.429V17.5H81.177V16.456H81.1174C81.0212 16.6482 80.8771 16.8371 80.6848 17.0227C80.4926 17.2083 80.2457 17.3625 79.9441 17.4851C79.6425 17.6077 79.2812 17.669 78.8603 17.669ZM79.1834 16.4759C79.5944 16.4759 79.9457 16.3946 80.2374 16.2322C80.5324 16.0698 80.7561 15.8577 80.9086 15.5959C81.0643 15.3307 81.1422 15.0473 81.1422 14.7457V13.7614C81.0892 13.8144 80.9864 13.8641 80.834 13.9105C80.6848 13.9536 80.5141 13.9917 80.3219 14.0249C80.1297 14.0547 79.9424 14.0829 79.7601 14.1094C79.5778 14.1326 79.4254 14.1525 79.3027 14.169C79.0144 14.2055 78.7509 14.2668 78.5123 14.353C78.2769 14.4392 78.088 14.5634 77.9455 14.7259C77.8063 14.8849 77.7367 15.0971 77.7367 15.3622C77.7367 15.7301 77.8726 16.0085 78.1444 16.1974C78.4161 16.383 78.7625 16.4759 79.1834 16.4759ZM88.1733 9.86364V11.0568H84.0021V9.86364H88.1733ZM85.1207 8.03409H86.6072V15.2578C86.6072 15.5462 86.6503 15.7633 86.7365 15.9091C86.8227 16.0516 86.9337 16.1494 87.0696 16.2024C87.2088 16.2521 87.3596 16.277 87.522 16.277C87.6413 16.277 87.7457 16.2687 87.8352 16.2521C87.9247 16.2356 87.9943 16.2223 88.044 16.2124L88.3125 17.4403C88.2263 17.4735 88.1037 17.5066 87.9446 17.5398C87.7855 17.5762 87.5866 17.5961 87.348 17.5994C86.9569 17.6061 86.5923 17.5365 86.2543 17.3906C85.9162 17.2448 85.6428 17.0194 85.4339 16.7145C85.2251 16.4096 85.1207 16.0268 85.1207 15.5661V8.03409ZM93.642 17.5V7.31818H95.1783V16.1776H99.7919V17.5H93.642ZM101.425 17.5V9.86364H102.912V17.5H101.425ZM102.176 8.68537C101.917 8.68537 101.695 8.59919 101.51 8.42685C101.327 8.25118 101.236 8.04238 101.236 7.80043C101.236 7.55516 101.327 7.34635 101.51 7.17401C101.695 6.99834 101.917 6.91051 102.176 6.91051C102.434 6.91051 102.655 6.99834 102.837 7.17401C103.023 7.34635 103.115 7.55516 103.115 7.80043C103.115 8.04238 103.023 8.25118 102.837 8.42685C102.655 8.59919 102.434 8.68537 102.176 8.68537ZM111.444 9.86364L108.675 17.5H107.084L104.31 9.86364H105.906L107.84 15.7401H107.919L109.848 9.86364H111.444ZM115.873 17.6541C115.12 17.6541 114.472 17.4934 113.929 17.1719C113.388 16.8471 112.971 16.3913 112.676 15.8047C112.384 15.2147 112.238 14.5237 112.238 13.7315C112.238 12.9493 112.384 12.2599 112.676 11.6634C112.971 11.0668 113.382 10.6011 113.909 10.2663C114.439 9.93158 115.059 9.7642 115.768 9.7642C116.199 9.7642 116.617 9.83546 117.021 9.97798C117.425 10.1205 117.788 10.3442 118.11 10.6491C118.431 10.9541 118.685 11.3501 118.87 11.8374C119.056 12.3213 119.149 12.9096 119.149 13.6023V14.1293H113.078V13.0156H117.692C117.692 12.6245 117.613 12.2782 117.453 11.9766C117.294 11.6716 117.071 11.4313 116.782 11.2557C116.497 11.08 116.163 10.9922 115.778 10.9922C115.36 10.9922 114.996 11.0949 114.684 11.3004C114.376 11.5026 114.137 11.7678 113.968 12.0959C113.803 12.4207 113.72 12.7737 113.72 13.1548V14.0249C113.72 14.5353 113.809 14.9695 113.988 15.3274C114.171 15.6854 114.424 15.9588 114.749 16.1477C115.074 16.3333 115.453 16.4261 115.887 16.4261C116.169 16.4261 116.426 16.3864 116.658 16.3068C116.89 16.224 117.091 16.1013 117.26 15.9389C117.429 15.7765 117.558 15.576 117.647 15.3374L119.054 15.5909C118.942 16.0052 118.739 16.3681 118.448 16.6797C118.159 16.9879 117.797 17.2282 117.359 17.4006C116.925 17.5696 116.429 17.6541 115.873 17.6541Z\"\r\n            fill=\"#262626\"\r\n          />\r\n        </svg>\r\n      ),\r\n      hasOwnBackground: true,\r\n    },\r\n    {\r\n      id: \"E-invites\",\r\n      label: \"E-invites\",\r\n      defaultSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"url(#paint0_linear_159_59448)\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <g clipPath=\"url(#clip0_159_59448)\">\r\n            <path\r\n              d=\"M8.84337 32.9353V32.9402C8.86239 32.9832 8.88894 33.0226 8.92178 33.0563C8.93131 33.0658 8.94135 33.0748 8.95184 33.0832C8.9776 33.1061 9.00629 33.1255 9.03716 33.1408C9.05016 33.1473 9.06194 33.1538 9.07534 33.1591C9.12103 33.1772 9.16962 33.1868 9.21875 33.1876H32.7812C32.8298 33.1866 32.8778 33.177 32.923 33.1591C32.9364 33.1538 32.9482 33.1473 32.9612 33.1408C32.9921 33.1255 33.0208 33.1061 33.0465 33.0832C33.057 33.0748 33.0671 33.0658 33.0766 33.0563C33.1094 33.0226 33.136 32.9832 33.155 32.9402V32.9353C33.1719 32.8914 33.1812 32.845 33.1826 32.798C33.1826 32.7919 33.1859 32.787 33.1859 32.7813V20.5938C33.1855 20.5359 33.1724 20.4788 33.1477 20.4264C33.1172 20.3792 33.0831 20.3344 33.0457 20.2924C33.0408 20.2879 33.0388 20.281 33.0335 20.2765L31.1562 18.7738V10.8438C31.1562 10.7361 31.1134 10.6327 31.0373 10.5565C30.9611 10.4804 30.8577 10.4376 30.75 10.4376H23.5594L21.2255 8.88081C21.1587 8.83627 21.0803 8.8125 21 8.8125C20.9197 8.8125 20.8413 8.83627 20.7745 8.88081L18.4406 10.4376H11.25C11.1423 10.4376 11.0389 10.4804 10.9627 10.5565C10.8866 10.6327 10.8437 10.7361 10.8437 10.8438V18.7738L8.96484 20.2769C8.95956 20.2814 8.95753 20.2883 8.95266 20.2928C8.92305 20.32 8.89783 20.3517 8.87791 20.3866C8.86799 20.3996 8.8589 20.4131 8.85069 20.4272C8.82605 20.4794 8.81302 20.5362 8.8125 20.5938V32.7813C8.8125 32.787 8.81575 32.7919 8.81575 32.798C8.81712 32.845 8.82646 32.8914 8.84337 32.9353ZM10.9965 22.5353H10.9997L15.2942 25.9096L9.625 31.7767V21.4388L10.9965 22.5353ZM10.1763 32.3751L18.9553 23.2933C19.2205 23.019 19.5382 22.8008 19.8895 22.6518C20.2408 22.5028 20.6184 22.426 21 22.426C21.3816 22.426 21.7592 22.5028 22.1105 22.6518C22.4618 22.8008 22.7795 23.019 23.0447 23.2933L31.8237 32.3751H10.1763ZM31.0011 22.5381H31.0043L32.375 21.4388V31.7767L26.7066 25.9128L31.0011 22.5381ZM32.1312 20.5938L31.1562 21.3734V19.8142L32.1312 20.5938ZM21 9.70631L22.0969 10.4376H19.9031L21 9.70631ZM11.6562 11.2501H30.3437V22.0214L26.1391 25.3258L23.6288 22.7287C23.2879 22.3759 22.8794 22.0954 22.4278 21.9038C21.9762 21.7123 21.4906 21.6135 21 21.6135C20.5094 21.6135 20.0238 21.7123 19.5722 21.9038C19.1206 22.0954 18.7121 22.3759 18.3712 22.7287L15.8613 25.3254L11.6562 22.0214V11.2501ZM10.8437 19.8142V21.3734L9.86875 20.5938L10.8437 19.8142Z\"\r\n              fill=\"#292D32\"\r\n              stroke=\"#292D32\"\r\n              strokeWidth=\"0.4\"\r\n            />\r\n            <path\r\n              d=\"M19.6212 16.034C19.6718 16.0557 19.7262 16.0669 19.7812 16.0669C19.8363 16.0669 19.8907 16.0557 19.9413 16.034C20.3137 15.8692 20.6691 15.6684 21.0024 15.4344C21.335 15.667 21.6886 15.8678 22.0587 16.0344C22.1092 16.0564 22.1637 16.0679 22.2188 16.0681C22.2746 16.0685 22.33 16.0571 22.3813 16.0348C22.6144 15.9349 24.6562 15.0159 24.6562 13.6306V13.4966C24.6563 13.3082 24.6192 13.1217 24.5472 12.9477C24.4751 12.7737 24.3695 12.6156 24.2363 12.4824C24.1032 12.3492 23.9451 12.2436 23.771 12.1716C23.597 12.0995 23.4105 12.0625 23.2222 12.0625C22.8467 12.0639 22.4866 12.212 22.2188 12.4753C22.0583 12.3204 21.8644 12.2046 21.652 12.1367C21.4396 12.0688 21.2144 12.0506 20.9939 12.0836C20.9255 12.0714 20.8562 12.0644 20.7867 12.0625C20.4102 12.0613 20.0487 12.2094 19.7812 12.4744C19.5801 12.2764 19.3249 12.1421 19.0477 12.0886C18.7705 12.0351 18.4836 12.0647 18.2232 12.1737C17.9627 12.2827 17.7403 12.4662 17.5839 12.7012C17.4274 12.9362 17.3439 13.2122 17.3438 13.4945V13.6294C17.3438 15.018 19.388 15.9341 19.6212 16.034ZM22.5056 13.3369L22.7859 13.0566C22.9041 12.9445 23.0609 12.882 23.2238 12.882C23.3868 12.882 23.5435 12.9445 23.6617 13.0566C23.7197 13.1142 23.7656 13.1828 23.7969 13.2583C23.8281 13.3339 23.8441 13.4148 23.8438 13.4966V13.6306C23.8438 14.2806 22.7712 14.9424 22.2188 15.215C22.0205 15.1161 21.8278 15.0065 21.6415 14.8868C21.9877 14.5569 22.1943 14.1069 22.2188 13.6294V13.4945C22.2188 13.4811 22.2151 13.4685 22.2147 13.4539C22.2686 13.4549 22.3221 13.445 22.3722 13.4249C22.4222 13.4048 22.4673 13.3749 22.5056 13.3369ZM18.1562 13.4945C18.1567 13.3723 18.1932 13.253 18.2612 13.1515C18.3292 13.05 18.4257 12.9708 18.5386 12.924C18.6514 12.8772 18.7756 12.8647 18.8955 12.8882C19.0154 12.9118 19.1257 12.9702 19.2125 13.0562L19.4924 13.3365C19.5701 13.4099 19.6729 13.4508 19.7798 13.4508C19.8867 13.4508 19.9896 13.4099 20.0673 13.3365L20.3468 13.0566C20.4333 12.9693 20.5438 12.9097 20.6643 12.8855C20.7848 12.8612 20.9098 12.8733 21.0234 12.9202C21.137 12.9672 21.234 13.0469 21.3022 13.1491C21.3704 13.2514 21.4066 13.3716 21.4062 13.4945V13.6294C21.4062 14.2835 20.3325 14.9432 19.7812 15.2138C19.23 14.944 18.1562 14.2843 18.1562 13.6294V13.4945ZM21.1044 26.0087L21 26.1127L20.8956 26.0083C20.6379 25.7506 20.3095 25.5751 19.9521 25.5041C19.5946 25.433 19.2241 25.4695 18.8874 25.609C18.5507 25.7485 18.263 25.9847 18.0605 26.2878C17.858 26.5908 17.75 26.9471 17.75 27.3115V27.5C17.75 29.3399 20.5239 30.5814 20.8399 30.7171C20.8905 30.7388 20.945 30.75 21 30.75C21.055 30.75 21.1095 30.7388 21.1601 30.7171C21.4761 30.5814 24.25 29.3399 24.25 27.5V27.3111C24.25 26.9466 24.142 26.5904 23.9395 26.2873C23.737 25.9843 23.4492 25.7481 23.1125 25.6087C22.7757 25.4693 22.4052 25.4328 22.0477 25.504C21.6903 25.5752 21.362 25.7508 21.1044 26.0087ZM23.4375 27.3111V27.5C23.4375 28.5355 21.8052 29.5187 21 29.8969C20.194 29.5187 18.5625 28.5372 18.5625 27.5V27.3115C18.5625 27.1077 18.6228 26.9085 18.736 26.7391C18.8492 26.5696 19.0101 26.4375 19.1983 26.3595C19.3866 26.2815 19.5937 26.2611 19.7936 26.3008C19.9935 26.3405 20.177 26.4386 20.3212 26.5827L20.7128 26.9747C20.7904 27.0482 20.8933 27.0892 21.0002 27.0892C21.1071 27.0892 21.21 27.0482 21.2876 26.9747L21.6788 26.5831C21.8229 26.4391 22.0064 26.341 22.2062 26.3012C22.406 26.2615 22.6131 26.2819 22.8013 26.3598C22.9895 26.4377 23.1504 26.5696 23.2636 26.739C23.3769 26.9083 23.4374 27.1074 23.4375 27.3111ZM14.5 17.75H16.9375C17.0452 17.75 17.1486 17.7072 17.2248 17.631C17.3009 17.5548 17.3438 17.4515 17.3438 17.3438C17.3438 17.236 17.3009 17.1327 17.2248 17.0565C17.1486 16.9803 17.0452 16.9375 16.9375 16.9375H14.5C14.3923 16.9375 14.2889 16.9803 14.2127 17.0565C14.1366 17.1327 14.0938 17.236 14.0938 17.3438C14.0938 17.4515 14.1366 17.5548 14.2127 17.631C14.2889 17.7072 14.3923 17.75 14.5 17.75ZM14.5 19.375H17.75C17.8577 19.375 17.9611 19.3322 18.0373 19.256C18.1134 19.1798 18.1562 19.0765 18.1562 18.9688C18.1562 18.861 18.1134 18.7577 18.0373 18.6815C17.9611 18.6053 17.8577 18.5625 17.75 18.5625H14.5C14.3923 18.5625 14.2889 18.6053 14.2127 18.6815C14.1366 18.7577 14.0938 18.861 14.0938 18.9688C14.0938 19.0765 14.1366 19.1798 14.2127 19.256C14.2889 19.3322 14.3923 19.375 14.5 19.375ZM14.5 21H18.5625C18.6702 21 18.7736 20.9572 18.8498 20.881C18.9259 20.8048 18.9688 20.7015 18.9688 20.5938C18.9688 20.486 18.9259 20.3827 18.8498 20.3065C18.7736 20.2303 18.6702 20.1875 18.5625 20.1875H14.5C14.3923 20.1875 14.2889 20.2303 14.2127 20.3065C14.1366 20.3827 14.0938 20.486 14.0938 20.5938C14.0938 20.7015 14.1366 20.8048 14.2127 20.881C14.2889 20.9572 14.3923 21 14.5 21ZM18.1562 17.3438C18.1562 17.4515 18.1991 17.5548 18.2752 17.631C18.3514 17.7072 18.4548 17.75 18.5625 17.75H27.5C27.6077 17.75 27.7111 17.7072 27.7873 17.631C27.8634 17.5548 27.9062 17.4515 27.9062 17.3438C27.9062 17.236 27.8634 17.1327 27.7873 17.0565C27.7111 16.9803 27.6077 16.9375 27.5 16.9375H18.5625C18.4548 16.9375 18.3514 16.9803 18.2752 17.0565C18.1991 17.1327 18.1562 17.236 18.1562 17.3438ZM27.5 18.5625H19.375C19.2673 18.5625 19.1639 18.6053 19.0877 18.6815C19.0116 18.7577 18.9688 18.861 18.9688 18.9688C18.9688 19.0765 19.0116 19.1798 19.0877 19.256C19.1639 19.3322 19.2673 19.375 19.375 19.375H27.5C27.6077 19.375 27.7111 19.3322 27.7873 19.256C27.8634 19.1798 27.9062 19.0765 27.9062 18.9688C27.9062 18.861 27.8634 18.7577 27.7873 18.6815C27.7111 18.6053 27.6077 18.5625 27.5 18.5625ZM27.5 20.1875H20.1875C20.0798 20.1875 19.9764 20.2303 19.9002 20.3065C19.8241 20.3827 19.7812 20.486 19.7812 20.5938C19.7812 20.7015 19.8241 20.8048 19.9002 20.881C19.9764 20.9572 20.0798 21 20.1875 21H27.5C27.6077 21 27.7111 20.9572 27.7873 20.881C27.8634 20.8048 27.9062 20.7015 27.9062 20.5938C27.9062 20.486 27.8634 20.3827 27.7873 20.3065C27.7111 20.2303 27.6077 20.1875 27.5 20.1875Z\"\r\n              fill=\"#292D32\"\r\n            />\r\n          </g>\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_159_59448\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n            <clipPath id=\"clip0_159_59448\">\r\n              <rect\r\n                width=\"26\"\r\n                height=\"26\"\r\n                fill=\"white\"\r\n                transform=\"translate(8 8)\"\r\n              />\r\n            </clipPath>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      activeSvg: (\r\n        <svg\r\n          width=\"42\"\r\n          height=\"42\"\r\n          viewBox=\"0 0 42 42\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            fill=\"#B31B1E\"\r\n          />\r\n          <rect\r\n            x=\"0.5\"\r\n            y=\"0.5\"\r\n            width=\"41\"\r\n            height=\"41\"\r\n            rx=\"20.5\"\r\n            stroke=\"#D9D9D9\"\r\n          />\r\n          <g clipPath=\"url(#clip0_159_59448)\">\r\n            <path\r\n              d=\"M8.84337 32.9353V32.9402C8.86239 32.9832 8.88894 33.0226 8.92178 33.0563C8.93131 33.0658 8.94135 33.0748 8.95184 33.0832C8.9776 33.1061 9.00629 33.1255 9.03716 33.1408C9.05016 33.1473 9.06194 33.1538 9.07534 33.1591C9.12103 33.1772 9.16962 33.1868 9.21875 33.1876H32.7812C32.8298 33.1866 32.8778 33.177 32.923 33.1591C32.9364 33.1538 32.9482 33.1473 32.9612 33.1408C32.9921 33.1255 33.0208 33.1061 33.0465 33.0832C33.057 33.0748 33.0671 33.0658 33.0766 33.0563C33.1094 33.0226 33.136 32.9832 33.155 32.9402V32.9353C33.1719 32.8914 33.1812 32.845 33.1826 32.798C33.1826 32.7919 33.1859 32.787 33.1859 32.7813V20.5938C33.1855 20.5359 33.1724 20.4788 33.1477 20.4264C33.1172 20.3792 33.0831 20.3344 33.0457 20.2924C33.0408 20.2879 33.0388 20.281 33.0335 20.2765L31.1562 18.7738V10.8438C31.1562 10.7361 31.1134 10.6327 31.0373 10.5565C30.9611 10.4804 30.8577 10.4376 30.75 10.4376H23.5594L21.2255 8.88081C21.1587 8.83627 21.0803 8.8125 21 8.8125C20.9197 8.8125 20.8413 8.83627 20.7745 8.88081L18.4406 10.4376H11.25C11.1423 10.4376 11.0389 10.4804 10.9627 10.5565C10.8866 10.6327 10.8437 10.7361 10.8437 10.8438V18.7738L8.96484 20.2769C8.95956 20.2814 8.95753 20.2883 8.95266 20.2928C8.92305 20.32 8.89783 20.3517 8.87791 20.3866C8.86799 20.3996 8.8589 20.4131 8.85069 20.4272C8.82605 20.4794 8.81302 20.5362 8.8125 20.5938V32.7813C8.8125 32.787 8.81575 32.7919 8.81575 32.798C8.81712 32.845 8.82646 32.8914 8.84337 32.9353ZM10.9965 22.5353H10.9997L15.2942 25.9096L9.625 31.7767V21.4388L10.9965 22.5353ZM10.1763 32.3751L18.9553 23.2933C19.2205 23.019 19.5382 22.8008 19.8895 22.6518C20.2408 22.5028 20.6184 22.426 21 22.426C21.3816 22.426 21.7592 22.5028 22.1105 22.6518C22.4618 22.8008 22.7795 23.019 23.0447 23.2933L31.8237 32.3751H10.1763ZM31.0011 22.5381H31.0043L32.375 21.4388V31.7767L26.7066 25.9128L31.0011 22.5381ZM32.1312 20.5938L31.1562 21.3734V19.8142L32.1312 20.5938ZM21 9.70631L22.0969 10.4376H19.9031L21 9.70631ZM11.6562 11.2501H30.3437V22.0214L26.1391 25.3258L23.6288 22.7287C23.2879 22.3759 22.8794 22.0954 22.4278 21.9038C21.9762 21.7123 21.4906 21.6135 21 21.6135C20.5094 21.6135 20.0238 21.7123 19.5722 21.9038C19.1206 22.0954 18.7121 22.3759 18.3712 22.7287L15.8613 25.3254L11.6562 22.0214V11.2501ZM10.8437 19.8142V21.3734L9.86875 20.5938L10.8437 19.8142Z\"\r\n              fill=\"#292D32\"\r\n              stroke=\"#292D32\"\r\n              strokeWidth=\"0.4\"\r\n            />\r\n            <path\r\n              d=\"M19.6212 16.034C19.6718 16.0557 19.7262 16.0669 19.7812 16.0669C19.8363 16.0669 19.8907 16.0557 19.9413 16.034C20.3137 15.8692 20.6691 15.6684 21.0024 15.4344C21.335 15.667 21.6886 15.8678 22.0587 16.0344C22.1092 16.0564 22.1637 16.0679 22.2188 16.0681C22.2746 16.0685 22.33 16.0571 22.3813 16.0348C22.6144 15.9349 24.6562 15.0159 24.6562 13.6306V13.4966C24.6563 13.3082 24.6192 13.1217 24.5472 12.9477C24.4751 12.7737 24.3695 12.6156 24.2363 12.4824C24.1032 12.3492 23.9451 12.2436 23.771 12.1716C23.597 12.0995 23.4105 12.0625 23.2222 12.0625C22.8467 12.0639 22.4866 12.212 22.2188 12.4753C22.0583 12.3204 21.8644 12.2046 21.652 12.1367C21.4396 12.0688 21.2144 12.0506 20.9939 12.0836C20.9255 12.0714 20.8562 12.0644 20.7867 12.0625C20.4102 12.0613 20.0487 12.2094 19.7812 12.4744C19.5801 12.2764 19.3249 12.1421 19.0477 12.0886C18.7705 12.0351 18.4836 12.0647 18.2232 12.1737C17.9627 12.2827 17.7403 12.4662 17.5839 12.7012C17.4274 12.9362 17.3439 13.2122 17.3438 13.4945V13.6294C17.3438 15.018 19.388 15.9341 19.6212 16.034ZM22.5056 13.3369L22.7859 13.0566C22.9041 12.9445 23.0609 12.882 23.2238 12.882C23.3868 12.882 23.5435 12.9445 23.6617 13.0566C23.7197 13.1142 23.7656 13.1828 23.7969 13.2583C23.8281 13.3339 23.8441 13.4148 23.8438 13.4966V13.6306C23.8438 14.2806 22.7712 14.9424 22.2188 15.215C22.0205 15.1161 21.8278 15.0065 21.6415 14.8868C21.9877 14.5569 22.1943 14.1069 22.2188 13.6294V13.4945C22.2188 13.4811 22.2151 13.4685 22.2147 13.4539C22.2686 13.4549 22.3221 13.445 22.3722 13.4249C22.4222 13.4048 22.4673 13.3749 22.5056 13.3369ZM18.1562 13.4945C18.1567 13.3723 18.1932 13.253 18.2612 13.1515C18.3292 13.05 18.4257 12.9708 18.5386 12.924C18.6514 12.8772 18.7756 12.8647 18.8955 12.8882C19.0154 12.9118 19.1257 12.9702 19.2125 13.0562L19.4924 13.3365C19.5701 13.4099 19.6729 13.4508 19.7798 13.4508C19.8867 13.4508 19.9896 13.4099 20.0673 13.3365L20.3468 13.0566C20.4333 12.9693 20.5438 12.9097 20.6643 12.8855C20.7848 12.8612 20.9098 12.8733 21.0234 12.9202C21.137 12.9672 21.234 13.0469 21.3022 13.1491C21.3704 13.2514 21.4066 13.3716 21.4062 13.4945V13.6294C21.4062 14.2835 20.3325 14.9432 19.7812 15.2138C19.23 14.944 18.1562 14.2843 18.1562 13.6294V13.4945ZM21.1044 26.0087L21 26.1127L20.8956 26.0083C20.6379 25.7506 20.3095 25.5751 19.9521 25.5041C19.5946 25.433 19.2241 25.4695 18.8874 25.609C18.5507 25.7485 18.263 25.9847 18.0605 26.2878C17.858 26.5908 17.75 26.9471 17.75 27.3115V27.5C17.75 29.3399 20.5239 30.5814 20.8399 30.7171C20.8905 30.7388 20.945 30.75 21 30.75C21.055 30.75 21.1095 30.7388 21.1601 30.7171C21.4761 30.5814 24.25 29.3399 24.25 27.5V27.3111C24.25 26.9466 24.142 26.5904 23.9395 26.2873C23.737 25.9843 23.4492 25.7481 23.1125 25.6087C22.7757 25.4693 22.4052 25.4328 22.0477 25.504C21.6903 25.5752 21.362 25.7508 21.1044 26.0087ZM23.4375 27.3111V27.5C23.4375 28.5355 21.8052 29.5187 21 29.8969C20.194 29.5187 18.5625 28.5372 18.5625 27.5V27.3115C18.5625 27.1077 18.6228 26.9085 18.736 26.7391C18.8492 26.5696 19.0101 26.4375 19.1983 26.3595C19.3866 26.2815 19.5937 26.2611 19.7936 26.3008C19.9935 26.3405 20.177 26.4386 20.3212 26.5827L20.7128 26.9747C20.7904 27.0482 20.8933 27.0892 21.0002 27.0892C21.1071 27.0892 21.21 27.0482 21.2876 26.9747L21.6788 26.5831C21.8229 26.4391 22.0064 26.341 22.2062 26.3012C22.406 26.2615 22.6131 26.2819 22.8013 26.3598C22.9895 26.4377 23.1504 26.5696 23.2636 26.739C23.3769 26.9083 23.4374 27.1074 23.4375 27.3111ZM14.5 17.75H16.9375C17.0452 17.75 17.1486 17.7072 17.2248 17.631C17.3009 17.5548 17.3438 17.4515 17.3438 17.3438C17.3438 17.236 17.3009 17.1327 17.2248 17.0565C17.1486 16.9803 17.0452 16.9375 16.9375 16.9375H14.5C14.3923 16.9375 14.2889 16.9803 14.2127 17.0565C14.1366 17.1327 14.0938 17.236 14.0938 17.3438C14.0938 17.4515 14.1366 17.5548 14.2127 17.631C14.2889 17.7072 14.3923 17.75 14.5 17.75ZM14.5 19.375H17.75C17.8577 19.375 17.9611 19.3322 18.0373 19.256C18.1134 19.1798 18.1562 19.0765 18.1562 18.9688C18.1562 18.861 18.1134 18.7577 18.0373 18.6815C17.9611 18.6053 17.8577 18.5625 17.75 18.5625H14.5C14.3923 18.5625 14.2889 18.6053 14.2127 18.6815C14.1366 18.7577 14.0938 18.861 14.0938 18.9688C14.0938 19.0765 14.1366 19.1798 14.2127 19.256C14.2889 19.3322 14.3923 19.375 14.5 19.375ZM14.5 21H18.5625C18.6702 21 18.7736 20.9572 18.8498 20.881C18.9259 20.8048 18.9688 20.7015 18.9688 20.5938C18.9688 20.486 18.9259 20.3827 18.8498 20.3065C18.7736 20.2303 18.6702 20.1875 18.5625 20.1875H14.5C14.3923 20.1875 14.2889 20.2303 14.2127 20.3065C14.1366 20.3827 14.0938 20.486 14.0938 20.5938C14.0938 20.7015 14.1366 20.8048 14.2127 20.881C14.2889 20.9572 14.3923 21 14.5 21ZM18.1562 17.3438C18.1562 17.4515 18.1991 17.5548 18.2752 17.631C18.3514 17.7072 18.4548 17.75 18.5625 17.75H27.5C27.6077 17.75 27.7111 17.7072 27.7873 17.631C27.8634 17.5548 27.9062 17.4515 27.9062 17.3438C27.9062 17.236 27.8634 17.1327 27.7873 17.0565C27.7111 16.9803 27.6077 16.9375 27.5 16.9375H18.5625C18.4548 16.9375 18.3514 16.9803 18.2752 17.0565C18.1991 17.1327 18.1562 17.236 18.1562 17.3438ZM27.5 18.5625H19.375C19.2673 18.5625 19.1639 18.6053 19.0877 18.6815C19.0116 18.7577 18.9688 18.861 18.9688 18.9688C18.9688 19.0765 19.0116 19.1798 19.0877 19.256C19.1639 19.3322 19.2673 19.375 19.375 19.375H27.5C27.6077 19.375 27.7111 19.3322 27.7873 19.256C27.8634 19.1798 27.9062 19.0765 27.9062 18.9688C27.9062 18.861 27.8634 18.7577 27.7873 18.6815C27.7111 18.6053 27.6077 18.5625 27.5 18.5625ZM27.5 20.1875H20.1875C20.0798 20.1875 19.9764 20.2303 19.9002 20.3065C19.8241 20.3827 19.7812 20.486 19.7812 20.5938C19.7812 20.7015 19.8241 20.8048 19.9002 20.881C19.9764 20.9572 20.0798 21 20.1875 21H27.5C27.6077 21 27.7111 20.9572 27.7873 20.881C27.8634 20.8048 27.9062 20.7015 27.9062 20.5938C27.9062 20.486 27.8634 20.3827 27.7873 20.3065C27.7111 20.2303 27.6077 20.1875 27.5 20.1875Z\"\r\n              fill=\"#292D32\"\r\n            />\r\n          </g>\r\n          <defs>\r\n            <linearGradient\r\n              id=\"paint0_linear_159_59448\"\r\n              x1=\"21\"\r\n              y1=\"0\"\r\n              x2=\"21\"\r\n              y2=\"41.9976\"\r\n              gradientUnits=\"userSpaceOnUse\"\r\n            >\r\n              <stop stopColor=\"#FAE6C4\" />\r\n              <stop offset=\"0.997947\" stopColor=\"white\" />\r\n            </linearGradient>\r\n            <clipPath id=\"clip0_159_59448\">\r\n              <rect\r\n                width=\"26\"\r\n                height=\"26\"\r\n                fill=\"white\"\r\n                transform=\"translate(8 8)\"\r\n              />\r\n            </clipPath>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      expandedDefaultSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <g clipPath=\"url(#clip0_159_59309)\">\r\n            <path\r\n              d=\"M4.7785 23.0172V23.0217C4.79605 23.0615 4.82056 23.0978 4.85088 23.1289C4.85967 23.1377 4.86894 23.1459 4.87862 23.1537C4.9024 23.1748 4.92888 23.1927 4.95737 23.2069C4.96937 23.2129 4.98025 23.2189 4.99263 23.2238C5.0348 23.2405 5.07965 23.2494 5.125 23.2501H26.875C26.9198 23.2492 26.9642 23.2403 27.0059 23.2238C27.0182 23.2189 27.0291 23.2129 27.0411 23.2069C27.0696 23.1927 27.0961 23.1748 27.1199 23.1537C27.1296 23.1459 27.1388 23.1377 27.1476 23.1289C27.1779 23.0978 27.2024 23.0615 27.22 23.0217V23.0172C27.2356 22.9767 27.2442 22.9338 27.2455 22.8904C27.2455 22.8848 27.2485 22.8803 27.2485 22.8751V11.6251C27.2481 11.5716 27.2361 11.5189 27.2132 11.4706C27.1851 11.4269 27.1537 11.3856 27.1191 11.3468C27.1146 11.3427 27.1127 11.3363 27.1079 11.3322L25.375 9.94506V2.62506C25.375 2.5256 25.3355 2.43022 25.2652 2.35989C25.1948 2.28957 25.0995 2.25006 25 2.25006H18.3625L16.2081 0.813056C16.1465 0.771941 16.0741 0.75 16 0.75C15.9259 0.75 15.8535 0.771941 15.7919 0.813056L13.6375 2.25006H7C6.90054 2.25006 6.80516 2.28957 6.73484 2.35989C6.66451 2.43022 6.625 2.5256 6.625 2.62506V9.94506L4.89062 11.3326C4.88575 11.3367 4.88388 11.3431 4.87938 11.3472C4.85204 11.3723 4.82877 11.4015 4.81038 11.4338C4.80122 11.4458 4.79283 11.4583 4.78525 11.4713C4.76251 11.5194 4.75048 11.5719 4.75 11.6251V22.8751C4.75 22.8803 4.753 22.8848 4.753 22.8904C4.75427 22.9338 4.76289 22.9767 4.7785 23.0172ZM6.766 13.4172H6.769L10.7331 16.5319L5.5 21.9477V12.4051L6.766 13.4172ZM6.00887 22.5001L14.1126 14.1169C14.3574 13.8637 14.6507 13.6623 14.9749 13.5248C15.2992 13.3872 15.6478 13.3163 16 13.3163C16.3522 13.3163 16.7008 13.3872 17.0251 13.5248C17.3493 13.6623 17.6426 13.8637 17.8874 14.1169L25.9911 22.5001H6.00887ZM25.2318 13.4198H25.2347L26.5 12.4051V21.9477L21.2676 16.5349L25.2318 13.4198ZM26.275 11.6251L25.375 12.3447V10.9054L26.275 11.6251ZM16 1.57506L17.0125 2.25006H14.9875L16 1.57506ZM7.375 3.00006H24.625V12.9428L20.7438 15.9931L18.4266 13.5957C18.1119 13.2701 17.7348 13.0112 17.318 12.8343C16.9011 12.6575 16.4528 12.5663 16 12.5663C15.5472 12.5663 15.0989 12.6575 14.682 12.8343C14.2651 13.0112 13.8881 13.2701 13.5734 13.5957L11.2566 15.9927L7.375 12.9428V3.00006ZM6.625 10.9054V12.3447L5.725 11.6251L6.625 10.9054Z\"\r\n              fill=\"#292D32\"\r\n              stroke=\"#292D32\"\r\n              strokeWidth=\"0.4\"\r\n            />\r\n            <path\r\n              d=\"M14.7273 7.41601C14.7739 7.43602 14.8242 7.44634 14.875 7.44634C14.9258 7.44634 14.9761 7.43602 15.0227 7.41601C15.3665 7.2639 15.6946 7.07851 16.0023 6.86251C16.3092 7.07719 16.6357 7.26263 16.9772 7.41638C17.0239 7.43667 17.0741 7.44726 17.125 7.44751C17.1766 7.44781 17.2277 7.43734 17.275 7.41676C17.4903 7.32451 19.375 6.47626 19.375 5.19751V5.07376C19.375 4.89991 19.3408 4.72775 19.2743 4.56712C19.2078 4.40649 19.1103 4.26055 18.9874 4.13761C18.8645 4.01468 18.7185 3.91718 18.5579 3.85067C18.3973 3.78416 18.2251 3.74996 18.0513 3.75001C17.7046 3.75132 17.3722 3.88804 17.125 4.13101C16.9769 3.98809 16.7979 3.88115 16.6018 3.81847C16.4058 3.75579 16.1979 3.73903 15.9944 3.76951C15.9312 3.75824 15.8673 3.75173 15.8031 3.75001C15.4556 3.74889 15.1218 3.88563 14.875 4.13026C14.6893 3.9474 14.4537 3.8235 14.1978 3.77411C13.942 3.72472 13.6772 3.75204 13.4368 3.85265C13.1964 3.95326 12.9911 4.12267 12.8466 4.33959C12.7022 4.55651 12.6251 4.81128 12.625 5.07188V5.19638C12.625 6.47813 14.512 7.32376 14.7273 7.41601ZM17.3898 4.92638L17.6485 4.66763C17.7577 4.56416 17.9023 4.50649 18.0527 4.50649C18.2032 4.50649 18.3478 4.56416 18.457 4.66763C18.5105 4.72083 18.5529 4.78412 18.5817 4.85384C18.6106 4.92356 18.6253 4.99831 18.625 5.07376V5.19751C18.625 5.79751 17.635 6.40838 17.125 6.66001C16.942 6.56873 16.7641 6.46758 16.5921 6.35701C16.9117 6.05255 17.1024 5.63718 17.125 5.19638V5.07188C17.125 5.05951 17.1216 5.04788 17.1213 5.03438C17.171 5.03526 17.2204 5.02616 17.2666 5.00761C17.3128 4.98906 17.3544 4.96144 17.3898 4.92638ZM13.375 5.07188C13.3754 4.95909 13.4091 4.84893 13.4719 4.75523C13.5346 4.66152 13.6237 4.58847 13.7279 4.54524C13.8321 4.502 13.9467 4.49052 14.0574 4.51223C14.1681 4.53394 14.2699 4.58787 14.35 4.66726L14.6084 4.92601C14.6801 4.99378 14.775 5.03155 14.8737 5.03155C14.9724 5.03155 15.0673 4.99378 15.139 4.92601L15.397 4.66763C15.4769 4.58706 15.5789 4.53207 15.6901 4.50965C15.8014 4.48723 15.9167 4.4984 16.0216 4.54174C16.1264 4.58508 16.216 4.65863 16.279 4.75304C16.3419 4.84744 16.3753 4.95843 16.375 5.07188V5.19638C16.375 5.80013 15.3839 6.40913 14.875 6.65888C14.3661 6.40988 13.375 5.80088 13.375 5.19638V5.07188ZM16.0964 16.6234L16 16.7194L15.9036 16.623C15.6657 16.3852 15.3626 16.2232 15.0327 16.1576C14.7027 16.092 14.3607 16.1257 14.0499 16.2545C13.7391 16.3833 13.4735 16.6013 13.2866 16.881C13.0997 17.1607 13 17.4896 13 17.826V18C13 19.6984 15.5605 20.8444 15.8523 20.9696C15.8989 20.9896 15.9492 21 16 21C16.0508 21 16.1011 20.9896 16.1478 20.9696C16.4395 20.8444 19 19.6984 19 18V17.8256C19 17.4892 18.9003 17.1603 18.7134 16.8806C18.5265 16.6009 18.2608 16.3829 17.95 16.2542C17.6391 16.1255 17.2971 16.0918 16.9672 16.1576C16.6372 16.2233 16.3342 16.3854 16.0964 16.6234ZM18.25 17.8256V18C18.25 18.9559 16.7432 19.8634 16 20.2125C15.256 19.8634 13.75 18.9574 13.75 18V17.826C13.75 17.6379 13.8057 17.454 13.9102 17.2976C14.0146 17.1412 14.1632 17.0193 14.3369 16.9473C14.5107 16.8752 14.7019 16.8564 14.8864 16.893C15.0709 16.9297 15.2404 17.0203 15.3734 17.1533L15.7349 17.5151C15.8065 17.583 15.9015 17.6208 16.0002 17.6208C16.0989 17.6208 16.1938 17.583 16.2655 17.5151L16.6266 17.1536C16.7596 17.0207 16.929 16.9301 17.1134 16.8935C17.2978 16.8568 17.489 16.8756 17.6627 16.9475C17.8365 17.0194 17.985 17.1412 18.0895 17.2975C18.194 17.4538 18.2499 17.6376 18.25 17.8256ZM10 9.00001H12.25C12.3495 9.00001 12.4448 8.9605 12.5152 8.89017C12.5855 8.81985 12.625 8.72446 12.625 8.62501C12.625 8.52555 12.5855 8.43017 12.5152 8.35984C12.4448 8.28952 12.3495 8.25001 12.25 8.25001H10C9.90054 8.25001 9.80516 8.28952 9.73484 8.35984C9.66451 8.43017 9.625 8.52555 9.625 8.62501C9.625 8.72446 9.66451 8.81985 9.73484 8.89017C9.80516 8.9605 9.90054 9.00001 10 9.00001ZM10 10.5H13C13.0995 10.5 13.1948 10.4605 13.2652 10.3902C13.3355 10.3198 13.375 10.2245 13.375 10.125C13.375 10.0256 13.3355 9.93017 13.2652 9.85984C13.1948 9.78952 13.0995 9.75001 13 9.75001H10C9.90054 9.75001 9.80516 9.78952 9.73484 9.85984C9.66451 9.93017 9.625 10.0256 9.625 10.125C9.625 10.2245 9.66451 10.3198 9.73484 10.3902C9.80516 10.4605 9.90054 10.5 10 10.5ZM10 12H13.75C13.8495 12 13.9448 11.9605 14.0152 11.8902C14.0855 11.8198 14.125 11.7245 14.125 11.625C14.125 11.5256 14.0855 11.4302 14.0152 11.3598C13.9448 11.2895 13.8495 11.25 13.75 11.25H10C9.90054 11.25 9.80516 11.2895 9.73484 11.3598C9.66451 11.4302 9.625 11.5256 9.625 11.625C9.625 11.7245 9.66451 11.8198 9.73484 11.8902C9.80516 11.9605 9.90054 12 10 12ZM13.375 8.62501C13.375 8.72446 13.4145 8.81985 13.4848 8.89017C13.5552 8.9605 13.6505 9.00001 13.75 9.00001H22C22.0995 9.00001 22.1948 8.9605 22.2652 8.89017C22.3355 8.81985 22.375 8.72446 22.375 8.62501C22.375 8.52555 22.3355 8.43017 22.2652 8.35984C22.1948 8.28952 22.0995 8.25001 22 8.25001H13.75C13.6505 8.25001 13.5552 8.28952 13.4848 8.35984C13.4145 8.43017 13.375 8.52555 13.375 8.62501ZM22 9.75001H14.5C14.4005 9.75001 14.3052 9.78952 14.2348 9.85984C14.1645 9.93017 14.125 10.0256 14.125 10.125C14.125 10.2245 14.1645 10.3198 14.2348 10.3902C14.3052 10.4605 14.4005 10.5 14.5 10.5H22C22.0995 10.5 22.1948 10.4605 22.2652 10.3902C22.3355 10.3198 22.375 10.2245 22.375 10.125C22.375 10.0256 22.3355 9.93017 22.2652 9.85984C22.1948 9.78952 22.0995 9.75001 22 9.75001ZM22 11.25H15.25C15.1505 11.25 15.0552 11.2895 14.9848 11.3598C14.9145 11.4302 14.875 11.5256 14.875 11.625C14.875 11.7245 14.9145 11.8198 14.9848 11.8902C15.0552 11.9605 15.1505 12 15.25 12H22C22.0995 12 22.1948 11.9605 22.2652 11.8902C22.3355 11.8198 22.375 11.7245 22.375 11.625C22.375 11.5256 22.3355 11.4302 22.2652 11.3598C22.1948 11.2895 22.0995 11.25 22 11.25Z\"\r\n              fill=\"#292D32\"\r\n            />\r\n          </g>\r\n          <path\r\n            d=\"M39.1186 17.5V7.31818H45.5021V8.64062H40.6548V11.7429H45.169V13.0604H40.6548V16.1776H45.5618V17.5H39.1186ZM51.9341 11.7628V13.0554H47.4199V11.7628H51.9341ZM55.5708 7.31818V17.5H54.0346V7.31818H55.5708ZM59.1752 12.9659V17.5H57.6887V9.86364H59.1156V11.1065H59.21C59.3857 10.7022 59.6608 10.3774 60.0353 10.1321C60.4132 9.88684 60.8888 9.7642 61.4622 9.7642C61.9825 9.7642 62.4383 9.87358 62.8294 10.0923C63.2205 10.3078 63.5237 10.6293 63.7392 11.0568C63.9546 11.4844 64.0623 12.013 64.0623 12.6428V17.5H62.5758V12.8217C62.5758 12.2682 62.4316 11.8357 62.1433 11.5241C61.8549 11.2093 61.4589 11.0518 60.9551 11.0518C60.6104 11.0518 60.3038 11.1264 60.0353 11.2756C59.7702 11.4247 59.5597 11.6435 59.4039 11.9318C59.2515 12.2169 59.1752 12.5616 59.1752 12.9659ZM72.3152 9.86364L69.546 17.5H67.9551L65.1809 9.86364H66.7768L68.7108 15.7401H68.7903L70.7193 9.86364H72.3152ZM73.7122 17.5V9.86364H75.1987V17.5H73.7122ZM74.4629 8.68537C74.2044 8.68537 73.9823 8.59919 73.7967 8.42685C73.6144 8.25118 73.5233 8.04238 73.5233 7.80043C73.5233 7.55516 73.6144 7.34635 73.7967 7.17401C73.9823 6.99834 74.2044 6.91051 74.4629 6.91051C74.7214 6.91051 74.9418 6.99834 75.1241 7.17401C75.3097 7.34635 75.4025 7.55516 75.4025 7.80043C75.4025 8.04238 75.3097 8.25118 75.1241 8.42685C74.9418 8.59919 74.7214 8.68537 74.4629 8.68537ZM80.7631 9.86364V11.0568H76.592V9.86364H80.7631ZM77.7106 8.03409H79.1971V15.2578C79.1971 15.5462 79.2402 15.7633 79.3263 15.9091C79.4125 16.0516 79.5236 16.1494 79.6594 16.2024C79.7987 16.2521 79.9495 16.277 80.1119 16.277C80.2312 16.277 80.3356 16.2687 80.4251 16.2521C80.5146 16.2356 80.5842 16.2223 80.6339 16.2124L80.9023 17.4403C80.8162 17.4735 80.6935 17.5066 80.5344 17.5398C80.3754 17.5762 80.1765 17.5961 79.9379 17.5994C79.5468 17.6061 79.1822 17.5365 78.8441 17.3906C78.506 17.2448 78.2326 17.0194 78.0238 16.7145C77.815 16.4096 77.7106 16.0268 77.7106 15.5661V8.03409ZM85.6167 17.6541C84.8643 17.6541 84.2163 17.4934 83.6728 17.1719C83.1325 16.8471 82.7149 16.3913 82.4199 15.8047C82.1283 15.2147 81.9824 14.5237 81.9824 13.7315C81.9824 12.9493 82.1283 12.2599 82.4199 11.6634C82.7149 11.0668 83.1259 10.6011 83.6529 10.2663C84.1832 9.93158 84.803 9.7642 85.5123 9.7642C85.9431 9.7642 86.3607 9.83546 86.7651 9.97798C87.1694 10.1205 87.5324 10.3442 87.8539 10.6491C88.1754 10.9541 88.4289 11.3501 88.6145 11.8374C88.8001 12.3213 88.8929 12.9096 88.8929 13.6023V14.1293H82.8226V13.0156H87.4363C87.4363 12.6245 87.3567 12.2782 87.1976 11.9766C87.0385 11.6716 86.8148 11.4313 86.5265 11.2557C86.2414 11.08 85.9067 10.9922 85.5222 10.9922C85.1046 10.9922 84.74 11.0949 84.4284 11.3004C84.1202 11.5026 83.8816 11.7678 83.7125 12.0959C83.5468 12.4207 83.464 12.7737 83.464 13.1548V14.0249C83.464 14.5353 83.5534 14.9695 83.7324 15.3274C83.9147 15.6854 84.1683 15.9588 84.4931 16.1477C84.8179 16.3333 85.1974 16.4261 85.6316 16.4261C85.9133 16.4261 86.1702 16.3864 86.4022 16.3068C86.6342 16.224 86.8347 16.1013 87.0037 15.9389C87.1728 15.7765 87.302 15.576 87.3915 15.3374L88.7985 15.5909C88.6858 16.0052 88.4836 16.3681 88.1919 16.6797C87.9036 16.9879 87.5407 17.2282 87.1032 17.4006C86.669 17.5696 86.1735 17.6541 85.6167 17.6541ZM96.2646 11.728L94.9173 11.9666C94.8609 11.7943 94.7714 11.6302 94.6488 11.4744C94.5295 11.3187 94.3671 11.1911 94.1616 11.0916C93.9561 10.9922 93.6992 10.9425 93.391 10.9425C92.9701 10.9425 92.6187 11.0369 92.337 11.2259C92.0553 11.4115 91.9144 11.6518 91.9144 11.9467C91.9144 12.2019 92.0089 12.4074 92.1978 12.5632C92.3867 12.719 92.6916 12.8466 93.1126 12.946L94.3256 13.2244C95.0283 13.3868 95.552 13.6371 95.8967 13.9751C96.2414 14.3132 96.4137 14.7524 96.4137 15.2926C96.4137 15.75 96.2811 16.1577 96.016 16.5156C95.7541 16.8703 95.3879 17.1487 94.9173 17.3509C94.4499 17.553 93.908 17.6541 93.2915 17.6541C92.4364 17.6541 91.7388 17.4718 91.1985 17.1072C90.6583 16.7393 90.3268 16.2173 90.2042 15.5412L91.641 15.3224C91.7305 15.697 91.9144 15.9804 92.1928 16.1726C92.4712 16.3615 92.8342 16.456 93.2816 16.456C93.7688 16.456 94.1583 16.3549 94.4499 16.1527C94.7416 15.9472 94.8874 15.697 94.8874 15.402C94.8874 15.1634 94.7979 14.9628 94.619 14.8004C94.4433 14.638 94.1732 14.5154 93.8086 14.4325L92.516 14.1491C91.8034 13.9867 91.2764 13.7282 90.935 13.3736C90.5969 13.0189 90.4279 12.5698 90.4279 12.0263C90.4279 11.5755 90.5539 11.1811 90.8058 10.843C91.0576 10.505 91.4057 10.2415 91.8498 10.0526C92.2939 9.86032 92.8027 9.7642 93.3761 9.7642C94.2013 9.7642 94.851 9.94318 95.3249 10.3011C95.7989 10.6558 96.1121 11.1314 96.2646 11.728Z\"\r\n            fill=\"#262626\"\r\n          />\r\n          <defs>\r\n            <clipPath id=\"clip0_159_59309\">\r\n              <rect\r\n                width=\"24\"\r\n                height=\"24\"\r\n                fill=\"white\"\r\n                transform=\"translate(4)\"\r\n              />\r\n            </clipPath>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      expandedActiveSvg: (\r\n        <svg\r\n          width=\"128\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 128 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <g clipPath=\"url(#clip0_159_59309)\">\r\n            <path\r\n              d=\"M4.7785 23.0172V23.0217C4.79605 23.0615 4.82056 23.0978 4.85088 23.1289C4.85967 23.1377 4.86894 23.1459 4.87862 23.1537C4.9024 23.1748 4.92888 23.1927 4.95737 23.2069C4.96937 23.2129 4.98025 23.2189 4.99263 23.2238C5.0348 23.2405 5.07965 23.2494 5.125 23.2501H26.875C26.9198 23.2492 26.9642 23.2403 27.0059 23.2238C27.0182 23.2189 27.0291 23.2129 27.0411 23.2069C27.0696 23.1927 27.0961 23.1748 27.1199 23.1537C27.1296 23.1459 27.1388 23.1377 27.1476 23.1289C27.1779 23.0978 27.2024 23.0615 27.22 23.0217V23.0172C27.2356 22.9767 27.2442 22.9338 27.2455 22.8904C27.2455 22.8848 27.2485 22.8803 27.2485 22.8751V11.6251C27.2481 11.5716 27.2361 11.5189 27.2132 11.4706C27.1851 11.4269 27.1537 11.3856 27.1191 11.3468C27.1146 11.3427 27.1127 11.3363 27.1079 11.3322L25.375 9.94506V2.62506C25.375 2.5256 25.3355 2.43022 25.2652 2.35989C25.1948 2.28957 25.0995 2.25006 25 2.25006H18.3625L16.2081 0.813056C16.1465 0.771941 16.0741 0.75 16 0.75C15.9259 0.75 15.8535 0.771941 15.7919 0.813056L13.6375 2.25006H7C6.90054 2.25006 6.80516 2.28957 6.73484 2.35989C6.66451 2.43022 6.625 2.5256 6.625 2.62506V9.94506L4.89062 11.3326C4.88575 11.3367 4.88388 11.3431 4.87938 11.3472C4.85204 11.3723 4.82877 11.4015 4.81038 11.4338C4.80122 11.4458 4.79283 11.4583 4.78525 11.4713C4.76251 11.5194 4.75048 11.5719 4.75 11.6251V22.8751C4.75 22.8803 4.753 22.8848 4.753 22.8904C4.75427 22.9338 4.76289 22.9767 4.7785 23.0172ZM6.766 13.4172H6.769L10.7331 16.5319L5.5 21.9477V12.4051L6.766 13.4172ZM6.00887 22.5001L14.1126 14.1169C14.3574 13.8637 14.6507 13.6623 14.9749 13.5248C15.2992 13.3872 15.6478 13.3163 16 13.3163C16.3522 13.3163 16.7008 13.3872 17.0251 13.5248C17.3493 13.6623 17.6426 13.8637 17.8874 14.1169L25.9911 22.5001H6.00887ZM25.2318 13.4198H25.2347L26.5 12.4051V21.9477L21.2676 16.5349L25.2318 13.4198ZM26.275 11.6251L25.375 12.3447V10.9054L26.275 11.6251ZM16 1.57506L17.0125 2.25006H14.9875L16 1.57506ZM7.375 3.00006H24.625V12.9428L20.7438 15.9931L18.4266 13.5957C18.1119 13.2701 17.7348 13.0112 17.318 12.8343C16.9011 12.6575 16.4528 12.5663 16 12.5663C15.5472 12.5663 15.0989 12.6575 14.682 12.8343C14.2651 13.0112 13.8881 13.2701 13.5734 13.5957L11.2566 15.9927L7.375 12.9428V3.00006ZM6.625 10.9054V12.3447L5.725 11.6251L6.625 10.9054Z\"\r\n              fill=\"#292D32\"\r\n              stroke=\"#292D32\"\r\n              strokeWidth=\"0.4\"\r\n            />\r\n            <path\r\n              d=\"M14.7273 7.41601C14.7739 7.43602 14.8242 7.44634 14.875 7.44634C14.9258 7.44634 14.9761 7.43602 15.0227 7.41601C15.3665 7.2639 15.6946 7.07851 16.0023 6.86251C16.3092 7.07719 16.6357 7.26263 16.9772 7.41638C17.0239 7.43667 17.0741 7.44726 17.125 7.44751C17.1766 7.44781 17.2277 7.43734 17.275 7.41676C17.4903 7.32451 19.375 6.47626 19.375 5.19751V5.07376C19.375 4.89991 19.3408 4.72775 19.2743 4.56712C19.2078 4.40649 19.1103 4.26055 18.9874 4.13761C18.8645 4.01468 18.7185 3.91718 18.5579 3.85067C18.3973 3.78416 18.2251 3.74996 18.0513 3.75001C17.7046 3.75132 17.3722 3.88804 17.125 4.13101C16.9769 3.98809 16.7979 3.88115 16.6018 3.81847C16.4058 3.75579 16.1979 3.73903 15.9944 3.76951C15.9312 3.75824 15.8673 3.75173 15.8031 3.75001C15.4556 3.74889 15.1218 3.88563 14.875 4.13026C14.6893 3.9474 14.4537 3.8235 14.1978 3.77411C13.942 3.72472 13.6772 3.75204 13.4368 3.85265C13.1964 3.95326 12.9911 4.12267 12.8466 4.33959C12.7022 4.55651 12.6251 4.81128 12.625 5.07188V5.19638C12.625 6.47813 14.512 7.32376 14.7273 7.41601ZM17.3898 4.92638L17.6485 4.66763C17.7577 4.56416 17.9023 4.50649 18.0527 4.50649C18.2032 4.50649 18.3478 4.56416 18.457 4.66763C18.5105 4.72083 18.5529 4.78412 18.5817 4.85384C18.6106 4.92356 18.6253 4.99831 18.625 5.07376V5.19751C18.625 5.79751 17.635 6.40838 17.125 6.66001C16.942 6.56873 16.7641 6.46758 16.5921 6.35701C16.9117 6.05255 17.1024 5.63718 17.125 5.19638V5.07188C17.125 5.05951 17.1216 5.04788 17.1213 5.03438C17.171 5.03526 17.2204 5.02616 17.2666 5.00761C17.3128 4.98906 17.3544 4.96144 17.3898 4.92638ZM13.375 5.07188C13.3754 4.95909 13.4091 4.84893 13.4719 4.75523C13.5346 4.66152 13.6237 4.58847 13.7279 4.54524C13.8321 4.502 13.9467 4.49052 14.0574 4.51223C14.1681 4.53394 14.2699 4.58787 14.35 4.66726L14.6084 4.92601C14.6801 4.99378 14.775 5.03155 14.8737 5.03155C14.9724 5.03155 15.0673 4.99378 15.139 4.92601L15.397 4.66763C15.4769 4.58706 15.5789 4.53207 15.6901 4.50965C15.8014 4.48723 15.9167 4.4984 16.0216 4.54174C16.1264 4.58508 16.216 4.65863 16.279 4.75304C16.3419 4.84744 16.3753 4.95843 16.375 5.07188V5.19638C16.375 5.80013 15.3839 6.40913 14.875 6.65888C14.3661 6.40988 13.375 5.80088 13.375 5.19638V5.07188ZM16.0964 16.6234L16 16.7194L15.9036 16.623C15.6657 16.3852 15.3626 16.2232 15.0327 16.1576C14.7027 16.092 14.3607 16.1257 14.0499 16.2545C13.7391 16.3833 13.4735 16.6013 13.2866 16.881C13.0997 17.1607 13 17.4896 13 17.826V18C13 19.6984 15.5605 20.8444 15.8523 20.9696C15.8989 20.9896 15.9492 21 16 21C16.0508 21 16.1011 20.9896 16.1478 20.9696C16.4395 20.8444 19 19.6984 19 18V17.8256C19 17.4892 18.9003 17.1603 18.7134 16.8806C18.5265 16.6009 18.2608 16.3829 17.95 16.2542C17.6391 16.1255 17.2971 16.0918 16.9672 16.1576C16.6372 16.2233 16.3342 16.3854 16.0964 16.6234ZM18.25 17.8256V18C18.25 18.9559 16.7432 19.8634 16 20.2125C15.256 19.8634 13.75 18.9574 13.75 18V17.826C13.75 17.6379 13.8057 17.454 13.9102 17.2976C14.0146 17.1412 14.1632 17.0193 14.3369 16.9473C14.5107 16.8752 14.7019 16.8564 14.8864 16.893C15.0709 16.9297 15.2404 17.0203 15.3734 17.1533L15.7349 17.5151C15.8065 17.583 15.9015 17.6208 16.0002 17.6208C16.0989 17.6208 16.1938 17.583 16.2655 17.5151L16.6266 17.1536C16.7596 17.0207 16.929 16.9301 17.1134 16.8935C17.2978 16.8568 17.489 16.8756 17.6627 16.9475C17.8365 17.0194 17.985 17.1412 18.0895 17.2975C18.194 17.4538 18.2499 17.6376 18.25 17.8256ZM10 9.00001H12.25C12.3495 9.00001 12.4448 8.9605 12.5152 8.89017C12.5855 8.81985 12.625 8.72446 12.625 8.62501C12.625 8.52555 12.5855 8.43017 12.5152 8.35984C12.4448 8.28952 12.3495 8.25001 12.25 8.25001H10C9.90054 8.25001 9.80516 8.28952 9.73484 8.35984C9.66451 8.43017 9.625 8.52555 9.625 8.62501C9.625 8.72446 9.66451 8.81985 9.73484 8.89017C9.80516 8.9605 9.90054 9.00001 10 9.00001ZM10 10.5H13C13.0995 10.5 13.1948 10.4605 13.2652 10.3902C13.3355 10.3198 13.375 10.2245 13.375 10.125C13.375 10.0256 13.3355 9.93017 13.2652 9.85984C13.1948 9.78952 13.0995 9.75001 13 9.75001H10C9.90054 9.75001 9.80516 9.78952 9.73484 9.85984C9.66451 9.93017 9.625 10.0256 9.625 10.125C9.625 10.2245 9.66451 10.3198 9.73484 10.3902C9.80516 10.4605 9.90054 10.5 10 10.5ZM10 12H13.75C13.8495 12 13.9448 11.9605 14.0152 11.8902C14.0855 11.8198 14.125 11.7245 14.125 11.625C14.125 11.5256 14.0855 11.4302 14.0152 11.3598C13.9448 11.2895 13.8495 11.25 13.75 11.25H10C9.90054 11.25 9.80516 11.2895 9.73484 11.3598C9.66451 11.4302 9.625 11.5256 9.625 11.625C9.625 11.7245 9.66451 11.8198 9.73484 11.8902C9.80516 11.9605 9.90054 12 10 12ZM13.375 8.62501C13.375 8.72446 13.4145 8.81985 13.4848 8.89017C13.5552 8.9605 13.6505 9.00001 13.75 9.00001H22C22.0995 9.00001 22.1948 8.9605 22.2652 8.89017C22.3355 8.81985 22.375 8.72446 22.375 8.62501C22.375 8.52555 22.3355 8.43017 22.2652 8.35984C22.1948 8.28952 22.0995 8.25001 22 8.25001H13.75C13.6505 8.25001 13.5552 8.28952 13.4848 8.35984C13.4145 8.43017 13.375 8.52555 13.375 8.62501ZM22 9.75001H14.5C14.4005 9.75001 14.3052 9.78952 14.2348 9.85984C14.1645 9.93017 14.125 10.0256 14.125 10.125C14.125 10.2245 14.1645 10.3198 14.2348 10.3902C14.3052 10.4605 14.4005 10.5 14.5 10.5H22C22.0995 10.5 22.1948 10.4605 22.2652 10.3902C22.3355 10.3198 22.375 10.2245 22.375 10.125C22.375 10.0256 22.3355 9.93017 22.2652 9.85984C22.1948 9.78952 22.0995 9.75001 22 9.75001ZM22 11.25H15.25C15.1505 11.25 15.0552 11.2895 14.9848 11.3598C14.9145 11.4302 14.875 11.5256 14.875 11.625C14.875 11.7245 14.9145 11.8198 14.9848 11.8902C15.0552 11.9605 15.1505 12 15.25 12H22C22.0995 12 22.1948 11.9605 22.2652 11.8902C22.3355 11.8198 22.375 11.7245 22.375 11.625C22.375 11.5256 22.3355 11.4302 22.2652 11.3598C22.1948 11.2895 22.0995 11.25 22 11.25Z\"\r\n              fill=\"#292D32\"\r\n            />\r\n          </g>\r\n          <path\r\n            d=\"M39.1186 17.5V7.31818H45.5021V8.64062H40.6548V11.7429H45.169V13.0604H40.6548V16.1776H45.5618V17.5H39.1186ZM51.9341 11.7628V13.0554H47.4199V11.7628H51.9341ZM55.5708 7.31818V17.5H54.0346V7.31818H55.5708ZM59.1752 12.9659V17.5H57.6887V9.86364H59.1156V11.1065H59.21C59.3857 10.7022 59.6608 10.3774 60.0353 10.1321C60.4132 9.88684 60.8888 9.7642 61.4622 9.7642C61.9825 9.7642 62.4383 9.87358 62.8294 10.0923C63.2205 10.3078 63.5237 10.6293 63.7392 11.0568C63.9546 11.4844 64.0623 12.013 64.0623 12.6428V17.5H62.5758V12.8217C62.5758 12.2682 62.4316 11.8357 62.1433 11.5241C61.8549 11.2093 61.4589 11.0518 60.9551 11.0518C60.6104 11.0518 60.3038 11.1264 60.0353 11.2756C59.7702 11.4247 59.5597 11.6435 59.4039 11.9318C59.2515 12.2169 59.1752 12.5616 59.1752 12.9659ZM72.3152 9.86364L69.546 17.5H67.9551L65.1809 9.86364H66.7768L68.7108 15.7401H68.7903L70.7193 9.86364H72.3152ZM73.7122 17.5V9.86364H75.1987V17.5H73.7122ZM74.4629 8.68537C74.2044 8.68537 73.9823 8.59919 73.7967 8.42685C73.6144 8.25118 73.5233 8.04238 73.5233 7.80043C73.5233 7.55516 73.6144 7.34635 73.7967 7.17401C73.9823 6.99834 74.2044 6.91051 74.4629 6.91051C74.7214 6.91051 74.9418 6.99834 75.1241 7.17401C75.3097 7.34635 75.4025 7.55516 75.4025 7.80043C75.4025 8.04238 75.3097 8.25118 75.1241 8.42685C74.9418 8.59919 74.7214 8.68537 74.4629 8.68537ZM80.7631 9.86364V11.0568H76.592V9.86364H80.7631ZM77.7106 8.03409H79.1971V15.2578C79.1971 15.5462 79.2402 15.7633 79.3263 15.9091C79.4125 16.0516 79.5236 16.1494 79.6594 16.2024C79.7987 16.2521 79.9495 16.277 80.1119 16.277C80.2312 16.277 80.3356 16.2687 80.4251 16.2521C80.5146 16.2356 80.5842 16.2223 80.6339 16.2124L80.9023 17.4403C80.8162 17.4735 80.6935 17.5066 80.5344 17.5398C80.3754 17.5762 80.1765 17.5961 79.9379 17.5994C79.5468 17.6061 79.1822 17.5365 78.8441 17.3906C78.506 17.2448 78.2326 17.0194 78.0238 16.7145C77.815 16.4096 77.7106 16.0268 77.7106 15.5661V8.03409ZM85.6167 17.6541C84.8643 17.6541 84.2163 17.4934 83.6728 17.1719C83.1325 16.8471 82.7149 16.3913 82.4199 15.8047C82.1283 15.2147 81.9824 14.5237 81.9824 13.7315C81.9824 12.9493 82.1283 12.2599 82.4199 11.6634C82.7149 11.0668 83.1259 10.6011 83.6529 10.2663C84.1832 9.93158 84.803 9.7642 85.5123 9.7642C85.9431 9.7642 86.3607 9.83546 86.7651 9.97798C87.1694 10.1205 87.5324 10.3442 87.8539 10.6491C88.1754 10.9541 88.4289 11.3501 88.6145 11.8374C88.8001 12.3213 88.8929 12.9096 88.8929 13.6023V14.1293H82.8226V13.0156H87.4363C87.4363 12.6245 87.3567 12.2782 87.1976 11.9766C87.0385 11.6716 86.8148 11.4313 86.5265 11.2557C86.2414 11.08 85.9067 10.9922 85.5222 10.9922C85.1046 10.9922 84.74 11.0949 84.4284 11.3004C84.1202 11.5026 83.8816 11.7678 83.7125 12.0959C83.5468 12.4207 83.464 12.7737 83.464 13.1548V14.0249C83.464 14.5353 83.5534 14.9695 83.7324 15.3274C83.9147 15.6854 84.1683 15.9588 84.4931 16.1477C84.8179 16.3333 85.1974 16.4261 85.6316 16.4261C85.9133 16.4261 86.1702 16.3864 86.4022 16.3068C86.6342 16.224 86.8347 16.1013 87.0037 15.9389C87.1728 15.7765 87.302 15.576 87.3915 15.3374L88.7985 15.5909C88.6858 16.0052 88.4836 16.3681 88.1919 16.6797C87.9036 16.9879 87.5407 17.2282 87.1032 17.4006C86.669 17.5696 86.1735 17.6541 85.6167 17.6541ZM96.2646 11.728L94.9173 11.9666C94.8609 11.7943 94.7714 11.6302 94.6488 11.4744C94.5295 11.3187 94.3671 11.1911 94.1616 11.0916C93.9561 10.9922 93.6992 10.9425 93.391 10.9425C92.9701 10.9425 92.6187 11.0369 92.337 11.2259C92.0553 11.4115 91.9144 11.6518 91.9144 11.9467C91.9144 12.2019 92.0089 12.4074 92.1978 12.5632C92.3867 12.719 92.6916 12.8466 93.1126 12.946L94.3256 13.2244C95.0283 13.3868 95.552 13.6371 95.8967 13.9751C96.2414 14.3132 96.4137 14.7524 96.4137 15.2926C96.4137 15.75 96.2811 16.1577 96.016 16.5156C95.7541 16.8703 95.3879 17.1487 94.9173 17.3509C94.4499 17.553 93.908 17.6541 93.2915 17.6541C92.4364 17.6541 91.7388 17.4718 91.1985 17.1072C90.6583 16.7393 90.3268 16.2173 90.2042 15.5412L91.641 15.3224C91.7305 15.697 91.9144 15.9804 92.1928 16.1726C92.4712 16.3615 92.8342 16.456 93.2816 16.456C93.7688 16.456 94.1583 16.3549 94.4499 16.1527C94.7416 15.9472 94.8874 15.697 94.8874 15.402C94.8874 15.1634 94.7979 14.9628 94.619 14.8004C94.4433 14.638 94.1732 14.5154 93.8086 14.4325L92.516 14.1491C91.8034 13.9867 91.2764 13.7282 90.935 13.3736C90.5969 13.0189 90.4279 12.5698 90.4279 12.0263C90.4279 11.5755 90.5539 11.1811 90.8058 10.843C91.0576 10.505 91.4057 10.2415 91.8498 10.0526C92.2939 9.86032 92.8027 9.7642 93.3761 9.7642C94.2013 9.7642 94.851 9.94318 95.3249 10.3011C95.7989 10.6558 96.1121 11.1314 96.2646 11.728Z\"\r\n            fill=\"#262626\"\r\n          />\r\n          <defs>\r\n            <clipPath id=\"clip0_159_59309\">\r\n              <rect\r\n                width=\"24\"\r\n                height=\"24\"\r\n                fill=\"white\"\r\n                transform=\"translate(4)\"\r\n              />\r\n            </clipPath>\r\n          </defs>\r\n        </svg>\r\n      ),\r\n      hasOwnBackground: true,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <nav\r\n      className={`fixed left-0 top-20 bottom-0 z-40 bg-white shadow-md transition-all duration-500 ease-in-out ${expanded ? \"w-\" : \"w-20\"\r\n        } hidden md:block`}\r\n      onMouseEnter={onExpand}\r\n      onMouseLeave={onCollapse}\r\n    >\r\n      <div className=\"p-4 flex flex-col h-full\">\r\n        <ul className=\"space-y-6 mt-2\">\r\n          {navItems.map((item) => {\r\n            const isActive = activePage === item.id;\r\n\r\n            return (\r\n              <li key={item.id}>\r\n                <a\r\n                  href={\r\n                    item.id === \"home\"\r\n                      ? \"/home\"\r\n                      : item.id === \"vendor\"\r\n                        ? \"/vendor/dashboard\"\r\n                        : item.id === \"info\"\r\n                          ? \"/home/<USER>\"\r\n                          : item.id === \"Wedzat Live\"\r\n                            ? \"/home/<USER>\"\r\n                            : \"#\"\r\n                  }\r\n                  className={`flex items-center ${expanded ? \"p-3\" : \"\"}`}\r\n                  onClick={(e) => {\r\n                    if (item.id !== \"home\" && item.id !== \"vendor\" && item.id !== \"info\" && item.id !== \"Wedzat Live\") {\r\n                      e.preventDefault();\r\n                      setActivePage(item.id);\r\n                    }\r\n                  }}\r\n                >\r\n                  {/* Just show the appropriate SVG based on state */}\r\n                  {expanded\r\n                    ? // EXPANDED STATE - use expanded SVGs if available\r\n                    isActive\r\n                      ? item.expandedActiveSvg || item.activeSvg\r\n                      : item.expandedDefaultSvg || item.defaultSvg\r\n                    : // COLLAPSED STATE - use regular SVGs\r\n                    isActive\r\n                      ? item.activeSvg\r\n                      : item.defaultSvg}\r\n\r\n                  {/* Only show label text if SVG doesn't include it already */}\r\n                  {expanded && !item.expandedActiveSvg && (\r\n                    <span className=\"ml-3 whitespace-nowrap\">{item.label}</span>\r\n                  )}\r\n                </a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n// Mobile Navigation Component\r\nexport const MobileNavigation: React.FC = () => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const { userProfile } = useAuth();\r\n  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(undefined);\r\n  const [userName, setUserName] = useState<string>('User');\r\n\r\n  // Directly fetch user profile data from API\r\n  useEffect(() => {\r\n    const fetchUserProfile = async () => {\r\n      try {\r\n        // Get token from localStorage\r\n        const token = localStorage.getItem('token') ||\r\n          localStorage.getItem('jwt_token') ||\r\n          localStorage.getItem('wedzat_token');\r\n\r\n        if (!token) {\r\n          console.warn('Mobile nav: No token found in localStorage');\r\n          return;\r\n        }\r\n\r\n        // Make API call to get user profile\r\n        const response = await axios.get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user', {\r\n          headers: { Authorization: `Bearer ${token}` }\r\n        });\r\n\r\n        console.log('Mobile nav: User profile from API:', response.data);\r\n\r\n        // Set avatar URL and user name\r\n        if (response.data && response.data.user_avatar) {\r\n          setAvatarUrl(response.data.user_avatar);\r\n          localStorage.setItem('user_avatar', response.data.user_avatar);\r\n        }\r\n\r\n        if (response.data && response.data.name) {\r\n          setUserName(response.data.name);\r\n        }\r\n      } catch (error) {\r\n        console.error('Mobile nav: Error fetching user profile:', error);\r\n\r\n        // Try to get avatar from localStorage as fallback\r\n        const storedAvatar = localStorage.getItem('user_avatar');\r\n        if (storedAvatar) {\r\n          console.log('Mobile nav: Using avatar from localStorage');\r\n          setAvatarUrl(storedAvatar);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Call the function immediately\r\n    fetchUserProfile();\r\n\r\n    // Set up a refresh interval\r\n    const refreshInterval = setInterval(fetchUserProfile, 30000); // Refresh every 30 seconds\r\n\r\n    // Also use userProfile from AuthContext if available\r\n    if (userProfile) {\r\n      if (userProfile.user_avatar) {\r\n        setAvatarUrl(userProfile.user_avatar);\r\n      }\r\n      if (userProfile.name) {\r\n        setUserName(userProfile.name);\r\n      }\r\n    }\r\n\r\n    // Clean up the interval on component unmount\r\n    return () => clearInterval(refreshInterval);\r\n  }, [userProfile]);\r\n\r\n  return (\r\n    <>\r\n      {/* Bottom tab navigation for mobile */}\r\n      <div className=\"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-30\">\r\n        <div className=\"grid grid-cols-7\">\r\n          <a\r\n            href=\"/home\"\r\n            className=\"flex flex-col items-center py-2 text-red-600\"\r\n          >\r\n            <Home size={20} />\r\n            <span className=\"text-xs mt-1\">Home</span>\r\n          </a>\r\n          <a href=\"/vendor/dashboard\" className=\"flex flex-col items-center py-2 text-gray-500\">\r\n            <ShoppingBag size={20} />\r\n            <span className=\"text-xs mt-1\">Vendor</span>\r\n          </a>\r\n          <a href=\"#\" className=\"flex flex-col items-center py-2 text-gray-500\">\r\n            <Calendar size={20} />\r\n            <span className=\"text-xs mt-1\">Weddings</span>\r\n          </a>\r\n          <a href=\"/home/<USER>\" className=\"flex flex-col items-center py-2 text-gray-500\">\r\n            <svg\r\n              width=\"20\"\r\n              height=\"20\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"text-gray-500\"\r\n            >\r\n              <path\r\n                d=\"M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <path\r\n                d=\"M12 14.75C9.66 14.75 7.75 12.84 7.75 10.5C7.75 8.16 9.66 6.25 12 6.25C14.34 6.25 16.25 8.16 16.25 10.5C16.25 12.84 14.34 14.75 12 14.75ZM12 7.75C10.48 7.75 9.25 8.98 9.25 10.5C9.25 12.02 10.48 13.25 12 13.25C13.52 13.25 14.75 12.02 14.75 10.5C14.75 8.98 13.52 7.75 12 7.75Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <path\r\n                d=\"M15.42 19.75C15.3 19.75 15.19 19.72 15.08 19.68C14.75 19.55 14.54 19.24 14.54 18.9V17.88C14.54 16.85 13.71 16.01 12.67 16.01H11.31C10.28 16.01 9.44 16.85 9.44 17.88V18.9C9.44 19.24 9.23 19.55 8.9 19.68C8.57 19.81 8.2 19.74 7.94 19.5C6.99 18.63 6.2 17.58 5.62 16.39C5.04 15.2 4.75 13.9 4.75 12.58C4.75 7.95 8.52 4.17 13.16 4.17C17.8 4.17 21.57 7.95 21.57 12.58C21.57 13.91 21.27 15.21 20.69 16.4C20.11 17.59 19.31 18.65 18.36 19.51C18.19 19.67 17.98 19.75 17.76 19.75C17.65 19.75 17.53 19.73 17.42 19.68C17.09 19.55 16.88 19.24 16.88 18.9V17.88C16.88 16.85 16.04 16.01 15.01 16.01H15C14.76 16.01 14.54 15.8 14.54 15.55C14.54 15.3 14.75 15.09 15 15.09H15.01C16.54 15.09 17.79 16.35 17.79 17.88V18.32C18.39 17.64 18.89 16.86 19.26 16.01C19.75 14.98 20 13.86 20 12.7C20 8.83 16.85 5.67 12.98 5.67C9.11 5.67 5.96 8.82 5.96 12.7C5.96 13.86 6.21 14.98 6.7 16.01C7.07 16.86 7.57 17.64 8.17 18.32V17.88C8.17 16.35 9.42 15.09 10.95 15.09H12.31C13.84 15.09 15.09 16.35 15.09 17.88V18.32C15.69 17.64 16.19 16.86 16.56 16.01C16.65 15.79 16.9 15.67 17.12 15.76C17.34 15.85 17.46 16.1 17.37 16.32C16.95 17.28 16.37 18.15 15.66 18.9C15.59 19.02 15.51 19.13 15.42 19.23V19.75Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n            </svg>\r\n            <span className=\"text-xs mt-1\">Tool</span>\r\n          </a>\r\n          <a href=\"/home/<USER>\" className=\"flex flex-col items-center py-2 text-gray-500\">\r\n            <Info size={20} />\r\n            <span className=\"text-xs mt-1\">Info</span>\r\n          </a>\r\n          <a\r\n            href=\"#\"\r\n            className=\"flex flex-col items-center py-2 text-gray-500 relative\"\r\n          >\r\n            <MessageCircle size={20} />\r\n            <span className=\"text-xs mt-1\">Chat</span>\r\n            <span className=\"absolute top-0 right-5 h-4 w-4 bg-red-500 rounded-full text-white text-xs flex items-center justify-center\">\r\n              2\r\n            </span>\r\n          </a>\r\n          <a href=\"/userProfile\" className=\"flex flex-col items-center py-2 text-gray-500\">\r\n            {avatarUrl ? (\r\n              <div className=\"w-5 h-5 rounded-full overflow-hidden\">\r\n                <img\r\n                  src={avatarUrl}\r\n                  alt={userName || userProfile?.name || \"User\"}\r\n                  className=\"w-full h-full object-cover\"\r\n                />\r\n              </div>\r\n            ) : (\r\n              <User size={20} />\r\n            )}\r\n            <span className=\"text-xs mt-1\">Profile</span>\r\n          </a>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile menu button */}\r\n      <button\r\n        className=\"md:hidden fixed bottom-20 right-4 z-50 bg-red-600 text-white p-3 rounded-full shadow-lg\"\r\n        onClick={() => setIsOpen(!isOpen)}\r\n      >\r\n        {isOpen ? <X size={24} /> : <Menu size={24} />}\r\n      </button>\r\n\r\n      {/* Mobile navigation menu */}\r\n      {isOpen && (\r\n        <div className=\"fixed inset-0 z-40 bg-white md:hidden\">\r\n          <div className=\"flex flex-col h-full\">\r\n            <div className=\"p-4 border-b border-gray-200\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center\">\r\n                  <svg\r\n                    width=\"24\"\r\n                    height=\"24\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"text-red-600\"\r\n                  >\r\n                    <path\r\n                      d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\r\n                      fill=\"currentColor\"\r\n                    />\r\n                  </svg>\r\n                  <h1 className=\"text-2xl font-bold text-red-600 ml-2\">\r\n                    WEDZAT\r\n                  </h1>\r\n                </div>\r\n                <button onClick={() => setIsOpen(false)}>\r\n                  <X size={24} className=\"text-gray-700\" />\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-4 flex-1 overflow-y-auto\">\r\n              <div className=\"flex flex-col space-y-6\">\r\n                <div>\r\n                  <h3 className=\"text-xs uppercase text-gray-500 font-medium mb-2\">\r\n                    Navigation\r\n                  </h3>\r\n                  <ul className=\"space-y-2\">\r\n                    <li>\r\n                      <a\r\n                        href=\"/home\"\r\n                        className=\"flex items-center p-3 rounded-lg bg-red-600 text-white\"\r\n                      >\r\n                        <Home size={20} />\r\n                        <span className=\"ml-3\">Home</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"/vendor/dashboard\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <ShoppingBag size={20} />\r\n                        <span className=\"ml-3\">Vendor</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <Users size={20} />\r\n                        <span className=\"ml-3\">Flashes</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <Calendar size={20} />\r\n                        <span className=\"ml-3\">My Weddings</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"/home/<USER>\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <Video size={20} />\r\n                        <span className=\"ml-3\">Wedzat Live</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <Mail size={20} />\r\n                        <span className=\"ml-3\">E-Invites</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"/home/<USER>\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <Info size={20} />\r\n                        <span className=\"ml-3\">Information</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <svg\r\n                          width=\"20\"\r\n                          height=\"20\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          fill=\"none\"\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          className=\"mr-0\"\r\n                        >\r\n                          <path\r\n                            d=\"M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z\"\r\n                            fill=\"currentColor\"\r\n                          />\r\n                          <path\r\n                            d=\"M12 14.75C9.66 14.75 7.75 12.84 7.75 10.5C7.75 8.16 9.66 6.25 12 6.25C14.34 6.25 16.25 8.16 16.25 10.5C16.25 12.84 14.34 14.75 12 14.75ZM12 7.75C10.48 7.75 9.25 8.98 9.25 10.5C9.25 12.02 10.48 13.25 12 13.25C13.52 13.25 14.75 12.02 14.75 10.5C14.75 8.98 13.52 7.75 12 7.75Z\"\r\n                            fill=\"currentColor\"\r\n                          />\r\n                          <path\r\n                            d=\"M15.42 19.75C15.3 19.75 15.19 19.72 15.08 19.68C14.75 19.55 14.54 19.24 14.54 18.9V17.88C14.54 16.85 13.71 16.01 12.67 16.01H11.31C10.28 16.01 9.44 16.85 9.44 17.88V18.9C9.44 19.24 9.23 19.55 8.9 19.68C8.57 19.81 8.2 19.74 7.94 19.5C6.99 18.63 6.2 17.58 5.62 16.39C5.04 15.2 4.75 13.9 4.75 12.58C4.75 7.95 8.52 4.17 13.16 4.17C17.8 4.17 21.57 7.95 21.57 12.58C21.57 13.91 21.27 15.21 20.69 16.4C20.11 17.59 19.31 18.65 18.36 19.51C18.19 19.67 17.98 19.75 17.76 19.75C17.65 19.75 17.53 19.73 17.42 19.68C17.09 19.55 16.88 19.24 16.88 18.9V17.88C16.88 16.85 16.04 16.01 15.01 16.01H15C14.76 16.01 14.54 15.8 14.54 15.55C14.54 15.3 14.75 15.09 15 15.09H15.01C16.54 15.09 17.79 16.35 17.79 17.88V18.32C18.39 17.64 18.89 16.86 19.26 16.01C19.75 14.98 20 13.86 20 12.7C20 8.83 16.85 5.67 12.98 5.67C9.11 5.67 5.96 8.82 5.96 12.7C5.96 13.86 6.21 14.98 6.7 16.01C7.07 16.86 7.57 17.64 8.17 18.32V17.88C8.17 16.35 9.42 15.09 10.95 15.09H12.31C13.84 15.09 15.09 16.35 15.09 17.88V18.32C15.69 17.64 16.19 16.86 16.56 16.01C16.65 15.79 16.9 15.67 17.12 15.76C17.34 15.85 17.46 16.1 17.37 16.32C16.95 17.28 16.37 18.15 15.66 18.9C15.59 19.02 15.51 19.13 15.42 19.23V19.75Z\"\r\n                            fill=\"currentColor\"\r\n                          />\r\n                        </svg>\r\n                        <span className=\"ml-3\">Wedding Tool</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <MessageCircle size={20} />\r\n                        <span className=\"ml-3\">Messages</span>\r\n                        <span className=\"ml-auto bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                          2\r\n                        </span>\r\n                      </a>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n\r\n                <div>\r\n                  <h3 className=\"text-xs uppercase text-gray-500 font-medium mb-2\">\r\n                    Account\r\n                  </h3>\r\n                  <ul className=\"space-y-2\">\r\n                    <li>\r\n                      <a\r\n                        href=\"/userProfile\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        {avatarUrl ? (\r\n                          <div className=\"w-5 h-5 rounded-full overflow-hidden\">\r\n                            <img\r\n                              src={avatarUrl}\r\n                              alt={userName || userProfile?.name || \"User\"}\r\n                              className=\"w-full h-full object-cover\"\r\n                            />\r\n                          </div>\r\n                        ) : (\r\n                          <User size={20} />\r\n                        )}\r\n                        <span className=\"ml-3\">Profile</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <Settings size={20} />\r\n                        <span className=\"ml-3\">Settings</span>\r\n                      </a>\r\n                    </li>\r\n                    <li>\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        <LogOut size={20} />\r\n                        <span className=\"ml-3\">Logout</span>\r\n                      </a>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-4 border-t border-gray-200\">\r\n              <div className=\"bg-purple-100 p-4 rounded-lg\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"w-10 h-10 rounded-full bg-purple-200 flex items-center justify-center text-purple-700\">\r\n                    <span>🤖</span>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <h4 className=\"font-medium\">WedzBot</h4>\r\n                    <p className=\"text-xs text-gray-500\">\r\n                      Your wedding assistant\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <button\r\n                  className=\"mt-2 bg-purple-600 text-white text-sm rounded-lg py-2 px-3 w-full\"\r\n                  onClick={() => {\r\n                    // Use the global function to open Botpress chat\r\n                    if (typeof window !== 'undefined' && window.openBotpressChat) {\r\n                      window.openBotpressChat();\r\n                    } else {\r\n                      console.error('openBotpressChat function not available');\r\n                    }\r\n                  }}\r\n                >\r\n                  Chat Now\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\ninterface RightSidebarProps {\r\n  expanded: boolean;\r\n  onExpand: () => void;\r\n  onCollapse: () => void;\r\n}\r\n\r\nexport const RightSidebar: React.FC<RightSidebarProps> = ({\r\n  expanded,\r\n  onExpand,\r\n  onCollapse,\r\n}) => {\r\n  const [isSmallScreen, setIsSmallScreen] = useState(false);\r\n  const [sidebarVisible, setSidebarVisible] = useState(true);\r\n\r\n  // Function to check screen size and set state\r\n  const checkScreenSize = () => {\r\n    const smallScreen = window.innerWidth < 1024;\r\n    setIsSmallScreen(smallScreen);\r\n\r\n    // Only auto-collapse on initial load\r\n    if (smallScreen) {\r\n      setSidebarVisible(false);\r\n    } else {\r\n      setSidebarVisible(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Set initial value\r\n    if (typeof window !== \"undefined\") {\r\n      checkScreenSize();\r\n\r\n      // Add event listener for window resize\r\n      window.addEventListener(\"resize\", checkScreenSize);\r\n\r\n      // Cleanup\r\n      return () => window.removeEventListener(\"resize\", checkScreenSize);\r\n    }\r\n  }, []);\r\n\r\n  const toggleSidebar = () => {\r\n    console.log(\"Toggle sidebar called, current state:\", sidebarVisible);\r\n    setSidebarVisible((prevState) => !prevState);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Hamburger toggle button for small screens */}\r\n      {isSmallScreen && (\r\n        <button\r\n          onClick={toggleSidebar}\r\n          className=\"fixed right-4 top-24 z-30 bg-white p-2 rounded-full shadow-md hover:bg-gray-100 active:bg-gray-200\"\r\n          style={{ display: isSmallScreen ? \"flex\" : \"none\" }}\r\n        >\r\n          {sidebarVisible ? <X size={24} /> : <Menu size={24} />}\r\n        </button>\r\n      )}\r\n\r\n      <aside\r\n        className={`fixed right-0 top-20 bottom-0 bg-white shadow-md p-4 z-20 transition-all duration-300 ease-in-out overflow-y-auto scrollbar-hide`}\r\n        style={{\r\n          width: isSmallScreen ? \"280px\" : expanded ? \"auto\" : \"320px\",\r\n          maxWidth: isSmallScreen ? \"280px\" : expanded ? \"480px\" : \"320px\",\r\n          boxShadow: \"0 0 10px rgba(0,0,0,0.2)\",\r\n          transform: sidebarVisible ? \"translateX(0)\" : \"translateX(100%)\",\r\n          height: \"calc(100vh - 80px)\",\r\n        }}\r\n        onMouseEnter={!isSmallScreen ? onExpand : undefined}\r\n        onMouseLeave={!isSmallScreen ? onCollapse : undefined}\r\n      >\r\n        <div className=\"space-y-6\">\r\n          {/* WedzBot - Updated to match image 2 */}\r\n          <div className=\"space-y-1\">\r\n            {/* Super Chatbot header outside the container */}\r\n            <div className=\"flex items-center mb-2\">\r\n              <span\r\n                style={{\r\n                  fontFamily: \"Inter\",\r\n                  fontWeight: 600,\r\n                  fontSize: \"20px\",\r\n                  lineHeight: \"18px\",\r\n                  letterSpacing: \"0%\",\r\n                  verticalAlign: \"middle\",\r\n                  color: \"#333333\",\r\n                }}\r\n              >\r\n                Super Chatbot\r\n              </span>\r\n              <img\r\n                src=\"\\pics\\ranking.png\"\r\n                alt=\"Building Icon\"\r\n                className=\"ml-1 h-4 w-4\"\r\n              />\r\n            </div>\r\n\r\n            {/* Main purple container with proper width */}\r\n            <div\r\n              className=\"rounded-lg overflow-visible relative\"\r\n              style={{\r\n                backgroundColor: \"#28163E\",\r\n                width: \"100%\",\r\n                minHeight: \"180px\",\r\n                transition: \"width 300ms ease-in-out\",\r\n              }}\r\n            >\r\n              <div className=\"p-4 pb-6 text-white\">\r\n                <div className=\"flex\">\r\n                  {/* Left content - limited width to avoid overlap */}\r\n                  <div style={{ width: \"60%\" }}>\r\n                    {/* WedzBot with star icon */}\r\n                    <h3 className=\"flex items-center mb-2\">\r\n                      <span\r\n                        style={{\r\n                          fontFamily: \"Inter\",\r\n                          fontWeight: \"bold\",\r\n                          fontSize: \"18px\",\r\n                          lineHeight: \"18px\",\r\n                          letterSpacing: \"0%\",\r\n                          verticalAlign: \"middle\",\r\n                          color: \"white\",\r\n                        }}\r\n                      >\r\n                        WedzBot\r\n                      </span>\r\n                      <span className=\"ml-1\">\r\n                        <svg\r\n                          width=\"16\"\r\n                          height=\"16\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          fill=\"none\"\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                        >\r\n                          <rect width=\"24\" height=\"24\" fill=\"#28163E\" />\r\n                          <path\r\n                            d=\"M12 4L14.5 9.5L20 10.5L16 14.5L17 20L12 17.5L7 20L8 14.5L4 10.5L9.5 9.5L12 4Z\"\r\n                            fill=\"#FFCB11\"\r\n                          />\r\n                          <rect\r\n                            x=\"6\"\r\n                            y=\"2\"\r\n                            width=\"2\"\r\n                            height=\"6\"\r\n                            rx=\"1\"\r\n                            fill=\"white\"\r\n                          />\r\n                          <rect\r\n                            x=\"16\"\r\n                            y=\"2\"\r\n                            width=\"2\"\r\n                            height=\"6\"\r\n                            rx=\"1\"\r\n                            fill=\"white\"\r\n                          />\r\n                          <rect\r\n                            x=\"11\"\r\n                            y=\"1\"\r\n                            width=\"2\"\r\n                            height=\"3\"\r\n                            rx=\"1\"\r\n                            fill=\"white\"\r\n                          />\r\n                        </svg>\r\n                      </span>\r\n                    </h3>\r\n\r\n                    {/* Description with two color segments */}\r\n                    <p\r\n                      style={{\r\n                        fontFamily: \"Roboto\",\r\n                        fontSize: \"13px\",\r\n                        lineHeight: \"18px\",\r\n                        letterSpacing: \"0%\",\r\n                        verticalAlign: \"middle\",\r\n                        fontWeight: 400,\r\n                        marginBottom:\r\n                          \"20px\" /* Increased bottom margin for more space */,\r\n                      }}\r\n                    >\r\n                      The\r\n                      <span\r\n                        style={{\r\n                          fontWeight: 600,\r\n                        }}\r\n                      >\r\n                        {\" \"}\r\n                        lightning-fast chatbot\r\n                      </span>{\" \"}\r\n                      that helps you discover the absolute best of everything!\"\r\n                    </p>\r\n\r\n                    {/* Button */}\r\n                    <button\r\n                      onClick={() => {\r\n                        // Use the global function to open Botpress chat\r\n                        if (typeof window !== 'undefined' && window.openBotpressChat) {\r\n                          window.openBotpressChat();\r\n                        } else {\r\n                          console.error('openBotpressChat function not available');\r\n                        }\r\n                      }}\r\n                      style={{\r\n                        fontFamily: \"Inter\",\r\n                        fontWeight: 600,\r\n                        fontSize: \"17px\",\r\n                        lineHeight: \"16px\",\r\n                        letterSpacing: \"0%\",\r\n                        verticalAlign: \"middle\",\r\n                        backgroundColor: \"#5D3A98\",\r\n                        color: \"white\",\r\n                        borderRadius: \"6px\",\r\n                        padding: \"12px 16px\",\r\n                        width: \"100%\",\r\n                        maxWidth: \"220px\",\r\n                        cursor: \"pointer\",\r\n                      }}\r\n                    >\r\n                      Chat with Wedzbot\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Robot image positioned on the right with proper sizing */}\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  right: \"20px\",\r\n                  top: \"30px\",\r\n                  bottom: \"60px\",\r\n                  width: \"30%\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                }}\r\n              >\r\n                <img\r\n                  src=\"\\pics\\image 12.png\"\r\n                  alt=\"WedzBot Robot\"\r\n                  style={{\r\n                    height: \"calc(100% + 40px)\",\r\n                    objectFit: \"contain\",\r\n                    transform: \"scale(1.6)\",\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"space-y-1\">\r\n            {/* Wedding Counsellors header outside the container */}\r\n            <div className=\"flex items-center  mb-1\">\r\n              <span\r\n                style={{\r\n                  fontFamily: \"Inter\",\r\n                  fontWeight: 600,\r\n                  fontSize: \"20px\",\r\n                  lineHeight: \"18px\",\r\n                  letterSpacing: \"0%\",\r\n                  verticalAlign: \"middle\",\r\n                  color: \"#333333\",\r\n                }}\r\n              >\r\n                Wedding Counsellors\r\n              </span>\r\n              <img\r\n                src=\"\\pics\\monitor-recorder.png\"\r\n                alt=\"Computer Icon\"\r\n                className=\"h-5 w-5 ml-1\"\r\n              />\r\n            </div>\r\n\r\n            {/* Main blue container with proper width */}\r\n            <div\r\n              className=\"rounded-lg overflow-visible relative\"\r\n              style={{\r\n                backgroundColor: \"#00326B\",\r\n                width: \"100%\",\r\n                minHeight: \"180px\",\r\n                transition: \"width 300ms ease-in-out\",\r\n              }}\r\n            >\r\n              <div className=\"p-4 pb-6 text-white\">\r\n                <div className=\"flex\">\r\n                  {/* Left content - limited width to avoid overlap */}\r\n                  <div style={{ width: \"60%\" }}>\r\n                    {/* Wedding Counsellors title */}\r\n                    <h3\r\n                      style={{\r\n                        fontFamily: \"Inter\",\r\n                        fontWeight: 700,\r\n                        fontSize: \"16px\",\r\n                        lineHeight: \"18px\",\r\n                        letterSpacing: \"0%\",\r\n                        verticalAlign: \"middle\",\r\n                        color: \"white\",\r\n                        marginBottom: \"12px\",\r\n                      }}\r\n                    >\r\n                      Wedding Counsellors\r\n                    </h3>\r\n\r\n                    {/* Description with proper styling */}\r\n                    <p\r\n                      style={{\r\n                        fontFamily: \"Roboto\",\r\n                        fontSize: \"16px\",\r\n                        lineHeight: \"18px\",\r\n                        letterSpacing: \"0%\",\r\n                        verticalAlign: \"middle\",\r\n                        fontWeight: 400,\r\n                        marginBottom: \"24px\",\r\n                      }}\r\n                    >\r\n                      \"\r\n                      <span\r\n                        style={{\r\n                          fontWeight: 500,\r\n                        }}\r\n                      >\r\n                        Plan Your Wedding Plans Perfectly\r\n                      </span>{\" \"}\r\n                      with Our Expertise!\"\r\n                    </p>\r\n\r\n                    {/* Button */}\r\n                    <button\r\n                      style={{\r\n                        fontFamily: \"Inter\",\r\n                        fontWeight: 600,\r\n                        fontSize: \"16px\",\r\n                        lineHeight: \"18px\",\r\n                        letterSpacing: \"0%\",\r\n                        verticalAlign: \"middle\",\r\n                        backgroundColor: \"#FFFFFF\",\r\n                        color: \"#000000\",\r\n                        borderRadius: \"6px\",\r\n                        padding: \"12px 16px\",\r\n                        width: \"100%\",\r\n                        maxWidth: \"220px\",\r\n                        border: \"2px solid #FFFFFF\",\r\n                      }}\r\n                    >\r\n                      Call with Expertise\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Counsellor character image positioned on the right */}\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  right: \"20px\",\r\n                  top: \"20px\",\r\n                  bottom: \"50px\",\r\n                  width: \"30%\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                }}\r\n              >\r\n                <img\r\n                  src=\"\\pics\\wedcouncler.png\"\r\n                  alt=\"Wedding Counsellor\"\r\n                  style={{\r\n                    height: \"calc(80% + 40px)\",\r\n                    objectFit: \"contain\",\r\n                    transform: \"scale(1.7)\",\r\n                  }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            className=\"bg-white rounded-lg overflow-hidden shadow border border-gray-200\"\r\n            style={{\r\n              width: \"100%\",\r\n              transition: \"width 300ms ease-in-out\",\r\n            }}\r\n          >\r\n            <div className=\"p-4\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <span\r\n                  style={{\r\n                    fontFamily: \"Inter\",\r\n                    fontWeight: 600,\r\n                    fontSize: \"20px\",\r\n                    lineHeight: \"18px\",\r\n                    letterSpacing: \"0%\",\r\n                    verticalAlign: \"middle\",\r\n                    color: \"#333333\",\r\n                  }}\r\n                >\r\n                  Wedding Counsellors\r\n                </span>\r\n                <a href=\"#\" className=\"text-blue-600 text-sm\">\r\n                  See all\r\n                </a>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <img\r\n                  src=\"./pics/Councellor.png\"\r\n                  alt=\"Wedding Counsellor\"\r\n                  className=\"w-full h-full overflow-hidden rounded-lg\"\r\n                />\r\n                <p className=\"text-sm text-gray-600 mt-2\">\r\n                  Top 10 Wedding Counsellors that helps you!\r\n                </p>\r\n                <div className=\"mt-2 flex items-center\">\r\n                  <div className=\"flex -space-x-2\">\r\n                    <img\r\n                      src=\"https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=150&auto=format'\"\r\n                      alt=\"User 1\"\r\n                      className=\"w-8 h-8 rounded-full border-2 border-white\"\r\n                    />\r\n                    <img\r\n                      src=\"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format\"\r\n                      alt=\"User 2\"\r\n                      className=\"w-8 h-8 rounded-full border-2 border-white\"\r\n                    />\r\n                    <img\r\n                      src=\"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=150&auto=format\"\r\n                      alt=\"User 3\"\r\n                      className=\"w-8 h-8 rounded-full border-2 border-white\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"ml-2 text-xs text-gray-500 flex items-center\">\r\n                    +7\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            className=\"relative rounded-lg shadow-lg p-6 mx-auto\"\r\n            style={{\r\n              backgroundColor: \"#FFAA1F\",\r\n              width: \"100%\",\r\n              transition: \"width 300ms ease-in-out\",\r\n            }}\r\n          >\r\n            {/* Title */}\r\n            <h2 className=\"text-black text-xl font-bold text-center\">\r\n              Wedding Planning Tool\r\n            </h2>\r\n\r\n            {/* Floating Buttons */}\r\n            <div className=\"relative flex flex-wrap justify-center gap-4 mt-6\">\r\n              <div\r\n                className=\"bg-black text-yellow-400 font-semibold px-4 py-2 rounded-lg shadow-md cursor-pointer hover:bg-gray-800 transition-colors duration-200\"\r\n                onClick={() => (window.location.href = \"/home/<USER>\")}\r\n              >\r\n                Hashtag Generator\r\n              </div>\r\n              <div\r\n                className=\"bg-purple-700 text-white font-semibold px-4 py-2 rounded-lg shadow-md cursor-pointer hover:bg-purple-800 transition-colors duration-200\"\r\n                onClick={() =>\r\n                  (window.location.href = \"/home/<USER>\")\r\n                }\r\n              >\r\n                Checklist\r\n              </div>\r\n              <div\r\n                className=\"bg-green-700 text-white font-semibold px-4 py-2 rounded-lg shadow-md cursor-pointer hover:bg-green-800 transition-colors duration-200\"\r\n                onClick={() =>\r\n                  (window.location.href = \"/home/<USER>\")\r\n                }\r\n              >\r\n                Budget\r\n              </div>\r\n              <div\r\n                className=\"bg-blue-700 text-white font-semibold px-4 py-2 rounded-lg shadow-md cursor-pointer hover:bg-blue-800 transition-colors duration-200\"\r\n                onClick={() =>\r\n                  (window.location.href = \"/home/<USER>\")\r\n                }\r\n              >\r\n                Wedding Vendors\r\n              </div>\r\n              <div\r\n                className=\"bg-red-700 text-white font-semibold px-4 py-2 rounded-lg shadow-md cursor-pointer hover:bg-red-800 transition-colors duration-200\"\r\n                onClick={() =>\r\n                  (window.location.href = \"/home/<USER>\")\r\n                }\r\n              >\r\n                Wedding Websites\r\n              </div>\r\n              <div\r\n                className=\"bg-cyan-700 text-white font-semibold px-4 py-2 rounded-lg shadow-md cursor-pointer hover:bg-cyan-800 transition-colors duration-200\"\r\n                onClick={() =>\r\n                  (window.location.href = \"/home/<USER>\")\r\n                }\r\n              >\r\n                Guest\r\n              </div>\r\n            </div>\r\n\r\n            {/* Call to Action Box */}\r\n            <div className=\"bg-white rounded-lg shadow-lg mt-6 p-4 text-center\">\r\n              <h3 className=\"text-purple-700 font-bold text-lg\">\r\n                Wedding Planning Tool\r\n              </h3>\r\n              <p className=\"text-black text-sm mt-2\">\r\n                \"Plan Your Wedding Budget Perfectly with Our Tool!\"{\" \"}\r\n                <span className=\"font-bold\">Try Now!</span>\r\n              </p>\r\n              <button\r\n                onClick={() => window.location.href = '/home/<USER>'}\r\n                className=\"mt-4 bg-purple-600 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:bg-purple-700 transition-colors duration-200\">\r\n                Open Wedding Tools\r\n              </button>\r\n            </div>\r\n          </div>\r\n          {/* Special Offers Section */}\r\n          <div\r\n            className=\"rounded-lg shadow-lg p-6 mx-auto bg-purple-600 text-white\"\r\n            style={{\r\n              width: \"100%\",\r\n              transition: \"width 300ms ease-in-out\",\r\n            }}\r\n          >\r\n            <h2 className=\"text-xl font-bold flex items-center gap-2\">\r\n              Special Offers <span>🎟️</span>\r\n            </h2>\r\n            <div className=\"flex items-center gap-4 mt-4\">\r\n              <img\r\n                src=\"./pics/image_13.png\"\r\n                alt=\"Marriage Coupons\"\r\n                className=\"w-16 h-16 rounded-lg\"\r\n              />\r\n              <div>\r\n                <h3 className=\"text-lg font-semibold\">Marriage Coupons</h3>\r\n                <p className=\"text-sm\">\r\n                  Top to Bottom All Works Available for <strong>5 Lakhs</strong>{\" \"}\r\n                  for <strong>1300</strong> people's food, day and night!\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <button className=\"mt-4 bg-white text-purple-600 px-4 py-2 rounded-lg font-semibold\">\r\n              Buy Coupon\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* CSS to hide scrollbar across browsers */}\r\n        <style jsx>{`\r\n          /* Hide scrollbar for Chrome, Safari and Opera */\r\n          .scrollbar-hide::-webkit-scrollbar {\r\n            display: none;\r\n          }\r\n\r\n          /* Hide scrollbar for IE, Edge and Firefox */\r\n          .scrollbar-hide {\r\n            -ms-overflow-style: none; /* IE and Edge */\r\n            scrollbar-width: none; /* Firefox */\r\n          }\r\n        `}</style>\r\n      </aside>\r\n    </>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA,+CAA+C;AAC/C;AACA;AACA;AACA;AA9BA;;;;;;;;;;AAiDA,MAAM,UAAkC,CAAC,EACvC,IAAI,EACJ,KAAK,EACL,MAAM,EACN,OAAO,EACP,KAAK,EACN;IACC,qBACE,8OAAC;QAAG,WAAU;kBACZ,cAAA,8OAAC;YACC,MAAK;YACL,WAAW,CAAC,kBAAkB,EAAE,SAC5B,0BACA,mDACD,8CAA8C,CAAC;YAClD,SAAS;;8BAET,8OAAC;oBAAI,WAAU;8BAA8C;;;;;;gBAC5D,uBAAS,8OAAC;oBAAK,WAAU;8BAA0B;;;;;;gBACnD,SAAS,QAAQ,mBAChB,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAMb;AAGO,MAAM,gBAA0B;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;IAC1C,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,8BAA8B;IAE9B,wBAAwB;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjD,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC,YACjC,aAAa,OAAO,CAAC,gBACrB,aAAa,OAAO,CAAC;gBAEvB,IAAI,CAAC,OAAO;oBACV,QAAQ,IAAI,CAAC;oBACb;gBACF;gBAEA,oCAAoC;gBACpC,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAK,CAAC,GAAG,CAAC,yEAAyE;oBACxG,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAC9C;gBAEA,QAAQ,GAAG,CAAC,0BAA0B,SAAS,IAAI;gBAEnD,+BAA+B;gBAC/B,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE;oBAC9C,aAAa,SAAS,IAAI,CAAC,WAAW;oBACtC,aAAa,OAAO,CAAC,eAAe,SAAS,IAAI,CAAC,WAAW;gBAC/D;gBAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;oBACvC,YAAY,SAAS,IAAI,CAAC,IAAI;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAE9C,kDAAkD;gBAClD,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,IAAI,cAAc;oBAChB,QAAQ,GAAG,CAAC;oBACZ,aAAa;gBACf;YACF;QACF;QAEA,gCAAgC;QAChC;QAEA,4BAA4B;QAC5B,MAAM,kBAAkB,YAAY,kBAAkB,QAAQ,2BAA2B;QAEzF,qDAAqD;QACrD,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,IAAI,YAAY,WAAW,EAAE;gBAC3B,aAAa,YAAY,WAAW;YACtC;YACA,IAAI,YAAY,IAAI,EAAE;gBACpB,YAAY,YAAY,IAAI;YAC9B;QACF;QAEA,6CAA6C;QAC7C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAY;IAEhB,qDAAqD;IACrD,MAAM,gBAAwB;QAC5B;YAAE,IAAI;YAAG,MAAM;YAAU,aAAa;QAAoB;QAC1D;YAAE,IAAI;YAAG,MAAM;YAAS,aAAa;QAAmB;QACxD;YAAE,IAAI;YAAG,MAAM;YAAa,aAAa;QAAS;QAClD;YAAE,IAAI;YAAG,MAAM;YAAa,aAAa;QAAa;QACtD;YAAE,IAAI;YAAG,MAAM;YAAW,aAAa;QAAyB;QAChE;YAAE,IAAI;YAAG,MAAM;YAAW,aAAa;QAAc;QACrD;YAAE,IAAI;YAAG,MAAM;YAAa,aAAa;QAAsB;QAC/D;YAAE,IAAI;YAAG,MAAM;YAAQ,aAAa;QAAqB;KAC1D;IAED,sCAAsC;IACtC,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,OAAO;YACV,UAAU;YACV,aAAa;YACb;QACF;QAEA,aAAa;QAEb,IAAI;YACF,0EAA0E;YAC1E,MAAM,WAAW,MAAM,MACrB,CAAC,2DAA2D,EAAE,MAAM,uBAAuB,CAAC,EAC5F;gBACE,QAAQ;gBACR,SAAS;oBACP,kBACE;oBACF,mBAAmB;gBACrB;YACF;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,gEAAgE;gBAChE,QAAQ,IAAI,CAAC;gBACb,UAAU;gBACV,aAAa;gBACb;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,qDAAqD;YACrD,MAAM,kBAA0B,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC5D,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;oBACnB,SAAS,KAAK,OAAO;oBACrB,aAAa,KAAK,MAAM,IAAI;gBAC9B,CAAC;YAED,yFAAyF;YACzF,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,mFAAmF;YACnF,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF;IAEA,2CAA2C;IAC3C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,cAAc;QAEd,6BAA6B;QAC7B,IAAI,cAAc,OAAO,EAAE;YACzB,aAAa,cAAc,OAAO;QACpC;QAEA,0CAA0C;QAC1C,cAAc,OAAO,GAAG,WAAW;YACjC,aAAa;QACf,GAAG,MAAM,iBAAiB;IAC5B;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;QAEV,oCAAoC;QACpC,OAAO;YACL,IAAI,cAAc,OAAO,EAAE;gBACzB,aAAa,cAAc,OAAO;YACpC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,oBAAoB;QACpB,cAAc,KAAK,oCAAoC;IACzD;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,MAAM,WAAW,SAAS,cAAc,CAAC;YACzC,MAAM,SAAS,SAAS,cAAc,CAAC;YACvC,IACE,YACA,UACA,CAAC,SAAS,QAAQ,CAAC,MAAM,MAAM,KAC/B,CAAC,OAAO,QAAQ,CAAC,MAAM,MAAM,GAC7B;gBACA,oBAAoB;YACtB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;wBAAmC,SAAS,IAAM,OAAO,IAAI,CAAC;kCAC3E,cAAA,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;wCACf,WAAU;kDAEV,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhB,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc,CAAC;kCAE9B,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS;;kDAET,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,OAAO;4CAAE,OAAO;wCAAQ;;;;;;kDAC1C,8OAAC;wCAAK,OAAO;4CAAE,OAAO;wCAAQ;kDAAG;;;;;;;;;;;;0CAEnC,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,OAAO,IAAI,CAAC;0CAE3B,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAErC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC1B,8OAAC;wCAAK,WAAU;kDAA6G;;;;;;;;;;;;0CAI/H,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,IAAG;wCACH,WAAU;wCACV,SAAS,IAAM,oBAAoB,CAAC;;0DAEpC,8OAAC,0MAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,8OAAC;gDAAK,WAAU;0DACb,gBAAgB;;;;;;0DAEnB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;;oCAIlC,kCACC,8OAAC;wCACC,IAAG;wCACH,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEAGpD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAK1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU;gEACV,aAAY;gEACZ,WAAU;;;;;;4DAEX,2BACC,8OAAC,sMAAA,CAAA,SAAM;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;;;;;;;0DAMlB,8OAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,KAAK,KAAK,CAAC,0BACvB,8OAAC;oDAAI,WAAU;8DAA0B;;;;;2DAIzC,OAAO,GAAG,CAAC,CAAC,qBACV,8OAAC;wDAEC,WAAU;wDACV,SAAS,IAAM,iBAAiB,KAAK,IAAI;kEAEzC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,KAAK,IAAI;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EACb,KAAK,WAAW,IACf,CAAC,KAAK,MAAM,GACR,GAAG,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,OAAO,IAAI,SAAS,GAC5C,OAAO;;;;;;;;;;;;uDAZZ,KAAK,EAAE;;;;;;;;;;0DAoBpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,SAAS,IAAM,oBAAoB;8DACpC;;;;;;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAO,WAAU;gCAChB,SAAS,IAAM,OAAO,IAAI,CAAC;0CAE3B,cAAA,8OAAC,0IAAA,CAAA,UAAU;oCACT,MAAK;oCACL,UAAU;oCACV,UAAU,YAAY,aAAa;;;;;;;;;;;;;;;;;;;;;;;YAO1C,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,SAAS;;;;;;sCAEX,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc;sCAE7B,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;YAO9B,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAO,SAAS,IAAM,oBAAoB;8CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU;wCACV,aAAY;wCACZ,WAAU;;;;;;oCAEX,2BACC,8OAAC,sMAAA,CAAA,SAAM;wCACL,MAAM;wCACN,WAAU;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;sCACZ,OAAO,MAAM,KAAK,KAAK,CAAC,0BACvB,8OAAC;gCAAI,WAAU;0CAA0B;;;;;uCAEzC,OAAO,GAAG,CAAC,CAAC,qBACV,8OAAC;oCAEC,WAAU;oCACV,SAAS,IAAM,iBAAiB,KAAK,IAAI;8CAEzC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,KAAK,WAAW,IACf,CAAC,KAAK,MAAM,GACR,GAAG,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,OAAO,IAAI,SAAS,GAC5C,OAAO;;;;;;;;;;;;mCAZZ,KAAK,EAAE;;;;;;;;;;sCAoBpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,oBAAoB;0CACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;AAQO,MAAM,iBAAgD,CAAC,EAC5D,QAAQ,EACR,QAAQ,EACR,UAAU,EACX;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qDAAqD;IACrD,MAAM,WAAW;QACf,yFAAyF;QACzF,0BAA0B;QAC1B,4BAA4B;QAC5B,iBAAiB;QACjB;YACE,IAAI;YACJ,OAAO;YACP,0BACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK1C,WACE,8CAA8C;0BAC9C,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,kCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,mBACE,yDAAyD;0BAEzD,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,kBAAkB;QACpB;QACA;YACE,IAAI;YACJ,OAAO;YACP,0BACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,yBACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,kCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBAAK,OAAM;wBAAM,QAAO;wBAAK,IAAG;wBAAI,MAAK;;;;;;kCAC1C,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,iCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBAAK,OAAM;wBAAM,QAAO;wBAAK,IAAG;wBAAI,MAAK;;;;;;kCAC1C,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,qDAAqD;YACrD,qEAAqE;YACrE,kBAAkB;QACpB;QACA;YACE,IAAI;YACJ,OAAO;YACP,0BACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK1C,yBACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,kCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,iCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,kBAAkB;QACpB;QACA;YACE,IAAI;YACJ,OAAO;YACP,0BACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK1C,yBACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK1C,kCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,iCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,kBAAkB;QACpB;QACA;YACE,IAAI;YACJ,OAAO;YACP,0BACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK1C,yBACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK1C,kCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,iCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,kBAAkB;QACpB;QACA;YACE,IAAI;YACJ,OAAO;YACP,0BACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK1C,yBACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAK,QAAO;oCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK1C,kCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,iCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;YAIX,kBAAkB;QACpB;QACA;YACE,IAAI;YACJ,OAAO;YACP,0BACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBAAE,UAAS;;0CACV,8OAAC;gCACC,GAAE;gCACF,MAAK;gCACL,QAAO;gCACP,aAAY;;;;;;0CAEd,8OAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;;kCAGT,8OAAC;;0CACC,8OAAC;gCACC,IAAG;gCACH,IAAG;gCACH,IAAG;gCACH,IAAG;gCACH,IAAG;gCACH,eAAc;;kDAEd,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,QAAO;wCAAW,WAAU;;;;;;;;;;;;0CAEpC,8OAAC;gCAAS,IAAG;0CACX,cAAA,8OAAC;oCACC,OAAM;oCACN,QAAO;oCACP,MAAK;oCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAMpB,yBACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,QAAO;;;;;;kCAET,8OAAC;wBAAE,UAAS;;0CACV,8OAAC;gCACC,GAAE;gCACF,MAAK;gCACL,QAAO;gCACP,aAAY;;;;;;0CAEd,8OAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;;kCAGT,8OAAC;;0CACC,8OAAC;gCACC,IAAG;gCACH,IAAG;gCACH,IAAG;gCACH,IAAG;gCACH,IAAG;gCACH,eAAc;;kDAEd,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,QAAO;wCAAW,WAAU;;;;;;;;;;;;0CAEpC,8OAAC;gCAAS,IAAG;0CACX,cAAA,8OAAC;oCACC,OAAM;oCACN,QAAO;oCACP,MAAK;oCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAMpB,kCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBAAE,UAAS;;0CACV,8OAAC;gCACC,GAAE;gCACF,MAAK;gCACL,QAAO;gCACP,aAAY;;;;;;0CAEd,8OAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;;kCAGT,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BAAS,IAAG;sCACX,cAAA,8OAAC;gCACC,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMpB,iCACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBAAE,UAAS;;0CACV,8OAAC;gCACC,GAAE;gCACF,MAAK;gCACL,QAAO;gCACP,aAAY;;;;;;0CAEd,8OAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;;kCAGT,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;kCACC,cAAA,8OAAC;4BAAS,IAAG;sCACX,cAAA,8OAAC;gCACC,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMpB,kBAAkB;QACpB;KACD;IAED,qBACE,8OAAC;QACC,WAAW,CAAC,6FAA6F,EAAE,WAAW,OAAO,OAC1H,gBAAgB,CAAC;QACpB,cAAc;QACd,cAAc;kBAEd,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAG,WAAU;0BACX,SAAS,GAAG,CAAC,CAAC;oBACb,MAAM,WAAW,eAAe,KAAK,EAAE;oBAEvC,qBACE,8OAAC;kCACC,cAAA,8OAAC;4BACC,MACE,KAAK,EAAE,KAAK,SACR,UACA,KAAK,EAAE,KAAK,WACV,sBACA,KAAK,EAAE,KAAK,SACV,eACA,KAAK,EAAE,KAAK,gBACV,uBACA;4BAEZ,WAAW,CAAC,kBAAkB,EAAE,WAAW,QAAQ,IAAI;4BACvD,SAAS,CAAC;gCACR,IAAI,KAAK,EAAE,KAAK,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,EAAE,KAAK,UAAU,KAAK,EAAE,KAAK,eAAe;oCACjG,EAAE,cAAc;oCAChB,cAAc,KAAK,EAAE;gCACvB;4BACF;;gCAGC,WAEC,WACI,KAAK,iBAAiB,IAAI,KAAK,SAAS,GACxC,KAAK,kBAAkB,IAAI,KAAK,UAAU,GAE9C,WACI,KAAK,SAAS,GACd,KAAK,UAAU;gCAGpB,YAAY,CAAC,KAAK,iBAAiB,kBAClC,8OAAC;oCAAK,WAAU;8CAA0B,KAAK,KAAK;;;;;;;;;;;;uBAlCjD,KAAK,EAAE;;;;;gBAuCpB;;;;;;;;;;;;;;;;AAKV;AAEO,MAAM,mBAA6B;IACxC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjD,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC,YACjC,aAAa,OAAO,CAAC,gBACrB,aAAa,OAAO,CAAC;gBAEvB,IAAI,CAAC,OAAO;oBACV,QAAQ,IAAI,CAAC;oBACb;gBACF;gBAEA,oCAAoC;gBACpC,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAK,CAAC,GAAG,CAAC,yEAAyE;oBACxG,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAC9C;gBAEA,QAAQ,GAAG,CAAC,sCAAsC,SAAS,IAAI;gBAE/D,+BAA+B;gBAC/B,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE;oBAC9C,aAAa,SAAS,IAAI,CAAC,WAAW;oBACtC,aAAa,OAAO,CAAC,eAAe,SAAS,IAAI,CAAC,WAAW;gBAC/D;gBAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;oBACvC,YAAY,SAAS,IAAI,CAAC,IAAI;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4CAA4C;gBAE1D,kDAAkD;gBAClD,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,IAAI,cAAc;oBAChB,QAAQ,GAAG,CAAC;oBACZ,aAAa;gBACf;YACF;QACF;QAEA,gCAAgC;QAChC;QAEA,4BAA4B;QAC5B,MAAM,kBAAkB,YAAY,kBAAkB,QAAQ,2BAA2B;QAEzF,qDAAqD;QACrD,IAAI,aAAa;YACf,IAAI,YAAY,WAAW,EAAE;gBAC3B,aAAa,YAAY,WAAW;YACtC;YACA,IAAI,YAAY,IAAI,EAAE;gBACpB,YAAY,YAAY,IAAI;YAC9B;QACF;QAEA,6CAA6C;QAC7C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAY;IAEhB,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,8OAAC,mMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;8CACZ,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEjC,8OAAC;4BAAE,MAAK;4BAAoB,WAAU;;8CACpC,8OAAC,oNAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;8CACnB,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEjC,8OAAC;4BAAE,MAAK;4BAAI,WAAU;;8CACpB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEjC,8OAAC;4BAAE,MAAK;4BAAqB,WAAU;;8CACrC,8OAAC;oCACC,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,OAAM;oCACN,WAAU;;sDAEV,8OAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,8OAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,8OAAC;4CACC,GAAE;4CACF,MAAK;;;;;;;;;;;;8CAGT,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEjC,8OAAC;4BAAE,MAAK;4BAAa,WAAU;;8CAC7B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;8CACZ,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEjC,8OAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,MAAM;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAAe;;;;;;8CAC/B,8OAAC;oCAAK,WAAU;8CAA6G;;;;;;;;;;;;sCAI/H,8OAAC;4BAAE,MAAK;4BAAe,WAAU;;gCAC9B,0BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAK;wCACL,KAAK,YAAY,aAAa,QAAQ;wCACtC,WAAU;;;;;;;;;;yDAId,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;8CAEd,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;;;;;;;;;;;;0BAMrC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU,CAAC;0BAEzB,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oBAAC,MAAM;;;;;yCAAS,8OAAC,kMAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;;;;;;YAIzC,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,OAAM;gDACN,WAAU;0DAEV,cAAA,8OAAC;oDACC,GAAE;oDACF,MAAK;;;;;;;;;;;0DAGT,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;;;;;;;kDAIvD,8OAAC;wCAAO,SAAS,IAAM,UAAU;kDAC/B,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK7B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,mMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;8EACZ,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,oNAAA,CAAA,cAAW;oEAAC,MAAM;;;;;;8EACnB,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,oMAAA,CAAA,QAAK;oEAAC,MAAM;;;;;;8EACb,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,MAAM;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,oMAAA,CAAA,QAAK;oEAAC,MAAM;;;;;;8EACb,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;8EACZ,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;8EACZ,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC;oEACC,OAAM;oEACN,QAAO;oEACP,SAAQ;oEACR,MAAK;oEACL,OAAM;oEACN,WAAU;;sFAEV,8OAAC;4EACC,GAAE;4EACF,MAAK;;;;;;sFAEP,8OAAC;4EACC,GAAE;4EACF,MAAK;;;;;;sFAEP,8OAAC;4EACC,GAAE;4EACF,MAAK;;;;;;;;;;;;8EAGT,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,MAAM;;;;;;8EACrB,8OAAC;oEAAK,WAAU;8EAAO;;;;;;8EACvB,8OAAC;oEAAK,WAAU;8EAA8F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQtH,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;gEAET,0BACC,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,KAAK;wEACL,KAAK,YAAY,aAAa,QAAQ;wEACtC,WAAU;;;;;;;;;;yFAId,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;8EAEd,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,MAAM;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;kEAG3B,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,SAAM;oEAAC,MAAM;;;;;;8EACd,8OAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;8DAAK;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAKzC,8OAAC;wCACC,WAAU;wCACV,SAAS;4CACP,gDAAgD;4CAChD,uCAA8D;;4CAE9D,OAAO;gDACL,QAAQ,KAAK,CAAC;4CAChB;wCACF;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;AAQO,MAAM,eAA4C,CAAC,EACxD,QAAQ,EACR,QAAQ,EACR,UAAU,EACX;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,8CAA8C;IAC9C,MAAM,kBAAkB;QACtB,MAAM,cAAc,OAAO,UAAU,GAAG;QACxC,iBAAiB;QAEjB,qCAAqC;QACrC,IAAI,aAAa;YACf,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;QACpB,uCAAmC;;QAQnC;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,QAAQ,GAAG,CAAC,yCAAyC;QACrD,kBAAkB,CAAC,YAAc,CAAC;IACpC;IAEA,qBACE;;YAEG,+BACC,8OAAC;gBACC,SAAS;gBACT,WAAU;gBACV,OAAO;oBAAE,SAAS,gBAAgB,SAAS;gBAAO;0BAEjD,+BAAiB,8OAAC,4LAAA,CAAA,IAAC;oBAAC,MAAM;;;;;yCAAS,8OAAC,kMAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;;;;;;0BAIpD,8OAAC;gBAEC,OAAO;oBACL,OAAO,gBAAgB,UAAU,WAAW,SAAS;oBACrD,UAAU,gBAAgB,UAAU,WAAW,UAAU;oBACzD,WAAW;oBACX,WAAW,iBAAiB,kBAAkB;oBAC9C,QAAQ;gBACV;gBACA,cAAc,CAAC,gBAAgB,WAAW;gBAC1C,cAAc,CAAC,gBAAgB,aAAa;0DATjC,CAAC,gIAAgI,CAAC;;kCAW7I,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;kDAEb,8OAAC;kFAAc;;0DACb,8OAAC;gDACC,OAAO;oDACL,YAAY;oDACZ,YAAY;oDACZ,UAAU;oDACV,YAAY;oDACZ,eAAe;oDACf,eAAe;oDACf,OAAO;gDACT;;0DACD;;;;;;0DAGD,8OAAC;gDACC,KAAI;gDACJ,KAAI;0FACM;;;;;;;;;;;;kDAKd,8OAAC;wCAEC,OAAO;4CACL,iBAAiB;4CACjB,OAAO;4CACP,WAAW;4CACX,YAAY;wCACd;kFANU;;0DAQV,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAc;8DAEb,cAAA,8OAAC;wDAAI,OAAO;4DAAE,OAAO;wDAAM;;;0EAEzB,8OAAC;0GAAa;;kFACZ,8OAAC;wEACC,OAAO;4EACL,YAAY;4EACZ,YAAY;4EACZ,UAAU;4EACV,YAAY;4EACZ,eAAe;4EACf,eAAe;4EACf,OAAO;wEACT;;kFACD;;;;;;kFAGD,8OAAC;kHAAe;kFACd,cAAA,8OAAC;4EACC,OAAM;4EACN,QAAO;4EACP,SAAQ;4EACR,MAAK;4EACL,OAAM;;;8FAEN,8OAAC;oFAAK,OAAM;oFAAK,QAAO;oFAAK,MAAK;;;;;;;8FAClC,8OAAC;oFACC,GAAE;oFACF,MAAK;;;;;;;8FAEP,8OAAC;oFACC,GAAE;oFACF,GAAE;oFACF,OAAM;oFACN,QAAO;oFACP,IAAG;oFACH,MAAK;;;;;;;8FAEP,8OAAC;oFACC,GAAE;oFACF,GAAE;oFACF,OAAM;oFACN,QAAO;oFACP,IAAG;oFACH,MAAK;;;;;;;8FAEP,8OAAC;oFACC,GAAE;oFACF,GAAE;oFACF,OAAM;oFACN,QAAO;oFACP,IAAG;oFACH,MAAK;;;;;;;;;;;;;;;;;;;;;;;;0EAOb,8OAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,UAAU;oEACV,YAAY;oEACZ,eAAe;oEACf,eAAe;oEACf,YAAY;oEACZ,cACE,OAAO,0CAA0C;gEACrD;;;oEACD;kFAEC,8OAAC;wEACC,OAAO;4EACL,YAAY;wEACd;;;4EAEC;4EAAI;;;;;;;oEAEC;oEAAI;;;;;;;0EAKd,8OAAC;gEACC,SAAS;oEACP,gDAAgD;oEAChD,uCAA8D;;oEAE9D,OAAO;wEACL,QAAQ,KAAK,CAAC;oEAChB;gEACF;gEACA,OAAO;oEACL,YAAY;oEACZ,YAAY;oEACZ,UAAU;oEACV,YAAY;oEACZ,eAAe;oEACf,eAAe;oEACf,iBAAiB;oEACjB,OAAO;oEACP,cAAc;oEACd,SAAS;oEACT,OAAO;oEACP,UAAU;oEACV,QAAQ;gEACV;;0EACD;;;;;;;;;;;;;;;;;;;;;;0DAQP,8OAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,KAAK;oDACL,QAAQ;oDACR,OAAO;oDACP,SAAS;oDACT,YAAY;oDACZ,gBAAgB;gDAClB;;0DAEA,cAAA,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,OAAO;wDACL,QAAQ;wDACR,WAAW;wDACX,WAAW;oDACb;;;;;;;;;;;;;;;;;;;;;;;;0CAMR,8OAAC;0EAAc;;kDAEb,8OAAC;kFAAc;;0DACb,8OAAC;gDACC,OAAO;oDACL,YAAY;oDACZ,YAAY;oDACZ,UAAU;oDACV,YAAY;oDACZ,eAAe;oDACf,eAAe;oDACf,OAAO;gDACT;;0DACD;;;;;;0DAGD,8OAAC;gDACC,KAAI;gDACJ,KAAI;0FACM;;;;;;;;;;;;kDAKd,8OAAC;wCAEC,OAAO;4CACL,iBAAiB;4CACjB,OAAO;4CACP,WAAW;4CACX,YAAY;wCACd;kFANU;;0DAQV,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAc;8DAEb,cAAA,8OAAC;wDAAI,OAAO;4DAAE,OAAO;wDAAM;;;0EAEzB,8OAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,YAAY;oEACZ,UAAU;oEACV,YAAY;oEACZ,eAAe;oEACf,eAAe;oEACf,OAAO;oEACP,cAAc;gEAChB;;0EACD;;;;;;0EAKD,8OAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,UAAU;oEACV,YAAY;oEACZ,eAAe;oEACf,eAAe;oEACf,YAAY;oEACZ,cAAc;gEAChB;;;oEACD;kFAEC,8OAAC;wEACC,OAAO;4EACL,YAAY;wEACd;;kFACD;;;;;;oEAEO;oEAAI;;;;;;;0EAKd,8OAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,YAAY;oEACZ,UAAU;oEACV,YAAY;oEACZ,eAAe;oEACf,eAAe;oEACf,iBAAiB;oEACjB,OAAO;oEACP,cAAc;oEACd,SAAS;oEACT,OAAO;oEACP,UAAU;oEACV,QAAQ;gEACV;;0EACD;;;;;;;;;;;;;;;;;;;;;;0DAQP,8OAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,KAAK;oDACL,QAAQ;oDACR,OAAO;oDACP,SAAS;oDACT,YAAY;oDACZ,gBAAgB;gDAClB;;0DAEA,cAAA,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,OAAO;wDACL,QAAQ;wDACR,WAAW;wDACX,WAAW;oDACb;;;;;;;;;;;;;;;;;;;;;;;;0CAMR,8OAAC;gCAEC,OAAO;oCACL,OAAO;oCACP,YAAY;gCACd;0EAJU;0CAMV,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;oDACC,OAAO;wDACL,YAAY;wDACZ,YAAY;wDACZ,UAAU;wDACV,YAAY;wDACZ,eAAe;wDACf,eAAe;wDACf,OAAO;oDACT;;8DACD;;;;;;8DAGD,8OAAC;oDAAE,MAAK;8FAAc;8DAAwB;;;;;;;;;;;;sDAIhD,8OAAC;sFAAc;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;8FACM;;;;;;8DAEZ,8OAAC;8FAAY;8DAA6B;;;;;;8DAG1C,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;;8EACb,8OAAC;oEACC,KAAI;oEACJ,KAAI;8GACM;;;;;;8EAEZ,8OAAC;oEACC,KAAI;oEACJ,KAAI;8GACM;;;;;;8EAEZ,8OAAC;oEACC,KAAI;oEACJ,KAAI;8GACM;;;;;;;;;;;;sEAGd,8OAAC;sGAAc;sEAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQtE,8OAAC;gCAEC,OAAO;oCACL,iBAAiB;oCACjB,OAAO;oCACP,YAAY;gCACd;0EALU;;kDAQV,8OAAC;kFAAa;kDAA2C;;;;;;kDAKzD,8OAAC;kFAAc;;0DACb,8OAAC;gDAEC,SAAS,IAAO,OAAO,QAAQ,CAAC,IAAI,GAAG;0FAD7B;0DAEX;;;;;;0DAGD,8OAAC;gDAEC,SAAS,IACN,OAAO,QAAQ,CAAC,IAAI,GAAG;0FAFhB;0DAIX;;;;;;0DAGD,8OAAC;gDAEC,SAAS,IACN,OAAO,QAAQ,CAAC,IAAI,GAAG;0FAFhB;0DAIX;;;;;;0DAGD,8OAAC;gDAEC,SAAS,IACN,OAAO,QAAQ,CAAC,IAAI,GAAG;0FAFhB;0DAIX;;;;;;0DAGD,8OAAC;gDAEC,SAAS,IACN,OAAO,QAAQ,CAAC,IAAI,GAAG;0FAFhB;0DAIX;;;;;;0DAGD,8OAAC;gDAEC,SAAS,IACN,OAAO,QAAQ,CAAC,IAAI,GAAG;0FAFhB;0DAIX;;;;;;;;;;;;kDAMH,8OAAC;kFAAc;;0DACb,8OAAC;0FAAa;0DAAoC;;;;;;0DAGlD,8OAAC;0FAAY;;oDAA0B;oDACe;kEACpD,8OAAC;kGAAe;kEAAY;;;;;;;;;;;;0DAE9B,8OAAC;gDACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0FAC5B;0DAAgI;;;;;;;;;;;;;;;;;;0CAMhJ,8OAAC;gCAEC,OAAO;oCACL,OAAO;oCACP,YAAY;gCACd;0EAJU;;kDAMV,8OAAC;kFAAa;;4CAA4C;0DACzC,8OAAC;;0DAAK;;;;;;;;;;;;kDAEvB,8OAAC;kFAAc;;0DACb,8OAAC;gDACC,KAAI;gDACJ,KAAI;0FACM;;;;;;0DAEZ,8OAAC;;;kEACC,8OAAC;kGAAa;kEAAwB;;;;;;kEACtC,8OAAC;kGAAY;;4DAAU;0EACiB,8OAAC;;0EAAO;;;;;;4DAAiB;4DAAI;0EAC/D,8OAAC;;0EAAO;;;;;;4DAAa;;;;;;;;;;;;;;;;;;;kDAI/B,8OAAC;kFAAiB;kDAAmE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBjG", "debugId": null}}, {"offset": {"line": 5418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}