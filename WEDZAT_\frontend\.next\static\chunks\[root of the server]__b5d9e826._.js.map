{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/services/api.ts"], "sourcesContent": ["// services/api.ts\r\nimport axios, { AxiosRequestConfig } from 'axios';\r\n\r\n// Use environment variable or fallback to localhost for development\r\nconst BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:5000/hub';\r\n\r\n\r\n// Log the API URL being used\r\n// console.log(`API Service using base URL: ${BASE_URL}`);\r\n\r\n// For development, use a CORS proxy if needed\r\nconst API_URL = BASE_URL;\r\nif (process.env.NODE_ENV === 'development') {\r\n  // Uncomment the line below to use a CORS proxy in development if needed\r\n  // API_URL = `https://cors-anywhere.herokuapp.com/${BASE_URL}`;\r\n\r\n  // Log the API URL for debugging\r\n  console.log('Development mode detected, using API URL:', API_URL);\r\n}\r\n\r\n// Log the API URL being used\r\nconsole.log(`API Service using base URL: ${API_URL}`);\r\nconst TOKEN_KEY = 'token';  // Keep using your existing token key\r\nconst JWT_TOKEN_KEY = 'jwt_token'; // Alternative key for compatibility\r\n\r\n// Create axios instance\r\nconst api = axios.create({\r\n  baseURL: API_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  timeout: 60000, // 60 seconds default timeout\r\n  withCredentials: false // Helps with CORS issues\r\n});\r\n\r\n// Helper to get token from storage (checking both keys)\r\nconst getToken = (): string | null => {\r\n  if (typeof window === 'undefined') return null;\r\n  return localStorage.getItem(TOKEN_KEY);\r\n};\r\n\r\n// Helper to save token to storage (using both keys for compatibility)\r\nconst saveToken = (token: string): void => {\r\n  if (typeof window === 'undefined') return;\r\n  console.log('Saving token to localStorage:', token.substring(0, 15) + '...');\r\n  localStorage.setItem(TOKEN_KEY, token);\r\n  localStorage.setItem(JWT_TOKEN_KEY, token); // For compatibility with other components\r\n};\r\n\r\n// Request interceptor to add auth token to all requests\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    const token = getToken();\r\n    if (token && config.headers) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Authentication services\r\nexport const authService = {\r\n  // Register a new user\r\n  signup: async (signupData: any) => {\r\n    try {\r\n      console.log('Attempting signup with data:', {\r\n        ...signupData,\r\n        password: signupData.password ? '********' : undefined\r\n      });\r\n\r\n      const response = await axios.post(`${BASE_URL}/signup`, signupData, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n\r\n      console.log('Signup response received:', {\r\n        success: true,\r\n        hasToken: !!response.data.token\r\n      });\r\n\r\n      if (response.data.token) {\r\n        saveToken(response.data.token);\r\n      } else {\r\n        console.warn('No token received in signup response');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error(\"Signup error:\", error);\r\n      throw error.response?.data || { error: \"An error occurred during signup\" };\r\n    }\r\n  },\r\n\r\n  // Register a new vendor\r\n  registerVendor: async (vendorData: any) => {\r\n    try {\r\n      console.log('Attempting vendor registration with data:', {\r\n        ...vendorData,\r\n        password: vendorData.password ? '********' : undefined\r\n      });\r\n\r\n      const response = await axios.post(`${BASE_URL}/register-vendor`, vendorData, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n\r\n      console.log('Vendor registration response received:', {\r\n        success: true,\r\n        hasToken: !!response.data.token\r\n      });\r\n\r\n      if (response.data.token) {\r\n        saveToken(response.data.token);\r\n      } else {\r\n        console.warn('No token received in vendor registration response');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error(\"Vendor registration error:\", error);\r\n      throw error.response?.data || { error: \"An error occurred during vendor registration\" };\r\n    }\r\n  },\r\n\r\n  // Get business types\r\n  getBusinessTypes: async () => {\r\n    try {\r\n      const response = await axios.get(`${BASE_URL}/business-types`);\r\n      return response.data.business_types;\r\n    } catch (error: any) {\r\n      console.error(\"Get business types error:\", error);\r\n      throw error.response?.data || { error: \"An error occurred while fetching business types\" };\r\n    }\r\n  },\r\n\r\n  // Get vendor profile\r\n  getVendorProfile: async () => {\r\n    try {\r\n      const token = getToken();\r\n      if (!token) {\r\n        throw { error: 'No authentication token found' };\r\n      }\r\n\r\n      const response = await axios.get(`${BASE_URL}/vendor-profile`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n\r\n      return response.data.profile;\r\n    } catch (error: any) {\r\n      console.error(\"Get vendor profile error:\", error);\r\n      throw error.response?.data || { error: \"An error occurred while fetching vendor profile\" };\r\n    }\r\n  },\r\n\r\n\r\n\r\n\r\n\r\n  // Login with email/mobile and password\r\n  // In services/api.ts - login function\r\n  login: async (credentials: { email?: string; mobile_number?: string; password: string }) => {\r\n    try {\r\n      console.log('Attempting login with:', {\r\n        email: credentials.email,\r\n        mobile_number: credentials.mobile_number,\r\n        password: '********'\r\n      });\r\n\r\n      const response = await api.post('/login', credentials);\r\n\r\n      console.log('Login response received:', {\r\n        success: true,\r\n        hasToken: !!response.data.token,\r\n        tokenPreview: response.data.token ? `${response.data.token.substring(0, 10)}...` : 'none'\r\n      });\r\n\r\n      // Save token to localStorage with explicit console logs\r\n      if (response.data.token) {\r\n        console.log('Saving token to localStorage...');\r\n        localStorage.setItem('token', response.data.token);\r\n\r\n        // Verify token was saved\r\n        const savedToken = localStorage.getItem('token');\r\n        console.log(`Token verification: ${savedToken ? 'Successfully saved' : 'Failed to save'}`);\r\n      } else {\r\n        console.warn('No token received in login response');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Login error:', error);\r\n      throw error.response?.data || { error: 'Invalid credentials' };\r\n    }\r\n  },\r\n\r\n  // Authenticate with Clerk\r\n  clerkAuth: async (clerkData: {\r\n    clerk_token: string;\r\n    user_type: string;\r\n    user_email?: string;\r\n    user_name?: string;\r\n    user_id?: string;\r\n  }) => {\r\n    try {\r\n      console.log('Attempting Clerk authentication');\r\n\r\n      const response = await api.post('/clerk_auth', clerkData);\r\n\r\n      console.log('Clerk auth response received:', {\r\n        success: true,\r\n        hasToken: !!response.data.token\r\n      });\r\n\r\n      // Save token to localStorage\r\n      if (response.data.token) {\r\n        saveToken(response.data.token);\r\n      } else {\r\n        console.warn('No token received in Clerk auth response');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Clerk authentication error:', error);\r\n      throw error.response?.data || { error: 'Clerk authentication failed' };\r\n    }\r\n  },\r\n\r\n  // Check if user profile is complete\r\n  checkProfile: async () => {\r\n    try {\r\n      const token = getToken();\r\n\r\n      if (!token) {\r\n        throw { error: 'No authentication token found' };\r\n      }\r\n\r\n      console.log('Checking user profile');\r\n\r\n      const response = await axios.get(`${BASE_URL}/check-profile`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Profile check error:', error);\r\n      throw error.response?.data || { error: 'Failed to check profile' };\r\n    }\r\n  },\r\n\r\n  // Get the current token\r\n  getToken: () => {\r\n    return getToken();\r\n  },\r\n\r\n  // Check if the user is authenticated\r\n  isAuthenticated: () => {\r\n    return !!getToken();\r\n  },\r\n\r\n  // Logout - clear token from localStorage\r\n  logout: () => {\r\n    console.log('Logging out and removing tokens');\r\n    localStorage.removeItem(TOKEN_KEY);\r\n    localStorage.removeItem(JWT_TOKEN_KEY);\r\n  },\r\n};\r\n\r\n// User services\r\nexport const userService = {\r\n  // Get user details - uses GET method with explicit token in header\r\n  getUserDetails: async () => {\r\n    try {\r\n      const token = getToken();\r\n      if (!token) {\r\n        throw { error: 'No authentication token found' };\r\n      }\r\n\r\n      console.log('Fetching user details');\r\n\r\n      // Set the correct Authorization format (Bearer + token)\r\n      const response = await axios.get(`${BASE_URL}/user-details`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error(\"Error fetching user details:\", error);\r\n      throw error.response?.data || { error: 'Failed to fetch user details' };\r\n    }\r\n  },\r\n\r\n  // Update user details\r\n  updateUser: async (userData: {\r\n    name: string;\r\n    email: string;\r\n    mobile_number?: string;\r\n    dob?: string;\r\n    marital_status?: string;\r\n    place?: string;\r\n  }) => {\r\n    try {\r\n      console.log('Updating user details');\r\n\r\n      const response = await api.put('/update-user', userData);\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Update user error:', error);\r\n      throw error.response?.data || { error: 'Failed to update user' };\r\n    }\r\n  },\r\n};\r\n\r\n// Helper to check if user is authenticated\r\nexport const isAuthenticated = (): boolean => {\r\n  if (typeof window === 'undefined') {\r\n    return false; // For server-side rendering\r\n  }\r\n  const token = getToken();\r\n  return !!token; // Return true if token exists, false otherwise\r\n};\r\n\r\n// Upload and Media Interfaces\r\nexport interface PresignedUrlRequest {\r\n  media_type: 'photo' | 'video';\r\n  media_subtype: string; // 'story', 'flash', 'glimpse', 'movie' for video; 'story', 'post' for photo\r\n  video_category?: string; // 'my_wedding', 'wedding_influencer', 'friends_family_video'\r\n  filename: string;\r\n  content_type: string;\r\n  file_size: number;\r\n}\r\n\r\nexport interface PresignedUrlResponse {\r\n  media_id: string;\r\n  upload_urls: {\r\n    main: string;\r\n    thumbnail?: string;\r\n    preview?: string;\r\n  };\r\n  access_urls?: {\r\n    main: string;\r\n    thumbnail?: string;\r\n    preview?: string;\r\n  };\r\n  expires_in_seconds?: number;\r\n  fields?: Record<string, string>;\r\n}\r\n\r\nexport interface CompleteUploadRequest {\r\n  media_id: string;\r\n  media_type: 'photo' | 'video';\r\n  media_subtype?: string; // 'story', 'flash', 'glimpse', 'movie' for video; 'story', 'post' for photo\r\n  title: string;\r\n  description?: string;\r\n  tags?: string[];\r\n  duration?: number; // for videos only\r\n  personal_details?: {\r\n    caption?: string;\r\n    life_partner?: string;\r\n    wedding_style?: string;\r\n    place?: string;\r\n    additional_details?: Record<string, string>;\r\n  };\r\n  vendor_details?: {\r\n    venue_name?: string;\r\n    venue_contact?: string;\r\n    photographer_name?: string;\r\n    photographer_contact?: string;\r\n    makeup_artist_name?: string;\r\n    makeup_artist_contact?: string;\r\n    decoration_name?: string;\r\n    decoration_contact?: string;\r\n    caterer_name?: string;\r\n    caterer_contact?: string;\r\n    [key: string]: string | undefined;\r\n  };\r\n  transcoded_versions?: any[]; // for videos only\r\n}\r\n\r\nexport interface CompleteUploadResponse {\r\n  message: string;\r\n  photo_id?: string;\r\n  video_id?: string;\r\n  url: string;\r\n  thumbnail_url: string | null;\r\n  status?: string;\r\n  media_id?: string;\r\n  media_type?: 'photo' | 'video';\r\n  access_urls?: {\r\n    main: string;\r\n    thumbnail?: string;\r\n    preview?: string;\r\n  };\r\n}\r\n\r\nexport interface FaceVerificationResponse {\r\n  message: string;\r\n  face_verified: boolean;\r\n  face_image_url?: string;\r\n}\r\n\r\n// Upload services\r\nexport const uploadService = {\r\n  /**\r\n   * Verify user's face using captured image\r\n   * @param faceImage Base64 encoded image data (without data URL prefix)\r\n   */\r\n  verifyFace: async (faceImage: string): Promise<FaceVerificationResponse> => {\r\n    try {\r\n      console.log('Sending face verification request');\r\n      const token = getToken();\r\n\r\n      // Create the request payload\r\n      const payload = { face_image: faceImage };\r\n      const payloadSize = JSON.stringify(payload).length;\r\n\r\n      console.log('Request payload size:', payloadSize);\r\n\r\n      // Check if payload is too large\r\n      if (payloadSize > 1000000) {\r\n        console.warn('Warning: Payload is very large, which may cause issues with the API');\r\n      }\r\n\r\n      // No mock implementation - always use the real API\r\n      console.log('Using real face verification API');\r\n\r\n      // Make the real API call\r\n      const response = await axios.post(`${API_URL}/verify-face`, payload, {\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': token ? `Bearer ${token}` : ''\r\n        },\r\n        timeout: 60000, // 60 seconds\r\n        withCredentials: false\r\n      });\r\n\r\n      console.log('Face verification response:', response.data);\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error verifying face:', error);\r\n      throw error.response?.data || { error: 'Face verification failed' };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get pre-signed URLs for uploading media to S3\r\n   */\r\n  getPresignedUrl: async (request: PresignedUrlRequest): Promise<PresignedUrlResponse> => {\r\n    try {\r\n      console.log('Getting presigned URL with request:', request);\r\n\r\n      // Ensure we have a valid token\r\n      const token = getToken();\r\n      if (!token) {\r\n        console.warn('No authentication token found when getting presigned URL');\r\n      }\r\n\r\n      // Make the API call with explicit headers\r\n      const response = await axios.post(`${API_URL}/get-upload-url`, request, {\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': token ? `Bearer ${token}` : ''\r\n        },\r\n        timeout: 30000 // 30 seconds timeout\r\n      });\r\n\r\n      console.log('Presigned URL response:', response.data);\r\n\r\n      // Validate the response\r\n      if (!response.data.media_id || !response.data.upload_urls || !response.data.upload_urls.main) {\r\n        throw new Error('Invalid response from get-upload-url API');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error getting presigned URL:', error);\r\n      if (error.response?.status === 401) {\r\n        throw { error: 'Authentication failed. Please log in again.' };\r\n      }\r\n      throw error.response?.data || { error: 'Failed to get upload URL' };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Complete the upload process by notifying the backend\r\n   */\r\n  completeUpload: async (request: CompleteUploadRequest): Promise<CompleteUploadResponse> => {\r\n    try {\r\n      console.log('Completing upload with request:', JSON.stringify(request, null, 2));\r\n\r\n      // Log the media_subtype for debugging\r\n      console.log(`API SERVICE - Complete upload - Media subtype: ${request.media_subtype || 'Not set'}`);\r\n      console.log(`API SERVICE - Complete upload - Media type: ${request.media_type}`);\r\n      console.log(`API SERVICE - Complete upload - Media ID: ${request.media_id}`);\r\n\r\n      // Validate the request\r\n      if (!request.media_id) {\r\n        console.error('Missing media_id in completeUpload request');\r\n        throw new Error('Missing media_id in request');\r\n      }\r\n\r\n      if (!request.title || !request.title.trim()) {\r\n        console.error('Missing title in completeUpload request');\r\n        throw new Error('Please provide a title for your upload');\r\n      }\r\n\r\n      // Ensure we have a valid token\r\n      const token = getToken();\r\n      if (!token) {\r\n        console.warn('No authentication token found when completing upload');\r\n      }\r\n\r\n      // Make the API call with explicit headers\r\n      const response = await axios.post(`${API_URL}/complete-upload`, request, {\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': token ? `Bearer ${token}` : ''\r\n        },\r\n        timeout: 60000 // 60 seconds timeout for completion\r\n      });\r\n\r\n      console.log('Upload completion response:', response.data);\r\n\r\n      // Validate the response\r\n      if (!response.data.message) {\r\n        throw new Error('Invalid response from complete-upload API');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error completing upload:', error);\r\n      if (error.response?.status === 401) {\r\n        throw { error: 'Authentication failed. Please log in again.' };\r\n      }\r\n      throw error.response?.data || { error: 'Failed to complete upload' };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Upload a file to a presigned URL\r\n   */\r\n  uploadToPresignedUrl: async (\r\n    url: string,\r\n    file: File,\r\n    onProgress?: (progress: number) => void\r\n  ): Promise<void> => {\r\n    try {\r\n      console.log('Uploading file to presigned URL:', url);\r\n      console.log('File type:', file.type);\r\n      console.log('File size:', file.size);\r\n\r\n      // Check if the URL contains query parameters (S3 presigned URL)\r\n      const isS3PresignedUrl = url.includes('?');\r\n\r\n      // For S3 or CloudFront URLs, use our proxy API to avoid CORS issues\r\n      if (isS3PresignedUrl || url.includes('cloudfront.net')) {\r\n        console.log('Using proxy API for upload URL');\r\n\r\n        // Determine if this is a CloudFront URL\r\n        const isCloudFrontUrl = url.includes('cloudfront.net');\r\n        const isSpecificCloudFront = url.includes('d32sv0f8c41dk.cloudfront.net');\r\n\r\n        if (isCloudFrontUrl) {\r\n          console.log('Detected CloudFront URL');\r\n          if (isSpecificCloudFront) {\r\n            console.log('Detected specific CloudFront domain: d32sv0f8c41dk.cloudfront.net');\r\n          }\r\n        }\r\n\r\n        try {\r\n          // Report initial progress\r\n          if (onProgress) onProgress(10);\r\n\r\n          console.log('Using upload-proxy API to avoid CORS issues');\r\n\r\n          // Create a FormData object to send the file\r\n          const formData = new FormData();\r\n          formData.append('file', file);\r\n\r\n          // Add the presigned URL as a query parameter\r\n          const proxyUrl = `/api/upload-proxy?url=${encodeURIComponent(url)}`;\r\n\r\n          // Send the file to our proxy API\r\n          const response = await axios.post(proxyUrl, formData, {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            onUploadProgress: (progressEvent) => {\r\n              if (onProgress && progressEvent.total) {\r\n                // Map progress from 10-90%\r\n                const percentCompleted = 10 + Math.round((progressEvent.loaded * 80) / progressEvent.total);\r\n                onProgress(percentCompleted);\r\n              }\r\n            },\r\n            timeout: 600000, // 10 minutes for large uploads\r\n          });\r\n\r\n          console.log('Upload via proxy successful:', response.status);\r\n\r\n          // Report completion\r\n          if (onProgress) onProgress(90);\r\n\r\n          // Final progress\r\n          if (onProgress) onProgress(100);\r\n\r\n          console.log('Upload completed successfully');\r\n        } catch (uploadError) {\r\n          console.error('Error in upload via proxy:', uploadError);\r\n          throw uploadError;\r\n        }\r\n      } else {\r\n        // For API Gateway or other endpoints, use POST with FormData\r\n        console.log('Using POST method with FormData');\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n\r\n        // Track upload progress\r\n        const config: AxiosRequestConfig = {\r\n          onUploadProgress: (progressEvent) => {\r\n            if (onProgress && progressEvent.total) {\r\n              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\r\n              onProgress(percentCompleted);\r\n            }\r\n          },\r\n          headers: {},\r\n          timeout: 300000, // 5 minutes for large uploads\r\n        };\r\n\r\n        await axios.post(url, formData, config);\r\n      }\r\n\r\n      console.log('File uploaded successfully');\r\n    } catch (error: any) {\r\n      console.error('Error uploading file:', error);\r\n\r\n      // Provide more detailed error information\r\n      let errorMessage = 'Failed to upload file';\r\n\r\n      if (error.message) {\r\n        if (error.message.includes('Network Error') || error.message.includes('CORS')) {\r\n          errorMessage = 'Network error or CORS issue. Please try again or contact support.';\r\n        } else {\r\n          errorMessage = `Upload error: ${error.message}`;\r\n        }\r\n      }\r\n\r\n      if (error.response) {\r\n        console.error('Response status:', error.response.status);\r\n        console.error('Response headers:', error.response.headers);\r\n        console.error('Response data:', error.response.data);\r\n\r\n        if (error.response.status === 403) {\r\n          errorMessage = 'Permission denied. The upload URL may have expired.';\r\n        }\r\n      }\r\n\r\n      throw { error: errorMessage };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Handle the complete upload process\r\n   */\r\n  handleUpload: async (\r\n    file: File,\r\n    mediaType: 'photo' | 'video',\r\n    category: string,\r\n    title: string,\r\n    description?: string,\r\n    tags?: string[],\r\n    details?: Record<string, string>,\r\n    duration?: number,\r\n    thumbnail?: File | null,\r\n    onProgress?: (progress: number) => void\r\n  ): Promise<CompleteUploadResponse> => {\r\n    try {\r\n      console.log('API SERVICE - handleUpload called with params:', {\r\n        fileName: file?.name,\r\n        fileSize: Math.round(file.size / (1024 * 1024) * 100) / 100 + ' MB',\r\n        mediaType,\r\n        category,\r\n        title,\r\n        description,\r\n        tagsCount: tags?.length,\r\n        detailsCount: details ? Object.keys(details).length : 0,\r\n        videoCategory: details?.video_category || 'Not set',\r\n        duration,\r\n        hasThumbnail: !!thumbnail\r\n      });\r\n\r\n      // Log the video_category from details\r\n      if (mediaType === 'video') {\r\n        console.log('API SERVICE - Video category from details:', details?.video_category);\r\n        console.log('API SERVICE - All detail fields:', details ? JSON.stringify(details) : 'No details');\r\n      }\r\n\r\n      if (!file) {\r\n        throw new Error('No file provided');\r\n      }\r\n\r\n      // Log the file size and selected category without overriding the user's selection\r\n      if (mediaType === 'video') {\r\n        const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\r\n        const categoryLimits = {\r\n          'story': 50,\r\n          'flash': 100,\r\n          'glimpse': 250,\r\n          'movie': 2000\r\n        };\r\n\r\n        const sizeLimit = categoryLimits[category as keyof typeof categoryLimits] || 250;\r\n\r\n        console.log(`API SERVICE - File size: ${fileSizeMB} MB, Selected category: ${category}`);\r\n        console.log(`API SERVICE - Size limit for ${category}: ${sizeLimit} MB`);\r\n\r\n        // Warning is now handled in the presignedUrlRequest section\r\n      }\r\n\r\n      console.log('API SERVICE - Starting upload process for:', file.name);\r\n\r\n      // Update progress to 10%\r\n      if (onProgress) onProgress(10);\r\n\r\n      // Step 1: Get presigned URLs\r\n      console.log('API SERVICE - Creating presigned URL request with category:', category);\r\n\r\n      // Ensure we're using the correct backend category names\r\n      // Frontend: flashes, glimpses, movies, moments\r\n      // Backend: flash, glimpse, movie, story\r\n      let backendCategory = mediaType === 'photo'\r\n        ? (category === 'story' ? 'story' : 'post')  // For photos: either 'story' or 'post'\r\n        : category;  // For videos: keep the original category\r\n\r\n      // Double-check that we have a valid category\r\n      const validCategories = ['story', 'flash', 'glimpse', 'movie', 'post'];\r\n      if (!validCategories.includes(backendCategory)) {\r\n        console.log(`API SERVICE - WARNING: Invalid category '${backendCategory}'. Using 'flash' as fallback.`);\r\n        console.log(`API SERVICE - Valid categories are: ${validCategories.join(', ')}`);\r\n        console.log(`API SERVICE - Category type: ${typeof backendCategory}`);\r\n        console.log(`API SERVICE - Category value: '${backendCategory}'`);\r\n\r\n        // Use 'flash' as the default for videos instead of 'glimpse'\r\n        backendCategory = 'flash';\r\n      }\r\n\r\n      console.log(`API SERVICE - Original category from context: ${category}`);\r\n      console.log(`API SERVICE - Using backend category: ${backendCategory}`);\r\n\r\n      // Log the file size to help with debugging\r\n      const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\r\n      console.log(`API SERVICE - File size: ${fileSizeMB} MB`);\r\n\r\n      // Log the category limits\r\n      const categoryLimits = {\r\n        'story': 50,\r\n        'flash': 100,\r\n        'glimpse': 250,\r\n        'movie': 2000\r\n      };\r\n\r\n      const sizeLimit = categoryLimits[backendCategory as keyof typeof categoryLimits] || 250;\r\n      console.log(`API SERVICE - Size limit for ${backendCategory}: ${sizeLimit} MB`);\r\n\r\n      // Log a warning if the file size exceeds the limit\r\n      if (fileSizeMB > sizeLimit) {\r\n        console.log(`API SERVICE - WARNING: File size (${fileSizeMB} MB) exceeds the limit for ${backendCategory} (${sizeLimit} MB).`);\r\n        console.log(`API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.`);\r\n      }\r\n\r\n      const presignedUrlRequest: PresignedUrlRequest = {\r\n        media_type: mediaType,\r\n        media_subtype: backendCategory, // Must be one of: 'story', 'flash', 'glimpse', or 'movie'\r\n        filename: file.name,\r\n        content_type: file.type,\r\n        file_size: file.size,\r\n      };\r\n\r\n      // Log the presigned URL request\r\n      console.log(`API SERVICE - Sending presigned URL request with media_subtype: ${backendCategory}`);\r\n\r\n      // Add video_category for videos\r\n      if (mediaType === 'video') {\r\n        // Get the video_category from details - no default value\r\n        console.log('API SERVICE - Details object:', details);\r\n        console.log('API SERVICE - Details keys:', details ? Object.keys(details) : 'No details');\r\n\r\n        const videoCategory = details?.video_category;\r\n        console.log('API SERVICE - Video category from details:', videoCategory);\r\n\r\n        // Make sure we're using a valid video_category\r\n        const allowedCategories = ['my_wedding', 'wedding_influencer', 'friends_family_video'];\r\n\r\n        // If no video_category is provided, use a default one\r\n        if (!videoCategory) {\r\n          console.error(`API SERVICE - Missing video_category. Using default 'my_wedding'.`);\r\n          // Use 'my_wedding' as the default video_category\r\n          presignedUrlRequest.video_category = 'my_wedding';\r\n          console.log('API SERVICE - Using default video_category: my_wedding');\r\n        } else {\r\n          // Map the UI category to the backend category if needed\r\n          let backendVideoCategory = videoCategory;\r\n\r\n          // If the category is in UI format, map it to backend format\r\n          if (videoCategory === 'friends_family_videos') {\r\n            backendVideoCategory = 'friends_family_video';\r\n            console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\r\n          } else if (videoCategory === 'my_wedding_videos') {\r\n            backendVideoCategory = 'my_wedding';\r\n            console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\r\n          }\r\n\r\n          // If the video_category is not allowed, throw an error\r\n          if (!allowedCategories.includes(backendVideoCategory)) {\r\n            console.error(`API SERVICE - Invalid video_category: ${backendVideoCategory}. Must be one of ${JSON.stringify(allowedCategories)}`);\r\n            throw new Error(`Invalid video category. Must be one of ${JSON.stringify(allowedCategories)}`);\r\n          }\r\n\r\n          // Use the provided video_category\r\n          presignedUrlRequest.video_category = backendVideoCategory;\r\n          console.log('API SERVICE - Using video_category:', backendVideoCategory);\r\n\r\n          console.log(`API SERVICE - Final video category: ${backendVideoCategory}`);\r\n          console.log(`API SERVICE - Complete presigned URL request:`, JSON.stringify(presignedUrlRequest, null, 2));\r\n        }\r\n\r\n        // Log the category and file size limits\r\n        console.log(`API SERVICE - Original category from UI: ${category}`);\r\n        console.log(`API SERVICE - Using backend media subtype: ${backendCategory}`);\r\n\r\n        // Log size limits based on category without overriding the user's selection\r\n        const categoryLimits = {\r\n          'story': 50,\r\n          'flash': 100,\r\n          'glimpse': 250,\r\n          'movie': 2000\r\n        };\r\n\r\n        const sizeLimit = categoryLimits[backendCategory as keyof typeof categoryLimits] || 250;\r\n        console.log(`API SERVICE - Size limit for ${backendCategory}: ${sizeLimit} MB (file size: ${fileSizeMB} MB)`);\r\n\r\n        // Log a warning if the file size exceeds the limit for the selected category\r\n        if (fileSizeMB > sizeLimit) {\r\n          console.log(`API SERVICE - WARNING: File size (${fileSizeMB} MB) exceeds the limit for ${backendCategory} (${sizeLimit} MB).`);\r\n          console.log(`API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.`);\r\n        }\r\n\r\n        // Add duration if available\r\n        if (duration) {\r\n          console.log(`Video duration: ${duration} seconds`);\r\n          // You could add this to the request if the API supports it\r\n          // presignedUrlRequest.duration = duration;\r\n        }\r\n      }\r\n\r\n      const presignedUrlResponse = await uploadService.getPresignedUrl(presignedUrlRequest);\r\n\r\n      // Update progress to 20%\r\n      if (onProgress) onProgress(20);\r\n\r\n      // Step 2: Upload the file to the presigned URL (20-70% of progress)\r\n      await uploadService.uploadToPresignedUrl(\r\n        presignedUrlResponse.upload_urls.main,\r\n        file,\r\n        (uploadProgress) => {\r\n          // Map the upload progress from 0-100 to 20-70 in our overall progress\r\n          if (onProgress) onProgress(20 + (uploadProgress * 0.5));\r\n        }\r\n      );\r\n\r\n      // Step 3: Upload thumbnail if available (70-90% of progress)\r\n      if (thumbnail && presignedUrlResponse.upload_urls.thumbnail) {\r\n        console.log('Uploading thumbnail:', thumbnail.name);\r\n\r\n        // Update progress to 70%\r\n        if (onProgress) onProgress(70);\r\n\r\n        await uploadService.uploadToPresignedUrl(\r\n          presignedUrlResponse.upload_urls.thumbnail,\r\n          thumbnail,\r\n          (uploadProgress) => {\r\n            // Map the upload progress from 0-100 to 70-90 in our overall progress\r\n            if (onProgress) onProgress(70 + (uploadProgress * 0.2));\r\n          }\r\n        );\r\n      }\r\n\r\n      // Update progress to 90%\r\n      if (onProgress) onProgress(90);\r\n\r\n      // Step 4: Complete the upload (90-100% of progress)\r\n      try {\r\n        // Validate title\r\n        if (!title || !title.trim()) {\r\n          console.error('Title is empty or missing');\r\n          throw new Error('Please provide a title for your upload');\r\n        }\r\n\r\n        console.log('Title is valid:', title);\r\n\r\n        // Prepare the complete upload request\r\n        const completeRequest: CompleteUploadRequest = {\r\n          media_id: presignedUrlResponse.media_id,\r\n          media_type: mediaType,\r\n          media_subtype: backendCategory, // Include the media_subtype\r\n          title: title.trim(),\r\n          description: description || '',\r\n          tags: tags || [],\r\n        };\r\n\r\n        console.log(`API SERVICE - Setting media_subtype in completeRequest: ${backendCategory}`);\r\n\r\n        // Log the description being sent\r\n        console.log('Sending description:', description || '');\r\n\r\n        // Add duration for videos if available\r\n        if (mediaType === 'video' && duration) {\r\n          completeRequest.duration = duration;\r\n        }\r\n\r\n        // Extract personal details from the details object\r\n        const personalDetails: CompleteUploadRequest['personal_details'] = {\r\n          caption: title.trim(),\r\n          life_partner: details?.lifePartner || '',\r\n          wedding_style: description || '',\r\n          place: details?.location || ''\r\n        };\r\n\r\n        // Extract vendor details from the details object\r\n        const vendorDetails: CompleteUploadRequest['vendor_details'] = {};\r\n\r\n        // Process vendor details from the details object\r\n        if (details) {\r\n          console.log('API SERVICE - Processing vendor details from details object');\r\n          console.log('API SERVICE - Raw details object:', JSON.stringify(details, null, 2));\r\n\r\n          // Count vendor fields in the details object\r\n          let vendorFieldCount = 0;\r\n          Object.keys(details).forEach(key => {\r\n            if (key.startsWith('vendor_')) {\r\n              vendorFieldCount++;\r\n            }\r\n          });\r\n          console.log(`API SERVICE - Found ${vendorFieldCount} vendor-related fields in details object`);\r\n\r\n          // Keep track of which vendor types we've already processed\r\n          const processedVendors = new Set();\r\n\r\n          Object.entries(details).forEach(([key, value]) => {\r\n            if (key.startsWith('vendor_') && value) {\r\n              const parts = key.split('_');\r\n              if (parts.length >= 3) {\r\n                const vendorType = parts[1];\r\n                const fieldType = parts.slice(2).join('_');\r\n\r\n                // Normalize vendor type to avoid duplicates\r\n                const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n                  vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n                console.log(`API SERVICE - Processing vendor field: ${key} = ${value}`, {\r\n                  vendorType,\r\n                  fieldType,\r\n                  normalizedType,\r\n                  alreadyProcessed: processedVendors.has(normalizedType)\r\n                });\r\n\r\n                // Skip if we've already processed this vendor type\r\n                if (processedVendors.has(normalizedType)) {\r\n                  console.log(`API SERVICE - Skipping ${key} as ${normalizedType} is already processed`);\r\n                  return;\r\n                }\r\n\r\n                if (fieldType === 'name') {\r\n                  vendorDetails[`${normalizedType}_name`] = value;\r\n                  console.log(`API SERVICE - Set ${normalizedType}_name = ${value}`);\r\n\r\n                  // Also set the contact if available\r\n                  const contactKey = `vendor_${vendorType}_contact`;\r\n                  if (details[contactKey]) {\r\n                    vendorDetails[`${normalizedType}_contact`] = details[contactKey];\r\n                    console.log(`API SERVICE - Also set ${normalizedType}_contact = ${details[contactKey]}`);\r\n                    processedVendors.add(normalizedType);\r\n                    console.log(`API SERVICE - Marked ${normalizedType} as processed (has both name and contact)`);\r\n                  }\r\n                } else if (fieldType === 'contact') {\r\n                  vendorDetails[`${normalizedType}_contact`] = value;\r\n                  console.log(`API SERVICE - Set ${normalizedType}_contact = ${value}`);\r\n\r\n                  // Also set the name if available\r\n                  const nameKey = `vendor_${vendorType}_name`;\r\n                  if (details[nameKey]) {\r\n                    vendorDetails[`${normalizedType}_name`] = details[nameKey];\r\n                    console.log(`API SERVICE - Also set ${normalizedType}_name = ${details[nameKey]}`);\r\n                    processedVendors.add(normalizedType);\r\n                    console.log(`API SERVICE - Marked ${normalizedType} as processed (has both name and contact)`);\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          });\r\n\r\n          // Log the processed vendor details\r\n          // console.log('API SERVICE - Processed vendor details:', JSON.stringify(vendorDetails, null, 2));\r\n          // console.log(`API SERVICE - Processed ${processedVendors.size} complete vendor types: ${Array.from(processedVendors).join(', ')}`);\r\n\r\n          // Final check to ensure we have both name and contact for each vendor\r\n          const vendorNames = new Set();\r\n          const vendorContacts = new Set();\r\n          let completeVendorCount = 0;\r\n\r\n          Object.keys(vendorDetails).forEach(key => {\r\n            if (key.endsWith('_name')) {\r\n              vendorNames.add(key.replace('_name', ''));\r\n            } else if (key.endsWith('_contact')) {\r\n              vendorContacts.add(key.replace('_contact', ''));\r\n            }\r\n          });\r\n\r\n          // Count complete pairs\r\n          vendorNames.forEach(name => {\r\n            if (vendorContacts.has(name)) {\r\n              completeVendorCount++;\r\n            } else {\r\n              // If we have a name but no contact, add a default contact\r\n              console.log(`API SERVICE - WARNING: Vendor ${name} has name but no contact, adding default contact`);\r\n              vendorDetails[`${name}_contact`] = '0000000000';\r\n              completeVendorCount++;\r\n            }\r\n          });\r\n\r\n          // Check for contacts without names\r\n          vendorContacts.forEach(contact => {\r\n            if (!vendorNames.has(contact)) {\r\n              // If we have a contact but no name, add a default name\r\n              console.log(`API SERVICE - WARNING: Vendor ${contact} has contact but no name, adding default name`);\r\n              if (typeof contact === 'string') {\r\n                vendorDetails[`${contact}_name`] = `Vendor ${contact.charAt(0).toUpperCase() + contact.slice(1)}`;\r\n              }\r\n              completeVendorCount++;\r\n            }\r\n          });\r\n\r\n          console.log(`API SERVICE - Final check: Found ${completeVendorCount} complete vendor pairs`);\r\n          console.log('API SERVICE - Final vendor details:', JSON.stringify(vendorDetails, null, 2));\r\n        }\r\n\r\n        // Ensure we have the required vendor fields for wedding videos\r\n        if (mediaType === 'video' && details?.video_category &&\r\n          ['my_wedding', 'wedding_influencer', 'friends_family_video'].includes(details.video_category)) {\r\n          // Try to get vendor details from localStorage if they're missing from the details object\r\n          const initialVendorKeys = Object.keys(vendorDetails);\r\n          const vendorCount = initialVendorKeys.filter(key => key.endsWith('_name')).length;\r\n\r\n          console.log(`API SERVICE - Initial vendor count: ${vendorCount} name fields found`);\r\n          console.log('API SERVICE - Initial vendor details:', JSON.stringify(vendorDetails, null, 2));\r\n\r\n          // Always check localStorage for vendor details to ensure we have the most complete set\r\n          console.log('API SERVICE - Checking localStorage for vendor details');\r\n          try {\r\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\r\n            if (storedVendorDetails) {\r\n              const parsedVendorDetails = JSON.parse(storedVendorDetails);\r\n              console.log('API SERVICE - Retrieved vendor details from localStorage:', storedVendorDetails);\r\n\r\n              // Track how many complete vendor details we've added\r\n              let completeVendorCount = 0;\r\n\r\n              // Process vendor details from localStorage\r\n              Object.entries(parsedVendorDetails).forEach(([vendorType, details]: [string, any]) => {\r\n                if (details && details.name && details.mobileNumber) {\r\n                  // Add to vendorDetails\r\n                  const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n                    vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n                  vendorDetails[`${normalizedType}_name`] = details.name;\r\n                  vendorDetails[`${normalizedType}_contact`] = details.mobileNumber;\r\n\r\n                  console.log(`API SERVICE - Added vendor ${normalizedType} with name: ${details.name} and contact: ${details.mobileNumber}`);\r\n                  completeVendorCount++;\r\n\r\n                  // Also add the original type if it's different\r\n                  if (normalizedType !== vendorType) {\r\n                    vendorDetails[`${vendorType}_name`] = details.name;\r\n                    vendorDetails[`${vendorType}_contact`] = details.mobileNumber;\r\n                    console.log(`API SERVICE - Also added original vendor ${vendorType}`);\r\n                  }\r\n                }\r\n              });\r\n              console.log(`API SERVICE - Added ${completeVendorCount} complete vendor details from localStorage`);\r\n              console.log('API SERVICE - Updated vendor details:', JSON.stringify(vendorDetails, null, 2));\r\n\r\n              // Force update the state with these vendor details\r\n              try {\r\n                // This is a direct state update to ensure the vendor details are available for validation\r\n                if (window && window.dispatchEvent) {\r\n                  const vendorUpdateEvent = new CustomEvent('vendor-details-update', { detail: parsedVendorDetails });\r\n                  window.dispatchEvent(vendorUpdateEvent);\r\n                  console.log('API SERVICE - Dispatched vendor-details-update event');\r\n                }\r\n              } catch (eventError) {\r\n                console.error('API SERVICE - Failed to dispatch vendor-details-update event:', eventError);\r\n              }\r\n            } else {\r\n              console.log('API SERVICE - No vendor details found in localStorage');\r\n            }\r\n          } catch (error) {\r\n            console.error('API SERVICE - Failed to retrieve vendor details from localStorage:', error);\r\n          }\r\n\r\n          // Make sure we have at least 4 vendor details with both name and contact\r\n          // Define required vendor types for fallback if needed\r\n          const requiredVendorTypes = ['venue', 'photographer', 'makeup_artist', 'decoration', 'caterer'];\r\n\r\n          // Count all vendor details, including additional ones\r\n          let validVendorCount = 0;\r\n\r\n          // Count all vendor details where both name and contact are provided\r\n          console.log('API SERVICE - Checking vendor details for validation:', JSON.stringify(vendorDetails, null, 2));\r\n          const allVendorKeys = Object.keys(vendorDetails);\r\n          console.log('API SERVICE - Vendor keys:', allVendorKeys.join(', '));\r\n\r\n          // Keep track of which vendors we've already counted to avoid duplicates\r\n          const countedVendors = new Set();\r\n\r\n          // First pass: Check for standard vendor types\r\n          for (let i = 0; i < allVendorKeys.length; i++) {\r\n            const key = allVendorKeys[i];\r\n            if (key.endsWith('_name')) {\r\n              const baseKey = key.replace('_name', '');\r\n              const contactKey = `${baseKey}_contact`;\r\n\r\n              // Normalize the key to handle both frontend and backend naming\r\n              const normalizedKey = baseKey === 'makeupArtist' ? 'makeup_artist' :\r\n                baseKey === 'decorations' ? 'decoration' : baseKey;\r\n\r\n              // Skip if we've already counted this vendor\r\n              if (countedVendors.has(normalizedKey)) {\r\n                continue;\r\n              }\r\n\r\n              console.log(`Checking vendor ${baseKey}:`, {\r\n                name: vendorDetails[key],\r\n                contact: vendorDetails[contactKey]\r\n              });\r\n\r\n              if (vendorDetails[key] && vendorDetails[contactKey]) {\r\n                validVendorCount++;\r\n                countedVendors.add(normalizedKey);\r\n                console.log(`Valid vendor found: ${baseKey} with name: ${vendorDetails[key]} and contact: ${vendorDetails[contactKey]}`);\r\n              }\r\n            }\r\n          }\r\n\r\n          console.log(`Total valid vendor count after first pass: ${validVendorCount}`);\r\n\r\n          // Second pass: Check for additional vendors with different naming patterns\r\n          for (let i = 1; i <= 10; i++) {\r\n            const nameKey = `additional${i}_name`;\r\n            const contactKey = `additional${i}_contact`;\r\n\r\n            // Skip if we've already counted this vendor\r\n            if (countedVendors.has(`additional${i}`)) {\r\n              continue;\r\n            }\r\n\r\n            if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\r\n              validVendorCount++;\r\n              countedVendors.add(`additional${i}`);\r\n              console.log(`Valid additional vendor found: additional${i} with name: ${vendorDetails[nameKey]} and contact: ${vendorDetails[contactKey]}`);\r\n            }\r\n          }\r\n\r\n          // Third pass: Check for additionalVendor pattern (used in some parts of the code)\r\n          for (let i = 1; i <= 10; i++) {\r\n            const nameKey = `additionalVendor${i}_name`;\r\n            const contactKey = `additionalVendor${i}_contact`;\r\n\r\n            // Skip if we've already counted this vendor\r\n            if (countedVendors.has(`additionalVendor${i}`)) {\r\n              continue;\r\n            }\r\n\r\n            if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\r\n              validVendorCount++;\r\n              countedVendors.add(`additionalVendor${i}`);\r\n              console.log(`Valid additionalVendor found: additionalVendor${i} with name: ${vendorDetails[nameKey]} and contact: ${vendorDetails[contactKey]}`);\r\n            }\r\n          }\r\n\r\n          console.log(`Total valid vendor count after all passes: ${validVendorCount}`);\r\n\r\n          // Map additional vendors to the predefined vendor types if needed\r\n          // This ensures the backend will count them correctly\r\n          if (validVendorCount < 4) {\r\n            console.log('Need to map additional vendors to predefined types');\r\n\r\n            // First, collect all additional vendors from all patterns\r\n            const additionalVendors: { name: string, contact: string, key: string }[] = [];\r\n\r\n            // Collect all additional vendors with 'additional' prefix\r\n            for (let i = 1; i <= 10; i++) {\r\n              const nameKey = `additional${i}_name`;\r\n              const contactKey = `additional${i}_contact`;\r\n              if (vendorDetails[nameKey] && vendorDetails[contactKey] &&\r\n                !countedVendors.has(`additional${i}`)) {\r\n                additionalVendors.push({\r\n                  name: vendorDetails[nameKey],\r\n                  contact: vendorDetails[contactKey],\r\n                  key: `additional${i}`\r\n                });\r\n                countedVendors.add(`additional${i}`);\r\n                console.log(`Found additional vendor ${i}: ${vendorDetails[nameKey]}`);\r\n              }\r\n            }\r\n\r\n            // Collect all additional vendors with 'additionalVendor' prefix\r\n            for (let i = 1; i <= 10; i++) {\r\n              const nameKey = `additionalVendor${i}_name`;\r\n              const contactKey = `additionalVendor${i}_contact`;\r\n              if (vendorDetails[nameKey] && vendorDetails[contactKey] &&\r\n                !countedVendors.has(`additionalVendor${i}`)) {\r\n                additionalVendors.push({\r\n                  name: vendorDetails[nameKey],\r\n                  contact: vendorDetails[contactKey],\r\n                  key: `additionalVendor${i}`\r\n                });\r\n                countedVendors.add(`additionalVendor${i}`);\r\n                console.log(`Found additionalVendor ${i}: ${vendorDetails[nameKey]}`);\r\n              }\r\n            }\r\n\r\n            console.log(`Found ${additionalVendors.length} additional vendors to map`);\r\n\r\n            // Map additional vendors to empty predefined vendor slots\r\n            let additionalIndex = 0;\r\n            for (const type of requiredVendorTypes) {\r\n              // Check if this type is already filled\r\n              const normalizedType = type === 'makeup_artist' ? 'makeupArtist' :\r\n                type === 'decoration' ? 'decorations' : type;\r\n\r\n              // Skip if we've already counted this vendor type\r\n              if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\r\n                console.log(`Skipping ${type} as it's already counted`);\r\n                continue;\r\n              }\r\n\r\n              if (additionalIndex < additionalVendors.length) {\r\n                // Map this additional vendor to this predefined type\r\n                vendorDetails[`${type}_name`] = additionalVendors[additionalIndex].name;\r\n                vendorDetails[`${type}_contact`] = additionalVendors[additionalIndex].contact;\r\n                console.log(`Mapped additional vendor ${additionalVendors[additionalIndex].key} to ${type}: ${additionalVendors[additionalIndex].name}`);\r\n                additionalIndex++;\r\n                validVendorCount++;\r\n                countedVendors.add(type);\r\n\r\n                if (validVendorCount >= 4) {\r\n                  console.log(`Reached 4 valid vendors after mapping, no need to continue`);\r\n                  break;\r\n                }\r\n              }\r\n            }\r\n\r\n            // If we still don't have enough, add placeholders\r\n            if (validVendorCount < 4) {\r\n              console.log('Still need more vendors, adding placeholders');\r\n\r\n              for (const type of requiredVendorTypes) {\r\n                // Check if this type is already filled\r\n                const normalizedType = type === 'makeup_artist' ? 'makeupArtist' :\r\n                  type === 'decoration' ? 'decorations' : type;\r\n\r\n                // Skip if we've already counted this vendor type\r\n                if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\r\n                  console.log(`Skipping ${type} for placeholder as it's already counted`);\r\n                  continue;\r\n                }\r\n\r\n                // Add placeholder for this vendor type\r\n                vendorDetails[`${type}_name`] = vendorDetails[`${type}_name`] || `${type.charAt(0).toUpperCase() + type.slice(1)}`;\r\n                vendorDetails[`${type}_contact`] = vendorDetails[`${type}_contact`] || '0000000000';\r\n                validVendorCount++;\r\n                countedVendors.add(type);\r\n                console.log(`Added placeholder for ${type}: ${vendorDetails[`${type}_name`]}`);\r\n\r\n                if (validVendorCount >= 4) {\r\n                  console.log(`Reached 4 valid vendors after adding placeholders`);\r\n                  break;\r\n                }\r\n              }\r\n\r\n              // Final check - if we still don't have 4, force add the remaining required types\r\n              if (validVendorCount < 4) {\r\n                console.log('CRITICAL: Still don\\'t have 4 vendors, forcing placeholders for all required types');\r\n                for (const type of requiredVendorTypes) {\r\n                  if (!vendorDetails[`${type}_name`] || !vendorDetails[`${type}_contact`]) {\r\n                    vendorDetails[`${type}_name`] = `${type.charAt(0).toUpperCase() + type.slice(1)}`;\r\n                    vendorDetails[`${type}_contact`] = '0000000000';\r\n                    validVendorCount++;\r\n                    console.log(`Force added placeholder for ${type}`);\r\n                    if (validVendorCount >= 4) break;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            // Final log of vendor count\r\n            console.log(`Final valid vendor count: ${validVendorCount}`);\r\n          }\r\n        }\r\n\r\n        // Add the details to the request\r\n        completeRequest.personal_details = personalDetails;\r\n        completeRequest.vendor_details = vendorDetails;\r\n\r\n        // Final check before sending - ensure we have at least 4 complete vendor details\r\n        if (mediaType === 'video') {\r\n          // Count complete vendor details (with both name and contact)\r\n          const vendorNames = new Set();\r\n          const vendorContacts = new Set();\r\n          let completeVendorCount = 0;\r\n\r\n          if (vendorDetails) {\r\n            Object.keys(vendorDetails).forEach(key => {\r\n              if (key.endsWith('_name')) {\r\n                vendorNames.add(key.replace('_name', ''));\r\n              } else if (key.endsWith('_contact')) {\r\n                vendorContacts.add(key.replace('_contact', ''));\r\n              }\r\n            });\r\n\r\n            // Count complete pairs\r\n            vendorNames.forEach(name => {\r\n              if (vendorContacts.has(name)) {\r\n                completeVendorCount++;\r\n              }\r\n            });\r\n          }\r\n\r\n          console.log(`API SERVICE - FINAL CHECK: Found ${completeVendorCount} complete vendor pairs before sending request`);\r\n\r\n          // If we don't have enough vendors, add placeholders to reach 4\r\n          if (completeVendorCount < 4) {\r\n            console.log(`API SERVICE - FINAL CHECK: Adding placeholders to reach 4 vendors`);\r\n\r\n            const requiredVendorTypes = ['venue', 'photographer', 'makeup_artist', 'decoration', 'caterer'];\r\n\r\n            for (const type of requiredVendorTypes) {\r\n              // Skip if this vendor is already complete\r\n              if (vendorDetails[`${type}_name`] && vendorDetails[`${type}_contact`]) {\r\n                continue;\r\n              }\r\n\r\n              // Add placeholder for this vendor\r\n              vendorDetails[`${type}_name`] = vendorDetails[`${type}_name`] || `${type.charAt(0).toUpperCase() + type.slice(1)}`;\r\n              vendorDetails[`${type}_contact`] = vendorDetails[`${type}_contact`] || '0000000000';\r\n              completeVendorCount++;\r\n\r\n              console.log(`API SERVICE - FINAL CHECK: Added placeholder for ${type}`);\r\n\r\n              if (completeVendorCount >= 4) {\r\n                break;\r\n              }\r\n            }\r\n\r\n            // Update the request with the new vendor details\r\n            completeRequest.vendor_details = vendorDetails;\r\n            console.log(`API SERVICE - FINAL CHECK: Now have ${completeVendorCount} complete vendor pairs`);\r\n          }\r\n        }\r\n\r\n        console.log('Sending complete upload request:', JSON.stringify(completeRequest, null, 2));\r\n\r\n        // Complete the upload\r\n        const completeResponse = await uploadService.completeUpload(completeRequest);\r\n\r\n        // Log the complete response\r\n        console.log('Complete upload response:', JSON.stringify(completeResponse, null, 2));\r\n\r\n        // Update progress to 100%\r\n        if (onProgress) onProgress(100);\r\n\r\n        console.log('Upload process completed successfully');\r\n        return completeResponse;\r\n      } catch (completeError: any) {\r\n        console.error('Error in complete upload step:', completeError);\r\n\r\n        // Check if the error is related to the title\r\n        if (completeError.message && completeError.message.includes('title')) {\r\n          console.error('Title-related error detected:', completeError.message);\r\n          throw { error: 'Please provide a title for your upload' };\r\n        }\r\n\r\n        // Handle other errors\r\n        const errorMessage = completeError.response?.data?.error ||\r\n          completeError.message ||\r\n          'Failed to complete the upload process';\r\n\r\n        console.error('Throwing error:', errorMessage);\r\n        throw { error: errorMessage };\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error in upload process:', error);\r\n\r\n      // Check if the error is related to the title\r\n      if (error.error && typeof error.error === 'string' && error.error.includes('title')) {\r\n        console.error('Title-related error detected in main catch block');\r\n        throw { error: 'Please provide a title for your upload' };\r\n      }\r\n\r\n      // If error is already formatted correctly, just pass it through\r\n      if (error.error) {\r\n        throw error;\r\n      }\r\n\r\n      // Otherwise, format the error\r\n      throw error.response?.data || { error: 'Upload process failed' };\r\n    }\r\n  }\r\n};\r\n\r\nexport default {\r\n  authService,\r\n  userService,\r\n  uploadService,\r\n  isAuthenticated,\r\n};\r\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;;;;;AAID;AAHjB;;AAEA,oEAAoE;AACpE,MAAM,WAAW,wGAAmC;AAGpD,6BAA6B;AAC7B,0DAA0D;AAE1D,8CAA8C;AAC9C,MAAM,UAAU;AAChB,wCAA4C;IAC1C,wEAAwE;IACxE,+DAA+D;IAE/D,gCAAgC;IAChC,QAAQ,GAAG,CAAC,6CAA6C;AAC3D;AAEA,6BAA6B;AAC7B,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,SAAS;AACpD,MAAM,YAAY,SAAU,qCAAqC;AACjE,MAAM,gBAAgB,aAAa,oCAAoC;AAEvE,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;IACT,iBAAiB,MAAM,yBAAyB;AAClD;AAEA,wDAAwD;AACxD,MAAM,WAAW;IACf,uCAAmC;;IAAW;IAC9C,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEA,sEAAsE;AACtE,MAAM,YAAY,CAAC;IACjB,uCAAmC;;IAAM;IACzC,QAAQ,GAAG,CAAC,iCAAiC,MAAM,SAAS,CAAC,GAAG,MAAM;IACtE,aAAa,OAAO,CAAC,WAAW;IAChC,aAAa,OAAO,CAAC,eAAe,QAAQ,0CAA0C;AACxF;AAEA,wDAAwD;AACxD,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ;IACd,IAAI,SAAS,OAAO,OAAO,EAAE;QAC3B,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,cAAc;IACzB,sBAAsB;IACtB,QAAQ,OAAO;QACb,IAAI;YACF,QAAQ,GAAG,CAAC,gCAAgC;gBAC1C,GAAG,UAAU;gBACb,UAAU,WAAW,QAAQ,GAAG,aAAa;YAC/C;YAEA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,SAAS,OAAO,CAAC,EAAE,YAAY;gBAClE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,6BAA6B;gBACvC,SAAS;gBACT,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK;YACjC;YAEA,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;gBACvB,UAAU,SAAS,IAAI,CAAC,KAAK;YAC/B,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAAkC;QAC3E;IACF;IAEA,wBAAwB;IACxB,gBAAgB,OAAO;QACrB,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C;gBACvD,GAAG,UAAU;gBACb,UAAU,WAAW,QAAQ,GAAG,aAAa;YAC/C;YAEA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,SAAS,gBAAgB,CAAC,EAAE,YAAY;gBAC3E,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,0CAA0C;gBACpD,SAAS;gBACT,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK;YACjC;YAEA,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;gBACvB,UAAU,SAAS,IAAI,CAAC,KAAK;YAC/B,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAA+C;QACxF;IACF;IAEA,qBAAqB;IACrB,kBAAkB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,SAAS,eAAe,CAAC;YAC7D,OAAO,SAAS,IAAI,CAAC,cAAc;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAAkD;QAC3F;IACF;IAEA,qBAAqB;IACrB,kBAAkB;QAChB,IAAI;YACF,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM;oBAAE,OAAO;gBAAgC;YACjD;YAEA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,SAAS,eAAe,CAAC,EAAE;gBAC7D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI,CAAC,OAAO;QAC9B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAAkD;QAC3F;IACF;IAMA,uCAAuC;IACvC,sCAAsC;IACtC,OAAO,OAAO;QACZ,IAAI;YACF,QAAQ,GAAG,CAAC,0BAA0B;gBACpC,OAAO,YAAY,KAAK;gBACxB,eAAe,YAAY,aAAa;gBACxC,UAAU;YACZ;YAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,UAAU;YAE1C,QAAQ,GAAG,CAAC,4BAA4B;gBACtC,SAAS;gBACT,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK;gBAC/B,cAAc,SAAS,IAAI,CAAC,KAAK,GAAG,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;YACrF;YAEA,wDAAwD;YACxD,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;gBACvB,QAAQ,GAAG,CAAC;gBACZ,aAAa,OAAO,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK;gBAEjD,yBAAyB;gBACzB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,aAAa,uBAAuB,kBAAkB;YAC3F,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAAsB;QAC/D;IACF;IAEA,0BAA0B;IAC1B,WAAW,OAAO;QAOhB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,eAAe;YAE/C,QAAQ,GAAG,CAAC,iCAAiC;gBAC3C,SAAS;gBACT,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK;YACjC;YAEA,6BAA6B;YAC7B,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;gBACvB,UAAU,SAAS,IAAI,CAAC,KAAK;YAC/B,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAA8B;QACvE;IACF;IAEA,oCAAoC;IACpC,cAAc;QACZ,IAAI;YACF,MAAM,QAAQ;YAEd,IAAI,CAAC,OAAO;gBACV,MAAM;oBAAE,OAAO;gBAAgC;YACjD;YAEA,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,SAAS,cAAc,CAAC,EAAE;gBAC5D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAA0B;QACnE;IACF;IAEA,wBAAwB;IACxB,UAAU;QACR,OAAO;IACT;IAEA,qCAAqC;IACrC,iBAAiB;QACf,OAAO,CAAC,CAAC;IACX;IAEA,yCAAyC;IACzC,QAAQ;QACN,QAAQ,GAAG,CAAC;QACZ,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;AACF;AAGO,MAAM,cAAc;IACzB,mEAAmE;IACnE,gBAAgB;QACd,IAAI;YACF,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,MAAM;oBAAE,OAAO;gBAAgC;YACjD;YAEA,QAAQ,GAAG,CAAC;YAEZ,wDAAwD;YACxD,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,SAAS,aAAa,CAAC,EAAE;gBAC3D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAA+B;QACxE;IACF;IAEA,sBAAsB;IACtB,YAAY,OAAO;QAQjB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,gBAAgB;YAC/C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAAwB;QACjE;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,uCAAmC;;IAEnC;IACA,MAAM,QAAQ;IACd,OAAO,CAAC,CAAC,OAAO,+CAA+C;AACjE;AAkFO,MAAM,gBAAgB;IAC3B;;;GAGC,GACD,YAAY,OAAO;QACjB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,QAAQ;YAEd,6BAA6B;YAC7B,MAAM,UAAU;gBAAE,YAAY;YAAU;YACxC,MAAM,cAAc,KAAK,SAAS,CAAC,SAAS,MAAM;YAElD,QAAQ,GAAG,CAAC,yBAAyB;YAErC,gCAAgC;YAChC,IAAI,cAAc,SAAS;gBACzB,QAAQ,IAAI,CAAC;YACf;YAEA,mDAAmD;YACnD,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,YAAY,CAAC,EAAE,SAAS;gBACnE,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG;gBAC/C;gBACA,SAAS;gBACT,iBAAiB;YACnB;YAEA,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAA2B;QACpE;IACF;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,+BAA+B;YAC/B,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;YACf;YAEA,0CAA0C;YAC1C,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,eAAe,CAAC,EAAE,SAAS;gBACtE,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG;gBAC/C;gBACA,SAAS,MAAM,qBAAqB;YACtC;YAEA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;YAEpD,wBAAwB;YACxB,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;gBAC5F,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM;oBAAE,OAAO;gBAA8C;YAC/D;YACA,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAA2B;QACpE;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC,KAAK,SAAS,CAAC,SAAS,MAAM;YAE7E,sCAAsC;YACtC,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,QAAQ,aAAa,IAAI,WAAW;YAClG,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,QAAQ,UAAU,EAAE;YAC/E,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,QAAQ,QAAQ,EAAE;YAE3E,uBAAuB;YACvB,IAAI,CAAC,QAAQ,QAAQ,EAAE;gBACrB,QAAQ,KAAK,CAAC;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI;gBAC3C,QAAQ,KAAK,CAAC;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,+BAA+B;YAC/B,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;YACf;YAEA,0CAA0C;YAC1C,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,gBAAgB,CAAC,EAAE,SAAS;gBACvE,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG;gBAC/C;gBACA,SAAS,MAAM,oCAAoC;YACrD;YAEA,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI;YAExD,wBAAwB;YACxB,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM;oBAAE,OAAO;gBAA8C;YAC/D;YACA,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAA4B;QACrE;IACF;IAEA;;GAEC,GACD,sBAAsB,OACpB,KACA,MACA;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;YAChD,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YACnC,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YAEnC,gEAAgE;YAChE,MAAM,mBAAmB,IAAI,QAAQ,CAAC;YAEtC,oEAAoE;YACpE,IAAI,oBAAoB,IAAI,QAAQ,CAAC,mBAAmB;gBACtD,QAAQ,GAAG,CAAC;gBAEZ,wCAAwC;gBACxC,MAAM,kBAAkB,IAAI,QAAQ,CAAC;gBACrC,MAAM,uBAAuB,IAAI,QAAQ,CAAC;gBAE1C,IAAI,iBAAiB;oBACnB,QAAQ,GAAG,CAAC;oBACZ,IAAI,sBAAsB;wBACxB,QAAQ,GAAG,CAAC;oBACd;gBACF;gBAEA,IAAI;oBACF,0BAA0B;oBAC1B,IAAI,YAAY,WAAW;oBAE3B,QAAQ,GAAG,CAAC;oBAEZ,4CAA4C;oBAC5C,MAAM,WAAW,IAAI;oBACrB,SAAS,MAAM,CAAC,QAAQ;oBAExB,6CAA6C;oBAC7C,MAAM,WAAW,CAAC,sBAAsB,EAAE,mBAAmB,MAAM;oBAEnE,iCAAiC;oBACjC,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,UAAU,UAAU;wBACpD,SAAS;4BACP,gBAAgB;wBAClB;wBACA,kBAAkB,CAAC;4BACjB,IAAI,cAAc,cAAc,KAAK,EAAE;gCACrC,2BAA2B;gCAC3B,MAAM,mBAAmB,KAAK,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,GAAG,KAAM,cAAc,KAAK;gCAC1F,WAAW;4BACb;wBACF;wBACA,SAAS;oBACX;oBAEA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,MAAM;oBAE3D,oBAAoB;oBACpB,IAAI,YAAY,WAAW;oBAE3B,iBAAiB;oBACjB,IAAI,YAAY,WAAW;oBAE3B,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,aAAa;oBACpB,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,MAAM;gBACR;YACF,OAAO;gBACL,6DAA6D;gBAC7D,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,wBAAwB;gBACxB,MAAM,SAA6B;oBACjC,kBAAkB,CAAC;wBACjB,IAAI,cAAc,cAAc,KAAK,EAAE;4BACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;4BACtF,WAAW;wBACb;oBACF;oBACA,SAAS,CAAC;oBACV,SAAS;gBACX;gBAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK,UAAU;YAClC;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,0CAA0C;YAC1C,IAAI,eAAe;YAEnB,IAAI,MAAM,OAAO,EAAE;gBACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,oBAAoB,MAAM,OAAO,CAAC,QAAQ,CAAC,SAAS;oBAC7E,eAAe;gBACjB,OAAO;oBACL,eAAe,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;gBACjD;YACF;YAEA,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;gBACvD,QAAQ,KAAK,CAAC,qBAAqB,MAAM,QAAQ,CAAC,OAAO;gBACzD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;gBAEnD,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;oBACjC,eAAe;gBACjB;YACF;YAEA,MAAM;gBAAE,OAAO;YAAa;QAC9B;IACF;IAEA;;GAEC,GACD,cAAc,OACZ,MACA,WACA,UACA,OACA,aACA,MACA,SACA,UACA,WACA;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,kDAAkD;gBAC5D,UAAU,MAAM;gBAChB,UAAU,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,OAAO,MAAM;gBAC9D;gBACA;gBACA;gBACA;gBACA,WAAW,MAAM;gBACjB,cAAc,UAAU,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;gBACtD,eAAe,SAAS,kBAAkB;gBAC1C;gBACA,cAAc,CAAC,CAAC;YAClB;YAEA,sCAAsC;YACtC,IAAI,cAAc,SAAS;gBACzB,QAAQ,GAAG,CAAC,8CAA8C,SAAS;gBACnE,QAAQ,GAAG,CAAC,oCAAoC,UAAU,KAAK,SAAS,CAAC,WAAW;YACtF;YAEA,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,kFAAkF;YAClF,IAAI,cAAc,SAAS;gBACzB,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,OAAO;gBACjE,MAAM,iBAAiB;oBACrB,SAAS;oBACT,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBAEA,MAAM,YAAY,cAAc,CAAC,SAAwC,IAAI;gBAE7E,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,wBAAwB,EAAE,UAAU;gBACvF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC;YAEvE,4DAA4D;YAC9D;YAEA,QAAQ,GAAG,CAAC,8CAA8C,KAAK,IAAI;YAEnE,yBAAyB;YACzB,IAAI,YAAY,WAAW;YAE3B,6BAA6B;YAC7B,QAAQ,GAAG,CAAC,+DAA+D;YAE3E,wDAAwD;YACxD,+CAA+C;YAC/C,wCAAwC;YACxC,IAAI,kBAAkB,cAAc,UAC/B,aAAa,UAAU,UAAU,SAClC,UAAW,yCAAyC;YAExD,6CAA6C;YAC7C,MAAM,kBAAkB;gBAAC;gBAAS;gBAAS;gBAAW;gBAAS;aAAO;YACtE,IAAI,CAAC,gBAAgB,QAAQ,CAAC,kBAAkB;gBAC9C,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,gBAAgB,6BAA6B,CAAC;gBACtG,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,gBAAgB,IAAI,CAAC,OAAO;gBAC/E,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,iBAAiB;gBACpE,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,gBAAgB,CAAC,CAAC;gBAEhE,6DAA6D;gBAC7D,kBAAkB;YACpB;YAEA,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,UAAU;YACvE,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,iBAAiB;YAEtE,2CAA2C;YAC3C,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,OAAO;YACjE,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,GAAG,CAAC;YAEvD,0BAA0B;YAC1B,MAAM,iBAAiB;gBACrB,SAAS;gBACT,SAAS;gBACT,WAAW;gBACX,SAAS;YACX;YAEA,MAAM,YAAY,cAAc,CAAC,gBAA+C,IAAI;YACpF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,gBAAgB,EAAE,EAAE,UAAU,GAAG,CAAC;YAE9E,mDAAmD;YACnD,IAAI,aAAa,WAAW;gBAC1B,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,WAAW,2BAA2B,EAAE,gBAAgB,EAAE,EAAE,UAAU,KAAK,CAAC;gBAC7H,QAAQ,GAAG,CAAC,CAAC,0FAA0F,CAAC;YAC1G;YAEA,MAAM,sBAA2C;gBAC/C,YAAY;gBACZ,eAAe;gBACf,UAAU,KAAK,IAAI;gBACnB,cAAc,KAAK,IAAI;gBACvB,WAAW,KAAK,IAAI;YACtB;YAEA,gCAAgC;YAChC,QAAQ,GAAG,CAAC,CAAC,gEAAgE,EAAE,iBAAiB;YAEhG,gCAAgC;YAChC,IAAI,cAAc,SAAS;gBACzB,yDAAyD;gBACzD,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,QAAQ,GAAG,CAAC,+BAA+B,UAAU,OAAO,IAAI,CAAC,WAAW;gBAE5E,MAAM,gBAAgB,SAAS;gBAC/B,QAAQ,GAAG,CAAC,8CAA8C;gBAE1D,+CAA+C;gBAC/C,MAAM,oBAAoB;oBAAC;oBAAc;oBAAsB;iBAAuB;gBAEtF,sDAAsD;gBACtD,IAAI,CAAC,eAAe;oBAClB,QAAQ,KAAK,CAAC,CAAC,iEAAiE,CAAC;oBACjF,iDAAiD;oBACjD,oBAAoB,cAAc,GAAG;oBACrC,QAAQ,GAAG,CAAC;gBACd,OAAO;oBACL,wDAAwD;oBACxD,IAAI,uBAAuB;oBAE3B,4DAA4D;oBAC5D,IAAI,kBAAkB,yBAAyB;wBAC7C,uBAAuB;wBACvB,QAAQ,GAAG,CAAC,yDAAyD;oBACvE,OAAO,IAAI,kBAAkB,qBAAqB;wBAChD,uBAAuB;wBACvB,QAAQ,GAAG,CAAC,yDAAyD;oBACvE;oBAEA,uDAAuD;oBACvD,IAAI,CAAC,kBAAkB,QAAQ,CAAC,uBAAuB;wBACrD,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,qBAAqB,iBAAiB,EAAE,KAAK,SAAS,CAAC,oBAAoB;wBAClI,MAAM,IAAI,MAAM,CAAC,uCAAuC,EAAE,KAAK,SAAS,CAAC,oBAAoB;oBAC/F;oBAEA,kCAAkC;oBAClC,oBAAoB,cAAc,GAAG;oBACrC,QAAQ,GAAG,CAAC,uCAAuC;oBAEnD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,sBAAsB;oBACzE,QAAQ,GAAG,CAAC,CAAC,6CAA6C,CAAC,EAAE,KAAK,SAAS,CAAC,qBAAqB,MAAM;gBACzG;gBAEA,wCAAwC;gBACxC,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,UAAU;gBAClE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,iBAAiB;gBAE3E,4EAA4E;gBAC5E,MAAM,iBAAiB;oBACrB,SAAS;oBACT,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBAEA,MAAM,YAAY,cAAc,CAAC,gBAA+C,IAAI;gBACpF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,gBAAgB,EAAE,EAAE,UAAU,gBAAgB,EAAE,WAAW,IAAI,CAAC;gBAE5G,6EAA6E;gBAC7E,IAAI,aAAa,WAAW;oBAC1B,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,WAAW,2BAA2B,EAAE,gBAAgB,EAAE,EAAE,UAAU,KAAK,CAAC;oBAC7H,QAAQ,GAAG,CAAC,CAAC,0FAA0F,CAAC;gBAC1G;gBAEA,4BAA4B;gBAC5B,IAAI,UAAU;oBACZ,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,QAAQ,CAAC;gBACjD,2DAA2D;gBAC3D,2CAA2C;gBAC7C;YACF;YAEA,MAAM,uBAAuB,MAAM,cAAc,eAAe,CAAC;YAEjE,yBAAyB;YACzB,IAAI,YAAY,WAAW;YAE3B,oEAAoE;YACpE,MAAM,cAAc,oBAAoB,CACtC,qBAAqB,WAAW,CAAC,IAAI,EACrC,MACA,CAAC;gBACC,sEAAsE;gBACtE,IAAI,YAAY,WAAW,KAAM,iBAAiB;YACpD;YAGF,6DAA6D;YAC7D,IAAI,aAAa,qBAAqB,WAAW,CAAC,SAAS,EAAE;gBAC3D,QAAQ,GAAG,CAAC,wBAAwB,UAAU,IAAI;gBAElD,yBAAyB;gBACzB,IAAI,YAAY,WAAW;gBAE3B,MAAM,cAAc,oBAAoB,CACtC,qBAAqB,WAAW,CAAC,SAAS,EAC1C,WACA,CAAC;oBACC,sEAAsE;oBACtE,IAAI,YAAY,WAAW,KAAM,iBAAiB;gBACpD;YAEJ;YAEA,yBAAyB;YACzB,IAAI,YAAY,WAAW;YAE3B,oDAAoD;YACpD,IAAI;gBACF,iBAAiB;gBACjB,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;oBAC3B,QAAQ,KAAK,CAAC;oBACd,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC,mBAAmB;gBAE/B,sCAAsC;gBACtC,MAAM,kBAAyC;oBAC7C,UAAU,qBAAqB,QAAQ;oBACvC,YAAY;oBACZ,eAAe;oBACf,OAAO,MAAM,IAAI;oBACjB,aAAa,eAAe;oBAC5B,MAAM,QAAQ,EAAE;gBAClB;gBAEA,QAAQ,GAAG,CAAC,CAAC,wDAAwD,EAAE,iBAAiB;gBAExF,iCAAiC;gBACjC,QAAQ,GAAG,CAAC,wBAAwB,eAAe;gBAEnD,uCAAuC;gBACvC,IAAI,cAAc,WAAW,UAAU;oBACrC,gBAAgB,QAAQ,GAAG;gBAC7B;gBAEA,mDAAmD;gBACnD,MAAM,kBAA6D;oBACjE,SAAS,MAAM,IAAI;oBACnB,cAAc,SAAS,eAAe;oBACtC,eAAe,eAAe;oBAC9B,OAAO,SAAS,YAAY;gBAC9B;gBAEA,iDAAiD;gBACjD,MAAM,gBAAyD,CAAC;gBAEhE,iDAAiD;gBACjD,IAAI,SAAS;oBACX,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,qCAAqC,KAAK,SAAS,CAAC,SAAS,MAAM;oBAE/E,4CAA4C;oBAC5C,IAAI,mBAAmB;oBACvB,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,CAAA;wBAC3B,IAAI,IAAI,UAAU,CAAC,YAAY;4BAC7B;wBACF;oBACF;oBACA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,iBAAiB,wCAAwC,CAAC;oBAE7F,2DAA2D;oBAC3D,MAAM,mBAAmB,IAAI;oBAE7B,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC3C,IAAI,IAAI,UAAU,CAAC,cAAc,OAAO;4BACtC,MAAM,QAAQ,IAAI,KAAK,CAAC;4BACxB,IAAI,MAAM,MAAM,IAAI,GAAG;gCACrB,MAAM,aAAa,KAAK,CAAC,EAAE;gCAC3B,MAAM,YAAY,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;gCAEtC,4CAA4C;gCAC5C,MAAM,iBAAiB,eAAe,iBAAiB,kBACrD,eAAe,gBAAgB,eAAe;gCAEhD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI,GAAG,EAAE,OAAO,EAAE;oCACtE;oCACA;oCACA;oCACA,kBAAkB,iBAAiB,GAAG,CAAC;gCACzC;gCAEA,mDAAmD;gCACnD,IAAI,iBAAiB,GAAG,CAAC,iBAAiB;oCACxC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,IAAI,IAAI,EAAE,eAAe,qBAAqB,CAAC;oCACrF;gCACF;gCAEA,IAAI,cAAc,QAAQ;oCACxB,aAAa,CAAC,GAAG,eAAe,KAAK,CAAC,CAAC,GAAG;oCAC1C,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,eAAe,QAAQ,EAAE,OAAO;oCAEjE,oCAAoC;oCACpC,MAAM,aAAa,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC;oCACjD,IAAI,OAAO,CAAC,WAAW,EAAE;wCACvB,aAAa,CAAC,GAAG,eAAe,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,WAAW;wCAChE,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;wCACvF,iBAAiB,GAAG,CAAC;wCACrB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,eAAe,yCAAyC,CAAC;oCAC/F;gCACF,OAAO,IAAI,cAAc,WAAW;oCAClC,aAAa,CAAC,GAAG,eAAe,QAAQ,CAAC,CAAC,GAAG;oCAC7C,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,eAAe,WAAW,EAAE,OAAO;oCAEpE,iCAAiC;oCACjC,MAAM,UAAU,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC;oCAC3C,IAAI,OAAO,CAAC,QAAQ,EAAE;wCACpB,aAAa,CAAC,GAAG,eAAe,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,QAAQ;wCAC1D,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE;wCACjF,iBAAiB,GAAG,CAAC;wCACrB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,eAAe,yCAAyC,CAAC;oCAC/F;gCACF;4BACF;wBACF;oBACF;oBAEA,mCAAmC;oBACnC,kGAAkG;oBAClG,qIAAqI;oBAErI,sEAAsE;oBACtE,MAAM,cAAc,IAAI;oBACxB,MAAM,iBAAiB,IAAI;oBAC3B,IAAI,sBAAsB;oBAE1B,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,CAAA;wBACjC,IAAI,IAAI,QAAQ,CAAC,UAAU;4BACzB,YAAY,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS;wBACvC,OAAO,IAAI,IAAI,QAAQ,CAAC,aAAa;4BACnC,eAAe,GAAG,CAAC,IAAI,OAAO,CAAC,YAAY;wBAC7C;oBACF;oBAEA,uBAAuB;oBACvB,YAAY,OAAO,CAAC,CAAA;wBAClB,IAAI,eAAe,GAAG,CAAC,OAAO;4BAC5B;wBACF,OAAO;4BACL,0DAA0D;4BAC1D,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,KAAK,gDAAgD,CAAC;4BACnG,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG;4BACnC;wBACF;oBACF;oBAEA,mCAAmC;oBACnC,eAAe,OAAO,CAAC,CAAA;wBACrB,IAAI,CAAC,YAAY,GAAG,CAAC,UAAU;4BAC7B,uDAAuD;4BACvD,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,QAAQ,6CAA6C,CAAC;4BACnG,IAAI,OAAO,YAAY,UAAU;gCAC/B,aAAa,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC,IAAI;4BACnG;4BACA;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,oBAAoB,sBAAsB,CAAC;oBAC3F,QAAQ,GAAG,CAAC,uCAAuC,KAAK,SAAS,CAAC,eAAe,MAAM;gBACzF;gBAEA,+DAA+D;gBAC/D,IAAI,cAAc,WAAW,SAAS,kBACpC;oBAAC;oBAAc;oBAAsB;iBAAuB,CAAC,QAAQ,CAAC,QAAQ,cAAc,GAAG;oBAC/F,yFAAyF;oBACzF,MAAM,oBAAoB,OAAO,IAAI,CAAC;oBACtC,MAAM,cAAc,kBAAkB,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC,UAAU,MAAM;oBAEjF,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,YAAY,kBAAkB,CAAC;oBAClF,QAAQ,GAAG,CAAC,yCAAyC,KAAK,SAAS,CAAC,eAAe,MAAM;oBAEzF,uFAAuF;oBACvF,QAAQ,GAAG,CAAC;oBACZ,IAAI;wBACF,MAAM,sBAAsB,aAAa,OAAO,CAAC;wBACjD,IAAI,qBAAqB;4BACvB,MAAM,sBAAsB,KAAK,KAAK,CAAC;4BACvC,QAAQ,GAAG,CAAC,6DAA6D;4BAEzE,qDAAqD;4BACrD,IAAI,sBAAsB;4BAE1B,2CAA2C;4BAC3C,OAAO,OAAO,CAAC,qBAAqB,OAAO,CAAC,CAAC,CAAC,YAAY,QAAuB;gCAC/E,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;oCACnD,uBAAuB;oCACvB,MAAM,iBAAiB,eAAe,iBAAiB,kBACrD,eAAe,gBAAgB,eAAe;oCAEhD,aAAa,CAAC,GAAG,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;oCACtD,aAAa,CAAC,GAAG,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;oCAEjE,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,eAAe,YAAY,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,QAAQ,YAAY,EAAE;oCAC1H;oCAEA,+CAA+C;oCAC/C,IAAI,mBAAmB,YAAY;wCACjC,aAAa,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;wCAClD,aAAa,CAAC,GAAG,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wCAC7D,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,YAAY;oCACtE;gCACF;4BACF;4BACA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,oBAAoB,0CAA0C,CAAC;4BAClG,QAAQ,GAAG,CAAC,yCAAyC,KAAK,SAAS,CAAC,eAAe,MAAM;4BAEzF,mDAAmD;4BACnD,IAAI;gCACF,0FAA0F;gCAC1F,IAAI,UAAU,OAAO,aAAa,EAAE;oCAClC,MAAM,oBAAoB,IAAI,YAAY,yBAAyB;wCAAE,QAAQ;oCAAoB;oCACjG,OAAO,aAAa,CAAC;oCACrB,QAAQ,GAAG,CAAC;gCACd;4BACF,EAAE,OAAO,YAAY;gCACnB,QAAQ,KAAK,CAAC,iEAAiE;4BACjF;wBACF,OAAO;4BACL,QAAQ,GAAG,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sEAAsE;oBACtF;oBAEA,yEAAyE;oBACzE,sDAAsD;oBACtD,MAAM,sBAAsB;wBAAC;wBAAS;wBAAgB;wBAAiB;wBAAc;qBAAU;oBAE/F,sDAAsD;oBACtD,IAAI,mBAAmB;oBAEvB,oEAAoE;oBACpE,QAAQ,GAAG,CAAC,yDAAyD,KAAK,SAAS,CAAC,eAAe,MAAM;oBACzG,MAAM,gBAAgB,OAAO,IAAI,CAAC;oBAClC,QAAQ,GAAG,CAAC,8BAA8B,cAAc,IAAI,CAAC;oBAE7D,wEAAwE;oBACxE,MAAM,iBAAiB,IAAI;oBAE3B,8CAA8C;oBAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;wBAC7C,MAAM,MAAM,aAAa,CAAC,EAAE;wBAC5B,IAAI,IAAI,QAAQ,CAAC,UAAU;4BACzB,MAAM,UAAU,IAAI,OAAO,CAAC,SAAS;4BACrC,MAAM,aAAa,GAAG,QAAQ,QAAQ,CAAC;4BAEvC,+DAA+D;4BAC/D,MAAM,gBAAgB,YAAY,iBAAiB,kBACjD,YAAY,gBAAgB,eAAe;4BAE7C,4CAA4C;4BAC5C,IAAI,eAAe,GAAG,CAAC,gBAAgB;gCACrC;4BACF;4BAEA,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC,EAAE;gCACzC,MAAM,aAAa,CAAC,IAAI;gCACxB,SAAS,aAAa,CAAC,WAAW;4BACpC;4BAEA,IAAI,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,WAAW,EAAE;gCACnD;gCACA,eAAe,GAAG,CAAC;gCACnB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ,YAAY,EAAE,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,WAAW,EAAE;4BACzH;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,kBAAkB;oBAE5E,2EAA2E;oBAC3E,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;wBAC5B,MAAM,UAAU,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC;wBACrC,MAAM,aAAa,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC;wBAE3C,4CAA4C;wBAC5C,IAAI,eAAe,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;4BACxC;wBACF;wBAEA,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,WAAW,EAAE;4BACvD;4BACA,eAAe,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG;4BACnC,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,EAAE,YAAY,EAAE,aAAa,CAAC,QAAQ,CAAC,cAAc,EAAE,aAAa,CAAC,WAAW,EAAE;wBAC5I;oBACF;oBAEA,kFAAkF;oBAClF,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;wBAC5B,MAAM,UAAU,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC;wBAC3C,MAAM,aAAa,CAAC,gBAAgB,EAAE,EAAE,QAAQ,CAAC;wBAEjD,4CAA4C;wBAC5C,IAAI,eAAe,GAAG,CAAC,CAAC,gBAAgB,EAAE,GAAG,GAAG;4BAC9C;wBACF;wBAEA,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,WAAW,EAAE;4BACvD;4BACA,eAAe,GAAG,CAAC,CAAC,gBAAgB,EAAE,GAAG;4BACzC,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,EAAE,YAAY,EAAE,aAAa,CAAC,QAAQ,CAAC,cAAc,EAAE,aAAa,CAAC,WAAW,EAAE;wBACjJ;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,kBAAkB;oBAE5E,kEAAkE;oBAClE,qDAAqD;oBACrD,IAAI,mBAAmB,GAAG;wBACxB,QAAQ,GAAG,CAAC;wBAEZ,0DAA0D;wBAC1D,MAAM,oBAAsE,EAAE;wBAE9E,0DAA0D;wBAC1D,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;4BAC5B,MAAM,UAAU,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC;4BACrC,MAAM,aAAa,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC;4BAC3C,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,WAAW,IACrD,CAAC,eAAe,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,GAAG;gCACvC,kBAAkB,IAAI,CAAC;oCACrB,MAAM,aAAa,CAAC,QAAQ;oCAC5B,SAAS,aAAa,CAAC,WAAW;oCAClC,KAAK,CAAC,UAAU,EAAE,GAAG;gCACvB;gCACA,eAAe,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG;gCACnC,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,QAAQ,EAAE;4BACvE;wBACF;wBAEA,gEAAgE;wBAChE,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;4BAC5B,MAAM,UAAU,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC;4BAC3C,MAAM,aAAa,CAAC,gBAAgB,EAAE,EAAE,QAAQ,CAAC;4BACjD,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,WAAW,IACrD,CAAC,eAAe,GAAG,CAAC,CAAC,gBAAgB,EAAE,GAAG,GAAG;gCAC7C,kBAAkB,IAAI,CAAC;oCACrB,MAAM,aAAa,CAAC,QAAQ;oCAC5B,SAAS,aAAa,CAAC,WAAW;oCAClC,KAAK,CAAC,gBAAgB,EAAE,GAAG;gCAC7B;gCACA,eAAe,GAAG,CAAC,CAAC,gBAAgB,EAAE,GAAG;gCACzC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,QAAQ,EAAE;4BACtE;wBACF;wBAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,kBAAkB,MAAM,CAAC,0BAA0B,CAAC;wBAEzE,0DAA0D;wBAC1D,IAAI,kBAAkB;wBACtB,KAAK,MAAM,QAAQ,oBAAqB;4BACtC,uCAAuC;4BACvC,MAAM,iBAAiB,SAAS,kBAAkB,iBAChD,SAAS,eAAe,gBAAgB;4BAE1C,iDAAiD;4BACjD,IAAI,eAAe,GAAG,CAAC,SAAS,eAAe,GAAG,CAAC,iBAAiB;gCAClE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,KAAK,wBAAwB,CAAC;gCACtD;4BACF;4BAEA,IAAI,kBAAkB,kBAAkB,MAAM,EAAE;gCAC9C,qDAAqD;gCACrD,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,IAAI;gCACvE,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,OAAO;gCAC7E,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,EAAE;gCACvI;gCACA;gCACA,eAAe,GAAG,CAAC;gCAEnB,IAAI,oBAAoB,GAAG;oCACzB,QAAQ,GAAG,CAAC,CAAC,0DAA0D,CAAC;oCACxE;gCACF;4BACF;wBACF;wBAEA,kDAAkD;wBAClD,IAAI,mBAAmB,GAAG;4BACxB,QAAQ,GAAG,CAAC;4BAEZ,KAAK,MAAM,QAAQ,oBAAqB;gCACtC,uCAAuC;gCACvC,MAAM,iBAAiB,SAAS,kBAAkB,iBAChD,SAAS,eAAe,gBAAgB;gCAE1C,iDAAiD;gCACjD,IAAI,eAAe,GAAG,CAAC,SAAS,eAAe,GAAG,CAAC,iBAAiB;oCAClE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,KAAK,wCAAwC,CAAC;oCACtE;gCACF;gCAEA,uCAAuC;gCACvC,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAAI;gCAClH,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,IAAI;gCACvE;gCACA,eAAe,GAAG,CAAC;gCACnB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,KAAK,EAAE,EAAE,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE;gCAE7E,IAAI,oBAAoB,GAAG;oCACzB,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;oCAC/D;gCACF;4BACF;4BAEA,iFAAiF;4BACjF,IAAI,mBAAmB,GAAG;gCACxB,QAAQ,GAAG,CAAC;gCACZ,KAAK,MAAM,QAAQ,oBAAqB;oCACtC,IAAI,CAAC,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE;wCACvE,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAAI;wCACjF,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG;wCACnC;wCACA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,MAAM;wCACjD,IAAI,oBAAoB,GAAG;oCAC7B;gCACF;4BACF;wBACF;wBAEA,4BAA4B;wBAC5B,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,kBAAkB;oBAC7D;gBACF;gBAEA,iCAAiC;gBACjC,gBAAgB,gBAAgB,GAAG;gBACnC,gBAAgB,cAAc,GAAG;gBAEjC,iFAAiF;gBACjF,IAAI,cAAc,SAAS;oBACzB,6DAA6D;oBAC7D,MAAM,cAAc,IAAI;oBACxB,MAAM,iBAAiB,IAAI;oBAC3B,IAAI,sBAAsB;oBAE1B,wCAAmB;wBACjB,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,CAAA;4BACjC,IAAI,IAAI,QAAQ,CAAC,UAAU;gCACzB,YAAY,GAAG,CAAC,IAAI,OAAO,CAAC,SAAS;4BACvC,OAAO,IAAI,IAAI,QAAQ,CAAC,aAAa;gCACnC,eAAe,GAAG,CAAC,IAAI,OAAO,CAAC,YAAY;4BAC7C;wBACF;wBAEA,uBAAuB;wBACvB,YAAY,OAAO,CAAC,CAAA;4BAClB,IAAI,eAAe,GAAG,CAAC,OAAO;gCAC5B;4BACF;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,oBAAoB,6CAA6C,CAAC;oBAElH,+DAA+D;oBAC/D,IAAI,sBAAsB,GAAG;wBAC3B,QAAQ,GAAG,CAAC,CAAC,iEAAiE,CAAC;wBAE/E,MAAM,sBAAsB;4BAAC;4BAAS;4BAAgB;4BAAiB;4BAAc;yBAAU;wBAE/F,KAAK,MAAM,QAAQ,oBAAqB;4BACtC,0CAA0C;4BAC1C,IAAI,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE;gCACrE;4BACF;4BAEA,kCAAkC;4BAClC,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAAI;4BAClH,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,IAAI;4BACvE;4BAEA,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,MAAM;4BAEtE,IAAI,uBAAuB,GAAG;gCAC5B;4BACF;wBACF;wBAEA,iDAAiD;wBACjD,gBAAgB,cAAc,GAAG;wBACjC,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,oBAAoB,sBAAsB,CAAC;oBAChG;gBACF;gBAEA,QAAQ,GAAG,CAAC,oCAAoC,KAAK,SAAS,CAAC,iBAAiB,MAAM;gBAEtF,sBAAsB;gBACtB,MAAM,mBAAmB,MAAM,cAAc,cAAc,CAAC;gBAE5D,4BAA4B;gBAC5B,QAAQ,GAAG,CAAC,6BAA6B,KAAK,SAAS,CAAC,kBAAkB,MAAM;gBAEhF,0BAA0B;gBAC1B,IAAI,YAAY,WAAW;gBAE3B,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT,EAAE,OAAO,eAAoB;gBAC3B,QAAQ,KAAK,CAAC,kCAAkC;gBAEhD,6CAA6C;gBAC7C,IAAI,cAAc,OAAO,IAAI,cAAc,OAAO,CAAC,QAAQ,CAAC,UAAU;oBACpE,QAAQ,KAAK,CAAC,iCAAiC,cAAc,OAAO;oBACpE,MAAM;wBAAE,OAAO;oBAAyC;gBAC1D;gBAEA,sBAAsB;gBACtB,MAAM,eAAe,cAAc,QAAQ,EAAE,MAAM,SACjD,cAAc,OAAO,IACrB;gBAEF,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,MAAM;oBAAE,OAAO;gBAAa;YAC9B;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,6CAA6C;YAC7C,IAAI,MAAM,KAAK,IAAI,OAAO,MAAM,KAAK,KAAK,YAAY,MAAM,KAAK,CAAC,QAAQ,CAAC,UAAU;gBACnF,QAAQ,KAAK,CAAC;gBACd,MAAM;oBAAE,OAAO;gBAAyC;YAC1D;YAEA,gEAAgE;YAChE,IAAI,MAAM,KAAK,EAAE;gBACf,MAAM;YACR;YAEA,8BAA8B;YAC9B,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAAE,OAAO;YAAwB;QACjE;IACF;AACF;uCAEe;IACb;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 1260, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/contexts/AuthContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  createContext,\r\n  useState,\r\n  useContext,\r\n  ReactNode,\r\n  useEffect,\r\n} from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useUser } from \"@clerk/nextjs\";\r\nimport { authService } from \"../services/api\";\r\nimport { userService } from \"../services/api\";\r\nimport { UserProfile } from \"../utils/auth\";\r\n\r\ninterface AuthContextType {\r\n  isAuthenticated: boolean;\r\n  userProfile: UserProfile | null;\r\n  isLoading: boolean;\r\n  login: (token: string) => void;\r\n  logout: () => void;\r\n  updateProfile: (profile: UserProfile) => void;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({\r\n  children,\r\n}) => {\r\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);\r\n  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);\r\n  const [isLoading, setIsLoading] = useState<boolean>(true);\r\n  const router = useRouter();\r\n  const { isSignedIn, isLoaded: isClerkLoaded, user: clerkUser } = useUser();\r\n  const [authInitialized, setAuthInitialized] = useState<boolean>(false);\r\n\r\n  useEffect(() => {\r\n    // Function to initialize authentication\r\n    const initAuth = async () => {\r\n      setIsLoading(true);\r\n      try {\r\n        // Check if we have a token (including vendor token)\r\n        const token =\r\n          localStorage.getItem(\"token\") || localStorage.getItem(\"jwt_token\");\r\n\r\n        console.log(\r\n          `Auth context initialization: Token ${token ? \"found\" : \"not found\"}`\r\n        );\r\n        console.log(\r\n          `Clerk authentication: ${isClerkLoaded\r\n            ? isSignedIn\r\n              ? \"signed in\"\r\n              : \"not signed in\"\r\n            : \"loading\"\r\n          }`\r\n        );\r\n\r\n        // Check if authenticated with Clerk\r\n        if (isClerkLoaded && isSignedIn) {\r\n          console.log(\"User is authenticated with Clerk\");\r\n          setIsAuthenticated(true);\r\n\r\n          // If we don't have a token but have Clerk auth, try to get one\r\n          if (!token && clerkUser) {\r\n            try {\r\n              // This assumes you have an endpoint to convert Clerk session to JWT\r\n              console.log(\"Getting token from Clerk session\");\r\n              const sessions = await clerkUser.getSessions();\r\n              const clerkToken = sessions[0]?.id;\r\n\r\n              if (clerkToken) {\r\n                // Exchange Clerk token for your JWT\r\n                // Extract user info to send along with the token\r\n                const userEmail =\r\n                  clerkUser?.primaryEmailAddress?.emailAddress || \"\";\r\n                const userName = clerkUser?.fullName || \"\";\r\n                const userId = clerkUser?.id || \"\";\r\n\r\n                console.log(\"Sending user info to backend:\", {\r\n                  email: userEmail,\r\n                  name: userName,\r\n                  id: userId,\r\n                });\r\n\r\n                const response = await authService.clerkAuth({\r\n                  clerk_token: clerkToken,\r\n                  user_type: \"customer\",\r\n                  user_email: userEmail,\r\n                  user_name: userName,\r\n                  user_id: userId,\r\n                });\r\n\r\n                if (response && response.token) {\r\n                  localStorage.setItem(\"token\", response.token);\r\n                  localStorage.setItem(\"wedzat_token\", response.token); // Also save as wedzat_token for consistency\r\n                  console.log(\"Token obtained from Clerk session\");\r\n                } else {\r\n                  // If backend auth fails, store the Clerk token as a fallback\r\n                  console.log(\r\n                    \"No token received from backend, using Clerk token as fallback\"\r\n                  );\r\n                  // Use the token we already have\r\n                  localStorage.setItem(\"token\", clerkToken);\r\n                  localStorage.setItem(\"wedzat_token\", clerkToken);\r\n                }\r\n              }\r\n            } catch (error) {\r\n              console.error(\"Error getting token from Clerk session:\", error);\r\n\r\n              // If there's an error with the backend, use the Clerk token as a fallback\r\n              // Get the token again if needed\r\n              try {\r\n                const sessions = await clerkUser.getSessions();\r\n                const fallbackToken = sessions[0]?.id;\r\n\r\n                if (fallbackToken) {\r\n                  console.log(\r\n                    \"Using Clerk token as fallback due to backend error\"\r\n                  );\r\n                  localStorage.setItem(\"token\", fallbackToken);\r\n                  localStorage.setItem(\"wedzat_token\", fallbackToken);\r\n                }\r\n              } catch (tokenError) {\r\n                console.error(\"Error getting fallback token:\", tokenError);\r\n              }\r\n            }\r\n          }\r\n\r\n          setIsLoading(false);\r\n          setAuthInitialized(true);\r\n          return;\r\n        }\r\n\r\n        if (!token) {\r\n          setIsAuthenticated(false);\r\n          setUserProfile(null);\r\n          setIsLoading(false);\r\n          setAuthInitialized(true);\r\n\r\n          // If we're on a protected route, redirect to login\r\n          if (typeof window !== \"undefined\") {\r\n            const pathname = window.location.pathname;\r\n            const protectedRoutes = [\r\n              \"/home\",\r\n              \"/dashboard\",\r\n              \"/profile\",\r\n              \"/protected\",\r\n            ];\r\n\r\n            if (\r\n              protectedRoutes.some(\r\n                (route) =>\r\n                  pathname === route || pathname.startsWith(`${route}/`)\r\n              )\r\n            ) {\r\n              console.log(\r\n                \"No token found on protected route, redirecting to login\"\r\n              );\r\n              router.push(\"/\");\r\n            }\r\n          }\r\n          return;\r\n        }\r\n\r\n        // Attempt to get user profile\r\n        try {\r\n          console.log(\"Fetching user profile\");\r\n\r\n          // Check if this is a vendor token\r\n          const isVendorToken = localStorage.getItem(\"is_vendor\") === \"true\";\r\n\r\n          if (isVendorToken) {\r\n            console.log(\"Detected vendor token, setting authenticated state\");\r\n            // For vendors, we don't need to fetch a profile, just set authenticated\r\n            setIsAuthenticated(true);\r\n            setUserProfile(null); // No regular user profile for vendors\r\n          } else {\r\n            // For regular users, fetch the profile\r\n            const profile = await userService.getUserDetails();\r\n            setUserProfile(profile);\r\n            setIsAuthenticated(true);\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching user profile:\", error);\r\n\r\n          // Check if this might be a vendor token\r\n          try {\r\n            // If we're on a vendor page, this might be a vendor token\r\n            const pathname = window.location.pathname;\r\n            if (pathname.includes(\"/vendor/\")) {\r\n              console.log(\"On vendor page with possible vendor token, setting authenticated\");\r\n              localStorage.setItem(\"is_vendor\", \"true\");\r\n              setIsAuthenticated(true);\r\n              setUserProfile(null);\r\n              return;\r\n            }\r\n          } catch (vendorCheckError) {\r\n            console.error(\"Error checking for vendor token:\", vendorCheckError);\r\n          }\r\n\r\n          // Invalid or expired token\r\n          authService.logout();\r\n          setIsAuthenticated(false);\r\n          setUserProfile(null);\r\n\r\n          // Redirect to login if on a protected page\r\n          if (typeof window !== \"undefined\") {\r\n            const pathname = window.location.pathname;\r\n            if (pathname !== \"/\" && !pathname.includes(\"/auth/\")) {\r\n              router.push(\"/\");\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Auth initialization error:\", error);\r\n        setIsAuthenticated(false);\r\n        setUserProfile(null);\r\n      } finally {\r\n        setIsLoading(false);\r\n        setAuthInitialized(true);\r\n      }\r\n    };\r\n\r\n    // Only run initAuth when Clerk has loaded\r\n    if (isClerkLoaded) {\r\n      initAuth();\r\n    }\r\n  }, [isClerkLoaded, isSignedIn, clerkUser, router]);\r\n\r\n  // Add a backup timeout to prevent infinite loading state\r\n  useEffect(() => {\r\n    const loadingTimeoutId = setTimeout(() => {\r\n      if (isLoading && !authInitialized) {\r\n        console.log(\"Auth initialization timed out, resetting loading state\");\r\n        setIsLoading(false);\r\n        setAuthInitialized(true);\r\n\r\n        // Check if we have a token before redirecting\r\n        const token = localStorage.getItem(\"token\") || \r\n                      localStorage.getItem(\"jwt_token\") || \r\n                      localStorage.getItem(\"wedzat_token\");\r\n        \r\n        // Only redirect if no token is found\r\n        if (!token && typeof window !== \"undefined\") {\r\n          const pathname = window.location.pathname;\r\n          const protectedRoutes = [\r\n            \"/home\",\r\n            \"/dashboard\",\r\n            \"/profile\",\r\n            \"/protected\",\r\n          ];\r\n\r\n          if (\r\n            protectedRoutes.some(\r\n              (route) => pathname === route || pathname.startsWith(`${route}/`)\r\n            )\r\n          ) {\r\n            console.log(\"No token found, redirecting to login\");\r\n            router.push(\"/\");\r\n          }\r\n        }\r\n      }\r\n    }, 10000); // Increased to 10 seconds for more time to initialize\r\n\r\n    return () => clearTimeout(loadingTimeoutId);\r\n  }, [isLoading, authInitialized, router]);\r\n\r\n  // Add listener for storage events to handle token removal in other tabs\r\n  useEffect(() => {\r\n    const handleStorageChange = (e: StorageEvent) => {\r\n      if (e.key === \"token\" || e.key === \"jwt_token\") {\r\n        // If token was removed\r\n        if (!e.newValue) {\r\n          console.log(\"Token removed in another tab/window\");\r\n          setIsAuthenticated(false);\r\n          setUserProfile(null);\r\n\r\n          // If on a protected route, redirect to login\r\n          if (typeof window !== \"undefined\") {\r\n            const pathname = window.location.pathname;\r\n            const protectedRoutes = [\r\n              \"/home\",\r\n              \"/dashboard\",\r\n              \"/profile\",\r\n              \"/protected\",\r\n            ];\r\n\r\n            if (\r\n              protectedRoutes.some(\r\n                (route) =>\r\n                  pathname === route || pathname.startsWith(`${route}/`)\r\n              )\r\n            ) {\r\n              router.push(\"/\");\r\n            }\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n    return () => window.removeEventListener(\"storage\", handleStorageChange);\r\n  }, [router]);\r\n\r\n  const login = (token: string) => {\r\n    console.log(\"Storing token and setting authenticated state\");\r\n    localStorage.setItem(\"token\", token);\r\n    localStorage.setItem(\"jwt_token\", token); // Store in both keys for compatibility\r\n    setIsAuthenticated(true);\r\n\r\n    // Fetch user profile after login\r\n    userService\r\n      .getUserDetails()\r\n      .then((profile) => {\r\n        console.log(\"User profile fetched:\", profile);\r\n        setUserProfile(profile);\r\n      })\r\n      .catch((error) => console.error(\"Error fetching user profile:\", error));\r\n  };\r\n\r\n  const logout = () => {\r\n    console.log(\"Logging out\");\r\n    authService.logout();\r\n    setIsAuthenticated(false);\r\n    setUserProfile(null);\r\n    router.push(\"/\");\r\n  };\r\n\r\n  const updateProfile = (profile: UserProfile) => {\r\n    console.log(\"Updating user profile\", profile);\r\n    setUserProfile(profile);\r\n  };\r\n\r\n  // Refresh user profile data\r\n  useEffect(() => {\r\n    // Only refresh if already authenticated\r\n    if (isAuthenticated && !isLoading) {\r\n      const refreshInterval = setInterval(() => {\r\n        console.log(\"Refreshing user profile data\");\r\n        userService.getUserDetails()\r\n          .then(profile => {\r\n            console.log(\"Refreshed user profile:\", profile);\r\n            setUserProfile(profile);\r\n          })\r\n          .catch(error => {\r\n            console.error(\"Error refreshing user profile:\", error);\r\n            // Don't log out on profile refresh errors\r\n            // This prevents unnecessary redirects\r\n          });\r\n      }, 300000); // Refresh every 5 minutes instead of every minute\r\n\r\n      return () => clearInterval(refreshInterval);\r\n    }\r\n  }, [isAuthenticated, isLoading]);\r\n\r\n  return (\r\n    <AuthContext.Provider\r\n      value={{\r\n        isAuthenticated,\r\n        userProfile,\r\n        isLoading,\r\n        login,\r\n        logout,\r\n        updateProfile,\r\n      }}\r\n    >\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useAuth = (): AuthContextType => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useAuth must be used within an AuthProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAOA;AACA;AACA;;;AAXA;;;;;;AAwBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAkD,CAAC,EAC9D,QAAQ,EACT;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,UAAU,aAAa,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,+JAA<PERSON>,CAAA,UAAO,AAAD;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,wCAAwC;YACxC,MAAM;mDAAW;oBACf,aAAa;oBACb,IAAI;wBACF,oDAAoD;wBACpD,MAAM,QACJ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;wBAExD,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,QAAQ,UAAU,aAAa;wBAEvE,QAAQ,GAAG,CACT,CAAC,sBAAsB,EAAE,gBACrB,aACE,cACA,kBACF,WACF;wBAGJ,oCAAoC;wBACpC,IAAI,iBAAiB,YAAY;4BAC/B,QAAQ,GAAG,CAAC;4BACZ,mBAAmB;4BAEnB,+DAA+D;4BAC/D,IAAI,CAAC,SAAS,WAAW;gCACvB,IAAI;oCACF,oEAAoE;oCACpE,QAAQ,GAAG,CAAC;oCACZ,MAAM,WAAW,MAAM,UAAU,WAAW;oCAC5C,MAAM,aAAa,QAAQ,CAAC,EAAE,EAAE;oCAEhC,IAAI,YAAY;wCACd,oCAAoC;wCACpC,iDAAiD;wCACjD,MAAM,YACJ,WAAW,qBAAqB,gBAAgB;wCAClD,MAAM,WAAW,WAAW,YAAY;wCACxC,MAAM,SAAS,WAAW,MAAM;wCAEhC,QAAQ,GAAG,CAAC,iCAAiC;4CAC3C,OAAO;4CACP,MAAM;4CACN,IAAI;wCACN;wCAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,cAAW,CAAC,SAAS,CAAC;4CAC3C,aAAa;4CACb,WAAW;4CACX,YAAY;4CACZ,WAAW;4CACX,SAAS;wCACX;wCAEA,IAAI,YAAY,SAAS,KAAK,EAAE;4CAC9B,aAAa,OAAO,CAAC,SAAS,SAAS,KAAK;4CAC5C,aAAa,OAAO,CAAC,gBAAgB,SAAS,KAAK,GAAG,4CAA4C;4CAClG,QAAQ,GAAG,CAAC;wCACd,OAAO;4CACL,6DAA6D;4CAC7D,QAAQ,GAAG,CACT;4CAEF,gCAAgC;4CAChC,aAAa,OAAO,CAAC,SAAS;4CAC9B,aAAa,OAAO,CAAC,gBAAgB;wCACvC;oCACF;gCACF,EAAE,OAAO,OAAO;oCACd,QAAQ,KAAK,CAAC,2CAA2C;oCAEzD,0EAA0E;oCAC1E,gCAAgC;oCAChC,IAAI;wCACF,MAAM,WAAW,MAAM,UAAU,WAAW;wCAC5C,MAAM,gBAAgB,QAAQ,CAAC,EAAE,EAAE;wCAEnC,IAAI,eAAe;4CACjB,QAAQ,GAAG,CACT;4CAEF,aAAa,OAAO,CAAC,SAAS;4CAC9B,aAAa,OAAO,CAAC,gBAAgB;wCACvC;oCACF,EAAE,OAAO,YAAY;wCACnB,QAAQ,KAAK,CAAC,iCAAiC;oCACjD;gCACF;4BACF;4BAEA,aAAa;4BACb,mBAAmB;4BACnB;wBACF;wBAEA,IAAI,CAAC,OAAO;4BACV,mBAAmB;4BACnB,eAAe;4BACf,aAAa;4BACb,mBAAmB;4BAEnB,mDAAmD;4BACnD,wCAAmC;gCACjC,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;gCACzC,MAAM,kBAAkB;oCACtB;oCACA;oCACA;oCACA;iCACD;gCAED,IACE,gBAAgB,IAAI;uEAClB,CAAC,QACC,aAAa,SAAS,SAAS,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC;uEAEzD;oCACA,QAAQ,GAAG,CACT;oCAEF,OAAO,IAAI,CAAC;gCACd;4BACF;4BACA;wBACF;wBAEA,8BAA8B;wBAC9B,IAAI;4BACF,QAAQ,GAAG,CAAC;4BAEZ,kCAAkC;4BAClC,MAAM,gBAAgB,aAAa,OAAO,CAAC,iBAAiB;4BAE5D,IAAI,eAAe;gCACjB,QAAQ,GAAG,CAAC;gCACZ,wEAAwE;gCACxE,mBAAmB;gCACnB,eAAe,OAAO,sCAAsC;4BAC9D,OAAO;gCACL,uCAAuC;gCACvC,MAAM,UAAU,MAAM,kHAAA,CAAA,cAAW,CAAC,cAAc;gCAChD,eAAe;gCACf,mBAAmB;4BACrB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,gCAAgC;4BAE9C,wCAAwC;4BACxC,IAAI;gCACF,0DAA0D;gCAC1D,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;gCACzC,IAAI,SAAS,QAAQ,CAAC,aAAa;oCACjC,QAAQ,GAAG,CAAC;oCACZ,aAAa,OAAO,CAAC,aAAa;oCAClC,mBAAmB;oCACnB,eAAe;oCACf;gCACF;4BACF,EAAE,OAAO,kBAAkB;gCACzB,QAAQ,KAAK,CAAC,oCAAoC;4BACpD;4BAEA,2BAA2B;4BAC3B,kHAAA,CAAA,cAAW,CAAC,MAAM;4BAClB,mBAAmB;4BACnB,eAAe;4BAEf,2CAA2C;4BAC3C,wCAAmC;gCACjC,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;gCACzC,IAAI,aAAa,OAAO,CAAC,SAAS,QAAQ,CAAC,WAAW;oCACpD,OAAO,IAAI,CAAC;gCACd;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;wBAC5C,mBAAmB;wBACnB,eAAe;oBACjB,SAAU;wBACR,aAAa;wBACb,mBAAmB;oBACrB;gBACF;;YAEA,0CAA0C;YAC1C,IAAI,eAAe;gBACjB;YACF;QACF;iCAAG;QAAC;QAAe;QAAY;QAAW;KAAO;IAEjD,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,mBAAmB;2DAAW;oBAClC,IAAI,aAAa,CAAC,iBAAiB;wBACjC,QAAQ,GAAG,CAAC;wBACZ,aAAa;wBACb,mBAAmB;wBAEnB,8CAA8C;wBAC9C,MAAM,QAAQ,aAAa,OAAO,CAAC,YACrB,aAAa,OAAO,CAAC,gBACrB,aAAa,OAAO,CAAC;wBAEnC,qCAAqC;wBACrC,IAAI,CAAC,SAAS,aAAkB,aAAa;4BAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;4BACzC,MAAM,kBAAkB;gCACtB;gCACA;gCACA;gCACA;6BACD;4BAED,IACE,gBAAgB,IAAI;2EAClB,CAAC,QAAU,aAAa,SAAS,SAAS,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC;2EAElE;gCACA,QAAQ,GAAG,CAAC;gCACZ,OAAO,IAAI,CAAC;4BACd;wBACF;oBACF;gBACF;0DAAG,QAAQ,sDAAsD;YAEjE;0CAAO,IAAM,aAAa;;QAC5B;iCAAG;QAAC;QAAW;QAAiB;KAAO;IAEvC,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;8DAAsB,CAAC;oBAC3B,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,aAAa;wBAC9C,uBAAuB;wBACvB,IAAI,CAAC,EAAE,QAAQ,EAAE;4BACf,QAAQ,GAAG,CAAC;4BACZ,mBAAmB;4BACnB,eAAe;4BAEf,6CAA6C;4BAC7C,wCAAmC;gCACjC,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;gCACzC,MAAM,kBAAkB;oCACtB;oCACA;oCACA;oCACA;iCACD;gCAED,IACE,gBAAgB,IAAI;kFAClB,CAAC,QACC,aAAa,SAAS,SAAS,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC;kFAEzD;oCACA,OAAO,IAAI,CAAC;gCACd;4BACF;wBACF;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;0CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;iCAAG;QAAC;KAAO;IAEX,MAAM,QAAQ,CAAC;QACb,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,CAAC,SAAS;QAC9B,aAAa,OAAO,CAAC,aAAa,QAAQ,uCAAuC;QACjF,mBAAmB;QAEnB,iCAAiC;QACjC,kHAAA,CAAA,cAAW,CACR,cAAc,GACd,IAAI,CAAC,CAAC;YACL,QAAQ,GAAG,CAAC,yBAAyB;YACrC,eAAe;QACjB,GACC,KAAK,CAAC,CAAC,QAAU,QAAQ,KAAK,CAAC,gCAAgC;IACpE;IAEA,MAAM,SAAS;QACb,QAAQ,GAAG,CAAC;QACZ,kHAAA,CAAA,cAAW,CAAC,MAAM;QAClB,mBAAmB;QACnB,eAAe;QACf,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB,CAAC;QACrB,QAAQ,GAAG,CAAC,yBAAyB;QACrC,eAAe;IACjB;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,wCAAwC;YACxC,IAAI,mBAAmB,CAAC,WAAW;gBACjC,MAAM,kBAAkB;8DAAY;wBAClC,QAAQ,GAAG,CAAC;wBACZ,kHAAA,CAAA,cAAW,CAAC,cAAc,GACvB,IAAI;sEAAC,CAAA;gCACJ,QAAQ,GAAG,CAAC,2BAA2B;gCACvC,eAAe;4BACjB;qEACC,KAAK;sEAAC,CAAA;gCACL,QAAQ,KAAK,CAAC,kCAAkC;4BAChD,0CAA0C;4BAC1C,sCAAsC;4BACxC;;oBACJ;6DAAG,SAAS,kDAAkD;gBAE9D;8CAAO,IAAM,cAAc;;YAC7B;QACF;iCAAG;QAAC;QAAiB;KAAU;IAE/B,qBACE,6LAAC,YAAY,QAAQ;QACnB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAvVa;;QAMI,qIAAA,CAAA,YAAS;QACyC,+JAAA,CAAA,UAAO;;;KAP7D;AAyVN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1626, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/utils/uploadUtils.ts"], "sourcesContent": ["// utils/uploadUtils.ts\r\n\r\n/**\r\n * Validate a file before upload\r\n * @param file The file to validate\r\n * @param mediaType The media type to validate against\r\n * @returns An object containing validation result and error message if any\r\n */\r\nexport const validateFile = (\r\n    file: File,\r\n    mediaType: 'photo' | 'video'\r\n  ): { isValid: boolean; error?: string } => {\r\n    // Check if file exists\r\n    if (!file) {\r\n      return { isValid: false, error: 'No file selected' };\r\n    }\r\n\r\n    // Check file type based on mediaType\r\n    if (mediaType === 'photo') {\r\n      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n      if (!validTypes.includes(file.type)) {\r\n        return {\r\n          isValid: false,\r\n          error: 'Invalid file format. Please upload a JPEG, PNG, GIF, or WebP image.',\r\n        };\r\n      }\r\n\r\n      // Check file size (5GB max for photos)\r\n      const maxSize = 5000 * 1024 * 1024; // 5GB in bytes\r\n      if (file.size > maxSize) {\r\n        return {\r\n          isValid: false,\r\n          error: 'File is too large. Maximum size for photos is 5GB.',\r\n        };\r\n      }\r\n    } else if (mediaType === 'video') {\r\n      const validTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm'];\r\n      if (!validTypes.includes(file.type)) {\r\n        return {\r\n          isValid: false,\r\n          error: 'Invalid file format. Please upload an MP4, MOV, AVI, or WebM video.',\r\n        };\r\n      }\r\n\r\n      // Check file size (5GB max for videos)\r\n      const maxSize = 5 * 1024 * 1024 * 1024; // 5GB in bytes\r\n      if (file.size > maxSize) {\r\n        return {\r\n          isValid: false,\r\n          error: 'File is too large. Maximum size for videos is 5GB.',\r\n        };\r\n      }\r\n    }\r\n\r\n    return { isValid: true };\r\n  };\r\n\r\n  /**\r\n   * Get the video duration from a video file\r\n   * @param videoFile The video file\r\n   * @returns A promise that resolves to the duration in seconds\r\n   */\r\n  export const getVideoDuration = (videoFile: File): Promise<number> => {\r\n    return new Promise((resolve, reject) => {\r\n      try {\r\n        const videoElement = document.createElement('video');\r\n        videoElement.preload = 'metadata';\r\n\r\n        videoElement.onloadedmetadata = () => {\r\n          URL.revokeObjectURL(videoElement.src);\r\n          resolve(videoElement.duration);\r\n        };\r\n\r\n        videoElement.onerror = () => {\r\n          URL.revokeObjectURL(videoElement.src);\r\n          reject(new Error('Error loading video file'));\r\n        };\r\n\r\n        videoElement.src = URL.createObjectURL(videoFile);\r\n      } catch (error) {\r\n        reject(error);\r\n      }\r\n    });\r\n  };\r\n\r\n  /**\r\n   * Format file size into a human-readable string\r\n   * @param bytes Size in bytes\r\n   * @returns Formatted string (e.g., \"1.5 MB\")\r\n   */\r\n  export const formatFileSize = (bytes: number): string => {\r\n    if (bytes === 0) return '0 Bytes';\r\n\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  /**\r\n   * Extract maximum allowed size for a media type and category\r\n   * @param mediaType The media type (photo or video)\r\n   * @param category The category within that media type\r\n   * @returns The maximum size in MB\r\n   */\r\n  // Define video restrictions with both min and max durations\r\n  const VIDEO_RESTRICTIONS: Record<string, {\r\n    max_size_mb: number,\r\n    min_duration_seconds: number,\r\n    max_duration_seconds: number\r\n  }> = {\r\n    'story': { max_size_mb: 5000, min_duration_seconds: 0, max_duration_seconds: 60 },\r\n    'flash': { max_size_mb: 5000, min_duration_seconds: 0, max_duration_seconds: 90 },\r\n    'glimpse': { max_size_mb: 5000, min_duration_seconds: 0, max_duration_seconds: 420 },\r\n    'movie': { max_size_mb: 5000, min_duration_seconds: 0, max_duration_seconds: 3600 }\r\n  };\r\n\r\n  export const getMaxFileSize = (mediaType: 'photo' | 'video', category: string): number => {\r\n    const PHOTO_RESTRICTIONS: Record<string, { max_size_mb: number }> = {\r\n      'story': { max_size_mb: 5000 },\r\n      'post': { max_size_mb: 5000 }\r\n    };\r\n\r\n    if (mediaType === 'video') {\r\n      return VIDEO_RESTRICTIONS[category]?.max_size_mb || 5000; // Default to 5GB if category not found\r\n    } else {\r\n      return PHOTO_RESTRICTIONS[category]?.max_size_mb || 5000; // Default to 5GB if category not found\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Get maximum video duration for a video category\r\n   * @param category The video category\r\n   * @returns Max duration in seconds, or undefined if not applicable\r\n   */\r\n  export const getMaxVideoDuration = (category: string): number | undefined => {\r\n    return VIDEO_RESTRICTIONS[category]?.max_duration_seconds;\r\n  };\r\n\r\n  /**\r\n   * Get minimum video duration for a video category\r\n   * @param category The video category\r\n   * @returns Min duration in seconds, or undefined if not applicable\r\n   */\r\n  export const getMinVideoDuration = (category: string): number | undefined => {\r\n    return VIDEO_RESTRICTIONS[category]?.min_duration_seconds;\r\n  };\r\n\r\n  // Note: formatTime function is defined later in this file\r\n\r\n  /**\r\n   * Validate if a video duration is appropriate for a given category\r\n   * @param duration Video duration in seconds\r\n   * @param category The video category\r\n   * @returns Validation result with error message if invalid and suggested category\r\n   */\r\n  export const validateVideoDuration = (duration: number, category: string): {\r\n    isValid: boolean;\r\n    error?: string;\r\n    suggestedCategory?: string;\r\n  } => {\r\n    const minDuration = getMinVideoDuration(category);\r\n    const maxDuration = getMaxVideoDuration(category);\r\n\r\n    if (minDuration === undefined || maxDuration === undefined) {\r\n      return { isValid: true }; // No restrictions for this category\r\n    }\r\n\r\n    // Get user-friendly category names\r\n    const getCategoryDisplayName = (cat: string): string => {\r\n      switch (cat) {\r\n        case 'story': return 'Moments';\r\n        case 'flash': return 'Flash';\r\n        case 'glimpse': return 'Glimpse';\r\n        case 'movie': return 'Movie';\r\n        default: return cat;\r\n      }\r\n    };\r\n\r\n    const categoryDisplayName = getCategoryDisplayName(category);\r\n\r\n    // Find the appropriate category for this duration, but never suggest 'story' (moments) for other categories\r\n    let appropriateCategory = Object.entries(VIDEO_RESTRICTIONS).find(([cat, restrictions]) =>\r\n      cat !== 'story' && // Never suggest 'story' (moments) for other categories\r\n      restrictions.min_duration_seconds <= duration &&\r\n      restrictions.max_duration_seconds >= duration\r\n    )?.[0];\r\n\r\n    if (duration > maxDuration) {\r\n      let errorMessage = `Your video is ${formatTime(duration)} long, which is too long for the ${categoryDisplayName} category.`;\r\n      errorMessage += `\\n\\nMaximum duration for ${categoryDisplayName} is ${formatTime(maxDuration)}.`;\r\n\r\n      if (appropriateCategory) {\r\n        const appropriateCategoryDisplay = getCategoryDisplayName(appropriateCategory);\r\n        errorMessage += `\\n\\nThis video would fit better in the ${appropriateCategoryDisplay} category.`;\r\n      }\r\n\r\n      return {\r\n        isValid: false,\r\n        error: errorMessage,\r\n        suggestedCategory: appropriateCategory\r\n      };\r\n    }\r\n\r\n    // If we have a more appropriate category but the video still fits in the current category,\r\n    // suggest the better category but don't mark it as invalid\r\n    // Never suggest 'story' (moments) for other categories\r\n    if (appropriateCategory && appropriateCategory !== category && appropriateCategory !== 'story') {\r\n      const appropriateCategoryDisplay = getCategoryDisplayName(appropriateCategory);\r\n      const message = `Your video is ${formatTime(duration)} long. It would fit better in the ${appropriateCategoryDisplay} category, but it's still valid for ${categoryDisplayName}.`;\r\n\r\n      return {\r\n        isValid: true,\r\n        error: message,\r\n        suggestedCategory: appropriateCategory\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n  };\r\n\r\n  /**\r\n   * Format time in seconds to a readable format (MM:SS or HH:MM:SS)\r\n   * @param seconds Time in seconds\r\n   * @returns Formatted time string\r\n   */\r\n  export const formatTime = (seconds: number): string => {\r\n    const hours = Math.floor(seconds / 3600);\r\n    const minutes = Math.floor((seconds % 3600) / 60);\r\n    const secs = Math.floor(seconds % 60);\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\r\n    }\r\n\r\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  /**\r\n   * Get a list of suggested tags based on file metadata and category\r\n   * @param file The file being uploaded\r\n   * @param category The selected category\r\n   * @returns Array of suggested tags\r\n   */\r\n  export const getSuggestedTags = (file: File, category: string): string[] => {\r\n    const suggestions: string[] = [];\r\n\r\n    // Add category as a tag\r\n    suggestions.push(category);\r\n\r\n    // Add file type as a tag\r\n    if (file.type.startsWith('image/')) {\r\n      suggestions.push('photo');\r\n\r\n      // Add specific image type\r\n      const format = file.type.split('/')[1];\r\n      if (format) {\r\n        suggestions.push(format);\r\n      }\r\n    } else if (file.type.startsWith('video/')) {\r\n      suggestions.push('video');\r\n\r\n      // Add specific video type\r\n      const format = file.type.split('/')[1];\r\n      if (format) {\r\n        suggestions.push(format);\r\n      }\r\n    }\r\n\r\n    // Add suggestions based on category\r\n    switch (category) {\r\n      case 'story':\r\n        suggestions.push('moments', 'daily', 'highlight');\r\n        break;\r\n      case 'glimpse':\r\n        suggestions.push('preview', 'teaser', 'sneak-peek');\r\n        break;\r\n      case 'short':\r\n        suggestions.push('clip', 'short-form', 'brief');\r\n        break;\r\n      case 'long':\r\n        suggestions.push('full-length', 'documentary', 'extended');\r\n        break;\r\n      case 'post':\r\n        suggestions.push('album', 'gallery', 'collection');\r\n        break;\r\n    }\r\n\r\n    return [...new Set(suggestions)]; // Remove duplicates\r\n  };"], "names": [], "mappings": "AAAA,uBAAuB;AAEvB;;;;;CAKC;;;;;;;;;;;AACM,MAAM,eAAe,CACxB,MACA;IAEA,uBAAuB;IACvB,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD;IAEA,qCAAqC;IACrC,IAAI,cAAc,SAAS;QACzB,MAAM,aAAa;YAAC;YAAc;YAAa;YAAa;SAAa;QACzE,IAAI,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,GAAG;YACnC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,uCAAuC;QACvC,MAAM,UAAU,OAAO,OAAO,MAAM,eAAe;QACnD,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IACF,OAAO,IAAI,cAAc,SAAS;QAChC,MAAM,aAAa;YAAC;YAAa;YAAmB;YAAmB;SAAa;QACpF,IAAI,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,GAAG;YACnC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,uCAAuC;QACvC,MAAM,UAAU,IAAI,OAAO,OAAO,MAAM,eAAe;QACvD,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAOO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,OAAO,GAAG;YAEvB,aAAa,gBAAgB,GAAG;gBAC9B,IAAI,eAAe,CAAC,aAAa,GAAG;gBACpC,QAAQ,aAAa,QAAQ;YAC/B;YAEA,aAAa,OAAO,GAAG;gBACrB,IAAI,eAAe,CAAC,aAAa,GAAG;gBACpC,OAAO,IAAI,MAAM;YACnB;YAEA,aAAa,GAAG,GAAG,IAAI,eAAe,CAAC;QACzC,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAOO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEA;;;;;GAKC,GACD,4DAA4D;AAC5D,MAAM,qBAID;IACH,SAAS;QAAE,aAAa;QAAM,sBAAsB;QAAG,sBAAsB;IAAG;IAChF,SAAS;QAAE,aAAa;QAAM,sBAAsB;QAAG,sBAAsB;IAAG;IAChF,WAAW;QAAE,aAAa;QAAM,sBAAsB;QAAG,sBAAsB;IAAI;IACnF,SAAS;QAAE,aAAa;QAAM,sBAAsB;QAAG,sBAAsB;IAAK;AACpF;AAEO,MAAM,iBAAiB,CAAC,WAA8B;IAC3D,MAAM,qBAA8D;QAClE,SAAS;YAAE,aAAa;QAAK;QAC7B,QAAQ;YAAE,aAAa;QAAK;IAC9B;IAEA,IAAI,cAAc,SAAS;QACzB,OAAO,kBAAkB,CAAC,SAAS,EAAE,eAAe,MAAM,uCAAuC;IACnG,OAAO;QACL,OAAO,kBAAkB,CAAC,SAAS,EAAE,eAAe,MAAM,uCAAuC;IACnG;AACF;AAOO,MAAM,sBAAsB,CAAC;IAClC,OAAO,kBAAkB,CAAC,SAAS,EAAE;AACvC;AAOO,MAAM,sBAAsB,CAAC;IAClC,OAAO,kBAAkB,CAAC,SAAS,EAAE;AACvC;AAUO,MAAM,wBAAwB,CAAC,UAAkB;IAKtD,MAAM,cAAc,oBAAoB;IACxC,MAAM,cAAc,oBAAoB;IAExC,IAAI,gBAAgB,aAAa,gBAAgB,WAAW;QAC1D,OAAO;YAAE,SAAS;QAAK,GAAG,oCAAoC;IAChE;IAEA,mCAAmC;IACnC,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,uBAAuB;IAEnD,4GAA4G;IAC5G,IAAI,sBAAsB,OAAO,OAAO,CAAC,oBAAoB,IAAI,CAAC,CAAC,CAAC,KAAK,aAAa,GACpF,QAAQ,WAAW,uDAAuD;QAC1E,aAAa,oBAAoB,IAAI,YACrC,aAAa,oBAAoB,IAAI,WACpC,CAAC,EAAE;IAEN,IAAI,WAAW,aAAa;QAC1B,IAAI,eAAe,CAAC,cAAc,EAAE,WAAW,UAAU,iCAAiC,EAAE,oBAAoB,UAAU,CAAC;QAC3H,gBAAgB,CAAC,yBAAyB,EAAE,oBAAoB,IAAI,EAAE,WAAW,aAAa,CAAC,CAAC;QAEhG,IAAI,qBAAqB;YACvB,MAAM,6BAA6B,uBAAuB;YAC1D,gBAAgB,CAAC,uCAAuC,EAAE,2BAA2B,UAAU,CAAC;QAClG;QAEA,OAAO;YACL,SAAS;YACT,OAAO;YACP,mBAAmB;QACrB;IACF;IAEA,2FAA2F;IAC3F,2DAA2D;IAC3D,uDAAuD;IACvD,IAAI,uBAAuB,wBAAwB,YAAY,wBAAwB,SAAS;QAC9F,MAAM,6BAA6B,uBAAuB;QAC1D,MAAM,UAAU,CAAC,cAAc,EAAE,WAAW,UAAU,kCAAkC,EAAE,2BAA2B,oCAAoC,EAAE,oBAAoB,CAAC,CAAC;QAEjL,OAAO;YACL,SAAS;YACT,OAAO;YACP,mBAAmB;QACrB;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAOO,MAAM,aAAa,CAAC;IACzB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;IAElC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC9F;IAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACzD;AAQO,MAAM,mBAAmB,CAAC,MAAY;IAC3C,MAAM,cAAwB,EAAE;IAEhC,wBAAwB;IACxB,YAAY,IAAI,CAAC;IAEjB,yBAAyB;IACzB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;QAClC,YAAY,IAAI,CAAC;QAEjB,0BAA0B;QAC1B,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACtC,IAAI,QAAQ;YACV,YAAY,IAAI,CAAC;QACnB;IACF,OAAO,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;QACzC,YAAY,IAAI,CAAC;QAEjB,0BAA0B;QAC1B,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACtC,IAAI,QAAQ;YACV,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,oCAAoC;IACpC,OAAQ;QACN,KAAK;YACH,YAAY,IAAI,CAAC,WAAW,SAAS;YACrC;QACF,KAAK;YACH,YAAY,IAAI,CAAC,WAAW,UAAU;YACtC;QACF,KAAK;YACH,YAAY,IAAI,CAAC,QAAQ,cAAc;YACvC;QACF,KAAK;YACH,YAAY,IAAI,CAAC,eAAe,eAAe;YAC/C;QACF,KAAK;YACH,YAAY,IAAI,CAAC,SAAS,WAAW;YACrC;IACJ;IAEA,OAAO;WAAI,IAAI,IAAI;KAAa,EAAE,oBAAoB;AACxD", "debugId": null}}, {"offset": {"line": 1890, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1896, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/contexts/UploadContexts.tsx"], "sourcesContent": ["// contexts/UploadContext.tsx\r\n'use client';\r\n\r\nimport React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';\r\nimport { uploadService } from '../services/api';\r\nimport { validateFile, getVideoDuration } from '../utils/uploadUtils';\r\n\r\n// Define types for our context state\r\ninterface UploadState {\r\n  file: File | null;\r\n  thumbnail: File | null;\r\n  mediaType: 'photo' | 'video';\r\n  // This is the media_subtype (e.g., 'glimpse', 'flash', 'movie', 'story')\r\n  category : string;\r\n  mediaSubtype: string;\r\n  title: string;\r\n  description: string;\r\n  tags: string[];\r\n  detailFields: Record<string, string> & {\r\n    // This is the video_category (e.g., 'my_wedding', 'wedding_influencer', 'friends_family_video')\r\n    video_category?: string;\r\n  };\r\n  duration?: number;\r\n  step: 'selecting' | 'details' | 'uploading' | 'processing' | 'complete' | 'error';\r\n  progress: number;\r\n  isUploading: boolean;\r\n  error?: string;\r\n  response?: any;\r\n  uploadResult?: any;\r\n}\r\n\r\n// Define types for personal details\r\ninterface PersonalDetails {\r\n  caption: string;\r\n  lifePartner: string;\r\n  weddingStyle: string;\r\n  place: string;\r\n}\r\n\r\n// Define types for vendor details\r\ninterface VendorDetail {\r\n  name: string;\r\n  mobileNumber: string;\r\n}\r\n\r\n// Define types for context functions\r\ninterface UploadContextType {\r\n  state: UploadState;\r\n  setFile: (file: File | null) => void;\r\n  setThumbnail: (file: File | null) => void;\r\n  setMediaType: (type: 'photo' | 'video') => void;\r\n  setMediaSubtype: (mediaSubtype: string) => void;\r\n  // Deprecated - use setMediaSubtype instead\r\n  setCategory: (category: string) => void;\r\n  setTitle: (title: string) => void;\r\n  setDescription: (description: string) => void;\r\n  addTag: (tag: string) => void;\r\n  removeTag: (tag: string) => void;\r\n  setDetailField: (key: string, value: string) => void;\r\n  setPersonalDetails: (details: PersonalDetails) => void;\r\n  setVendorDetails: (details: Record<string, VendorDetail>) => void;\r\n  setDuration: (duration: number) => void;\r\n  resetUpload: () => void;\r\n  startUpload: () => Promise<void>;\r\n  startUploadWithCategory: (category: string, videoCategory?: string) => Promise<void>;\r\n  validateForm: () => { isValid: boolean; error?: string };\r\n  goToStep: (step: UploadState['step']) => void;\r\n}\r\n\r\n// Initial state\r\nconst initialState: UploadState = {\r\n  file: null,\r\n  thumbnail: null,\r\n  mediaType: 'photo',\r\n  mediaSubtype: 'story',\r\n  category:'',  // Changed from category to mediaSubtype\r\n  title: '',\r\n  description: '',\r\n  tags: [],\r\n  detailFields: {},\r\n  step: 'selecting',\r\n  progress: 0,\r\n  isUploading: false,\r\n};\r\n\r\n// Create context\r\nconst UploadContext = createContext<UploadContextType | undefined>(undefined);\r\n\r\n// Provider component\r\nexport const UploadProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n  const [state, setState] = useState<UploadState>(initialState);\r\n\r\n  // Set file and automatically determine media type\r\n  const setFile = async (file: File | null) => {\r\n    if (!file) {\r\n      setState({ ...state, file: null });\r\n      return;\r\n    }\r\n\r\n    const isVideo = file.type.startsWith('video/');\r\n    const mediaType = isVideo ? 'video' : 'photo';\r\n\r\n    // Default media subtypes based on media type\r\n    const mediaSubtype = isVideo ? 'glimpse' : 'post'; // Default to 'post' for photos\r\n\r\n    setState({\r\n      ...state,\r\n      file,\r\n      mediaType,\r\n      mediaSubtype,\r\n      step: 'details',\r\n    });\r\n  };\r\n\r\n  // Set thumbnail image\r\n  const setThumbnail = (thumbnail: File | null) => {\r\n    setState({\r\n      ...state,\r\n      thumbnail\r\n    });\r\n  };\r\n\r\n  const setMediaType = (type: 'photo' | 'video') => {\r\n    // Don't set a default category - let the user's selection flow through the process\r\n    // Just update the media type\r\n    setState({ ...state, mediaType: type });\r\n  };\r\n\r\n  const setMediaSubtype = (mediaSubtype: string) => {\r\n    setState({ ...state, mediaSubtype });\r\n  };\r\n\r\n  // Keep the old function for backward compatibility\r\n  const setCategory = (category: string) => {\r\n    console.log('UPLOAD CONTEXT - setCategory is deprecated, use setMediaSubtype instead');\r\n    setState({ ...state, mediaSubtype: category });\r\n  };\r\n\r\n  const setTitle = (title: string) => {\r\n    // Ensure title is not empty\r\n    if (!title || !title.trim()) {\r\n      console.warn('Attempted to set empty title');\r\n      return;\r\n    }\r\n\r\n    console.log('Setting title to:', title.trim());\r\n    setState({ ...state, title: title.trim() });\r\n  };\r\n\r\n  const setDescription = (description: string) => {\r\n    setState({ ...state, description });\r\n  };\r\n\r\n  const addTag = (tag: string) => {\r\n    if (tag.trim() && !state.tags.includes(tag.trim())) {\r\n      setState({ ...state, tags: [...state.tags, tag.trim()] });\r\n    }\r\n  };\r\n\r\n  const removeTag = (tag: string) => {\r\n    setState({ ...state, tags: state.tags.filter((t) => t !== tag) });\r\n  };\r\n\r\n  const setDetailField = (field: string, value: string) => {\r\n    // Special handling for video_category to ensure it's properly set\r\n    if (field === 'video_category') {\r\n      console.log(`UPLOAD CONTEXT - Setting video_category to: ${value}`);\r\n\r\n      // Store video_category in localStorage\r\n      try {\r\n        localStorage.setItem('wedzat_video_category', value);\r\n        console.log(`UPLOAD CONTEXT - Stored video_category in localStorage: ${value}`);\r\n      } catch (error) {\r\n        console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\r\n      }\r\n    }\r\n\r\n    // Special handling for vendor fields to ensure they're properly set\r\n    if (field.startsWith('vendor_')) {\r\n      // If this is a vendor field, update the vendor details in localStorage\r\n      try {\r\n        // Get existing vendor details from localStorage\r\n        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\r\n        let vendorDetails = storedVendorDetails ? JSON.parse(storedVendorDetails) : {};\r\n\r\n        // If this is a vendor name field, extract the vendor type and update the name\r\n        if (field.endsWith('_name')) {\r\n          const vendorType = field.replace('vendor_', '').replace('_name', '');\r\n          // Check if we already have this vendor in the details\r\n          if (!vendorDetails[vendorType]) {\r\n            vendorDetails[vendorType] = { name: value, mobileNumber: '' };\r\n          } else {\r\n            vendorDetails[vendorType].name = value;\r\n          }\r\n        }\r\n\r\n        // If this is a vendor contact field, extract the vendor type and update the contact\r\n        if (field.endsWith('_contact')) {\r\n          const vendorType = field.replace('vendor_', '').replace('_contact', '');\r\n          // Check if we already have this vendor in the details\r\n          if (!vendorDetails[vendorType]) {\r\n            vendorDetails[vendorType] = { name: '', mobileNumber: value };\r\n          } else {\r\n            vendorDetails[vendorType].mobileNumber = value;\r\n          }\r\n        }\r\n\r\n        // Store the updated vendor details in localStorage\r\n        localStorage.setItem('wedzat_vendor_details', JSON.stringify(vendorDetails));\r\n      } catch (error) {\r\n        console.error('UPLOAD CONTEXT - Failed to update vendor details in localStorage:', error);\r\n      }\r\n    }\r\n\r\n    // Create a new detailFields object with the updated field\r\n    const updatedDetailFields = {\r\n      ...state.detailFields,\r\n      [field]: value\r\n    };\r\n\r\n    // Update the state with the new detailFields\r\n    setState(prevState => ({\r\n      ...prevState,\r\n      detailFields: updatedDetailFields\r\n    }));\r\n\r\n    // For video_category, log the updated state after a short delay\r\n    if (field === 'video_category') {\r\n      setTimeout(() => {\r\n        console.log(`UPLOAD CONTEXT - Verified video_category is set to: ${state.detailFields.video_category || 'Not set'}`);\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  // Set all personal details at once and update the title and related detail fields\r\n  const setPersonalDetails = (details: PersonalDetails) => {\r\n    // console.log('Setting all personal details:', details);\r\n\r\n    // Validate caption/title\r\n    if (!details.caption || !details.caption.trim()) {\r\n      console.warn('Attempted to set personal details with empty caption/title');\r\n      return;\r\n    }\r\n\r\n    // Update title\r\n    const title = details.caption.trim();\r\n    // console.log('Setting title from personal details:', title);\r\n\r\n    // Update detail fields\r\n    const updatedDetailFields = {\r\n      ...state.detailFields,\r\n      'personal_caption': title,\r\n      'personal_life_partner': details.lifePartner || '',\r\n      'personal_wedding_style': details.weddingStyle || '',\r\n      'personal_place': details.place || '',\r\n      'lifePartner': details.lifePartner || '',\r\n      'location': details.place || ''\r\n    };\r\n\r\n    // Update state with all changes at once\r\n    setState({\r\n      ...state,\r\n      title,\r\n      description: details.weddingStyle || '', // Set description from weddingStyle\r\n      detailFields: updatedDetailFields\r\n    });\r\n\r\n    // Log the description being set\r\n    console.log('Setting description to:', details.weddingStyle || '');\r\n\r\n    // Log the updated state after a short delay to ensure state has updated\r\n    setTimeout(() => {\r\n      console.log('Personal details set successfully');\r\n      console.log('Title after update:', title);\r\n    }, 0);\r\n  };\r\n\r\n  // Set all vendor details at once and update the related detail fields\r\n  const setVendorDetails = (vendorDetails: Record<string, VendorDetail>) => {\r\n    console.log('UPLOAD CONTEXT - Setting all vendor details:', JSON.stringify(vendorDetails, null, 2));\r\n\r\n    // Create a copy of the current detail fields\r\n    const updatedDetailFields = { ...state.detailFields };\r\n\r\n    // Save the video_category if it exists\r\n    const videoCategory = state.detailFields.video_category;\r\n    console.log('UPLOAD CONTEXT - Preserving video_category:', videoCategory);\r\n\r\n    // Count how many complete vendor details we're receiving\r\n    const completeVendorCount = Object.entries(vendorDetails).filter(([_, detail]) =>\r\n      detail && detail.name && detail.mobileNumber &&\r\n      detail.name.trim() !== '' && detail.mobileNumber.trim() !== ''\r\n    ).length;\r\n\r\n    console.log(`UPLOAD CONTEXT - Received ${completeVendorCount} complete vendor details`);\r\n\r\n    // Process vendor details\r\n    Object.entries(vendorDetails).forEach(([vendorType, details]) => {\r\n      if (details) { // Check if details exist\r\n        // Only include vendors that have BOTH name AND mobile number\r\n        if (details.name && details.mobileNumber &&\r\n            details.name.trim() !== '' && details.mobileNumber.trim() !== '') {\r\n          // Handle special mappings for makeup_artist and decoration\r\n          let backendVendorType = vendorType;\r\n\r\n          // Map frontend field names to backend field names\r\n          if (vendorType === 'makeupArtist') {\r\n            backendVendorType = 'makeup_artist';\r\n          } else if (vendorType === 'decorations') {\r\n            backendVendorType = 'decoration';\r\n          }\r\n\r\n          // Store vendor details in the format expected by the backend\r\n          updatedDetailFields[`vendor_${backendVendorType}_name`] = details.name || '';\r\n          updatedDetailFields[`vendor_${backendVendorType}_contact`] = details.mobileNumber || '';\r\n\r\n          // Always store with the original vendorType to ensure we count it correctly\r\n          // This ensures both frontend and backend field names are present\r\n          // This is especially important for Edge browser compatibility\r\n          if (vendorType !== backendVendorType) {\r\n            updatedDetailFields[`vendor_${vendorType}_name`] = details.name || '';\r\n            updatedDetailFields[`vendor_${vendorType}_contact`] = details.mobileNumber || '';\r\n          }\r\n\r\n          // Also store with common vendor types to ensure cross-browser compatibility\r\n          if (vendorType === 'makeupArtist' || vendorType === 'makeup_artist') {\r\n            // Ensure both makeupArtist and makeup_artist are present\r\n            updatedDetailFields[`vendor_makeupArtist_name`] = details.name || '';\r\n            updatedDetailFields[`vendor_makeupArtist_contact`] = details.mobileNumber || '';\r\n            updatedDetailFields[`vendor_makeup_artist_name`] = details.name || '';\r\n            updatedDetailFields[`vendor_makeup_artist_contact`] = details.mobileNumber || '';\r\n          } else if (vendorType === 'decorations' || vendorType === 'decoration') {\r\n            // Ensure both decorations and decoration are present\r\n            updatedDetailFields[`vendor_decorations_name`] = details.name || '';\r\n            updatedDetailFields[`vendor_decorations_contact`] = details.mobileNumber || '';\r\n            updatedDetailFields[`vendor_decoration_name`] = details.name || '';\r\n            updatedDetailFields[`vendor_decoration_contact`] = details.mobileNumber || '';\r\n          }\r\n\r\n          // console.log(`UPLOAD CONTEXT - Setting vendor detail: ${vendorType} (backend: ${backendVendorType})`, {\r\n          //   name: details.name || '',\r\n          //   contact: details.mobileNumber || ''\r\n          // });\r\n        } else {\r\n          console.log(`UPLOAD CONTEXT - Skipping incomplete vendor detail: ${vendorType}`);\r\n        }\r\n      }\r\n    });\r\n\r\n    // Don't update state here - we'll do it after restoring the video_category\r\n\r\n    // console.log('UPLOAD CONTEXT - Vendor details set successfully');\r\n    // console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(updatedDetailFields).length);\r\n\r\n    // Count how many complete vendor details we have after processing\r\n    let completeVendorPairs = 0;\r\n    const vendorNames = new Set();\r\n    const vendorContacts = new Set();\r\n\r\n    // Log all vendor details for debugging\r\n    Object.keys(updatedDetailFields).forEach(key => {\r\n      if (key.startsWith('vendor_')) {\r\n        // console.log(`UPLOAD CONTEXT - Vendor field: ${key} = ${updatedDetailFields[key]}`);\r\n\r\n        if (key.endsWith('_name')) {\r\n          vendorNames.add(key.replace('vendor_', '').replace('_name', ''));\r\n        } else if (key.endsWith('_contact')) {\r\n          vendorContacts.add(key.replace('vendor_', '').replace('_contact', ''));\r\n        }\r\n      }\r\n    });\r\n\r\n    // Count complete pairs (both name and contact)\r\n    vendorNames.forEach(name => {\r\n      if (vendorContacts.has(name)) {\r\n        completeVendorPairs++;\r\n      }\r\n    });\r\n\r\n    // console.log(`UPLOAD CONTEXT - Found ${completeVendorPairs} complete vendor pairs (name + contact)`);\r\n    // console.log(`UPLOAD CONTEXT - Vendor names: ${Array.from(vendorNames).join(', ')}`);\r\n    // console.log(`UPLOAD CONTEXT - Vendor contacts: ${Array.from(vendorContacts).join(', ')}`);\r\n\r\n    // Restore the video_category if it exists\r\n    if (videoCategory) {\r\n      console.log('UPLOAD CONTEXT - Restoring video_category:', videoCategory);\r\n      updatedDetailFields.video_category = videoCategory;\r\n    }\r\n\r\n    // Log the detail fields before updating state\r\n    console.log('UPLOAD CONTEXT - Detail fields before update:', JSON.stringify(state.detailFields, null, 2));\r\n    console.log('UPLOAD CONTEXT - Updated detail fields to be set:', JSON.stringify(updatedDetailFields, null, 2));\r\n\r\n    // Create a completely new state object to ensure Edge updates correctly\r\n    const newState = {\r\n      ...state,\r\n      detailFields: { ...updatedDetailFields }\r\n    };\r\n\r\n    // For Edge browser compatibility, directly set the vendor fields in the state\r\n    // This is a workaround for Edge where the state update doesn't properly preserve vendor details\r\n    if (typeof window !== 'undefined' && /Edge|Edg/.test(window.navigator.userAgent)) {\r\n      console.log('UPLOAD CONTEXT - Edge browser detected, using direct state update');\r\n\r\n      // Create a direct reference to the state object\r\n      const directState = state;\r\n\r\n      // Directly set the detailFields\r\n      directState.detailFields = { ...updatedDetailFields };\r\n\r\n      // Log the direct update\r\n      console.log('UPLOAD CONTEXT - Direct state update:', JSON.stringify(directState.detailFields, null, 2));\r\n    }\r\n\r\n    // Update the state with the updated detail fields\r\n    setState(newState);\r\n\r\n    // Force a re-render to ensure the state is updated\r\n    setTimeout(() => {\r\n      console.log('UPLOAD CONTEXT - Vendor details set successfully');\r\n      console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(state.detailFields).length);\r\n      console.log('UPLOAD CONTEXT - Detail fields after update:', JSON.stringify(state.detailFields, null, 2));\r\n\r\n      // Double-check that the vendor details were set correctly\r\n      const vendorNameFields = Object.keys(state.detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_name'));\r\n      const vendorContactFields = Object.keys(state.detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_contact'));\r\n      console.log('UPLOAD CONTEXT - Vendor name fields count:', vendorNameFields.length);\r\n      console.log('UPLOAD CONTEXT - Vendor contact fields count:', vendorContactFields.length);\r\n    }, 100);\r\n\r\n\r\n  };\r\n\r\n  const resetUpload = () => {\r\n    console.log('UPLOAD CONTEXT - Completely resetting upload state');\r\n\r\n    // Create a fresh copy of the initial state\r\n    const freshState = {\r\n      file: null,\r\n      thumbnail: null,\r\n      mediaType: '',\r\n      mediaSubtype: '',\r\n      title: '',\r\n      description: '',\r\n      tags: [],\r\n      detailFields: {},\r\n      step: 'select',\r\n      duration: 0\r\n    };\r\n\r\n    // Set the state to the fresh state\r\n    setState(freshState);\r\n\r\n    // Log the reset\r\n    console.log('UPLOAD CONTEXT - Upload state reset to:', JSON.stringify(freshState, null, 2));\r\n  };\r\n\r\n  // Helper function to detect Edge browser\r\n  const isEdgeBrowser = () => {\r\n    if (typeof window !== 'undefined') {\r\n      return /Edge|Edg/.test(window.navigator.userAgent);\r\n    }\r\n    return false;\r\n  };\r\n\r\n  const validateForm = (): { isValid: boolean; error?: string } => {\r\n    // Check if we're running in Edge browser\r\n    const isEdge = isEdgeBrowser();\r\n    if (isEdge) {\r\n      console.log('VALIDATE FORM - Running in Edge browser, applying special handling');\r\n    }\r\n    // console.log('VALIDATE FORM - Validating form with state:', {\r\n    //   file: state.file ? state.file.name : 'No file',\r\n    //   mediaType: state.mediaType,\r\n    //   title: state.title,\r\n    //   description: state.description,\r\n    //   detailFieldsCount: Object.keys(state.detailFields).length,\r\n    //   tags: state.tags\r\n    // });\r\n\r\n    // Log all detail fields for debugging\r\n    // console.log('VALIDATE FORM - All detail fields:', JSON.stringify(state.detailFields, null, 2));\r\n\r\n    // Check if file is selected\r\n    if (!state.file) {\r\n      // console.log('Validation failed: No file selected');\r\n      return { isValid: false, error: 'Please select a file to upload' };\r\n    }\r\n\r\n    // Validate file type and size\r\n    const fileValidation = validateFile(state.file, state.mediaType);\r\n    if (!fileValidation.isValid) {\r\n      console.log('Validation failed: File validation failed', fileValidation);\r\n      return fileValidation;\r\n    }\r\n\r\n    // Check if title is provided\r\n    if (!state.title || !state.title.trim()) {\r\n      console.log('Validation failed: Title is empty');\r\n      return { isValid: false, error: 'Please provide a title for your upload' };\r\n    }\r\n\r\n    // First, try to get vendor details from localStorage\r\n    let detailFields = { ...state.detailFields };\r\n    let vendorDetailsFromStorage = null;\r\n\r\n    try {\r\n      const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\r\n      if (storedVendorDetails) {\r\n        vendorDetailsFromStorage = JSON.parse(storedVendorDetails);\r\n        console.log('VALIDATE FORM - Retrieved vendor details from localStorage:', storedVendorDetails);\r\n\r\n        // Process vendor details from localStorage\r\n        if (vendorDetailsFromStorage && Object.keys(vendorDetailsFromStorage).length > 0) {\r\n          Object.entries(vendorDetailsFromStorage).forEach(([vendorType, details]: [string, any]) => {\r\n            if (details && details.name && details.mobileNumber) {\r\n              // Add to detailFields\r\n              detailFields[`vendor_${vendorType}_name`] = details.name;\r\n              detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;\r\n\r\n              console.log(`VALIDATE FORM - Added vendor ${vendorType} with name: ${details.name} and contact: ${details.mobileNumber}`);\r\n\r\n              // Also add normalized versions\r\n              const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n                                    vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n              if (normalizedType !== vendorType) {\r\n                detailFields[`vendor_${normalizedType}_name`] = details.name;\r\n                detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;\r\n                console.log(`VALIDATE FORM - Also added normalized vendor ${normalizedType}`);\r\n              }\r\n            }\r\n          });\r\n          console.log('VALIDATE FORM - Updated detail fields with vendor details from localStorage');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('VALIDATE FORM - Failed to retrieve vendor details from localStorage:', error);\r\n    }\r\n\r\n    // Now use the updated detailFields for validation\r\n    console.log('Detail fields count:', Object.keys(detailFields).length);\r\n    console.log('Detail fields present:', Object.keys(detailFields));\r\n    console.log('Detail fields values:', detailFields);\r\n\r\n    // For videos, check if at least 4 vendor details are present\r\n    if (state.mediaType === 'video') {\r\n      // Special handling for Edge browser\r\n      if (isEdge) {\r\n        console.log('VALIDATE FORM - Edge browser detected, checking vendor details directly');\r\n\r\n        // In Edge, we'll count vendor details directly from the detailFields\r\n        const vendorNameFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_name'));\r\n        const vendorContactFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_contact'));\r\n\r\n        console.log('VALIDATE FORM - Edge browser - Vendor name fields:', vendorNameFields);\r\n        console.log('VALIDATE FORM - Edge browser - Vendor contact fields:', vendorContactFields);\r\n\r\n        // If we have at least 4 vendor name fields and 4 vendor contact fields, assume we have enough vendors\r\n        if (vendorNameFields.length >= 4 && vendorContactFields.length >= 4) {\r\n          console.log('VALIDATE FORM - Edge browser - Found enough vendor fields, validation passed');\r\n          return { isValid: true };\r\n        }\r\n\r\n        // Edge browser workaround - if we're uploading a video, assume vendor details are valid\r\n        // This is a temporary workaround for Edge browser compatibility\r\n        if (state.mediaType === 'video') {\r\n          console.log('VALIDATE FORM - Edge browser workaround: Assuming vendor details are valid for video upload');\r\n          return { isValid: true };\r\n        }\r\n      }\r\n      console.log('VALIDATE FORM - Checking vendor details for video upload');\r\n      console.log('VALIDATE FORM - Detail fields:', JSON.stringify(detailFields, null, 2));\r\n\r\n      // Count how many complete vendor details we have (where BOTH name AND contact are provided)\r\n      let validVendorCount = 0;\r\n\r\n      // Include both frontend and backend field names to ensure we count all vendor details\r\n      const vendorPrefixes = [\r\n        'venue', 'photographer', 'makeup_artist', 'makeupArtist',\r\n        'decoration', 'decorations', 'caterer',\r\n        'additional1', 'additional2', 'additionalVendor1', 'additionalVendor2'\r\n      ];\r\n\r\n      console.log('VALIDATE FORM - Checking these vendor prefixes:', vendorPrefixes.join(', '));\r\n\r\n      // Keep track of which vendors we've already counted to avoid duplicates\r\n      const countedVendors = new Set();\r\n\r\n      // First, log all vendor-related fields for debugging\r\n      const vendorFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_'));\r\n      console.log('VALIDATE FORM - All vendor fields:', vendorFields.join(', '));\r\n\r\n      for (const prefix of vendorPrefixes) {\r\n        // Skip if we've already counted this vendor (to avoid counting both makeupArtist and makeup_artist)\r\n        const normalizedPrefix = prefix === 'makeupArtist' ? 'makeup_artist' :\r\n                                prefix === 'decorations' ? 'decoration' : prefix;\r\n\r\n        if (countedVendors.has(normalizedPrefix)) {\r\n          console.log(`VALIDATE FORM - Skipping ${prefix} as we already counted ${normalizedPrefix}`);\r\n          continue;\r\n        }\r\n\r\n        const nameField = `vendor_${prefix}_name`;\r\n        const contactField = `vendor_${prefix}_contact`;\r\n\r\n        console.log(`VALIDATE FORM - Checking vendor ${prefix}:`, {\r\n          nameField,\r\n          nameValue: detailFields[nameField],\r\n          contactField,\r\n          contactValue: detailFields[contactField],\r\n          hasName: !!detailFields[nameField],\r\n          hasContact: !!detailFields[contactField]\r\n        });\r\n\r\n        if (detailFields[nameField] && detailFields[contactField]) {\r\n          validVendorCount++;\r\n          countedVendors.add(normalizedPrefix);\r\n          console.log(`VALIDATE FORM - Found valid vendor: ${prefix} with name: ${detailFields[nameField]} and contact: ${detailFields[contactField]}`);\r\n        }\r\n      }\r\n\r\n      // Also check for any other vendor_ fields that might have been added\r\n      console.log('VALIDATE FORM - Checking for additional vendor fields');\r\n      Object.keys(detailFields).forEach(key => {\r\n        if (key.startsWith('vendor_') && key.endsWith('_name')) {\r\n          const baseKey = key.replace('vendor_', '').replace('_name', '');\r\n          const contactKey = `vendor_${baseKey}_contact`;\r\n\r\n          // Skip if we've already counted this vendor\r\n          const normalizedPrefix = baseKey === 'makeupArtist' ? 'makeup_artist' :\r\n                                  baseKey === 'decorations' ? 'decoration' : baseKey;\r\n\r\n          console.log(`VALIDATE FORM - Checking additional vendor ${baseKey}:`, {\r\n            normalizedPrefix,\r\n            alreadyCounted: countedVendors.has(normalizedPrefix),\r\n            hasName: !!detailFields[key],\r\n            hasContact: !!detailFields[contactKey]\r\n          });\r\n\r\n          if (!countedVendors.has(normalizedPrefix) &&\r\n              detailFields[key] &&\r\n              detailFields[contactKey]) {\r\n            validVendorCount++;\r\n            countedVendors.add(normalizedPrefix);\r\n            console.log(`VALIDATE FORM - Found additional valid vendor: ${baseKey} with name: ${detailFields[key]} and contact: ${detailFields[contactKey]}`);\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(`VALIDATE FORM - Total valid vendor count: ${validVendorCount}`);\r\n      console.log(`VALIDATE FORM - Counted vendors: ${Array.from(countedVendors).join(', ')}`);\r\n\r\n      // Special handling for Edge browser - if we have vendor fields but they're not being counted correctly\r\n      let edgeVendorNameFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_name'));\r\n      let edgeVendorContactFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_contact'));\r\n\r\n      console.log('VALIDATE FORM - Edge browser check - Vendor name fields:', edgeVendorNameFields);\r\n      console.log('VALIDATE FORM - Edge browser check - Vendor contact fields:', edgeVendorContactFields);\r\n\r\n      // If we have at least 4 vendor name fields and 4 vendor contact fields, but validVendorCount is less than 4,\r\n      // this is likely an Edge browser issue where the fields aren't being properly counted\r\n      if (validVendorCount < 4 && edgeVendorNameFields.length >= 4 && edgeVendorContactFields.length >= 4) {\r\n        console.log('VALIDATE FORM - Edge browser workaround: Found enough vendor fields but they were not counted correctly');\r\n        console.log('VALIDATE FORM - Vendor name fields:', edgeVendorNameFields.join(', '));\r\n        console.log('VALIDATE FORM - Vendor contact fields:', edgeVendorContactFields.join(', '));\r\n\r\n        // Count unique vendor prefixes (excluding the _name/_contact suffix)\r\n        const vendorPrefixSet = new Set();\r\n        edgeVendorNameFields.forEach(field => {\r\n          const prefix = field.replace('vendor_', '').replace('_name', '');\r\n          if (edgeVendorContactFields.includes(`vendor_${prefix}_contact`)) {\r\n            vendorPrefixSet.add(prefix);\r\n          }\r\n        });\r\n\r\n        const uniqueVendorCount = vendorPrefixSet.size;\r\n        console.log(`VALIDATE FORM - Unique vendor count: ${uniqueVendorCount}`);\r\n\r\n        if (uniqueVendorCount >= 4) {\r\n          console.log('VALIDATE FORM - Edge browser workaround: Found at least 4 unique vendors with both name and contact');\r\n          validVendorCount = uniqueVendorCount;\r\n        }\r\n      }\r\n\r\n      // Log the vendor field counts\r\n      console.log('VALIDATE FORM - Vendor name fields count:', edgeVendorNameFields.length);\r\n      console.log('VALIDATE FORM - Vendor contact fields count:', edgeVendorContactFields.length);\r\n\r\n      if (validVendorCount < 4) {\r\n        console.log('VALIDATE FORM - Validation failed: Not enough vendor details', validVendorCount);\r\n        return {\r\n          isValid: false,\r\n          error: `At least 4 complete vendor details (with both name and contact) are required for video uploads. You provided ${validVendorCount}/4.`\r\n        };\r\n      } else {\r\n        console.log('VALIDATE FORM - Vendor validation passed with', validVendorCount, 'vendors');\r\n      }\r\n    }\r\n\r\n    // Just log the detail fields count for now\r\n    console.log('Detail fields count:', Object.keys(state.detailFields).length);\r\n\r\n    // Log the detail fields that are present\r\n    console.log('Detail fields present:', Object.keys(state.detailFields));\r\n    console.log('Detail fields values:', state.detailFields);\r\n\r\n    console.log('Form validation passed');\r\n    return { isValid: true };\r\n  };\r\n\r\n  // Start upload with a specific category and video_category (used when correcting the category)\r\n  const startUploadWithCategory = async (category: string, videoCategory?: string): Promise<void> => {\r\n    console.log(`Starting upload process with corrected category: ${category}`);\r\n    console.log(`Using video_category: ${videoCategory || 'Not provided'}`);\r\n\r\n    // Try to get vendor details from localStorage\r\n    let vendorDetails = {};\r\n    try {\r\n      const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\r\n      if (storedVendorDetails) {\r\n        vendorDetails = JSON.parse(storedVendorDetails);\r\n        console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage');\r\n      }\r\n    } catch (error) {\r\n      console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\r\n    }\r\n\r\n    // Create detail fields from vendor details\r\n    const detailFields: Record<string, string> = { ...state.detailFields };\r\n\r\n    // Process vendor details to create detail fields\r\n    if (Object.keys(vendorDetails).length > 0) {\r\n      console.log('UPLOAD CONTEXT - Processing vendor details from localStorage:', JSON.stringify(vendorDetails));\r\n\r\n      // Track how many complete vendor details we've added\r\n      let completeVendorCount = 0;\r\n\r\n      Object.entries(vendorDetails).forEach(([vendorType, details]: [string, any]) => {\r\n        if (details && details.name && details.mobileNumber) {\r\n          detailFields[`vendor_${vendorType}_name`] = details.name;\r\n          detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;\r\n\r\n          console.log(`UPLOAD CONTEXT - Added vendor ${vendorType} with name: ${details.name} and contact: ${details.mobileNumber}`);\r\n          completeVendorCount++;\r\n\r\n          // Also set normalized versions\r\n          const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n                                vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n          if (normalizedType !== vendorType) {\r\n            detailFields[`vendor_${normalizedType}_name`] = details.name;\r\n            detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;\r\n            console.log(`UPLOAD CONTEXT - Also added normalized vendor ${normalizedType}`);\r\n          }\r\n        }\r\n      });\r\n      console.log(`UPLOAD CONTEXT - Added ${completeVendorCount} complete vendor details from localStorage to detailFields`);\r\n      console.log('UPLOAD CONTEXT - Detail fields after adding vendors:', JSON.stringify(detailFields));\r\n    }\r\n\r\n    // Update the state with the corrected category and video_category if provided\r\n    const updatedState = {\r\n      ...state,\r\n      mediaSubtype: category,\r\n      category: category,\r\n      detailFields: detailFields\r\n    };\r\n\r\n    // If videoCategory is provided, update the detailFields\r\n    if (videoCategory) {\r\n      updatedState.detailFields.video_category = videoCategory;\r\n      console.log(`Setting video_category in state to: ${videoCategory}`);\r\n\r\n      // Also store in localStorage\r\n      try {\r\n        localStorage.setItem('wedzat_video_category', videoCategory);\r\n        console.log('UPLOAD CONTEXT - Stored video_category in localStorage:', videoCategory);\r\n      } catch (error) {\r\n        console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\r\n      }\r\n    }\r\n\r\n    // Apply the state update immediately\r\n    setState(updatedState);\r\n\r\n    // Then start the upload process with the updated category\r\n    const validation = validateForm();\r\n    if (!validation.isValid) {\r\n      console.log('Upload validation failed:', validation.error);\r\n      setState({\r\n        ...state,\r\n        mediaSubtype: category,  // Use category as mediaSubtype\r\n        error: validation.error,\r\n        step: 'error',\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (!state.file) {\r\n      console.log('No file to upload');\r\n      return;\r\n    }\r\n\r\n    setState({\r\n      ...state,\r\n      mediaSubtype: category,  // Use category as mediaSubtype\r\n      isUploading: true,\r\n      progress: 0,\r\n      step: 'uploading',\r\n      error: undefined,\r\n    });\r\n\r\n    try {\r\n      console.log('UPLOAD CONTEXT - Starting upload process with corrected category...');\r\n      console.log('UPLOAD CONTEXT - Upload details:', {\r\n        file: state.file ? state.file.name : 'No file',\r\n        fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\r\n        mediaType: state.mediaType,\r\n        category: category, // Use the corrected category\r\n        title: state.title,\r\n        videoCategory: videoCategory || 'Not set'\r\n      });\r\n\r\n      // Log the video_category that will be used\r\n      console.log('UPLOAD CONTEXT - Using explicit video_category:', videoCategory);\r\n      console.log('UPLOAD CONTEXT - Video category from detail fields:', state.detailFields.video_category);\r\n\r\n      // Create a copy of the detail fields with the explicit video_category\r\n      const updatedDetailFields = { ...state.detailFields };\r\n\r\n      // If videoCategory is provided, use it\r\n      if (videoCategory) {\r\n        updatedDetailFields.video_category = videoCategory;\r\n        console.log('UPLOAD CONTEXT - Setting video_category in details to:', videoCategory);\r\n      }\r\n\r\n      // Use the upload service to handle the complete upload process with the corrected category\r\n      const result = await uploadService.handleUpload(\r\n        state.file,\r\n        state.mediaType,\r\n        category, // Use the corrected category\r\n        state.title,\r\n        state.description,\r\n        state.tags,\r\n        updatedDetailFields, // Use the updated detail fields with the video_category\r\n        state.duration,\r\n        state.thumbnail, // Pass the thumbnail file\r\n        (progress) => {\r\n          setState({\r\n            ...state,\r\n            mediaSubtype: category, // Keep the corrected category\r\n            progress,\r\n          });\r\n        }\r\n      );\r\n\r\n      // Update the state with the upload result\r\n      setState({\r\n        ...state,\r\n        mediaSubtype: category, // Keep the corrected category\r\n        isUploading: false,\r\n        progress: 100,\r\n        step: 'complete',\r\n        uploadResult: result,\r\n      });\r\n\r\n      // Upload completed successfully\r\n    } catch (error) {\r\n      console.error('Upload failed:', error);\r\n      setState({\r\n        ...state,\r\n        mediaSubtype: category, // Keep the corrected category\r\n        isUploading: false,\r\n        error: error instanceof Error ? error.message : 'Unknown error',\r\n      });\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const startUpload = async (): Promise<void> => {\r\n    console.log('Starting upload process...');\r\n\r\n    // Try to get vendor details from localStorage\r\n    try {\r\n      const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\r\n      if (storedVendorDetails) {\r\n        const vendorDetails = JSON.parse(storedVendorDetails);\r\n        console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage for startUpload:', storedVendorDetails);\r\n\r\n        // Create a new detailFields object to hold all the vendor details\r\n        const updatedDetailFields = { ...state.detailFields };\r\n        let completeVendorCount = 0;\r\n\r\n        // Process vendor details to create detail fields\r\n        if (Object.keys(vendorDetails).length > 0) {\r\n          Object.entries(vendorDetails).forEach(([vendorType, details]: [string, any]) => {\r\n            if (details && details.name && details.mobileNumber) {\r\n              // Add to the updated detail fields\r\n              updatedDetailFields[`vendor_${vendorType}_name`] = details.name;\r\n              updatedDetailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;\r\n\r\n              console.log(`UPLOAD CONTEXT - Added vendor ${vendorType} with name: ${details.name} and contact: ${details.mobileNumber}`);\r\n              completeVendorCount++;\r\n\r\n              // Also set normalized versions\r\n              const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n                                    vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n              if (normalizedType !== vendorType) {\r\n                updatedDetailFields[`vendor_${normalizedType}_name`] = details.name;\r\n                updatedDetailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;\r\n                console.log(`UPLOAD CONTEXT - Also added normalized vendor ${normalizedType}`);\r\n              }\r\n            }\r\n          });\r\n\r\n          // Update the state with all vendor details at once\r\n          setState(prevState => ({\r\n            ...prevState,\r\n            detailFields: updatedDetailFields\r\n          }));\r\n\r\n          console.log(`UPLOAD CONTEXT - Added ${completeVendorCount} complete vendor details from localStorage to state`);\r\n          console.log('UPLOAD CONTEXT - Updated detail fields:', JSON.stringify(updatedDetailFields));\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\r\n    }\r\n\r\n    // Check if we have a video_category for videos\r\n    if (state.mediaType === 'video') {\r\n      // Try to get video_category from localStorage if not in state\r\n      let videoCategory = state.detailFields.video_category;\r\n      if (!videoCategory) {\r\n        try {\r\n          const storedVideoCategory = localStorage.getItem('wedzat_video_category');\r\n          if (storedVideoCategory) {\r\n            videoCategory = storedVideoCategory;\r\n            console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', videoCategory);\r\n            setDetailField('video_category', videoCategory);\r\n          }\r\n        } catch (error) {\r\n          console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\r\n        }\r\n      }\r\n\r\n      console.log(`UPLOAD CONTEXT - Current video_category: ${videoCategory || 'Not set'}`);\r\n\r\n      // If we don't have a video_category, use a default one\r\n      if (!videoCategory) {\r\n        console.log('UPLOAD CONTEXT - No video_category found, using default: my_wedding');\r\n        // Use startUploadWithCategory to ensure the video_category is properly set\r\n        return startUploadWithCategory(state.mediaSubtype, 'my_wedding');\r\n      } else {\r\n        // Use startUploadWithCategory to ensure the video_category is properly passed\r\n        console.log(`UPLOAD CONTEXT - Using existing video_category: ${videoCategory}`);\r\n        return startUploadWithCategory(state.mediaSubtype, videoCategory);\r\n      }\r\n    }\r\n\r\n    // For photos, just use the regular upload flow\r\n    const validation = validateForm();\r\n    if (!validation.isValid) {\r\n      console.log('Upload validation failed:', validation.error);\r\n      setState({\r\n        ...state,\r\n        error: validation.error,\r\n        step: 'error',\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (!state.file) {\r\n      console.log('No file to upload');\r\n      return;\r\n    }\r\n\r\n    setState({\r\n      ...state,\r\n      isUploading: true,\r\n      progress: 0,\r\n      step: 'uploading',\r\n      error: undefined,\r\n    });\r\n\r\n    try {\r\n      console.log('UPLOAD CONTEXT - Starting upload process...');\r\n      console.log('UPLOAD CONTEXT - Upload details:', {\r\n        file: state.file ? state.file.name : 'No file',\r\n        fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\r\n        mediaType: state.mediaType,\r\n        mediaSubtype: state.mediaSubtype,\r\n        title: state.title,\r\n        videoCategory: state.detailFields.video_category || 'Not set'\r\n      });\r\n\r\n      // Use the upload service to handle the complete upload process\r\n      const result = await uploadService.handleUpload(\r\n        state.file,\r\n        state.mediaType,\r\n        state.mediaSubtype,\r\n        state.title,\r\n        state.description,\r\n        state.tags,\r\n        state.detailFields,\r\n        state.duration,\r\n        state.thumbnail, // Pass the thumbnail file\r\n        (progress) => {\r\n          console.log(`Upload progress: ${progress}%`);\r\n\r\n          setState((prevState) => {\r\n            // Only update if the new progress is greater\r\n            if (progress > prevState.progress) {\r\n              return {\r\n                ...prevState,\r\n                progress,\r\n                // Change to processing state when we reach 80%\r\n                step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\r\n              };\r\n            }\r\n            return prevState;\r\n          });\r\n        }\r\n      );\r\n\r\n      console.log('Upload completed successfully:', result);\r\n\r\n      // Upload complete\r\n      setState({\r\n        ...state,\r\n        isUploading: false,\r\n        progress: 100,\r\n        step: 'complete',\r\n        response: result,\r\n      });\r\n    } catch (error) {\r\n      console.error('Upload failed:', error);\r\n      setState({\r\n        ...state,\r\n        isUploading: false,\r\n        progress: 0,\r\n        step: 'error',\r\n        error: error instanceof Error ? error.message : 'Upload failed. Please try again.',\r\n      });\r\n    }\r\n  };\r\n\r\n  const goToStep = (step: UploadState['step']) => {\r\n    setState({ ...state, step });\r\n  };\r\n\r\n  // Set video duration\r\n  const setDuration = (duration: number) => {\r\n    setState({\r\n      ...state,\r\n      duration,\r\n    });\r\n    console.log(`Duration set to ${duration} seconds`);\r\n  };\r\n\r\n  // Effect to initialize the upload context and listen for vendor details updates\r\n  useEffect(() => {\r\n    // Check if we have a video_category in localStorage\r\n    try {\r\n      const storedVideoCategory = localStorage.getItem('wedzat_video_category');\r\n      if (storedVideoCategory) {\r\n        console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', storedVideoCategory);\r\n        setDetailField('video_category', storedVideoCategory);\r\n      }\r\n    } catch (error) {\r\n      console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\r\n    }\r\n\r\n    // Add event listener for vendor details updates from API service\r\n    const handleVendorDetailsUpdate = (event: any) => {\r\n      if (event.detail) {\r\n        console.log('UPLOAD CONTEXT - Received vendor-details-update event:', event.detail);\r\n\r\n        // Process vendor details from event\r\n        const vendorDetails = event.detail;\r\n        const updatedDetailFields: Record<string, string> = { ...state.detailFields };\r\n        let completeVendorCount = 0;\r\n\r\n        Object.entries(vendorDetails).forEach(([vendorType, details]: [string, any]) => {\r\n          if (details && details.name && details.mobileNumber) {\r\n            // Add to detailFields\r\n            updatedDetailFields[`vendor_${vendorType}_name`] = details.name;\r\n            updatedDetailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;\r\n\r\n            console.log(`UPLOAD CONTEXT - Event handler added vendor ${vendorType} with name: ${details.name} and contact: ${details.mobileNumber}`);\r\n            completeVendorCount++;\r\n\r\n            // Also add normalized versions\r\n            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n                                  vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n            if (normalizedType !== vendorType) {\r\n              updatedDetailFields[`vendor_${normalizedType}_name`] = details.name;\r\n              updatedDetailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;\r\n              console.log(`UPLOAD CONTEXT - Event handler also added normalized vendor ${normalizedType}`);\r\n            }\r\n          }\r\n        });\r\n\r\n        // Update the state with all vendor details at once\r\n        setState(prevState => ({\r\n          ...prevState,\r\n          detailFields: updatedDetailFields\r\n        }));\r\n\r\n        console.log(`UPLOAD CONTEXT - Event handler added ${completeVendorCount} complete vendor details to state`);\r\n        console.log('UPLOAD CONTEXT - Event handler updated detail fields:', JSON.stringify(updatedDetailFields));\r\n      }\r\n    };\r\n\r\n    // Add event listener\r\n    window.addEventListener('vendor-details-update', handleVendorDetailsUpdate);\r\n\r\n    // Remove event listener on cleanup\r\n    return () => {\r\n      window.removeEventListener('vendor-details-update', handleVendorDetailsUpdate);\r\n    };\r\n  }, []);\r\n\r\n  // Create the context value\r\n  const contextValue: UploadContextType = {\r\n    state,\r\n    setFile,\r\n    setThumbnail,\r\n    setMediaType,\r\n    setMediaSubtype,\r\n    setCategory,\r\n    setTitle,\r\n    setDescription,\r\n    addTag,\r\n    removeTag,\r\n    setDetailField,\r\n    setPersonalDetails,\r\n    setVendorDetails,\r\n    setDuration,\r\n    resetUpload,\r\n    startUpload,\r\n    startUploadWithCategory,\r\n    validateForm,\r\n    goToStep,\r\n  };\r\n\r\n  return (\r\n    <UploadContext.Provider value={contextValue}>\r\n      {children}\r\n    </UploadContext.Provider>\r\n  );\r\n};\r\n\r\n// Custom hook to use the context\r\nexport const useUpload = (): UploadContextType => {\r\n  const context = useContext(UploadContext);\r\n  if (context === undefined) {\r\n    throw new Error('useUpload must be used within an UploadProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\n\r\n\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;AAG7B;AACA;AACA;;;AAJA;;;;AAoEA,gBAAgB;AAChB,MAAM,eAA4B;IAChC,MAAM;IACN,WAAW;IACX,WAAW;IACX,cAAc;IACd,UAAS;IACT,OAAO;IACP,aAAa;IACb,MAAM,EAAE;IACR,cAAc,CAAC;IACf,MAAM;IACN,UAAU;IACV,aAAa;AACf;AAEA,iBAAiB;AACjB,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAG5D,MAAM,iBAAoD,CAAC,EAAE,QAAQ,EAAE;;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEhD,kDAAkD;IAClD,MAAM,UAAU,OAAO;QACrB,IAAI,CAAC,MAAM;YACT,SAAS;gBAAE,GAAG,KAAK;gBAAE,MAAM;YAAK;YAChC;QACF;QAEA,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC;QACrC,MAAM,YAAY,UAAU,UAAU;QAEtC,6CAA6C;QAC7C,MAAM,eAAe,UAAU,YAAY,QAAQ,+BAA+B;QAElF,SAAS;YACP,GAAG,KAAK;YACR;YACA;YACA;YACA,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,eAAe,CAAC;QACpB,SAAS;YACP,GAAG,KAAK;YACR;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,mFAAmF;QACnF,6BAA6B;QAC7B,SAAS;YAAE,GAAG,KAAK;YAAE,WAAW;QAAK;IACvC;IAEA,MAAM,kBAAkB,CAAC;QACvB,SAAS;YAAE,GAAG,KAAK;YAAE;QAAa;IACpC;IAEA,mDAAmD;IACnD,MAAM,cAAc,CAAC;QACnB,QAAQ,GAAG,CAAC;QACZ,SAAS;YAAE,GAAG,KAAK;YAAE,cAAc;QAAS;IAC9C;IAEA,MAAM,WAAW,CAAC;QAChB,4BAA4B;QAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,QAAQ,GAAG,CAAC,qBAAqB,MAAM,IAAI;QAC3C,SAAS;YAAE,GAAG,KAAK;YAAE,OAAO,MAAM,IAAI;QAAG;IAC3C;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,GAAG,KAAK;YAAE;QAAY;IACnC;IAEA,MAAM,SAAS,CAAC;QACd,IAAI,IAAI,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK;YAClD,SAAS;gBAAE,GAAG,KAAK;gBAAE,MAAM;uBAAI,MAAM,IAAI;oBAAE,IAAI,IAAI;iBAAG;YAAC;QACzD;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,SAAS;YAAE,GAAG,KAAK;YAAE,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,IAAM,MAAM;QAAK;IACjE;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,kEAAkE;QAClE,IAAI,UAAU,kBAAkB;YAC9B,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,OAAO;YAElE,uCAAuC;YACvC,IAAI;gBACF,aAAa,OAAO,CAAC,yBAAyB;gBAC9C,QAAQ,GAAG,CAAC,CAAC,wDAAwD,EAAE,OAAO;YAChF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oEAAoE;YACpF;QACF;QAEA,oEAAoE;QACpE,IAAI,MAAM,UAAU,CAAC,YAAY;YAC/B,uEAAuE;YACvE,IAAI;gBACF,gDAAgD;gBAChD,MAAM,sBAAsB,aAAa,OAAO,CAAC;gBACjD,IAAI,gBAAgB,sBAAsB,KAAK,KAAK,CAAC,uBAAuB,CAAC;gBAE7E,8EAA8E;gBAC9E,IAAI,MAAM,QAAQ,CAAC,UAAU;oBAC3B,MAAM,aAAa,MAAM,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,SAAS;oBACjE,sDAAsD;oBACtD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;wBAC9B,aAAa,CAAC,WAAW,GAAG;4BAAE,MAAM;4BAAO,cAAc;wBAAG;oBAC9D,OAAO;wBACL,aAAa,CAAC,WAAW,CAAC,IAAI,GAAG;oBACnC;gBACF;gBAEA,oFAAoF;gBACpF,IAAI,MAAM,QAAQ,CAAC,aAAa;oBAC9B,MAAM,aAAa,MAAM,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,YAAY;oBACpE,sDAAsD;oBACtD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;wBAC9B,aAAa,CAAC,WAAW,GAAG;4BAAE,MAAM;4BAAI,cAAc;wBAAM;oBAC9D,OAAO;wBACL,aAAa,CAAC,WAAW,CAAC,YAAY,GAAG;oBAC3C;gBACF;gBAEA,mDAAmD;gBACnD,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;YAC/D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qEAAqE;YACrF;QACF;QAEA,0DAA0D;QAC1D,MAAM,sBAAsB;YAC1B,GAAG,MAAM,YAAY;YACrB,CAAC,MAAM,EAAE;QACX;QAEA,6CAA6C;QAC7C,SAAS,CAAA,YAAa,CAAC;gBACrB,GAAG,SAAS;gBACZ,cAAc;YAChB,CAAC;QAED,gEAAgE;QAChE,IAAI,UAAU,kBAAkB;YAC9B,WAAW;gBACT,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,MAAM,YAAY,CAAC,cAAc,IAAI,WAAW;YACrH,GAAG;QACL;IACF;IAEA,kFAAkF;IAClF,MAAM,qBAAqB,CAAC;QAC1B,yDAAyD;QAEzD,yBAAyB;QACzB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,IAAI,IAAI;YAC/C,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,eAAe;QACf,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI;QAClC,8DAA8D;QAE9D,uBAAuB;QACvB,MAAM,sBAAsB;YAC1B,GAAG,MAAM,YAAY;YACrB,oBAAoB;YACpB,yBAAyB,QAAQ,WAAW,IAAI;YAChD,0BAA0B,QAAQ,YAAY,IAAI;YAClD,kBAAkB,QAAQ,KAAK,IAAI;YACnC,eAAe,QAAQ,WAAW,IAAI;YACtC,YAAY,QAAQ,KAAK,IAAI;QAC/B;QAEA,wCAAwC;QACxC,SAAS;YACP,GAAG,KAAK;YACR;YACA,aAAa,QAAQ,YAAY,IAAI;YACrC,cAAc;QAChB;QAEA,gCAAgC;QAChC,QAAQ,GAAG,CAAC,2BAA2B,QAAQ,YAAY,IAAI;QAE/D,wEAAwE;QACxE,WAAW;YACT,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uBAAuB;QACrC,GAAG;IACL;IAEA,sEAAsE;IACtE,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,gDAAgD,KAAK,SAAS,CAAC,eAAe,MAAM;QAEhG,6CAA6C;QAC7C,MAAM,sBAAsB;YAAE,GAAG,MAAM,YAAY;QAAC;QAEpD,uCAAuC;QACvC,MAAM,gBAAgB,MAAM,YAAY,CAAC,cAAc;QACvD,QAAQ,GAAG,CAAC,+CAA+C;QAE3D,yDAAyD;QACzD,MAAM,sBAAsB,OAAO,OAAO,CAAC,eAAe,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,GAC3E,UAAU,OAAO,IAAI,IAAI,OAAO,YAAY,IAC5C,OAAO,IAAI,CAAC,IAAI,OAAO,MAAM,OAAO,YAAY,CAAC,IAAI,OAAO,IAC5D,MAAM;QAER,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,oBAAoB,wBAAwB,CAAC;QAEtF,yBAAyB;QACzB,OAAO,OAAO,CAAC,eAAe,OAAO,CAAC,CAAC,CAAC,YAAY,QAAQ;YAC1D,IAAI,SAAS;gBACX,6DAA6D;gBAC7D,IAAI,QAAQ,IAAI,IAAI,QAAQ,YAAY,IACpC,QAAQ,IAAI,CAAC,IAAI,OAAO,MAAM,QAAQ,YAAY,CAAC,IAAI,OAAO,IAAI;oBACpE,2DAA2D;oBAC3D,IAAI,oBAAoB;oBAExB,kDAAkD;oBAClD,IAAI,eAAe,gBAAgB;wBACjC,oBAAoB;oBACtB,OAAO,IAAI,eAAe,eAAe;wBACvC,oBAAoB;oBACtB;oBAEA,6DAA6D;oBAC7D,mBAAmB,CAAC,CAAC,OAAO,EAAE,kBAAkB,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI;oBAC1E,mBAAmB,CAAC,CAAC,OAAO,EAAE,kBAAkB,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY,IAAI;oBAErF,4EAA4E;oBAC5E,iEAAiE;oBACjE,8DAA8D;oBAC9D,IAAI,eAAe,mBAAmB;wBACpC,mBAAmB,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI;wBACnE,mBAAmB,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY,IAAI;oBAChF;oBAEA,4EAA4E;oBAC5E,IAAI,eAAe,kBAAkB,eAAe,iBAAiB;wBACnE,yDAAyD;wBACzD,mBAAmB,CAAC,CAAC,wBAAwB,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI;wBAClE,mBAAmB,CAAC,CAAC,2BAA2B,CAAC,CAAC,GAAG,QAAQ,YAAY,IAAI;wBAC7E,mBAAmB,CAAC,CAAC,yBAAyB,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI;wBACnE,mBAAmB,CAAC,CAAC,4BAA4B,CAAC,CAAC,GAAG,QAAQ,YAAY,IAAI;oBAChF,OAAO,IAAI,eAAe,iBAAiB,eAAe,cAAc;wBACtE,qDAAqD;wBACrD,mBAAmB,CAAC,CAAC,uBAAuB,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI;wBACjE,mBAAmB,CAAC,CAAC,0BAA0B,CAAC,CAAC,GAAG,QAAQ,YAAY,IAAI;wBAC5E,mBAAmB,CAAC,CAAC,sBAAsB,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI;wBAChE,mBAAmB,CAAC,CAAC,yBAAyB,CAAC,CAAC,GAAG,QAAQ,YAAY,IAAI;oBAC7E;gBAEA,yGAAyG;gBACzG,8BAA8B;gBAC9B,wCAAwC;gBACxC,MAAM;gBACR,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,YAAY;gBACjF;YACF;QACF;QAEA,2EAA2E;QAE3E,mEAAmE;QACnE,uGAAuG;QAEvG,kEAAkE;QAClE,IAAI,sBAAsB;QAC1B,MAAM,cAAc,IAAI;QACxB,MAAM,iBAAiB,IAAI;QAE3B,uCAAuC;QACvC,OAAO,IAAI,CAAC,qBAAqB,OAAO,CAAC,CAAA;YACvC,IAAI,IAAI,UAAU,CAAC,YAAY;gBAC7B,sFAAsF;gBAEtF,IAAI,IAAI,QAAQ,CAAC,UAAU;oBACzB,YAAY,GAAG,CAAC,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,SAAS;gBAC9D,OAAO,IAAI,IAAI,QAAQ,CAAC,aAAa;oBACnC,eAAe,GAAG,CAAC,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,YAAY;gBACpE;YACF;QACF;QAEA,+CAA+C;QAC/C,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,eAAe,GAAG,CAAC,OAAO;gBAC5B;YACF;QACF;QAEA,uGAAuG;QACvG,uFAAuF;QACvF,6FAA6F;QAE7F,0CAA0C;QAC1C,IAAI,eAAe;YACjB,QAAQ,GAAG,CAAC,8CAA8C;YAC1D,oBAAoB,cAAc,GAAG;QACvC;QAEA,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,iDAAiD,KAAK,SAAS,CAAC,MAAM,YAAY,EAAE,MAAM;QACtG,QAAQ,GAAG,CAAC,qDAAqD,KAAK,SAAS,CAAC,qBAAqB,MAAM;QAE3G,wEAAwE;QACxE,MAAM,WAAW;YACf,GAAG,KAAK;YACR,cAAc;gBAAE,GAAG,mBAAmB;YAAC;QACzC;QAEA,8EAA8E;QAC9E,gGAAgG;QAChG,IAAI,aAAkB,eAAe,WAAW,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS,GAAG;YAChF,QAAQ,GAAG,CAAC;YAEZ,gDAAgD;YAChD,MAAM,cAAc;YAEpB,gCAAgC;YAChC,YAAY,YAAY,GAAG;gBAAE,GAAG,mBAAmB;YAAC;YAEpD,wBAAwB;YACxB,QAAQ,GAAG,CAAC,yCAAyC,KAAK,SAAS,CAAC,YAAY,YAAY,EAAE,MAAM;QACtG;QAEA,kDAAkD;QAClD,SAAS;QAET,mDAAmD;QACnD,WAAW;YACT,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,+CAA+C,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,MAAM;YACjG,QAAQ,GAAG,CAAC,gDAAgD,KAAK,SAAS,CAAC,MAAM,YAAY,EAAE,MAAM;YAErG,0DAA0D;YAC1D,MAAM,mBAAmB,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC;YACjH,MAAM,sBAAsB,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC;YACpH,QAAQ,GAAG,CAAC,8CAA8C,iBAAiB,MAAM;YACjF,QAAQ,GAAG,CAAC,iDAAiD,oBAAoB,MAAM;QACzF,GAAG;IAGL;IAEA,MAAM,cAAc;QAClB,QAAQ,GAAG,CAAC;QAEZ,2CAA2C;QAC3C,MAAM,aAAa;YACjB,MAAM;YACN,WAAW;YACX,WAAW;YACX,cAAc;YACd,OAAO;YACP,aAAa;YACb,MAAM,EAAE;YACR,cAAc,CAAC;YACf,MAAM;YACN,UAAU;QACZ;QAEA,mCAAmC;QACnC,SAAS;QAET,gBAAgB;QAChB,QAAQ,GAAG,CAAC,2CAA2C,KAAK,SAAS,CAAC,YAAY,MAAM;IAC1F;IAEA,yCAAyC;IACzC,MAAM,gBAAgB;QACpB,wCAAmC;YACjC,OAAO,WAAW,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;QACnD;;IAEF;IAEA,MAAM,eAAe;QACnB,yCAAyC;QACzC,MAAM,SAAS;QACf,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC;QACd;QACA,+DAA+D;QAC/D,oDAAoD;QACpD,gCAAgC;QAChC,wBAAwB;QACxB,oCAAoC;QACpC,+DAA+D;QAC/D,qBAAqB;QACrB,MAAM;QAEN,sCAAsC;QACtC,kGAAkG;QAElG,4BAA4B;QAC5B,IAAI,CAAC,MAAM,IAAI,EAAE;YACf,sDAAsD;YACtD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAiC;QACnE;QAEA,8BAA8B;QAC9B,MAAM,iBAAiB,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,IAAI,EAAE,MAAM,SAAS;QAC/D,IAAI,CAAC,eAAe,OAAO,EAAE;YAC3B,QAAQ,GAAG,CAAC,6CAA6C;YACzD,OAAO;QACT;QAEA,6BAA6B;QAC7B,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI;YACvC,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyC;QAC3E;QAEA,qDAAqD;QACrD,IAAI,eAAe;YAAE,GAAG,MAAM,YAAY;QAAC;QAC3C,IAAI,2BAA2B;QAE/B,IAAI;YACF,MAAM,sBAAsB,aAAa,OAAO,CAAC;YACjD,IAAI,qBAAqB;gBACvB,2BAA2B,KAAK,KAAK,CAAC;gBACtC,QAAQ,GAAG,CAAC,+DAA+D;gBAE3E,2CAA2C;gBAC3C,IAAI,4BAA4B,OAAO,IAAI,CAAC,0BAA0B,MAAM,GAAG,GAAG;oBAChF,OAAO,OAAO,CAAC,0BAA0B,OAAO,CAAC,CAAC,CAAC,YAAY,QAAuB;wBACpF,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;4BACnD,sBAAsB;4BACtB,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;4BACxD,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;4BAEnE,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,WAAW,YAAY,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,QAAQ,YAAY,EAAE;4BAExH,+BAA+B;4BAC/B,MAAM,iBAAiB,eAAe,iBAAiB,kBACjC,eAAe,gBAAgB,eAAe;4BAEpE,IAAI,mBAAmB,YAAY;gCACjC,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;gCAC5D,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;gCACvE,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,gBAAgB;4BAC9E;wBACF;oBACF;oBACA,QAAQ,GAAG,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wEAAwE;QACxF;QAEA,kDAAkD;QAClD,QAAQ,GAAG,CAAC,wBAAwB,OAAO,IAAI,CAAC,cAAc,MAAM;QACpE,QAAQ,GAAG,CAAC,0BAA0B,OAAO,IAAI,CAAC;QAClD,QAAQ,GAAG,CAAC,yBAAyB;QAErC,6DAA6D;QAC7D,IAAI,MAAM,SAAS,KAAK,SAAS;YAC/B,oCAAoC;YACpC,IAAI,QAAQ;gBACV,QAAQ,GAAG,CAAC;gBAEZ,qEAAqE;gBACrE,MAAM,mBAAmB,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC;gBAC3G,MAAM,sBAAsB,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC;gBAE9G,QAAQ,GAAG,CAAC,sDAAsD;gBAClE,QAAQ,GAAG,CAAC,yDAAyD;gBAErE,sGAAsG;gBACtG,IAAI,iBAAiB,MAAM,IAAI,KAAK,oBAAoB,MAAM,IAAI,GAAG;oBACnE,QAAQ,GAAG,CAAC;oBACZ,OAAO;wBAAE,SAAS;oBAAK;gBACzB;gBAEA,wFAAwF;gBACxF,gEAAgE;gBAChE,IAAI,MAAM,SAAS,KAAK,SAAS;oBAC/B,QAAQ,GAAG,CAAC;oBACZ,OAAO;wBAAE,SAAS;oBAAK;gBACzB;YACF;YACA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,kCAAkC,KAAK,SAAS,CAAC,cAAc,MAAM;YAEjF,4FAA4F;YAC5F,IAAI,mBAAmB;YAEvB,sFAAsF;YACtF,MAAM,iBAAiB;gBACrB;gBAAS;gBAAgB;gBAAiB;gBAC1C;gBAAc;gBAAe;gBAC7B;gBAAe;gBAAe;gBAAqB;aACpD;YAED,QAAQ,GAAG,CAAC,mDAAmD,eAAe,IAAI,CAAC;YAEnF,wEAAwE;YACxE,MAAM,iBAAiB,IAAI;YAE3B,qDAAqD;YACrD,MAAM,eAAe,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC;YAC5E,QAAQ,GAAG,CAAC,sCAAsC,aAAa,IAAI,CAAC;YAEpE,KAAK,MAAM,UAAU,eAAgB;gBACnC,oGAAoG;gBACpG,MAAM,mBAAmB,WAAW,iBAAiB,kBAC7B,WAAW,gBAAgB,eAAe;gBAElE,IAAI,eAAe,GAAG,CAAC,mBAAmB;oBACxC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,OAAO,uBAAuB,EAAE,kBAAkB;oBAC1F;gBACF;gBAEA,MAAM,YAAY,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;gBACzC,MAAM,eAAe,CAAC,OAAO,EAAE,OAAO,QAAQ,CAAC;gBAE/C,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,OAAO,CAAC,CAAC,EAAE;oBACxD;oBACA,WAAW,YAAY,CAAC,UAAU;oBAClC;oBACA,cAAc,YAAY,CAAC,aAAa;oBACxC,SAAS,CAAC,CAAC,YAAY,CAAC,UAAU;oBAClC,YAAY,CAAC,CAAC,YAAY,CAAC,aAAa;gBAC1C;gBAEA,IAAI,YAAY,CAAC,UAAU,IAAI,YAAY,CAAC,aAAa,EAAE;oBACzD;oBACA,eAAe,GAAG,CAAC;oBACnB,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,OAAO,YAAY,EAAE,YAAY,CAAC,UAAU,CAAC,cAAc,EAAE,YAAY,CAAC,aAAa,EAAE;gBAC9I;YACF;YAEA,qEAAqE;YACrE,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;gBAChC,IAAI,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC,UAAU;oBACtD,MAAM,UAAU,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,SAAS;oBAC5D,MAAM,aAAa,CAAC,OAAO,EAAE,QAAQ,QAAQ,CAAC;oBAE9C,4CAA4C;oBAC5C,MAAM,mBAAmB,YAAY,iBAAiB,kBAC9B,YAAY,gBAAgB,eAAe;oBAEnE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,QAAQ,CAAC,CAAC,EAAE;wBACpE;wBACA,gBAAgB,eAAe,GAAG,CAAC;wBACnC,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI;wBAC5B,YAAY,CAAC,CAAC,YAAY,CAAC,WAAW;oBACxC;oBAEA,IAAI,CAAC,eAAe,GAAG,CAAC,qBACpB,YAAY,CAAC,IAAI,IACjB,YAAY,CAAC,WAAW,EAAE;wBAC5B;wBACA,eAAe,GAAG,CAAC;wBACnB,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,QAAQ,YAAY,EAAE,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,WAAW,EAAE;oBAClJ;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,kBAAkB;YAC3E,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO;YAEvF,uGAAuG;YACvG,IAAI,uBAAuB,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC;YAC7G,IAAI,0BAA0B,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC;YAEhH,QAAQ,GAAG,CAAC,4DAA4D;YACxE,QAAQ,GAAG,CAAC,+DAA+D;YAE3E,6GAA6G;YAC7G,sFAAsF;YACtF,IAAI,mBAAmB,KAAK,qBAAqB,MAAM,IAAI,KAAK,wBAAwB,MAAM,IAAI,GAAG;gBACnG,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,uCAAuC,qBAAqB,IAAI,CAAC;gBAC7E,QAAQ,GAAG,CAAC,0CAA0C,wBAAwB,IAAI,CAAC;gBAEnF,qEAAqE;gBACrE,MAAM,kBAAkB,IAAI;gBAC5B,qBAAqB,OAAO,CAAC,CAAA;oBAC3B,MAAM,SAAS,MAAM,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,SAAS;oBAC7D,IAAI,wBAAwB,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,QAAQ,CAAC,GAAG;wBAChE,gBAAgB,GAAG,CAAC;oBACtB;gBACF;gBAEA,MAAM,oBAAoB,gBAAgB,IAAI;gBAC9C,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,mBAAmB;gBAEvE,IAAI,qBAAqB,GAAG;oBAC1B,QAAQ,GAAG,CAAC;oBACZ,mBAAmB;gBACrB;YACF;YAEA,8BAA8B;YAC9B,QAAQ,GAAG,CAAC,6CAA6C,qBAAqB,MAAM;YACpF,QAAQ,GAAG,CAAC,gDAAgD,wBAAwB,MAAM;YAE1F,IAAI,mBAAmB,GAAG;gBACxB,QAAQ,GAAG,CAAC,gEAAgE;gBAC5E,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,6GAA6G,EAAE,iBAAiB,GAAG,CAAC;gBAC9I;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,iDAAiD,kBAAkB;YACjF;QACF;QAEA,2CAA2C;QAC3C,QAAQ,GAAG,CAAC,wBAAwB,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,MAAM;QAE1E,yCAAyC;QACzC,QAAQ,GAAG,CAAC,0BAA0B,OAAO,IAAI,CAAC,MAAM,YAAY;QACpE,QAAQ,GAAG,CAAC,yBAAyB,MAAM,YAAY;QAEvD,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,+FAA+F;IAC/F,MAAM,0BAA0B,OAAO,UAAkB;QACvD,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,UAAU;QAC1E,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,iBAAiB,gBAAgB;QAEtE,8CAA8C;QAC9C,IAAI,gBAAgB,CAAC;QACrB,IAAI;YACF,MAAM,sBAAsB,aAAa,OAAO,CAAC;YACjD,IAAI,qBAAqB;gBACvB,gBAAgB,KAAK,KAAK,CAAC;gBAC3B,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yEAAyE;QACzF;QAEA,2CAA2C;QAC3C,MAAM,eAAuC;YAAE,GAAG,MAAM,YAAY;QAAC;QAErE,iDAAiD;QACjD,IAAI,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG,GAAG;YACzC,QAAQ,GAAG,CAAC,iEAAiE,KAAK,SAAS,CAAC;YAE5F,qDAAqD;YACrD,IAAI,sBAAsB;YAE1B,OAAO,OAAO,CAAC,eAAe,OAAO,CAAC,CAAC,CAAC,YAAY,QAAuB;gBACzE,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;oBACnD,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;oBACxD,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;oBAEnE,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,WAAW,YAAY,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,QAAQ,YAAY,EAAE;oBACzH;oBAEA,+BAA+B;oBAC/B,MAAM,iBAAiB,eAAe,iBAAiB,kBACjC,eAAe,gBAAgB,eAAe;oBAEpE,IAAI,mBAAmB,YAAY;wBACjC,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;wBAC5D,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wBACvE,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,gBAAgB;oBAC/E;gBACF;YACF;YACA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,oBAAoB,0DAA0D,CAAC;YACrH,QAAQ,GAAG,CAAC,wDAAwD,KAAK,SAAS,CAAC;QACrF;QAEA,8EAA8E;QAC9E,MAAM,eAAe;YACnB,GAAG,KAAK;YACR,cAAc;YACd,UAAU;YACV,cAAc;QAChB;QAEA,wDAAwD;QACxD,IAAI,eAAe;YACjB,aAAa,YAAY,CAAC,cAAc,GAAG;YAC3C,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;YAElE,6BAA6B;YAC7B,IAAI;gBACF,aAAa,OAAO,CAAC,yBAAyB;gBAC9C,QAAQ,GAAG,CAAC,2DAA2D;YACzE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oEAAoE;YACpF;QACF;QAEA,qCAAqC;QACrC,SAAS;QAET,0DAA0D;QAC1D,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,QAAQ,GAAG,CAAC,6BAA6B,WAAW,KAAK;YACzD,SAAS;gBACP,GAAG,KAAK;gBACR,cAAc;gBACd,OAAO,WAAW,KAAK;gBACvB,MAAM;YACR;YACA;QACF;QAEA,IAAI,CAAC,MAAM,IAAI,EAAE;YACf,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,SAAS;YACP,GAAG,KAAK;YACR,cAAc;YACd,aAAa;YACb,UAAU;YACV,MAAM;YACN,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,oCAAoC;gBAC9C,MAAM,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG;gBACrC,UAAU,MAAM,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,OAAO,MAAM,QAAQ;gBACzF,WAAW,MAAM,SAAS;gBAC1B,UAAU;gBACV,OAAO,MAAM,KAAK;gBAClB,eAAe,iBAAiB;YAClC;YAEA,2CAA2C;YAC3C,QAAQ,GAAG,CAAC,mDAAmD;YAC/D,QAAQ,GAAG,CAAC,uDAAuD,MAAM,YAAY,CAAC,cAAc;YAEpG,sEAAsE;YACtE,MAAM,sBAAsB;gBAAE,GAAG,MAAM,YAAY;YAAC;YAEpD,uCAAuC;YACvC,IAAI,eAAe;gBACjB,oBAAoB,cAAc,GAAG;gBACrC,QAAQ,GAAG,CAAC,0DAA0D;YACxE;YAEA,2FAA2F;YAC3F,MAAM,SAAS,MAAM,kHAAA,CAAA,gBAAa,CAAC,YAAY,CAC7C,MAAM,IAAI,EACV,MAAM,SAAS,EACf,UACA,MAAM,KAAK,EACX,MAAM,WAAW,EACjB,MAAM,IAAI,EACV,qBACA,MAAM,QAAQ,EACd,MAAM,SAAS,EACf,CAAC;gBACC,SAAS;oBACP,GAAG,KAAK;oBACR,cAAc;oBACd;gBACF;YACF;YAGF,0CAA0C;YAC1C,SAAS;gBACP,GAAG,KAAK;gBACR,cAAc;gBACd,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,cAAc;YAChB;QAEA,gCAAgC;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,SAAS;gBACP,GAAG,KAAK;gBACR,cAAc;gBACd,aAAa;gBACb,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;YACA,MAAM;QACR;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ,GAAG,CAAC;QAEZ,8CAA8C;QAC9C,IAAI;YACF,MAAM,sBAAsB,aAAa,OAAO,CAAC;YACjD,IAAI,qBAAqB;gBACvB,MAAM,gBAAgB,KAAK,KAAK,CAAC;gBACjC,QAAQ,GAAG,CAAC,gFAAgF;gBAE5F,kEAAkE;gBAClE,MAAM,sBAAsB;oBAAE,GAAG,MAAM,YAAY;gBAAC;gBACpD,IAAI,sBAAsB;gBAE1B,iDAAiD;gBACjD,IAAI,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG,GAAG;oBACzC,OAAO,OAAO,CAAC,eAAe,OAAO,CAAC,CAAC,CAAC,YAAY,QAAuB;wBACzE,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;4BACnD,mCAAmC;4BACnC,mBAAmB,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;4BAC/D,mBAAmB,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;4BAE1E,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,WAAW,YAAY,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,QAAQ,YAAY,EAAE;4BACzH;4BAEA,+BAA+B;4BAC/B,MAAM,iBAAiB,eAAe,iBAAiB,kBACjC,eAAe,gBAAgB,eAAe;4BAEpE,IAAI,mBAAmB,YAAY;gCACjC,mBAAmB,CAAC,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;gCACnE,mBAAmB,CAAC,CAAC,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;gCAC9E,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,gBAAgB;4BAC/E;wBACF;oBACF;oBAEA,mDAAmD;oBACnD,SAAS,CAAA,YAAa,CAAC;4BACrB,GAAG,SAAS;4BACZ,cAAc;wBAChB,CAAC;oBAED,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,oBAAoB,mDAAmD,CAAC;oBAC9G,QAAQ,GAAG,CAAC,2CAA2C,KAAK,SAAS,CAAC;gBACxE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yEAAyE;QACzF;QAEA,+CAA+C;QAC/C,IAAI,MAAM,SAAS,KAAK,SAAS;YAC/B,8DAA8D;YAC9D,IAAI,gBAAgB,MAAM,YAAY,CAAC,cAAc;YACrD,IAAI,CAAC,eAAe;gBAClB,IAAI;oBACF,MAAM,sBAAsB,aAAa,OAAO,CAAC;oBACjD,IAAI,qBAAqB;wBACvB,gBAAgB;wBAChB,QAAQ,GAAG,CAAC,gEAAgE;wBAC5E,eAAe,kBAAkB;oBACnC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yEAAyE;gBACzF;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,iBAAiB,WAAW;YAEpF,uDAAuD;YACvD,IAAI,CAAC,eAAe;gBAClB,QAAQ,GAAG,CAAC;gBACZ,2EAA2E;gBAC3E,OAAO,wBAAwB,MAAM,YAAY,EAAE;YACrD,OAAO;gBACL,8EAA8E;gBAC9E,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,eAAe;gBAC9E,OAAO,wBAAwB,MAAM,YAAY,EAAE;YACrD;QACF;QAEA,+CAA+C;QAC/C,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,QAAQ,GAAG,CAAC,6BAA6B,WAAW,KAAK;YACzD,SAAS;gBACP,GAAG,KAAK;gBACR,OAAO,WAAW,KAAK;gBACvB,MAAM;YACR;YACA;QACF;QAEA,IAAI,CAAC,MAAM,IAAI,EAAE;YACf,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,SAAS;YACP,GAAG,KAAK;YACR,aAAa;YACb,UAAU;YACV,MAAM;YACN,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,oCAAoC;gBAC9C,MAAM,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG;gBACrC,UAAU,MAAM,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,OAAO,MAAM,QAAQ;gBACzF,WAAW,MAAM,SAAS;gBAC1B,cAAc,MAAM,YAAY;gBAChC,OAAO,MAAM,KAAK;gBAClB,eAAe,MAAM,YAAY,CAAC,cAAc,IAAI;YACtD;YAEA,+DAA+D;YAC/D,MAAM,SAAS,MAAM,kHAAA,CAAA,gBAAa,CAAC,YAAY,CAC7C,MAAM,IAAI,EACV,MAAM,SAAS,EACf,MAAM,YAAY,EAClB,MAAM,KAAK,EACX,MAAM,WAAW,EACjB,MAAM,IAAI,EACV,MAAM,YAAY,EAClB,MAAM,QAAQ,EACd,MAAM,SAAS,EACf,CAAC;gBACC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;gBAE3C,SAAS,CAAC;oBACR,6CAA6C;oBAC7C,IAAI,WAAW,UAAU,QAAQ,EAAE;wBACjC,OAAO;4BACL,GAAG,SAAS;4BACZ;4BACA,+CAA+C;4BAC/C,MAAM,YAAY,MAAM,WAAW,MAAM,eAAe,UAAU,IAAI;wBACxE;oBACF;oBACA,OAAO;gBACT;YACF;YAGF,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,kBAAkB;YAClB,SAAS;gBACP,GAAG,KAAK;gBACR,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,SAAS;gBACP,GAAG,KAAK;gBACR,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,SAAS;YAAE,GAAG,KAAK;YAAE;QAAK;IAC5B;IAEA,qBAAqB;IACrB,MAAM,cAAc,CAAC;QACnB,SAAS;YACP,GAAG,KAAK;YACR;QACF;QACA,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,QAAQ,CAAC;IACnD;IAEA,gFAAgF;IAChF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,oDAAoD;YACpD,IAAI;gBACF,MAAM,sBAAsB,aAAa,OAAO,CAAC;gBACjD,IAAI,qBAAqB;oBACvB,QAAQ,GAAG,CAAC,gEAAgE;oBAC5E,eAAe,kBAAkB;gBACnC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yEAAyE;YACzF;YAEA,iEAAiE;YACjE,MAAM;sEAA4B,CAAC;oBACjC,IAAI,MAAM,MAAM,EAAE;wBAChB,QAAQ,GAAG,CAAC,0DAA0D,MAAM,MAAM;wBAElF,oCAAoC;wBACpC,MAAM,gBAAgB,MAAM,MAAM;wBAClC,MAAM,sBAA8C;4BAAE,GAAG,MAAM,YAAY;wBAAC;wBAC5E,IAAI,sBAAsB;wBAE1B,OAAO,OAAO,CAAC,eAAe,OAAO;kFAAC,CAAC,CAAC,YAAY,QAAuB;gCACzE,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;oCACnD,sBAAsB;oCACtB,mBAAmB,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;oCAC/D,mBAAmB,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;oCAE1E,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,WAAW,YAAY,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,QAAQ,YAAY,EAAE;oCACvI;oCAEA,+BAA+B;oCAC/B,MAAM,iBAAiB,eAAe,iBAAiB,kBACjC,eAAe,gBAAgB,eAAe;oCAEpE,IAAI,mBAAmB,YAAY;wCACjC,mBAAmB,CAAC,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;wCACnE,mBAAmB,CAAC,CAAC,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wCAC9E,QAAQ,GAAG,CAAC,CAAC,4DAA4D,EAAE,gBAAgB;oCAC7F;gCACF;4BACF;;wBAEA,mDAAmD;wBACnD;kFAAS,CAAA,YAAa,CAAC;oCACrB,GAAG,SAAS;oCACZ,cAAc;gCAChB,CAAC;;wBAED,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,oBAAoB,iCAAiC,CAAC;wBAC1G,QAAQ,GAAG,CAAC,yDAAyD,KAAK,SAAS,CAAC;oBACtF;gBACF;;YAEA,qBAAqB;YACrB,OAAO,gBAAgB,CAAC,yBAAyB;YAEjD,mCAAmC;YACnC;4CAAO;oBACL,OAAO,mBAAmB,CAAC,yBAAyB;gBACtD;;QACF;mCAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,eAAkC;QACtC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;GAziCa;KAAA;AA4iCN,MAAM,YAAY;;IACvB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 2897, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2903, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/upload/UploadTypeSelection.tsx"], "sourcesContent": ["// components/upload/UploadTypeSelection.tsx\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { X } from 'lucide-react';\r\n\r\ninterface UploadTypeSelectionProps {\r\n  onNext: (type: string) => void;\r\n  onClose: () => void;\r\n}\r\n\r\nconst UploadTypeSelection: React.FC<UploadTypeSelectionProps> = ({\r\n  onNext,\r\n  onClose\r\n}) => {\r\n  const [selectedType, setSelectedType] = useState<string | null>(null);\r\n\r\n  const handleTypeSelect = (type: string) => {\r\n    setSelectedType(type);\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (selectedType) {\r\n      onNext(selectedType);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-2 sm:p-4 bg-black/50 backdrop-blur-sm\">\r\n      <div className=\"bg-[#FFF7E8] rounded-2xl w-full max-w-[95%] sm:max-w-[90%] md:max-w-[80%] lg:max-w-[60%] xl:max-w-[50%] p-3 sm:p-6 relative overflow-y-auto max-h-[90vh]\">\r\n        {/* Close button */}\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-2 sm:top-4 right-2 sm:right-4 text-gray-800 hover:text-red-600\"\r\n        >\r\n          <X size={20} className=\"sm:w-6 sm:h-6\" />\r\n        </button>\r\n\r\n        {/* Header */}\r\n        <div className=\"flex items-center mb-4 sm:mb-6\">\r\n          <div className=\"flex items-center mr-2\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={28}\r\n              height={28}\r\n              className=\"object-cover sm:w-8 sm:h-8\"\r\n            />\r\n          </div>\r\n          <h2 className=\"text-lg sm:text-xl font-bold\">Create New</h2>\r\n          <div className=\"ml-2\">\r\n            <Image\r\n              src=\"/pics/umoments.png\"\r\n              alt=\"Moments\"\r\n              width={18}\r\n              height={18}\r\n              className=\"object-cover sm:w-5 sm:h-5\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <p className=\"text-sm sm:text-base mb-4 sm:mb-6\">Upload What is Relevant to you</p>\r\n\r\n        {/* Upload type options */}\r\n        <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6 border border-blue-200 border-dashed p-2 sm:p-4 rounded-lg\">\r\n          <div\r\n            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n              ${selectedType === 'flashes' ? 'border-red-600' : 'border-gray-200'}\r\n            `}\r\n            onClick={() => handleTypeSelect('flashes')}\r\n          >\r\n            <div className=\"flex justify-center mb-1 sm:mb-2\">\r\n              <Image\r\n                src=\"/pics/uflashes.png\"\r\n                alt=\"Flashes\"\r\n                width={32}\r\n                height={32}\r\n                className=\"object-contain w-7 h-7 sm:w-10 sm:h-10\"\r\n              />\r\n            </div>\r\n            <div className=\"flex items-center justify-center\">\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center\r\n                ${selectedType === 'flashes' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedType === 'flashes' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-xs sm:text-sm font-medium\">Flashes</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n              ${selectedType === 'glimpses' ? 'border-red-600' : 'border-gray-200'}\r\n            `}\r\n            onClick={() => handleTypeSelect('glimpses')}\r\n          >\r\n            <div className=\"flex justify-center mb-1 sm:mb-2\">\r\n              <Image\r\n                src=\"/pics/uglimpses.png\"\r\n                alt=\"Glimpses\"\r\n                width={32}\r\n                height={32}\r\n                className=\"object-contain w-7 h-7 sm:w-10 sm:h-10\"\r\n              />\r\n            </div>\r\n            <div className=\"flex items-center justify-center\">\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center\r\n                ${selectedType === 'glimpses' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedType === 'glimpses' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-xs sm:text-sm font-medium\">Glimpses</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n              ${selectedType === 'movies' ? 'border-red-600' : 'border-gray-200'}\r\n            `}\r\n            onClick={() => handleTypeSelect('movies')}\r\n          >\r\n            <div className=\"flex justify-center mb-1 sm:mb-2\">\r\n              <Image\r\n                src=\"/pics/umovies.png\"\r\n                alt=\"Movies\"\r\n                width={32}\r\n                height={32}\r\n                className=\"object-contain w-7 h-7 sm:w-10 sm:h-10\"\r\n              />\r\n            </div>\r\n            <div className=\"flex items-center justify-center\">\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center\r\n                ${selectedType === 'movies' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedType === 'movies' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-xs sm:text-sm font-medium\">Movies</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n              ${selectedType === 'photos' ? 'border-red-600' : 'border-gray-200'}\r\n            `}\r\n            onClick={() => handleTypeSelect('photos')}\r\n          >\r\n            <div className=\"flex justify-center mb-1 sm:mb-2\">\r\n              <Image\r\n                src=\"/pics/uphotos.png\"\r\n                alt=\"Photos\"\r\n                width={32}\r\n                height={32}\r\n                className=\"object-contain w-7 h-7 sm:w-10 sm:h-10\"\r\n              />\r\n            </div>\r\n            <div className=\"flex items-center justify-center\">\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center\r\n                ${selectedType === 'photos' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedType === 'photos' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-xs sm:text-sm font-medium\">Photos</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n              ${selectedType === 'live' ? 'border-red-600' : 'border-gray-200'}\r\n            `}\r\n            onClick={() => handleTypeSelect('live')}\r\n          >\r\n            <div className=\"flex justify-center mb-1 sm:mb-2\">\r\n              <Image\r\n                src=\"/pics/ulive.png\"\r\n                alt=\"Live\"\r\n                width={32}\r\n                height={32}\r\n                className=\"object-contain w-7 h-7 sm:w-10 sm:h-10\"\r\n              />\r\n            </div>\r\n            <div className=\"flex items-center justify-center\">\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center\r\n                ${selectedType === 'live' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedType === 'live' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-xs sm:text-sm font-medium\">Live</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            className={`flex flex-col items-center justify-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n              ${selectedType === 'moments' ? 'border-red-600' : 'border-gray-200'}\r\n            `}\r\n            onClick={() => handleTypeSelect('moments')}\r\n          >\r\n            <div className=\"flex justify-center mb-1 sm:mb-2\">\r\n              <Image\r\n                src=\"/pics/umoments.png\"\r\n                alt=\"Moments\"\r\n                width={32}\r\n                height={32}\r\n                className=\"object-contain w-7 h-7 sm:w-10 sm:h-10\"\r\n              />\r\n            </div>\r\n            <div className=\"flex items-center justify-center\">\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-1 sm:mr-2 flex items-center justify-center\r\n                ${selectedType === 'moments' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedType === 'moments' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-xs sm:text-sm font-medium\">Moments</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Next button */}\r\n        <div className=\"flex justify-center\">\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={!selectedType}\r\n            className={`flex items-center justify-center px-4 sm:px-6 py-1.5 sm:py-2 rounded-md text-sm sm:text-base ${selectedType\r\n              ? 'bg-red-600 text-white hover:bg-red-700'\r\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n              } transition duration-200`}\r\n          >\r\n            Next\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-4 w-4 sm:h-5 sm:w-5 ml-1\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UploadTypeSelection;"], "names": [], "mappings": "AAAA,4CAA4C;;;;;AAG5C;AACA;AACA;;;AAJA;;;;AAWA,MAAM,sBAA0D,CAAC,EAC/D,MAAM,EACN,OAAO,EACR;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc;YAChB,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;wBAAI,WAAU;;;;;;;;;;;8BAIzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAC7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAE,WAAU;8BAAoC;;;;;;8BAGjD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAW,CAAC;cACV,EAAE,iBAAiB,YAAY,mBAAmB,kBAAkB;YACtE,CAAC;4BACD,SAAS,IAAM,iBAAiB;;8CAEhC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,iBAAiB,YAAY,mBAAmB,kBAAkB;cACtE,CAAC;sDACE,iBAAiB,2BAChB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;sCAIrD,6LAAC;4BACC,WAAW,CAAC;cACV,EAAE,iBAAiB,aAAa,mBAAmB,kBAAkB;YACvE,CAAC;4BACD,SAAS,IAAM,iBAAiB;;8CAEhC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,iBAAiB,aAAa,mBAAmB,kBAAkB;cACvE,CAAC;sDACE,iBAAiB,4BAChB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;sCAIrD,6LAAC;4BACC,WAAW,CAAC;cACV,EAAE,iBAAiB,WAAW,mBAAmB,kBAAkB;YACrE,CAAC;4BACD,SAAS,IAAM,iBAAiB;;8CAEhC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,iBAAiB,WAAW,mBAAmB,kBAAkB;cACrE,CAAC;sDACE,iBAAiB,0BAChB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;sCAIrD,6LAAC;4BACC,WAAW,CAAC;cACV,EAAE,iBAAiB,WAAW,mBAAmB,kBAAkB;YACrE,CAAC;4BACD,SAAS,IAAM,iBAAiB;;8CAEhC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,iBAAiB,WAAW,mBAAmB,kBAAkB;cACrE,CAAC;sDACE,iBAAiB,0BAChB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;sCAIrD,6LAAC;4BACC,WAAW,CAAC;cACV,EAAE,iBAAiB,SAAS,mBAAmB,kBAAkB;YACnE,CAAC;4BACD,SAAS,IAAM,iBAAiB;;8CAEhC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,iBAAiB,SAAS,mBAAmB,kBAAkB;cACnE,CAAC;sDACE,iBAAiB,wBAChB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;sCAIrD,6LAAC;4BACC,WAAW,CAAC;cACV,EAAE,iBAAiB,YAAY,mBAAmB,kBAAkB;YACtE,CAAC;4BACD,SAAS,IAAM,iBAAiB;;8CAEhC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,iBAAiB,YAAY,mBAAmB,kBAAkB;cACtE,CAAC;sDACE,iBAAiB,2BAChB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;;;;;;;8BAMvD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,SAAS;wBACT,UAAU,CAAC;wBACX,WAAW,CAAC,6FAA6F,EAAE,eACvG,2CACA,+CACD,wBAAwB,CAAC;;4BAC7B;0CAEC,6LAAC;gCACC,OAAM;gCACN,WAAU;gCACV,SAAQ;gCACR,MAAK;0CAEL,cAAA,6LAAC;oCACC,UAAS;oCACT,GAAE;oCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GArPM;KAAA;uCAuPS", "debugId": null}}, {"offset": {"line": 3455, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3461, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/upload/VideoCategorySelection.tsx"], "sourcesContent": ["// components/upload/VideoCategorySelection.tsx\r\n// This component is used for both video and photo category selection\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { X, Clock, Image as ImageIcon, Upload } from 'lucide-react';\r\n\r\ninterface VideoCategorySelectionProps {\r\n  onNext: (category: string) => void;\r\n  onBack: () => void;\r\n  onUpload: (category: string) => void;\r\n  onThumbnailUpload: () => void;\r\n  onClose: () => void;\r\n  mediaType?: 'photo' | 'video'; // Optional prop to specify if this is for photos or videos\r\n  selectedType?: string; // Optional prop to specify the selected type (moments, flashes, etc.)\r\n}\r\n\r\nconst VideoCategorySelection: React.FC<VideoCategorySelectionProps> = ({\r\n  onNext,\r\n  onBack,\r\n  onUpload,\r\n  onThumbnailUpload,\r\n  onClose,\r\n  mediaType = 'video', // Default to video if not specified\r\n  selectedType = '' // Default to empty string if not specified\r\n}) => {\r\n  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);\r\n\r\n  const handleCategorySelect = (category: string) => {\r\n    console.log('VIDEO CATEGORY SELECTION - Selected category:', category);\r\n    setSelectedCategory(category);\r\n\r\n    // Just set the selected category, don't automatically proceed\r\n    // This allows the user to click the Upload button\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (selectedCategory) {\r\n      onNext(selectedCategory);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-2 sm:p-4 bg-black/50 backdrop-blur-sm\">\r\n      <div className=\"bg-[#FFF7E8] rounded-2xl w-full max-w-[95%] sm:max-w-[90%] md:max-w-[80%] lg:max-w-[60%] xl:max-w-[50%] p-3 sm:p-6 relative overflow-y-auto max-h-[90vh]\">\r\n        {/* Close button */}\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-2 sm:top-4 right-2 sm:right-4 text-gray-800 hover:text-red-600\"\r\n        >\r\n          <X size={20} className=\"sm:w-6 sm:h-6\" />\r\n        </button>\r\n\r\n        {/* Header */}\r\n        <div className=\"flex items-center mb-3 sm:mb-6\">\r\n          <div className=\"flex items-center mr-2\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={28}\r\n              height={28}\r\n              className=\"object-cover sm:w-8 sm:h-8\"\r\n            />\r\n          </div>\r\n          <h2 className=\"text-lg sm:text-xl font-bold\">Create New</h2>\r\n          <div className=\"ml-2\">\r\n            <Image\r\n              src=\"/pics/umoments.png\"\r\n              alt=\"Moments\"\r\n              width={18}\r\n              height={18}\r\n              className=\"object-cover sm:w-5 sm:h-5\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <p className=\"text-sm sm:text-base mb-3 sm:mb-6\">Choose {selectedType === 'photos' || (selectedType === '' && mediaType === 'photo') ? 'Photo' : ['flashes', 'glimpses', 'movies'].includes(selectedType) || (selectedType === '' && mediaType === 'video') ? 'Video' : selectedType === 'moments' ? 'Moments' : 'Media'} Category</p>\r\n\r\n        {/* Add warning for moments videos */}\r\n        {selectedType === 'moments' && (\r\n          <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-2 sm:p-4 mb-3 sm:mb-6 text-xs sm:text-sm\">\r\n            <div className=\"flex\">\r\n              <div className=\"flex-shrink-0\">\r\n                <svg className=\"h-4 w-4 sm:h-5 sm:w-5 text-yellow-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <div className=\"ml-2 sm:ml-3\">\r\n                <p className=\"text-yellow-700\">\r\n                  <strong>Important:</strong> Moments videos must be 1 minute or less in duration. Longer videos will be rejected.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-6\">\r\n          {/* Left side - Video category options */}\r\n          <div className=\"flex flex-col space-y-2 sm:space-y-4 mb-3 sm:mb-6 flex-grow\">\r\n            <div\r\n              className={`flex items-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n                ${selectedCategory === 'my_wedding_videos' ? 'border-red-600' : 'border-gray-200'}\r\n              `}\r\n              onClick={() => handleCategorySelect('my_wedding_videos')}\r\n            >\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-2 sm:mr-3 flex items-center justify-center\r\n                ${selectedCategory === 'my_wedding_videos' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedCategory === 'my_wedding_videos' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-sm sm:text-base font-medium\">My Wedding {selectedType === 'photos' || (selectedType === '' && mediaType === 'photo') ? 'Photos' : ['flashes', 'glimpses', 'movies'].includes(selectedType) || (selectedType === '' && mediaType === 'video') ? 'Videos' : selectedType === 'moments' ? 'Moments' : 'Media'}</span>\r\n            </div>\r\n\r\n            <div\r\n              className={`flex items-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n                ${selectedCategory === 'wedding_influencer' ? 'border-red-600' : 'border-gray-200'}\r\n              `}\r\n              onClick={() => handleCategorySelect('wedding_influencer')}\r\n            >\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-2 sm:mr-3 flex items-center justify-center\r\n                ${selectedCategory === 'wedding_influencer' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedCategory === 'wedding_influencer' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-sm sm:text-base font-medium\">Wedding Influencer</span>\r\n            </div>\r\n\r\n            <div\r\n              className={`flex items-center p-2 sm:p-4 border rounded-lg cursor-pointer\r\n                ${selectedCategory === 'friends_family_videos' ? 'border-red-600' : 'border-gray-200'}\r\n              `}\r\n              onClick={() => handleCategorySelect('friends_family_videos')}\r\n            >\r\n              <div className={`w-4 h-4 sm:w-5 sm:h-5 border rounded-full mr-2 sm:mr-3 flex items-center justify-center\r\n                ${selectedCategory === 'friends_family_videos' ? 'border-red-600' : 'border-gray-300'}\r\n              `}>\r\n                {selectedCategory === 'friends_family_videos' && (\r\n                  <div className=\"w-2 h-2 sm:w-3 sm:h-3 bg-red-600 rounded-full\"></div>\r\n                )}\r\n              </div>\r\n              <span className=\"text-sm sm:text-base font-medium\">Friends / Family {selectedType === 'photos' || (selectedType === '' && mediaType === 'photo') ? 'Photos' : ['flashes', 'glimpses', 'movies'].includes(selectedType) || (selectedType === '' && mediaType === 'video') ? 'Videos' : selectedType === 'moments' ? 'Moments' : 'Media'}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right side - Upload buttons */}\r\n          <div className=\"flex flex-col space-y-2 sm:space-y-4\">\r\n            <button\r\n              onClick={() => {\r\n                if (selectedCategory) {\r\n                  console.log('VIDEO CATEGORY SELECTION - Upload button clicked with category:', selectedCategory);\r\n                  // Only call onNext to set the category and proceed to file upload\r\n                  // This will trigger handleCategorySelected in the parent which will call handleFileUpload\r\n                  onNext(selectedCategory);\r\n                } else {\r\n                  alert(`Please select a ${selectedType === 'photos' || (selectedType === '' && mediaType === 'photo') ? 'photo' : ['flashes', 'glimpses', 'movies'].includes(selectedType) || (selectedType === '' && mediaType === 'video') ? 'video' : selectedType === 'moments' ? 'moments' : 'media'} category first`);\r\n                }\r\n              }}\r\n              disabled={!selectedCategory}\r\n              className={`flex flex-col items-center justify-center px-4 sm:px-6 py-3 sm:py-4 rounded-md w-full sm:w-48 h-14 sm:h-16 ${selectedCategory ? 'bg-black text-white hover:bg-gray-800' : 'bg-gray-300 text-gray-500 cursor-not-allowed'} transition duration-200`}\r\n            >\r\n              {selectedType === 'moments' ? (\r\n                <Upload size={18} className=\"mb-1 sm:mb-1 sm:w-5 sm:h-5\" />\r\n              ) : selectedType === 'photos' || mediaType === 'photo' ? (\r\n                <ImageIcon size={18} className=\"mb-1 sm:mb-1 sm:w-5 sm:h-5\" />\r\n              ) : ['flashes', 'glimpses', 'movies'].includes(selectedType) || mediaType === 'video' ? (\r\n                <Clock size={18} className=\"mb-1 sm:mb-1 sm:w-5 sm:h-5\" />\r\n              ) : (\r\n                <ImageIcon size={18} className=\"mb-1 sm:mb-1 sm:w-5 sm:h-5\" />\r\n              )}\r\n              <span className=\"text-sm sm:text-base\">\r\n                {selectedType === 'moments' ? 'Upload Files' :\r\n                  selectedType === 'photos' ? 'Upload Photos' :\r\n                    ['flashes', 'glimpses', 'movies'].includes(selectedType) ? 'Upload Videos' :\r\n                      mediaType === 'photo' ? 'Upload Photos' : 'Upload Videos'}\r\n              </span>\r\n            </button>\r\n\r\n            {/* Thumbnail upload button removed */}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation buttons */}\r\n        <div className=\"flex justify-between mt-4 sm:mt-8\">\r\n          <button\r\n            onClick={onBack}\r\n            className=\"flex items-center justify-center px-3 sm:px-6 py-1.5 sm:py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm sm:text-base\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-4 w-4 sm:h-5 sm:w-5 mr-1\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Back\r\n          </button>\r\n\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={!selectedCategory}\r\n            className={`flex items-center justify-center px-3 sm:px-6 py-1.5 sm:py-2 rounded-md text-sm sm:text-base ${selectedCategory\r\n              ? 'bg-red-600 text-white hover:bg-red-700'\r\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n              } transition duration-200`}\r\n          >\r\n            Next\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-4 w-4 sm:h-5 sm:w-5 ml-1\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoCategorySelection;"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,qEAAqE;;;;;AAGrE;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAgBA,MAAM,yBAAgE,CAAC,EACrE,MAAM,EACN,MAAM,EACN,QAAQ,EACR,iBAAiB,EACjB,OAAO,EACP,YAAY,OAAO,EACnB,eAAe,GAAG,2CAA2C;AAA5C,EAClB;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,iDAAiD;QAC7D,oBAAoB;IAEpB,8DAA8D;IAC9D,kDAAkD;IACpD;IAEA,MAAM,aAAa;QACjB,IAAI,kBAAkB;YACpB,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;wBAAI,WAAU;;;;;;;;;;;8BAIzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAC7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAE,WAAU;;wBAAoC;wBAAQ,iBAAiB,YAAa,iBAAiB,MAAM,cAAc,UAAW,UAAU;4BAAC;4BAAW;4BAAY;yBAAS,CAAC,QAAQ,CAAC,iBAAkB,iBAAiB,MAAM,cAAc,UAAW,UAAU,iBAAiB,YAAY,YAAY;wBAAQ;;;;;;;gBAGxT,iBAAiB,2BAChB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAwC,SAAQ;oCAAY,MAAK;8CAC9E,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAoN,UAAS;;;;;;;;;;;;;;;;0CAG5P,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;sDAAO;;;;;;wCAAmB;;;;;;;;;;;;;;;;;;;;;;;8BAOrC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAW,CAAC;gBACV,EAAE,qBAAqB,sBAAsB,mBAAmB,kBAAkB;cACpF,CAAC;oCACD,SAAS,IAAM,qBAAqB;;sDAEpC,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,qBAAqB,sBAAsB,mBAAmB,kBAAkB;cACpF,CAAC;sDACE,qBAAqB,qCACpB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;;gDAAmC;gDAAY,iBAAiB,YAAa,iBAAiB,MAAM,cAAc,UAAW,WAAW;oDAAC;oDAAW;oDAAY;iDAAS,CAAC,QAAQ,CAAC,iBAAkB,iBAAiB,MAAM,cAAc,UAAW,WAAW,iBAAiB,YAAY,YAAY;;;;;;;;;;;;;8CAG3T,6LAAC;oCACC,WAAW,CAAC;gBACV,EAAE,qBAAqB,uBAAuB,mBAAmB,kBAAkB;cACrF,CAAC;oCACD,SAAS,IAAM,qBAAqB;;sDAEpC,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,qBAAqB,uBAAuB,mBAAmB,kBAAkB;cACrF,CAAC;sDACE,qBAAqB,sCACpB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;8CAGrD,6LAAC;oCACC,WAAW,CAAC;gBACV,EAAE,qBAAqB,0BAA0B,mBAAmB,kBAAkB;cACxF,CAAC;oCACD,SAAS,IAAM,qBAAqB;;sDAEpC,6LAAC;4CAAI,WAAW,CAAC;gBACf,EAAE,qBAAqB,0BAA0B,mBAAmB,kBAAkB;cACxF,CAAC;sDACE,qBAAqB,yCACpB,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,6LAAC;4CAAK,WAAU;;gDAAmC;gDAAkB,iBAAiB,YAAa,iBAAiB,MAAM,cAAc,UAAW,WAAW;oDAAC;oDAAW;oDAAY;iDAAS,CAAC,QAAQ,CAAC,iBAAkB,iBAAiB,MAAM,cAAc,UAAW,WAAW,iBAAiB,YAAY,YAAY;;;;;;;;;;;;;;;;;;;sCAKnU,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;oCACP,IAAI,kBAAkB;wCACpB,QAAQ,GAAG,CAAC,mEAAmE;wCAC/E,kEAAkE;wCAClE,0FAA0F;wCAC1F,OAAO;oCACT,OAAO;wCACL,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,YAAa,iBAAiB,MAAM,cAAc,UAAW,UAAU;4CAAC;4CAAW;4CAAY;yCAAS,CAAC,QAAQ,CAAC,iBAAkB,iBAAiB,MAAM,cAAc,UAAW,UAAU,iBAAiB,YAAY,YAAY,QAAQ,eAAe,CAAC;oCAC3S;gCACF;gCACA,UAAU,CAAC;gCACX,WAAW,CAAC,2GAA2G,EAAE,mBAAmB,0CAA0C,+CAA+C,wBAAwB,CAAC;;oCAE7P,iBAAiB,0BAChB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;+CAC1B,iBAAiB,YAAY,cAAc,wBAC7C,6LAAC,uMAAA,CAAA,QAAS;wCAAC,MAAM;wCAAI,WAAU;;;;;+CAC7B;wCAAC;wCAAW;wCAAY;qCAAS,CAAC,QAAQ,CAAC,iBAAiB,cAAc,wBAC5E,6LAAC,uMAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;6DAE3B,6LAAC,uMAAA,CAAA,QAAS;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAEjC,6LAAC;wCAAK,WAAU;kDACb,iBAAiB,YAAY,iBAC5B,iBAAiB,WAAW,kBAC1B;4CAAC;4CAAW;4CAAY;yCAAS,CAAC,QAAQ,CAAC,gBAAgB,kBACzD,cAAc,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;8BASxD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;oCACC,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,MAAK;8CAEL,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAEP;;;;;;;sCAIR,6LAAC;4BACC,SAAS;4BACT,UAAU,CAAC;4BACX,WAAW,CAAC,6FAA6F,EAAE,mBACvG,2CACA,+CACD,wBAAwB,CAAC;;gCAC7B;8CAEC,6LAAC;oCACC,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,MAAK;8CAEL,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GAvNM;KAAA;uCAyNS", "debugId": null}}, {"offset": {"line": 3955, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3961, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/upload/ThumbnailSelection.tsx"], "sourcesContent": ["// components/upload/ThumbnailSelection.tsx\r\n'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport { X, Upload, Check } from 'lucide-react';\r\nimport { useUpload } from '../../contexts/UploadContexts';\r\n\r\ninterface ThumbnailSelectionProps {\r\n  videoFile: File;\r\n  onNext: (thumbnailFile?: File) => void;\r\n  onBack: () => void;\r\n  onClose: () => void;\r\n}\r\n\r\nconst ThumbnailSelection: React.FC<ThumbnailSelectionProps> = ({\r\n  videoFile,\r\n  onNext,\r\n  onBack,\r\n  onClose\r\n}) => {\r\n  const { setDuration } = useUpload();\r\n  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);\r\n  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);\r\n  const [videoThumbnails, setVideoThumbnails] = useState<string[]>([]);\r\n  const [selectedThumbnail, setSelectedThumbnail] = useState<number | null>(null);\r\n  const [isGenerating, setIsGenerating] = useState<boolean>(false);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  useEffect(() => {\r\n    // Generate thumbnails when the component mounts or when videoFile changes\r\n    if (videoFile) {\r\n      console.log('ThumbnailSelection: Processing video file:', videoFile.name);\r\n\r\n      // Check if the file is actually a video\r\n      if (!videoFile.type.startsWith('video/')) {\r\n        console.error('ThumbnailSelection: File is not a video:', videoFile.type);\r\n        alert('Error: The selected file is not a video. Please go back and select a video file.');\r\n        generateDefaultThumbnail();\r\n        setIsGenerating(false);\r\n        return;\r\n      }\r\n\r\n      // Clear any existing thumbnails\r\n      setVideoThumbnails([]);\r\n      setSelectedThumbnail(null);\r\n      setThumbnailPreview(null);\r\n      setThumbnailFile(null);\r\n\r\n      try {\r\n        generateVideoThumbnails();\r\n      } catch (error) {\r\n        console.error('Error generating thumbnails:', error);\r\n        generateDefaultThumbnail();\r\n        setIsGenerating(false);\r\n      }\r\n    } else {\r\n      console.error('ThumbnailSelection: No video file provided');\r\n      // Generate a default thumbnail instead of showing an error\r\n      generateDefaultThumbnail();\r\n      setIsGenerating(false);\r\n    }\r\n  }, [videoFile]);\r\n\r\n  const generateVideoThumbnails = () => {\r\n    console.log('Generating video thumbnails');\r\n    setIsGenerating(true);\r\n\r\n    // Check if videoFile exists\r\n    if (!videoFile) {\r\n      console.error('No video file provided to generateVideoThumbnails');\r\n      generateDefaultThumbnail();\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Create a video element to load the video\r\n      const video = document.createElement('video');\r\n      video.preload = 'metadata';\r\n\r\n      // Create object URL for the video file\r\n      const videoUrl = URL.createObjectURL(videoFile);\r\n      video.src = videoUrl;\r\n\r\n      // Set a timeout to handle cases where the video doesn't load\r\n      const timeoutId = setTimeout(() => {\r\n        console.error('Timeout waiting for video to load');\r\n        setIsGenerating(false);\r\n        URL.revokeObjectURL(videoUrl);\r\n        // Generate a default thumbnail if we can't load the video\r\n        generateDefaultThumbnail();\r\n      }, 10000); // 10 second timeout\r\n\r\n      video.onloadedmetadata = () => {\r\n        clearTimeout(timeoutId);\r\n\r\n        // Get video duration\r\n        const duration = video.duration;\r\n\r\n        // Set the duration in the upload context\r\n        setDuration(duration);\r\n        console.log(`Video duration set to ${duration} seconds`);\r\n\r\n        // Generate 3 thumbnails at different points in the video\r\n        const thumbnailPoints = [\r\n          duration * 0.25, // 25% of the way through\r\n          duration * 0.5,  // 50% of the way through\r\n          duration * 0.75  // 75% of the way through\r\n        ];\r\n\r\n        const thumbnails: string[] = [];\r\n        let loadedCount = 0;\r\n\r\n        // Set a timeout for each thumbnail generation\r\n        const seekTimeoutId = setTimeout(() => {\r\n          // Instead of warning, just log that we're using a fallback approach\r\n          console.log('Using fallback approach for thumbnail generation');\r\n          if (thumbnails.length > 0) {\r\n            // Use whatever thumbnails we have\r\n            setVideoThumbnails(thumbnails);\r\n          } else {\r\n            // Generate a default thumbnail\r\n            generateDefaultThumbnail();\r\n          }\r\n          setIsGenerating(false);\r\n          URL.revokeObjectURL(videoUrl);\r\n        }, 8000); // Increase timeout to 8 seconds for slower devices\r\n\r\n        thumbnailPoints.forEach((time, index) => {\r\n          try {\r\n            // Set the current time of the video\r\n            video.currentTime = time;\r\n\r\n            // When the time updates, capture the frame\r\n            video.onseeked = () => {\r\n              try {\r\n                // Create a canvas to draw the video frame\r\n                const canvas = document.createElement('canvas');\r\n                canvas.width = video.videoWidth || 320;\r\n                canvas.height = video.videoHeight || 240;\r\n\r\n                // Draw the video frame on the canvas\r\n                const ctx = canvas.getContext('2d');\r\n                if (ctx) {\r\n                  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\r\n\r\n                  // Convert canvas to data URL\r\n                  const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.7);\r\n                  thumbnails[index] = thumbnailUrl;\r\n\r\n                  loadedCount++;\r\n\r\n                  // When all thumbnails are generated\r\n                  if (loadedCount === thumbnailPoints.length) {\r\n                    clearTimeout(seekTimeoutId);\r\n                    setVideoThumbnails(thumbnails);\r\n                    setIsGenerating(false);\r\n\r\n                    // Revoke the object URL to free memory\r\n                    URL.revokeObjectURL(videoUrl);\r\n                  }\r\n                }\r\n              } catch (error) {\r\n                console.error('Error capturing video frame:', error);\r\n                // If we fail to capture a frame, still try to use what we have\r\n                if (thumbnails.length === 0) {\r\n                  generateDefaultThumbnail();\r\n                } else if (loadedCount === thumbnailPoints.length - 1) {\r\n                  // If this was the last thumbnail and it failed, use what we have\r\n                  setVideoThumbnails(thumbnails);\r\n                  setIsGenerating(false);\r\n                  URL.revokeObjectURL(videoUrl);\r\n                }\r\n              }\r\n            };\r\n          } catch (error) {\r\n            console.error('Error seeking video:', error);\r\n            loadedCount++; // Count this as processed even though it failed\r\n\r\n            // If all thumbnails have been attempted (even with errors)\r\n            if (loadedCount === thumbnailPoints.length) {\r\n              if (thumbnails.length > 0) {\r\n                setVideoThumbnails(thumbnails);\r\n              } else {\r\n                generateDefaultThumbnail();\r\n              }\r\n              setIsGenerating(false);\r\n              URL.revokeObjectURL(videoUrl);\r\n            }\r\n          }\r\n        });\r\n      };\r\n\r\n      video.onerror = (e) => {\r\n        clearTimeout(timeoutId);\r\n        console.error('Error loading video for thumbnail generation:', e);\r\n        setIsGenerating(false);\r\n        URL.revokeObjectURL(videoUrl);\r\n        // Generate a default thumbnail\r\n        generateDefaultThumbnail();\r\n      };\r\n    } catch (error) {\r\n      console.error('Error in thumbnail generation:', error);\r\n      setIsGenerating(false);\r\n      // Generate a default thumbnail\r\n      generateDefaultThumbnail();\r\n    }\r\n  };\r\n\r\n  // Generate a default thumbnail if we can't extract from the video\r\n  const generateDefaultThumbnail = () => {\r\n    try {\r\n      // Create a canvas with a placeholder image\r\n      const canvas = document.createElement('canvas');\r\n      canvas.width = 320;\r\n      canvas.height = 240;\r\n\r\n      const ctx = canvas.getContext('2d');\r\n      if (ctx) {\r\n        // Fill with a gradient background\r\n        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);\r\n        gradient.addColorStop(0, '#f0f0f0');\r\n        gradient.addColorStop(1, '#d0d0d0');\r\n        ctx.fillStyle = gradient;\r\n        ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n\r\n        // Add text\r\n        ctx.fillStyle = '#333';\r\n        ctx.font = 'bold 24px Arial';\r\n        ctx.textAlign = 'center';\r\n        ctx.fillText('Video Preview', canvas.width / 2, canvas.height / 2);\r\n\r\n        // Add file name if available\r\n        if (videoFile) {\r\n          ctx.font = '14px Arial';\r\n          let fileName = videoFile.name;\r\n          if (fileName.length > 25) {\r\n            fileName = fileName.substring(0, 22) + '...';\r\n          }\r\n          ctx.fillText(fileName, canvas.width / 2, canvas.height / 2 + 30);\r\n        } else {\r\n          ctx.font = '14px Arial';\r\n          ctx.fillText('No file selected', canvas.width / 2, canvas.height / 2 + 30);\r\n        }\r\n\r\n        // Convert to data URL\r\n        const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.8);\r\n        setVideoThumbnails([thumbnailUrl]);\r\n        setIsGenerating(false); // Make sure we're not stuck in generating state\r\n      }\r\n    } catch (error) {\r\n      console.error('Error generating default thumbnail:', error);\r\n      setIsGenerating(false); // Make sure we're not stuck in generating state\r\n      // Create a simple fallback thumbnail\r\n      setVideoThumbnails(['data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==']);\r\n    }\r\n  };\r\n\r\n  const handleThumbnailUpload = () => {\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files;\r\n    if (files && files.length > 0) {\r\n      const file = files[0];\r\n\r\n      // Check if the file is an image\r\n      if (!file.type.startsWith('image/')) {\r\n        alert('Please select an image file for the thumbnail.');\r\n        return;\r\n      }\r\n\r\n      setThumbnailFile(file);\r\n\r\n      // Create a preview of the uploaded thumbnail\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        if (e.target?.result) {\r\n          setThumbnailPreview(e.target.result as string);\r\n          setSelectedThumbnail(null); // Deselect any auto-generated thumbnails\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleSelectThumbnail = (index: number) => {\r\n    setSelectedThumbnail(index);\r\n    setThumbnailPreview(null); // Clear any uploaded thumbnail preview\r\n    setThumbnailFile(null);    // Clear any uploaded thumbnail file\r\n  };\r\n\r\n  const handleNext = async () => {\r\n    console.log('ThumbnailSelection: handleNext called');\r\n    try {\r\n      if (thumbnailFile) {\r\n        // User uploaded a custom thumbnail\r\n        console.log('Using user-uploaded thumbnail');\r\n        onNext(thumbnailFile);\r\n      } else if (selectedThumbnail !== null && videoThumbnails[selectedThumbnail]) {\r\n        // User selected an auto-generated thumbnail\r\n        // Convert the data URL to a File object\r\n        console.log('Using selected auto-generated thumbnail');\r\n        try {\r\n          const dataUrl = videoThumbnails[selectedThumbnail];\r\n          const res = await fetch(dataUrl);\r\n          const blob = await res.blob();\r\n          const file = new File([blob], `thumbnail-${Date.now()}.jpg`, { type: 'image/jpeg' });\r\n          onNext(file);\r\n        } catch (error) {\r\n          console.error('Error converting selected thumbnail to file:', error);\r\n          // Try to use the first thumbnail as fallback\r\n          if (videoThumbnails.length > 0 && selectedThumbnail !== 0) {\r\n            try {\r\n              console.log('Using fallback thumbnail');\r\n              const dataUrl = videoThumbnails[0];\r\n              const res = await fetch(dataUrl);\r\n              const blob = await res.blob();\r\n              const file = new File([blob], `thumbnail-fallback-${Date.now()}.jpg`, { type: 'image/jpeg' });\r\n              onNext(file);\r\n              return;\r\n            } catch (innerError) {\r\n              console.error('Error converting fallback thumbnail to file:', innerError);\r\n              console.log('Proceeding without thumbnail due to fallback error');\r\n            }\r\n          }\r\n          console.log('Proceeding without thumbnail');\r\n          onNext(); // Proceed without thumbnail\r\n        }\r\n      } else {\r\n        // No thumbnail selected, use the first auto-generated one if available\r\n        if (videoThumbnails.length > 0) {\r\n          try {\r\n            console.log('Using first auto-generated thumbnail');\r\n            const dataUrl = videoThumbnails[0];\r\n            const res = await fetch(dataUrl);\r\n            const blob = await res.blob();\r\n            const file = new File([blob], `thumbnail-auto-${Date.now()}.jpg`, { type: 'image/jpeg' });\r\n            onNext(file);\r\n          } catch (error) {\r\n            console.error('Error converting auto thumbnail to file:', error);\r\n            console.log('Proceeding without thumbnail due to auto-thumbnail error');\r\n            onNext(); // Proceed without thumbnail\r\n          }\r\n        } else {\r\n          // No thumbnails available\r\n          console.log('No thumbnails available, proceeding without thumbnail');\r\n          onNext();\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Unexpected error in handleNext:', error);\r\n      console.log('Proceeding without thumbnail due to unexpected error');\r\n      onNext(); // Proceed without thumbnail in case of any error\r\n    }\r\n  };\r\n\r\n  // If there's no video file, show a message but don't break the flow\r\n  useEffect(() => {\r\n    if (!videoFile) {\r\n      console.error('ThumbnailSelection: No video file provided');\r\n      // Generate a default thumbnail instead of showing an error\r\n      generateDefaultThumbnail();\r\n      setIsGenerating(false);\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]\">\r\n        {/* Close button */}\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-4 right-4 text-gray-800 hover:text-red-600\"\r\n        >\r\n          <X size={24} />\r\n        </button>\r\n\r\n        {/* Header */}\r\n        <div className=\"flex items-center mb-6\">\r\n          <div className=\"flex items-center mr-2\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={32}\r\n              height={32}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n          <h2 className=\"text-xl font-bold\">Choose Thumbnail</h2>\r\n          <div className=\"ml-2\">\r\n            <Image\r\n              src=\"/pics/umoments.png\"\r\n              alt=\"Moments\"\r\n              width={20}\r\n              height={20}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <p className=\"text-base mb-6\">Select a thumbnail for your video or upload your own</p>\r\n\r\n        {/* Thumbnail options */}\r\n        <div className=\"mb-6\">\r\n          {isGenerating ? (\r\n            <div className=\"flex justify-center items-center h-40\">\r\n              <p>Generating thumbnails from your video...</p>\r\n            </div>\r\n          ) : !videoFile ? (\r\n            <div className=\"flex justify-center items-center h-40 bg-gray-100 rounded-lg\">\r\n              <div className=\"text-center\">\r\n                <p className=\"text-gray-600 mb-2\">No video file available</p>\r\n                <button\r\n                  onClick={onBack}\r\n                  className=\"px-4 py-2 bg-red-600 text-white rounded-md\"\r\n                >\r\n                  Go Back to Select Video\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div>\r\n              {/* Auto-generated thumbnails */}\r\n              {videoThumbnails.length > 0 && (\r\n                <div className=\"mb-4\">\r\n                  <h3 className=\"text-lg font-medium mb-2\">Auto-generated thumbnails</h3>\r\n                  <div className=\"grid grid-cols-3 gap-4\">\r\n                    {videoThumbnails.map((thumbnail, index) => (\r\n                      <div\r\n                        key={index}\r\n                        className={`relative cursor-pointer border-2 rounded-lg overflow-hidden ${\r\n                          selectedThumbnail === index ? 'border-red-600' : 'border-gray-200'\r\n                        }`}\r\n                        onClick={() => handleSelectThumbnail(index)}\r\n                      >\r\n                        <img\r\n                          src={thumbnail}\r\n                          alt={`Thumbnail ${index + 1}`}\r\n                          className=\"w-full h-32 object-cover\"\r\n                        />\r\n                        {selectedThumbnail === index && (\r\n                          <div className=\"absolute top-2 right-2 bg-red-600 rounded-full p-1\">\r\n                            <Check size={16} className=\"text-white\" />\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Custom thumbnail upload */}\r\n              <div className=\"mt-6\">\r\n                <h3 className=\"text-lg font-medium mb-2\">Upload custom thumbnail</h3>\r\n                <div className=\"flex items-center\">\r\n                  {thumbnailPreview ? (\r\n                    <div className=\"relative mr-4 border-2 border-red-600 rounded-lg overflow-hidden\">\r\n                      <img\r\n                        src={thumbnailPreview}\r\n                        alt=\"Custom thumbnail\"\r\n                        className=\"w-32 h-32 object-cover\"\r\n                      />\r\n                      <div className=\"absolute top-2 right-2 bg-red-600 rounded-full p-1\">\r\n                        <Check size={16} className=\"text-white\" />\r\n                      </div>\r\n                    </div>\r\n                  ) : null}\r\n\r\n                  <button\r\n                    onClick={handleThumbnailUpload}\r\n                    className=\"flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n                  >\r\n                    <Upload size={18} className=\"mr-2\" />\r\n                    Upload Image\r\n                  </button>\r\n\r\n                  <input\r\n                    type=\"file\"\r\n                    ref={fileInputRef}\r\n                    onChange={handleFileChange}\r\n                    accept=\"image/*\"\r\n                    className=\"hidden\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Navigation buttons */}\r\n        <div className=\"flex justify-between mt-8\">\r\n          <button\r\n            onClick={onBack}\r\n            className=\"flex items-center justify-center px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-5 w-5 mr-1\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Back\r\n          </button>\r\n\r\n          <button\r\n            onClick={handleNext}\r\n            className=\"flex items-center justify-center px-6 py-2 rounded-md bg-red-600 text-white hover:bg-red-700 transition duration-200\"\r\n          >\r\n            Next\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-5 w-5 ml-1\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ThumbnailSelection;\r\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;AAG3C;AACA;AACA;AAAA;AAAA;AACA;;;AALA;;;;;AAcA,MAAM,qBAAwD,CAAC,EAC7D,SAAS,EACT,MAAM,EACN,MAAM,EACN,OAAO,EACR;;IACC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,0EAA0E;YAC1E,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,8CAA8C,UAAU,IAAI;gBAExE,wCAAwC;gBACxC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW;oBACxC,QAAQ,KAAK,CAAC,4CAA4C,UAAU,IAAI;oBACxE,MAAM;oBACN;oBACA,gBAAgB;oBAChB;gBACF;gBAEA,gCAAgC;gBAChC,mBAAmB,EAAE;gBACrB,qBAAqB;gBACrB,oBAAoB;gBACpB,iBAAiB;gBAEjB,IAAI;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C;oBACA,gBAAgB;gBAClB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,2DAA2D;gBAC3D;gBACA,gBAAgB;YAClB;QACF;uCAAG;QAAC;KAAU;IAEd,MAAM,0BAA0B;QAC9B,QAAQ,GAAG,CAAC;QACZ,gBAAgB;QAEhB,4BAA4B;QAC5B,IAAI,CAAC,WAAW;YACd,QAAQ,KAAK,CAAC;YACd;YACA;QACF;QAEA,IAAI;YACF,2CAA2C;YAC3C,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,OAAO,GAAG;YAEhB,uCAAuC;YACvC,MAAM,WAAW,IAAI,eAAe,CAAC;YACrC,MAAM,GAAG,GAAG;YAEZ,6DAA6D;YAC7D,MAAM,YAAY,WAAW;gBAC3B,QAAQ,KAAK,CAAC;gBACd,gBAAgB;gBAChB,IAAI,eAAe,CAAC;gBACpB,0DAA0D;gBAC1D;YACF,GAAG,QAAQ,oBAAoB;YAE/B,MAAM,gBAAgB,GAAG;gBACvB,aAAa;gBAEb,qBAAqB;gBACrB,MAAM,WAAW,MAAM,QAAQ;gBAE/B,yCAAyC;gBACzC,YAAY;gBACZ,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,SAAS,QAAQ,CAAC;gBAEvD,yDAAyD;gBACzD,MAAM,kBAAkB;oBACtB,WAAW;oBACX,WAAW;oBACX,WAAW,KAAM,yBAAyB;iBAC3C;gBAED,MAAM,aAAuB,EAAE;gBAC/B,IAAI,cAAc;gBAElB,8CAA8C;gBAC9C,MAAM,gBAAgB,WAAW;oBAC/B,oEAAoE;oBACpE,QAAQ,GAAG,CAAC;oBACZ,IAAI,WAAW,MAAM,GAAG,GAAG;wBACzB,kCAAkC;wBAClC,mBAAmB;oBACrB,OAAO;wBACL,+BAA+B;wBAC/B;oBACF;oBACA,gBAAgB;oBAChB,IAAI,eAAe,CAAC;gBACtB,GAAG,OAAO,mDAAmD;gBAE7D,gBAAgB,OAAO,CAAC,CAAC,MAAM;oBAC7B,IAAI;wBACF,oCAAoC;wBACpC,MAAM,WAAW,GAAG;wBAEpB,2CAA2C;wBAC3C,MAAM,QAAQ,GAAG;4BACf,IAAI;gCACF,0CAA0C;gCAC1C,MAAM,SAAS,SAAS,aAAa,CAAC;gCACtC,OAAO,KAAK,GAAG,MAAM,UAAU,IAAI;gCACnC,OAAO,MAAM,GAAG,MAAM,WAAW,IAAI;gCAErC,qCAAqC;gCACrC,MAAM,MAAM,OAAO,UAAU,CAAC;gCAC9B,IAAI,KAAK;oCACP,IAAI,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oCAEtD,6BAA6B;oCAC7B,MAAM,eAAe,OAAO,SAAS,CAAC,cAAc;oCACpD,UAAU,CAAC,MAAM,GAAG;oCAEpB;oCAEA,oCAAoC;oCACpC,IAAI,gBAAgB,gBAAgB,MAAM,EAAE;wCAC1C,aAAa;wCACb,mBAAmB;wCACnB,gBAAgB;wCAEhB,uCAAuC;wCACvC,IAAI,eAAe,CAAC;oCACtB;gCACF;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,gCAAgC;gCAC9C,+DAA+D;gCAC/D,IAAI,WAAW,MAAM,KAAK,GAAG;oCAC3B;gCACF,OAAO,IAAI,gBAAgB,gBAAgB,MAAM,GAAG,GAAG;oCACrD,iEAAiE;oCACjE,mBAAmB;oCACnB,gBAAgB;oCAChB,IAAI,eAAe,CAAC;gCACtB;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,eAAe,gDAAgD;wBAE/D,2DAA2D;wBAC3D,IAAI,gBAAgB,gBAAgB,MAAM,EAAE;4BAC1C,IAAI,WAAW,MAAM,GAAG,GAAG;gCACzB,mBAAmB;4BACrB,OAAO;gCACL;4BACF;4BACA,gBAAgB;4BAChB,IAAI,eAAe,CAAC;wBACtB;oBACF;gBACF;YACF;YAEA,MAAM,OAAO,GAAG,CAAC;gBACf,aAAa;gBACb,QAAQ,KAAK,CAAC,iDAAiD;gBAC/D,gBAAgB;gBAChB,IAAI,eAAe,CAAC;gBACpB,+BAA+B;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,gBAAgB;YAChB,+BAA+B;YAC/B;QACF;IACF;IAEA,kEAAkE;IAClE,MAAM,2BAA2B;QAC/B,IAAI;YACF,2CAA2C;YAC3C,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,KAAK,GAAG;YACf,OAAO,MAAM,GAAG;YAEhB,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,KAAK;gBACP,kCAAkC;gBAClC,MAAM,WAAW,IAAI,oBAAoB,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBAC3E,SAAS,YAAY,CAAC,GAAG;gBACzB,SAAS,YAAY,CAAC,GAAG;gBACzB,IAAI,SAAS,GAAG;gBAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBAE9C,WAAW;gBACX,IAAI,SAAS,GAAG;gBAChB,IAAI,IAAI,GAAG;gBACX,IAAI,SAAS,GAAG;gBAChB,IAAI,QAAQ,CAAC,iBAAiB,OAAO,KAAK,GAAG,GAAG,OAAO,MAAM,GAAG;gBAEhE,6BAA6B;gBAC7B,IAAI,WAAW;oBACb,IAAI,IAAI,GAAG;oBACX,IAAI,WAAW,UAAU,IAAI;oBAC7B,IAAI,SAAS,MAAM,GAAG,IAAI;wBACxB,WAAW,SAAS,SAAS,CAAC,GAAG,MAAM;oBACzC;oBACA,IAAI,QAAQ,CAAC,UAAU,OAAO,KAAK,GAAG,GAAG,OAAO,MAAM,GAAG,IAAI;gBAC/D,OAAO;oBACL,IAAI,IAAI,GAAG;oBACX,IAAI,QAAQ,CAAC,oBAAoB,OAAO,KAAK,GAAG,GAAG,OAAO,MAAM,GAAG,IAAI;gBACzE;gBAEA,sBAAsB;gBACtB,MAAM,eAAe,OAAO,SAAS,CAAC,cAAc;gBACpD,mBAAmB;oBAAC;iBAAa;gBACjC,gBAAgB,QAAQ,gDAAgD;YAC1E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,gBAAgB,QAAQ,gDAAgD;YACxE,qCAAqC;YACrC,mBAAmB;gBAAC;aAAyH;QAC/I;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,gCAAgC;YAChC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,MAAM;gBACN;YACF;YAEA,iBAAiB;YAEjB,6CAA6C;YAC7C,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,IAAI,EAAE,MAAM,EAAE,QAAQ;oBACpB,oBAAoB,EAAE,MAAM,CAAC,MAAM;oBACnC,qBAAqB,OAAO,yCAAyC;gBACvE;YACF;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,qBAAqB;QACrB,oBAAoB,OAAO,uCAAuC;QAClE,iBAAiB,OAAU,oCAAoC;IACjE;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,IAAI,eAAe;gBACjB,mCAAmC;gBACnC,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT,OAAO,IAAI,sBAAsB,QAAQ,eAAe,CAAC,kBAAkB,EAAE;gBAC3E,4CAA4C;gBAC5C,wCAAwC;gBACxC,QAAQ,GAAG,CAAC;gBACZ,IAAI;oBACF,MAAM,UAAU,eAAe,CAAC,kBAAkB;oBAClD,MAAM,MAAM,MAAM,MAAM;oBACxB,MAAM,OAAO,MAAM,IAAI,IAAI;oBAC3B,MAAM,OAAO,IAAI,KAAK;wBAAC;qBAAK,EAAE,CAAC,UAAU,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;wBAAE,MAAM;oBAAa;oBAClF,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gDAAgD;oBAC9D,6CAA6C;oBAC7C,IAAI,gBAAgB,MAAM,GAAG,KAAK,sBAAsB,GAAG;wBACzD,IAAI;4BACF,QAAQ,GAAG,CAAC;4BACZ,MAAM,UAAU,eAAe,CAAC,EAAE;4BAClC,MAAM,MAAM,MAAM,MAAM;4BACxB,MAAM,OAAO,MAAM,IAAI,IAAI;4BAC3B,MAAM,OAAO,IAAI,KAAK;gCAAC;6BAAK,EAAE,CAAC,mBAAmB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;gCAAE,MAAM;4BAAa;4BAC3F,OAAO;4BACP;wBACF,EAAE,OAAO,YAAY;4BACnB,QAAQ,KAAK,CAAC,gDAAgD;4BAC9D,QAAQ,GAAG,CAAC;wBACd;oBACF;oBACA,QAAQ,GAAG,CAAC;oBACZ,UAAU,4BAA4B;gBACxC;YACF,OAAO;gBACL,uEAAuE;gBACvE,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,IAAI;wBACF,QAAQ,GAAG,CAAC;wBACZ,MAAM,UAAU,eAAe,CAAC,EAAE;wBAClC,MAAM,MAAM,MAAM,MAAM;wBACxB,MAAM,OAAO,MAAM,IAAI,IAAI;wBAC3B,MAAM,OAAO,IAAI,KAAK;4BAAC;yBAAK,EAAE,CAAC,eAAe,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;4BAAE,MAAM;wBAAa;wBACvF,OAAO;oBACT,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4CAA4C;wBAC1D,QAAQ,GAAG,CAAC;wBACZ,UAAU,4BAA4B;oBACxC;gBACF,OAAO;oBACL,0BAA0B;oBAC1B,QAAQ,GAAG,CAAC;oBACZ;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,QAAQ,GAAG,CAAC;YACZ,UAAU,iDAAiD;QAC7D;IACF;IAEA,oEAAoE;IACpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,WAAW;gBACd,QAAQ,KAAK,CAAC;gBACd,2DAA2D;gBAC3D;gBACA,gBAAgB;YAClB;QACF;uCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAE,WAAU;8BAAiB;;;;;;8BAG9B,6LAAC;oBAAI,WAAU;8BACZ,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAE;;;;;;;;;;+BAEH,CAAC,0BACH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;6CAML,6LAAC;;4BAEE,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,WAAW,sBAC/B,6LAAC;gDAEC,WAAW,CAAC,4DAA4D,EACtE,sBAAsB,QAAQ,mBAAmB,mBACjD;gDACF,SAAS,IAAM,sBAAsB;;kEAErC,6LAAC;wDACC,KAAK;wDACL,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG;wDAC7B,WAAU;;;;;;oDAEX,sBAAsB,uBACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;;+CAb1B;;;;;;;;;;;;;;;;0CAuBf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;;4CACZ,iCACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,KAAK;wDACL,KAAI;wDACJ,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;;;;;;uDAG7B;0DAEJ,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,yMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAAS;;;;;;;0DAIvC,6LAAC;gDACC,MAAK;gDACL,KAAK;gDACL,UAAU;gDACV,QAAO;gDACP,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;oCACC,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,MAAK;8CAEL,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAEP;;;;;;;sCAIR,6LAAC;4BACC,SAAS;4BACT,WAAU;;gCACX;8CAEC,6LAAC;oCACC,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,MAAK;8CAEL,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GAzgBM;;QAMoB,8HAAA,CAAA,YAAS;;;KAN7B;uCA2gBS", "debugId": null}}, {"offset": {"line": 4695, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4701, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/upload/PersonalDetails.tsx"], "sourcesContent": ["// components/upload/PersonalDetails.tsx\r\n'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport { X, ChevronDown, Loader, MapPin } from 'lucide-react';\r\n\r\ninterface City {\r\n  id: number;\r\n  name: string;\r\n  region?: string;\r\n  country?: string;\r\n  description?: string;\r\n}\r\n\r\ninterface PersonalDetailsProps {\r\n  onNext: (details: {\r\n    caption: string;\r\n    lifePartner: string;\r\n    weddingStyle: string;\r\n    place: string;\r\n  }) => void;\r\n  onBack: () => void;\r\n  onClose: () => void;\r\n  previewImage: string | null; // URL of the preview image\r\n  videoFile?: File | null; // Video file for preview\r\n  mediaType?: 'photo' | 'video'; // Type of media being uploaded\r\n  initialDetails?: {\r\n    caption: string;\r\n    lifePartner: string;\r\n    weddingStyle: string;\r\n    place: string;\r\n  }; // Initial details for persistence between screens\r\n}\r\n\r\nconst PersonalDetails: React.FC<PersonalDetailsProps> = ({\r\n  onNext,\r\n  onBack,\r\n  onClose,\r\n  previewImage,\r\n  videoFile,\r\n  mediaType = 'photo',\r\n  initialDetails\r\n}) => {\r\n  const [videoUrl, setVideoUrl] = useState<string | null>(null);\r\n  const [details, setDetails] = useState({\r\n    caption: initialDetails?.caption || '',\r\n    lifePartner: initialDetails?.lifePartner || '',\r\n    weddingStyle: initialDetails?.weddingStyle || '',\r\n    place: initialDetails?.place || ''\r\n  });\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  // Log the initial details for debugging\r\n  useEffect(() => {\r\n    console.log('PersonalDetails component initialized with:', {\r\n      initialDetails,\r\n      currentDetails: details\r\n    });\r\n  }, []);\r\n\r\n  // City search states\r\n  const [showCityDropdown, setShowCityDropdown] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [cities, setCities] = useState<City[]>([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const searchTimeout = useRef<NodeJS.Timeout | null>(null);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Default cities to show when no search is performed\r\n  const defaultCities: City[] = [\r\n    { id: 1, name: \"Mumbai\", description: \"Financial Capital\" },\r\n    { id: 2, name: \"Delhi\", description: \"National Capital\" },\r\n    { id: 3, name: \"Bangalore\", description: \"IT Hub\" },\r\n    { id: 4, name: \"Hyderabad\", description: \"Pearl City\" },\r\n    { id: 5, name: \"Chennai\", description: \"Gateway of South India\" },\r\n    { id: 6, name: \"Kolkata\", description: \"City of Joy\" },\r\n    { id: 7, name: \"Ahmedabad\", description: \"Manchester of India\" },\r\n    { id: 8, name: \"Pune\", description: \"Oxford of the East\" },\r\n  ];\r\n\r\n  // Use React.useCallback to memoize the handler function\r\n  const handleInputChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Clear error for this field when user types\r\n    if (errors[name]) {\r\n      setErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[name];\r\n        return newErrors;\r\n      });\r\n    }\r\n\r\n    if (name === 'place') {\r\n      setSearchTerm(value);\r\n      setDetails(prev => ({ ...prev, place: value }));\r\n\r\n      // Only show dropdown when input is focused and has value\r\n      if (value.length > 0) {\r\n        setShowCityDropdown(true);\r\n\r\n        // Debounce search\r\n        if (searchTimeout.current) {\r\n          clearTimeout(searchTimeout.current);\r\n        }\r\n\r\n        searchTimeout.current = setTimeout(() => {\r\n          searchCities(value);\r\n        }, 500);\r\n      } else {\r\n        setCities(defaultCities);\r\n      }\r\n    } else {\r\n      setDetails(prev => ({\r\n        ...prev,\r\n        [name]: value\r\n      }));\r\n    }\r\n  }, [errors, defaultCities]); // Remove searchCities from dependencies to avoid circular dependency\r\n\r\n  // Function to search cities using API - memoized with useCallback\r\n  const searchCities = React.useCallback(async (query: string) => {\r\n    if (!query) {\r\n      setCities(defaultCities);\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `https://wft-geo-db.p.rapidapi.com/v1/geo/cities?namePrefix=${query}&limit=10&countryIds=IN`,\r\n        {\r\n          method: 'GET',\r\n          headers: {\r\n            'X-RapidAPI-Key': '**************************************************',\r\n            'X-RapidAPI-Host': 'wft-geo-db.p.rapidapi.com'\r\n          }\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        console.warn('Failed to fetch cities from API, using default cities');\r\n        setCities(defaultCities);\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      const formattedCities: City[] = data.data.map((city: any) => ({\r\n        id: city.id,\r\n        name: city.name,\r\n        region: city.region,\r\n        country: city.country,\r\n        description: city.region || 'India'\r\n      }));\r\n\r\n      setCities(formattedCities);\r\n    } catch (err) {\r\n      console.warn('Error fetching cities, using default cities:', err);\r\n      setCities(defaultCities);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [defaultCities]); // Add defaultCities as a dependency\r\n\r\n  const selectCity = React.useCallback((cityName: string) => {\r\n    setDetails(prev => ({ ...prev, place: cityName }));\r\n    setSearchTerm(cityName); // Update the search term as well\r\n    setShowCityDropdown(false);\r\n  }, []);\r\n\r\n  // Handle click outside to close dropdown\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setShowCityDropdown(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  // Initialize with default cities\r\n  useEffect(() => {\r\n    setCities(defaultCities);\r\n\r\n    // Cleanup timeout on unmount\r\n    return () => {\r\n      if (searchTimeout.current) {\r\n        clearTimeout(searchTimeout.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Create and cleanup video object URL - only when videoFile or mediaType changes\r\n  useEffect(() => {\r\n    console.log('PersonalDetails: videoFile changed:', videoFile?.name);\r\n    console.log('PersonalDetails: mediaType:', mediaType);\r\n\r\n    // Only create a video URL if we have a video file AND the media type is video\r\n    // AND we don't already have a URL for this file\r\n    if (videoFile && mediaType === 'video' && (!videoUrl || !videoUrl.includes(videoFile.name))) {\r\n      try {\r\n        // Clear previous video URL if it exists\r\n        if (videoUrl) {\r\n          URL.revokeObjectURL(videoUrl);\r\n        }\r\n\r\n        const url = URL.createObjectURL(videoFile);\r\n        console.log('PersonalDetails: Created new video URL for:', videoFile.name);\r\n        setVideoUrl(url);\r\n\r\n        // Cleanup function\r\n        return () => {\r\n          URL.revokeObjectURL(url);\r\n        };\r\n      } catch (error) {\r\n        console.error('Error creating object URL:', error);\r\n      }\r\n    } else if (videoFile && mediaType !== 'video') {\r\n      console.log('PersonalDetails: Not creating video URL because mediaType is not video');\r\n    } else if (!videoFile && mediaType === 'video') {\r\n      console.log('PersonalDetails: Not creating video URL because videoFile is null');\r\n    }\r\n\r\n    return () => {\r\n      // Only revoke the URL when the component unmounts or when videoFile/mediaType changes\r\n      // Not on every re-render\r\n    };\r\n  }, [videoFile, mediaType]); // Only depend on videoFile and mediaType, not videoUrl\r\n\r\n  const handleSubmit = React.useCallback(() => {\r\n    // Validate form\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    // Caption/Title is required\r\n    if (!details.caption.trim()) {\r\n      newErrors.caption = 'Please provide a title for your upload';\r\n    }\r\n\r\n    // Wedding Style is required\r\n    if (!details.weddingStyle.trim()) {\r\n      newErrors.weddingStyle = 'Please provide a wedding style';\r\n    }\r\n\r\n    // Place is required\r\n    if (!details.place.trim()) {\r\n      newErrors.place = 'Please provide a location';\r\n    }\r\n\r\n    // Set errors if any\r\n    setErrors(newErrors);\r\n\r\n    // Only proceed if there are no errors\r\n    if (Object.keys(newErrors).length === 0) {\r\n      onNext(details);\r\n    }\r\n  }, [details, onNext]);\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]\">\r\n        {/* Close button */}\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-4 right-4 text-gray-800 hover:text-red-600\"\r\n        >\r\n          <X size={24} />\r\n        </button>\r\n\r\n        {/* Header */}\r\n        <div className=\"flex items-center mb-6\">\r\n          <div className=\"flex items-center mr-2\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={32}\r\n              height={32}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n          <h2 className=\"text-xl font-bold\">Details</h2>\r\n          <div className=\"ml-2\">\r\n            <Image\r\n              src=\"/pics/umoments.png\"\r\n              alt=\"Moments\"\r\n              width={20}\r\n              height={20}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center mb-6\">\r\n          <Image\r\n            src=\"/pics/user-profile.png\"\r\n            alt=\"User\"\r\n            width={24}\r\n            height={24}\r\n            className=\"object-cover mr-2\"\r\n          />\r\n          <p className=\"text-base\">Add Personal Details</p>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6\">\r\n          {/* Left side - Form fields */}\r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <input\r\n                type=\"text\"\r\n                name=\"caption\"\r\n                value={details.caption}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Add Title (required)\"\r\n                className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.caption ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.caption && (\r\n                <p className=\"text-red-500 text-sm mt-1\">{errors.caption}</p>\r\n              )}\r\n            </div>\r\n\r\n            <div>\r\n              <input\r\n                type=\"text\"\r\n                name=\"lifePartner\"\r\n                value={details.lifePartner}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Tag Life Partner\"\r\n                className=\"w-full p-4 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500\"\r\n              />\r\n            </div>\r\n\r\n            <div>\r\n              <input\r\n                type=\"text\"\r\n                name=\"weddingStyle\"\r\n                value={details.weddingStyle}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Add style of wedding (required)\"\r\n                className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.weddingStyle ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.weddingStyle && (\r\n                <p className=\"text-red-500 text-sm mt-1\">{errors.weddingStyle}</p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"relative\" ref={dropdownRef}>\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"place\"\r\n                  value={searchTerm}\r\n                  onChange={handleInputChange}\r\n                  onFocus={() => searchTerm.length > 0 && setShowCityDropdown(true)}\r\n                  placeholder=\"Place (required)\"\r\n                  className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 pr-10 ${errors.place ? 'border-red-500' : 'border-gray-300'}`}\r\n                />\r\n                <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\r\n                  {isLoading ? (\r\n                    <Loader size={18} className=\"animate-spin text-gray-400\" />\r\n                  ) : (\r\n                    <MapPin size={18} className=\"text-gray-500\" />\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {errors.place && (\r\n                <p className=\"text-red-500 text-sm mt-1\">{errors.place}</p>\r\n              )}\r\n\r\n              {/* City dropdown */}\r\n              {showCityDropdown && (\r\n                <div className=\"absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg border border-gray-200 max-h-60 overflow-y-auto\">\r\n                  {isLoading ? (\r\n                    <div className=\"px-4 py-3 flex items-center justify-center\">\r\n                      <Loader size={18} className=\"animate-spin text-gray-400 mr-2\" />\r\n                      <span className=\"text-gray-500\">Searching...</span>\r\n                    </div>\r\n                  ) : cities.length === 0 ? (\r\n                    <div className=\"px-4 py-3 text-gray-500\">No cities found</div>\r\n                  ) : (\r\n                    cities.map((city) => (\r\n                      <div\r\n                        key={city.id}\r\n                        className=\"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\"\r\n                        onClick={() => selectCity(city.name)}\r\n                      >\r\n                        <div className=\"flex flex-col\">\r\n                          <span className=\"font-medium text-gray-800\">{city.name}</span>\r\n                          <span className=\"text-sm text-gray-500\">\r\n                            {city.description || (city.region ? `${city.region}, ${city.country || 'India'}` : 'India')}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right side - Preview image or video */}\r\n          <div className=\"rounded-lg overflow-hidden bg-gray-100 h-60\">\r\n            {mediaType === 'photo' && previewImage ? (\r\n              <div className=\"w-full h-full relative\">\r\n                <img\r\n                  src={previewImage}\r\n                  alt=\"Preview\"\r\n                  className=\"w-full h-full object-cover\"\r\n                />\r\n                <div className=\"absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs\">\r\n                  Photo\r\n                </div>\r\n              </div>\r\n            ) : mediaType === 'video' && videoUrl ? (\r\n              <div className=\"w-full h-full relative\">\r\n                <video\r\n                  key={`video-${videoFile?.name || 'video'}`} // Use stable key based only on file name\r\n                  src={videoUrl}\r\n                  controls\r\n                  className=\"w-full h-full object-cover\"\r\n                  onError={(e) => {\r\n                    console.error('Error loading video:', e);\r\n                  }}\r\n                  autoPlay\r\n                  muted\r\n                  loop\r\n                />\r\n                <div className=\"absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs\">\r\n                  Video\r\n                </div>\r\n                {/* Fallback if video fails to load */}\r\n                <div className=\"absolute inset-0 flex items-center justify-center pointer-events-none opacity-0\">\r\n                  <div className=\"bg-gray-200 p-2 rounded text-gray-600\">\r\n                    Video Preview\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"w-full h-full flex items-center justify-center text-gray-400\">\r\n                No media selected\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation buttons */}\r\n        <div className=\"flex justify-between mt-8\">\r\n          <button\r\n            onClick={onBack}\r\n            className=\"flex items-center justify-center px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-5 w-5 mr-1\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Back\r\n          </button>\r\n\r\n          <button\r\n            onClick={handleSubmit}\r\n            className=\"flex items-center justify-center px-6 py-2 rounded-md bg-red-600 text-white hover:bg-red-700 transition duration-200\"\r\n          >\r\n            Next\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-5 w-5 ml-1\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PersonalDetails;"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AAGxC;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAkCA,MAAM,kBAAkD,CAAC,EACvD,MAAM,EACN,MAAM,EACN,OAAO,EACP,YAAY,EACZ,SAAS,EACT,YAAY,OAAO,EACnB,cAAc,EACf;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,SAAS,gBAAgB,WAAW;QACpC,aAAa,gBAAgB,eAAe;QAC5C,cAAc,gBAAgB,gBAAgB;QAC9C,OAAO,gBAAgB,SAAS;IAClC;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,QAAQ,GAAG,CAAC,+CAA+C;gBACzD;gBACA,gBAAgB;YAClB;QACF;oCAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,qDAAqD;IACrD,MAAM,gBAAwB;QAC5B;YAAE,IAAI;YAAG,MAAM;YAAU,aAAa;QAAoB;QAC1D;YAAE,IAAI;YAAG,MAAM;YAAS,aAAa;QAAmB;QACxD;YAAE,IAAI;YAAG,MAAM;YAAa,aAAa;QAAS;QAClD;YAAE,IAAI;YAAG,MAAM;YAAa,aAAa;QAAa;QACtD;YAAE,IAAI;YAAG,MAAM;YAAW,aAAa;QAAyB;QAChE;YAAE,IAAI;YAAG,MAAM;YAAW,aAAa;QAAc;QACrD;YAAE,IAAI;YAAG,MAAM;YAAa,aAAa;QAAsB;QAC/D;YAAE,IAAI;YAAG,MAAM;YAAQ,aAAa;QAAqB;KAC1D;IAED,wDAAwD;IACxD,MAAM,oBAAoB,6JAAA,CAAA,UAAK,CAAC,WAAW;0DAAC,CAAC;YAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;YAEhC,6CAA6C;YAC7C,IAAI,MAAM,CAAC,KAAK,EAAE;gBAChB;sEAAU,CAAA;wBACR,MAAM,YAAY;4BAAE,GAAG,IAAI;wBAAC;wBAC5B,OAAO,SAAS,CAAC,KAAK;wBACtB,OAAO;oBACT;;YACF;YAEA,IAAI,SAAS,SAAS;gBACpB,cAAc;gBACd;sEAAW,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,OAAO;wBAAM,CAAC;;gBAE7C,yDAAyD;gBACzD,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,oBAAoB;oBAEpB,kBAAkB;oBAClB,IAAI,cAAc,OAAO,EAAE;wBACzB,aAAa,cAAc,OAAO;oBACpC;oBAEA,cAAc,OAAO,GAAG;0EAAW;4BACjC,aAAa;wBACf;yEAAG;gBACL,OAAO;oBACL,UAAU;gBACZ;YACF,OAAO;gBACL;sEAAW,CAAA,OAAQ,CAAC;4BAClB,GAAG,IAAI;4BACP,CAAC,KAAK,EAAE;wBACV,CAAC;;YACH;QACF;yDAAG;QAAC;QAAQ;KAAc,GAAG,qEAAqE;IAElG,kEAAkE;IAClE,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,WAAW;qDAAC,OAAO;YAC5C,IAAI,CAAC,OAAO;gBACV,UAAU;gBACV,aAAa;gBACb;YACF;YAEA,aAAa;YAEb,IAAI;gBACF,MAAM,WAAW,MAAM,MACrB,CAAC,2DAA2D,EAAE,MAAM,uBAAuB,CAAC,EAC5F;oBACE,QAAQ;oBACR,SAAS;wBACP,kBAAkB;wBAClB,mBAAmB;oBACrB;gBACF;gBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,QAAQ,IAAI,CAAC;oBACb,UAAU;oBACV,aAAa;oBACb;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,MAAM,kBAA0B,KAAK,IAAI,CAAC,GAAG;iFAAC,CAAC,OAAc,CAAC;4BAC5D,IAAI,KAAK,EAAE;4BACX,MAAM,KAAK,IAAI;4BACf,QAAQ,KAAK,MAAM;4BACnB,SAAS,KAAK,OAAO;4BACrB,aAAa,KAAK,MAAM,IAAI;wBAC9B,CAAC;;gBAED,UAAU;YACZ,EAAE,OAAO,KAAK;gBACZ,QAAQ,IAAI,CAAC,gDAAgD;gBAC7D,UAAU;YACZ,SAAU;gBACR,aAAa;YACf;QACF;oDAAG;QAAC;KAAc,GAAG,oCAAoC;IAEzD,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,WAAW;mDAAC,CAAC;YACpC;2DAAW,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO;oBAAS,CAAC;;YAChD,cAAc,WAAW,iCAAiC;YAC1D,oBAAoB;QACtB;kDAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,oBAAoB;oBACtB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;oCAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,UAAU;YAEV,6BAA6B;YAC7B;6CAAO;oBACL,IAAI,cAAc,OAAO,EAAE;wBACzB,aAAa,cAAc,OAAO;oBACpC;gBACF;;QACF;oCAAG,EAAE;IAEL,iFAAiF;IACjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,QAAQ,GAAG,CAAC,uCAAuC,WAAW;YAC9D,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,8EAA8E;YAC9E,gDAAgD;YAChD,IAAI,aAAa,cAAc,WAAW,CAAC,CAAC,YAAY,CAAC,SAAS,QAAQ,CAAC,UAAU,IAAI,CAAC,GAAG;gBAC3F,IAAI;oBACF,wCAAwC;oBACxC,IAAI,UAAU;wBACZ,IAAI,eAAe,CAAC;oBACtB;oBAEA,MAAM,MAAM,IAAI,eAAe,CAAC;oBAChC,QAAQ,GAAG,CAAC,+CAA+C,UAAU,IAAI;oBACzE,YAAY;oBAEZ,mBAAmB;oBACnB;qDAAO;4BACL,IAAI,eAAe,CAAC;wBACtB;;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;YACF,OAAO,IAAI,aAAa,cAAc,SAAS;gBAC7C,QAAQ,GAAG,CAAC;YACd,OAAO,IAAI,CAAC,aAAa,cAAc,SAAS;gBAC9C,QAAQ,GAAG,CAAC;YACd;YAEA;6CAAO;gBACL,sFAAsF;gBACtF,yBAAyB;gBAC3B;;QACF;oCAAG;QAAC;QAAW;KAAU,GAAG,uDAAuD;IAEnF,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,WAAW;qDAAC;YACrC,gBAAgB;YAChB,MAAM,YAAoC,CAAC;YAE3C,4BAA4B;YAC5B,IAAI,CAAC,QAAQ,OAAO,CAAC,IAAI,IAAI;gBAC3B,UAAU,OAAO,GAAG;YACtB;YAEA,4BAA4B;YAC5B,IAAI,CAAC,QAAQ,YAAY,CAAC,IAAI,IAAI;gBAChC,UAAU,YAAY,GAAG;YAC3B;YAEA,oBAAoB;YACpB,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI;gBACzB,UAAU,KAAK,GAAG;YACpB;YAEA,oBAAoB;YACpB,UAAU;YAEV,sCAAsC;YACtC,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK,GAAG;gBACvC,OAAO;YACT;QACF;oDAAG;QAAC;QAAS;KAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,6LAAC;4BAAE,WAAU;sCAAY;;;;;;;;;;;;8BAG3B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,QAAQ,OAAO;4CACtB,UAAU;4CACV,aAAY;4CACZ,WAAW,CAAC,yFAAyF,EAAE,OAAO,OAAO,GAAG,mBAAmB,mBAAmB;;;;;;wCAE/J,OAAO,OAAO,kBACb,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO;;;;;;;;;;;;8CAI5D,6LAAC;8CACC,cAAA,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAO,QAAQ,WAAW;wCAC1B,UAAU;wCACV,aAAY;wCACZ,WAAU;;;;;;;;;;;8CAId,6LAAC;;sDACC,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,QAAQ,YAAY;4CAC3B,UAAU;4CACV,aAAY;4CACZ,WAAW,CAAC,yFAAyF,EAAE,OAAO,YAAY,GAAG,mBAAmB,mBAAmB;;;;;;wCAEpK,OAAO,YAAY,kBAClB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,YAAY;;;;;;;;;;;;8CAIjE,6LAAC;oCAAI,WAAU;oCAAW,KAAK;;sDAC7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO;oDACP,UAAU;oDACV,SAAS,IAAM,WAAW,MAAM,GAAG,KAAK,oBAAoB;oDAC5D,aAAY;oDACZ,WAAW,CAAC,+FAA+F,EAAE,OAAO,KAAK,GAAG,mBAAmB,mBAAmB;;;;;;8DAEpK,6LAAC;oDAAI,WAAU;8DACZ,0BACC,6LAAC,yMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;6EAE5B,6LAAC,6MAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;;;;;;;;;;;;wCAKjC,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;wCAIvD,kCACC,6LAAC;4CAAI,WAAU;sDACZ,0BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAC5B,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;uDAEhC,OAAO,MAAM,KAAK,kBACpB,6LAAC;gDAAI,WAAU;0DAA0B;;;;;uDAEzC,OAAO,GAAG,CAAC,CAAC,qBACV,6LAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,WAAW,KAAK,IAAI;8DAEnC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA6B,KAAK,IAAI;;;;;;0EACtD,6LAAC;gEAAK,WAAU;0EACb,KAAK,WAAW,IAAI,CAAC,KAAK,MAAM,GAAG,GAAG,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,OAAO,IAAI,SAAS,GAAG,OAAO;;;;;;;;;;;;mDAPzF,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAmB1B,6LAAC;4BAAI,WAAU;sCACZ,cAAc,WAAW,6BACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDAAqF;;;;;;;;;;;uCAIpG,cAAc,WAAW,yBAC3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAEC,KAAK;wCACL,QAAQ;wCACR,WAAU;wCACV,SAAS,CAAC;4CACR,QAAQ,KAAK,CAAC,wBAAwB;wCACxC;wCACA,QAAQ;wCACR,KAAK;wCACL,IAAI;uCATC,CAAC,MAAM,EAAE,WAAW,QAAQ,SAAS;;;;;kDAW5C,6LAAC;wCAAI,WAAU;kDAAqF;;;;;;kDAIpG,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAAwC;;;;;;;;;;;;;;;;qDAM3D,6LAAC;gCAAI,WAAU;0CAA+D;;;;;;;;;;;;;;;;;8BAQpF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;oCACC,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,MAAK;8CAEL,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAEP;;;;;;;sCAIR,6LAAC;4BACC,SAAS;4BACT,WAAU;;gCACX;8CAEC,6LAAC;oCACC,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,MAAK;8CAEL,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GA7cM;KAAA;uCA+cS", "debugId": null}}, {"offset": {"line": 5495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5501, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/upload/VendorDetails.tsx"], "sourcesContent": ["// components/upload/VendorDetails.tsx\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { X, Plus, Info } from 'lucide-react';\r\n\r\ninterface VendorDetail {\r\n  name: string;\r\n  mobileNumber: string;\r\n}\r\n\r\ninterface VendorDetailsProps {\r\n  onNext: (vendorDetails: Record<string, VendorDetail>) => void;\r\n  onBack: () => void;\r\n  onClose: () => void;\r\n  initialVendorDetails?: Record<string, VendorDetail>; // Initial vendor details for persistence between screens\r\n}\r\n\r\nconst VendorDetails: React.FC<VendorDetailsProps> = ({\r\n  onNext,\r\n  onBack,\r\n  onClose,\r\n  initialVendorDetails\r\n}) => {\r\n  // Create default vendor details\r\n  const defaultVendorDetails = {\r\n    venue: { name: '', mobileNumber: '' },\r\n    photographer: { name: '', mobileNumber: '' },\r\n    makeupArtist: { name: '', mobileNumber: '' },\r\n    decorations: { name: '', mobileNumber: '' },\r\n    caterer: { name: '', mobileNumber: '' }\r\n  };\r\n\r\n  // Merge initialVendorDetails with default values to ensure all fields exist\r\n  // Also handle mapping between frontend and backend field names\r\n  const mergedVendorDetails = initialVendorDetails\r\n    ? {\r\n        venue: initialVendorDetails.venue || defaultVendorDetails.venue,\r\n        photographer: initialVendorDetails.photographer || defaultVendorDetails.photographer,\r\n        // Handle both makeupArtist and makeup_artist (backend name)\r\n        makeupArtist: initialVendorDetails.makeupArtist || initialVendorDetails.makeup_artist || defaultVendorDetails.makeupArtist,\r\n        // Handle both decorations and decoration (backend name)\r\n        decorations: initialVendorDetails.decorations || initialVendorDetails.decoration || defaultVendorDetails.decorations,\r\n        caterer: initialVendorDetails.caterer || defaultVendorDetails.caterer,\r\n        ...Object.entries(initialVendorDetails)\r\n          .filter(([key]) => !['venue', 'photographer', 'makeupArtist', 'makeup_artist', 'decorations', 'decoration', 'caterer'].includes(key))\r\n          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})\r\n      }\r\n    : defaultVendorDetails;\r\n\r\n  // Log the merged vendor details to help with debugging\r\n  // console.log('Merged vendor details:', mergedVendorDetails);\r\n\r\n  // Use the merged vendor details\r\n  const [vendorDetails, setVendorDetails] = useState<Record<string, VendorDetail>>(mergedVendorDetails);\r\n\r\n  // Log the initial vendor details for debugging\r\n  React.useEffect(() => {\r\n    console.log('VendorDetails component initialized with:', {\r\n      initialVendorDetails,\r\n      currentVendorDetails: vendorDetails\r\n    });\r\n  }, []);\r\n\r\n  // Extract additional vendor types from initialVendorDetails\r\n  const initialAdditionalVendors = initialVendorDetails\r\n    ? Object.keys(initialVendorDetails).filter(\r\n        key => !['venue', 'photographer', 'makeupArtist', 'decorations', 'caterer'].includes(key)\r\n      )\r\n    : [];\r\n\r\n  const [additionalVendors, setAdditionalVendors] = useState<string[]>(initialAdditionalVendors);\r\n  const [errors, setErrors] = useState<{ general?: string; [key: string]: string | undefined }>({});\r\n\r\n  const handleInputChange = (\r\n    vendorType: string,\r\n    field: 'name' | 'mobileNumber',\r\n    value: string\r\n  ) => {\r\n    // Clear error for this field when user types\r\n    if (field === 'name' && errors[`${vendorType}_name`]) {\r\n      setErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[`${vendorType}_name`];\r\n        return newErrors;\r\n      });\r\n    } else if (field === 'mobileNumber' && errors[`${vendorType}_mobile`]) {\r\n      setErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[`${vendorType}_mobile`];\r\n        return newErrors;\r\n      });\r\n    }\r\n\r\n    // Clear general error if we're filling in a field\r\n    if (errors.general) {\r\n      setErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors.general;\r\n        return newErrors;\r\n      });\r\n    }\r\n\r\n    setVendorDetails(prev => ({\r\n      ...prev,\r\n      [vendorType]: {\r\n        ...prev[vendorType],\r\n        [field]: value\r\n      }\r\n    }));\r\n  };\r\n\r\n  const addMoreVendor = () => {\r\n    // Logic to add more vendor types if needed\r\n    const newVendorType = `additionalVendor${additionalVendors.length + 1}`;\r\n    setAdditionalVendors(prev => [...prev, newVendorType]);\r\n    setVendorDetails(prev => ({\r\n      ...prev,\r\n      [newVendorType]: { name: '', mobileNumber: '' }\r\n    }));\r\n  };\r\n\r\n  const validateVendorDetail = (_vendorType: string, detail: VendorDetail): string[] => {\r\n    const fieldErrors: string[] = [];\r\n\r\n    // Check if detail exists\r\n    if (!detail) {\r\n      fieldErrors.push('missing');\r\n      return fieldErrors;\r\n    }\r\n\r\n    // Check if name exists and is not empty\r\n    if (!detail.name || detail.name.trim() === '') {\r\n      fieldErrors.push('name');\r\n    }\r\n\r\n    // Check if mobileNumber exists and is not empty\r\n    if (!detail.mobileNumber || detail.mobileNumber.trim() === '') {\r\n      fieldErrors.push('mobileNumber');\r\n    } else if (!/^\\d{10}$/.test(detail.mobileNumber.trim())) {\r\n      fieldErrors.push('invalidMobileNumber');\r\n    }\r\n\r\n    return fieldErrors;\r\n  };\r\n\r\n  const handleSubmit = () => {\r\n    // Clear previous errors\r\n    setErrors({});\r\n\r\n    // Validate if at least 4 vendor details are filled\r\n    const filledVendors = Object.entries(vendorDetails).filter(\r\n      ([_, detail]) => detail && detail.name && detail.mobileNumber &&\r\n                      detail.name.trim() !== '' && detail.mobileNumber.trim() !== ''\r\n    );\r\n\r\n    // Collect validation errors\r\n    const newErrors: { [key: string]: string | undefined } = {};\r\n\r\n    // Check each vendor that has at least one field filled\r\n    Object.entries(vendorDetails).forEach(([vendorType, detail]) => {\r\n      // Skip if detail is undefined\r\n      if (!detail) {\r\n        console.warn(`Vendor detail for ${vendorType} is undefined`);\r\n        return;\r\n      }\r\n\r\n      // Only validate if at least one field has been filled\r\n      if ((detail.name && detail.name.trim() !== '') ||\r\n          (detail.mobileNumber && detail.mobileNumber.trim() !== '')) {\r\n        const fieldErrors = validateVendorDetail(vendorType, detail);\r\n\r\n        if (fieldErrors.includes('missing')) {\r\n          newErrors[`${vendorType}_name`] = 'Vendor details are missing';\r\n          return;\r\n        }\r\n\r\n        if (fieldErrors.includes('name')) {\r\n          newErrors[`${vendorType}_name`] = 'Vendor name is required';\r\n        }\r\n\r\n        if (fieldErrors.includes('mobileNumber')) {\r\n          newErrors[`${vendorType}_mobile`] = 'Mobile number is required';\r\n        } else if (fieldErrors.includes('invalidMobileNumber')) {\r\n          newErrors[`${vendorType}_mobile`] = 'Please enter a valid 10-digit mobile number';\r\n        }\r\n      }\r\n    });\r\n\r\n    // Check if we have enough complete vendor details\r\n    if (filledVendors.length < 4) {\r\n      newErrors.general = `At least 4 complete vendor details (with both name and contact) are required for video uploads. You provided ${filledVendors.length}/4.`;\r\n    }\r\n\r\n    // Set errors if any\r\n    setErrors(newErrors);\r\n\r\n    // Only proceed if we have at least 4 complete vendor details and no errors\r\n    if (filledVendors.length >= 4 && Object.keys(newErrors).length === 0) {\r\n      // Map our vendor details to the format expected by the backend\r\n      const mappedVendorDetails: Record<string, VendorDetail> = {};\r\n\r\n      // Count how many valid vendors we have\r\n      let validVendorCount = 0;\r\n\r\n      // Map the vendor types to the backend expected format\r\n      // Only include vendors that have BOTH name AND mobile number\r\n      if (vendorDetails.venue && vendorDetails.venue.name && vendorDetails.venue.mobileNumber) {\r\n        mappedVendorDetails.venue = vendorDetails.venue;\r\n        validVendorCount++;\r\n        console.log('Added venue vendor');\r\n      }\r\n\r\n      if (vendorDetails.photographer && vendorDetails.photographer.name && vendorDetails.photographer.mobileNumber) {\r\n        mappedVendorDetails.photographer = vendorDetails.photographer;\r\n        validVendorCount++;\r\n        console.log('Added photographer vendor');\r\n      }\r\n\r\n      if (vendorDetails.makeupArtist && vendorDetails.makeupArtist.name && vendorDetails.makeupArtist.mobileNumber) {\r\n        // Add both frontend and backend field names for cross-browser compatibility\r\n        mappedVendorDetails.makeupArtist = vendorDetails.makeupArtist;\r\n        mappedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\r\n        validVendorCount++;\r\n        console.log('Added makeup artist vendor');\r\n      }\r\n\r\n      if (vendorDetails.decorations && vendorDetails.decorations.name && vendorDetails.decorations.mobileNumber) {\r\n        // Add both frontend and backend field names for cross-browser compatibility\r\n        mappedVendorDetails.decorations = vendorDetails.decorations;\r\n        mappedVendorDetails.decoration = vendorDetails.decorations;\r\n        validVendorCount++;\r\n        console.log('Added decorations vendor');\r\n      }\r\n\r\n      if (vendorDetails.caterer && vendorDetails.caterer.name && vendorDetails.caterer.mobileNumber) {\r\n        mappedVendorDetails.caterer = vendorDetails.caterer;\r\n        validVendorCount++;\r\n        console.log('Added caterer vendor');\r\n      }\r\n\r\n      // Log the current valid vendor count\r\n      // console.log(`Current valid vendor count before additional vendors: ${validVendorCount}`);\r\n      // console.log(`Additional vendors to process: ${additionalVendors.length}`);\r\n\r\n      // Debug all vendor details\r\n      // console.log('All vendor details:', JSON.stringify(vendorDetails, null, 2));\r\n\r\n      // Add any additional vendors - only if they have BOTH name AND mobile number\r\n      // If we don't have enough predefined vendors, map additional vendors to the predefined types\r\n      const emptyPredefinedTypes: string[] = [];\r\n      if (validVendorCount < 4) {\r\n        // Check which predefined types are empty\r\n        if (!mappedVendorDetails.venue) emptyPredefinedTypes.push('venue');\r\n        if (!mappedVendorDetails.photographer) emptyPredefinedTypes.push('photographer');\r\n        // Check both frontend and backend field names\r\n        if (!mappedVendorDetails.makeupArtist && !mappedVendorDetails.makeup_artist) {\r\n          emptyPredefinedTypes.push('makeupArtist'); // Use frontend name for consistency\r\n        }\r\n        // Check both frontend and backend field names\r\n        if (!mappedVendorDetails.decorations && !mappedVendorDetails.decoration) {\r\n          emptyPredefinedTypes.push('decorations'); // Use frontend name for consistency\r\n        }\r\n        if (!mappedVendorDetails.caterer) emptyPredefinedTypes.push('caterer');\r\n\r\n        console.log('Empty predefined types:', emptyPredefinedTypes);\r\n      }\r\n\r\n      // Collect valid additional vendors\r\n      const validAdditionalVendors: {type: string, detail: VendorDetail}[] = [];\r\n      additionalVendors.forEach((vendorType) => {\r\n        if (vendorDetails[vendorType]?.name && vendorDetails[vendorType]?.mobileNumber) {\r\n          validAdditionalVendors.push({\r\n            type: vendorType,\r\n            detail: vendorDetails[vendorType]\r\n          });\r\n          console.log(`Found valid additional vendor: ${vendorType}`);\r\n        }\r\n      });\r\n\r\n      // If we need more vendors to reach 4, map additional vendors to predefined types\r\n      if (validVendorCount < 4 && validAdditionalVendors.length > 0) {\r\n        let additionalIndex = 0;\r\n        for (const type of emptyPredefinedTypes) {\r\n          if (additionalIndex < validAdditionalVendors.length) {\r\n            mappedVendorDetails[type] = validAdditionalVendors[additionalIndex].detail;\r\n            console.log(`Mapped additional vendor ${validAdditionalVendors[additionalIndex].type} to predefined type ${type}`);\r\n            additionalIndex++;\r\n            validVendorCount++;\r\n\r\n            if (validVendorCount >= 4) break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If we still have additional vendors, add them with the additional prefix\r\n      additionalVendors.forEach((vendorType, index) => {\r\n        if (vendorDetails[vendorType]?.name && vendorDetails[vendorType]?.mobileNumber) {\r\n          // Check if this vendor was already mapped to a predefined type\r\n          let alreadyMapped = false;\r\n          for (const type of emptyPredefinedTypes) {\r\n            if (mappedVendorDetails[type] === vendorDetails[vendorType]) {\r\n              alreadyMapped = true;\r\n              break;\r\n            }\r\n          }\r\n\r\n          // If not already mapped, add it as an additional vendor\r\n          if (!alreadyMapped) {\r\n            mappedVendorDetails[`additional${index + 1}`] = vendorDetails[vendorType];\r\n            console.log(`Adding additional vendor ${index + 1}:`, vendorDetails[vendorType]);\r\n          }\r\n        }\r\n      });\r\n\r\n      // Log the final vendor details being sent to the next step\r\n      // console.log('VENDOR DETAILS - Final mapped vendor details being sent to next step:', JSON.stringify(mappedVendorDetails, null, 2));\r\n\r\n      // Count how many complete vendor details we're sending\r\n      const completeVendorCount = Object.entries(mappedVendorDetails).filter(([_, detail]) =>\r\n        detail && detail.name && detail.mobileNumber &&\r\n        detail.name.trim() !== '' && detail.mobileNumber.trim() !== ''\r\n      ).length;\r\n\r\n      console.log(`VENDOR DETAILS - Sending ${completeVendorCount} complete vendor details`);\r\n      console.log('VENDOR DETAILS - Final mapped vendor details:', JSON.stringify(mappedVendorDetails, null, 2));\r\n\r\n      // Add a small delay before proceeding to ensure state updates properly in Edge\r\n      setTimeout(() => {\r\n        // Double-check that we have at least 4 complete vendor details\r\n        if (completeVendorCount >= 4) {\r\n          console.log('VENDOR DETAILS - Proceeding with', completeVendorCount, 'vendor details');\r\n          onNext(mappedVendorDetails);\r\n        } else {\r\n          console.error('VENDOR DETAILS - Not enough complete vendor details:', completeVendorCount);\r\n          alert('Please provide at least 4 complete vendor details (with both name and contact)');\r\n        }\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  // Count how many vendors have both name and mobile filled\r\n  const filledVendorCount = Object.values(vendorDetails).filter(\r\n    vendor => vendor && vendor.name && vendor.mobileNumber &&\r\n              vendor.name.trim() !== '' && vendor.mobileNumber.trim() !== ''\r\n  ).length;\r\n\r\n  // Check if at least 4 vendors have both name and mobile filled\r\n  const isValid = filledVendorCount >= 4;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]\">\r\n        {/* Close button */}\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-4 right-4 text-gray-800 hover:text-red-600\"\r\n        >\r\n          <X size={24} />\r\n        </button>\r\n\r\n        {/* Header */}\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"flex items-center mr-2\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={32}\r\n              height={32}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n          <h2 className=\"text-xl font-bold\">Vendor Details</h2>\r\n          <div className=\"ml-2 text-gray-500 cursor-help\" title=\"At least 4 complete vendor details (with both name and contact) are required for video uploads.\">\r\n            <Info size={16} />\r\n          </div>\r\n          <div className=\"ml-2\">\r\n            <Image\r\n              src=\"/pics/umoments.png\"\r\n              alt=\"Moments\"\r\n              width={20}\r\n              height={20}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Monetization note */}\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"bg-gray-200 text-sm rounded-md px-3 py-1 inline-block\">\r\n            More vendor details, more monetization\r\n          </div>\r\n          <div className=\"ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-xs\">\r\n            {filledVendorCount}/4 complete\r\n          </div>\r\n        </div>\r\n\r\n        {/* General error message */}\r\n        {errors.general && (\r\n          <div className=\"bg-red-100 text-red-800 p-3 rounded-md mb-4\">\r\n            {errors.general}\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex items-center mb-6\">\r\n          <Image\r\n            src=\"/pics/store-front.png\"\r\n            alt=\"Store\"\r\n            width={24}\r\n            height={24}\r\n            className=\"object-cover mr-2\"\r\n          />\r\n          <p className=\"text-base font-medium\">4 Complete Vendor Details Are Mandatory (Both Name and Contact)</p>\r\n\r\n          <div className=\"ml-auto\">\r\n            <button\r\n              onClick={addMoreVendor}\r\n              className=\"flex items-center bg-gray-200 rounded-full px-3 py-1 text-sm\"\r\n            >\r\n              Add More\r\n              <Plus size={16} className=\"ml-1\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Vendor form fields */}\r\n        <div className=\"space-y-4\">\r\n          <div className=\"grid grid-cols-5 gap-4 items-center\">\r\n            <div className=\"text-sm font-medium\">Venue</div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Name (required)\"\r\n                value={vendorDetails.venue.name}\r\n                onChange={(e) => handleInputChange('venue', 'name', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.venue_name ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.venue_name && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.venue_name}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Mobile Number (required)\"\r\n                value={vendorDetails.venue.mobileNumber}\r\n                onChange={(e) => handleInputChange('venue', 'mobileNumber', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.venue_mobile ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.venue_mobile && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.venue_mobile}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-5 gap-4 items-center\">\r\n            <div className=\"text-sm font-medium\">Photograph</div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Name (required)\"\r\n                value={vendorDetails.photographer.name}\r\n                onChange={(e) => handleInputChange('photographer', 'name', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.photographer_name ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.photographer_name && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.photographer_name}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Mobile Number (required)\"\r\n                value={vendorDetails.photographer.mobileNumber}\r\n                onChange={(e) => handleInputChange('photographer', 'mobileNumber', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.photographer_mobile ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.photographer_mobile && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.photographer_mobile}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-5 gap-4 items-center\">\r\n            <div className=\"text-sm font-medium\">Make up Artist</div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Name (required)\"\r\n                value={vendorDetails.makeupArtist.name}\r\n                onChange={(e) => handleInputChange('makeupArtist', 'name', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.makeupArtist_name ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.makeupArtist_name && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.makeupArtist_name}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Mobile Number (required)\"\r\n                value={vendorDetails.makeupArtist.mobileNumber}\r\n                onChange={(e) => handleInputChange('makeupArtist', 'mobileNumber', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.makeupArtist_mobile ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.makeupArtist_mobile && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.makeupArtist_mobile}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-5 gap-4 items-center\">\r\n            <div className=\"text-sm font-medium\">Decorations</div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Name (required)\"\r\n                value={vendorDetails.decorations.name}\r\n                onChange={(e) => handleInputChange('decorations', 'name', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.decorations_name ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.decorations_name && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.decorations_name}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Mobile Number (required)\"\r\n                value={vendorDetails.decorations.mobileNumber}\r\n                onChange={(e) => handleInputChange('decorations', 'mobileNumber', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.decorations_mobile ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.decorations_mobile && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.decorations_mobile}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-5 gap-4 items-center\">\r\n            <div className=\"text-sm font-medium\">Caterer</div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Name (required)\"\r\n                value={vendorDetails.caterer.name}\r\n                onChange={(e) => handleInputChange('caterer', 'name', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.caterer_name ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.caterer_name && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.caterer_name}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"col-span-2\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Mobile Number (required)\"\r\n                value={vendorDetails.caterer.mobileNumber}\r\n                onChange={(e) => handleInputChange('caterer', 'mobileNumber', e.target.value)}\r\n                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.caterer_mobile ? 'border-red-500' : 'border-gray-300'}`}\r\n              />\r\n              {errors.caterer_mobile && (\r\n                <p className=\"text-red-500 text-xs mt-1\">{errors.caterer_mobile}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Additional vendor fields */}\r\n          {additionalVendors.map((vendorType, index) => (\r\n            <div key={vendorType} className=\"grid grid-cols-5 gap-4 items-center\">\r\n              <div className=\"text-sm font-medium\">Additional {index + 1}</div>\r\n              <div className=\"col-span-2\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Name (required)\"\r\n                  value={vendorDetails[vendorType]?.name || ''}\r\n                  onChange={(e) => handleInputChange(vendorType, 'name', e.target.value)}\r\n                  className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors[`${vendorType}_name`] ? 'border-red-500' : 'border-gray-300'}`}\r\n                />\r\n                {errors[`${vendorType}_name`] && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">{errors[`${vendorType}_name`]}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"col-span-2\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Mobile Number (required)\"\r\n                  value={vendorDetails[vendorType]?.mobileNumber || ''}\r\n                  onChange={(e) => handleInputChange(vendorType, 'mobileNumber', e.target.value)}\r\n                  className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors[`${vendorType}_mobile`] ? 'border-red-500' : 'border-gray-300'}`}\r\n                />\r\n                {errors[`${vendorType}_mobile`] && (\r\n                  <p className=\"text-red-500 text-xs mt-1\">{errors[`${vendorType}_mobile`]}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Navigation buttons */}\r\n        <div className=\"flex justify-between mt-8\">\r\n          <button\r\n            onClick={onBack}\r\n            className=\"flex items-center justify-center px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-5 w-5 mr-1\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Back\r\n          </button>\r\n\r\n          <div className=\"flex flex-col items-end\">\r\n            {!isValid && (\r\n              <div className=\"text-red-600 text-sm mb-2\">\r\n                Please complete at least 4 vendor details ({filledVendorCount}/4)\r\n              </div>\r\n            )}\r\n            <button\r\n              onClick={handleSubmit}\r\n              disabled={!isValid}\r\n              className={`flex items-center justify-center px-6 py-2 rounded-md ${\r\n                isValid\r\n                  ? 'bg-red-600 text-white hover:bg-red-700'\r\n                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n              } transition duration-200`}\r\n            >\r\n              Next\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                className=\"h-5 w-5 ml-1\"\r\n                viewBox=\"0 0 20 20\"\r\n                fill=\"currentColor\"\r\n              >\r\n                <path\r\n                  fillRule=\"evenodd\"\r\n                  d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\"\r\n                  clipRule=\"evenodd\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VendorDetails;"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AAGtC;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAkBA,MAAM,gBAA8C,CAAC,EACnD,MAAM,EACN,MAAM,EACN,OAAO,EACP,oBAAoB,EACrB;;IACC,gCAAgC;IAChC,MAAM,uBAAuB;QAC3B,OAAO;YAAE,MAAM;YAAI,cAAc;QAAG;QACpC,cAAc;YAAE,MAAM;YAAI,cAAc;QAAG;QAC3C,cAAc;YAAE,MAAM;YAAI,cAAc;QAAG;QAC3C,aAAa;YAAE,MAAM;YAAI,cAAc;QAAG;QAC1C,SAAS;YAAE,MAAM;YAAI,cAAc;QAAG;IACxC;IAEA,4EAA4E;IAC5E,+DAA+D;IAC/D,MAAM,sBAAsB,uBACxB;QACE,OAAO,qBAAqB,KAAK,IAAI,qBAAqB,KAAK;QAC/D,cAAc,qBAAqB,YAAY,IAAI,qBAAqB,YAAY;QACpF,4DAA4D;QAC5D,cAAc,qBAAqB,YAAY,IAAI,qBAAqB,aAAa,IAAI,qBAAqB,YAAY;QAC1H,wDAAwD;QACxD,aAAa,qBAAqB,WAAW,IAAI,qBAAqB,UAAU,IAAI,qBAAqB,WAAW;QACpH,SAAS,qBAAqB,OAAO,IAAI,qBAAqB,OAAO;QACrE,GAAG,OAAO,OAAO,CAAC,sBACf,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK,CAAC;gBAAC;gBAAS;gBAAgB;gBAAgB;gBAAiB;gBAAe;gBAAc;aAAU,CAAC,QAAQ,CAAC,MAC/H,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM,GAAK,CAAC;gBAAE,GAAG,GAAG;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC,GAAG,CAAC,EAAE;IAClE,IACA;IAEJ,uDAAuD;IACvD,8DAA8D;IAE9D,gCAAgC;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAEjF,+CAA+C;IAC/C,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,QAAQ,GAAG,CAAC,6CAA6C;gBACvD;gBACA,sBAAsB;YACxB;QACF;kCAAG,EAAE;IAEL,4DAA4D;IAC5D,MAAM,2BAA2B,uBAC7B,OAAO,IAAI,CAAC,sBAAsB,MAAM,CACtC,CAAA,MAAO,CAAC;YAAC;YAAS;YAAgB;YAAgB;YAAe;SAAU,CAAC,QAAQ,CAAC,QAEvF,EAAE;IAEN,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2D,CAAC;IAE/F,MAAM,oBAAoB,CACxB,YACA,OACA;QAEA,6CAA6C;QAC7C,IAAI,UAAU,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,EAAE;YACpD,UAAU,CAAA;gBACR,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC;gBACtC,OAAO;YACT;QACF,OAAO,IAAI,UAAU,kBAAkB,MAAM,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC,EAAE;YACrE,UAAU,CAAA;gBACR,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC;gBACxC,OAAO;YACT;QACF;QAEA,kDAAkD;QAClD,IAAI,OAAO,OAAO,EAAE;YAClB,UAAU,CAAA;gBACR,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,UAAU,OAAO;gBACxB,OAAO;YACT;QACF;QAEA,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;oBACZ,GAAG,IAAI,CAAC,WAAW;oBACnB,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;IACH;IAEA,MAAM,gBAAgB;QACpB,2CAA2C;QAC3C,MAAM,gBAAgB,CAAC,gBAAgB,EAAE,kBAAkB,MAAM,GAAG,GAAG;QACvE,qBAAqB,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QACrD,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,cAAc,EAAE;oBAAE,MAAM;oBAAI,cAAc;gBAAG;YAChD,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC,aAAqB;QACjD,MAAM,cAAwB,EAAE;QAEhC,yBAAyB;QACzB,IAAI,CAAC,QAAQ;YACX,YAAY,IAAI,CAAC;YACjB,OAAO;QACT;QAEA,wCAAwC;QACxC,IAAI,CAAC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI;YAC7C,YAAY,IAAI,CAAC;QACnB;QAEA,gDAAgD;QAChD,IAAI,CAAC,OAAO,YAAY,IAAI,OAAO,YAAY,CAAC,IAAI,OAAO,IAAI;YAC7D,YAAY,IAAI,CAAC;QACnB,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,OAAO,YAAY,CAAC,IAAI,KAAK;YACvD,YAAY,IAAI,CAAC;QACnB;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,wBAAwB;QACxB,UAAU,CAAC;QAEX,mDAAmD;QACnD,MAAM,gBAAgB,OAAO,OAAO,CAAC,eAAe,MAAM,CACxD,CAAC,CAAC,GAAG,OAAO,GAAK,UAAU,OAAO,IAAI,IAAI,OAAO,YAAY,IAC7C,OAAO,IAAI,CAAC,IAAI,OAAO,MAAM,OAAO,YAAY,CAAC,IAAI,OAAO;QAG9E,4BAA4B;QAC5B,MAAM,YAAmD,CAAC;QAE1D,uDAAuD;QACvD,OAAO,OAAO,CAAC,eAAe,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO;YACzD,8BAA8B;YAC9B,IAAI,CAAC,QAAQ;gBACX,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,WAAW,aAAa,CAAC;gBAC3D;YACF;YAEA,sDAAsD;YACtD,IAAI,AAAC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,MACtC,OAAO,YAAY,IAAI,OAAO,YAAY,CAAC,IAAI,OAAO,IAAK;gBAC9D,MAAM,cAAc,qBAAqB,YAAY;gBAErD,IAAI,YAAY,QAAQ,CAAC,YAAY;oBACnC,SAAS,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG;oBAClC;gBACF;gBAEA,IAAI,YAAY,QAAQ,CAAC,SAAS;oBAChC,SAAS,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG;gBACpC;gBAEA,IAAI,YAAY,QAAQ,CAAC,iBAAiB;oBACxC,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC,GAAG;gBACtC,OAAO,IAAI,YAAY,QAAQ,CAAC,wBAAwB;oBACtD,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC,GAAG;gBACtC;YACF;QACF;QAEA,kDAAkD;QAClD,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,UAAU,OAAO,GAAG,CAAC,6GAA6G,EAAE,cAAc,MAAM,CAAC,GAAG,CAAC;QAC/J;QAEA,oBAAoB;QACpB,UAAU;QAEV,2EAA2E;QAC3E,IAAI,cAAc,MAAM,IAAI,KAAK,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK,GAAG;YACpE,+DAA+D;YAC/D,MAAM,sBAAoD,CAAC;YAE3D,uCAAuC;YACvC,IAAI,mBAAmB;YAEvB,sDAAsD;YACtD,6DAA6D;YAC7D,IAAI,cAAc,KAAK,IAAI,cAAc,KAAK,CAAC,IAAI,IAAI,cAAc,KAAK,CAAC,YAAY,EAAE;gBACvF,oBAAoB,KAAK,GAAG,cAAc,KAAK;gBAC/C;gBACA,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,cAAc,YAAY,IAAI,cAAc,YAAY,CAAC,IAAI,IAAI,cAAc,YAAY,CAAC,YAAY,EAAE;gBAC5G,oBAAoB,YAAY,GAAG,cAAc,YAAY;gBAC7D;gBACA,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,cAAc,YAAY,IAAI,cAAc,YAAY,CAAC,IAAI,IAAI,cAAc,YAAY,CAAC,YAAY,EAAE;gBAC5G,4EAA4E;gBAC5E,oBAAoB,YAAY,GAAG,cAAc,YAAY;gBAC7D,oBAAoB,aAAa,GAAG,cAAc,YAAY;gBAC9D;gBACA,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,cAAc,WAAW,IAAI,cAAc,WAAW,CAAC,IAAI,IAAI,cAAc,WAAW,CAAC,YAAY,EAAE;gBACzG,4EAA4E;gBAC5E,oBAAoB,WAAW,GAAG,cAAc,WAAW;gBAC3D,oBAAoB,UAAU,GAAG,cAAc,WAAW;gBAC1D;gBACA,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,cAAc,OAAO,IAAI,cAAc,OAAO,CAAC,IAAI,IAAI,cAAc,OAAO,CAAC,YAAY,EAAE;gBAC7F,oBAAoB,OAAO,GAAG,cAAc,OAAO;gBACnD;gBACA,QAAQ,GAAG,CAAC;YACd;YAEA,qCAAqC;YACrC,4FAA4F;YAC5F,6EAA6E;YAE7E,2BAA2B;YAC3B,8EAA8E;YAE9E,6EAA6E;YAC7E,6FAA6F;YAC7F,MAAM,uBAAiC,EAAE;YACzC,IAAI,mBAAmB,GAAG;gBACxB,yCAAyC;gBACzC,IAAI,CAAC,oBAAoB,KAAK,EAAE,qBAAqB,IAAI,CAAC;gBAC1D,IAAI,CAAC,oBAAoB,YAAY,EAAE,qBAAqB,IAAI,CAAC;gBACjE,8CAA8C;gBAC9C,IAAI,CAAC,oBAAoB,YAAY,IAAI,CAAC,oBAAoB,aAAa,EAAE;oBAC3E,qBAAqB,IAAI,CAAC,iBAAiB,oCAAoC;gBACjF;gBACA,8CAA8C;gBAC9C,IAAI,CAAC,oBAAoB,WAAW,IAAI,CAAC,oBAAoB,UAAU,EAAE;oBACvE,qBAAqB,IAAI,CAAC,gBAAgB,oCAAoC;gBAChF;gBACA,IAAI,CAAC,oBAAoB,OAAO,EAAE,qBAAqB,IAAI,CAAC;gBAE5D,QAAQ,GAAG,CAAC,2BAA2B;YACzC;YAEA,mCAAmC;YACnC,MAAM,yBAAiE,EAAE;YACzE,kBAAkB,OAAO,CAAC,CAAC;gBACzB,IAAI,aAAa,CAAC,WAAW,EAAE,QAAQ,aAAa,CAAC,WAAW,EAAE,cAAc;oBAC9E,uBAAuB,IAAI,CAAC;wBAC1B,MAAM;wBACN,QAAQ,aAAa,CAAC,WAAW;oBACnC;oBACA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,YAAY;gBAC5D;YACF;YAEA,iFAAiF;YACjF,IAAI,mBAAmB,KAAK,uBAAuB,MAAM,GAAG,GAAG;gBAC7D,IAAI,kBAAkB;gBACtB,KAAK,MAAM,QAAQ,qBAAsB;oBACvC,IAAI,kBAAkB,uBAAuB,MAAM,EAAE;wBACnD,mBAAmB,CAAC,KAAK,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,MAAM;wBAC1E,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,EAAE,MAAM;wBACjH;wBACA;wBAEA,IAAI,oBAAoB,GAAG;oBAC7B;gBACF;YACF;YAEA,2EAA2E;YAC3E,kBAAkB,OAAO,CAAC,CAAC,YAAY;gBACrC,IAAI,aAAa,CAAC,WAAW,EAAE,QAAQ,aAAa,CAAC,WAAW,EAAE,cAAc;oBAC9E,+DAA+D;oBAC/D,IAAI,gBAAgB;oBACpB,KAAK,MAAM,QAAQ,qBAAsB;wBACvC,IAAI,mBAAmB,CAAC,KAAK,KAAK,aAAa,CAAC,WAAW,EAAE;4BAC3D,gBAAgB;4BAChB;wBACF;oBACF;oBAEA,wDAAwD;oBACxD,IAAI,CAAC,eAAe;wBAClB,mBAAmB,CAAC,CAAC,UAAU,EAAE,QAAQ,GAAG,CAAC,GAAG,aAAa,CAAC,WAAW;wBACzE,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,WAAW;oBACjF;gBACF;YACF;YAEA,2DAA2D;YAC3D,sIAAsI;YAEtI,uDAAuD;YACvD,MAAM,sBAAsB,OAAO,OAAO,CAAC,qBAAqB,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,GACjF,UAAU,OAAO,IAAI,IAAI,OAAO,YAAY,IAC5C,OAAO,IAAI,CAAC,IAAI,OAAO,MAAM,OAAO,YAAY,CAAC,IAAI,OAAO,IAC5D,MAAM;YAER,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,oBAAoB,wBAAwB,CAAC;YACrF,QAAQ,GAAG,CAAC,iDAAiD,KAAK,SAAS,CAAC,qBAAqB,MAAM;YAEvG,+EAA+E;YAC/E,WAAW;gBACT,+DAA+D;gBAC/D,IAAI,uBAAuB,GAAG;oBAC5B,QAAQ,GAAG,CAAC,oCAAoC,qBAAqB;oBACrE,OAAO;gBACT,OAAO;oBACL,QAAQ,KAAK,CAAC,wDAAwD;oBACtE,MAAM;gBACR;YACF,GAAG;QACL;IACF;IAEA,0DAA0D;IAC1D,MAAM,oBAAoB,OAAO,MAAM,CAAC,eAAe,MAAM,CAC3D,CAAA,SAAU,UAAU,OAAO,IAAI,IAAI,OAAO,YAAY,IAC5C,OAAO,IAAI,CAAC,IAAI,OAAO,MAAM,OAAO,YAAY,CAAC,IAAI,OAAO,IACtE,MAAM;IAER,+DAA+D;IAC/D,MAAM,UAAU,qBAAqB;IAErC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,6LAAC;4BAAI,WAAU;4BAAiC,OAAM;sCACpD,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;sCAEd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;8BAMhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAwD;;;;;;sCAGvE,6LAAC;4BAAI,WAAU;;gCACZ;gCAAkB;;;;;;;;;;;;;gBAKtB,OAAO,OAAO,kBACb,6LAAC;oBAAI,WAAU;8BACZ,OAAO,OAAO;;;;;;8BAInB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAErC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;;oCACX;kDAEC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;8CACrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,KAAK,CAAC,IAAI;4CAC/B,UAAU,CAAC,IAAM,kBAAkB,SAAS,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAClE,WAAW,CAAC,yFAAyF,EAAE,OAAO,UAAU,GAAG,mBAAmB,mBAAmB;;;;;;wCAElK,OAAO,UAAU,kBAChB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,UAAU;;;;;;;;;;;;8CAG/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,KAAK,CAAC,YAAY;4CACvC,UAAU,CAAC,IAAM,kBAAkB,SAAS,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC1E,WAAW,CAAC,yFAAyF,EAAE,OAAO,YAAY,GAAG,mBAAmB,mBAAmB;;;;;;wCAEpK,OAAO,YAAY,kBAClB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,YAAY;;;;;;;;;;;;;;;;;;sCAKnE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;8CACrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,YAAY,CAAC,IAAI;4CACtC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACzE,WAAW,CAAC,yFAAyF,EAAE,OAAO,iBAAiB,GAAG,mBAAmB,mBAAmB;;;;;;wCAEzK,OAAO,iBAAiB,kBACvB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,iBAAiB;;;;;;;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,YAAY,CAAC,YAAY;4CAC9C,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CACjF,WAAW,CAAC,yFAAyF,EAAE,OAAO,mBAAmB,GAAG,mBAAmB,mBAAmB;;;;;;wCAE3K,OAAO,mBAAmB,kBACzB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,mBAAmB;;;;;;;;;;;;;;;;;;sCAK1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;8CACrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,YAAY,CAAC,IAAI;4CACtC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACzE,WAAW,CAAC,yFAAyF,EAAE,OAAO,iBAAiB,GAAG,mBAAmB,mBAAmB;;;;;;wCAEzK,OAAO,iBAAiB,kBACvB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,iBAAiB;;;;;;;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,YAAY,CAAC,YAAY;4CAC9C,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CACjF,WAAW,CAAC,yFAAyF,EAAE,OAAO,mBAAmB,GAAG,mBAAmB,mBAAmB;;;;;;wCAE3K,OAAO,mBAAmB,kBACzB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,mBAAmB;;;;;;;;;;;;;;;;;;sCAK1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;8CACrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,WAAW,CAAC,IAAI;4CACrC,UAAU,CAAC,IAAM,kBAAkB,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACxE,WAAW,CAAC,yFAAyF,EAAE,OAAO,gBAAgB,GAAG,mBAAmB,mBAAmB;;;;;;wCAExK,OAAO,gBAAgB,kBACtB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,gBAAgB;;;;;;;;;;;;8CAGrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,WAAW,CAAC,YAAY;4CAC7C,UAAU,CAAC,IAAM,kBAAkB,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAChF,WAAW,CAAC,yFAAyF,EAAE,OAAO,kBAAkB,GAAG,mBAAmB,mBAAmB;;;;;;wCAE1K,OAAO,kBAAkB,kBACxB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;sCAKzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsB;;;;;;8CACrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,OAAO,CAAC,IAAI;4CACjC,UAAU,CAAC,IAAM,kBAAkB,WAAW,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACpE,WAAW,CAAC,yFAAyF,EAAE,OAAO,YAAY,GAAG,mBAAmB,mBAAmB;;;;;;wCAEpK,OAAO,YAAY,kBAClB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,YAAY;;;;;;;;;;;;8CAGjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,cAAc,OAAO,CAAC,YAAY;4CACzC,UAAU,CAAC,IAAM,kBAAkB,WAAW,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC5E,WAAW,CAAC,yFAAyF,EAAE,OAAO,cAAc,GAAG,mBAAmB,mBAAmB;;;;;;wCAEtK,OAAO,cAAc,kBACpB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,cAAc;;;;;;;;;;;;;;;;;;wBAMpE,kBAAkB,GAAG,CAAC,CAAC,YAAY,sBAClC,6LAAC;gCAAqB,WAAU;;kDAC9B,6LAAC;wCAAI,WAAU;;4CAAsB;4CAAY,QAAQ;;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,aAAa,CAAC,WAAW,EAAE,QAAQ;gDAC1C,UAAU,CAAC,IAAM,kBAAkB,YAAY,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACrE,WAAW,CAAC,yFAAyF,EAAE,MAAM,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,mBAAmB,mBAAmB;;;;;;4CAE7K,MAAM,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,kBAC3B,6LAAC;gDAAE,WAAU;0DAA6B,MAAM,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC;;;;;;;;;;;;kDAG1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,aAAa,CAAC,WAAW,EAAE,gBAAgB;gDAClD,UAAU,CAAC,IAAM,kBAAkB,YAAY,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC7E,WAAW,CAAC,yFAAyF,EAAE,MAAM,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC,GAAG,mBAAmB,mBAAmB;;;;;;4CAE/K,MAAM,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC,kBAC7B,6LAAC;gDAAE,WAAU;0DAA6B,MAAM,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC;;;;;;;;;;;;;+BAvBpE;;;;;;;;;;;8BA+Bd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;oCACC,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,MAAK;8CAEL,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAEP;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;;gCACZ,CAAC,yBACA,6LAAC;oCAAI,WAAU;;wCAA4B;wCACG;wCAAkB;;;;;;;8CAGlE,6LAAC;oCACC,SAAS;oCACT,UAAU,CAAC;oCACX,WAAW,CAAC,sDAAsD,EAChE,UACI,2CACA,+CACL,wBAAwB,CAAC;;wCAC3B;sDAEC,6LAAC;4CACC,OAAM;4CACN,WAAU;4CACV,SAAQ;4CACR,MAAK;sDAEL,cAAA,6LAAC;gDACC,UAAS;gDACT,GAAE;gDACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;GA5nBM;KAAA;uCA8nBS", "debugId": null}}, {"offset": {"line": 6560, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6566, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/upload/FaceVerification.tsx"], "sourcesContent": ["// components/upload/FaceVerification.tsx\r\n'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport { X, Camera, CheckCircle, AlertCircle } from 'lucide-react';\r\nimport { uploadService } from '../../services/api';\r\n\r\ninterface FaceVerificationProps {\r\n  onUpload: () => void;\r\n  onBack: () => void;\r\n  onClose: () => void;\r\n}\r\n\r\nconst FaceVerification: React.FC<FaceVerificationProps> = ({\r\n  onUpload,\r\n  onBack,\r\n  onClose\r\n}) => {\r\n  // We no longer need cameraActive state as we use stream presence instead\r\n  const [capturedImage, setCapturedImage] = useState<string | null>(null);\r\n  const [cameraError, setCameraError] = useState<string | null>(null);\r\n  const [stream, setStream] = useState<MediaStream | null>(null);\r\n  const [verifying, setVerifying] = useState(false);\r\n  const [verificationStatus, setVerificationStatus] = useState<'none' | 'success' | 'error'>('none');\r\n  const [verificationMessage, setVerificationMessage] = useState('');\r\n  const [cameraLoading, setCameraLoading] = useState(false);\r\n  const [videoReady, setVideoReady] = useState(false);\r\n\r\n  // Automatically open camera when component mounts\r\n  useEffect(() => {\r\n    // Open camera automatically after a short delay\r\n    const timer = setTimeout(() => {\r\n      if (!stream && !capturedImage && !cameraLoading) {\r\n        console.log('Auto-opening camera...');\r\n        handleOpenCamera();\r\n      }\r\n    }, 1000);\r\n\r\n    // Clean up camera resources when component unmounts\r\n    return () => {\r\n      clearTimeout(timer);\r\n      if (stream) {\r\n        stream.getTracks().forEach(track => track.stop());\r\n      }\r\n      // Reset video ready state when cleaning up\r\n      setVideoReady(false);\r\n    };\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [stream, capturedImage]);\r\n\r\n  // Effect to handle connecting the stream to the video element\r\n  useEffect(() => {\r\n    if (stream) {\r\n      const videoElement = document.getElementById('camera-preview') as HTMLVideoElement;\r\n      if (videoElement) {\r\n        console.log('Connecting stream to video element');\r\n        videoElement.srcObject = stream;\r\n\r\n        // Check if video already has dimensions (might be ready immediately)\r\n        if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {\r\n          console.log('Video already has dimensions:', {\r\n            width: videoElement.videoWidth,\r\n            height: videoElement.videoHeight\r\n          });\r\n          setVideoReady(true);\r\n        }\r\n\r\n        // Add event listeners to detect when video is ready\r\n        const handleMetadataLoaded = () => {\r\n          console.log('Video metadata loaded via event listener');\r\n          if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {\r\n            setVideoReady(true);\r\n          }\r\n        };\r\n\r\n        const handleCanPlay = () => {\r\n          console.log('Video can play event fired');\r\n          if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {\r\n            setVideoReady(true);\r\n          }\r\n        };\r\n\r\n        videoElement.addEventListener('loadedmetadata', handleMetadataLoaded);\r\n        videoElement.addEventListener('canplay', handleCanPlay);\r\n\r\n        // Clean up event listeners\r\n        return () => {\r\n          videoElement.removeEventListener('loadedmetadata', handleMetadataLoaded);\r\n          videoElement.removeEventListener('canplay', handleCanPlay);\r\n        };\r\n      }\r\n    }\r\n  }, [stream]);\r\n\r\n  const handleOpenCamera = async () => {\r\n    try {\r\n      setCameraError(null);\r\n      setCameraLoading(true);\r\n      setCapturedImage(null); // Clear any previous captured image\r\n      setVideoReady(false); // Reset video ready state\r\n\r\n      // Clear any previous streams\r\n      if (stream) {\r\n        console.log(\"Stopping previous camera stream...\");\r\n        stream.getTracks().forEach(track => {\r\n          console.log(`Stopping track: ${track.kind} - ${track.label}`);\r\n          track.stop();\r\n        });\r\n        setStream(null); // Clear the stream reference\r\n      }\r\n\r\n      console.log(\"Requesting camera access...\");\r\n\r\n      // Use higher quality camera settings while maintaining compatibility\r\n      console.log(\"Using improved camera settings for better quality\");\r\n      const constraints = {\r\n        video: {\r\n          width: { ideal: 1280 },\r\n          height: { ideal: 720 },\r\n          facingMode: 'user' // Front camera for face verification\r\n        },\r\n        audio: false\r\n      };\r\n\r\n      console.log(\"Camera constraints:\", constraints);\r\n\r\n      let cameraStream = null;\r\n\r\n      try {\r\n        // First try with high-quality settings\r\n        console.log(\"Attempting to get high-quality camera stream...\");\r\n        cameraStream = await navigator.mediaDevices.getUserMedia(constraints);\r\n\r\n        // Log the tracks to help with debugging\r\n        const videoTracks = cameraStream.getVideoTracks();\r\n        console.log(`Using video device: ${videoTracks[0].label}`);\r\n        console.log(\"Video track settings:\", videoTracks[0].getSettings());\r\n      } catch (highQualityError) {\r\n        console.warn(\"Failed to get high-quality camera, falling back to basic settings\", highQualityError);\r\n\r\n        // Fall back to basic camera settings\r\n        const basicConstraints = { video: true, audio: false };\r\n        console.log(\"Attempting to get basic camera stream...\");\r\n        cameraStream = await navigator.mediaDevices.getUserMedia(basicConstraints);\r\n\r\n        // Log the fallback tracks\r\n        const fallbackTracks = cameraStream.getVideoTracks();\r\n        console.log(`Using fallback video device: ${fallbackTracks[0].label}`);\r\n        console.log(\"Fallback video track settings:\", fallbackTracks[0].getSettings());\r\n      }\r\n\r\n      if (!cameraStream) {\r\n        throw new Error(\"Failed to initialize camera stream\");\r\n      }\r\n\r\n      // Ensure we have active tracks\r\n      const activeTracks = cameraStream.getVideoTracks();\r\n      if (activeTracks.length === 0 || !activeTracks[0].enabled) {\r\n        console.warn(\"Camera tracks not enabled, attempting to enable...\");\r\n        activeTracks.forEach(track => track.enabled = true);\r\n      }\r\n\r\n      // Set the stream state - this will trigger the video element to update\r\n      console.log(\"Setting camera stream to state...\");\r\n      setStream(cameraStream);\r\n\r\n      // Camera is now active because we have a stream\r\n      setCameraLoading(false);\r\n      console.log(\"Camera activated successfully\");\r\n\r\n      // Video readiness will be detected by the onLoadedMetadata event\r\n\r\n      // Connect to any existing video element immediately\r\n      // We'll use a single attempt with a short delay to avoid race conditions\r\n      setTimeout(() => {\r\n        const videoElement = document.getElementById('camera-preview') as HTMLVideoElement;\r\n\r\n        if (videoElement && cameraStream && videoElement.srcObject !== cameraStream) {\r\n          console.log(\"Connecting stream to existing video element...\");\r\n          videoElement.srcObject = cameraStream;\r\n\r\n          // We'll use a timeout to avoid race conditions with React rendering\r\n          setTimeout(() => {\r\n            if (videoElement && !videoElement.paused) {\r\n              // Video is already playing, don't try to play again\r\n              console.log(\"Video is already playing, skipping play() call\");\r\n              return;\r\n            }\r\n\r\n            // Use a single play attempt with proper error handling\r\n            try {\r\n              const playPromise = videoElement.play();\r\n              if (playPromise !== undefined) {\r\n                playPromise\r\n                  .then(() => console.log(\"Video playback started successfully\"))\r\n                  .catch(err => {\r\n                    if (err.name === 'NotAllowedError') {\r\n                      console.warn(\"Autoplay prevented by browser. User interaction required.\");\r\n                    } else if (err.name === 'AbortError' || err.message.includes('interrupted')) {\r\n                      console.warn(\"Play request was interrupted, this is normal during rapid state changes\");\r\n                    } else {\r\n                      console.error(\"Error playing video after stream set:\", err);\r\n                    }\r\n                  });\r\n              }\r\n            } catch (e) {\r\n              console.warn(\"Exception when trying to play video:\", e);\r\n            }\r\n          }, 100);\r\n        }\r\n      }, 200);\r\n    } catch (error) {\r\n      console.error(\"Error accessing camera:\", error);\r\n      setCameraError(\"Could not access camera. Please check your permissions and try again.\");\r\n      setCameraLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCapturePhoto = () => {\r\n    try {\r\n      // Don't proceed if video isn't ready\r\n      if (!videoReady) {\r\n        console.log(\"Video not ready for capture yet\");\r\n        return;\r\n      }\r\n\r\n      console.log(\"Attempting to capture photo...\");\r\n      setCameraError(null);\r\n\r\n      // Get the video element\r\n      const videoElement = document.getElementById('camera-preview') as HTMLVideoElement;\r\n      if (!videoElement) {\r\n        console.error(\"Video element not found for capture\");\r\n        setCameraError(\"Error: Could not find camera preview\");\r\n        return;\r\n      }\r\n\r\n      console.log(\"Video element found, dimensions:\", {\r\n        videoWidth: videoElement.videoWidth,\r\n        videoHeight: videoElement.videoHeight,\r\n        offsetWidth: videoElement.offsetWidth,\r\n        offsetHeight: videoElement.offsetHeight,\r\n        readyState: videoElement.readyState\r\n      });\r\n\r\n      // Double-check if video is actually playing and has dimensions\r\n      if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {\r\n        console.error(\"Video dimensions are zero. Video may not be playing yet.\");\r\n        setCameraError(\"Camera not ready. Please try refreshing the page and allowing camera access.\");\r\n        setVideoReady(false); // Reset video ready state\r\n        return;\r\n      }\r\n\r\n      // Use fixed dimensions for consistency\r\n      const width = 320;\r\n      const height = 240;\r\n\r\n      // Create canvas with fixed dimensions\r\n      const canvas = document.createElement('canvas');\r\n      canvas.width = width;\r\n      canvas.height = height;\r\n\r\n      try {\r\n        const context = canvas.getContext('2d');\r\n        if (!context) {\r\n          throw new Error(\"Could not get canvas context\");\r\n        }\r\n\r\n        // Draw the current video frame to the canvas\r\n        context.drawImage(videoElement, 0, 0, width, height);\r\n        console.log(\"Image captured successfully with dimensions:\", { width, height });\r\n\r\n        // Get image as base64 string\r\n        const imageData = canvas.toDataURL('image/jpeg', 0.9);\r\n        console.log('Captured image data length:', imageData.length);\r\n\r\n        // Store the image data\r\n        setCapturedImage(imageData);\r\n\r\n        // Keep the camera active but just switch to showing the captured image\r\n        // Don't stop the stream or set cameraActive to false\r\n        // This allows the user to easily switch back to camera mode\r\n      } catch (canvasError) {\r\n        console.error(\"Canvas error:\", canvasError);\r\n        setCameraError(\"Failed to create capture context\");\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error during capture:\", err);\r\n      setCameraError(\"Failed to capture image\");\r\n    }\r\n  };\r\n\r\n  const handleRetake = () => {\r\n    setCapturedImage(null);\r\n    // No need to call handleOpenCamera() since we're keeping the camera active\r\n    // Just clear the captured image to show the camera again\r\n  };\r\n\r\n  const handleVerifyAndUpload = async () => {\r\n    // Prevent multiple uploads by checking if verification is already in progress or successful\r\n    if (verifying || verificationStatus === 'success') {\r\n      console.log('Verification already in progress or completed, ignoring additional upload attempts');\r\n      return;\r\n    }\r\n\r\n    if (!capturedImage) {\r\n      // Face image is required\r\n      setVerificationStatus('error');\r\n      setVerificationMessage('Please capture your face image before proceeding');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setVerifying(true);\r\n      setVerificationStatus('none');\r\n      setVerificationMessage('Verifying your face...');\r\n\r\n      // Process the image data to ensure it's in the correct format\r\n      // The backend expects a base64 string without the data URL prefix\r\n      let imageBase64 = capturedImage;\r\n\r\n      // Remove the data URL prefix if present\r\n      if (imageBase64.startsWith('data:')) {\r\n        imageBase64 = imageBase64.split(',')[1];\r\n      }\r\n\r\n      // Log the start time for performance tracking\r\n      const startTime = Date.now();\r\n\r\n      try {\r\n        // Call the API to verify the face\r\n        const response = await uploadService.verifyFace(imageBase64);\r\n\r\n        // Log the end time and calculate duration\r\n        const endTime = Date.now();\r\n        console.log(`Face verification API call completed in ${endTime - startTime}ms`);\r\n        console.log('API response:', response);\r\n\r\n        setVerifying(false);\r\n        setVerificationStatus('success');\r\n        setVerificationMessage(response.message || 'Face verification successful');\r\n\r\n        // Show success message for a moment before proceeding\r\n        // Use a longer delay to ensure the user sees the success message\r\n        setTimeout(() => {\r\n          console.log('Verification successful, proceeding with upload');\r\n          onUpload();\r\n        }, 2000);\r\n      } catch (apiError: any) {\r\n        console.error('API error during face verification:', apiError);\r\n\r\n        // Log more details about the error\r\n        if (apiError.response) {\r\n          console.error('API response status:', apiError.response.status);\r\n          console.error('API response data:', apiError.response.data);\r\n        } else if (apiError.request) {\r\n          console.error('No response received from API');\r\n        } else {\r\n          console.error('Error message:', apiError.message);\r\n          console.error('Error stack:', apiError.stack);\r\n        }\r\n\r\n        setVerifying(false);\r\n        setVerificationStatus('error');\r\n\r\n        // Show a more detailed error message\r\n        const errorMessage = apiError instanceof Error\r\n          ? apiError.message\r\n          : 'Unknown error during face verification';\r\n\r\n        setVerificationMessage(`Face verification failed: ${errorMessage}. Please try again.`);\r\n      }\r\n    } catch (error: any) {\r\n      setVerifying(false);\r\n      setVerificationStatus('error');\r\n      setVerificationMessage(error instanceof Error ? error.message : 'Face verification failed');\r\n      console.error('Face verification error:', error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]\">\r\n        {/* Close button */}\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-4 right-4 text-gray-800 hover:text-red-600\"\r\n        >\r\n          <X size={24} />\r\n        </button>\r\n\r\n        {/* Header */}\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"flex items-center mr-2\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={32}\r\n              height={32}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n          <h2 className=\"text-xl font-bold\">Face Verification</h2>\r\n          <div className=\"ml-2\">\r\n            <Image\r\n              src=\"/pics/user-profile.png\"\r\n              alt=\"User\"\r\n              width={20}\r\n              height={20}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center mb-6\">\r\n          <Image\r\n            src=\"/pics/user-profile.png\"\r\n            alt=\"User\"\r\n            width={24}\r\n            height={24}\r\n            className=\"object-cover mr-2\"\r\n          />\r\n          <p className=\"text-base\">Verify your face ID</p>\r\n        </div>\r\n\r\n        {/* Camera or captured image display */}\r\n        <div className=\"border border-gray-200 rounded-lg p-4 mb-6\">\r\n          {cameraLoading ? (\r\n            <div className=\"flex flex-col items-center justify-center h-60\">\r\n              <div className=\"w-12 h-12 border-4 border-gray-300 border-t-red-600 rounded-full animate-spin mb-4\"></div>\r\n              <p className=\"text-center text-gray-600\">Initializing camera...</p>\r\n            </div>\r\n          ) : stream ? (\r\n            <div className=\"relative\">\r\n              {/* Always show the video stream when it exists */}\r\n              {!capturedImage && (\r\n                <video\r\n                  id=\"camera-preview\"\r\n                  autoPlay\r\n                  playsInline\r\n                  muted\r\n                  width=\"640\"\r\n                  height=\"480\"\r\n                  className=\"w-full object-contain rounded-lg shadow-md bg-black\"\r\n                  style={{ maxHeight: '300px', minHeight: '200px' }}\r\n                  onLoadedMetadata={(e) => {\r\n                    const video = e.target as HTMLVideoElement;\r\n                    console.log(\"Video metadata loaded, dimensions:\", {\r\n                      videoWidth: video.videoWidth,\r\n                      videoHeight: video.videoHeight\r\n                    });\r\n                    if (video.videoWidth > 0 && video.videoHeight > 0) {\r\n                      setVideoReady(true);\r\n                      // Don't call play() here to avoid conflicts\r\n                    }\r\n                  }}\r\n                  onLoadedData={(e) => {\r\n                    const video = e.target as HTMLVideoElement;\r\n                    console.log(\"Video data loaded, dimensions:\", {\r\n                      videoWidth: video.videoWidth,\r\n                      videoHeight: video.videoHeight\r\n                    });\r\n                    if (video.videoWidth > 0 && video.videoHeight > 0) {\r\n                      setVideoReady(true);\r\n                      // Don't call play() here to avoid conflicts\r\n                    }\r\n                  }}\r\n                  ref={(videoElement) => {\r\n                    if (videoElement && stream) {\r\n                      // Only set srcObject if it's different to avoid unnecessary reloads\r\n                      if (videoElement.srcObject !== stream) {\r\n                        console.log(\"Setting stream to video element in ref callback\");\r\n                        videoElement.srcObject = stream;\r\n\r\n                        // We'll use a timeout to avoid race conditions with React rendering\r\n                        setTimeout(() => {\r\n                          if (videoElement && !videoElement.paused) {\r\n                            // Video is already playing, don't try to play again\r\n                            console.log(\"Video is already playing, skipping play() call\");\r\n                            return;\r\n                          }\r\n\r\n                          // Use a single play attempt with proper error handling\r\n                          try {\r\n                            const playPromise = videoElement.play();\r\n                            if (playPromise !== undefined) {\r\n                              playPromise\r\n                                .then(() => console.log(\"Video playback started successfully\"))\r\n                                .catch(err => {\r\n                                  if (err.name === 'NotAllowedError') {\r\n                                    console.warn(\"Autoplay prevented by browser. User interaction required.\");\r\n                                  } else if (err.name === 'AbortError' || err.message.includes('interrupted')) {\r\n                                    console.warn(\"Play request was interrupted, this is normal during rapid state changes\");\r\n                                  } else {\r\n                                    console.error(\"Error playing video in ref:\", err);\r\n                                  }\r\n                                });\r\n                            }\r\n                          } catch (e) {\r\n                            console.warn(\"Exception when trying to play video:\", e);\r\n                          }\r\n                        }, 100);\r\n                      }\r\n                    }\r\n                  }}\r\n                />\r\n              )}\r\n\r\n              {/* Show captured image when available */}\r\n              {capturedImage && (\r\n                <img\r\n                  src={capturedImage}\r\n                  alt=\"Captured face\"\r\n                  className=\"w-full object-contain rounded-lg shadow-md\"\r\n                  style={{ maxHeight: '300px', minHeight: '200px' }}\r\n                />\r\n              )}\r\n\r\n              {/* Camera controls */}\r\n              <div className=\"absolute bottom-4 left-0 right-0 flex justify-center space-x-4\">\r\n                {!capturedImage ? (\r\n                  <button\r\n                    onClick={handleCapturePhoto}\r\n                    disabled={!videoReady}\r\n                    className={`${videoReady ? 'bg-white' : 'bg-gray-200'} p-3 rounded-full shadow-lg transition-colors duration-300`}\r\n                  >\r\n                    <Camera size={24} className={videoReady ? 'text-red-600' : 'text-gray-400'} />\r\n                  </button>\r\n                ) : (\r\n                  <button\r\n                    onClick={handleRetake}\r\n                    className=\"bg-white p-3 rounded-full shadow-lg\"\r\n                    title=\"Retake photo\"\r\n                  >\r\n                    <Camera size={24} className=\"text-red-600\" />\r\n                  </button>\r\n                )}\r\n              </div>\r\n\r\n              {/* Loading overlay when camera is initializing */}\r\n              {stream && !videoReady && !capturedImage && (\r\n                <div className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg\">\r\n                  <div className=\"text-white text-center p-4\">\r\n                    <div className=\"w-8 h-8 border-4 border-gray-300 border-t-red-600 rounded-full animate-spin mb-2 mx-auto\"></div>\r\n                    <p>Camera initializing...</p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Fallback camera button if video isn't showing but stream exists and no capture button is visible */}\r\n              {stream && !capturedImage && !videoReady && (\r\n                <div className=\"absolute bottom-4 left-0 right-0 flex justify-center\">\r\n                  <button\r\n                    onClick={() => {\r\n                      // Try to reinitialize the camera connection\r\n                      const videoElement = document.getElementById('camera-preview') as HTMLVideoElement;\r\n                      if (videoElement && stream) {\r\n                        console.log(\"Reconnecting stream to video element...\");\r\n\r\n                        // Only set srcObject if it's different\r\n                        if (videoElement.srcObject !== stream) {\r\n                          videoElement.srcObject = stream;\r\n                        }\r\n\r\n                        // We'll use a timeout to avoid race conditions with React rendering\r\n                        setTimeout(() => {\r\n                          if (videoElement && !videoElement.paused) {\r\n                            // Video is already playing, don't try to play again\r\n                            console.log(\"Video is already playing, skipping play() call\");\r\n                            setVideoReady(true);\r\n                            return;\r\n                          }\r\n\r\n                          // Use a single play attempt with proper error handling\r\n                          try {\r\n                            const playPromise = videoElement.play();\r\n                            if (playPromise !== undefined) {\r\n                              playPromise\r\n                                .then(() => {\r\n                                  console.log(\"Video playback started successfully\");\r\n                                  setVideoReady(true);\r\n                                })\r\n                                .catch(err => {\r\n                                  if (err.name === 'NotAllowedError') {\r\n                                    console.warn(\"Autoplay prevented by browser. User interaction required.\");\r\n                                  } else if (err.name === 'AbortError' || err.message.includes('interrupted')) {\r\n                                    console.warn(\"Play request was interrupted, this is normal during rapid state changes\");\r\n                                  } else {\r\n                                    console.error(\"Error playing video:\", err);\r\n                                  }\r\n                                });\r\n                            }\r\n                          } catch (e) {\r\n                            console.warn(\"Exception when trying to play video:\", e);\r\n                          }\r\n                        }, 100);\r\n                      }\r\n                    }}\r\n                    className=\"bg-white p-3 rounded-full shadow-lg\"\r\n                  >\r\n                    <Camera size={24} className=\"text-red-600\" />\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex flex-col items-center justify-center h-60\">\r\n              <div\r\n                onClick={handleOpenCamera}\r\n                className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center cursor-pointer mb-4\"\r\n              >\r\n                <Camera size={32} className=\"text-gray-600\" />\r\n              </div>\r\n              <p className=\"text-center text-gray-600\">\r\n                Open Camera\r\n              </p>\r\n              {cameraError && (\r\n                <p className=\"text-center text-red-500 text-sm mt-2\">\r\n                  {cameraError}\r\n                </p>\r\n              )}\r\n              <p className=\"text-center text-gray-500 text-sm mt-4\">\r\n                This is a one-time face verification process.<br />\r\n                It won't appear again next time.\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Verification status */}\r\n        {verificationStatus !== 'none' && (\r\n          <div className={`mb-4 p-3 rounded-lg flex items-center ${\r\n            verificationStatus === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\r\n          }`}>\r\n            {verificationStatus === 'success' ? (\r\n              <CheckCircle className=\"mr-2 h-5 w-5\" />\r\n            ) : (\r\n              <AlertCircle className=\"mr-2 h-5 w-5\" />\r\n            )}\r\n            <span>{verificationMessage}</span>\r\n          </div>\r\n        )}\r\n\r\n        {/* Action buttons */}\r\n        <div className=\"flex justify-between\">\r\n          {/* Only show back button if verification is not successful */}\r\n          {verificationStatus !== 'success' && (\r\n            <button\r\n              onClick={onBack}\r\n              className=\"flex items-center px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition duration-200\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                className=\"h-5 w-5 mr-1\"\r\n                viewBox=\"0 0 20 20\"\r\n                fill=\"currentColor\"\r\n              >\r\n                <path\r\n                  fillRule=\"evenodd\"\r\n                  d=\"M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z\"\r\n                  clipRule=\"evenodd\"\r\n                />\r\n              </svg>\r\n              Back\r\n            </button>\r\n          )}\r\n\r\n          {/* Show upload button only if verification is not successful and not in progress */}\r\n          {verificationStatus !== 'success' && (\r\n            <button\r\n              onClick={handleVerifyAndUpload}\r\n              disabled={!capturedImage || verifying}\r\n              className={`flex items-center justify-center px-6 py-2 rounded-md ${\r\n                !capturedImage || verifying\r\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n                  : 'bg-red-600 text-white hover:bg-red-700'\r\n              } transition duration-200`}\r\n            >\r\n              {verifying ? (\r\n                <>\r\n                  <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                  Verifying...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  Verify & Upload\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"h-5 w-5 ml-1\"\r\n                    viewBox=\"0 0 20 20\"\r\n                    fill=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                </>\r\n              )}\r\n            </button>\r\n          )}\r\n\r\n          {/* Show success message when verification is successful */}\r\n          {verificationStatus === 'success' && (\r\n            <div className=\"w-full text-center text-green-600 font-medium\">\r\n              <CheckCircle className=\"inline-block mr-2 h-5 w-5\" />\r\n              Verification successful! Uploading your content...\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FaceVerification;\r\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAaA,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,MAAM,EACN,OAAO,EACR;;IACC,yEAAyE;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3F,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,gDAAgD;YAChD,MAAM,QAAQ;oDAAW;oBACvB,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,eAAe;wBAC/C,QAAQ,GAAG,CAAC;wBACZ;oBACF;gBACF;mDAAG;YAEH,oDAAoD;YACpD;8CAAO;oBACL,aAAa;oBACb,IAAI,QAAQ;wBACV,OAAO,SAAS,GAAG,OAAO;0DAAC,CAAA,QAAS,MAAM,IAAI;;oBAChD;oBACA,2CAA2C;oBAC3C,cAAc;gBAChB;;QACF,uDAAuD;QACvD;qCAAG;QAAC;QAAQ;KAAc;IAE1B,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ;gBACV,MAAM,eAAe,SAAS,cAAc,CAAC;gBAC7C,IAAI,cAAc;oBAChB,QAAQ,GAAG,CAAC;oBACZ,aAAa,SAAS,GAAG;oBAEzB,qEAAqE;oBACrE,IAAI,aAAa,UAAU,GAAG,KAAK,aAAa,WAAW,GAAG,GAAG;wBAC/D,QAAQ,GAAG,CAAC,iCAAiC;4BAC3C,OAAO,aAAa,UAAU;4BAC9B,QAAQ,aAAa,WAAW;wBAClC;wBACA,cAAc;oBAChB;oBAEA,oDAAoD;oBACpD,MAAM;2EAAuB;4BAC3B,QAAQ,GAAG,CAAC;4BACZ,IAAI,aAAa,UAAU,GAAG,KAAK,aAAa,WAAW,GAAG,GAAG;gCAC/D,cAAc;4BAChB;wBACF;;oBAEA,MAAM;oEAAgB;4BACpB,QAAQ,GAAG,CAAC;4BACZ,IAAI,aAAa,UAAU,GAAG,KAAK,aAAa,WAAW,GAAG,GAAG;gCAC/D,cAAc;4BAChB;wBACF;;oBAEA,aAAa,gBAAgB,CAAC,kBAAkB;oBAChD,aAAa,gBAAgB,CAAC,WAAW;oBAEzC,2BAA2B;oBAC3B;sDAAO;4BACL,aAAa,mBAAmB,CAAC,kBAAkB;4BACnD,aAAa,mBAAmB,CAAC,WAAW;wBAC9C;;gBACF;YACF;QACF;qCAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB;QACvB,IAAI;YACF,eAAe;YACf,iBAAiB;YACjB,iBAAiB,OAAO,oCAAoC;YAC5D,cAAc,QAAQ,0BAA0B;YAEhD,6BAA6B;YAC7B,IAAI,QAAQ;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA;oBACzB,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC,GAAG,EAAE,MAAM,KAAK,EAAE;oBAC5D,MAAM,IAAI;gBACZ;gBACA,UAAU,OAAO,6BAA6B;YAChD;YAEA,QAAQ,GAAG,CAAC;YAEZ,qEAAqE;YACrE,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc;gBAClB,OAAO;oBACL,OAAO;wBAAE,OAAO;oBAAK;oBACrB,QAAQ;wBAAE,OAAO;oBAAI;oBACrB,YAAY,OAAO,qCAAqC;gBAC1D;gBACA,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,IAAI,eAAe;YAEnB,IAAI;gBACF,uCAAuC;gBACvC,QAAQ,GAAG,CAAC;gBACZ,eAAe,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAEzD,wCAAwC;gBACxC,MAAM,cAAc,aAAa,cAAc;gBAC/C,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE;gBACzD,QAAQ,GAAG,CAAC,yBAAyB,WAAW,CAAC,EAAE,CAAC,WAAW;YACjE,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,IAAI,CAAC,qEAAqE;gBAElF,qCAAqC;gBACrC,MAAM,mBAAmB;oBAAE,OAAO;oBAAM,OAAO;gBAAM;gBACrD,QAAQ,GAAG,CAAC;gBACZ,eAAe,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAEzD,0BAA0B;gBAC1B,MAAM,iBAAiB,aAAa,cAAc;gBAClD,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE;gBACrE,QAAQ,GAAG,CAAC,kCAAkC,cAAc,CAAC,EAAE,CAAC,WAAW;YAC7E;YAEA,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAEA,+BAA+B;YAC/B,MAAM,eAAe,aAAa,cAAc;YAChD,IAAI,aAAa,MAAM,KAAK,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE;gBACzD,QAAQ,IAAI,CAAC;gBACb,aAAa,OAAO,CAAC,CAAA,QAAS,MAAM,OAAO,GAAG;YAChD;YAEA,uEAAuE;YACvE,QAAQ,GAAG,CAAC;YACZ,UAAU;YAEV,gDAAgD;YAChD,iBAAiB;YACjB,QAAQ,GAAG,CAAC;YAEZ,iEAAiE;YAEjE,oDAAoD;YACpD,yEAAyE;YACzE,WAAW;gBACT,MAAM,eAAe,SAAS,cAAc,CAAC;gBAE7C,IAAI,gBAAgB,gBAAgB,aAAa,SAAS,KAAK,cAAc;oBAC3E,QAAQ,GAAG,CAAC;oBACZ,aAAa,SAAS,GAAG;oBAEzB,oEAAoE;oBACpE,WAAW;wBACT,IAAI,gBAAgB,CAAC,aAAa,MAAM,EAAE;4BACxC,oDAAoD;4BACpD,QAAQ,GAAG,CAAC;4BACZ;wBACF;wBAEA,uDAAuD;wBACvD,IAAI;4BACF,MAAM,cAAc,aAAa,IAAI;4BACrC,IAAI,gBAAgB,WAAW;gCAC7B,YACG,IAAI,CAAC,IAAM,QAAQ,GAAG,CAAC,wCACvB,KAAK,CAAC,CAAA;oCACL,IAAI,IAAI,IAAI,KAAK,mBAAmB;wCAClC,QAAQ,IAAI,CAAC;oCACf,OAAO,IAAI,IAAI,IAAI,KAAK,gBAAgB,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;wCAC3E,QAAQ,IAAI,CAAC;oCACf,OAAO;wCACL,QAAQ,KAAK,CAAC,yCAAyC;oCACzD;gCACF;4BACJ;wBACF,EAAE,OAAO,GAAG;4BACV,QAAQ,IAAI,CAAC,wCAAwC;wBACvD;oBACF,GAAG;gBACL;YACF,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,eAAe;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,qCAAqC;YACrC,IAAI,CAAC,YAAY;gBACf,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,eAAe;YAEf,wBAAwB;YACxB,MAAM,eAAe,SAAS,cAAc,CAAC;YAC7C,IAAI,CAAC,cAAc;gBACjB,QAAQ,KAAK,CAAC;gBACd,eAAe;gBACf;YACF;YAEA,QAAQ,GAAG,CAAC,oCAAoC;gBAC9C,YAAY,aAAa,UAAU;gBACnC,aAAa,aAAa,WAAW;gBACrC,aAAa,aAAa,WAAW;gBACrC,cAAc,aAAa,YAAY;gBACvC,YAAY,aAAa,UAAU;YACrC;YAEA,+DAA+D;YAC/D,IAAI,aAAa,UAAU,KAAK,KAAK,aAAa,WAAW,KAAK,GAAG;gBACnE,QAAQ,KAAK,CAAC;gBACd,eAAe;gBACf,cAAc,QAAQ,0BAA0B;gBAChD;YACF;YAEA,uCAAuC;YACvC,MAAM,QAAQ;YACd,MAAM,SAAS;YAEf,sCAAsC;YACtC,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,KAAK,GAAG;YACf,OAAO,MAAM,GAAG;YAEhB,IAAI;gBACF,MAAM,UAAU,OAAO,UAAU,CAAC;gBAClC,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,MAAM;gBAClB;gBAEA,6CAA6C;gBAC7C,QAAQ,SAAS,CAAC,cAAc,GAAG,GAAG,OAAO;gBAC7C,QAAQ,GAAG,CAAC,gDAAgD;oBAAE;oBAAO;gBAAO;gBAE5E,6BAA6B;gBAC7B,MAAM,YAAY,OAAO,SAAS,CAAC,cAAc;gBACjD,QAAQ,GAAG,CAAC,+BAA+B,UAAU,MAAM;gBAE3D,uBAAuB;gBACvB,iBAAiB;YAEjB,uEAAuE;YACvE,qDAAqD;YACrD,4DAA4D;YAC9D,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,eAAe;YACjB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,iBAAiB;IACjB,2EAA2E;IAC3E,yDAAyD;IAC3D;IAEA,MAAM,wBAAwB;QAC5B,4FAA4F;QAC5F,IAAI,aAAa,uBAAuB,WAAW;YACjD,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,eAAe;YAClB,yBAAyB;YACzB,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QAEA,IAAI;YACF,aAAa;YACb,sBAAsB;YACtB,uBAAuB;YAEvB,8DAA8D;YAC9D,kEAAkE;YAClE,IAAI,cAAc;YAElB,wCAAwC;YACxC,IAAI,YAAY,UAAU,CAAC,UAAU;gBACnC,cAAc,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE;YACzC;YAEA,8CAA8C;YAC9C,MAAM,YAAY,KAAK,GAAG;YAE1B,IAAI;gBACF,kCAAkC;gBAClC,MAAM,WAAW,MAAM,kHAAA,CAAA,gBAAa,CAAC,UAAU,CAAC;gBAEhD,0CAA0C;gBAC1C,MAAM,UAAU,KAAK,GAAG;gBACxB,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU,UAAU,EAAE,CAAC;gBAC9E,QAAQ,GAAG,CAAC,iBAAiB;gBAE7B,aAAa;gBACb,sBAAsB;gBACtB,uBAAuB,SAAS,OAAO,IAAI;gBAE3C,sDAAsD;gBACtD,iEAAiE;gBACjE,WAAW;oBACT,QAAQ,GAAG,CAAC;oBACZ;gBACF,GAAG;YACL,EAAE,OAAO,UAAe;gBACtB,QAAQ,KAAK,CAAC,uCAAuC;gBAErD,mCAAmC;gBACnC,IAAI,SAAS,QAAQ,EAAE;oBACrB,QAAQ,KAAK,CAAC,wBAAwB,SAAS,QAAQ,CAAC,MAAM;oBAC9D,QAAQ,KAAK,CAAC,sBAAsB,SAAS,QAAQ,CAAC,IAAI;gBAC5D,OAAO,IAAI,SAAS,OAAO,EAAE;oBAC3B,QAAQ,KAAK,CAAC;gBAChB,OAAO;oBACL,QAAQ,KAAK,CAAC,kBAAkB,SAAS,OAAO;oBAChD,QAAQ,KAAK,CAAC,gBAAgB,SAAS,KAAK;gBAC9C;gBAEA,aAAa;gBACb,sBAAsB;gBAEtB,qCAAqC;gBACrC,MAAM,eAAe,oBAAoB,QACrC,SAAS,OAAO,GAChB;gBAEJ,uBAAuB,CAAC,0BAA0B,EAAE,aAAa,mBAAmB,CAAC;YACvF;QACF,EAAE,OAAO,OAAY;YACnB,aAAa;YACb,sBAAsB;YACtB,uBAAuB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChE,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,6LAAC;4BAAE,WAAU;sCAAY;;;;;;;;;;;;8BAI3B,6LAAC;oBAAI,WAAU;8BACZ,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;+BAEzC,uBACF,6LAAC;wBAAI,WAAU;;4BAEZ,CAAC,+BACA,6LAAC;gCACC,IAAG;gCACH,QAAQ;gCACR,WAAW;gCACX,KAAK;gCACL,OAAM;gCACN,QAAO;gCACP,WAAU;gCACV,OAAO;oCAAE,WAAW;oCAAS,WAAW;gCAAQ;gCAChD,kBAAkB,CAAC;oCACjB,MAAM,QAAQ,EAAE,MAAM;oCACtB,QAAQ,GAAG,CAAC,sCAAsC;wCAChD,YAAY,MAAM,UAAU;wCAC5B,aAAa,MAAM,WAAW;oCAChC;oCACA,IAAI,MAAM,UAAU,GAAG,KAAK,MAAM,WAAW,GAAG,GAAG;wCACjD,cAAc;oCACd,4CAA4C;oCAC9C;gCACF;gCACA,cAAc,CAAC;oCACb,MAAM,QAAQ,EAAE,MAAM;oCACtB,QAAQ,GAAG,CAAC,kCAAkC;wCAC5C,YAAY,MAAM,UAAU;wCAC5B,aAAa,MAAM,WAAW;oCAChC;oCACA,IAAI,MAAM,UAAU,GAAG,KAAK,MAAM,WAAW,GAAG,GAAG;wCACjD,cAAc;oCACd,4CAA4C;oCAC9C;gCACF;gCACA,KAAK,CAAC;oCACJ,IAAI,gBAAgB,QAAQ;wCAC1B,oEAAoE;wCACpE,IAAI,aAAa,SAAS,KAAK,QAAQ;4CACrC,QAAQ,GAAG,CAAC;4CACZ,aAAa,SAAS,GAAG;4CAEzB,oEAAoE;4CACpE,WAAW;gDACT,IAAI,gBAAgB,CAAC,aAAa,MAAM,EAAE;oDACxC,oDAAoD;oDACpD,QAAQ,GAAG,CAAC;oDACZ;gDACF;gDAEA,uDAAuD;gDACvD,IAAI;oDACF,MAAM,cAAc,aAAa,IAAI;oDACrC,IAAI,gBAAgB,WAAW;wDAC7B,YACG,IAAI,CAAC,IAAM,QAAQ,GAAG,CAAC,wCACvB,KAAK,CAAC,CAAA;4DACL,IAAI,IAAI,IAAI,KAAK,mBAAmB;gEAClC,QAAQ,IAAI,CAAC;4DACf,OAAO,IAAI,IAAI,IAAI,KAAK,gBAAgB,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;gEAC3E,QAAQ,IAAI,CAAC;4DACf,OAAO;gEACL,QAAQ,KAAK,CAAC,+BAA+B;4DAC/C;wDACF;oDACJ;gDACF,EAAE,OAAO,GAAG;oDACV,QAAQ,IAAI,CAAC,wCAAwC;gDACvD;4CACF,GAAG;wCACL;oCACF;gCACF;;;;;;4BAKH,+BACC,6LAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;gCACV,OAAO;oCAAE,WAAW;oCAAS,WAAW;gCAAQ;;;;;;0CAKpD,6LAAC;gCAAI,WAAU;0CACZ,CAAC,8BACA,6LAAC;oCACC,SAAS;oCACT,UAAU,CAAC;oCACX,WAAW,GAAG,aAAa,aAAa,cAAc,0DAA0D,CAAC;8CAEjH,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAW,aAAa,iBAAiB;;;;;;;;;;yDAG7D,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;4BAMjC,UAAU,CAAC,cAAc,CAAC,+BACzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAE;;;;;;;;;;;;;;;;;4BAMR,UAAU,CAAC,iBAAiB,CAAC,4BAC5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP,4CAA4C;wCAC5C,MAAM,eAAe,SAAS,cAAc,CAAC;wCAC7C,IAAI,gBAAgB,QAAQ;4CAC1B,QAAQ,GAAG,CAAC;4CAEZ,uCAAuC;4CACvC,IAAI,aAAa,SAAS,KAAK,QAAQ;gDACrC,aAAa,SAAS,GAAG;4CAC3B;4CAEA,oEAAoE;4CACpE,WAAW;gDACT,IAAI,gBAAgB,CAAC,aAAa,MAAM,EAAE;oDACxC,oDAAoD;oDACpD,QAAQ,GAAG,CAAC;oDACZ,cAAc;oDACd;gDACF;gDAEA,uDAAuD;gDACvD,IAAI;oDACF,MAAM,cAAc,aAAa,IAAI;oDACrC,IAAI,gBAAgB,WAAW;wDAC7B,YACG,IAAI,CAAC;4DACJ,QAAQ,GAAG,CAAC;4DACZ,cAAc;wDAChB,GACC,KAAK,CAAC,CAAA;4DACL,IAAI,IAAI,IAAI,KAAK,mBAAmB;gEAClC,QAAQ,IAAI,CAAC;4DACf,OAAO,IAAI,IAAI,IAAI,KAAK,gBAAgB,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;gEAC3E,QAAQ,IAAI,CAAC;4DACf,OAAO;gEACL,QAAQ,KAAK,CAAC,wBAAwB;4DACxC;wDACF;oDACJ;gDACF,EAAE,OAAO,GAAG;oDACV,QAAQ,IAAI,CAAC,wCAAwC;gDACvD;4CACF,GAAG;wCACL;oCACF;oCACA,WAAU;8CAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;6CAMpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;4BAGxC,6BACC,6LAAC;gCAAE,WAAU;0CACV;;;;;;0CAGL,6LAAC;gCAAE,WAAU;;oCAAyC;kDACP,6LAAC;;;;;oCAAK;;;;;;;;;;;;;;;;;;gBAQ1D,uBAAuB,wBACtB,6LAAC;oBAAI,WAAW,CAAC,sCAAsC,EACrD,uBAAuB,YAAY,gCAAgC,2BACnE;;wBACC,uBAAuB,0BACtB,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCAEzB,6LAAC;sCAAM;;;;;;;;;;;;8BAKX,6LAAC;oBAAI,WAAU;;wBAEZ,uBAAuB,2BACtB,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;oCACC,OAAM;oCACN,WAAU;oCACV,SAAQ;oCACR,MAAK;8CAEL,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAEP;;;;;;;wBAMT,uBAAuB,2BACtB,6LAAC;4BACC,SAAS;4BACT,UAAU,CAAC,iBAAiB;4BAC5B,WAAW,CAAC,sDAAsD,EAChE,CAAC,iBAAiB,YACd,iDACA,yCACL,wBAAwB,CAAC;sCAEzB,0BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAA0F;;6DAI3G;;oCAAE;kDAEA,6LAAC;wCACC,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,MAAK;kDAEL,cAAA,6LAAC;4CACC,UAAS;4CACT,GAAE;4CACF,UAAS;;;;;;;;;;;;;;;;;;wBASpB,uBAAuB,2BACtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;;;;;;;;;;;;;;;;;;AAQnE;GA7rBM;KAAA;uCA+rBS", "debugId": null}}, {"offset": {"line": 7484, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7490, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/upload/UploadProgress.tsx"], "sourcesContent": ["// components/upload/UploadProgress.tsx\r\n'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport { X, Upload, CheckCircle, RefreshCw } from 'lucide-react';\r\nimport { useUpload } from '../../contexts/UploadContexts';\r\nimport { handleUploadError } from '../../utils/uploadErrorHandler';\r\n\r\ninterface UploadProgressProps {\r\n  onClose: () => void;\r\n  onGoBack?: () => void;\r\n}\r\n\r\nconst UploadProgress: React.FC<UploadProgressProps> = ({ onClose, onGoBack }) => {\r\n  const { state, resetUpload, startUpload } = useUpload();\r\n  const [retrying, setRetrying] = useState(false);\r\n  const [retryCount, setRetryCount] = useState(0);\r\n\r\n  // Format progress as a percentage\r\n  const progressPercentage = Math.min(Math.round(state.progress), 100);\r\n  const isComplete = state.step === 'complete';\r\n  const hasError = state.step === 'error';\r\n\r\n  // Reset retrying state when error state changes\r\n  useEffect(() => {\r\n    if (!hasError) {\r\n      setRetrying(false);\r\n    }\r\n  }, [hasError]);\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]\">\r\n        {/* Close button - only show when complete or error */}\r\n        {(isComplete || hasError) && (\r\n          <button\r\n            onClick={onClose}\r\n            className=\"absolute top-4 right-4 text-gray-800 hover:text-red-600\"\r\n          >\r\n            <X size={24} />\r\n          </button>\r\n        )}\r\n\r\n        {/* Header */}\r\n        <div className=\"flex items-center mb-6\">\r\n          <div className=\"flex items-center mr-2\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={32}\r\n              height={32}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n          <h2 className=\"text-xl font-bold\">\r\n            {hasError ? 'Upload Failed' : isComplete ? 'Upload Complete' : 'Uploading...'}\r\n          </h2>\r\n          <div className=\"ml-2\">\r\n            <Image\r\n              src=\"/pics/umoments.png\"\r\n              alt=\"Moments\"\r\n              width={20}\r\n              height={20}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Progress indicator */}\r\n        <div className=\"mb-8\">\r\n          {hasError ? (\r\n            <div className=\"bg-red-100 text-red-800 p-4 rounded-lg mb-4\">\r\n              <p className=\"font-medium\">Error uploading your content</p>\r\n              <p className=\"text-sm mt-1\">{state.error || 'An unknown error occurred'}</p>\r\n\r\n              {/* Error type specific guidance */}\r\n              {state.error?.includes('title') && (\r\n                <>\r\n                  <p className=\"text-sm mt-2 font-medium text-red-700\">\r\n                    Please provide a title for your upload\r\n                  </p>\r\n                  <p className=\"text-sm mt-1\">\r\n                    Please go back to the personal details page and provide a title for your upload.\r\n                  </p>\r\n                </>\r\n              )}\r\n              {(state.error?.includes('Network') || state.error?.includes('network')) && (\r\n                <>\r\n                  <p className=\"text-sm mt-2 font-medium text-red-700\">\r\n                    Network error detected\r\n                  </p>\r\n                  <p className=\"text-sm mt-1\">\r\n                    Please check your internet connection and try again. If the problem persists, try using a different network or browser.\r\n                  </p>\r\n                </>\r\n              )}\r\n              {state.error?.includes('CORS') && (\r\n                <>\r\n                  <p className=\"text-sm mt-2 font-medium text-red-700\">\r\n                    Browser connection issue\r\n                  </p>\r\n                  <p className=\"text-sm mt-1\">\r\n                    There was a problem connecting to our servers. Please try again or use a different browser like Chrome.\r\n                  </p>\r\n                </>\r\n              )}\r\n              {state.error?.includes('timeout') && (\r\n                <>\r\n                  <p className=\"text-sm mt-2 font-medium text-red-700\">\r\n                    Upload timed out\r\n                  </p>\r\n                  <p className=\"text-sm mt-1\">\r\n                    The upload took too long to complete. This might be due to a slow internet connection or a very large file.\r\n                  </p>\r\n                </>\r\n              )}\r\n              {state.error?.includes('Permission denied') && (\r\n                <>\r\n                  <p className=\"text-sm mt-2 font-medium text-red-700\">\r\n                    Upload permission error\r\n                  </p>\r\n                  <p className=\"text-sm mt-1\">\r\n                    The upload link may have expired. Please try again.\r\n                  </p>\r\n                </>\r\n              )}\r\n              {state.error?.includes('too large') && (\r\n                <>\r\n                  <p className=\"text-sm mt-2 font-medium text-red-700\">\r\n                    File size limit exceeded\r\n                  </p>\r\n                  <p className=\"text-sm mt-1\">\r\n                    The file you're trying to upload is too large. Please try a smaller file or compress it.\r\n                  </p>\r\n                </>\r\n              )}\r\n            </div>\r\n          ) : isComplete ? (\r\n            <div className=\"flex flex-col items-center justify-center py-8\">\r\n              <div className=\"bg-green-100 rounded-full p-4 mb-4\">\r\n                <CheckCircle size={48} className=\"text-green-600\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-medium text-green-800 mb-2\">Upload Successful!</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Your content has been uploaded successfully and is now being processed.\r\n                It will be available on your profile shortly.\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <div className=\"flex justify-between mb-2\">\r\n                <span className=\"text-sm font-medium\">Uploading your content...</span>\r\n                <span className=\"text-sm font-medium\">{progressPercentage}%</span>\r\n              </div>\r\n              <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n                <div\r\n                  className=\"bg-red-600 h-2.5 rounded-full transition-all duration-300 ease-in-out\"\r\n                  style={{ width: `${progressPercentage}%` }}\r\n                ></div>\r\n              </div>\r\n              <div className=\"mt-4 flex items-center justify-center\">\r\n                <Upload className=\"animate-bounce text-red-600 mr-2\" size={20} />\r\n                <p className=\"text-gray-600\">\r\n                  {progressPercentage < 20 && \"Preparing your files...\"}\r\n                  {progressPercentage >= 20 && progressPercentage < 70 && \"Uploading main content...\"}\r\n                  {progressPercentage >= 70 && progressPercentage < 90 && \"Uploading thumbnail...\"}\r\n                  {progressPercentage >= 90 && \"Finalizing your upload...\"}\r\n                </p>\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        {/* Action buttons - only show when complete or error */}\r\n        {(isComplete || hasError) && (\r\n          <div className=\"flex justify-center gap-4\">\r\n            {hasError && (\r\n              <button\r\n                onClick={onGoBack || onClose} // Go back to personal details if available, otherwise just close\r\n                className=\"px-6 py-2 border border-red-600 text-red-600 rounded-md hover:bg-red-50 transition duration-200\"\r\n              >\r\n                Go Back\r\n              </button>\r\n            )}\r\n            <button\r\n              onClick={hasError ? async () => {\r\n                // If it's a network error, try to retry the upload automatically\r\n                if (state.error?.includes('Network') || state.error?.includes('network') ||\r\n                    state.error?.includes('CORS') || state.error?.includes('timeout')) {\r\n                  setRetrying(true);\r\n                  setRetryCount(prev => prev + 1);\r\n                  try {\r\n                    await startUpload();\r\n                  } catch (error) {\r\n                    console.error('Retry failed:', error);\r\n                  } finally {\r\n                    setRetrying(false);\r\n                  }\r\n                } else {\r\n                  onClose();\r\n                }\r\n              } : onClose}\r\n              className=\"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition duration-200 flex items-center justify-center\"\r\n              disabled={retrying}\r\n            >\r\n              {hasError ? (\r\n                retrying ? (\r\n                  <>\r\n                    <RefreshCw className=\"animate-spin mr-2\" size={16} />\r\n                    Retrying...\r\n                  </>\r\n                ) : (\r\n                  'Try Again'\r\n                )\r\n              ) : (\r\n                'Done'\r\n              )}\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UploadProgress;"], "names": [], "mappings": "AAAA,uCAAuC;;;;;AAGvC;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAaA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE;;IAC1E,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kCAAkC;IAClC,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,MAAM,QAAQ,GAAG;IAChE,MAAM,aAAa,MAAM,IAAI,KAAK;IAClC,MAAM,WAAW,MAAM,IAAI,KAAK;IAEhC,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,UAAU;gBACb,YAAY;YACd;QACF;mCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;gBAEZ,CAAC,cAAc,QAAQ,mBACtB,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAKb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAG,WAAU;sCACX,WAAW,kBAAkB,aAAa,oBAAoB;;;;;;sCAEjE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;8BAMhB,6LAAC;oBAAI,WAAU;8BACZ,yBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAc;;;;;;0CAC3B,6LAAC;gCAAE,WAAU;0CAAgB,MAAM,KAAK,IAAI;;;;;;4BAG3C,MAAM,KAAK,EAAE,SAAS,0BACrB;;kDACE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;4BAK/B,CAAC,MAAM,KAAK,EAAE,SAAS,cAAc,MAAM,KAAK,EAAE,SAAS,UAAU,mBACpE;;kDACE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;4BAK/B,MAAM,KAAK,EAAE,SAAS,yBACrB;;kDACE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;4BAK/B,MAAM,KAAK,EAAE,SAAS,4BACrB;;kDACE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;4BAK/B,MAAM,KAAK,EAAE,SAAS,sCACrB;;kDACE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;4BAK/B,MAAM,KAAK,EAAE,SAAS,8BACrB;;kDACE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;+BAMhC,2BACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAEnC,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CACxD,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;6CAM3C;;0CACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,6LAAC;wCAAK,WAAU;;4CAAuB;4CAAmB;;;;;;;;;;;;;0CAE5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAG7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;wCAAmC,MAAM;;;;;;kDAC3D,6LAAC;wCAAE,WAAU;;4CACV,qBAAqB,MAAM;4CAC3B,sBAAsB,MAAM,qBAAqB,MAAM;4CACvD,sBAAsB,MAAM,qBAAqB,MAAM;4CACvD,sBAAsB,MAAM;;;;;;;;;;;;;;;;;;;;gBAQtC,CAAC,cAAc,QAAQ,mBACtB,6LAAC;oBAAI,WAAU;;wBACZ,0BACC,6LAAC;4BACC,SAAS,YAAY;4BACrB,WAAU;sCACX;;;;;;sCAIH,6LAAC;4BACC,SAAS,WAAW;gCAClB,iEAAiE;gCACjE,IAAI,MAAM,KAAK,EAAE,SAAS,cAAc,MAAM,KAAK,EAAE,SAAS,cAC1D,MAAM,KAAK,EAAE,SAAS,WAAW,MAAM,KAAK,EAAE,SAAS,YAAY;oCACrE,YAAY;oCACZ,cAAc,CAAA,OAAQ,OAAO;oCAC7B,IAAI;wCACF,MAAM;oCACR,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,iBAAiB;oCACjC,SAAU;wCACR,YAAY;oCACd;gCACF,OAAO;oCACL;gCACF;4BACF,IAAI;4BACJ,WAAU;4BACV,UAAU;sCAET,WACC,yBACE;;kDACE,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;wCAAoB,MAAM;;;;;;oCAAM;;+CAIvD,cAGF;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GAlNM;;QACwC,8HAAA,CAAA,YAAS;;;KADjD;uCAoNS", "debugId": null}}, {"offset": {"line": 7949, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7955, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/ui/CustomAlert.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport { X, AlertTriangle, CheckCircle, Info } from 'lucide-react';\r\n\r\ninterface CustomAlertProps {\r\n  title: string;\r\n  message: string;\r\n  type?: 'error' | 'warning' | 'success' | 'info';\r\n  onClose: () => void;\r\n  onConfirm?: () => void;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n}\r\n\r\nconst CustomAlert: React.FC<CustomAlertProps> = ({\r\n  title,\r\n  message,\r\n  type = 'info',\r\n  onClose,\r\n  onConfirm,\r\n  confirmText = 'OK',\r\n  cancelText = 'Cancel'\r\n}) => {\r\n  const [isVisible, setIsVisible] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // Prevent scrolling of the background when alert is open\r\n    document.body.style.overflow = 'hidden';\r\n    return () => {\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, []);\r\n\r\n  const handleClose = () => {\r\n    setIsVisible(false);\r\n    setTimeout(() => {\r\n      onClose();\r\n    }, 300); // Match transition duration\r\n  };\r\n\r\n  const handleConfirm = () => {\r\n    if (onConfirm) {\r\n      onConfirm();\r\n    }\r\n    handleClose();\r\n  };\r\n\r\n  // Determine icon and colors based on type\r\n  const getTypeStyles = () => {\r\n    switch (type) {\r\n      case 'error':\r\n        return {\r\n          icon: <AlertTriangle size={20} className=\"text-red-600\" />,\r\n          confirmBtnColor: 'bg-red-600 hover:bg-red-700',\r\n        };\r\n      case 'warning':\r\n        return {\r\n          icon: <AlertTriangle size={20} className=\"text-red-600\" />,\r\n          confirmBtnColor: 'bg-red-600 hover:bg-red-700',\r\n        };\r\n      case 'success':\r\n        return {\r\n          icon: <CheckCircle size={20} className=\"text-red-600\" />,\r\n          confirmBtnColor: 'bg-red-600 hover:bg-red-700',\r\n        };\r\n      case 'info':\r\n      default:\r\n        return {\r\n          icon: <Info size={20} className=\"text-red-600\" />,\r\n          confirmBtnColor: 'bg-red-600 hover:bg-red-700',\r\n        };\r\n    }\r\n  };\r\n\r\n  const styles = getTypeStyles();\r\n\r\n  return (\r\n    <div\r\n      className={`fixed inset-x-0 top-0 flex justify-center z-[9999] transition-opacity duration-300 ${\r\n        isVisible ? 'opacity-100' : 'opacity-0'\r\n      }`}\r\n    >\r\n      <div\r\n        className={`bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-3 sm:p-4 relative overflow-y-auto max-h-[50vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%] transition-transform duration-300 shadow-lg mt-4 ${\r\n          isVisible ? 'translate-y-0' : '-translate-y-full'\r\n        }`}\r\n      >\r\n        {/* Close button */}\r\n        <button\r\n          onClick={handleClose}\r\n          className=\"absolute top-2 right-2 text-gray-800 hover:text-red-600\"\r\n        >\r\n          <X size={18} />\r\n        </button>\r\n\r\n        {/* Header */}\r\n        <div className=\"flex items-center mb-3\">\r\n          <div className=\"flex items-center mr-2\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={24}\r\n              height={24}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n          <h2 className=\"text-lg font-bold\">{title}</h2>\r\n        </div>\r\n\r\n        {/* Alert content */}\r\n        <div className=\"mb-4\">\r\n          <div className=\"flex items-start\">\r\n            <div className=\"flex-shrink-0 mr-2\">\r\n              {styles.icon}\r\n            </div>\r\n            <div className=\"flex-1\">\r\n              <p className=\"whitespace-pre-line text-gray-700 text-sm\">{message}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Action buttons */}\r\n        <div className=\"flex justify-end space-x-3\">\r\n          {onConfirm && (\r\n            <button\r\n              onClick={handleClose}\r\n              className=\"flex items-center justify-center px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm\"\r\n            >\r\n              {cancelText}\r\n            </button>\r\n          )}\r\n          <button\r\n            onClick={handleConfirm}\r\n            className={`flex items-center justify-center px-4 py-1.5 text-white rounded-md text-sm ${styles.confirmBtnColor}`}\r\n          >\r\n            {confirmText}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomAlert;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAgBA,MAAM,cAA0C,CAAC,EAC/C,KAAK,EACL,OAAO,EACP,OAAO,MAAM,EACb,OAAO,EACP,SAAS,EACT,cAAc,IAAI,EAClB,aAAa,QAAQ,EACtB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,yDAAyD;YACzD,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B;yCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;gCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;YACT;QACF,GAAG,MAAM,4BAA4B;IACvC;IAEA,MAAM,gBAAgB;QACpB,IAAI,WAAW;YACb;QACF;QACA;IACF;IAEA,0CAA0C;IAC1C,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,MAAM;wBAAI,WAAU;;;;;;oBACzC,iBAAiB;gBACnB;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,MAAM;wBAAI,WAAU;;;;;;oBACzC,iBAAiB;gBACnB;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,8NAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAU;;;;;;oBACvC,iBAAiB;gBACnB;YACF,KAAK;YACL;gBACE,OAAO;oBACL,oBAAM,6LAAC,qMAAA,CAAA,OAAI;wBAAC,MAAM;wBAAI,WAAU;;;;;;oBAChC,iBAAiB;gBACnB;QACJ;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,6LAAC;QACC,WAAW,CAAC,mFAAmF,EAC7F,YAAY,gBAAgB,aAC5B;kBAEF,cAAA,6LAAC;YACC,WAAW,CAAC,2LAA2L,EACrM,YAAY,kBAAkB,qBAC9B;;8BAGF,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;;;;;;;8BAIrC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI;;;;;;0CAEd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;;;;;;;;;;;;;;;;;8BAMhE,6LAAC;oBAAI,WAAU;;wBACZ,2BACC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAET;;;;;;sCAGL,6LAAC;4BACC,SAAS;4BACT,WAAW,CAAC,2EAA2E,EAAE,OAAO,eAAe,EAAE;sCAEhH;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA/HM;KAAA;uCAiIS", "debugId": null}}, {"offset": {"line": 8193, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/utils/alertUtils.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { createRoot } from 'react-dom/client';\r\nimport CustomAlert from '../components/ui/CustomAlert';\r\n\r\ninterface AlertOptions {\r\n  title: string;\r\n  message: string;\r\n  type?: 'error' | 'warning' | 'success' | 'info';\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  onConfirm?: () => void;\r\n}\r\n\r\nexport const showAlert = (options: AlertOptions): Promise<boolean> => {\r\n  return new Promise((resolve) => {\r\n    // Create a div element to mount the alert\r\n    const alertContainer = document.createElement('div');\r\n    alertContainer.id = 'custom-alert-container';\r\n    document.body.appendChild(alertContainer);\r\n\r\n    // Create a root for the alert\r\n    const root = createRoot(alertContainer);\r\n\r\n    // Function to remove the alert from the DOM\r\n    const removeAlert = () => {\r\n      root.unmount();\r\n      if (alertContainer.parentNode) {\r\n        document.body.removeChild(alertContainer);\r\n      }\r\n    };\r\n\r\n    // Render the alert\r\n    root.render(\r\n      <CustomAlert\r\n        title={options.title}\r\n        message={options.message}\r\n        type={options.type || 'info'}\r\n        confirmText={options.confirmText}\r\n        cancelText={options.cancelText}\r\n        onClose={() => {\r\n          removeAlert();\r\n          resolve(false);\r\n        }}\r\n        onConfirm={options.onConfirm ? () => {\r\n          if (options.onConfirm) options.onConfirm();\r\n          removeAlert();\r\n          resolve(true);\r\n        } : undefined}\r\n      />\r\n    );\r\n  });\r\n};\r\n\r\n// Helper functions for common alert types\r\nexport const showErrorAlert = (title: string, message: string, confirmText = 'OK') => {\r\n  return showAlert({ title, message, type: 'error', confirmText });\r\n};\r\n\r\nexport const showWarningAlert = (title: string, message: string, confirmText = 'OK') => {\r\n  return showAlert({ title, message, type: 'warning', confirmText });\r\n};\r\n\r\nexport const showSuccessAlert = (title: string, message: string, confirmText = 'OK') => {\r\n  return showAlert({ title, message, type: 'success', confirmText });\r\n};\r\n\r\nexport const showInfoAlert = (title: string, message: string, confirmText = 'OK') => {\r\n  return showAlert({ title, message, type: 'info', confirmText });\r\n};\r\n\r\nexport const showConfirmAlert = (title: string, message: string, confirmText = 'Yes', cancelText = 'No') => {\r\n  return showAlert({\r\n    title,\r\n    message,\r\n    type: 'warning',\r\n    confirmText,\r\n    cancelText,\r\n    onConfirm: () => {}\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AACA;AAJA;;;;AAeO,MAAM,YAAY,CAAC;IACxB,OAAO,IAAI,QAAQ,CAAC;QAClB,0CAA0C;QAC1C,MAAM,iBAAiB,SAAS,aAAa,CAAC;QAC9C,eAAe,EAAE,GAAG;QACpB,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,8BAA8B;QAC9B,MAAM,OAAO,CAAA,GAAA,qKAAA,CAAA,aAAU,AAAD,EAAE;QAExB,4CAA4C;QAC5C,MAAM,cAAc;YAClB,KAAK,OAAO;YACZ,IAAI,eAAe,UAAU,EAAE;gBAC7B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;QACF;QAEA,mBAAmB;QACnB,KAAK,MAAM,eACT,6LAAC,mIAAA,CAAA,UAAW;YACV,OAAO,QAAQ,KAAK;YACpB,SAAS,QAAQ,OAAO;YACxB,MAAM,QAAQ,IAAI,IAAI;YACtB,aAAa,QAAQ,WAAW;YAChC,YAAY,QAAQ,UAAU;YAC9B,SAAS;gBACP;gBACA,QAAQ;YACV;YACA,WAAW,QAAQ,SAAS,GAAG;gBAC7B,IAAI,QAAQ,SAAS,EAAE,QAAQ,SAAS;gBACxC;gBACA,QAAQ;YACV,IAAI;;;;;;IAGV;AACF;AAGO,MAAM,iBAAiB,CAAC,OAAe,SAAiB,cAAc,IAAI;IAC/E,OAAO,UAAU;QAAE;QAAO;QAAS,MAAM;QAAS;IAAY;AAChE;AAEO,MAAM,mBAAmB,CAAC,OAAe,SAAiB,cAAc,IAAI;IACjF,OAAO,UAAU;QAAE;QAAO;QAAS,MAAM;QAAW;IAAY;AAClE;AAEO,MAAM,mBAAmB,CAAC,OAAe,SAAiB,cAAc,IAAI;IACjF,OAAO,UAAU;QAAE;QAAO;QAAS,MAAM;QAAW;IAAY;AAClE;AAEO,MAAM,gBAAgB,CAAC,OAAe,SAAiB,cAAc,IAAI;IAC9E,OAAO,UAAU;QAAE;QAAO;QAAS,MAAM;QAAQ;IAAY;AAC/D;AAEO,MAAM,mBAAmB,CAAC,OAAe,SAAiB,cAAc,KAAK,EAAE,aAAa,IAAI;IACrG,OAAO,UAAU;QACf;QACA;QACA,MAAM;QACN;QACA;QACA,WAAW,KAAO;IACpB;AACF", "debugId": null}}, {"offset": {"line": 8297, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/components/upload/UploadManager.tsx"], "sourcesContent": ["// components/upload/UploadManager.tsx\r\n'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport UploadTypeSelection from './UploadTypeSelection';\r\nimport VideoCategorySelection from './VideoCategorySelection';\r\nimport ThumbnailSelection from './ThumbnailSelection';\r\nimport PersonalDetails from './PersonalDetails';\r\nimport VendorDetails from './VendorDetails';\r\nimport FaceVerification from './FaceVerification';\r\nimport UploadProgress from './UploadProgress';\r\nimport { useUpload } from '../../contexts/UploadContexts';\r\nimport { getVideoDuration, validateVideoDuration } from '../../utils/uploadUtils';\r\nimport { showWarningAlert, showErrorAlert, showAlert } from '../../utils/alertUtils';\r\n\r\nexport type UploadPhase =\r\n  | 'typeSelection'\r\n  | 'categorySelection'\r\n  | 'fileUpload'\r\n  | 'thumbnailSelection'\r\n  | 'personalDetails'\r\n  | 'vendorDetails'\r\n  | 'faceVerification'\r\n  | 'uploading'\r\n  | 'complete'\r\n  | 'closed';\r\n\r\ninterface PersonalDetailsData {\r\n  caption: string;\r\n  lifePartner: string;\r\n  weddingStyle: string;\r\n  place: string;\r\n}\r\n\r\ninterface VendorDetailItem {\r\n  name: string;\r\n  mobileNumber: string;\r\n}\r\n\r\ninterface UploadManagerProps {\r\n  onClose?: () => void;\r\n  initialType?: string;\r\n}\r\n\r\n// Helper function to format time in minutes and seconds\r\nconst formatTime = (seconds: number): string => {\r\n  const minutes = Math.floor(seconds / 60);\r\n  const remainingSeconds = Math.floor(seconds % 60);\r\n\r\n  if (minutes === 0) {\r\n    return `${remainingSeconds} seconds`;\r\n  } else if (minutes === 1 && remainingSeconds === 0) {\r\n    return '1 minute';\r\n  } else if (remainingSeconds === 0) {\r\n    return `${minutes} minutes`;\r\n  } else {\r\n    return `${minutes} minute${minutes > 1 ? 's' : ''} and ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;\r\n  }\r\n};\r\n\r\nconst UploadManager: React.FC<UploadManagerProps> = ({ onClose, initialType }) => {\r\n  const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setDuration, resetUpload } = useUpload();\r\n  const [phase, setPhase] = useState<UploadPhase>('typeSelection');\r\n  const [selectedType, setSelectedType] = useState<string>(initialType || '');\r\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\r\n  const [previewImage, setPreviewImage] = useState<string | null>(null);\r\n  const [thumbnailImage, setThumbnailImage] = useState<File | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const vendorDetailsRef = useRef<Record<string, VendorDetailItem>>({});\r\n\r\n  // Store personal details to persist between screens\r\n  const [personalDetails, setPersonalDetails] = useState<PersonalDetailsData>({\r\n    caption: '',\r\n    lifePartner: '',\r\n    weddingStyle: '',\r\n    place: ''\r\n  });\r\n\r\n  // Auto-select the type if initialType is provided\r\n  useEffect(() => {\r\n    if (initialType) {\r\n      console.log('Auto-selecting type from initialType:', initialType);\r\n      handleTypeSelected(initialType);\r\n    }\r\n  }, []);\r\n\r\n  // Store vendor details to persist between screens\r\n  const [vendorDetailsData, setVendorDetailsData] = useState<Record<string, VendorDetailItem>>({\r\n    venue: { name: '', mobileNumber: '' },\r\n    photographer: { name: '', mobileNumber: '' },\r\n    makeupArtist: { name: '', mobileNumber: '' },\r\n    decorations: { name: '', mobileNumber: '' },\r\n    caterer: { name: '', mobileNumber: '' }\r\n  });\r\n\r\n  // Handle media type selection\r\n  const handleTypeSelected = (type: string) => {\r\n    // First, completely reset everything\r\n    resetUpload();\r\n    setPreviewImage(null);\r\n    setThumbnailImage(null);\r\n\r\n    // Then set the new type\r\n    setSelectedType(type);\r\n    console.log(\"Selected type:\", type);\r\n\r\n    if (['flashes', 'glimpses', 'movies', 'photos', 'moments'].includes(type)) {\r\n      // For explicit video types, photos, and moments, set the appropriate media type and go to category selection\r\n      if (type === 'photos') {\r\n        console.log('Setting media type to photo for:', type);\r\n        setMediaType('photo');\r\n        setMediaSubtype('post');\r\n      } else if (type === 'moments') {\r\n        console.log('Setting media type for moments (will be determined by file type)');\r\n        // For moments, we'll set the media type later based on the file type (photo or video)\r\n        setMediaSubtype('story');\r\n      } else {\r\n        setMediaType('video');\r\n        setMediaSubtype(getMediaSubtypeFromSelectedType(type));\r\n      }\r\n      // Go to category selection for all media types\r\n      setPhase('categorySelection');\r\n    } else if (type === 'photo') {\r\n      // For single photo type (if it exists)\r\n      console.log('Setting media type to photo for:', type);\r\n      setMediaType('photo');\r\n      setMediaSubtype('post');\r\n      // Use a special photo-only upload handler for photos\r\n      handlePhotoUpload();\r\n    }\r\n  };\r\n\r\n  // Helper function to get the backend media subtype from the selected UI type\r\n  const getMediaSubtypeFromSelectedType = (type: string): string => {\r\n    // Map UI category to backend category for media_subtype\r\n    switch (type) {\r\n      // Photo types\r\n      case 'moments':\r\n        return 'story';  // Backend expects 'story' for moments\r\n      case 'photos':\r\n        return 'post';   // Backend expects 'post' for regular photos\r\n\r\n      // Video types\r\n      case 'flashes':\r\n        return 'flash';  // Backend expects 'flash'\r\n      case 'glimpses':\r\n        return 'glimpse';  // Backend expects 'glimpse'\r\n      case 'movies':\r\n        return 'movie';  // Backend expects 'movie'\r\n\r\n      // Default fallback\r\n      default:\r\n        return type === 'moments' ? 'story' : 'post';  // Default based on type\r\n    }\r\n  };\r\n\r\n  // Handle category selection for both videos and photos\r\n  const handleCategorySelected = (category: string) => {\r\n    // First, make sure we have a clean state for the new upload\r\n    // but preserve the selected type and media type\r\n    const currentType = selectedType;\r\n    const currentMediaType = state.mediaType;\r\n    resetUpload();\r\n    setSelectedType(currentType);\r\n    setMediaType(currentMediaType);\r\n    setPreviewImage(null);\r\n    setThumbnailImage(null);\r\n\r\n    // Now set the new category\r\n    setSelectedCategory(category);\r\n\r\n    // Get the media subtype based on the selected type\r\n    let mediaSubtype;\r\n    if (currentType === 'photos') {\r\n      // For photos, always use 'post' as the media subtype\r\n      mediaSubtype = 'post';\r\n      console.log(`UPLOAD MANAGER - Using media subtype 'post' for photos`);\r\n    } else {\r\n      // For videos, use the subtype based on the selected type\r\n      mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\r\n      console.log(`UPLOAD MANAGER - Using media subtype ${mediaSubtype} based on selected type ${selectedType}`);\r\n      console.log(`UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story`);\r\n    }\r\n\r\n    console.log(\"UPLOAD MANAGER - Selected category:\", category);\r\n    console.log(\"UPLOAD MANAGER - Setting media subtype to:\", mediaSubtype);\r\n\r\n    // Set the media subtype in the context\r\n    setMediaSubtype(mediaSubtype);\r\n\r\n    // Map the selected category to a valid backend video_category\r\n    let backendVideoCategory = '';\r\n\r\n    if (category === 'my_wedding_videos') {\r\n      backendVideoCategory = 'my_wedding';\r\n    } else if (category === 'wedding_influencer') {\r\n      backendVideoCategory = 'wedding_influencer';\r\n    } else if (category === 'friends_family_videos') {\r\n      backendVideoCategory = 'friends_family_video';\r\n    }\r\n\r\n    // Make sure we have a valid video_category\r\n    if (!backendVideoCategory) {\r\n      console.error('Invalid video category selected:', category);\r\n      alert('Please select a valid video category');\r\n      return;\r\n    }\r\n\r\n    // Set video category in the context for the backend\r\n    console.log(\"UPLOAD MANAGER - Setting video_category to:\", backendVideoCategory);\r\n    setDetailField('video_category', backendVideoCategory);\r\n\r\n    // Log the final values\r\n    console.log(\"UPLOAD MANAGER - Selected category:\", category);\r\n    console.log(\"UPLOAD MANAGER - Backend video category set to:\", backendVideoCategory);\r\n    console.log(\"UPLOAD MANAGER - Media subtype set to:\", mediaSubtype);\r\n\r\n    // Proceed to file upload after setting the category\r\n    if (currentType === 'photos') {\r\n      // For photos, use the photo-specific upload handler\r\n      handlePhotoUpload();\r\n    } else {\r\n      // For videos, use the standard file upload handler\r\n      handleFileUpload();\r\n    }\r\n  };\r\n\r\n  // Handle thumbnail upload\r\n  const handleThumbnailUpload = () => {\r\n    // Create a file input element\r\n    const input = document.createElement('input');\r\n    input.type = 'file';\r\n    input.accept = 'image/*';\r\n\r\n    // Handle file selection\r\n    input.onchange = (e) => {\r\n      const files = (e.target as HTMLInputElement).files;\r\n      if (files && files.length > 0) {\r\n        const file = files[0];\r\n\r\n        // Store the thumbnail\r\n        setThumbnailImage(file);\r\n        setThumbnail(file);\r\n\r\n        console.log(\"Thumbnail selected:\", file.name);\r\n\r\n        // Show a preview if needed\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          if (e.target?.result) {\r\n            // You could set a thumbnail preview here if needed\r\n            console.log(\"Thumbnail preview ready\");\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    };\r\n\r\n    // Trigger the file dialog\r\n    input.click();\r\n  };\r\n\r\n  // Get user-friendly display name for a category\r\n  const getCategoryDisplayName = (category: string): string => {\r\n    switch (category) {\r\n      case 'flash':\r\n        return 'Flash';\r\n      case 'glimpse':\r\n        return 'Glimpse';\r\n      case 'movie':\r\n        return 'Movie';\r\n      case 'story':\r\n        return 'Story';\r\n      case 'post':\r\n        return 'Photo';\r\n      default:\r\n        return category.charAt(0).toUpperCase() + category.slice(1);\r\n    }\r\n  };\r\n\r\n  // Get appropriate category based on duration\r\n  const getAppropriateCategory = (duration: number): string => {\r\n    // For very short videos (1 minute or less), use flash instead of story/moments\r\n    if (duration <= 60) {\r\n      return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'\r\n    } else if (duration <= 90) {\r\n      return 'flash'; // Short videos (1.5 minutes or less)\r\n    } else if (duration <= 420) {\r\n      return 'glimpse'; // Medium videos (7 minutes or less)\r\n    } else {\r\n      return 'movie'; // Long videos (over 7 minutes)\r\n    }\r\n  };\r\n\r\n  // Special handler for photo uploads that strictly enforces image-only files\r\n  const handlePhotoUpload = () => {\r\n    console.log('handlePhotoUpload called - strict image-only upload');\r\n\r\n    // Create a file input element specifically for photos\r\n    const input = document.createElement('input');\r\n    input.type = 'file';\r\n\r\n    // Reset the input value\r\n    input.value = '';\r\n\r\n    // Only accept image files - explicitly list allowed types\r\n    input.accept = 'image/jpeg,image/png,image/gif,image/webp';\r\n\r\n    // Handle file selection with strict validation\r\n    input.onchange = async (e) => {\r\n      const files = (e.target as HTMLInputElement).files;\r\n      if (!files || files.length === 0) return;\r\n\r\n      const file = files[0];\r\n      console.log('Photo file selected:', file.name, file.type, file.size);\r\n\r\n      // Strict validation - must be an image file\r\n      const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n\r\n      if (!validImageTypes.includes(file.type)) {\r\n        console.error('Invalid file type for photos:', file.type);\r\n        alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\r\n        return;\r\n      }\r\n\r\n      // Additional check - reject any file that might be a video\r\n      if (file.type.startsWith('video/')) {\r\n        console.error('Attempted to upload a video file as photo');\r\n        alert('Videos cannot be uploaded as photos. Please select an image file.');\r\n        return;\r\n      }\r\n\r\n      // For photos, we need to be more careful with state management\r\n      // First, set the media type and subtype\r\n      setMediaType('photo');\r\n      setMediaSubtype('post');\r\n\r\n      // Then set the file in the state\r\n      setFile(file);\r\n      console.log('Photo file set in state:', file.name);\r\n\r\n      // Create a local reference to the file for use in the timeout\r\n      const currentFile = file;\r\n\r\n      // Double-check that the file is set in the state before proceeding\r\n      setTimeout(() => {\r\n        // Check if the file is in the state\r\n        if (!state.file) {\r\n          console.log('File not found in state after setting, trying again');\r\n          // Try setting the file again\r\n          setFile(currentFile);\r\n\r\n          // Add another timeout to ensure the file is set\r\n          setTimeout(() => {\r\n            if (!state.file) {\r\n              console.log('File still not in state, setting it one more time');\r\n              setFile(currentFile);\r\n            } else {\r\n              console.log('File confirmed in state after second attempt:', state.file.name);\r\n            }\r\n            // Proceed to personal details regardless\r\n            console.log('Moving to personalDetails phase for photo');\r\n            setPhase('personalDetails');\r\n          }, 100);\r\n        } else {\r\n          console.log('File confirmed in state:', state.file.name);\r\n          // Proceed to personal details\r\n          console.log('Moving to personalDetails phase for photo');\r\n          setPhase('personalDetails');\r\n        }\r\n      }, 100);\r\n\r\n      // Handle image preview\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        if (e.target?.result) {\r\n          setPreviewImage(e.target.result as string);\r\n          console.log('Preview image set for photo');\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n\r\n      // Note: We don't set the phase here anymore - it's handled in the timeout above\r\n    };\r\n\r\n    // Trigger the file dialog\r\n    input.click();\r\n  };\r\n\r\n  // This function was previously used but is now replaced by getAppropriateCategory\r\n  // Keeping a comment here for reference in case it needs to be restored\r\n\r\n  // Handle manual upload button click\r\n  const handleFileUpload = async (category?: string) => {\r\n    console.log('handleFileUpload called with category:', category || 'none');\r\n\r\n    // Create a file input element\r\n    const input = document.createElement('input');\r\n    input.type = 'file';\r\n\r\n    // Reset the input value to ensure we get a new file selection event even if the same file is selected\r\n    input.value = '';\r\n\r\n    if (selectedType === 'moments') {\r\n      input.accept = 'image/*,video/*';\r\n    } else {\r\n      input.accept = selectedType === 'photo' || selectedType === 'photos'\r\n        ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only\r\n        : 'video/*';\r\n    }\r\n\r\n    // Handle file selection\r\n    input.onchange = async (e) => {\r\n      const files = (e.target as HTMLInputElement).files;\r\n      if (!files || files.length === 0) return;\r\n\r\n      const file = files[0];\r\n      console.log('File selected:', file.name, file.type, file.size);\r\n\r\n      // Strict validation for photo uploads - must be an image file\r\n      if (selectedType === 'photo' || selectedType === 'photos') {\r\n        const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n\r\n        // Check if file is a video or not a valid image type\r\n        if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {\r\n          console.error('Invalid file type for photos:', file.type);\r\n          showErrorAlert('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Reset the upload context before setting the new file\r\n      resetUpload();\r\n\r\n      // Set the file in the state\r\n      setFile(file);\r\n      console.log('File set in state:', file.name);\r\n\r\n      // If it's a video, calculate and set the duration\r\n      // Double-check that we're not trying to upload a video as a photo\r\n      if (file.type.startsWith('video/')) {\r\n        // Safety check - don't process videos for photo uploads\r\n        if (selectedType === 'photo' || selectedType === 'photos') {\r\n          console.error('Attempted to process a video file for photo upload');\r\n          showErrorAlert('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');\r\n          resetUpload();\r\n          return;\r\n        }\r\n        try {\r\n          const duration = await getVideoDuration(file);\r\n          console.log('Video duration calculated:', duration);\r\n          setDuration(duration);\r\n\r\n          // For moments, check if it's a video and validate the duration (max 1 minute)\r\n          if (selectedType === 'moments') {\r\n            console.log('Validating moments video duration...');\r\n            setMediaType('video');\r\n\r\n            // Check if the video is longer than 1 minute (60 seconds)\r\n            if (duration > 60) {\r\n              console.log(`Moments video too long: ${duration} seconds (max: 60 seconds)`);\r\n\r\n              // Show a more detailed error message with custom alert\r\n              showWarningAlert(\r\n                'Moments Video Too Long',\r\n                `Moments videos must be 1 minute or less.\\n\\nYour video is ${formatTime(duration)} long.\\n\\nPlease select a shorter video or trim this video to 1 minute or less.`\r\n              );\r\n\r\n              // Reset the upload context but preserve the selected type and category\r\n              const currentType = selectedType;\r\n              const currentCategory = selectedCategory;\r\n\r\n              // First set the phase back to category selection\r\n              setPhase('categorySelection');\r\n\r\n              // Then reset the upload state\r\n              setTimeout(() => {\r\n                resetUpload();\r\n                setSelectedType(currentType);\r\n                setSelectedCategory(currentCategory);\r\n                console.log('Reset upload state after moments video duration validation failure');\r\n              }, 100);\r\n\r\n              // Return early to prevent further processing\r\n              return;\r\n            }\r\n\r\n            console.log(`Moments video duration valid: ${duration} seconds (max: 60 seconds)`);\r\n            // For moments, we always use 'story' as the media subtype\r\n            console.log('Setting media subtype for moments video to story');\r\n            setMediaSubtype('story');\r\n          }\r\n\r\n          // If we have a category, validate the duration for that category\r\n          if (selectedType && ['flashes', 'glimpses', 'movies'].includes(selectedType)) {\r\n            const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\r\n            const validationResult = validateVideoDuration(duration, mediaSubtype);\r\n\r\n            if (!validationResult.isValid) {\r\n              // If there's a suggested category, automatically switch to it\r\n              if (validationResult.suggestedCategory) {\r\n                // For videos that exceed the maximum duration, automatically switch without asking\r\n                console.log(`Video exceeds maximum duration for ${mediaSubtype}. Automatically switching to ${validationResult.suggestedCategory}`);\r\n                showWarningAlert(\r\n                  'Video Duration Notice',\r\n                  `Your video is too long for the ${getCategoryDisplayName(mediaSubtype)} category. It will be uploaded as a ${getCategoryDisplayName(validationResult.suggestedCategory)} instead.`\r\n                );\r\n\r\n                // Switch to the suggested category\r\n                console.log(`Switching to suggested category: ${validationResult.suggestedCategory}`);\r\n\r\n                // Make sure we keep a reference to the file\r\n                const currentFile = file;\r\n\r\n                // Update the media subtype\r\n                setMediaSubtype(validationResult.suggestedCategory);\r\n\r\n                // Update the selected type to match the new category\r\n                // Never suggest 'story' (moments) for other categories\r\n                if (validationResult.suggestedCategory === 'flash') {\r\n                  setSelectedType('flashes');\r\n                } else if (validationResult.suggestedCategory === 'glimpse') {\r\n                  setSelectedType('glimpses');\r\n                } else if (validationResult.suggestedCategory === 'movie') {\r\n                  setSelectedType('movies');\r\n                }\r\n                // Removed the 'story' suggestion for short videos\r\n\r\n                // Make sure the file is still set in the state\r\n                setTimeout(() => {\r\n                  if (!state.file) {\r\n                    console.log('Re-setting file after category change:', currentFile.name);\r\n                    setFile(currentFile);\r\n                  }\r\n                }, 50);\r\n              } else {\r\n                // No suggested category, just show the error\r\n                showErrorAlert('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');\r\n              }\r\n            } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {\r\n              // Video is valid for current category but there's a better category\r\n              // For this case, we still give the user a choice since the video is valid for the current category\r\n              // Use our custom confirm dialog instead of window.confirm\r\n              const confirmSwitch = await showAlert({\r\n                title: 'Category Suggestion',\r\n                message: `${validationResult.error}\\n\\nWould you like to switch to the suggested category?`,\r\n                type: 'warning',\r\n                confirmText: 'Yes, Switch Category',\r\n                cancelText: 'No, Keep Current',\r\n                onConfirm: () => { }\r\n              });\r\n\r\n              if (confirmSwitch) {\r\n                // Switch to the suggested category\r\n                console.log(`Switching to suggested category: ${validationResult.suggestedCategory}`);\r\n\r\n                // Make sure we keep a reference to the file\r\n                const currentFile = file;\r\n\r\n                // Update the media subtype\r\n                setMediaSubtype(validationResult.suggestedCategory);\r\n\r\n                // Update the selected type to match the new category\r\n                // Never suggest 'story' (moments) for other categories\r\n                if (validationResult.suggestedCategory === 'flash') {\r\n                  setSelectedType('flashes');\r\n                } else if (validationResult.suggestedCategory === 'glimpse') {\r\n                  setSelectedType('glimpses');\r\n                } else if (validationResult.suggestedCategory === 'movie') {\r\n                  setSelectedType('movies');\r\n                }\r\n                // Removed the 'story' suggestion for short videos\r\n\r\n                // Make sure the file is still set in the state\r\n                setTimeout(() => {\r\n                  if (!state.file) {\r\n                    console.log('Re-setting file after category change:', currentFile.name);\r\n                    setFile(currentFile);\r\n                  }\r\n                }, 50);\r\n              }\r\n            }\r\n          }\r\n\r\n          // Always go to thumbnail selection for videos\r\n          console.log('Moving to thumbnailSelection phase');\r\n\r\n          // Keep a reference to the current file\r\n          const currentFile = file;\r\n\r\n          // Double-check that the file is set in the state before proceeding\r\n          if (state.file) {\r\n            console.log('File confirmed in state before phase change:', state.file.name);\r\n            setPhase('thumbnailSelection');\r\n          } else {\r\n            console.log('File not found in state before phase change, setting it again');\r\n            // Try setting the file again\r\n            setFile(currentFile);\r\n            // Add a small delay to ensure the state is updated\r\n            setTimeout(() => {\r\n              // Double-check again\r\n              if (!state.file) {\r\n                console.log('File still not in state, setting it one more time');\r\n                setFile(currentFile);\r\n              }\r\n              console.log('Delayed phase change to thumbnailSelection');\r\n              setPhase('thumbnailSelection');\r\n            }, 100);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error calculating video duration:', error);\r\n\r\n          // For moments videos, we need to enforce the duration check\r\n          // If we can't calculate duration, we can't validate it, so we should reject the upload\r\n          if (selectedType === 'moments') {\r\n            showErrorAlert('Video Error', 'Unable to determine video duration. Please try a different video file.');\r\n            resetUpload();\r\n            return;\r\n          }\r\n          console.log('Moving to thumbnailSelection phase despite error');\r\n\r\n          // Keep a reference to the current file\r\n          const currentFile = file;\r\n\r\n          // Double-check that the file is set in the state before proceeding\r\n          if (state.file) {\r\n            console.log('File confirmed in state before phase change (error case):', state.file.name);\r\n            setPhase('thumbnailSelection');\r\n          } else {\r\n            console.log('File not found in state before phase change (error case), setting it again');\r\n            // Try setting the file again\r\n            setFile(currentFile);\r\n            // Add a small delay to ensure the state is updated\r\n            setTimeout(() => {\r\n              // Double-check again\r\n              if (!state.file) {\r\n                console.log('File still not in state (error case), setting it one more time');\r\n                setFile(currentFile);\r\n              }\r\n              console.log('Delayed phase change to thumbnailSelection (error case)');\r\n              setPhase('thumbnailSelection');\r\n            }, 100);\r\n          }\r\n        }\r\n      } else {\r\n        // For photos or moments images\r\n        if (selectedType === 'moments') {\r\n          // For moments, we need to set the media type based on the file type\r\n          if (file.type.startsWith('image/')) {\r\n            console.log('Moments image detected');\r\n            setMediaType('photo');\r\n            // For moments images, we always use 'story' as the media subtype\r\n            setMediaSubtype('story');\r\n\r\n            // Create a local reference to the file for use in the timeout\r\n            const currentFile = file;\r\n\r\n            // Double-check that the file is set in the state before proceeding\r\n            setTimeout(() => {\r\n              // Check if the file is in the state\r\n              if (!state.file) {\r\n                console.log('Moments photo not found in state after setting, trying again');\r\n                // Try setting the file again\r\n                setFile(currentFile);\r\n              } else {\r\n                console.log('Moments photo confirmed in state:', state.file.name);\r\n              }\r\n            }, 50);\r\n          } else {\r\n            console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\r\n            showErrorAlert('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\r\n\r\n            // Reset the upload context but preserve the selected type and category\r\n            const currentType = selectedType;\r\n            const currentCategory = selectedCategory;\r\n\r\n            // First set the phase back to category selection\r\n            setPhase('categorySelection');\r\n\r\n            // Then reset the upload state\r\n            setTimeout(() => {\r\n              resetUpload();\r\n              setSelectedType(currentType);\r\n              setSelectedCategory(currentCategory);\r\n              console.log('Reset upload state after invalid file type for moments');\r\n            }, 100);\r\n            return;\r\n          }\r\n        }\r\n\r\n        // Handle image preview and set phase\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          if (e.target?.result) {\r\n            setPreviewImage(e.target.result as string);\r\n            console.log('Preview image set for file:', file.name);\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n\r\n        // Create a local reference to the file for use in the timeout\r\n        const currentFile = file;\r\n\r\n        // Double-check that the file is set in the state before proceeding\r\n        setTimeout(() => {\r\n          // Check if the file is in the state\r\n          if (!state.file) {\r\n            console.log('File not found in state before moving to personalDetails, setting it again');\r\n            // Try setting the file again\r\n            setFile(currentFile);\r\n\r\n            // Add another timeout to ensure the file is set\r\n            setTimeout(() => {\r\n              if (!state.file) {\r\n                console.log('File still not in state, setting it one more time');\r\n                setFile(currentFile);\r\n              } else {\r\n                console.log('File confirmed in state after second attempt:', state.file.name);\r\n              }\r\n              // Proceed to personal details regardless\r\n              console.log('Moving to personalDetails phase for photo/image');\r\n              setPhase('personalDetails');\r\n            }, 100);\r\n          } else {\r\n            console.log('File confirmed in state:', state.file.name);\r\n            // Proceed to personal details\r\n            console.log('Moving to personalDetails phase for photo/image');\r\n            setPhase('personalDetails');\r\n          }\r\n        }, 100);\r\n      }\r\n    };\r\n\r\n    // Trigger the file dialog\r\n    input.click();\r\n  };\r\n\r\n  // Handle personal details completed\r\n  const handlePersonalDetailsCompleted = (details: PersonalDetailsData) => {\r\n    console.log('Personal details completed:', details);\r\n\r\n    // Store the personal details in local state for component persistence\r\n    setPersonalDetails(details);\r\n\r\n    // Validate that we have a title\r\n    if (!details.caption || !details.caption.trim()) {\r\n      console.error('Caption/title is empty, this should not happen');\r\n      // Go back to personal details to fix this\r\n      setPhase('personalDetails');\r\n      return;\r\n    }\r\n\r\n    // Set the title in the upload context\r\n    setTitle(details.caption.trim());\r\n\r\n    // Also store in global context for persistence\r\n    setPersonalDetails(details);\r\n\r\n    // Check if we have a file in the state\r\n    if (!state.file) {\r\n      console.error('No file found in state after personal details');\r\n      showErrorAlert('Upload Error', 'Something went wrong with your file upload. Please try again.');\r\n      setPhase('typeSelection');\r\n      return;\r\n    }\r\n\r\n    console.log('File confirmed in state after personal details:', state.file.name);\r\n    console.log('Personal details set successfully');\r\n    console.log('Title set to:', details.caption.trim());\r\n    console.log('Current selectedType:', selectedType);\r\n    console.log('Current mediaSubtype:', state.mediaSubtype);\r\n\r\n    // Skip vendor details for photos and moments\r\n    // Also skip if mediaSubtype is 'story' (which is the backend term for moments)\r\n    if (state.mediaType === 'photo' || selectedType === 'moments' || state.mediaSubtype === 'story') {\r\n      console.log(`Skipping vendor details for ${state.mediaType === 'photo' ? 'photo' : (selectedType === 'moments' || state.mediaSubtype === 'story') ? 'moments' : 'unknown'}`);\r\n      setPhase('faceVerification');\r\n    } else {\r\n      // For videos (flashes, glimpses, movies), proceed to vendor details\r\n      setPhase('vendorDetails');\r\n    }\r\n  };\r\n\r\n  // Handle vendor details completed\r\n  const handleVendorDetailsCompleted = (vendorDetails: Record<string, VendorDetailItem>) => {\r\n    // console.log('Vendor details completed:', vendorDetails);\r\n\r\n    // Normalize vendor details to ensure consistent field names\r\n    const normalizedVendorDetails = { ...vendorDetails };\r\n\r\n    // Ensure we have both frontend and backend field names for makeup artist and decorations\r\n    if (vendorDetails.makeupArtist) {\r\n      normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\r\n    } else if (vendorDetails.makeup_artist) {\r\n      normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;\r\n    }\r\n\r\n    if (vendorDetails.decorations) {\r\n      normalizedVendorDetails.decoration = vendorDetails.decorations;\r\n    } else if (vendorDetails.decoration) {\r\n      normalizedVendorDetails.decorations = vendorDetails.decoration;\r\n    }\r\n\r\n    // Store the normalized vendor details for persistence between screens\r\n    setVendorDetailsData(normalizedVendorDetails);\r\n\r\n    // Also store in the ref for Edge browser compatibility\r\n    vendorDetailsRef.current = normalizedVendorDetails;\r\n\r\n    // Store vendor details in localStorage for persistence\r\n    try {\r\n      localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));\r\n      console.log('UPLOAD MANAGER - Stored vendor details in localStorage');\r\n    } catch (error) {\r\n      console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);\r\n    }\r\n\r\n    // Save the current video_category before setting vendor details\r\n    const currentVideoCategory = state.detailFields.video_category;\r\n    console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);\r\n\r\n    // Store video_category in localStorage\r\n    if (currentVideoCategory) {\r\n      try {\r\n        localStorage.setItem('wedzat_video_category', currentVideoCategory);\r\n        console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);\r\n      } catch (error) {\r\n        console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);\r\n      }\r\n    }\r\n\r\n    // Store in global context for persistence\r\n    setVendorDetails(normalizedVendorDetails);\r\n\r\n    // Explicitly set each vendor detail field\r\n    Object.entries(normalizedVendorDetails).forEach(([vendorType, details]) => {\r\n      if (details && details.name && details.mobileNumber) {\r\n        setDetailField(`vendor_${vendorType}_name`, details.name);\r\n        setDetailField(`vendor_${vendorType}_contact`, details.mobileNumber);\r\n      }\r\n    });\r\n\r\n    // Re-set the video_category after vendor details to ensure it's preserved\r\n    if (currentVideoCategory) {\r\n      console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);\r\n      setTimeout(() => {\r\n        setDetailField('video_category', currentVideoCategory);\r\n      }, 100);\r\n    }\r\n\r\n    // Log all detail fields after setting vendor details\r\n    setTimeout(() => {\r\n      console.log('All detail fields after vendor details:', state.detailFields);\r\n      console.log('Detail fields count:', Object.keys(state.detailFields).length);\r\n      console.log('Normalized vendor details:', normalizedVendorDetails);\r\n    }, 200);\r\n\r\n    // Add a small delay to ensure the state is updated before proceeding\r\n    // This helps with cross-browser compatibility, especially in Edge\r\n    setTimeout(() => {\r\n      // Double-check that we have at least 4 vendor details before proceeding\r\n      const vendorNameFields = Object.keys(state.detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_name'));\r\n      const vendorContactFields = Object.keys(state.detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_contact'));\r\n\r\n      console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);\r\n      console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);\r\n\r\n      // Edge browser workaround - directly set vendor details in the state\r\n      if (typeof window !== 'undefined' && /Edge|Edg/.test(window.navigator.userAgent)) {\r\n        console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');\r\n\r\n        // Create vendor detail fields directly in the state\r\n        // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details\r\n        Object.entries(normalizedVendorDetails).forEach(([vendorType, details]) => {\r\n          if (details && details.name && details.mobileNumber) {\r\n            // Set the vendor details directly in the state\r\n            state.detailFields[`vendor_${vendorType}_name`] = details.name;\r\n            state.detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;\r\n\r\n            // Also set the normalized version\r\n            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n              vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n            if (normalizedType !== vendorType) {\r\n              state.detailFields[`vendor_${normalizedType}_name`] = details.name;\r\n              state.detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;\r\n            }\r\n\r\n            console.log(`UPLOAD MANAGER - Edge workaround: Added vendor ${vendorType} directly to state`);\r\n          }\r\n        });\r\n\r\n        // Re-set the video_category directly\r\n        if (currentVideoCategory) {\r\n          state.detailFields.video_category = currentVideoCategory;\r\n          console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);\r\n        }\r\n      }\r\n\r\n      // Proceed to face verification\r\n      setPhase('faceVerification');\r\n    }, 300);\r\n  };\r\n\r\n  // Handle thumbnail selection\r\n  const handleThumbnailSelected = (thumbnailFile?: File) => {\r\n    if (thumbnailFile) {\r\n      // Set the thumbnail in the context\r\n      setThumbnail(thumbnailFile);\r\n      console.log('Thumbnail selected:', thumbnailFile.name);\r\n    } else {\r\n      console.log('No thumbnail selected, using auto-generated thumbnail');\r\n    }\r\n\r\n    // Move to personal details page\r\n    setPhase('personalDetails');\r\n  };\r\n\r\n  // Function to proceed with upload after vendor details are applied\r\n  const proceedWithUpload = (videoCategory: string | undefined) => {\r\n    // Double-check that we have a title before changing to uploading phase\r\n    if (!state.title || !state.title.trim()) {\r\n      console.error('Title is missing before upload, setting it from personal details');\r\n\r\n      // Try to set the title from personal details\r\n      if (personalDetails.caption && personalDetails.caption.trim()) {\r\n        // console.log('Setting personal details from local state:', personalDetails);\r\n        // Use the global context function to set all personal details at once\r\n        setPersonalDetails(personalDetails);\r\n        // Explicitly set the title as well\r\n        setTitle(personalDetails.caption.trim());\r\n      } else {\r\n        console.error('No title in personal details either, going back to personal details');\r\n        setPhase('personalDetails');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Edge browser workaround - directly set vendor details in the state\r\n    if (typeof window !== 'undefined' && /Edge|Edg/.test(window.navigator.userAgent)) {\r\n      console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');\r\n\r\n      // Get the vendor details from the vendor details data\r\n      const vendorDetailsData = vendorDetailsRef.current;\r\n      if (vendorDetailsData) {\r\n        console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);\r\n\r\n        // Create vendor detail fields directly in the state\r\n        Object.entries(vendorDetailsData).forEach(([vendorType, details]) => {\r\n          if (details && details.name && details.mobileNumber) {\r\n            // Set the vendor details directly in the state\r\n            state.detailFields[`vendor_${vendorType}_name`] = details.name;\r\n            state.detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;\r\n\r\n            // Also set the normalized version\r\n            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n              vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n            if (normalizedType !== vendorType) {\r\n              state.detailFields[`vendor_${normalizedType}_name`] = details.name;\r\n              state.detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;\r\n            }\r\n\r\n            console.log(`UPLOAD MANAGER - Edge workaround: Added vendor ${vendorType} directly to state`);\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // For videos, check if we have a video_category\r\n    if (state.mediaType === 'video') {\r\n      console.log(`UPLOAD MANAGER - Checking video_category before upload`);\r\n      console.log(`UPLOAD MANAGER - Current video_category: ${state.detailFields.video_category || 'Not set'}`);\r\n      console.log(`UPLOAD MANAGER - Current mediaSubtype: ${state.mediaSubtype}`);\r\n      console.log(`UPLOAD MANAGER - Selected category: ${selectedCategory || 'Not set'}`);\r\n\r\n      // Special handling for glimpses\r\n      if (state.mediaSubtype === 'glimpse') {\r\n        console.log(`UPLOAD MANAGER - Special handling for glimpses`);\r\n\r\n        // If we don't have a video_category yet, try to set it from selectedCategory\r\n        if (!state.detailFields.video_category && selectedCategory) {\r\n          // Map the UI category to the backend video_category\r\n          let videoCategory = '';\r\n\r\n          if (selectedCategory === 'my_wedding_videos') {\r\n            videoCategory = 'my_wedding';\r\n          } else if (selectedCategory === 'wedding_influencer') {\r\n            videoCategory = 'wedding_influencer';\r\n          } else if (selectedCategory === 'friends_family_videos') {\r\n            videoCategory = 'friends_family_video';\r\n          }\r\n\r\n          if (videoCategory) {\r\n            console.log(`UPLOAD MANAGER - Setting video_category for glimpse: ${videoCategory}`);\r\n            setDetailField('video_category', videoCategory);\r\n          }\r\n        } else {\r\n          console.log(`UPLOAD MANAGER - Glimpse already has video_category: ${state.detailFields.video_category}`);\r\n        }\r\n      }\r\n\r\n      // If we still don't have a video_category, use a default based on selectedCategory\r\n      if (!state.detailFields.video_category && selectedCategory) {\r\n        console.log(`UPLOAD MANAGER - No video_category set, using selectedCategory: ${selectedCategory}`);\r\n\r\n        // Map the UI category to the backend video_category\r\n        let videoCategory = '';\r\n\r\n        if (selectedCategory === 'my_wedding_videos') {\r\n          videoCategory = 'my_wedding';\r\n        } else if (selectedCategory === 'wedding_influencer') {\r\n          videoCategory = 'wedding_influencer';\r\n        } else if (selectedCategory === 'friends_family_videos') {\r\n          videoCategory = 'friends_family_video';\r\n        }\r\n\r\n        if (videoCategory) {\r\n          console.log(`UPLOAD MANAGER - Setting video_category from selectedCategory: ${videoCategory}`);\r\n          setDetailField('video_category', videoCategory);\r\n        }\r\n      }\r\n\r\n      // Final check - if we still don't have a video_category, use a default\r\n      if (!state.detailFields.video_category) {\r\n        console.log('No video_category found, using a default one');\r\n        // Use 'my_wedding' as a default category instead of asking the user again\r\n        setDetailField('video_category', 'my_wedding');\r\n        console.log('Set default video_category to my_wedding');\r\n      }\r\n    }\r\n\r\n    // Edge browser workaround - directly set vendor details in the state before upload\r\n    if (typeof window !== 'undefined' && /Edge|Edg/.test(window.navigator.userAgent)) {\r\n      console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');\r\n\r\n      // Get the vendor details from the vendor details data\r\n      const vendorDetailsData = vendorDetailsRef.current;\r\n      if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {\r\n        console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);\r\n\r\n        // Create vendor detail fields directly in the state\r\n        Object.entries(vendorDetailsData).forEach(([vendorType, details]) => {\r\n          if (details && details.name && details.mobileNumber) {\r\n            // Set the vendor details directly in the state\r\n            state.detailFields[`vendor_${vendorType}_name`] = details.name;\r\n            state.detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;\r\n\r\n            // Also set the normalized version\r\n            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n              vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n            if (normalizedType !== vendorType) {\r\n              state.detailFields[`vendor_${normalizedType}_name`] = details.name;\r\n              state.detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;\r\n            }\r\n\r\n            console.log(`UPLOAD MANAGER - Edge workaround: Added vendor ${vendorType} directly to state before upload`);\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // Check if we have a file before proceeding\r\n    if (!state.file) {\r\n      console.error('No file found in state before upload');\r\n      showErrorAlert('Upload Error', 'No file selected. Please select a file to upload.');\r\n\r\n      // Go back to type selection to start over\r\n      setPhase('typeSelection');\r\n      return;\r\n    }\r\n\r\n    // Now we can proceed to uploading phase\r\n    setPhase('uploading');\r\n\r\n    // Log the current state before starting upload\r\n    console.log('Current state before upload:', {\r\n      file: state.file ? state.file.name : 'No file',\r\n      mediaType: state.mediaType,\r\n      mediaSubtype: state.mediaSubtype,\r\n      title: state.title,\r\n      description: state.description,\r\n      detailFields: state.detailFields,\r\n      detailFieldsCount: Object.keys(state.detailFields).length\r\n    });\r\n\r\n    // Double-check that we're using the correct category\r\n    console.log(`UPLOAD MANAGER - Final check - Selected type: ${selectedType}`);\r\n    console.log(`UPLOAD MANAGER - Final check - MediaSubtype in state: ${state.mediaSubtype}`);\r\n\r\n    // If the mediaSubtype doesn't match what we expect based on the selected type, fix it\r\n    if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {\r\n      console.log(`UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!`);\r\n      console.log(`UPLOAD MANAGER - Expected mediaSubtype based on selected type: ${getMediaSubtypeFromSelectedType(selectedType)}`);\r\n      console.log(`UPLOAD MANAGER - Actual mediaSubtype in state: ${state.mediaSubtype}`);\r\n      console.log(`UPLOAD MANAGER - Correcting category before upload...`);\r\n\r\n      // Get the corrected category\r\n      const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);\r\n      console.log(`UPLOAD MANAGER - Category corrected to: ${correctedCategory}`);\r\n\r\n      // Get the video_category from the original selection\r\n      // We need to map it to the correct backend value\r\n      let videoCategory = '';\r\n\r\n      if (selectedCategory === 'my_wedding_videos') {\r\n        videoCategory = 'my_wedding';\r\n      } else if (selectedCategory === 'wedding_influencer') {\r\n        videoCategory = 'wedding_influencer';\r\n      } else if (selectedCategory === 'friends_family_videos') {\r\n        videoCategory = 'friends_family_video';\r\n      }\r\n\r\n      console.log(`UPLOAD MANAGER - Original selected category: ${selectedCategory}`);\r\n      console.log(`UPLOAD MANAGER - Mapped to backend video_category: ${videoCategory}`);\r\n\r\n      // Start the upload process with the corrected category and video_category\r\n      startUploadWithCategory(correctedCategory, videoCategory);\r\n    } else {\r\n      // Get the video_category from the state\r\n      const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';\r\n      console.log(`UPLOAD MANAGER - Using video_category for upload: ${finalVideoCategory}`);\r\n\r\n      // Start the upload process with the current category and video_category\r\n      startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(() => {\r\n        // Upload completed successfully\r\n        console.log('Upload completed successfully');\r\n      }).catch((error) => {\r\n        console.error('Upload failed:', error);\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle face verification completed and start upload\r\n  const handleFaceVerificationCompleted = () => {\r\n    console.log('Face verification completed, starting upload process');\r\n\r\n    // Check if we have a file in the state\r\n    if (!state.file) {\r\n      console.error('No file found in state after face verification');\r\n      showErrorAlert('Upload Error', 'Something went wrong with your file upload. Please try again.');\r\n      setPhase('typeSelection');\r\n      return;\r\n    }\r\n\r\n    console.log('File confirmed in state after face verification:', state.file.name);\r\n\r\n    // Try to get vendor details from localStorage first\r\n    let vendorDetailsData = vendorDetailsRef.current;\r\n\r\n    // If not in ref, try localStorage\r\n    if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {\r\n      try {\r\n        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\r\n        if (storedVendorDetails) {\r\n          vendorDetailsData = JSON.parse(storedVendorDetails);\r\n          console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);\r\n\r\n          // Update the ref with the localStorage data\r\n          vendorDetailsRef.current = vendorDetailsData;\r\n\r\n          // Log the vendor details we found\r\n          console.log(`UPLOAD MANAGER - Found ${Object.keys(vendorDetailsData).length} vendor details in localStorage`);\r\n          Object.entries(vendorDetailsData).forEach(([vendorType, details]: [string, any]) => {\r\n            if (details && details.name && details.mobileNumber) {\r\n              console.log(`UPLOAD MANAGER - Vendor ${vendorType}: ${details.name} (${details.mobileNumber})`);\r\n            }\r\n          });\r\n        } else {\r\n          console.log('UPLOAD MANAGER - No vendor details found in localStorage');\r\n        }\r\n      } catch (error) {\r\n        console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);\r\n      }\r\n    } else {\r\n      console.log(`UPLOAD MANAGER - Using ${Object.keys(vendorDetailsData).length} vendor details from ref`);\r\n    }\r\n\r\n    // Try to get video_category from localStorage\r\n    let videoCategory = state.detailFields.video_category;\r\n    if (!videoCategory) {\r\n      try {\r\n        const storedVideoCategory = localStorage.getItem('wedzat_video_category');\r\n        if (storedVideoCategory) {\r\n          videoCategory = storedVideoCategory;\r\n          console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);\r\n\r\n          // Set it in the state\r\n          setDetailField('video_category', videoCategory);\r\n        }\r\n      } catch (error) {\r\n        console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);\r\n      }\r\n    }\r\n\r\n    // Ensure vendor details are present\r\n    if (vendorDetailsData) {\r\n      console.log('UPLOAD MANAGER - Applying vendor details to state');\r\n\r\n      // Create a batch of all detail fields to update at once\r\n      const detailFieldUpdates: Record<string, string> = {};\r\n      let completeVendorCount = 0;\r\n\r\n      // Re-apply vendor details to ensure they're in the state\r\n      Object.entries(vendorDetailsData).forEach(([vendorType, details]) => {\r\n        if (details && details.name && details.mobileNumber) {\r\n          // Add to the batch\r\n          detailFieldUpdates[`vendor_${vendorType}_name`] = details.name;\r\n          detailFieldUpdates[`vendor_${vendorType}_contact`] = details.mobileNumber;\r\n          completeVendorCount++;\r\n\r\n          // Also set normalized versions\r\n          const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :\r\n            vendorType === 'decorations' ? 'decoration' : vendorType;\r\n\r\n          if (normalizedType !== vendorType) {\r\n            detailFieldUpdates[`vendor_${normalizedType}_name`] = details.name;\r\n            detailFieldUpdates[`vendor_${normalizedType}_contact`] = details.mobileNumber;\r\n          }\r\n        }\r\n      });\r\n\r\n      // Apply all updates at once\r\n      console.log(`UPLOAD MANAGER - Applying ${completeVendorCount} complete vendor details to state`);\r\n      console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));\r\n\r\n      // Apply each update individually to ensure they're all set\r\n      Object.entries(detailFieldUpdates).forEach(([field, value]) => {\r\n        setDetailField(field, value);\r\n      });\r\n\r\n      // Add a delay before proceeding to ensure state updates are applied\r\n      setTimeout(() => {\r\n        console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');\r\n        proceedWithUpload(videoCategory);\r\n      }, 500);\r\n    } else {\r\n      console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');\r\n      proceedWithUpload(videoCategory);\r\n    }\r\n\r\n    // This code has been moved to the proceedWithUpload function\r\n  };\r\n\r\n  // Handle going back to personal details from upload error\r\n  const handleBackToPersonalDetails = () => {\r\n    // console.log('Going back to personal details with stored data:', personalDetails);\r\n\r\n    // Make sure the personal details are set in the context\r\n    if (personalDetails.caption && personalDetails.caption.trim()) {\r\n      // Use the global context function to set all personal details at once\r\n      setPersonalDetails(personalDetails);\r\n    }\r\n\r\n    setPhase('personalDetails');\r\n  };\r\n\r\n  // Handle close modal\r\n  const handleClose = () => {\r\n    // Reset the phase first\r\n    setPhase('closed');\r\n\r\n    // Call the onClose callback if provided\r\n    if (onClose) {\r\n      onClose();\r\n    }\r\n\r\n    // Reset the upload state after a short delay to ensure the modal is closed first\r\n    setTimeout(() => {\r\n      resetUpload();\r\n      console.log('Upload state reset after modal close');\r\n    }, 100);\r\n  };\r\n\r\n  // Render selected phase component\r\n  if (phase === 'closed') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {phase === 'typeSelection' && (\r\n        <UploadTypeSelection\r\n          onNext={handleTypeSelected}\r\n          onClose={handleClose}\r\n        />\r\n      )}\r\n\r\n      {phase === 'categorySelection' && (\r\n        <VideoCategorySelection\r\n          onNext={handleCategorySelected}\r\n          onBack={() => setPhase('typeSelection')}\r\n          onUpload={handleCategorySelected} // This is now redundant but kept for compatibility\r\n          onThumbnailUpload={handleThumbnailUpload}\r\n          onClose={handleClose}\r\n          mediaType={state.mediaType as 'photo' | 'video'} // Pass the current media type\r\n          selectedType={selectedType} // Pass the selected type (moments, flashes, etc.)\r\n        />\r\n      )}\r\n\r\n      {phase === 'thumbnailSelection' && state.file && (\r\n        <ThumbnailSelection\r\n          videoFile={state.file}\r\n          onNext={handleThumbnailSelected}\r\n          onBack={() => {\r\n            // Go back to category selection instead of triggering file upload again\r\n            if (['flashes', 'glimpses', 'movies'].includes(selectedType)) {\r\n              setPhase('categorySelection');\r\n            } else {\r\n              // For moments, go back to type selection\r\n              setPhase('typeSelection');\r\n            }\r\n          }}\r\n          onClose={() => {\r\n            // Completely reset the state before closing\r\n            resetUpload();\r\n            handleClose();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {phase === 'personalDetails' && (\r\n        <PersonalDetails\r\n          onNext={handlePersonalDetailsCompleted}\r\n          onBack={() => {\r\n            // Go back to thumbnail selection for videos\r\n            if (state.mediaType === 'video' && state.file) {\r\n              setPhase('thumbnailSelection');\r\n            } else {\r\n              // For photos, go back to type selection\r\n              setPhase('typeSelection');\r\n            }\r\n          }}\r\n          onClose={() => {\r\n            // Completely reset the state before closing\r\n            resetUpload();\r\n            handleClose();\r\n          }}\r\n          previewImage={previewImage}\r\n          videoFile={state.mediaType === 'video' ? state.file : null}\r\n          mediaType={state.mediaType}\r\n          initialDetails={personalDetails} // Pass the stored personal details\r\n        />\r\n      )}\r\n\r\n      {phase === 'vendorDetails' && (\r\n        <VendorDetails\r\n          onNext={handleVendorDetailsCompleted}\r\n          onBack={() => setPhase('personalDetails')}\r\n          onClose={() => {\r\n            // Completely reset the state before closing\r\n            resetUpload();\r\n            handleClose();\r\n          }}\r\n          initialVendorDetails={vendorDetailsData} // Pass the stored vendor details\r\n        />\r\n      )}\r\n\r\n      {phase === 'faceVerification' && (\r\n        <FaceVerification\r\n          onUpload={handleFaceVerificationCompleted}\r\n          onBack={() => {\r\n            // Go back to personal details for photos and moments, vendor details for videos\r\n            if (state.mediaType === 'photo' || selectedType === 'moments' || state.mediaSubtype === 'story') {\r\n              setPhase('personalDetails');\r\n            } else {\r\n              setPhase('vendorDetails');\r\n            }\r\n          }}\r\n          onClose={() => {\r\n            // Completely reset the state before closing\r\n            resetUpload();\r\n            handleClose();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {(phase === 'uploading' || phase === 'complete') && (\r\n        <UploadProgress\r\n          onClose={() => {\r\n            // Completely reset the state before closing\r\n            resetUpload();\r\n            handleClose();\r\n          }}\r\n          onGoBack={handleBackToPersonalDetails}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default UploadManager;\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AAGtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AA2CA,wDAAwD;AACxD,MAAM,aAAa,CAAC;IAClB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;IAE9C,IAAI,YAAY,GAAG;QACjB,OAAO,GAAG,iBAAiB,QAAQ,CAAC;IACtC,OAAO,IAAI,YAAY,KAAK,qBAAqB,GAAG;QAClD,OAAO;IACT,OAAO,IAAI,qBAAqB,GAAG;QACjC,OAAO,GAAG,QAAQ,QAAQ,CAAC;IAC7B,OAAO;QACL,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,KAAK,EAAE,iBAAiB,OAAO,EAAE,qBAAqB,IAAI,MAAM,IAAI;IACxH;AACF;AAEA,MAAM,gBAA8C,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE;;IAC3E,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,uBAAuB,EAAE,YAAY,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IACzN,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,eAAe;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoC,CAAC;IAEnE,oDAAoD;IACpD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QAC1E,SAAS;QACT,aAAa;QACb,cAAc;QACd,OAAO;IACT;IAEA,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC,yCAAyC;gBACrD,mBAAmB;YACrB;QACF;kCAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;QAC3F,OAAO;YAAE,MAAM;YAAI,cAAc;QAAG;QACpC,cAAc;YAAE,MAAM;YAAI,cAAc;QAAG;QAC3C,cAAc;YAAE,MAAM;YAAI,cAAc;QAAG;QAC3C,aAAa;YAAE,MAAM;YAAI,cAAc;QAAG;QAC1C,SAAS;YAAE,MAAM;YAAI,cAAc;QAAG;IACxC;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,CAAC;QAC1B,qCAAqC;QACrC;QACA,gBAAgB;QAChB,kBAAkB;QAElB,wBAAwB;QACxB,gBAAgB;QAChB,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,IAAI;YAAC;YAAW;YAAY;YAAU;YAAU;SAAU,CAAC,QAAQ,CAAC,OAAO;YACzE,6GAA6G;YAC7G,IAAI,SAAS,UAAU;gBACrB,QAAQ,GAAG,CAAC,oCAAoC;gBAChD,aAAa;gBACb,gBAAgB;YAClB,OAAO,IAAI,SAAS,WAAW;gBAC7B,QAAQ,GAAG,CAAC;gBACZ,sFAAsF;gBACtF,gBAAgB;YAClB,OAAO;gBACL,aAAa;gBACb,gBAAgB,gCAAgC;YAClD;YACA,+CAA+C;YAC/C,SAAS;QACX,OAAO,IAAI,SAAS,SAAS;YAC3B,uCAAuC;YACvC,QAAQ,GAAG,CAAC,oCAAoC;YAChD,aAAa;YACb,gBAAgB;YAChB,qDAAqD;YACrD;QACF;IACF;IAEA,6EAA6E;IAC7E,MAAM,kCAAkC,CAAC;QACvC,wDAAwD;QACxD,OAAQ;YACN,cAAc;YACd,KAAK;gBACH,OAAO,SAAU,sCAAsC;YACzD,KAAK;gBACH,OAAO,QAAU,4CAA4C;YAE/D,cAAc;YACd,KAAK;gBACH,OAAO,SAAU,0BAA0B;YAC7C,KAAK;gBACH,OAAO,WAAY,4BAA4B;YACjD,KAAK;gBACH,OAAO,SAAU,0BAA0B;YAE7C,mBAAmB;YACnB;gBACE,OAAO,SAAS,YAAY,UAAU,QAAS,wBAAwB;QAC3E;IACF;IAEA,uDAAuD;IACvD,MAAM,yBAAyB,CAAC;QAC9B,4DAA4D;QAC5D,gDAAgD;QAChD,MAAM,cAAc;QACpB,MAAM,mBAAmB,MAAM,SAAS;QACxC;QACA,gBAAgB;QAChB,aAAa;QACb,gBAAgB;QAChB,kBAAkB;QAElB,2BAA2B;QAC3B,oBAAoB;QAEpB,mDAAmD;QACnD,IAAI;QACJ,IAAI,gBAAgB,UAAU;YAC5B,qDAAqD;YACrD,eAAe;YACf,QAAQ,GAAG,CAAC,CAAC,sDAAsD,CAAC;QACtE,OAAO;YACL,yDAAyD;YACzD,eAAe,gCAAgC;YAC/C,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,aAAa,wBAAwB,EAAE,cAAc;YACzG,QAAQ,GAAG,CAAC,CAAC,8FAA8F,CAAC;QAC9G;QAEA,QAAQ,GAAG,CAAC,uCAAuC;QACnD,QAAQ,GAAG,CAAC,8CAA8C;QAE1D,uCAAuC;QACvC,gBAAgB;QAEhB,8DAA8D;QAC9D,IAAI,uBAAuB;QAE3B,IAAI,aAAa,qBAAqB;YACpC,uBAAuB;QACzB,OAAO,IAAI,aAAa,sBAAsB;YAC5C,uBAAuB;QACzB,OAAO,IAAI,aAAa,yBAAyB;YAC/C,uBAAuB;QACzB;QAEA,2CAA2C;QAC3C,IAAI,CAAC,sBAAsB;YACzB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;YACN;QACF;QAEA,oDAAoD;QACpD,QAAQ,GAAG,CAAC,+CAA+C;QAC3D,eAAe,kBAAkB;QAEjC,uBAAuB;QACvB,QAAQ,GAAG,CAAC,uCAAuC;QACnD,QAAQ,GAAG,CAAC,mDAAmD;QAC/D,QAAQ,GAAG,CAAC,0CAA0C;QAEtD,oDAAoD;QACpD,IAAI,gBAAgB,UAAU;YAC5B,oDAAoD;YACpD;QACF,OAAO;YACL,mDAAmD;YACnD;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB;QAC5B,8BAA8B;QAC9B,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QACb,MAAM,MAAM,GAAG;QAEf,wBAAwB;QACxB,MAAM,QAAQ,GAAG,CAAC;YAChB,MAAM,QAAQ,AAAC,EAAE,MAAM,CAAsB,KAAK;YAClD,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,MAAM,OAAO,KAAK,CAAC,EAAE;gBAErB,sBAAsB;gBACtB,kBAAkB;gBAClB,aAAa;gBAEb,QAAQ,GAAG,CAAC,uBAAuB,KAAK,IAAI;gBAE5C,2BAA2B;gBAC3B,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,IAAI,EAAE,MAAM,EAAE,QAAQ;wBACpB,mDAAmD;wBACnD,QAAQ,GAAG,CAAC;oBACd;gBACF;gBACA,OAAO,aAAa,CAAC;YACvB;QACF;QAEA,0BAA0B;QAC1B,MAAM,KAAK;IACb;IAEA,gDAAgD;IAChD,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;QAC7D;IACF;IAEA,6CAA6C;IAC7C,MAAM,yBAAyB,CAAC;QAC9B,+EAA+E;QAC/E,IAAI,YAAY,IAAI;YAClB,OAAO,SAAS,yEAAyE;QAC3F,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,SAAS,qCAAqC;QACvD,OAAO,IAAI,YAAY,KAAK;YAC1B,OAAO,WAAW,oCAAoC;QACxD,OAAO;YACL,OAAO,SAAS,+BAA+B;QACjD;IACF;IAEA,4EAA4E;IAC5E,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC;QAEZ,sDAAsD;QACtD,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QAEb,wBAAwB;QACxB,MAAM,KAAK,GAAG;QAEd,0DAA0D;QAC1D,MAAM,MAAM,GAAG;QAEf,+CAA+C;QAC/C,MAAM,QAAQ,GAAG,OAAO;YACtB,MAAM,QAAQ,AAAC,EAAE,MAAM,CAAsB,KAAK;YAClD,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAElC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,QAAQ,GAAG,CAAC,wBAAwB,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;YAEnE,4CAA4C;YAC5C,MAAM,kBAAkB;gBAAC;gBAAc;gBAAa;gBAAa;aAAa;YAE9E,IAAI,CAAC,gBAAgB,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACxC,QAAQ,KAAK,CAAC,iCAAiC,KAAK,IAAI;gBACxD,MAAM;gBACN;YACF;YAEA,2DAA2D;YAC3D,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAClC,QAAQ,KAAK,CAAC;gBACd,MAAM;gBACN;YACF;YAEA,+DAA+D;YAC/D,wCAAwC;YACxC,aAAa;YACb,gBAAgB;YAEhB,iCAAiC;YACjC,QAAQ;YACR,QAAQ,GAAG,CAAC,4BAA4B,KAAK,IAAI;YAEjD,8DAA8D;YAC9D,MAAM,cAAc;YAEpB,mEAAmE;YACnE,WAAW;gBACT,oCAAoC;gBACpC,IAAI,CAAC,MAAM,IAAI,EAAE;oBACf,QAAQ,GAAG,CAAC;oBACZ,6BAA6B;oBAC7B,QAAQ;oBAER,gDAAgD;oBAChD,WAAW;wBACT,IAAI,CAAC,MAAM,IAAI,EAAE;4BACf,QAAQ,GAAG,CAAC;4BACZ,QAAQ;wBACV,OAAO;4BACL,QAAQ,GAAG,CAAC,iDAAiD,MAAM,IAAI,CAAC,IAAI;wBAC9E;wBACA,yCAAyC;wBACzC,QAAQ,GAAG,CAAC;wBACZ,SAAS;oBACX,GAAG;gBACL,OAAO;oBACL,QAAQ,GAAG,CAAC,4BAA4B,MAAM,IAAI,CAAC,IAAI;oBACvD,8BAA8B;oBAC9B,QAAQ,GAAG,CAAC;oBACZ,SAAS;gBACX;YACF,GAAG;YAEH,uBAAuB;YACvB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,IAAI,EAAE,MAAM,EAAE,QAAQ;oBACpB,gBAAgB,EAAE,MAAM,CAAC,MAAM;oBAC/B,QAAQ,GAAG,CAAC;gBACd;YACF;YACA,OAAO,aAAa,CAAC;QAErB,gFAAgF;QAClF;QAEA,0BAA0B;QAC1B,MAAM,KAAK;IACb;IAEA,kFAAkF;IAClF,uEAAuE;IAEvE,oCAAoC;IACpC,MAAM,mBAAmB,OAAO;QAC9B,QAAQ,GAAG,CAAC,0CAA0C,YAAY;QAElE,8BAA8B;QAC9B,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QAEb,sGAAsG;QACtG,MAAM,KAAK,GAAG;QAEd,IAAI,iBAAiB,WAAW;YAC9B,MAAM,MAAM,GAAG;QACjB,OAAO;YACL,MAAM,MAAM,GAAG,iBAAiB,WAAW,iBAAiB,WACxD,4CAA4C,mCAAmC;eAC/E;QACN;QAEA,wBAAwB;QACxB,MAAM,QAAQ,GAAG,OAAO;YACtB,MAAM,QAAQ,AAAC,EAAE,MAAM,CAAsB,KAAK;YAClD,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAElC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;YAE7D,8DAA8D;YAC9D,IAAI,iBAAiB,WAAW,iBAAiB,UAAU;gBACzD,MAAM,kBAAkB;oBAAC;oBAAc;oBAAa;oBAAa;iBAAa;gBAE9E,qDAAqD;gBACrD,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,gBAAgB,QAAQ,CAAC,KAAK,IAAI,GAAG;oBAC1E,QAAQ,KAAK,CAAC,iCAAiC,KAAK,IAAI;oBACxD,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB;oBACpC;gBACF;YACF;YAEA,uDAAuD;YACvD;YAEA,4BAA4B;YAC5B,QAAQ;YACR,QAAQ,GAAG,CAAC,sBAAsB,KAAK,IAAI;YAE3C,kDAAkD;YAClD,kEAAkE;YAClE,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAClC,wDAAwD;gBACxD,IAAI,iBAAiB,WAAW,iBAAiB,UAAU;oBACzD,QAAQ,KAAK,CAAC;oBACd,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB;oBACpC;oBACA;gBACF;gBACA,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE;oBACxC,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,YAAY;oBAEZ,8EAA8E;oBAC9E,IAAI,iBAAiB,WAAW;wBAC9B,QAAQ,GAAG,CAAC;wBACZ,aAAa;wBAEb,0DAA0D;wBAC1D,IAAI,WAAW,IAAI;4BACjB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,SAAS,0BAA0B,CAAC;4BAE3E,uDAAuD;4BACvD,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EACb,0BACA,CAAC,0DAA0D,EAAE,WAAW,UAAU,+EAA+E,CAAC;4BAGpK,uEAAuE;4BACvE,MAAM,cAAc;4BACpB,MAAM,kBAAkB;4BAExB,iDAAiD;4BACjD,SAAS;4BAET,8BAA8B;4BAC9B,WAAW;gCACT;gCACA,gBAAgB;gCAChB,oBAAoB;gCACpB,QAAQ,GAAG,CAAC;4BACd,GAAG;4BAEH,6CAA6C;4BAC7C;wBACF;wBAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,SAAS,0BAA0B,CAAC;wBACjF,0DAA0D;wBAC1D,QAAQ,GAAG,CAAC;wBACZ,gBAAgB;oBAClB;oBAEA,iEAAiE;oBACjE,IAAI,gBAAgB;wBAAC;wBAAW;wBAAY;qBAAS,CAAC,QAAQ,CAAC,eAAe;wBAC5E,MAAM,eAAe,gCAAgC;wBACrD,MAAM,mBAAmB,CAAA,GAAA,uHAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU;wBAEzD,IAAI,CAAC,iBAAiB,OAAO,EAAE;4BAC7B,8DAA8D;4BAC9D,IAAI,iBAAiB,iBAAiB,EAAE;gCACtC,mFAAmF;gCACnF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,aAAa,6BAA6B,EAAE,iBAAiB,iBAAiB,EAAE;gCAClI,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EACb,yBACA,CAAC,+BAA+B,EAAE,uBAAuB,cAAc,oCAAoC,EAAE,uBAAuB,iBAAiB,iBAAiB,EAAE,SAAS,CAAC;gCAGpL,mCAAmC;gCACnC,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,iBAAiB,iBAAiB,EAAE;gCAEpF,4CAA4C;gCAC5C,MAAM,cAAc;gCAEpB,2BAA2B;gCAC3B,gBAAgB,iBAAiB,iBAAiB;gCAElD,qDAAqD;gCACrD,uDAAuD;gCACvD,IAAI,iBAAiB,iBAAiB,KAAK,SAAS;oCAClD,gBAAgB;gCAClB,OAAO,IAAI,iBAAiB,iBAAiB,KAAK,WAAW;oCAC3D,gBAAgB;gCAClB,OAAO,IAAI,iBAAiB,iBAAiB,KAAK,SAAS;oCACzD,gBAAgB;gCAClB;gCACA,kDAAkD;gCAElD,+CAA+C;gCAC/C,WAAW;oCACT,IAAI,CAAC,MAAM,IAAI,EAAE;wCACf,QAAQ,GAAG,CAAC,0CAA0C,YAAY,IAAI;wCACtE,QAAQ;oCACV;gCACF,GAAG;4BACL,OAAO;gCACL,6CAA6C;gCAC7C,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,iBAAiB,KAAK,IAAI;4BACnE;wBACF,OAAO,IAAI,iBAAiB,iBAAiB,IAAI,iBAAiB,iBAAiB,KAAK,cAAc;4BACpG,oEAAoE;4BACpE,mGAAmG;4BACnG,0DAA0D;4BAC1D,MAAM,gBAAgB,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE;gCACpC,OAAO;gCACP,SAAS,GAAG,iBAAiB,KAAK,CAAC,uDAAuD,CAAC;gCAC3F,MAAM;gCACN,aAAa;gCACb,YAAY;gCACZ,WAAW,KAAQ;4BACrB;4BAEA,IAAI,eAAe;gCACjB,mCAAmC;gCACnC,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,iBAAiB,iBAAiB,EAAE;gCAEpF,4CAA4C;gCAC5C,MAAM,cAAc;gCAEpB,2BAA2B;gCAC3B,gBAAgB,iBAAiB,iBAAiB;gCAElD,qDAAqD;gCACrD,uDAAuD;gCACvD,IAAI,iBAAiB,iBAAiB,KAAK,SAAS;oCAClD,gBAAgB;gCAClB,OAAO,IAAI,iBAAiB,iBAAiB,KAAK,WAAW;oCAC3D,gBAAgB;gCAClB,OAAO,IAAI,iBAAiB,iBAAiB,KAAK,SAAS;oCACzD,gBAAgB;gCAClB;gCACA,kDAAkD;gCAElD,+CAA+C;gCAC/C,WAAW;oCACT,IAAI,CAAC,MAAM,IAAI,EAAE;wCACf,QAAQ,GAAG,CAAC,0CAA0C,YAAY,IAAI;wCACtE,QAAQ;oCACV;gCACF,GAAG;4BACL;wBACF;oBACF;oBAEA,8CAA8C;oBAC9C,QAAQ,GAAG,CAAC;oBAEZ,uCAAuC;oBACvC,MAAM,cAAc;oBAEpB,mEAAmE;oBACnE,IAAI,MAAM,IAAI,EAAE;wBACd,QAAQ,GAAG,CAAC,gDAAgD,MAAM,IAAI,CAAC,IAAI;wBAC3E,SAAS;oBACX,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,6BAA6B;wBAC7B,QAAQ;wBACR,mDAAmD;wBACnD,WAAW;4BACT,qBAAqB;4BACrB,IAAI,CAAC,MAAM,IAAI,EAAE;gCACf,QAAQ,GAAG,CAAC;gCACZ,QAAQ;4BACV;4BACA,QAAQ,GAAG,CAAC;4BACZ,SAAS;wBACX,GAAG;oBACL;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBAEnD,4DAA4D;oBAC5D,uFAAuF;oBACvF,IAAI,iBAAiB,WAAW;wBAC9B,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;wBAC9B;wBACA;oBACF;oBACA,QAAQ,GAAG,CAAC;oBAEZ,uCAAuC;oBACvC,MAAM,cAAc;oBAEpB,mEAAmE;oBACnE,IAAI,MAAM,IAAI,EAAE;wBACd,QAAQ,GAAG,CAAC,6DAA6D,MAAM,IAAI,CAAC,IAAI;wBACxF,SAAS;oBACX,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,6BAA6B;wBAC7B,QAAQ;wBACR,mDAAmD;wBACnD,WAAW;4BACT,qBAAqB;4BACrB,IAAI,CAAC,MAAM,IAAI,EAAE;gCACf,QAAQ,GAAG,CAAC;gCACZ,QAAQ;4BACV;4BACA,QAAQ,GAAG,CAAC;4BACZ,SAAS;wBACX,GAAG;oBACL;gBACF;YACF,OAAO;gBACL,+BAA+B;gBAC/B,IAAI,iBAAiB,WAAW;oBAC9B,oEAAoE;oBACpE,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;wBAClC,QAAQ,GAAG,CAAC;wBACZ,aAAa;wBACb,iEAAiE;wBACjE,gBAAgB;wBAEhB,8DAA8D;wBAC9D,MAAM,cAAc;wBAEpB,mEAAmE;wBACnE,WAAW;4BACT,oCAAoC;4BACpC,IAAI,CAAC,MAAM,IAAI,EAAE;gCACf,QAAQ,GAAG,CAAC;gCACZ,6BAA6B;gCAC7B,QAAQ;4BACV,OAAO;gCACL,QAAQ,GAAG,CAAC,qCAAqC,MAAM,IAAI,CAAC,IAAI;4BAClE;wBACF,GAAG;oBACL,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB;wBAEpC,uEAAuE;wBACvE,MAAM,cAAc;wBACpB,MAAM,kBAAkB;wBAExB,iDAAiD;wBACjD,SAAS;wBAET,8BAA8B;wBAC9B,WAAW;4BACT;4BACA,gBAAgB;4BAChB,oBAAoB;4BACpB,QAAQ,GAAG,CAAC;wBACd,GAAG;wBACH;oBACF;gBACF;gBAEA,qCAAqC;gBACrC,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,IAAI,EAAE,MAAM,EAAE,QAAQ;wBACpB,gBAAgB,EAAE,MAAM,CAAC,MAAM;wBAC/B,QAAQ,GAAG,CAAC,+BAA+B,KAAK,IAAI;oBACtD;gBACF;gBACA,OAAO,aAAa,CAAC;gBAErB,8DAA8D;gBAC9D,MAAM,cAAc;gBAEpB,mEAAmE;gBACnE,WAAW;oBACT,oCAAoC;oBACpC,IAAI,CAAC,MAAM,IAAI,EAAE;wBACf,QAAQ,GAAG,CAAC;wBACZ,6BAA6B;wBAC7B,QAAQ;wBAER,gDAAgD;wBAChD,WAAW;4BACT,IAAI,CAAC,MAAM,IAAI,EAAE;gCACf,QAAQ,GAAG,CAAC;gCACZ,QAAQ;4BACV,OAAO;gCACL,QAAQ,GAAG,CAAC,iDAAiD,MAAM,IAAI,CAAC,IAAI;4BAC9E;4BACA,yCAAyC;4BACzC,QAAQ,GAAG,CAAC;4BACZ,SAAS;wBACX,GAAG;oBACL,OAAO;wBACL,QAAQ,GAAG,CAAC,4BAA4B,MAAM,IAAI,CAAC,IAAI;wBACvD,8BAA8B;wBAC9B,QAAQ,GAAG,CAAC;wBACZ,SAAS;oBACX;gBACF,GAAG;YACL;QACF;QAEA,0BAA0B;QAC1B,MAAM,KAAK;IACb;IAEA,oCAAoC;IACpC,MAAM,iCAAiC,CAAC;QACtC,QAAQ,GAAG,CAAC,+BAA+B;QAE3C,sEAAsE;QACtE,mBAAmB;QAEnB,gCAAgC;QAChC,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,IAAI,IAAI;YAC/C,QAAQ,KAAK,CAAC;YACd,0CAA0C;YAC1C,SAAS;YACT;QACF;QAEA,sCAAsC;QACtC,SAAS,QAAQ,OAAO,CAAC,IAAI;QAE7B,+CAA+C;QAC/C,mBAAmB;QAEnB,uCAAuC;QACvC,IAAI,CAAC,MAAM,IAAI,EAAE;YACf,QAAQ,KAAK,CAAC;YACd,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB;YAC/B,SAAS;YACT;QACF;QAEA,QAAQ,GAAG,CAAC,mDAAmD,MAAM,IAAI,CAAC,IAAI;QAC9E,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,OAAO,CAAC,IAAI;QACjD,QAAQ,GAAG,CAAC,yBAAyB;QACrC,QAAQ,GAAG,CAAC,yBAAyB,MAAM,YAAY;QAEvD,6CAA6C;QAC7C,+EAA+E;QAC/E,IAAI,MAAM,SAAS,KAAK,WAAW,iBAAiB,aAAa,MAAM,YAAY,KAAK,SAAS;YAC/F,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,MAAM,SAAS,KAAK,UAAU,UAAU,AAAC,iBAAiB,aAAa,MAAM,YAAY,KAAK,UAAW,YAAY,WAAW;YAC3K,SAAS;QACX,OAAO;YACL,oEAAoE;YACpE,SAAS;QACX;IACF;IAEA,kCAAkC;IAClC,MAAM,+BAA+B,CAAC;QACpC,2DAA2D;QAE3D,4DAA4D;QAC5D,MAAM,0BAA0B;YAAE,GAAG,aAAa;QAAC;QAEnD,yFAAyF;QACzF,IAAI,cAAc,YAAY,EAAE;YAC9B,wBAAwB,aAAa,GAAG,cAAc,YAAY;QACpE,OAAO,IAAI,cAAc,aAAa,EAAE;YACtC,wBAAwB,YAAY,GAAG,cAAc,aAAa;QACpE;QAEA,IAAI,cAAc,WAAW,EAAE;YAC7B,wBAAwB,UAAU,GAAG,cAAc,WAAW;QAChE,OAAO,IAAI,cAAc,UAAU,EAAE;YACnC,wBAAwB,WAAW,GAAG,cAAc,UAAU;QAChE;QAEA,sEAAsE;QACtE,qBAAqB;QAErB,uDAAuD;QACvD,iBAAiB,OAAO,GAAG;QAE3B,uDAAuD;QACvD,IAAI;YACF,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;YAC7D,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oEAAoE;QACpF;QAEA,gEAAgE;QAChE,MAAM,uBAAuB,MAAM,YAAY,CAAC,cAAc;QAC9D,QAAQ,GAAG,CAAC,iEAAiE;QAE7E,uCAAuC;QACvC,IAAI,sBAAsB;YACxB,IAAI;gBACF,aAAa,OAAO,CAAC,yBAAyB;gBAC9C,QAAQ,GAAG,CAAC,2DAA2D;YACzE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oEAAoE;YACpF;QACF;QAEA,0CAA0C;QAC1C,iBAAiB;QAEjB,0CAA0C;QAC1C,OAAO,OAAO,CAAC,yBAAyB,OAAO,CAAC,CAAC,CAAC,YAAY,QAAQ;YACpE,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;gBACnD,eAAe,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,EAAE,QAAQ,IAAI;gBACxD,eAAe,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,EAAE,QAAQ,YAAY;YACrE;QACF;QAEA,0EAA0E;QAC1E,IAAI,sBAAsB;YACxB,QAAQ,GAAG,CAAC,oEAAoE;YAChF,WAAW;gBACT,eAAe,kBAAkB;YACnC,GAAG;QACL;QAEA,qDAAqD;QACrD,WAAW;YACT,QAAQ,GAAG,CAAC,2CAA2C,MAAM,YAAY;YACzE,QAAQ,GAAG,CAAC,wBAAwB,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,MAAM;YAC1E,QAAQ,GAAG,CAAC,8BAA8B;QAC5C,GAAG;QAEH,qEAAqE;QACrE,kEAAkE;QAClE,WAAW;YACT,wEAAwE;YACxE,MAAM,mBAAmB,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC;YACjH,MAAM,sBAAsB,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,cAAc,IAAI,QAAQ,CAAC;YAEpH,QAAQ,GAAG,CAAC,wCAAwC,iBAAiB,MAAM;YAC3E,QAAQ,GAAG,CAAC,2CAA2C,oBAAoB,MAAM;YAEjF,qEAAqE;YACrE,IAAI,aAAkB,eAAe,WAAW,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS,GAAG;gBAChF,QAAQ,GAAG,CAAC;gBAEZ,oDAAoD;gBACpD,wGAAwG;gBACxG,OAAO,OAAO,CAAC,yBAAyB,OAAO,CAAC,CAAC,CAAC,YAAY,QAAQ;oBACpE,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;wBACnD,+CAA+C;wBAC/C,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;wBAC9D,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wBAEzE,kCAAkC;wBAClC,MAAM,iBAAiB,eAAe,iBAAiB,kBACrD,eAAe,gBAAgB,eAAe;wBAEhD,IAAI,mBAAmB,YAAY;4BACjC,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;4BAClE,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wBAC/E;wBAEA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,WAAW,kBAAkB,CAAC;oBAC9F;gBACF;gBAEA,qCAAqC;gBACrC,IAAI,sBAAsB;oBACxB,MAAM,YAAY,CAAC,cAAc,GAAG;oBACpC,QAAQ,GAAG,CAAC,qEAAqE;gBACnF;YACF;YAEA,+BAA+B;YAC/B,SAAS;QACX,GAAG;IACL;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,IAAI,eAAe;YACjB,mCAAmC;YACnC,aAAa;YACb,QAAQ,GAAG,CAAC,uBAAuB,cAAc,IAAI;QACvD,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,gCAAgC;QAChC,SAAS;IACX;IAEA,mEAAmE;IACnE,MAAM,oBAAoB,CAAC;QACzB,uEAAuE;QACvE,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI;YACvC,QAAQ,KAAK,CAAC;YAEd,6CAA6C;YAC7C,IAAI,gBAAgB,OAAO,IAAI,gBAAgB,OAAO,CAAC,IAAI,IAAI;gBAC7D,8EAA8E;gBAC9E,sEAAsE;gBACtE,mBAAmB;gBACnB,mCAAmC;gBACnC,SAAS,gBAAgB,OAAO,CAAC,IAAI;YACvC,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,SAAS;gBACT;YACF;QACF;QAEA,qEAAqE;QACrE,IAAI,aAAkB,eAAe,WAAW,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS,GAAG;YAChF,QAAQ,GAAG,CAAC;YAEZ,sDAAsD;YACtD,MAAM,oBAAoB,iBAAiB,OAAO;YAClD,IAAI,mBAAmB;gBACrB,QAAQ,GAAG,CAAC,oEAAoE;gBAEhF,oDAAoD;gBACpD,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC,CAAC,CAAC,YAAY,QAAQ;oBAC9D,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;wBACnD,+CAA+C;wBAC/C,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;wBAC9D,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wBAEzE,kCAAkC;wBAClC,MAAM,iBAAiB,eAAe,iBAAiB,kBACrD,eAAe,gBAAgB,eAAe;wBAEhD,IAAI,mBAAmB,YAAY;4BACjC,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;4BAClE,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wBAC/E;wBAEA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,WAAW,kBAAkB,CAAC;oBAC9F;gBACF;YACF;QACF;QAEA,gDAAgD;QAChD,IAAI,MAAM,SAAS,KAAK,SAAS;YAC/B,QAAQ,GAAG,CAAC,CAAC,sDAAsD,CAAC;YACpE,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,MAAM,YAAY,CAAC,cAAc,IAAI,WAAW;YACxG,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,MAAM,YAAY,EAAE;YAC1E,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,oBAAoB,WAAW;YAElF,gCAAgC;YAChC,IAAI,MAAM,YAAY,KAAK,WAAW;gBACpC,QAAQ,GAAG,CAAC,CAAC,8CAA8C,CAAC;gBAE5D,6EAA6E;gBAC7E,IAAI,CAAC,MAAM,YAAY,CAAC,cAAc,IAAI,kBAAkB;oBAC1D,oDAAoD;oBACpD,IAAI,gBAAgB;oBAEpB,IAAI,qBAAqB,qBAAqB;wBAC5C,gBAAgB;oBAClB,OAAO,IAAI,qBAAqB,sBAAsB;wBACpD,gBAAgB;oBAClB,OAAO,IAAI,qBAAqB,yBAAyB;wBACvD,gBAAgB;oBAClB;oBAEA,IAAI,eAAe;wBACjB,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,eAAe;wBACnF,eAAe,kBAAkB;oBACnC;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,MAAM,YAAY,CAAC,cAAc,EAAE;gBACzG;YACF;YAEA,mFAAmF;YACnF,IAAI,CAAC,MAAM,YAAY,CAAC,cAAc,IAAI,kBAAkB;gBAC1D,QAAQ,GAAG,CAAC,CAAC,gEAAgE,EAAE,kBAAkB;gBAEjG,oDAAoD;gBACpD,IAAI,gBAAgB;gBAEpB,IAAI,qBAAqB,qBAAqB;oBAC5C,gBAAgB;gBAClB,OAAO,IAAI,qBAAqB,sBAAsB;oBACpD,gBAAgB;gBAClB,OAAO,IAAI,qBAAqB,yBAAyB;oBACvD,gBAAgB;gBAClB;gBAEA,IAAI,eAAe;oBACjB,QAAQ,GAAG,CAAC,CAAC,+DAA+D,EAAE,eAAe;oBAC7F,eAAe,kBAAkB;gBACnC;YACF;YAEA,uEAAuE;YACvE,IAAI,CAAC,MAAM,YAAY,CAAC,cAAc,EAAE;gBACtC,QAAQ,GAAG,CAAC;gBACZ,0EAA0E;gBAC1E,eAAe,kBAAkB;gBACjC,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,mFAAmF;QACnF,IAAI,aAAkB,eAAe,WAAW,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS,GAAG;YAChF,QAAQ,GAAG,CAAC;YAEZ,sDAAsD;YACtD,MAAM,oBAAoB,iBAAiB,OAAO;YAClD,IAAI,qBAAqB,OAAO,IAAI,CAAC,mBAAmB,MAAM,GAAG,GAAG;gBAClE,QAAQ,GAAG,CAAC,kFAAkF;gBAE9F,oDAAoD;gBACpD,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC,CAAC,CAAC,YAAY,QAAQ;oBAC9D,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;wBACnD,+CAA+C;wBAC/C,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;wBAC9D,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wBAEzE,kCAAkC;wBAClC,MAAM,iBAAiB,eAAe,iBAAiB,kBACrD,eAAe,gBAAgB,eAAe;wBAEhD,IAAI,mBAAmB,YAAY;4BACjC,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;4BAClE,MAAM,YAAY,CAAC,CAAC,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;wBAC/E;wBAEA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,WAAW,gCAAgC,CAAC;oBAC5G;gBACF;YACF;QACF;QAEA,4CAA4C;QAC5C,IAAI,CAAC,MAAM,IAAI,EAAE;YACf,QAAQ,KAAK,CAAC;YACd,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB;YAE/B,0CAA0C;YAC1C,SAAS;YACT;QACF;QAEA,wCAAwC;QACxC,SAAS;QAET,+CAA+C;QAC/C,QAAQ,GAAG,CAAC,gCAAgC;YAC1C,MAAM,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG;YACrC,WAAW,MAAM,SAAS;YAC1B,cAAc,MAAM,YAAY;YAChC,OAAO,MAAM,KAAK;YAClB,aAAa,MAAM,WAAW;YAC9B,cAAc,MAAM,YAAY;YAChC,mBAAmB,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,MAAM;QAC3D;QAEA,qDAAqD;QACrD,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,cAAc;QAC3E,QAAQ,GAAG,CAAC,CAAC,sDAAsD,EAAE,MAAM,YAAY,EAAE;QAEzF,sFAAsF;QACtF,IAAI,gBAAgB,MAAM,YAAY,KAAK,gCAAgC,eAAe;YACxF,QAAQ,GAAG,CAAC,CAAC,yDAAyD,CAAC;YACvE,QAAQ,GAAG,CAAC,CAAC,+DAA+D,EAAE,gCAAgC,eAAe;YAC7H,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,MAAM,YAAY,EAAE;YAClF,QAAQ,GAAG,CAAC,CAAC,qDAAqD,CAAC;YAEnE,6BAA6B;YAC7B,MAAM,oBAAoB,gCAAgC;YAC1D,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,mBAAmB;YAE1E,qDAAqD;YACrD,iDAAiD;YACjD,IAAI,gBAAgB;YAEpB,IAAI,qBAAqB,qBAAqB;gBAC5C,gBAAgB;YAClB,OAAO,IAAI,qBAAqB,sBAAsB;gBACpD,gBAAgB;YAClB,OAAO,IAAI,qBAAqB,yBAAyB;gBACvD,gBAAgB;YAClB;YAEA,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,kBAAkB;YAC9E,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,eAAe;YAEjF,0EAA0E;YAC1E,wBAAwB,mBAAmB;QAC7C,OAAO;YACL,wCAAwC;YACxC,MAAM,qBAAqB,iBAAiB,MAAM,YAAY,CAAC,cAAc,IAAI;YACjF,QAAQ,GAAG,CAAC,CAAC,kDAAkD,EAAE,oBAAoB;YAErF,wEAAwE;YACxE,wBAAwB,MAAM,YAAY,EAAE,oBAAoB,IAAI,CAAC;gBACnE,gCAAgC;gBAChC,QAAQ,GAAG,CAAC;YACd,GAAG,KAAK,CAAC,CAAC;gBACR,QAAQ,KAAK,CAAC,kBAAkB;YAClC;QACF;IACF;IAEA,sDAAsD;IACtD,MAAM,kCAAkC;QACtC,QAAQ,GAAG,CAAC;QAEZ,uCAAuC;QACvC,IAAI,CAAC,MAAM,IAAI,EAAE;YACf,QAAQ,KAAK,CAAC;YACd,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB;YAC/B,SAAS;YACT;QACF;QAEA,QAAQ,GAAG,CAAC,oDAAoD,MAAM,IAAI,CAAC,IAAI;QAE/E,oDAAoD;QACpD,IAAI,oBAAoB,iBAAiB,OAAO;QAEhD,kCAAkC;QAClC,IAAI,CAAC,qBAAqB,OAAO,IAAI,CAAC,mBAAmB,MAAM,KAAK,GAAG;YACrE,IAAI;gBACF,MAAM,sBAAsB,aAAa,OAAO,CAAC;gBACjD,IAAI,qBAAqB;oBACvB,oBAAoB,KAAK,KAAK,CAAC;oBAC/B,QAAQ,GAAG,CAAC,gEAAgE;oBAE5E,4CAA4C;oBAC5C,iBAAiB,OAAO,GAAG;oBAE3B,kCAAkC;oBAClC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,OAAO,IAAI,CAAC,mBAAmB,MAAM,CAAC,+BAA+B,CAAC;oBAC5G,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC,CAAC,CAAC,YAAY,QAAuB;wBAC7E,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;4BACnD,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,YAAY,CAAC,CAAC,CAAC;wBAChG;oBACF;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yEAAyE;YACzF;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,OAAO,IAAI,CAAC,mBAAmB,MAAM,CAAC,wBAAwB,CAAC;QACvG;QAEA,8CAA8C;QAC9C,IAAI,gBAAgB,MAAM,YAAY,CAAC,cAAc;QACrD,IAAI,CAAC,eAAe;YAClB,IAAI;gBACF,MAAM,sBAAsB,aAAa,OAAO,CAAC;gBACjD,IAAI,qBAAqB;oBACvB,gBAAgB;oBAChB,QAAQ,GAAG,CAAC,gEAAgE;oBAE5E,sBAAsB;oBACtB,eAAe,kBAAkB;gBACnC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yEAAyE;YACzF;QACF;QAEA,oCAAoC;QACpC,IAAI,mBAAmB;YACrB,QAAQ,GAAG,CAAC;YAEZ,wDAAwD;YACxD,MAAM,qBAA6C,CAAC;YACpD,IAAI,sBAAsB;YAE1B,yDAAyD;YACzD,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC,CAAC,CAAC,YAAY,QAAQ;gBAC9D,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,YAAY,EAAE;oBACnD,mBAAmB;oBACnB,kBAAkB,CAAC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;oBAC9D,kBAAkB,CAAC,CAAC,OAAO,EAAE,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;oBACzE;oBAEA,+BAA+B;oBAC/B,MAAM,iBAAiB,eAAe,iBAAiB,kBACrD,eAAe,gBAAgB,eAAe;oBAEhD,IAAI,mBAAmB,YAAY;wBACjC,kBAAkB,CAAC,CAAC,OAAO,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG,QAAQ,IAAI;wBAClE,kBAAkB,CAAC,CAAC,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC,GAAG,QAAQ,YAAY;oBAC/E;gBACF;YACF;YAEA,4BAA4B;YAC5B,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,oBAAoB,iCAAiC,CAAC;YAC/F,QAAQ,GAAG,CAAC,0CAA0C,KAAK,SAAS,CAAC;YAErE,2DAA2D;YAC3D,OAAO,OAAO,CAAC,oBAAoB,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM;gBACxD,eAAe,OAAO;YACxB;YAEA,oEAAoE;YACpE,WAAW;gBACT,QAAQ,GAAG,CAAC;gBACZ,kBAAkB;YACpB,GAAG;QACL,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,kBAAkB;QACpB;IAEA,6DAA6D;IAC/D;IAEA,0DAA0D;IAC1D,MAAM,8BAA8B;QAClC,oFAAoF;QAEpF,wDAAwD;QACxD,IAAI,gBAAgB,OAAO,IAAI,gBAAgB,OAAO,CAAC,IAAI,IAAI;YAC7D,sEAAsE;YACtE,mBAAmB;QACrB;QAEA,SAAS;IACX;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,wBAAwB;QACxB,SAAS;QAET,wCAAwC;QACxC,IAAI,SAAS;YACX;QACF;QAEA,iFAAiF;QACjF,WAAW;YACT;YACA,QAAQ,GAAG,CAAC;QACd,GAAG;IACL;IAEA,kCAAkC;IAClC,IAAI,UAAU,UAAU;QACtB,OAAO;IACT;IAEA,qBACE;;YACG,UAAU,iCACT,6LAAC,+IAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,SAAS;;;;;;YAIZ,UAAU,qCACT,6LAAC,kJAAA,CAAA,UAAsB;gBACrB,QAAQ;gBACR,QAAQ,IAAM,SAAS;gBACvB,UAAU;gBACV,mBAAmB;gBACnB,SAAS;gBACT,WAAW,MAAM,SAAS;gBAC1B,cAAc;;;;;;YAIjB,UAAU,wBAAwB,MAAM,IAAI,kBAC3C,6LAAC,8IAAA,CAAA,UAAkB;gBACjB,WAAW,MAAM,IAAI;gBACrB,QAAQ;gBACR,QAAQ;oBACN,wEAAwE;oBACxE,IAAI;wBAAC;wBAAW;wBAAY;qBAAS,CAAC,QAAQ,CAAC,eAAe;wBAC5D,SAAS;oBACX,OAAO;wBACL,yCAAyC;wBACzC,SAAS;oBACX;gBACF;gBACA,SAAS;oBACP,4CAA4C;oBAC5C;oBACA;gBACF;;;;;;YAIH,UAAU,mCACT,6LAAC,2IAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,QAAQ;oBACN,4CAA4C;oBAC5C,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM,IAAI,EAAE;wBAC7C,SAAS;oBACX,OAAO;wBACL,wCAAwC;wBACxC,SAAS;oBACX;gBACF;gBACA,SAAS;oBACP,4CAA4C;oBAC5C;oBACA;gBACF;gBACA,cAAc;gBACd,WAAW,MAAM,SAAS,KAAK,UAAU,MAAM,IAAI,GAAG;gBACtD,WAAW,MAAM,SAAS;gBAC1B,gBAAgB;;;;;;YAInB,UAAU,iCACT,6LAAC,yIAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,QAAQ,IAAM,SAAS;gBACvB,SAAS;oBACP,4CAA4C;oBAC5C;oBACA;gBACF;gBACA,sBAAsB;;;;;;YAIzB,UAAU,oCACT,6LAAC,4IAAA,CAAA,UAAgB;gBACf,UAAU;gBACV,QAAQ;oBACN,gFAAgF;oBAChF,IAAI,MAAM,SAAS,KAAK,WAAW,iBAAiB,aAAa,MAAM,YAAY,KAAK,SAAS;wBAC/F,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF;gBACA,SAAS;oBACP,4CAA4C;oBAC5C;oBACA;gBACF;;;;;;YAIH,CAAC,UAAU,eAAe,UAAU,UAAU,mBAC7C,6LAAC,0IAAA,CAAA,UAAc;gBACb,SAAS;oBACP,4CAA4C;oBAC5C;oBACA;gBACF;gBACA,UAAU;;;;;;;;AAKpB;GAlzCM;;QAC6M,8HAAA,CAAA,YAAS;;;KADtN;uCAozCS", "debugId": null}}, {"offset": {"line": 9544, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9550, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/contexts/GlobalUploadManager.tsx"], "sourcesContent": ["// contexts/GlobalUploadManager.tsx\r\n'use client';\r\n\r\nimport React, { createContext, useState, useContext, ReactNode } from 'react';\r\nimport UploadManager from '../components/upload/UploadManager';\r\n\r\ninterface GlobalUploadContextType {\r\n  isUploadModalOpen: boolean;\r\n  openUploadModal: (initialType?: string) => void;\r\n  closeUploadModal: () => void;\r\n}\r\n\r\nconst GlobalUploadContext = createContext<GlobalUploadContextType | undefined>(undefined);\r\n\r\nexport const GlobalUploadProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);\r\n  const [initialType, setInitialType] = useState<string | undefined>(undefined);\r\n\r\n  const openUploadModal = (type?: string) => {\r\n    setInitialType(type);\r\n    setIsUploadModalOpen(true);\r\n  };\r\n\r\n  const closeUploadModal = () => {\r\n    setIsUploadModalOpen(false);\r\n    setInitialType(undefined);\r\n  };\r\n\r\n  return (\r\n    <GlobalUploadContext.Provider value={{ isUploadModalOpen, openUploadModal, closeUploadModal }}>\r\n      {children}\r\n      {isUploadModalOpen && <UploadManager onClose={closeUploadModal} initialType={initialType} />}\r\n    </GlobalUploadContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useGlobalUpload = (): GlobalUploadContextType => {\r\n  const context = useContext(GlobalUploadContext);\r\n  if (!context) {\r\n    throw new Error('useGlobalUpload must be used within a GlobalUploadProvider');\r\n  }\r\n  return context;\r\n};"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;AAGnC;AACA;;;AAHA;;;AAWA,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAuC;AAExE,MAAM,uBAA0D,CAAC,EAAE,QAAQ,EAAE;;IAClF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnE,MAAM,kBAAkB,CAAC;QACvB,eAAe;QACf,qBAAqB;IACvB;IAEA,MAAM,mBAAmB;QACvB,qBAAqB;QACrB,eAAe;IACjB;IAEA,qBACE,6LAAC,oBAAoB,QAAQ;QAAC,OAAO;YAAE;YAAmB;YAAiB;QAAiB;;YACzF;YACA,mCAAqB,6LAAC,yIAAA,CAAA,UAAa;gBAAC,SAAS;gBAAkB,aAAa;;;;;;;;;;;;AAGnF;GApBa;KAAA;AAsBN,MAAM,kBAAkB;;IAC7B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 9615, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9621, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// Metada<PERSON> is now in a separate file\r\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON> } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport { <PERSON><PERSON>rov<PERSON> } from \"@clerk/nextjs\";\r\nimport { AuthProvider } from \"../contexts/AuthContext\";\r\nimport { UploadProvider } from \"../contexts/UploadContexts\";\r\nimport { GlobalUploadProvider } from \"../contexts/GlobalUploadManager\";\r\nimport <PERSON>ript from \"next/script\";\r\nimport { useEffect, useState } from \"react\";\r\n// Import camera utils to initialize global camera\r\n\r\nconst geistSans = Geist({\r\n  variable: \"--font-geist-sans\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\nconst geistMono = Geist_Mono({\r\n  variable: \"--font-geist-mono\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\nconst inter = Inter({ subsets: [\"latin\"] });\r\n\r\n// Metadata is now in metadata.ts\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  // Use client-side only rendering for the body content to avoid hydration mismatches\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  return (\r\n    <ClerkProvider\r\n      appearance={{\r\n        // Add custom appearance options to avoid hydration issues\r\n        variables: {\r\n          colorPrimary: \"#b31b1e\",\r\n        },\r\n        elements: {\r\n          // Add data-hydration-safe attributes to avoid hydration issues\r\n          rootBox: {\r\n            attributes: {\r\n              \"data-hydration-safe\": \"true\",\r\n            },\r\n          },\r\n          card: {\r\n            attributes: {\r\n              \"data-hydration-safe\": \"true\",\r\n            },\r\n          },\r\n        },\r\n      }}\r\n      // Use the new redirect props format\r\n      redirectUrl=\"/auth/callback\"\r\n      signInUrl=\"/\"\r\n      signUpUrl=\"/\"\r\n    >\r\n      <html lang=\"en\">\r\n        <body\r\n          className={`${geistSans.variable} ${geistMono.variable} antialiased`}\r\n          suppressHydrationWarning // Add this to suppress hydration warnings\r\n        >\r\n          {/* Use a more resilient approach to avoid hydration issues */}\r\n          <div suppressHydrationWarning>\r\n            {!mounted ? (\r\n              <div className=\"flex flex-col justify-center items-center h-screen\">\r\n                <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700\"></div>\r\n                <div className=\"text-gray-600 mt-4\">\r\n                  Loading your account...\r\n                </div>\r\n              </div>\r\n            ) : null}\r\n\r\n            <div style={{ display: mounted ? \"block\" : \"none\" }}>\r\n              <AuthProvider>\r\n                <UploadProvider>\r\n                  <GlobalUploadProvider>{children}</GlobalUploadProvider>\r\n                </UploadProvider>\r\n              </AuthProvider>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Script to sync localStorage token to cookie for middleware */}\r\n          <Script id=\"sync-token-to-cookie\" strategy=\"afterInteractive\">\r\n            {`\r\n              function syncTokenToCookie() {\r\n                try {\r\n                  const token = localStorage.getItem('wedzat_token') || localStorage.getItem('token') || localStorage.getItem('jwt_token');\r\n                  if (token) {\r\n                    document.cookie = 'wedzat_token=' + token + '; path=/; max-age=86400; SameSite=Lax';\r\n                    console.log('Token synced to cookie for middleware');\r\n                  }\r\n\r\n                  // Also sync vendor flag\r\n                  const isVendor = localStorage.getItem('is_vendor');\r\n                  if (isVendor === 'true') {\r\n                    document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';\r\n                    console.log('Vendor flag synced to cookie for middleware');\r\n                  }\r\n                } catch (e) {\r\n                  console.error('Error syncing token to cookie:', e);\r\n                }\r\n              }\r\n\r\n              // Run on page load\r\n              syncTokenToCookie();\r\n\r\n              // Also run when localStorage changes\r\n              window.addEventListener('storage', syncTokenToCookie);\r\n            `}\r\n          </Script>\r\n\r\n          {/* Script to clean up browser extension attributes */}\r\n          <Script id=\"cleanup-extension-attrs\" strategy=\"beforeInteractive\">\r\n            {`\r\n              (function() {\r\n                // Run immediately to clean up before React hydration\r\n                function cleanupExtensionAttributes() {\r\n                  // Target common extension attributes\r\n                  const attributesToRemove = [\r\n                    'bis_skin_checked',\r\n                    '__processed_',\r\n                    'data-bis-'\r\n                  ];\r\n\r\n                  // Get all elements\r\n                  const allElements = document.querySelectorAll('*');\r\n\r\n                  // Remove attributes from each element\r\n                  allElements.forEach(el => {\r\n                    for (let i = 0; i < el.attributes.length; i++) {\r\n                      const attr = el.attributes[i];\r\n                      for (const badAttr of attributesToRemove) {\r\n                        if (attr.name.includes(badAttr)) {\r\n                          el.removeAttribute(attr.name);\r\n                          // Adjust index since we removed an attribute\r\n                          i--;\r\n                          break;\r\n                        }\r\n                      }\r\n                    }\r\n                  });\r\n                }\r\n\r\n                // Run immediately\r\n                cleanupExtensionAttributes();\r\n\r\n                // Also run after a short delay to catch any late additions\r\n                setTimeout(cleanupExtensionAttributes, 0);\r\n              })();\r\n            `}\r\n          </Script>\r\n\r\n          {/* Script to initialize camera and prevent locking */}\r\n          <Script id=\"init-camera\" strategy=\"afterInteractive\">\r\n            {`\r\n              // This script helps ensure the camera is never locked\r\n              // by creating a persistent camera stream\r\n              console.log('Camera initialization script loaded');\r\n            `}\r\n          </Script>\r\n\r\n          {/* Botpress Webchat Scripts */}\r\n          <Script src=\"https://cdn.botpress.cloud/webchat/v2.3/inject.js\" strategy=\"afterInteractive\" />\r\n          <Script src=\"https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js\" strategy=\"afterInteractive\" />\r\n\r\n          {/* Custom script to handle Botpress webchat interaction */}\r\n          <Script id=\"botpress-helper\" strategy=\"afterInteractive\">\r\n            {`\r\n              // Create a global function to open the Botpress webchat\r\n              window.openBotpressChat = function() {\r\n                // Check if the Botpress webchat is available\r\n                if (window.botpressWebChat) {\r\n                  // Try to send the show event\r\n                  try {\r\n                    window.botpressWebChat.sendEvent({ type: 'show' });\r\n                    console.log('Botpress webchat opened successfully');\r\n                  } catch (error) {\r\n                    console.error('Error opening Botpress webchat:', error);\r\n                    // Fallback: Try to click the webchat button\r\n                    try {\r\n                      const botpressButton = document.querySelector('.bp-widget-button');\r\n                      if (botpressButton) {\r\n                        botpressButton.click();\r\n                        console.log('Clicked Botpress button as fallback');\r\n                      } else {\r\n                        console.error('Could not find Botpress button');\r\n                      }\r\n                    } catch (fallbackError) {\r\n                      console.error('Error with fallback method:', fallbackError);\r\n                    }\r\n                  }\r\n                } else {\r\n                  console.error('Botpress webchat not initialized');\r\n                  // Set a flag to open the chat when it becomes available\r\n                  window.openBotpressChatWhenReady = true;\r\n\r\n                  // Check periodically if the webchat becomes available\r\n                  const checkInterval = setInterval(() => {\r\n                    if (window.botpressWebChat) {\r\n                      window.botpressWebChat.sendEvent({ type: 'show' });\r\n                      console.log('Botpress webchat opened after delay');\r\n                      clearInterval(checkInterval);\r\n                      window.openBotpressChatWhenReady = false;\r\n                    }\r\n                  }, 500);\r\n\r\n                  // Clear the interval after 10 seconds to avoid infinite checking\r\n                  setTimeout(() => clearInterval(checkInterval), 10000);\r\n                }\r\n              };\r\n\r\n              // Check if the webchat is loaded and if we need to open it\r\n              document.addEventListener('DOMContentLoaded', () => {\r\n                // Wait a bit for the webchat to initialize\r\n                setTimeout(() => {\r\n                  if (window.openBotpressChatWhenReady && window.botpressWebChat) {\r\n                    window.botpressWebChat.sendEvent({ type: 'show' });\r\n                    console.log('Botpress webchat opened on load');\r\n                    window.openBotpressChatWhenReady = false;\r\n                  }\r\n                }, 1000);\r\n              });\r\n            `}\r\n          </Script>\r\n        </body>\r\n      </html>\r\n    </ClerkProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AA2Be,SAAS,WAAW,EACjC,QAAQ,EAGR;;IACA,oFAAoF;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,WAAW;QACb;+BAAG,EAAE;IAEL,qBACE,6LAAC,4KAAA,CAAA,gBAAa;QACZ,YAAY;YACV,0DAA0D;YAC1D,WAAW;gBACT,cAAc;YAChB;YACA,UAAU;gBACR,+DAA+D;gBAC/D,SAAS;oBACP,YAAY;wBACV,uBAAuB;oBACzB;gBACF;gBACA,MAAM;oBACJ,YAAY;wBACV,uBAAuB;oBACzB;gBACF;YACF;QACF;QACA,oCAAoC;QACpC,aAAY;QACZ,WAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YAAK,MAAK;sBACT,cAAA,6LAAC;gBACC,WAAW,GAAG,4IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;gBACpE,wBAAwB;;kCAGxB,6LAAC;wBAAI,wBAAwB;;4BAC1B,CAAC,wBACA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDAAqB;;;;;;;;;;;uCAIpC;0CAEJ,6LAAC;gCAAI,OAAO;oCAAE,SAAS,UAAU,UAAU;gCAAO;0CAChD,cAAA,6LAAC,2HAAA,CAAA,eAAY;8CACX,cAAA,6LAAC,8HAAA,CAAA,iBAAc;kDACb,cAAA,6LAAC,mIAAA,CAAA,uBAAoB;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC,iIAAA,CAAA,UAAM;wBAAC,IAAG;wBAAuB,UAAS;kCACxC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;YAyBF,CAAC;;;;;;kCAIH,6LAAC,iIAAA,CAAA,UAAM;wBAAC,IAAG;wBAA0B,UAAS;kCAC3C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAoCF,CAAC;;;;;;kCAIH,6LAAC,iIAAA,CAAA,UAAM;wBAAC,IAAG;wBAAc,UAAS;kCAC/B,CAAC;;;;YAIF,CAAC;;;;;;kCAIH,6LAAC,iIAAA,CAAA,UAAM;wBAAC,KAAI;wBAAoD,UAAS;;;;;;kCACzE,6LAAC,iIAAA,CAAA,UAAM;wBAAC,KAAI;wBAAyE,UAAS;;;;;;kCAG9F,6LAAC,iIAAA,CAAA,UAAM;wBAAC,IAAG;wBAAkB,UAAS;kCACnC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAuDF,CAAC;;;;;;;;;;;;;;;;;;;;;;AAMb;GAlNwB;KAAA", "debugId": null}}, {"offset": {"line": 9943, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}