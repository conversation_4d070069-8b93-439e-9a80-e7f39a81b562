"use client";
import React from "react";

interface VendorsProps {
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  setLoading: (loading: boolean) => void;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
}

const Vendors: React.FC<VendorsProps> = ({
  setError,
  setSuccessMessage,
  setLoading,
  loading,
  error,
  successMessage
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold mb-6 text-black">Vendors</h2>
      <p className="text-gray-600">This feature is coming soon. Stay tuned!</p>
      
      {/* Error message */}
      {error && (
        <div className="mt-4 p-2 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Success message */}
      {successMessage && (
        <div className="mt-4 p-2 bg-green-100 text-green-700 rounded-md">
          {successMessage}
        </div>
      )}
    </div>
  );
};

export default Vendors;
