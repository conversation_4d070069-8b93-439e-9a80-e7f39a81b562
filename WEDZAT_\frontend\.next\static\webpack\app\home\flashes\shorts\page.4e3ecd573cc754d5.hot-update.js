"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/shorts/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/shorts/page.tsx":
/*!******************************************!*\
  !*** ./app/home/<USER>/shorts/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashShortsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var _components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/FlashVendorDetails */ \"(app-pages-browser)/./components/FlashVendorDetails.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Navigation */ \"(app-pages-browser)/./components/HomeDashboard/Navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Create a Client component that uses useSearchParams\nfunction FlashShortsContent() {\n    var _flashes_currentIndex, _flashes_currentIndex1, _flashes_currentIndex2, _flashes_currentIndex3;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialIndex = parseInt((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"index\")) || \"0\");\n    const [flashes, setFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialIndex);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedFlashes, setLikedFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [leftSidebarExpanded, setLeftSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightSidebarExpanded, setRightSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showVendorDetails, setShowVendorDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [admiringUsers, setAdmiringUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // Track admiring state\n    const [videoLoading, setVideoLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Refs for video elements\n    const videoRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const youtubeTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // User avatar placeholders - using placeholder.svg which exists\n    const userAvatarPlaceholders = [\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>setIsClient(true)\n    }[\"FlashShortsContent.useEffect\"], []);\n    // Load admiring state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!isClient) return;\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                const admiredUsers = JSON.parse(admiredUsersJson);\n                setAdmiringUsers(admiredUsers);\n            } catch (error) {\n                console.error('Error loading admired users from localStorage:', error);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient\n    ]);\n    // Fetch flashes from the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const fetchFlashes = {\n                \"FlashShortsContent.useEffect.fetchFlashes\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Get token from localStorage\n                        const token = localStorage.getItem(\"token\");\n                        if (!token) {\n                            console.warn(\"No authentication token found\");\n                            setError(\"Authentication required\");\n                            return;\n                        }\n                        const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/flashes?page=\".concat(page, \"&limit=10\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.data && response.data.flashes) {\n                            console.log(\"Flashes API response:\", response.data);\n                            if (page === 1) {\n                                setFlashes(response.data.flashes);\n                                // Initialize loading state for all videos\n                                const initialLoadingState = {};\n                                response.data.flashes.forEach({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                        initialLoadingState[flash.video_id] = true;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                setVideoLoading(initialLoadingState);\n                            } else {\n                                setFlashes({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>[\n                                            ...prev,\n                                            ...response.data.flashes\n                                        ]\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Initialize loading state for new videos\n                                setVideoLoading({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>{\n                                        const newState = {\n                                            ...prev\n                                        };\n                                        response.data.flashes.forEach({\n                                            \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                                newState[flash.video_id] = true;\n                                            }\n                                        }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                        return newState;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                            }\n                            setHasMore(response.data.next_page);\n                        } else {\n                            console.warn(\"Unexpected API response format:\", response.data);\n                            setError(\"Failed to load flashes\");\n                        }\n                    } catch (err) {\n                        console.error(\"Error fetching flashes:\", err);\n                        setError(\"Failed to load flashes\");\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.fetchFlashes\"];\n            fetchFlashes();\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        page\n    ]);\n    // Load more flashes when reaching the end\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (currentIndex >= flashes.length - 2 && hasMore && !loading) {\n                setPage({\n                    \"FlashShortsContent.useEffect\": (prevPage)=>prevPage + 1\n                }[\"FlashShortsContent.useEffect\"]);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length,\n        hasMore,\n        loading\n    ]);\n    // Handle video playback when current index changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (flashes.length === 0 || !isClient) return;\n            // Clear any existing YouTube timer\n            if (youtubeTimerRef.current) {\n                clearTimeout(youtubeTimerRef.current);\n                youtubeTimerRef.current = null;\n            }\n            // Pause all videos\n            Object.values(videoRefs.current).forEach({\n                \"FlashShortsContent.useEffect\": (videoEl)=>{\n                    if (videoEl && !videoEl.paused) {\n                        videoEl.pause();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect\"]);\n            const currentFlash = flashes[currentIndex];\n            if (!currentFlash) return;\n            // Handle YouTube videos with timer-based auto-advance\n            if (isYoutubeVideo(currentFlash) && !isPaused[currentFlash.video_id]) {\n                // Set a timer for YouTube videos (assuming average duration of 30 seconds)\n                // In a real implementation, you might want to use YouTube API to get actual duration\n                youtubeTimerRef.current = setTimeout({\n                    \"FlashShortsContent.useEffect\": ()=>{\n                        console.log(\"YouTube video timer ended: \".concat(currentFlash.video_id, \", auto-advancing to next flash\"));\n                        if (!isPaused[currentFlash.video_id]) {\n                            navigateToNext();\n                        }\n                    }\n                }[\"FlashShortsContent.useEffect\"], 30000); // 30 seconds default duration for YouTube videos\n            } else {\n                // Play current video if not manually paused (for non-YouTube videos)\n                const currentVideoId = currentFlash.video_id;\n                const currentVideo = videoRefs.current[currentVideoId];\n                if (currentVideo && !isPaused[currentVideoId]) {\n                    const playPromise = currentVideo.play();\n                    if (playPromise !== undefined) {\n                        playPromise.catch({\n                            \"FlashShortsContent.useEffect\": (error)=>{\n                                console.error(\"Error playing video:\", error);\n                            }\n                        }[\"FlashShortsContent.useEffect\"]);\n                    }\n                }\n            }\n            // Cleanup function\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes,\n        isClient,\n        isPaused\n    ]);\n    // Handle keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"FlashShortsContent.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") {\n                        navigateToPrevious();\n                    } else if (e.key === \"ArrowDown\" || e.key === \"ArrowRight\") {\n                        navigateToNext();\n                    } else if (e.key === \"Escape\") {\n                        router.push(\"/home/<USER>");\n                    } else if (e.key === \" \" || e.key === \"Spacebar\") {\n                        var _flashes_currentIndex;\n                        // Toggle play/pause on spacebar\n                        togglePlayPause((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleKeyDown\"];\n            window.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle touch events for swiping\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            let startY = 0;\n            // let startTime = 0; // Uncomment if needed for timing-based gestures\n            const handleTouchStart = {\n                \"FlashShortsContent.useEffect.handleTouchStart\": (e)=>{\n                    startY = e.touches[0].clientY;\n                // startTime = Date.now(); // Uncomment if needed for timing-based gestures\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchStart\"];\n            const handleTouchEnd = {\n                \"FlashShortsContent.useEffect.handleTouchEnd\": (e)=>{\n                    const deltaY = e.changedTouches[0].clientY - startY;\n                    // const deltaTime = Date.now() - startTime; // Uncomment if needed for timing-based gestures\n                    // Make touch more responsive by reducing the threshold\n                    if (Math.abs(deltaY) > 30) {\n                        if (deltaY > 0) {\n                            // Swipe down - go to previous\n                            navigateToPrevious();\n                        } else {\n                            // Swipe up - go to next\n                            navigateToNext();\n                        }\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchEnd\"];\n            // Add touchmove handler for more responsive scrolling\n            let lastY = 0;\n            let touchMoveThrottle = false;\n            const handleTouchMove = {\n                \"FlashShortsContent.useEffect.handleTouchMove\": (e)=>{\n                    const currentY = e.touches[0].clientY;\n                    // Only process every few pixels of movement to avoid too many updates\n                    if (!touchMoveThrottle && Math.abs(currentY - lastY) > 20) {\n                        lastY = currentY;\n                        touchMoveThrottle = true;\n                        // Schedule reset of throttle\n                        setTimeout({\n                            \"FlashShortsContent.useEffect.handleTouchMove\": ()=>{\n                                touchMoveThrottle = false;\n                            }\n                        }[\"FlashShortsContent.useEffect.handleTouchMove\"], 100);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchMove\"];\n            const container = containerRef.current;\n            container.addEventListener(\"touchstart\", handleTouchStart);\n            container.addEventListener(\"touchmove\", handleTouchMove, {\n                passive: true\n            });\n            container.addEventListener(\"touchend\", handleTouchEnd);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"touchstart\", handleTouchStart);\n                    container.removeEventListener(\"touchmove\", handleTouchMove);\n                    container.removeEventListener(\"touchend\", handleTouchEnd);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle wheel events for touchpad scrolling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            const handleWheel = {\n                \"FlashShortsContent.useEffect.handleWheel\": (e)=>{\n                    // Debounce the wheel event to prevent too many navigations\n                    if (e.deltaY > 50) {\n                        // Scroll down - go to next\n                        navigateToNext();\n                    } else if (e.deltaY < -50) {\n                        // Scroll up - go to previous\n                        navigateToPrevious();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleWheel\"];\n            const container = containerRef.current;\n            container.addEventListener(\"wheel\", handleWheel, {\n                passive: true\n            });\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"wheel\", handleWheel);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    const navigateToNext = ()=>{\n        if (currentIndex < flashes.length - 1) {\n            setCurrentIndex((prevIndex)=>prevIndex + 1);\n        } else {\n            // When reaching the last video, loop back to the first one\n            setCurrentIndex(0);\n        }\n    };\n    const navigateToPrevious = ()=>{\n        if (currentIndex > 0) {\n            setCurrentIndex((prevIndex)=>prevIndex - 1);\n        }\n    };\n    // Auto-advance to next flash when current video ends\n    const handleVideoEnded = (videoId)=>{\n        console.log(\"Video ended: \".concat(videoId, \", auto-advancing to next flash\"));\n        // Only auto-advance if the video wasn't manually paused\n        if (!isPaused[videoId]) {\n            navigateToNext();\n        }\n    };\n    const toggleLike = async (flashId)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedFlashes.has(flashId);\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: flashId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" flash: \").concat(flashId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedFlashes.has(flashId)) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    // Fetch like status for content\n    const fetchLikeStatus = async (contentIds, contentType)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token || contentIds.length === 0) return;\n            // For now, we'll check each content individually\n            // In a real app, you might want a batch API\n            const likedIds = new Set();\n            for (const contentId of contentIds){\n                try {\n                    const response = await fetch(\"/api/like-status\", {\n                        method: 'POST',\n                        headers: {\n                            'Authorization': \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            content_id: contentId,\n                            content_type: contentType\n                        })\n                    });\n                    if (response.ok) {\n                        const data = await response.json();\n                        if (data.liked) {\n                            likedIds.add(contentId);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error checking like status for \".concat(contentId, \":\"), error);\n                }\n            }\n            setLikedFlashes(likedIds);\n        } catch (error) {\n            console.error('Error fetching like status:', error);\n        }\n    };\n    const togglePlayPause = (videoId)=>{\n        if (!videoId) return;\n        const currentFlash = flashes.find((flash)=>flash.video_id === videoId);\n        if (!currentFlash) return;\n        setIsPaused((prev)=>{\n            const newState = {\n                ...prev\n            };\n            newState[videoId] = !prev[videoId];\n            if (isYoutubeVideo(currentFlash)) {\n                // Handle YouTube video pause/play\n                if (newState[videoId]) {\n                    // Paused - clear the timer\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                } else {\n                    // Resumed - restart the timer\n                    youtubeTimerRef.current = setTimeout(()=>{\n                        console.log(\"YouTube video timer ended: \".concat(videoId, \", auto-advancing to next flash\"));\n                        if (!newState[videoId]) {\n                            navigateToNext();\n                        }\n                    }, 30000); // 30 seconds default duration\n                }\n            } else {\n                // Handle regular video pause/play\n                const videoEl = videoRefs.current[videoId];\n                if (videoEl) {\n                    if (newState[videoId]) {\n                        videoEl.pause();\n                    } else {\n                        videoEl.play().catch((err)=>console.error(\"Error playing video:\", err));\n                    }\n                }\n            }\n            return newState;\n        });\n    };\n    // Extract YouTube video ID from URL\n    const getYoutubeId = (url)=>{\n        if (!url) return \"\";\n        // If it's already just an ID, return it\n        if (url.length < 20 && !url.includes(\"/\")) return url;\n        // Try to extract ID from YouTube URL\n        const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;\n        const match = url.match(regExp);\n        return match && match[2].length === 11 ? match[2] : \"\";\n    };\n    // Get appropriate image source for a flash\n    const getImageSource = (flash)=>{\n        // If we have a thumbnail, use it\n        if (flash.video_thumbnail) {\n            return flash.video_thumbnail;\n        }\n        // If it's a YouTube video, use the YouTube thumbnail\n        if (flash.video_url && flash.video_url.includes(\"youtube\")) {\n            const videoId = getYoutubeId(flash.video_url);\n            if (videoId) {\n                return \"https://img.youtube.com/vi/\".concat(videoId, \"/hqdefault.jpg\");\n            }\n        }\n        // Default fallback - use a local placeholder image\n        return \"/pics/placeholder.svg\";\n    };\n    // Check if the video is from YouTube\n    const isYoutubeVideo = (flash)=>{\n        return typeof flash.video_url === \"string\" && flash.video_url.includes(\"youtube\");\n    };\n    // Format numbers for display (e.g., 1.2K)\n    const formatNumber = (num)=>{\n        if (!num) return \"0\";\n        if (num >= 1000000) {\n            return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        } else if (num >= 1000) {\n            return \"\".concat((num / 1000).toFixed(1), \"K\");\n        }\n        return num.toString();\n    };\n    // Add custom CSS for styling and responsiveness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            // Add a style tag for styling and responsiveness\n            const style = document.createElement(\"style\");\n            style.innerHTML = \"\\n      .shorts-page {\\n        background-color: #f8f8f8;\\n      }\\n\\n      .shorts-container {\\n        background-color: #000;\\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\\n        border-radius: 12px;\\n        overflow: hidden;\\n        position: relative;\\n      }\\n\\n      .shorts-video {\\n        width: 100%;\\n        height: 100%;\\n        object-fit: cover;\\n        background-color: #000;\\n      }\\n\\n      .shorts-controls {\\n        position: absolute;\\n        right: 8px;\\n        bottom: 80px;\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        gap: 16px;\\n        z-index: 20;\\n      }\\n\\n      .shorts-info {\\n        position: absolute;\\n        bottom: 0;\\n        left: 0;\\n        right: 0;\\n        padding: 16px;\\n        background: linear-gradient(transparent, rgba(0,0,0,0.8));\\n        z-index: 10;\\n      }\\n\\n      /* Fixed layout styles for proper centering */\\n      .layout-container {\\n        display: flex;\\n        width: 100%;\\n      }\\n\\n      .left-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .left-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .right-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .right-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .main-content {\\n        flex: 1;\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      /* Mobile responsive styles */\\n      @media (max-width: 768px) {\\n        .shorts-page {\\n          background-color: #000;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n        }\\n\\n        .shorts-container {\\n          width: 100vw !important;\\n          max-width: none !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          max-height: none !important;\\n          border-radius: 0 !important;\\n          border: none !important;\\n          margin: 0 !important;\\n          padding-bottom: 20px !important;\\n        }\\n\\n        .mobile-video-item {\\n          margin-bottom: 10px !important;\\n          border-radius: 8px !important;\\n          overflow: hidden !important;\\n        }\\n\\n        .mobile-nav-buttons {\\n          position: fixed !important;\\n          left: 16px !important;\\n          top: 50% !important;\\n          transform: translateY(-50%) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-nav-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-nav-button:disabled {\\n          background: rgba(255, 255, 255, 0.3) !important;\\n          color: rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-interaction-buttons {\\n          position: fixed !important;\\n          right: 16px !important;\\n          bottom: calc(120px + env(safe-area-inset-bottom, 20px)) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-interaction-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          color: white !important;\\n        }\\n\\n        .mobile-back-button {\\n          position: fixed !important;\\n          top: 20px !important;\\n          left: 16px !important;\\n          z-index: 50 !important;\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-user-info {\\n          position: absolute !important;\\n          top: 20px !important;\\n          left: 80px !important;\\n          right: 16px !important;\\n          z-index: 50 !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 12px !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: space-between !important;\\n        }\\n\\n        .mobile-unlock-vendor {\\n          position: absolute !important;\\n          bottom: calc(20px + env(safe-area-inset-bottom, 10px)) !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n          background: #B31B1E !important;\\n          color: white !important;\\n          border: none !important;\\n          border-radius: 8px !important;\\n          padding: 12px 24px !important;\\n          font-weight: 600 !important;\\n          box-shadow: 0 4px 12px rgba(179, 27, 30, 0.4) !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n        }\\n\\n        .mobile-vendor-details {\\n          position: absolute !important;\\n          bottom: 80px !important;\\n          left: 16px !important;\\n          right: 16px !important;\\n          z-index: 60 !important;\\n          background: rgba(255, 255, 255, 0.95) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 16px !important;\\n          max-height: 50vh !important;\\n          overflow-y: auto !important;\\n          color: black !important;\\n        }\\n\\n        .mobile-progress-indicator {\\n          position: fixed !important;\\n          top: 80px !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n        }\\n\\n        /* Hide desktop navigation buttons on mobile */\\n        .desktop-nav-buttons {\\n          display: none !important;\\n        }\\n\\n        .desktop-interaction-buttons {\\n          display: none !important;\\n        }\\n\\n        /* Ensure main content takes full space on mobile */\\n        .main-content-mobile {\\n          padding: 0 !important;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n          margin: 0 !important;\\n          width: 100vw !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          position: relative !important;\\n        }\\n      }\\n\\n      /* Desktop styles */\\n      @media (min-width: 769px) {\\n        .mobile-nav-buttons,\\n        .mobile-interaction-buttons,\\n        .mobile-back-button,\\n        .mobile-user-info,\\n        .mobile-unlock-vendor,\\n        .mobile-progress-indicator {\\n          display: none !important;\\n        }\\n      }\\n    \";\n            document.head.appendChild(style);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    document.head.removeChild(style);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"opacity-0\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 803,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen w-full shorts-page\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.TopNavigation, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 809,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full h-[calc(100vh-80px)] md:mt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-sidebar \".concat(leftSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.SideNavigation, {\n                            expanded: leftSidebarExpanded,\n                            onExpand: ()=>setLeftSidebarExpanded(true),\n                            onCollapse: ()=>setLeftSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center px-4 md:px-4 px-0 relative main-content-mobile\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"absolute top-4 left-4 z-50 bg-white rounded-full p-3 text-black shadow-lg hover:bg-gray-200 transition-colors flex items-center justify-center hidden md:flex\",\n                                style: {\n                                    width: \"48px\",\n                                    height: \"48px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"mobile-back-button md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center w-full md:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-nav-buttons flex flex-col items-center justify-center space-y-4 mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === 0 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === flashes.length - 1 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-nav-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: containerRef,\n                                        className: \"shorts-container relative w-full md:w-[400px] h-full md:h-[min(calc(100vh-100px),89vh)]\",\n                                        style: {\n                                            // Desktop styles\n                                            ... true && window.innerWidth >= 768 ? {\n                                                width: \"400px\",\n                                                height: \"min(calc(100vh - 100px), 89vh)\",\n                                                margin: \"0 auto\",\n                                                border: \"2px solid #B31B1E\",\n                                                borderRadius: \"12px\",\n                                                overflow: \"hidden\"\n                                            } : {\n                                                // Mobile styles - full screen with safe area\n                                                width: \"100vw\",\n                                                height: \"calc(100vh - env(safe-area-inset-bottom, 20px))\",\n                                                margin: \"0\",\n                                                border: \"none\",\n                                                borderRadius: \"0\",\n                                                overflow: \"hidden\",\n                                                paddingBottom: \"env(safe-area-inset-bottom, 20px)\"\n                                            }\n                                        },\n                                        children: [\n                                            loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                                children: \"Loading flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 927,\n                                                columnNumber: 15\n                                            }, this),\n                                            error && !loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-red-500\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 15\n                                            }, this),\n                                            flashes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full w-full transition-transform duration-300 ease-out\",\n                                                style: {\n                                                    transform: \"translateY(-\".concat(currentIndex * 102, \"%)\")\n                                                },\n                                                children: flashes.map((flash, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full w-full flex items-center justify-center relative bg-black \".concat( true && window.innerWidth < 768 ? 'mobile-video-item' : ''),\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(index * 102, \"%\"),\n                                                            left: 0,\n                                                            right: 0,\n                                                            bottom: 0,\n                                                            overflow: \"hidden\",\n                                                            borderRadius: \"8px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-full w-full relative overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 overflow-hidden\",\n                                                                    children: isYoutubeVideo(flash) ? // YouTube iframe for YouTube videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 973,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 974,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 972,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 971,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                                                src: \"https://www.youtube.com/embed/\".concat(getYoutubeId(flash.video_url), \"?autoplay=1&controls=0&rel=0&showinfo=0&mute=0\"),\n                                                                                title: flash.video_name,\n                                                                                className: \"shorts-video\",\n                                                                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                                                                allowFullScreen: true,\n                                                                                onLoad: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 978,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 968,\n                                                                        columnNumber: 27\n                                                                    }, this) : // Video player for Cloudfront videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full overflow-hidden\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 998,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 999,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 997,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 996,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                ref: (el)=>{\n                                                                                    videoRefs.current[flash.video_id] = el;\n                                                                                },\n                                                                                src: flash.video_url,\n                                                                                className: \"shorts-video\",\n                                                                                playsInline: true,\n                                                                                muted: false,\n                                                                                controls: false,\n                                                                                poster: getImageSource(flash),\n                                                                                onLoadStart: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: true\n                                                                                        }));\n                                                                                },\n                                                                                onCanPlay: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onError: ()=>{\n                                                                                    console.error(\"Failed to load video: \".concat(flash.video_name));\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onEnded: ()=>handleVideoEnded(flash.video_id)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1003,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    togglePlayPause(flash.video_id);\n                                                                                },\n                                                                                className: \"absolute inset-0 w-full h-full flex items-center justify-center z-10 group\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(isPaused[flash.video_id] ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\", \" transition-opacity duration-200 bg-black/40 rounded-full p-4 shadow-lg\"),\n                                                                                    children: isPaused[flash.video_id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"white\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                                            points: \"5 3 19 12 5 21 5 3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1052,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1041,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"6\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1066,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"14\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1072,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1055,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1033,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1026,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 993,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 left-4 right-4 z-20 flex items-center bg-black/20 backdrop-blur-sm rounded-lg p-2 hidden md:flex\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1090,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1101,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1102,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1100,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1089,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1166,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1088,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-user-info md:hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1174,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1185,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1186,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1184,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1173,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1250,\n                                                                                columnNumber: 67\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1191,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1172,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-8 left-0 right-0 flex justify-center z-10 hidden md:flex\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-[#B31B1E] text-white text-sm font-medium px-4 py-2 rounded-md flex items-center\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowVendorDetails((prev)=>({\n                                                                                ...prev,\n                                                                                [flash.video_id]: !prev[flash.video_id]\n                                                                            }));\n                                                                    },\n                                                                    children: [\n                                                                        \"Unlock Vendor\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4 ml-1\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1269,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1268,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1257,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1256,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mobile-unlock-vendor md:hidden\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    setShowVendorDetails((prev)=>({\n                                                                            ...prev,\n                                                                            [flash.video_id]: !prev[flash.video_id]\n                                                                        }));\n                                                                },\n                                                                children: [\n                                                                    \"Unlock Vendor\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        className: \"h-4 w-4 ml-1\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1288,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1287,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-20 left-4 right-4 p-4 bg-white/90 rounded-lg text-black max-h-[40%] overflow-y-auto z-30 hidden md:block\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1296,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1295,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === currentIndex && showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-vendor-details md:hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1306,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1305,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(flash.video_id, \"-\").concat(index), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 15\n                                            }, this),\n                                            loading && page > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-20 left-0 right-0 text-center text-white bg-black/50 py-2 mx-auto w-48 rounded-full backdrop-blur-sm z-20\",\n                                                children: \"Loading more flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-14 left-0 right-0 px-4 z-20 hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1328,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1326,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mobile-progress-indicator md:hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1345,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1353,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1343,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1342,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 901,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-interaction-buttons flex flex-col items-center justify-center space-y-6 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100 mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1364,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1365,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1366,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1363,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1362,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex1 = flashes[currentIndex]) === null || _flashes_currentIndex1 === void 0 ? void 0 : _flashes_currentIndex1.video_id) || '') ? \"#B31B1E\" : \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1381,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1371,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1388,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1387,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1386,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1395,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1396,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1394,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1393,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1360,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-interaction-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash === null || currentFlash === void 0 ? void 0 : currentFlash.user_id) {\n                                                        // Navigate to user profile - you can implement this navigation\n                                                        console.log('Navigate to profile:', currentFlash.user_id);\n                                                    // router.push(`/profile/${currentFlash.user_id}`);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1416,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"7\",\n                                                            r: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1417,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1415,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1404,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex2 = flashes[currentIndex]) === null || _flashes_currentIndex2 === void 0 ? void 0 : _flashes_currentIndex2.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex3 = flashes[currentIndex]) === null || _flashes_currentIndex3 === void 0 ? void 0 : _flashes_currentIndex3.video_id) || '') ? \"#B31B1E\" : \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1432,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1422,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1439,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1438,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1437,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1446,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1447,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1445,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1444,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1454,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1455,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1456,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1452,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1402,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 828,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-sidebar \".concat(rightSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.RightSidebar, {\n                            expanded: rightSidebarExpanded,\n                            onExpand: ()=>setRightSidebarExpanded(true),\n                            onCollapse: ()=>setRightSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 1469,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 1464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 813,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 807,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashShortsContent, \"aeQpwdVKjXsnvNtC+OoDi2XkRdk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = FlashShortsContent;\n// Loading fallback component\nfunction FlashShortsLoading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-screen w-full bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-white text-xl\",\n            children: \"Loading flashes...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1484,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1483,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FlashShortsLoading;\n// Main page component with Suspense\nfunction FlashShortsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsLoading, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1492,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1493,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1492,\n        columnNumber: 5\n    }, this);\n}\n_c2 = FlashShortsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FlashShortsContent\");\n$RefreshReg$(_c1, \"FlashShortsLoading\");\n$RefreshReg$(_c2, \"FlashShortsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/shorts/page.tsx\n"));

/***/ })

});