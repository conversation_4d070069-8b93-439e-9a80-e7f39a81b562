/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/photo/[id]/route";
exports.ids = ["app/api/photo/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/photo/[id]/route.ts":
/*!*************************************!*\
  !*** ./app/api/photo/[id]/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\n\nasync function GET(request, context) {\n    // Await params before accessing properties (Next.js 13+ requirement)\n    const resolvedParams = await context.params;\n    const photoId = resolvedParams.id;\n    try {\n        // Get the authorization header (same pattern as working like/unlike routes)\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authorization header is required'\n            }, {\n                status: 401\n            });\n        }\n        console.log(`[PHOTO API] Fetching photo details for ID: ${photoId}`);\n        console.log('[PHOTO API] Authorization header received:', authHeader.substring(0, 50) + '...');\n        console.log('[PHOTO API] Full URL:', `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/photo/${photoId}`);\n        // Call the updated backend endpoint (same pattern as like/unlike routes)\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/photo/${photoId}`, {\n            headers: {\n                'Authorization': authHeader,\n                'Content-Type': 'application/json'\n            },\n            timeout: 15000\n        });\n        console.log('Photo API response received:', response.status);\n        // Check if the backend returned an error response\n        if (response.data.statusCode && response.data.statusCode !== 200) {\n            const errorBody = typeof response.data.body === 'string' ? JSON.parse(response.data.body) : response.data.body;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: errorBody.error || 'Backend error'\n            }, {\n                status: response.data.statusCode\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response.data);\n    } catch (error) {\n        console.error('Error fetching photo:', error);\n        // Detailed error response\n        let errorMessage = 'Failed to fetch photo details';\n        let statusCode = 500;\n        if (error.response) {\n            console.error('Response error data:', error.response.data);\n            statusCode = error.response.status;\n            errorMessage = `Server error: ${error.response.data?.error || error.response.data?.message || error.message}`;\n        } else if (error.request) {\n            console.error('Request error:', error.request);\n            errorMessage = 'No response received from server';\n        } else {\n            console.error('Error message:', error.message);\n            errorMessage = error.message;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage\n        }, {\n            status: statusCode\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/photo/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fphoto%2F%5Bid%5D%2Froute&page=%2Fapi%2Fphoto%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphoto%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fphoto%2F%5Bid%5D%2Froute&page=%2Fapi%2Fphoto%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphoto%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_app_api_photo_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/photo/[id]/route.ts */ \"(rsc)/./app/api/photo/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/photo/[id]/route\",\n        pathname: \"/api/photo/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/photo/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\api\\\\photo\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_app_api_photo_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fphoto%2F%5Bid%5D%2Froute&page=%2Fapi%2Fphoto%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphoto%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fphoto%2F%5Bid%5D%2Froute&page=%2Fapi%2Fphoto%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphoto%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();