"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/[id]/page.tsx":
/*!***************************************!*\
  !*** ./app/home/<USER>/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PhotoViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Navigation */ \"(app-pages-browser)/./components/HomeDashboard/Navigation.tsx\");\n/* harmony import */ var _components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _components_HomeDashboard_Photos__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Photos */ \"(app-pages-browser)/./components/HomeDashboard/Photos.tsx\");\n/* harmony import */ var _components_DetailPageLazyLoad__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../../components/DetailPageLazyLoad */ \"(app-pages-browser)/./components/DetailPageLazyLoad.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Main component for the photo viewing page\nfunction PhotoViewPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const photoId = params === null || params === void 0 ? void 0 : params.id;\n    const [photo, setPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarExpanded, setSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightSidebarExpanded, setRightSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likeCount, setLikeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [relatedPhotos, setRelatedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiking, setIsLiking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const imageRef = useRef(null);\n    // Set client-side rendering flag\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PhotoViewPage.useEffect\": ()=>setIsClient(true)\n    }[\"PhotoViewPage.useEffect\"], []);\n    // Fetch photo details when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PhotoViewPage.useEffect\": ()=>{\n            const fetchPhotoDetails = {\n                \"PhotoViewPage.useEffect.fetchPhotoDetails\": async ()=>{\n                    console.log('=== Starting photo fetch ===');\n                    console.log('Photo ID:', photoId);\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // Get token from localStorage\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                        console.log('Token found:', token ? 'Yes' : 'No');\n                        if (token) {\n                            console.log('Token preview:', token.substring(0, 50) + '...');\n                            console.log('Token length:', token.length);\n                            console.log('Token starts with eyJ (JWT):', token.startsWith('eyJ'));\n                            console.log('Full token:', token);\n                        }\n                        if (!token) {\n                            console.warn('No authentication token found');\n                            setError('Authentication required. Please log in.');\n                            setLoading(false);\n                            return;\n                        }\n                        // Store token in a cookie for server components to access\n                        document.cookie = \"token=\".concat(token, \"; path=/; max-age=3600; SameSite=Strict\");\n                        // First test if the photos list API is working\n                        console.log('Testing photos list API first...');\n                        try {\n                            const photosTestResponse = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get('/api/photos?page=1&limit=1', {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token)\n                                }\n                            });\n                            console.log('Photos list API test successful:', photosTestResponse.status);\n                        } catch (photosError) {\n                            var _photosError_response, _photosError_response_data, _photosError_response1;\n                            console.error('Photos list API test failed:', ((_photosError_response = photosError.response) === null || _photosError_response === void 0 ? void 0 : _photosError_response.data) || photosError.message);\n                            throw new Error('Photos list API is also failing: ' + (((_photosError_response1 = photosError.response) === null || _photosError_response1 === void 0 ? void 0 : (_photosError_response_data = _photosError_response1.data) === null || _photosError_response_data === void 0 ? void 0 : _photosError_response_data.error) || photosError.message));\n                        }\n                        // Try to get the photo from the photos list first as a workaround\n                        console.log('Trying to get photo from photos list...');\n                        let photoFromList = null;\n                        try {\n                            const photosResponse = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get('/api/photos?page=1&limit=50', {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token)\n                                }\n                            });\n                            if (photosResponse.data && photosResponse.data.photos) {\n                                photoFromList = photosResponse.data.photos.find({\n                                    \"PhotoViewPage.useEffect.fetchPhotoDetails\": (p)=>p.photo_id === photoId\n                                }[\"PhotoViewPage.useEffect.fetchPhotoDetails\"]);\n                                if (photoFromList) {\n                                    console.log('Found photo in photos list:', photoFromList);\n                                    // Add some default values that might be missing\n                                    photoFromList.liked = false;\n                                    photoFromList.comments = [];\n                                }\n                            }\n                        } catch (listError) {\n                            console.error('Failed to get photo from list:', listError);\n                        }\n                        // Try the individual photo API first, but fall back to photos list if it fails\n                        let response;\n                        try {\n                            var _response_data;\n                            response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/api/photo/\".concat(photoId), {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token)\n                                }\n                            });\n                            // Check if the response contains an error\n                            if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.error) {\n                                throw new Error(response.data.error);\n                            }\n                        } catch (individualError) {\n                            var _individualError_response, _individualError_response1;\n                            console.error('Individual photo API failed:', {\n                                error: individualError,\n                                response: (_individualError_response = individualError.response) === null || _individualError_response === void 0 ? void 0 : _individualError_response.data,\n                                message: individualError.message,\n                                status: (_individualError_response1 = individualError.response) === null || _individualError_response1 === void 0 ? void 0 : _individualError_response1.status\n                            });\n                            // If individual photo API fails, try to get the photo from the photos list\n                            if (photoFromList) {\n                                console.log('Using photo data from photos list as fallback');\n                                response = {\n                                    data: photoFromList\n                                };\n                            } else {\n                                // Try to fetch from photos list API as a last resort\n                                try {\n                                    var _photosResponse_data_photos, _photosResponse_data;\n                                    console.log('Attempting to fetch photo from photos list API...');\n                                    const photosResponse = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get('/api/photos?page=1&limit=50', {\n                                        headers: {\n                                            Authorization: \"Bearer \".concat(token)\n                                        }\n                                    });\n                                    const foundPhoto = (_photosResponse_data = photosResponse.data) === null || _photosResponse_data === void 0 ? void 0 : (_photosResponse_data_photos = _photosResponse_data.photos) === null || _photosResponse_data_photos === void 0 ? void 0 : _photosResponse_data_photos.find({\n                                        \"PhotoViewPage.useEffect.fetchPhotoDetails\": (p)=>p.photo_id === photoId\n                                    }[\"PhotoViewPage.useEffect.fetchPhotoDetails\"]);\n                                    if (foundPhoto) {\n                                        console.log('Found photo in photos list, using as fallback');\n                                        response = {\n                                            data: foundPhoto\n                                        };\n                                    } else {\n                                        throw new Error('Photo not found in photos list');\n                                    }\n                                } catch (listError) {\n                                    var _listError_response, _listError_response1;\n                                    console.error('Photos list API also failed:', {\n                                        error: listError,\n                                        response: (_listError_response = listError.response) === null || _listError_response === void 0 ? void 0 : _listError_response.data,\n                                        message: listError.message,\n                                        status: (_listError_response1 = listError.response) === null || _listError_response1 === void 0 ? void 0 : _listError_response1.status\n                                    });\n                                    throw new Error('Failed to load photo: Both individual photo API and photos list API failed');\n                                }\n                            }\n                        }\n                        if (response.data) {\n                            if (response.data.error) {\n                                throw new Error(response.data.error);\n                            }\n                            // The backend returns the photo data directly\n                            const photoData = response.data;\n                            if (!photoData.photo_id) {\n                                throw new Error('Invalid photo data received - missing photo_id');\n                            }\n                            setPhoto(photoData);\n                            setIsLiked(photoData.liked || false);\n                            setLikeCount(photoData.photo_likes || 0);\n                            // Fetch related photos\n                            fetchRelatedPhotos(photoData.photo_category, photoData.user_id);\n                        } else {\n                            throw new Error('No data received from server');\n                        }\n                    } catch (err) {\n                        var _err_response, _err_response1, _err_response2, _err_response3, _err_response_data, _err_response4;\n                        console.error('Error fetching photo details:', {\n                            error: err,\n                            message: err.message,\n                            response: (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data,\n                            status: (_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status,\n                            stack: err.stack\n                        });\n                        let errorMessage = 'Failed to load photo. Please try again later.';\n                        if (((_err_response2 = err.response) === null || _err_response2 === void 0 ? void 0 : _err_response2.status) === 401) {\n                            errorMessage = 'Authentication failed. Please log in again.';\n                        } else if (((_err_response3 = err.response) === null || _err_response3 === void 0 ? void 0 : _err_response3.status) === 404) {\n                            errorMessage = 'Photo not found.';\n                        } else if ((_err_response4 = err.response) === null || _err_response4 === void 0 ? void 0 : (_err_response_data = _err_response4.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) {\n                            errorMessage = err.response.data.error;\n                        } else if (err.message) {\n                            errorMessage = err.message;\n                        }\n                        setError(errorMessage);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"PhotoViewPage.useEffect.fetchPhotoDetails\"];\n            if (photoId && isClient) {\n                fetchPhotoDetails();\n            }\n        }\n    }[\"PhotoViewPage.useEffect\"], [\n        photoId,\n        isClient\n    ]);\n    // Function to fetch related photos\n    const fetchRelatedPhotos = async (category, userId)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) return;\n            // Prioritize category if available, otherwise fetch user's photos\n            const queryParam = category ? \"?category=\".concat(encodeURIComponent(category), \"&limit=6\") : userId ? \"?user_id=\".concat(encodeURIComponent(userId), \"&limit=6\") : '?limit=6';\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/api/photos\".concat(queryParam), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.data && response.data.photos) {\n                // Filter out the current photo\n                const filtered = response.data.photos.filter((p)=>p.photo_id !== photoId);\n                setRelatedPhotos(filtered.slice(0, 5)); // Limit to 5 related photos\n            }\n        } catch (err) {\n            console.error('Error fetching related photos:', err);\n        }\n    };\n    // Function to handle like/unlike\n    const handleLikeToggle = async ()=>{\n        if (isLiking) return; // Prevent multiple clicks\n        console.log('📸 Individual photo like button clicked for ID:', photoId);\n        try {\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('📸 No authentication token found');\n                return;\n            }\n            setIsLiking(true);\n            // Optimistically update UI\n            const newLikedState = !isLiked;\n            setIsLiked(newLikedState);\n            setLikeCount((prev)=>newLikedState ? prev + 1 : prev - 1);\n            // Make API request to like/unlike\n            const endpoint = newLikedState ? '/api/like' : '/api/unlike';\n            console.log('📸 Making API call to:', endpoint);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(endpoint, {\n                content_id: photoId,\n                content_type: 'photo'\n            }, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('📸 API Success Response:', response.data);\n            // Update localStorage for persistence\n            const likedPhotosData = localStorage.getItem('likedPhotos');\n            const likedPhotosArray = likedPhotosData ? JSON.parse(likedPhotosData) : [];\n            if (newLikedState) {\n                // Add to localStorage\n                if (!likedPhotosArray.includes(photoId)) {\n                    likedPhotosArray.push(photoId);\n                    localStorage.setItem('likedPhotos', JSON.stringify(likedPhotosArray));\n                    console.log('📸 Added to localStorage');\n                }\n            } else {\n                // Remove from localStorage\n                const updatedArray = likedPhotosArray.filter((id)=>id !== photoId);\n                localStorage.setItem('likedPhotos', JSON.stringify(updatedArray));\n                console.log('📸 Removed from localStorage');\n            }\n            console.log(\"\\uD83D\\uDCF8 Successfully \".concat(newLikedState ? 'liked' : 'unliked', \" photo: \").concat(photoId));\n        } catch (err) {\n            console.error('Error toggling like:', err);\n            // Revert optimistic update on error\n            setIsLiked(!isLiked);\n            setLikeCount((prev)=>isLiked ? prev + 1 : prev - 1);\n        } finally{\n            setIsLiking(false);\n        }\n    };\n    // Function to toggle fullscreen mode\n    const toggleFullscreen = ()=>{\n        setIsFullscreen(!isFullscreen);\n    };\n    // Function to navigate back\n    const goBack = ()=>{\n        router.back();\n    };\n    // Add this function near your other utility functions\n    const getImageSource = (photoUrl)=>{\n        if (!photoUrl || photoUrl.trim() === '') {\n            console.warn('Empty photo URL detected');\n            return '/pics/placeholder.svg';\n        }\n        return photoUrl;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: isClient ? \"flex flex-col min-h-screen bg-white w-full\" : \"opacity-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__.TopNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__.SideNavigation, {\n                        expanded: sidebarExpanded,\n                        onExpand: ()=>setSidebarExpanded(true),\n                        onCollapse: ()=>setSidebarExpanded(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 py-4 pr-4 pl-0 bg-white \".concat(sidebarExpanded ? \"md:ml-48\" : \"md:ml-20\"),\n                        style: {\n                            marginTop: \"80px\",\n                            transition: \"all 300ms ease-in-out\",\n                            minHeight: \"calc(100vh - 80px)\",\n                            paddingBottom: \"40px\",\n                            overflowY: \"auto\",\n                            overflowX: \"hidden\",\n                            marginRight: rightSidebarExpanded ? \"320px\" : \"0\",\n                            paddingRight: \"20px\",\n                            paddingLeft: \"0\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-8 max-w-[1100px] w-full pl-2\",\n                            children: [\n                                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-10 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Loading photo...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, this),\n                                error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-10 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-500 mb-4\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goBack,\n                                            className: \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                                            children: \"Go Back\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                !loading && !error && photo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: goBack,\n                                                className: \"flex items-center text-gray-600 hover:text-red-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        className: \"mr-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"15 18 9 12 15 6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Back\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-semibold\",\n                                                            children: photo.photo_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        photo.created_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mt-1\",\n                                                            children: new Date(photo.created_at).toLocaleDateString('en-US', {\n                                                                year: 'numeric',\n                                                                month: 'long',\n                                                                day: 'numeric'\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            username: photo.username || \"user\",\n                                                            size: \"md\",\n                                                            isGradientBorder: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm font-medium\",\n                                                            children: photo.username\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: imageRef,\n                                            className: \"relative \".concat(isFullscreen ? \"fixed inset-0 z-50 bg-black flex items-center justify-center\" : \"w-full rounded-lg overflow-hidden shadow-lg\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative \".concat(isFullscreen ? \"w-full h-full\" : \"w-full\"),\n                                                    style: {\n                                                        height: isFullscreen ? \"100vh\" : \"calc(100vh - 300px)\",\n                                                        maxHeight: isFullscreen ? \"100vh\" : \"70vh\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        src: getImageSource(photo.photo_url),\n                                                        alt: photo.photo_name || \"Photo\",\n                                                        fill: true,\n                                                        sizes: \"(max-width: 640px) 100vw, (max-width: 1024px) 80vw, 70vw\",\n                                                        className: \"object-contain\",\n                                                        priority: true,\n                                                        unoptimized: true,\n                                                        onError: (e)=>{\n                                                            console.error(\"Failed to load image: \".concat(photo.photo_url));\n                                                            const imgElement = e.target;\n                                                            imgElement.src = \"/pics/placeholder.svg\";\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: toggleFullscreen,\n                                                    className: \"absolute bottom-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all\",\n                                                    \"aria-label\": isFullscreen ? \"Exit fullscreen\" : \"View fullscreen\",\n                                                    children: isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"4 14 10 14 10 20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"20 10 14 10 14 4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"14\",\n                                                                y1: \"10\",\n                                                                x2: \"21\",\n                                                                y2: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"3\",\n                                                                y1: \"21\",\n                                                                x2: \"10\",\n                                                                y2: \"14\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"15 3 21 3 21 9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"9 21 3 21 3 15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"21\",\n                                                                y1: \"3\",\n                                                                x2: \"14\",\n                                                                y2: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"3\",\n                                                                y1: \"21\",\n                                                                x2: \"10\",\n                                                                y2: \"14\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: toggleFullscreen,\n                                                    className: \"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all\",\n                                                    \"aria-label\": \"Close fullscreen\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"24\",\n                                                        height: \"24\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"18\",\n                                                                y1: \"6\",\n                                                                x2: \"6\",\n                                                                y2: \"18\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"6\",\n                                                                y1: \"6\",\n                                                                x2: \"18\",\n                                                                y2: \"18\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                !loading && !error && photo && !isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex flex-col gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-6 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"18\",\n                                                            height: \"18\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                photo.photo_views || 0,\n                                                                \" views\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center gap-1 hover:text-red-500 \".concat(isLiking ? 'opacity-50 cursor-not-allowed' : ''),\n                                                    onClick: handleLikeToggle,\n                                                    disabled: isLiking,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"18\",\n                                                            height: \"18\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: isLiked ? \"currentColor\" : \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            className: isLiked ? \"text-red-500\" : \"\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                likeCount,\n                                                                \" likes\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"18\",\n                                                            height: \"18\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                photo.photo_comments || 0,\n                                                                \" comments\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this),\n                                        photo.photo_description && photo.photo_description.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-1\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: photo.photo_description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 19\n                                        }, this),\n                                        photo.photo_tags && photo.photo_tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-1\",\n                                                    children: \"Tags\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: photo.photo_tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-gray-100 rounded-full text-sm text-gray-700\",\n                                                            children: tag\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 19\n                                        }, this),\n                                        photo.comments && photo.comments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-2\",\n                                                    children: \"Comments\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: photo.comments.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-3 p-3 bg-gray-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    username: comment.username,\n                                                                    size: \"sm\",\n                                                                    isGradientBorder: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: comment.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-gray-700\",\n                                                                            children: comment.comment_text\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: new Date(comment.created_at).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, comment.comment_id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 19\n                                        }, this),\n                                        relatedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-4\",\n                                                    children: \"Related Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                                                    children: relatedPhotos.map((relatedPhoto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer\",\n                                                            style: {\n                                                                height: \"160px\"\n                                                            },\n                                                            onClick: ()=>{\n                                                                window.location.href = \"/home/<USER>/\".concat(relatedPhoto.photo_id);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative w-full h-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        src: relatedPhoto.photo_url || \"/pics/placeholder.svg\",\n                                                                        alt: relatedPhoto.photo_name || \"Related photo\",\n                                                                        fill: true,\n                                                                        sizes: \"(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw\",\n                                                                        className: \"object-cover\",\n                                                                        unoptimized: true,\n                                                                        onError: (e)=>{\n                                                                            const imgElement = e.target;\n                                                                            if (imgElement) {\n                                                                                imgElement.src = '/pics/placeholder.svg';\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium truncate\",\n                                                                        children: relatedPhoto.photo_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, \"\".concat(relatedPhoto.photo_id, \"-\").concat(index), true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-4\",\n                                                    children: \"More Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DetailPageLazyLoad__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    id: \"more-photos-section\",\n                                                    index: 1,\n                                                    rootMargin: \"0px 0px 200px 0px\",\n                                                    children: (param)=>{\n                                                        let { shouldLoad } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto w-full mx-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Photos__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                shouldLoad: shouldLoad\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__.RightSidebar, {\n                        expanded: rightSidebarExpanded,\n                        onExpand: ()=>setRightSidebarExpanded(true),\n                        onCollapse: ()=>setRightSidebarExpanded(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                        lineNumber: 745,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                lineNumber: 371,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__.MobileNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                lineNumber: 752,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n        lineNumber: 363,\n        columnNumber: 5\n    }, this);\n}\n_s(PhotoViewPage, \"6SBaxgtVxnlwHyHdjSxFY31Vp3E=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = PhotoViewPage;\nfunction useRef(initialValue) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(initialValue);\n}\nvar _c;\n$RefreshReg$(_c, \"PhotoViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/HomeDashboard/Photos.tsx":
/*!*********************************************!*\
  !*** ./components/HomeDashboard/Photos.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Photos = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [photos, setPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [likedPhotos, setLikedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set());\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // Fallback data if API fails - using empty array\n    const getFallbackPhotos = ()=>[];\n    // Function to fetch photos from the API\n    const fetchPhotos = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching photos for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setPhotos([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.photos) {\n                console.log(\"Loaded \".concat(response.data.photos.length, \" photos for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.photos.length > 0) {\n                    console.log('Sample photo data:', response.data.photos[0]);\n                }\n                // Process the response\n                const processedPhotos = response.data.photos.map((photo)=>{\n                    if (!photo.photo_url) {\n                        console.log(\"Photo missing URL: \".concat(photo.photo_id));\n                    }\n                    return photo;\n                });\n                if (pageNumber === 1) {\n                    setPhotos(processedPhotos);\n                } else {\n                    setPhotos((prev)=>[\n                            ...prev,\n                            ...processedPhotos\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load photos - unexpected response format');\n                setPhotos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load photos');\n            setPhotos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of photos as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Photos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial photos load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchPhotos function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for photos page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"Photos.useEffect\": (response)=>{\n                            console.log('API response received for photos page 1');\n                            if (response.data && response.data.photos) {\n                                setPhotos(response.data.photos);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setPhotos([]);\n                                setError('No photos found');\n                            }\n                        }\n                    }[\"Photos.useEffect\"]).catch({\n                        \"Photos.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load photos');\n                            setPhotos([]);\n                        }\n                    }[\"Photos.useEffect\"]).finally({\n                        \"Photos.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"Photos.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"Photos.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"Photos.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchPhotos(nextPage, false);\n                    }\n                }\n            }[\"Photos.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"Photos.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"Photos.useEffect\"];\n        }\n    }[\"Photos.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a photo\n    const getImageSource = (photo)=>{\n        if (photo.photo_url) {\n            return photo.photo_url;\n        }\n        return '/pics/placeholder.svg';\n    };\n    // Toggle like for photos\n    const toggleLike = async (photoId)=>{\n        try {\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedPhotos.has(photoId);\n            setLikedPhotos((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(photoId);\n                } else {\n                    newLiked.add(photoId);\n                }\n                return newLiked;\n            });\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: photoId,\n                    content_type: 'photo'\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" photo: \").concat(photoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedPhotos((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedPhotos.has(photoId)) {\n                    newLiked.delete(photoId);\n                } else {\n                    newLiked.add(photoId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-black\",\n                        children: \"Photos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading photos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying photos load...');\n                            setError(null);\n                            fetchPhotos(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: 'smooth',\n                    WebkitOverflowScrolling: 'touch',\n                    minHeight: photos.length === 0 && !error ? '220px' : 'auto'\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative\",\n                children: [\n                    photos.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No photos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-4 pb-4 flex-nowrap\",\n                        children: [\n                            photos.map((photo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: '250px',\n                                        height: '200px'\n                                    },\n                                    onClick: ()=>{\n                                        window.location.href = \"/home/<USER>/\".concat(photo.photo_id);\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute top-2 left-2 z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                username: photo.user_name || \"user\",\n                                                size: \"sm\",\n                                                isGradientBorder: true,\n                                                onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: getImageSource(photo),\n                                                alt: photo.photo_name || \"Photo\",\n                                                fill: true,\n                                                sizes: \"(max-width: 640px) 250px, 250px\",\n                                                className: \"object-cover\",\n                                                ...index < 4 ? {\n                                                    priority: true\n                                                } : {\n                                                    loading: 'lazy'\n                                                },\n                                                placeholder: \"blur\" // Show blur placeholder while loading\n                                                ,\n                                                blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                ,\n                                                onError: (e)=>{\n                                                    const imgElement = e.target;\n                                                    if (imgElement) {\n                                                        imgElement.src = '/pics/placeholder.svg';\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-sm font-medium truncate\",\n                                                    children: photo.photo_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                                            className: \"jsx-83037452c623c470\" + \" \" + \"cursor-pointer hover:underline\",\n                                                            children: photo.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-83037452c623c470\",\n                                                            children: photo.photo_likes ? \"\".concat(photo.photo_likes, \" likes\") : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(photo.photo_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: 'relative',\n                                    // Add debug outline in development\n                                    outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"photos-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[200px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 362,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Photos, \"0D7xLpRSc97qwaKU4HSyafwIrS4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = Photos;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Photos);\nvar _c;\n$RefreshReg$(_c, \"Photos\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Photos.tsx\n"));

/***/ })

});