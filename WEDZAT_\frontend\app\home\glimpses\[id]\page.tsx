"use client";
import React, { useState, useEffect } from "react";
import {
  TopNavigation,
  SideNavigation,
} from "../../../../components/HomeDashboard/Navigation";
import axios from "../../../../services/axiosConfig";
import Image from "next/image";
import { useParams } from "next/navigation";
import VideoInteractionBar from "../../../../components/VideoInteractionBar";
import VideoVendorDetails from "../../../../components/VideoVendorDetails";

// Import or create the content section components
import Glimpses from "../../../../components/HomeDashboard/Glimpses";
import Flashes from "../../../../components/HomeDashboard/Flashes";
import WeddingVideosSection from "../../../../components/HomeDashboard/WeddingVideos";
import Photos from "../../../../components/HomeDashboard/Photos";
import DetailPageLazyLoad from "../../../../components/DetailPageLazyLoad";

// Define interface for glimpse video items
interface GlimpseVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface RecommendedVideo {
  video_id: string;
  video_name: string;
  video_thumbnail?: string;
  video_duration?: number;
  user_name?: string;
  video_views?: number;
  created_at: string;
}

export default function GlimpseDetailPage() {
  const params = useParams();
  const glimpseId = params?.id as string;

  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  // No need for right sidebar state as we have a custom sidebar
  const [isClient, setIsClient] = useState(false);
  const [glimpse, setGlimpse] = useState<GlimpseVideo | null>(null);
  const [recommendedVideos, setRecommendedVideos] = useState<
    RecommendedVideo[]
  >([]);
  const [filteredVideos, setFilteredVideos] = useState<RecommendedVideo[]>([]);
  const [activeFilter, setActiveFilter] = useState<"all" | "creator">("all");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showRecommendations, setShowRecommendations] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Add body scroll lock when recommendations panel is open on mobile
  useEffect(() => {
    if (typeof document !== "undefined") {
      if (showRecommendations) {
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "";
      }
    }
    return () => {
      if (typeof document !== "undefined") {
        document.body.style.overflow = "";
      }
    };
  }, [showRecommendations]);

  useEffect(() => {
    const fetchGlimpseDetails = async () => {
      try {
        setLoading(true);

        // Get token from localStorage - try multiple possible keys
        const tokenKey = localStorage.getItem("token")
          ? "token"
          : localStorage.getItem("jwt_token")
          ? "jwt_token"
          : localStorage.getItem("auth_token")
          ? "auth_token"
          : null;

        const token = tokenKey ? localStorage.getItem(tokenKey) : null;

        if (!token) {
          console.warn("No authentication token found in any storage key");
          setError("Authentication required");
          return;
        }

        // First try to find the glimpse in the main glimpses endpoint
        const response = await axios.get(`/glimpses?page=1&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.data && response.data.glimpses) {
          // Find the specific glimpse by ID
          const foundGlimpse = response.data.glimpses.find(
            (g: GlimpseVideo) => g.video_id === glimpseId
          );

          if (foundGlimpse) {
            console.log("Found glimpse:", foundGlimpse);
            setGlimpse(foundGlimpse);

            // Filter out the current glimpse for recommendations
            const recommendations = response.data.glimpses
              .filter((g: GlimpseVideo) => g.video_id !== glimpseId)
              .slice(0, 7); // Limit to 7 recommendations

            setRecommendedVideos(recommendations);
          } else {
            console.warn(
              `Glimpse with ID ${glimpseId} not found in API response`
            );
            setError("Glimpse not found");
          }
        } else {
          console.warn("Unexpected API response format:", response.data);
          setError("Failed to load glimpse details");
        }
      } catch (err) {
        console.error("Error fetching glimpse details:", err);
        setError("Failed to load glimpse details");

        // Fallback to mock data for development/testing
        if (process.env.NODE_ENV === "development") {
          console.log("Using fallback mock data for development");

          // Mock data for the current glimpse
          const mockGlimpse: GlimpseVideo = {
            video_id: glimpseId,
            video_name: "Beautiful Wedding Highlights",
            video_url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            video_description:
              "This beautiful wedding ceremony took place in Paris with over 200 guests. The couple had been planning this day for over a year, and everything came together perfectly. The venue was decorated with thousands of flowers, and the ceremony was held outdoors under a beautiful arch.",
            video_thumbnail: "/pics/placeholder.svg",
            video_duration: 180,
            video_category: "Wedding",
            created_at: new Date().toISOString(),
            user_name: "Marcus Levin",
            user_id: "marcus123",
            is_own_content: false,
            video_views: 9000000,
            video_likes: 741000,
            video_comments: 15000,
          };

          setGlimpse(mockGlimpse);

          // Mock data for recommended videos
          const mockRecommendedVideos: RecommendedVideo[] = [
            {
              video_id: "L_LUpnjgPso",
              video_name: "Ep 1: Living to Serve | SEARCH ON",
              video_thumbnail: "/pics/placeholder.svg",
              video_duration: 180,
              user_name: "James Gouse",
              video_views: 1400000,
              created_at: "3 years ago",
            },
            {
              video_id: "9bZkp7q19f0",
              video_name: "Ep 5: Living to Serve | SEARCH ON",
              video_thumbnail: "/pics/placeholder.svg",
              video_duration: 240,
              user_name: "James Gouse",
              video_views: 1400000,
              created_at: "3 years ago",
            },
            {
              video_id: "jNQXAC9IVRw",
              video_name: "Ep 3: Living to Serve | SEARCH ON",
              video_thumbnail: "/pics/placeholder.svg",
              video_duration: 210,
              user_name: "James Gouse",
              video_views: 1400000,
              created_at: "3 years ago",
            },
            {
              video_id: "kJQP7kiw5Fk",
              video_name: "Ep 6: Living to Serve | SEARCH ON",
              video_thumbnail: "/pics/placeholder.svg",
              video_duration: 195,
              user_name: "James Gouse",
              video_views: 1400000,
              created_at: "3 years ago",
            },
          ];

          setRecommendedVideos(mockRecommendedVideos);
          setError(null); // Clear error since we're using fallback data
        }
      } finally {
        setLoading(false);
      }
    };

    if (glimpseId) {
      fetchGlimpseDetails();
    }
  }, [glimpseId]);

  // Format view count
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Format duration to MM:SS
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // Improved YouTube URL handling
  const getYoutubeEmbedUrl = (url: string): string => {
    // Handle youtu.be format
    if (url.includes('youtu.be/')) {
      const videoId = url.split('youtu.be/')[1].split('?')[0];
      return `https://www.youtube.com/embed/${videoId}`;
    }
    
    // Handle youtube.com/watch?v= format
    if (url.includes('youtube.com/watch?v=')) {
      const videoId = new URLSearchParams(url.split('?')[1]).get('v');
      return `https://www.youtube.com/embed/${videoId}`;
    }
    
    // Return original if no match
    return url;
  };

  // Filter videos based on active filter
  useEffect(() => {
    if (recommendedVideos.length > 0 && glimpse) {
      if (activeFilter === "all") {
        setFilteredVideos(recommendedVideos);
      } else if (activeFilter === "creator") {
        setFilteredVideos(
          recommendedVideos.filter(
            (video) => video.user_name === glimpse.user_name
          )
        );
      }
    } else {
      setFilteredVideos(recommendedVideos);
    }
  }, [recommendedVideos, activeFilter, glimpse]);

  return (
    <div
      className={
        isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"
      }
    >
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <div className="hidden md:block fixed left-0 top-[80px] h-[calc(100vh-80px)] z-30">
          <SideNavigation
            expanded={sidebarExpanded}
            onExpand={() => setSidebarExpanded(true)}
            onCollapse={() => setSidebarExpanded(false)}
          />
        </div>

        {/* Main Content Area */}
        <div className="flex flex-col md:flex-row flex-1 mt-[80px] w-full">
          {/* Video and Details Section */}
          <main
            className={`flex-1 p-2 sm:p-4 bg-white flex flex-col items-center w-full md:w-auto ${
              sidebarExpanded ? "md:ml-60" : "md:ml-20"
            }`}
            style={{
              marginLeft: "70px",
              marginRight: "0px",
              transition: "margin 300ms ease-in-out",
              minHeight: "calc(100vh - 80px)",
              paddingBottom: showRecommendations ? "0" : "40px",
              overflowY: showRecommendations ? "hidden" : "auto",
              overflowX: "hidden",
              position: "relative",
            }}
          >
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-red-600">{error}</div>
              </div>
            ) : glimpse ? (
              <div
                className="flex flex-col gap-4 sm:gap-8 w-full mx-auto px-2 sm:px-4"
                style={{ maxWidth: "900px" }}
              >
                {/* Video Player and Info Section */}
                <div
                  className="flex flex-col w-full py-3 items-center justify-center"
                  style={{ maxWidth: "100%", margin: "0 auto" }}
                >
                  {/* Video Player */}
                  <div
                    className="w-full bg-black rounded-t-xl overflow-hidden shadow-xl border border-gray-200 mx-auto relative"
                    style={{
                      maxWidth: "800px",
                      width: "100%",
                      height: "auto",
                    }}
                  >
                    {/* Video Title Overlay */}
                    <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/70 to-transparent p-4 z-10">
                      <h1 className="text-white text-lg sm:text-xl font-medium">
                        {glimpse.video_name}
                      </h1>
                    </div>

                    {glimpse.video_url && (glimpse.video_url.includes("youtube.com") ||
                    glimpse.video_url.includes("youtu.be")) ? (
                      // YouTube embed with improved error handling
                      <iframe
                        src={getYoutubeEmbedUrl(glimpse.video_url)}
                        className="w-full aspect-video object-contain"
                        title={glimpse.video_name}
                        style={{ border: "none" }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        onError={(e) => {
                          console.error("YouTube iframe error:", e);
                          // Attempt to reload the iframe
                          const iframe = e.currentTarget as HTMLIFrameElement;
                          if (iframe) {
                            const src = iframe.src;
                            iframe.src = '';
                            setTimeout(() => {
                              iframe.src = src;
                            }, 1000);
                          }
                        }}
                      ></iframe>
                    ) : (
                      // Native video player with improved error handling
                      <div className="w-full aspect-video relative">
                        <video
                          className="w-full h-full object-contain"
                          controls
                          poster={glimpse.video_thumbnail || '/pics/placeholder.svg'}
                          preload="metadata"
                          autoPlay
                          controlsList="nodownload"
                          playsInline
                          webkit-playsinline="true"
                          x5-playsinline="true"
                          x5-video-player-type="h5"
                          onError={(e) => {
                            console.error("Video playback error:", e);
                            const videoElement = e.currentTarget;
                            
                            // Try to recover by reloading the video
                            if (videoElement) {
                              // First, try changing the source slightly to force a reload
                              const currentSrc = videoElement.src || glimpse.video_url;
                              if (!currentSrc) {
                                console.error("No video source available");
                                return;
                              }
                              
                              // Add a timestamp to force reload
                              const newSrc = currentSrc.includes('?') 
                                ? `${currentSrc}&t=${Date.now()}` 
                                : `${currentSrc}?t=${Date.now()}`;
                              
                              // Set the new source
                              videoElement.src = newSrc;
                              
                              // Then try to play again
                              setTimeout(() => {
                                videoElement.load();
                                videoElement.play().catch(err => {
                                  console.error("Retry failed:", err);
                                  
                                  // If still failing, try a different approach
                                  if (glimpse.video_url) {
                                    try {
                                      // Create a new video element as a last resort
                                      const newVideo = document.createElement('video');
                                      newVideo.src = glimpse.video_url;
                                      newVideo.className = videoElement.className;
                                      newVideo.controls = true;
                                      newVideo.autoplay = true;
                                      newVideo.playsInline = true;
                                      
                                      // Add multiple source elements for different formats
                                      const mp4Source = document.createElement('source');
                                      mp4Source.src = glimpse.video_url;
                                      mp4Source.type = 'video/mp4';
                                      newVideo.appendChild(mp4Source);
                                      
                                      const webmSource = document.createElement('source');
                                      webmSource.src = glimpse.video_url;
                                      webmSource.type = 'video/webm';
                                      newVideo.appendChild(webmSource);
                                      
                                      if (videoElement.parentNode) {
                                        videoElement.parentNode.replaceChild(newVideo, videoElement);
                                      }
                                    } catch (error) {
                                      console.error("Failed to create replacement video:", error);
                                    }
                                  }
                                });
                              }, 1000);
                            }
                          }}
                        >
                          <source src={glimpse.video_url} type="video/mp4" />
                          <source src={glimpse.video_url} type="video/webm" />
                          <source src={glimpse.video_url} type="video/quicktime" />
                          <source src={glimpse.video_url} type="video/x-matroska" />
                          Your browser does not support the video tag.
                        </video>
                        
                        {/* Fallback message if video fails to load */}
                        <div id="video-error-fallback" className="hidden absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 text-white p-4 text-center">
                          <div>
                            <p className="text-lg font-semibold mb-2">Video playback error</p>
                            <p>The video could not be played. Please try again later or view another glimpse.</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Video container with interaction bar - directly connected to video */}
                  <div className="w-full mx-auto mb-4 overflow-hidden -mt-1">
                    {/* Video Interaction Bar */}
                    <VideoInteractionBar
                      username={glimpse.user_name || "Anonymous"}
                      uploadDate={glimpse.created_at || "recently"}
                      viewCount={glimpse.video_views || 0}
                      description={glimpse.video_description}
                      userId={glimpse.user_id || "default"}
                    />

                    {/* Vendor Details Section */}
                    <div className="bg-white px-4 pb-4">
                      <VideoVendorDetails
                        videoId={glimpse.video_id}
                        isVerified={false}
                      />
                    </div>
                  </div>
                </div>

                {/* Additional Content Sections - Lazy Loaded */}
                <DetailPageLazyLoad
                  id="glimpses-section"
                  index={0}
                  rootMargin="0px 0px 500px 0px"
                >
                  {({ shouldLoad }) => (
                    <div className="overflow-x-auto w-full mx-auto">
                      <Glimpses shouldLoad={shouldLoad} />
                    </div>
                  )}
                </DetailPageLazyLoad>

                <DetailPageLazyLoad
                  id="flashes-section"
                  index={1}
                  rootMargin="0px 0px 400px 0px"
                >
                  {({ shouldLoad }) => (
                    <div className="overflow-x-auto w-full mx-auto">
                      <Flashes shouldLoad={shouldLoad} />
                    </div>
                  )}
                </DetailPageLazyLoad>

                <DetailPageLazyLoad
                  id="wedding-videos-section"
                  index={2}
                  rootMargin="0px 0px 300px 0px"
                >
                  {({ shouldLoad }) => (
                    <div className="overflow-x-auto w-full mx-auto">
                      <WeddingVideosSection shouldLoad={shouldLoad} />
                    </div>
                  )}
                </DetailPageLazyLoad>

                <DetailPageLazyLoad
                  id="photos-section"
                  index={3}
                  rootMargin="0px 0px 200px 0px"
                >
                  {({ shouldLoad }) => (
                    <div className="overflow-x-auto w-full mx-auto">
                      <Photos shouldLoad={shouldLoad} />
                    </div>
                  )}
                </DetailPageLazyLoad>
              </div>
            ) : null}
          </main>

          {/* Hamburger menu for medium screens */}
          <div className="fixed top-24 right-4 z-50 md:hidden lg:hidden">
            <button
              className="bg-red-600 text-white p-3 rounded-full shadow-lg"
              onClick={() => setShowRecommendations(!showRecommendations)}
            >
              {showRecommendations ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="3" y1="12" x2="21" y2="12"></line>
                  <line x1="3" y1="6" x2="21" y2="6"></line>
                  <line x1="3" y1="18" x2="21" y2="18"></line>
                </svg>
              )}
            </button>
          </div>

          {/* Right Sidebar - Recommended Videos */}
          <aside
            className={`${
              showRecommendations ? "fixed inset-0 z-40 bg-black/50" : "hidden"
            } md:block lg:block md:relative md:bg-transparent md:w-[400px] lg:w-[400px] md:sticky md:top-20 bg-white border-l border-gray-100 overflow-y-auto md:h-[calc(100vh-80px)] shadow-md`}
          >
            <div
              className={`${
                showRecommendations
                  ? "w-[85%] sm:w-[400px] h-full absolute right-0 top-0 bg-white"
                  : ""
              } md:w-full md:static`}
            >
              {/* Mobile close button */}
              <div className="flex justify-between items-center p-3 md:hidden border-b">
                <h2 className="font-semibold text-lg text-black">
                  Recommended Glimpses
                </h2>
                <button
                  className="text-gray-500"
                  onClick={() => setShowRecommendations(false)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              <div className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="font-semibold text-lg text-black">
                    Recommended Glimpses
                  </h2>
                  <button className="bg-white border border-gray-200 rounded-full p-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect
                        width="18"
                        height="18"
                        x="3"
                        y="3"
                        rx="2"
                        ry="2"
                      ></rect>
                      <line x1="3" x2="21" y1="9" y2="9"></line>
                      <line x1="9" x2="9" y1="21" y2="9"></line>
                    </svg>
                  </button>
                </div>

                <div className="flex mb-3 gap-2 overflow-x-auto pb-1">
                  <button
                    onClick={() => setActiveFilter("all")}
                    className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                      activeFilter === "all"
                        ? "bg-red-600 text-white"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    All
                  </button>
                  <button
                    onClick={() => setActiveFilter("creator")}
                    className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                      activeFilter === "creator"
                        ? "bg-red-600 text-white"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    From {glimpse?.user_name || "Creator"}
                  </button>
                </div>

                <div className="space-y-3">
                  {filteredVideos.map((video) => (
                    <div
                      key={video.video_id}
                      className="flex space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-all duration-200"
                      onClick={() => {
                        window.location.href = `/home/<USER>/${video.video_id}`;
                      }}
                    >
                      <div className="relative w-28 sm:w-32 md:w-36 h-16 sm:h-18 md:h-20 flex-shrink-0 rounded-lg overflow-hidden">
                        <Image
                          src={video.video_thumbnail || "/pics/placeholder.svg"}
                          alt={video.video_name}
                          fill
                          className="object-cover"
                          sizes="128px"
                        />
                        <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 rounded">
                          {video.video_duration
                            ? formatDuration(video.video_duration)
                            : "0:00"}
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-sm font-medium line-clamp-2 text-black">
                          {video.video_name}
                        </h3>
                        <p className="text-xs text-black mt-1">
                          {video.user_name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatViewCount(video.video_views || 0)} views •{" "}
                          {video.created_at || "3 years ago"}
                        </p>
                      </div>
                      <button className="text-gray-500 self-start mt-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <circle cx="12" cy="12" r="1"></circle>
                          <circle cx="12" cy="5" r="1"></circle>
                          <circle cx="12" cy="19" r="1"></circle>
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}
