import os
import uuid
import bcrypt
import jwt
from datetime import datetime,date,timezone,timedelta
import requests
import json
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')
CLERK_SECRET_KEY = os.getenv('CLERK_SECRET_KEY')

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

def signup(event):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        data = json.loads(event['body'])
        name = data.get('name')
        email = data.get('email')
        password = data.get('password')
        mobile_number = data.get('mobile_number')
        dob = data.get('dob')
        marital_status = data.get('marital_status')
        place = data.get('place')
        user_type = data.get('user_type')

        # Convert empty mobile number to None
        if mobile_number == "" or (mobile_number and not mobile_number.strip()):
            mobile_number = None

        # Lowercase the email
        email = email.lower() if email else None

        # Check for required fields
        if not (name and email and password and user_type):
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Missing required fields"})
            }

        # Check if email already exists
        cursor.execute('SELECT user_id FROM users WHERE LOWER(email) = LOWER(%s)', (email,))
        existing_email = cursor.fetchone()
        if existing_email:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Email already exists"})
            }

        # Only check for mobile number if it's provided
        if mobile_number:
            cursor.execute('SELECT user_id FROM users WHERE mobile_number = %s', (mobile_number,))
            existing_mobile = cursor.fetchone()
            if existing_mobile:
                return {
                    'statusCode': 400,
                    "headers": {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Mobile number already exists"})
                }

        # Hash the password
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        try:
            cursor.execute(
                '''INSERT INTO users (name, email, password, mobile_number, dob, marital_status, place, user_type, face_verified, face_image_url)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s) RETURNING user_id''',
                (name, email, hashed_password, mobile_number, dob, marital_status, place, user_type, False, None)
            )
            user_id = cursor.fetchone()[0]
            conn.commit()

            # Generate JWT token
            token = jwt.encode(
                {'user_id': str(user_id), 'exp': datetime.now(timezone.utc) + timedelta(days=1)},
                JWT_SECRET,
                algorithm='HS256'
            )
            return {
                'statusCode': 201,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"token": token})
            }

        except psycopg2.Error as e:
            conn.rollback()
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": str(e)})
            }
    except Exception as e:
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def login(event):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        data = json.loads(event['body'])
        email = data.get('email')
        mobile_number = data.get('mobile_number')
        password = data.get('password')

        if not ((email or mobile_number) and password):
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Email/Mobile and password are required"})
            }

        # Use COALESCE to prioritize email if both are provided
        cursor.execute(
            '''SELECT user_id, password FROM users
            WHERE COALESCE(LOWER(email) = LOWER(%s), FALSE) OR COALESCE(mobile_number = %s, FALSE)''',
            (email, mobile_number)
        )
        user = cursor.fetchone()

        if user and bcrypt.checkpw(password.encode('utf-8'), user[1].encode('utf-8')):
            token = jwt.encode(
                {'user_id': str(user[0]), 'exp': datetime.now(timezone.utc) + timedelta(days=1)},
                JWT_SECRET,
                algorithm='HS256'
            )
            return {
                'statusCode': 200,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"token": token})
            }
        else:
            return {
                'statusCode': 401,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Invalid credentials"})
            }
    except Exception as e:
        return {
            'statusCode': 500,
            "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def clerk_auth(event):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        data = json.loads(event['body'])
        clerk_token = data.get('clerk_token')
        user_type = data.get('user_type')
        user_email = data.get('user_email')
        user_name = data.get('user_name')
        user_id = data.get('user_id')

        if not clerk_token or not user_type:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Clerk token and user type are required"})
            }

        # If user_id is provided, try to get user info directly from Clerk API
        email = user_email
        name = user_name

        if user_id:
            try:
                headers = {"Authorization": f"Bearer {CLERK_SECRET_KEY}"}
                # Get user by ID
                print(f"Getting user info for user_id: {user_id}")
                user_response = requests.get(
                    f'https://api.clerk.com/v1/users/{user_id}',
                    headers=headers
                )

                if user_response.status_code == 200:
                    user_data = user_response.json()

                    print(f"Successfully got user data from Clerk API")
                    print(f"clerk_token: {clerk_token}")
                    # Extract email
                    if 'email_addresses' in user_data and user_data['email_addresses']:
                        primary_id = user_data.get('primary_email_address_id')
                        for email_obj in user_data['email_addresses']:
                            if email_obj['id'] == primary_id:
                                email = email_obj['email_address']
                                break
                        if not email and user_data['email_addresses']:
                            email = user_data['email_addresses'][0]['email_address']

                    # Extract name
                    first_name = user_data.get('first_name', '')
                    last_name = user_data.get('last_name', '')
                    name = f"{first_name} {last_name}".strip()

                    print(f"Using verified user info: email={email}, name={name}")
                else:
                    print(f"Failed to get user data: {user_response.status_code}, {user_response.text[:100]}")
                    # Continue with the user info from frontend
            except Exception as e:
                print(f"Error getting user from Clerk API: {str(e)}")
                # Continue with the user info from frontend

        # If we still don't have an email but have a user ID, generate one
        if not email and user_id:
            email = f"{user_id}@clerk.user"
            print(f"Generated email from user ID: {email}")

        if not email:
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Could not determine user email"})
            }

        # Ensure email is lowercase for consistency
        email = email.lower()

        try:
            # Check if user already exists
            cursor.execute('SELECT user_id FROM users WHERE LOWER(email) = %s', (email,))
            existing_user = cursor.fetchone()

            if existing_user:
                user_id = existing_user[0]
                print(f"Found existing user with ID: {user_id}")
            else:
                print(f"Creating new user with email: {email}, name: {name}, type: {user_type}")

                # Generate a random password for the user (since password column is NOT NULL)
                import secrets
                import string
                random_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16))
                hashed_password = bcrypt.hashpw(random_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

                
                cursor.execute(
                    '''INSERT INTO users (name, email, user_type, password)
                        VALUES (%s, %s, %s, %s) RETURNING user_id''',
                    (name, email, user_type, hashed_password)
                )

                # Get the user_id from the successful query
                user_id = cursor.fetchone()[0]
                conn.commit()
                print(f"Created new user with ID: {user_id}")

            # Generate JWT token
            token = jwt.encode(
                {'user_id': str(user_id), 'exp': datetime.now(timezone.utc) + timedelta(days=1)},
                JWT_SECRET,
                algorithm='HS256'
            )

            # Return the token directly in the response body for easier frontend handling
            return {
                'statusCode': 200,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"token": token})
            }

        except Exception as e:
            conn.rollback()
            print(f"Database error: {str(e)}")
            return {
                'statusCode': 400,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": str(e)})
            }
    except Exception as e:
        return {
            'statusCode': 500,
            "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def check_profile(event):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        headers = event['headers']
        token = headers.get('Authorization')

        if not token:
            return {
                'statusCode': 401,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Token is required"})
            }

        try:
            data = jwt.decode(token.split()[1], JWT_SECRET, algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            return {
                'statusCode': 401,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Token expired"})
            }
        except jwt.InvalidTokenError:
            return {
                'statusCode': 401,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Invalid token"})
            }

        cursor.execute('SELECT mobile_number FROM users WHERE user_id = %s', (data['user_id'],))
        user = cursor.fetchone()

        if user and not user[0]:
            return {
                'statusCode': 200,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"message": "Mobile number is missing. Please update your profile."})
            }

        return {
            'statusCode': 200,
            "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
            'body': json.dumps({"message": "Profile is complete."})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()


def get_user_details(event):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        headers = event['headers']
        token = headers.get('Authorization')

        if not token:
            return {
                'statusCode': 401,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Token is required"})
            }

        try:
            data = jwt.decode(token.split()[1], JWT_SECRET, algorithms=['HS256'])
            user_id = data['user_id']

            cursor.execute('SELECT name, email, mobile_number, dob, marital_status, place, user_type FROM users WHERE user_id = %s', (user_id,))
            user = cursor.fetchone()

            if user:
                dob = user[3].isoformat() if isinstance(user[3], (date, datetime)) else user[3]
                return {
                    'statusCode': 200,
                    "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                    'body': json.dumps({
                        "name": user[0],
                        "email": user[1],
                        "mobile_number": user[2],
                        "dob": dob,
                        "marital_status": user[4],
                        "place": user[5],
                        "user_type": user[6]
                    })
                }
            else:
                return {
                    'statusCode': 404,
                    "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                    'body': json.dumps({"error": "User not found"})
                }

        except jwt.ExpiredSignatureError:
            return {
                'statusCode': 401,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Token expired"})
            }
        except jwt.InvalidTokenError:
            return {
                'statusCode': 401,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Invalid token"})
            }
    except Exception as e:
        return {
            'statusCode': 500,
            "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()