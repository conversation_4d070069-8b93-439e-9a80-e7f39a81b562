// utils/auth.ts
import authService from '../services/api/authService';
import userService from '../services/api/userService';
import { Dispatch, SetStateAction } from 'react';
import { jwtDecode } from 'jwt-decode';

// Interface for decoded JWT token
interface DecodedToken {
  user_id: string;
  exp: number;
  [key: string]: any;
}

// Interface for user profile
export interface UserProfile {
  name: string;
  email: string;
  mobile_number?: string;
  dob?: string;
  marital_status?: string;
  place?: string;
  user_type: string;
  user_avatar?: string;
  bio?: string;
  user_short_audio?: string;
  chapters_of_love?: any[];
  face_verified?: boolean;
}

// Token key constants
const TOKEN_KEY = 'jwt_token';
const LEGACY_TOKEN_KEY = 'token';

// Initialize auth by loading token from storage and fetching user info
export const initializeAuth = async (
  setIsAuthenticated: Dispatch<SetStateAction<boolean>>,
  setUserProfile: Dispatch<SetStateAction<UserProfile | null>>,
  setIsLoading: Dispatch<SetStateAction<boolean>>
): Promise<void> => {
  setIsLoading(true);
  try {
    // Check for token with both possible keys
    const token = typeof window !== 'undefined' ?
      (localStorage.getItem(TOKEN_KEY) || localStorage.getItem(LEGACY_TOKEN_KEY)) : null;

    console.log(`Auth initialization: Token ${token ? 'found' : 'not found'}`);

    if (!token) {
      setIsAuthenticated(false);
      setUserProfile(null);
      return;
    }

    // Check if token is valid and not expired
    try {
      const decoded = jwtDecode<DecodedToken>(token);
      const currentTime = Date.now() / 1000;

      if (decoded.exp < currentTime) {
        // Token is expired
        console.log('Token is expired, logging out');
        authService.logout();
        setIsAuthenticated(false);
        setUserProfile(null);
        return;
      }

      console.log('Token is valid, getting user profile');

      // Token is valid, get user profile
      const userDetails = await userService.getUserDetails();
      setUserProfile(userDetails);
      setIsAuthenticated(true);

      // Standardize token storage
      if (localStorage.getItem(LEGACY_TOKEN_KEY) && !localStorage.getItem(TOKEN_KEY)) {
        localStorage.setItem(TOKEN_KEY, token);
      }
    } catch (error) {
      // Invalid token
      console.error("Invalid token:", error);
      authService.logout();
      setIsAuthenticated(false);
      setUserProfile(null);
    }
  } catch (error) {
    console.error("Auth initialization error:", error);
    setIsAuthenticated(false);
    setUserProfile(null);
  } finally {
    setIsLoading(false);
  }
};

// Check if the current route is accessible based on auth state and user type
export const canAccessRoute = (
  path: string,
  isAuthenticated: boolean,
  userProfile: UserProfile | null
): boolean => {
  // Public routes that don't require authentication
  const publicRoutes = ['/', '/auth/callback'];
  if (publicRoutes.some(route => path === route || path.startsWith(route))) {
    return true;
  }

  // Routes that require authentication
  if (!isAuthenticated) {
    return false;
  }

  // Vendor-specific routes
  if (path.startsWith('/vendor') && (!userProfile || userProfile.user_type !== 'vendor')) {
    return false;
  }

  // User-specific routes
  if (path.startsWith('/user') && (!userProfile || userProfile.user_type !== 'normal')) {
    return false;
  }

  return true;
};

// Create a function to get the stored token, checking both keys
export const getStoredToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(TOKEN_KEY) || localStorage.getItem(LEGACY_TOKEN_KEY);
};