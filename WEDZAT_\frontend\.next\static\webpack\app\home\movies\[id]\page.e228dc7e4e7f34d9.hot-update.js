"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./components/VideoInteractionBar.tsx":
/*!********************************************!*\
  !*** ./components/VideoInteractionBar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MdFavorite,MdFavoriteBorder!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst VideoInteractionBar = (param)=>{\n    let { username, uploadDate, viewCount, description, userId, videoId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isCreatingChat, setIsCreatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiring, setIsAdmiring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiringLoading, setIsAdmiringLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiking, setIsLiking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Format view count\n    const formatViewCount = (count)=>{\n        if (count >= 1000000) {\n            return \"\".concat((count / 1000000).toFixed(1), \"M\");\n        } else if (count >= 1000) {\n            return \"\".concat((count / 1000).toFixed(1), \"K\");\n        }\n        return count.toString();\n    };\n    // Handle message button click\n    const handleMessageClick = async ()=>{\n        if (isCreatingChat) return;\n        try {\n            setIsCreatingChat(true);\n            // Get token from localStorage - use userToken as in the chat page\n            const token = localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n            if (!token) {\n                console.error(\"No authentication token found\");\n                alert(\"Please log in to send messages\");\n                setIsCreatingChat(false);\n                return;\n            }\n            // Use the hardcoded URL from the messages page\n            const apiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\";\n            // If userId is 'default' or missing, use a fallback ID for testing\n            // In a real app, you would handle this differently\n            const participantId = !userId || userId === \"default\" ? username.toLowerCase().replace(/\\s+/g, \"_\") + \"_id\" : userId;\n            console.log(\"Creating conversation with user ID: \".concat(participantId));\n            console.log(\"Using API URL: \".concat(apiUrl, \"/conversations\"));\n            console.log(\"Authorization token: \".concat(token.substring(0, 10), \"...\"));\n            try {\n                // Create or get existing conversation\n                console.log(\"Request payload:\", JSON.stringify({\n                    participants: [\n                        participantId\n                    ]\n                }));\n                const response = await fetch(\"\".concat(apiUrl, \"/conversations\"), {\n                    method: \"POST\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        participants: [\n                            participantId\n                        ]\n                    }),\n                    // Add these options to help with CORS issues\n                    mode: \"cors\",\n                    credentials: \"same-origin\"\n                });\n                console.log(\"Response status:\", response.status);\n                console.log(\"Response headers:\", [\n                    ...response.headers.entries()\n                ]);\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"Conversation created/retrieved:\", data);\n                // Handle different response formats\n                let conversationId = null;\n                // Check if the response has a direct conversation_id property\n                if (data.conversation_id) {\n                    conversationId = data.conversation_id;\n                } else if (data.body && typeof data.body === \"string\") {\n                    try {\n                        const bodyData = JSON.parse(data.body);\n                        if (bodyData.conversation_id) {\n                            conversationId = bodyData.conversation_id;\n                            console.log(\"Found conversation ID in body:\", conversationId);\n                        }\n                    } catch (e) {\n                        console.error(\"Error parsing body data:\", e);\n                    }\n                }\n                if (!conversationId) {\n                    console.error(\"No conversation ID found in any format\", data);\n                    throw new Error(\"Invalid response from server\");\n                }\n                // Navigate to the chat page\n                router.push(\"/messages/\".concat(conversationId));\n            } catch (innerError) {\n                console.error(\"Inner fetch error:\", innerError);\n                throw innerError;\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            // Provide more specific error messages based on the error\n            if (error instanceof TypeError && error.message.includes(\"Failed to fetch\")) {\n                alert(\"Network error: Please check your internet connection and try again.\");\n            } else if (error instanceof Error && error.message.includes(\"401\")) {\n                alert(\"Authentication error: Please log in again.\");\n            } else if (error instanceof Error && error.message.includes(\"403\")) {\n                alert(\"Permission denied: You don't have permission to message this user.\");\n            } else {\n                alert(\"Failed to start conversation. Please try again.\");\n            }\n        } finally{\n            setIsCreatingChat(false);\n        }\n    };\n    // Function to navigate to user profile\n    const navigateToUserProfile = ()=>{\n        if (userId) {\n            // Get the token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Navigate to the profile page with the userId\n            router.push(\"/profile/\".concat(userId));\n        }\n    };\n    // Function to handle like/unlike\n    const handleLikeToggle = async ()=>{\n        if (isLiking || !videoId) return;\n        try {\n            setIsLiking(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to like this video');\n                setIsLiking(false);\n                return;\n            }\n            // Optimistically update UI\n            const newLikedState = !isLiked;\n            setIsLiked(newLikedState);\n            // Make API call to Next.js API routes\n            const endpoint = isLiked ? '/api/unlike' : '/api/like';\n            console.log(\"Making API call to: \".concat(endpoint, \" for video: \").concat(videoId));\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: videoId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const responseData = await response.json();\n            console.log('API Success Response:', responseData);\n            // Update localStorage to persist like status\n            try {\n                // Determine if this is a glimpse or movie based on current page or content type\n                const currentPath = window.location.pathname;\n                const isGlimpse = currentPath.includes('/glimpses');\n                const storageKey = isGlimpse ? 'likedGlimpses' : 'likedMovies';\n                const likedData = localStorage.getItem(storageKey);\n                const likedArray = likedData ? JSON.parse(likedData) : [];\n                if (isLiked) {\n                    // Remove from liked items\n                    const updatedLiked = likedArray.filter((id)=>id !== videoId);\n                    localStorage.setItem(storageKey, JSON.stringify(updatedLiked));\n                } else {\n                    // Add to liked items\n                    if (!likedArray.includes(videoId)) {\n                        likedArray.push(videoId);\n                        localStorage.setItem(storageKey, JSON.stringify(likedArray));\n                    }\n                }\n                console.log(\"Updated \".concat(storageKey, \" in localStorage\"));\n            } catch (storageError) {\n                console.error('Error updating localStorage:', storageError);\n            }\n            console.log(\"\".concat(isLiked ? 'Unliked' : 'Liked', \" video: \").concat(videoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setIsLiked(!isLiked);\n        } finally{\n            setIsLiking(false);\n        }\n    };\n    // Function to handle admire/unadmire\n    const handleAdmireToggle = async ()=>{\n        if (isAdmiringLoading || !userId) return;\n        try {\n            setIsAdmiringLoading(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to admire this user');\n                setIsAdmiringLoading(false);\n                return;\n            }\n            // Optimistically update UI state immediately for better user experience\n            const newAdmiringState = !isAdmiring;\n            setIsAdmiring(newAdmiringState);\n            // Make API call to follow/unfollow user\n            const endpoint = isAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n            console.log(\"Making request to \".concat(endpoint, \" with user ID: \").concat(userId));\n            try {\n                // Make the API call\n                const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(endpoint, {\n                    target_user_id: userId\n                }, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('API response:', response.data);\n                console.log(\"Successfully \".concat(isAdmiring ? 'unadmired' : 'admired', \" user\"));\n                // Update localStorage with the new state\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    let admiredUsers = JSON.parse(admiredUsersJson);\n                    if (newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                    console.log('Updated admired users in localStorage:', admiredUsers);\n                    // Force a refresh of the following list to ensure it's up to date\n                    const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        }\n                    });\n                    console.log('Updated following list:', followingResponse.data);\n                    // Double-check our state is correct\n                    if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                        const isActuallyFollowing = followingResponse.data.following.some((user)=>user.user_id === userId || user === userId);\n                        if (isActuallyFollowing !== newAdmiringState) {\n                            console.log('State mismatch detected, correcting...');\n                            setIsAdmiring(isActuallyFollowing);\n                            // Update localStorage again with the correct state\n                            admiredUsers = JSON.parse(localStorage.getItem('admiredUsers') || '{}');\n                            if (isActuallyFollowing) {\n                                admiredUsers[userId] = true;\n                            } else {\n                                delete admiredUsers[userId];\n                            }\n                            localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                        }\n                    }\n                } catch (storageError) {\n                    console.error('Error updating localStorage:', storageError);\n                }\n            } catch (apiError) {\n                console.error(\"Error \".concat(isAdmiring ? 'unadmiring' : 'admiring', \" user:\"), apiError);\n                if (apiError.response) {\n                    console.log('Error response data:', apiError.response.data);\n                    console.log('Error response status:', apiError.response.status);\n                    // If the error is that the user is already following/not following, the UI state is already correct\n                    if (apiError.response.status === 400 && apiError.response.data && (apiError.response.data.error === \"Already following this user\" || apiError.response.data.error === \"Not following this user\")) {\n                        console.log('Already in desired state, keeping UI updated');\n                        return;\n                    }\n                }\n                // If there was an error that wasn't just \"already in desired state\", revert the UI\n                setIsAdmiring(!newAdmiringState);\n                // Also update localStorage to match\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (!newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                } catch (storageError) {\n                    console.error('Error updating localStorage after API error:', storageError);\n                }\n            }\n        } catch (error) {\n            console.error('Unexpected error in handleAdmireToggle:', error);\n            // Revert UI state on unexpected errors\n            setIsAdmiring(!isAdmiring);\n        } finally{\n            setIsAdmiringLoading(false);\n        }\n    };\n    // Initialize like status from user-specific localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!videoId) return;\n            try {\n                // Get current user ID\n                const getCurrentUserId = {\n                    \"VideoInteractionBar.useEffect.getCurrentUserId\": ()=>{\n                        const userDataStr = localStorage.getItem('userData') || localStorage.getItem('user') || localStorage.getItem('currentUser');\n                        if (userDataStr) {\n                            try {\n                                const userData = JSON.parse(userDataStr);\n                                return userData.user_id || userData.id || userData.userId;\n                            } catch (e) {\n                                console.warn('🎬 Failed to parse user data in VideoInteractionBar');\n                            }\n                        }\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                        if (token) {\n                            try {\n                                const payload = JSON.parse(atob(token.split('.')[1]));\n                                return payload.user_id || payload.id || payload.sub;\n                            } catch (e) {\n                                console.warn('🎬 Failed to decode token in VideoInteractionBar');\n                            }\n                        }\n                        return 'anonymous';\n                    }\n                }[\"VideoInteractionBar.useEffect.getCurrentUserId\"];\n                const currentUserId = getCurrentUserId();\n                // Check if this video is liked in user-specific localStorage\n                const likedGlimpsesData = localStorage.getItem(\"likedGlimpses_\".concat(currentUserId));\n                const likedMoviesData = localStorage.getItem(\"likedMovies_\".concat(currentUserId));\n                let isVideoLiked = false;\n                // Check glimpses\n                if (likedGlimpsesData) {\n                    const likedGlimpses = JSON.parse(likedGlimpsesData);\n                    if (Array.isArray(likedGlimpses) && likedGlimpses.includes(videoId)) {\n                        isVideoLiked = true;\n                    }\n                }\n                // Check movies\n                if (!isVideoLiked && likedMoviesData) {\n                    const likedMovies = JSON.parse(likedMoviesData);\n                    if (Array.isArray(likedMovies) && likedMovies.includes(videoId)) {\n                        isVideoLiked = true;\n                    }\n                }\n                setIsLiked(isVideoLiked);\n                console.log(\"\\uD83C\\uDFAC VideoInteractionBar: Video \".concat(videoId, \" like status for user \").concat(currentUserId, \":\"), isVideoLiked);\n            } catch (error) {\n                console.error('🎬 Error loading user-specific like status for VideoInteractionBar:', error);\n                setIsLiked(false);\n            }\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        videoId\n    ]);\n    // Check if user is already admiring on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!userId) return;\n            // First check localStorage for cached admiring state\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers');\n                if (admiredUsersJson) {\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (admiredUsers[userId]) {\n                        console.log('Found admiring status in localStorage:', true);\n                        setIsAdmiring(true);\n                    // Continue with API check to verify\n                    }\n                }\n            } catch (storageError) {\n                console.error('Error reading from localStorage:', storageError);\n            }\n            const checkAdmiringStatus = {\n                \"VideoInteractionBar.useEffect.checkAdmiringStatus\": async ()=>{\n                    console.log('Checking admiring status for user ID:', userId);\n                    // Get token from localStorage\n                    const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                    if (!token) {\n                        console.warn('No authentication token found');\n                        return;\n                    }\n                    // Check if this is the current user's content - no need to check admiring status\n                    try {\n                        const tokenParts = token.split('.');\n                        if (tokenParts.length === 3) {\n                            const payload = JSON.parse(atob(tokenParts[1]));\n                            if (payload.user_id === userId) {\n                                console.log('This is the current user\\'s content, skipping admiring status check');\n                                setIsAdmiring(false); // User can't admire themselves\n                                return;\n                            }\n                        }\n                    } catch (tokenError) {\n                        console.log('Could not decode token to check current user, proceeding with API calls');\n                    }\n                    try {\n                        // Direct API call to check following status - most reliable method\n                        const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('Following response:', followingResponse.data);\n                        if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                            // Check if userId is in the following array\n                            const isFollowing = followingResponse.data.following.some({\n                                \"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\": (user)=>user.user_id === userId || user === userId\n                            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\"]);\n                            console.log(\"User \".concat(isFollowing ? 'is' : 'is not', \" in following list:\"), userId);\n                            setIsAdmiring(isFollowing);\n                            // Update localStorage for future reference\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (isFollowing) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                            return;\n                        }\n                    } catch (followingError) {\n                        console.log('Error fetching following list, trying alternative method');\n                    }\n                    // Fallback: Try to get user profile\n                    try {\n                        const profileResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-profile?user_id=\".concat(userId), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('User profile response:', profileResponse.data);\n                        if (profileResponse.data && profileResponse.data.is_following !== undefined) {\n                            console.log('Setting admiring status from profile:', profileResponse.data.is_following);\n                            setIsAdmiring(profileResponse.data.is_following);\n                            // Update localStorage\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (profileResponse.data.is_following) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                        }\n                    } catch (profileError) {\n                        console.log('Error fetching user profile, trying another method');\n                        // Last resort: Try the user-follow-stats endpoint\n                        try {\n                            const statsResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-follow-stats?target_user_id=\".concat(userId), {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token),\n                                    'Content-Type': 'application/json'\n                                }\n                            });\n                            console.log('User follow stats response:', statsResponse.data);\n                            if (statsResponse.data && statsResponse.data.is_following !== undefined) {\n                                setIsAdmiring(statsResponse.data.is_following);\n                                // Update localStorage\n                                try {\n                                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                    const admiredUsers = JSON.parse(admiredUsersJson);\n                                    if (statsResponse.data.is_following) {\n                                        admiredUsers[userId] = true;\n                                    } else {\n                                        delete admiredUsers[userId];\n                                    }\n                                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                } catch (storageError) {\n                                    console.error('Error updating localStorage:', storageError);\n                                }\n                            }\n                        } catch (statsError) {\n                            console.log('All API methods failed to check admiring status, using localStorage fallback');\n                            // Fallback to localStorage if all API calls fail\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                const isAdmiringFromStorage = admiredUsers[userId] === true;\n                                setIsAdmiring(isAdmiringFromStorage);\n                                console.log(\"Using localStorage fallback: \".concat(isAdmiringFromStorage ? 'admiring' : 'not admiring'));\n                            } catch (storageError) {\n                                console.log('localStorage fallback also failed, defaulting to not admiring');\n                                setIsAdmiring(false);\n                            }\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus\"];\n            // Call the API check function\n            checkAdmiringStatus();\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        userId\n    ]);\n    // Add click outside handler to close menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"VideoInteractionBar.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.menu-container')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"VideoInteractionBar.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"VideoInteractionBar.useEffect\"];\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        showMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-white p-2 sm:p-3 md:p-4 rounded-b-xl border border-gray-200 border-t-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 sm:space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLikeToggle,\n                                    disabled: isLiking,\n                                    className: \"flex items-center justify-center rounded-full p-1.5 sm:p-2 transition-colors \".concat(isLiked ? 'bg-red-100 hover:bg-red-200' : 'bg-gray-100 hover:bg-gray-200'),\n                                    title: isLiked ? 'Unlike' : 'Like',\n                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdFavorite, {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        color: \"#B31B1E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdFavoriteBorder, {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        color: \"#666\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    onClick: handleMessageClick,\n                                    disabled: isCreatingChat,\n                                    title: \"Send a message\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    onClick: ()=>{\n                                        if (videoId) {\n                                            // Determine the content type based on current URL\n                                            const currentPath = window.location.pathname;\n                                            let shareUrl = '';\n                                            if (currentPath.includes('/glimpses/')) {\n                                                shareUrl = \"\".concat(window.location.origin, \"/home/<USER>/\").concat(videoId);\n                                            } else if (currentPath.includes('/movies/')) {\n                                                shareUrl = \"\".concat(window.location.origin, \"/home/<USER>/\").concat(videoId);\n                                            } else {\n                                                shareUrl = window.location.href; // fallback to current URL\n                                            }\n                                            navigator.clipboard.writeText(shareUrl).then(()=>{\n                                                console.log('Video link copied to clipboard:', shareUrl);\n                                                alert('Video link copied to clipboard!');\n                                            }).catch((err)=>{\n                                                console.error('Failed to copy video link:', err);\n                                                alert('Failed to copy link');\n                                            });\n                                        }\n                                    },\n                                    title: \"Share Video\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 683,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: formatViewCount(viewCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"views\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative menu-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMenu(!showMenu),\n                                        className: \"p-1.5 rounded-full hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 18,\n                                            className: \"text-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: \"Report content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 744,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 682,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"cursor-pointer\",\n                                        onClick: navigateToUserProfile,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            username: username || \"Anonymous\",\n                                            size: \"md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-black cursor-pointer hover:underline\",\n                                                onClick: navigateToUserProfile,\n                                                children: username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Uploaded \",\n                                                    uploadDate || 'recently'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 767,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMessageClick,\n                                        disabled: isCreatingChat,\n                                        className: \"flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-full text-sm transition-colors\",\n                                        title: \"Send a message\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isCreatingChat ? \"Opening...\" : \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAdmireToggle,\n                                        disabled: isAdmiringLoading,\n                                        className: \"flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors bg-[#B31B1E] text-white hover:bg-red-700\",\n                                        title: isAdmiring ? \"Admiring\" : \"Admire this user\",\n                                        children: isAdmiringLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                isAdmiring ? 'Admiring' : 'Admire',\n                                                !isAdmiring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                    lineNumber: 804,\n                                                    columnNumber: 35\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 766,\n                        columnNumber: 9\n                    }, undefined),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black mt-2 text-xs sm:text-sm line-clamp-3 sm:line-clamp-none\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 765,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n        lineNumber: 681,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoInteractionBar, \"Ub7IOFbhXSoy7hmqI9MF3vyUtUI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = VideoInteractionBar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VideoInteractionBar);\nvar _c;\n$RefreshReg$(_c, \"VideoInteractionBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/VideoInteractionBar.tsx\n"));

/***/ })

});