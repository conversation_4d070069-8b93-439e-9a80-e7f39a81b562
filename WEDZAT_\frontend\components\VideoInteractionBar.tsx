
"use client";

import React, { useState, useEffect } from "react";
import { Heart, MessageCircle, Share2, MoreVertical, Plus, Check } from "lucide-react";
import axios from "axios";
import UserAvatar from "./HomeDashboard/UserAvatar";
import { useRouter } from "next/navigation";

interface VideoInteractionBarProps {
  username: string;
  uploadDate: string;
  viewCount: number;
  description?: string;
  userId: string;
}

const VideoInteractionBar: React.FC<VideoInteractionBarProps> = ({
  username,
  uploadDate,
  viewCount,
  description,
  userId,
}) => {
  const router = useRouter();
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const [isAdmiring, setIsAdmiring] = useState(false);
  const [isAdmiringLoading, setIsAdmiringLoading] = useState(false);
  const [showMenu, setShowMenu] = useState(false);

  // Format view count
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Handle message button click
  const handleMessageClick = async () => {
    if (isCreatingChat) return;

    try {
      setIsCreatingChat(true);

      // Get token from localStorage - use userToken as in the chat page
      const token =
        localStorage.getItem("token") || sessionStorage.getItem("token");

      if (!token) {
        console.error("No authentication token found");
        alert("Please log in to send messages");
        setIsCreatingChat(false);
        return;
      }

      // Use the hardcoded URL from the messages page
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;

      // If userId is 'default' or missing, use a fallback ID for testing
      // In a real app, you would handle this differently
      const participantId =
        !userId || userId === "default"
          ? username.toLowerCase().replace(/\s+/g, "_") + "_id"
          : userId;

      console.log(`Creating conversation with user ID: ${participantId}`);
      console.log(`Using API URL: ${apiUrl}/conversations`);
      console.log(`Authorization token: ${token.substring(0, 10)}...`);

      try {
        // Create or get existing conversation
        console.log(
          "Request payload:",
          JSON.stringify({ participants: [participantId] })
        );

        const response = await fetch(`${apiUrl}/conversations`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            participants: [participantId],
          }),
          // Add these options to help with CORS issues
          mode: "cors",
          credentials: "same-origin",
        });

        console.log("Response status:", response.status);
        console.log("Response headers:", [...response.headers.entries()]);

        if (!response.ok) {
          throw new Error(`Failed to create conversation: ${response.status}`);
        }

        const data = await response.json();
        console.log("Conversation created/retrieved:", data);

        // Handle different response formats
        let conversationId = null;

        // Check if the response has a direct conversation_id property
        if (data.conversation_id) {
          conversationId = data.conversation_id;
        }
        // Check if the response has a body property that might contain the conversation_id
        else if (data.body && typeof data.body === "string") {
          try {
            const bodyData = JSON.parse(data.body);
            if (bodyData.conversation_id) {
              conversationId = bodyData.conversation_id;
              console.log("Found conversation ID in body:", conversationId);
            }
          } catch (e) {
            console.error("Error parsing body data:", e);
          }
        }

        if (!conversationId) {
          console.error("No conversation ID found in any format", data);
          throw new Error("Invalid response from server");
        }

        // Navigate to the chat page
        router.push(`/messages/${conversationId}`);
      } catch (innerError) {
        console.error("Inner fetch error:", innerError);
        throw innerError;
      }
    } catch (error) {
      console.error("Error creating conversation:", error);

      // Provide more specific error messages based on the error
      if (
        error instanceof TypeError &&
        error.message.includes("Failed to fetch")
      ) {
        alert(
          "Network error: Please check your internet connection and try again."
        );
      } else if (error instanceof Error && error.message.includes("401")) {
        alert("Authentication error: Please log in again.");
      } else if (error instanceof Error && error.message.includes("403")) {
        alert(
          "Permission denied: You don't have permission to message this user."
        );
      } else {
        alert("Failed to start conversation. Please try again.");
      }
    } finally {
      setIsCreatingChat(false);
    }
  };

  // Function to navigate to user profile
  const navigateToUserProfile = () => {
    if (userId) {
      // Get the token from localStorage
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Navigate to the profile page with the userId
      router.push(`/profile/${userId}`);
    }
  };

  // Function to handle admire/unadmire
  const handleAdmireToggle = async () => {
    if (isAdmiringLoading || !userId) return;

    try {
      setIsAdmiringLoading(true);

      // Get token from localStorage
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found');
        alert('Please log in to admire this user');
        setIsAdmiringLoading(false);
        return;
      }

      // Optimistically update UI state immediately for better user experience
      const newAdmiringState = !isAdmiring;
      setIsAdmiring(newAdmiringState);

      // Make API call to follow/unfollow user
      const endpoint = isAdmiring
        ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow'
        : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';

      // Log the request details for debugging
      console.log(`Making request to ${endpoint} with user ID: ${userId}`);

      try {
        const response = await axios.post(
          endpoint,
          { target_user_id: userId },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('API response:', response.data);
        console.log(`Successfully ${isAdmiring ? 'unadmired' : 'admired'} user`);

        // Store the admiring state in localStorage to persist across page reloads
        try {
          // Get existing admired users from localStorage
          const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
          const admiredUsers = JSON.parse(admiredUsersJson);

          // Update the admired users object
          if (newAdmiringState) {
            admiredUsers[userId] = true;
          } else {
            delete admiredUsers[userId];
          }

          // Save back to localStorage
          localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));
          console.log('Updated admired users in localStorage:', admiredUsers);
        } catch (storageError) {
          console.error('Error updating localStorage:', storageError);
        }
      } catch (apiError: any) {
        console.error(`Error ${isAdmiring ? 'unadmiring' : 'admiring'} user:`, apiError);

        // More detailed error handling
        if (apiError.response) {
          console.log('Error response data:', apiError.response.data);
          console.log('Error response status:', apiError.response.status);

          // If the error is that the user is already following/not following, the UI state is already correct
          if (apiError.response.status === 400 &&
              apiError.response.data &&
              (apiError.response.data.error === "Already following this user" ||
               apiError.response.data.error === "Not following this user")) {
            console.log('Already in desired state, keeping UI updated');
            return;
          }
        }

        // If there was an error that wasn't just "already in desired state", revert the UI
        setIsAdmiring(!newAdmiringState);
      }
    } catch (error: any) {
      console.error('Unexpected error in handleAdmireToggle:', error);
      // Revert UI state on unexpected errors
      setIsAdmiring(!isAdmiring);
    } finally {
      setIsAdmiringLoading(false);
    }
  };

  // Check if user is already admiring on component mount
  useEffect(() => {
    // First check localStorage for cached admiring state
    try {
      const admiredUsersJson = localStorage.getItem('admiredUsers');
      if (admiredUsersJson) {
        const admiredUsers = JSON.parse(admiredUsersJson);
        if (userId && admiredUsers[userId]) {
          console.log('Found admiring status in localStorage:', true);
          setIsAdmiring(true);
          return; // Skip API call if we found the state in localStorage
        }
      }
    } catch (storageError) {
      console.error('Error reading from localStorage:', storageError);
    }

    // If not in localStorage, check via API
    const checkAdmiringStatus = async () => {
      if (!userId) return;

      try {
        // Get token from localStorage
        const token = localStorage.getItem('token') ||
          localStorage.getItem('jwt_token') ||
          localStorage.getItem('wedzat_token');

        if (!token) {
          console.warn('No authentication token found');
          return;
        }

        console.log('Checking admiring status for user ID:', userId);

        // First try to get the user profile which includes following status
        try {
          const profileResponse = await axios.get(
            `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-profile?user_id=${userId}`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );

          console.log('User profile response:', profileResponse.data);

          if (profileResponse.data && profileResponse.data.is_following !== undefined) {
            console.log('Setting admiring status from profile:', profileResponse.data.is_following);
            setIsAdmiring(profileResponse.data.is_following);
            return;
          }
        } catch (profileError) {
          console.log('Error fetching user profile, trying follow stats endpoint');
        }

        // If profile doesn't work, try the follow stats endpoint
        const response = await axios.get(
          `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-follow-stats`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('User follow stats response:', response.data);

        // Check if the response contains the following array
        if (response.data && response.data.following && Array.isArray(response.data.following)) {
          const isFollowing = response.data.following.includes(userId);
          console.log(`User ${isFollowing ? 'is' : 'is not'} in following list:`, userId);
          setIsAdmiring(isFollowing);
        }

        // If that doesn't work, try a direct check
        if (!response.data || !response.data.following) {
          console.log('Trying direct follow check');

          // Try to unfollow the user - if it succeeds, it means we were following
          try {
            await axios.post(
              'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow',
              { target_user_id: userId },
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                  'Content-Type': 'application/json'
                }
              }
            );

            // If we get here, the unfollow succeeded, meaning we were following
            console.log('Unfollow succeeded, was following');
            setIsAdmiring(true);

            // Follow again to restore the state
            await axios.post(
              'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow',
              { target_user_id: userId },
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                  'Content-Type': 'application/json'
                }
              }
            );
          } catch (unfollowError: any) {
            // If we get a 400 with "Not following this user", we weren't following
            if (unfollowError.response &&
                unfollowError.response.status === 400 &&
                unfollowError.response.data &&
                unfollowError.response.data.error === "Not following this user") {
              console.log('Unfollow failed with "Not following", was not following');
              setIsAdmiring(false);
            }
          }
        }
      } catch (error: any) {
        console.error('Error checking admiring status:', error);

        // Log more detailed error information
        if (error.response) {
          console.log('Error response data:', error.response.data);
          console.log('Error response status:', error.response.status);
        }
      }
    };

    // Call the API check function
    checkAdmiringStatus();
  }, [userId]);

  // Add click outside handler to close menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showMenu) {
        const target = event.target as HTMLElement;
        if (!target.closest('.menu-container')) {
          setShowMenu(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu]);

  return (
    <div className="w-full bg-white p-2 sm:p-3 md:p-4 rounded-b-xl border border-gray-200 border-t-0">
      <div className="flex items-center justify-between mb-3 md:mb-4">
        <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-3">
          <div className="flex space-x-1 sm:space-x-2">
            <button className="flex items-center justify-center bg-red-100 hover:bg-red-200 rounded-full p-1.5 sm:p-2 transition-colors">
              <Heart
                className="w-4 h-4 sm:w-5 sm:h-5 text-red-600"
                fill="#dc2626"
              />
            </button>
            <button className="flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors">
              <MessageCircle className="w-4 h-4 sm:w-5 sm:h-5 text-gray-700" />
            </button>
            <button className="flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors">
              <Share2 className="w-4 h-4 sm:w-5 sm:h-5 text-gray-700" />
            </button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="text-sm text-gray-700">
            <span className="font-semibold">{formatViewCount(viewCount)}</span>{" "}
            views
          </div>
          <div className="relative menu-container">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-1.5 rounded-full hover:bg-gray-100"
            >
              <MoreVertical size={18} className="text-gray-700" />
            </button>

            {showMenu && (
              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                <div className="py-1">
                  <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Report content
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="border-t border-gray-200 pt-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="cursor-pointer" onClick={navigateToUserProfile}>
              <UserAvatar username={username || "Anonymous"} size="md" />
            </div>
            <div>
              <div
                className="font-medium text-black cursor-pointer hover:underline"
                onClick={navigateToUserProfile}
              >
                {username}
              </div>
              <div className="text-xs text-gray-500">Uploaded {uploadDate || 'recently'}</div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={handleMessageClick}
              disabled={isCreatingChat}
              className="flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-full text-sm transition-colors"
              title="Send a message"
            >
              <MessageCircle className="w-4 h-4" />
              <span>{isCreatingChat ? "Opening..." : "Message"}</span>
            </button>

            <button
              onClick={handleAdmireToggle}
              disabled={isAdmiringLoading}
              className={`flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                isAdmiring
                  ? 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                  : 'bg-[#B31B1E] text-white hover:bg-red-700'
              }`}
              title={isAdmiring ? "Admiring" : "Admire this user"}
            >
              {isAdmiringLoading ? (
                <span className="animate-pulse">...</span>
              ) : (
                <>
                  {isAdmiring ? 'Admiring' : 'Admire'}
                  {!isAdmiring && <Plus size={16} />}
                </>
              )}
            </button>
          </div>
        </div>

        {description && (
          <p className="text-black mt-2 text-xs sm:text-sm line-clamp-3 sm:line-clamp-none">
            {description}
          </p>
        )}
      </div>
    </div>
  );
};

export default VideoInteractionBar;
