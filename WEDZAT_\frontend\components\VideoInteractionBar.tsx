
"use client";

import React, { useState } from "react";
import { Heart, MessageCircle, Share2, MessageSquare } from "lucide-react";
import UserAvatar from "./HomeDashboard/UserAvatar";
import { useRouter } from "next/navigation";

interface VideoInteractionBarProps {
  username: string;
  uploadDate: string;
  viewCount: number;
  description?: string;
  userId: string;
}

const VideoInteractionBar: React.FC<VideoInteractionBarProps> = ({
  username,
  uploadDate,
  viewCount,
  description,
  userId,
}) => {
  const router = useRouter();
  const [isCreatingChat, setIsCreatingChat] = useState(false);

  // Format view count
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Handle message button click
  const handleMessageClick = async () => {
    if (isCreatingChat) return;

    try {
      setIsCreatingChat(true);

      // Get token from localStorage - use userToken as in the chat page
      const token =
        localStorage.getItem("token") || sessionStorage.getItem("token");

      if (!token) {
        console.error("No authentication token found");
        alert("Please log in to send messages");
        setIsCreatingChat(false);
        return;
      }

      // Use the hardcoded URL from the messages page
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;

      // If userId is 'default' or missing, use a fallback ID for testing
      // In a real app, you would handle this differently
      const participantId =
        !userId || userId === "default"
          ? username.toLowerCase().replace(/\s+/g, "_") + "_id"
          : userId;

      console.log(`Creating conversation with user ID: ${participantId}`);
      console.log(`Using API URL: ${apiUrl}/conversations`);
      console.log(`Authorization token: ${token.substring(0, 10)}...`);

      try {
        // Create or get existing conversation
        console.log(
          "Request payload:",
          JSON.stringify({ participants: [participantId] })
        );

        const response = await fetch(`${apiUrl}/conversations`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            participants: [participantId],
          }),
          // Add these options to help with CORS issues
          mode: "cors",
          credentials: "same-origin",
        });

        console.log("Response status:", response.status);
        console.log("Response headers:", [...response.headers.entries()]);

        if (!response.ok) {
          throw new Error(`Failed to create conversation: ${response.status}`);
        }

        const data = await response.json();
        console.log("Conversation created/retrieved:", data);

        // Handle different response formats
        let conversationId = null;

        // Check if the response has a direct conversation_id property
        if (data.conversation_id) {
          conversationId = data.conversation_id;
        }
        // Check if the response has a body property that might contain the conversation_id
        else if (data.body && typeof data.body === "string") {
          try {
            const bodyData = JSON.parse(data.body);
            if (bodyData.conversation_id) {
              conversationId = bodyData.conversation_id;
              console.log("Found conversation ID in body:", conversationId);
            }
          } catch (e) {
            console.error("Error parsing body data:", e);
          }
        }

        if (!conversationId) {
          console.error("No conversation ID found in any format", data);
          throw new Error("Invalid response from server");
        }

        // Navigate to the chat page
        router.push(`/messages/${conversationId}`);
      } catch (innerError) {
        console.error("Inner fetch error:", innerError);
        throw innerError;
      }
    } catch (error) {
      console.error("Error creating conversation:", error);

      // Provide more specific error messages based on the error
      if (
        error instanceof TypeError &&
        error.message.includes("Failed to fetch")
      ) {
        alert(
          "Network error: Please check your internet connection and try again."
        );
      } else if (error instanceof Error && error.message.includes("401")) {
        alert("Authentication error: Please log in again.");
      } else if (error instanceof Error && error.message.includes("403")) {
        alert(
          "Permission denied: You don't have permission to message this user."
        );
      } else {
        alert("Failed to start conversation. Please try again.");
      }
    } finally {
      setIsCreatingChat(false);
    }
  };

  // Function to navigate to user profile
  const navigateToUserProfile = () => {
    if (userId) {
      // Get the token from localStorage
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Navigate to the profile page with the userId
      router.push(`/profile/${userId}`);
    }
  };

  return (
    <div className="w-full bg-white p-2 sm:p-3 md:p-4 rounded-b-xl border border-gray-200 border-t-0">
      <div className="flex items-center justify-between mb-3 md:mb-4">
        <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-3">
          <div className="flex space-x-1 sm:space-x-2">
            <button className="flex items-center justify-center bg-red-100 hover:bg-red-200 rounded-full p-1.5 sm:p-2 transition-colors">
              <Heart
                className="w-4 h-4 sm:w-5 sm:h-5 text-red-600"
                fill="#dc2626"
              />
            </button>
            <button className="flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors">
              <MessageCircle className="w-4 h-4 sm:w-5 sm:h-5 text-gray-700" />
            </button>
            <button className="flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors">
              <Share2 className="w-4 h-4 sm:w-5 sm:h-5 text-gray-700" />
            </button>
          </div>
        </div>
        <div className="text-sm text-gray-700">
          <span className="font-semibold">{formatViewCount(viewCount)}</span>{" "}
          views
        </div>
      </div>

      <div className="border-t border-gray-200 pt-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="cursor-pointer" onClick={navigateToUserProfile}>
            <UserAvatar username={username || "Anonymous"} size="md" />
          </div>
          <div>
            <div
              className="font-medium text-black cursor-pointer hover:underline"
              onClick={navigateToUserProfile}
            >
              {username}
            </div>
            <div className="text-xs text-gray-500">Uploaded {uploadDate || 'recently'}</div>
          </div>
          <button
            onClick={handleMessageClick}
            disabled={isCreatingChat}
            className="flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-full text-sm transition-colors"
            title="Send a message"
          >
            <MessageSquare className="w-4 h-4" />
            <span>{isCreatingChat ? "Opening..." : "Message"}</span>
          </button>
        </div>

        {description && (
          <p className="text-black mt-2 text-xs sm:text-sm line-clamp-3 sm:line-clamp-none">
            {description}
          </p>
        )}
      </div>
    </div>
  );
};

export default VideoInteractionBar;
