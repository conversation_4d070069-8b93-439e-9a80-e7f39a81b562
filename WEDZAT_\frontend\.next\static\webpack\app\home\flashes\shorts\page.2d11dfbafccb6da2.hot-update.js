"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/shorts/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/shorts/page.tsx":
/*!******************************************!*\
  !*** ./app/home/<USER>/shorts/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashShortsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var _components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/FlashVendorDetails */ \"(app-pages-browser)/./components/FlashVendorDetails.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Navigation */ \"(app-pages-browser)/./components/HomeDashboard/Navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Create a Client component that uses useSearchParams\nfunction FlashShortsContent() {\n    var _flashes_currentIndex, _flashes_currentIndex1, _flashes_currentIndex2, _flashes_currentIndex3, _flashes_currentIndex4, _flashes_currentIndex5, _flashes_currentIndex6, _flashes_currentIndex7;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialIndex = parseInt((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"index\")) || \"0\");\n    const [flashes, setFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialIndex);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedFlashes, setLikedFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [leftSidebarExpanded, setLeftSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightSidebarExpanded, setRightSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showVendorDetails, setShowVendorDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [admiringUsers, setAdmiringUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // Track admiring state\n    const [videoLoading, setVideoLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Refs for video elements\n    const videoRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const youtubeTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // User avatar placeholders - using placeholder.svg which exists\n    const userAvatarPlaceholders = [\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>setIsClient(true)\n    }[\"FlashShortsContent.useEffect\"], []);\n    // Load admiring state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!isClient) return;\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                const admiredUsers = JSON.parse(admiredUsersJson);\n                setAdmiringUsers(admiredUsers);\n            } catch (error) {\n                console.error('Error loading admired users from localStorage:', error);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient\n    ]);\n    // Fetch flashes from the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const fetchFlashes = {\n                \"FlashShortsContent.useEffect.fetchFlashes\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Get token from localStorage\n                        const token = localStorage.getItem(\"token\");\n                        if (!token) {\n                            console.warn(\"No authentication token found\");\n                            setError(\"Authentication required\");\n                            return;\n                        }\n                        const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/flashes?page=\".concat(page, \"&limit=10\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.data && response.data.flashes) {\n                            console.log(\"Flashes API response:\", response.data);\n                            if (page === 1) {\n                                setFlashes(response.data.flashes);\n                                // Initialize loading state for all videos\n                                const initialLoadingState = {};\n                                response.data.flashes.forEach({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                        initialLoadingState[flash.video_id] = true;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                setVideoLoading(initialLoadingState);\n                                // Fetch like status for initial flashes\n                                fetchLikeStatus(response.data.flashes.map({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (f)=>f.video_id\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]));\n                            } else {\n                                setFlashes({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>[\n                                            ...prev,\n                                            ...response.data.flashes\n                                        ]\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Initialize loading state for new videos\n                                setVideoLoading({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>{\n                                        const newState = {\n                                            ...prev\n                                        };\n                                        response.data.flashes.forEach({\n                                            \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                                newState[flash.video_id] = true;\n                                            }\n                                        }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                        return newState;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Fetch like status for new flashes\n                                fetchLikeStatus(response.data.flashes.map({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (f)=>f.video_id\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]));\n                            }\n                            setHasMore(response.data.next_page);\n                        } else {\n                            console.warn(\"Unexpected API response format:\", response.data);\n                            setError(\"Failed to load flashes\");\n                        }\n                    } catch (err) {\n                        console.error(\"Error fetching flashes:\", err);\n                        setError(\"Failed to load flashes\");\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.fetchFlashes\"];\n            fetchFlashes();\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        page\n    ]);\n    // Load more flashes when reaching the end\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (currentIndex >= flashes.length - 2 && hasMore && !loading) {\n                setPage({\n                    \"FlashShortsContent.useEffect\": (prevPage)=>prevPage + 1\n                }[\"FlashShortsContent.useEffect\"]);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length,\n        hasMore,\n        loading\n    ]);\n    // Handle video playback when current index changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (flashes.length === 0 || !isClient) return;\n            // Clear any existing YouTube timer\n            if (youtubeTimerRef.current) {\n                clearTimeout(youtubeTimerRef.current);\n                youtubeTimerRef.current = null;\n            }\n            // Pause all videos\n            Object.values(videoRefs.current).forEach({\n                \"FlashShortsContent.useEffect\": (videoEl)=>{\n                    if (videoEl && !videoEl.paused) {\n                        videoEl.pause();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect\"]);\n            const currentFlash = flashes[currentIndex];\n            if (!currentFlash) return;\n            // Handle YouTube videos with timer-based auto-advance\n            if (isYoutubeVideo(currentFlash) && !isPaused[currentFlash.video_id]) {\n                // Set a timer for YouTube videos (assuming average duration of 30 seconds)\n                // In a real implementation, you might want to use YouTube API to get actual duration\n                youtubeTimerRef.current = setTimeout({\n                    \"FlashShortsContent.useEffect\": ()=>{\n                        console.log(\"YouTube video timer ended: \".concat(currentFlash.video_id, \", auto-advancing to next flash\"));\n                        if (!isPaused[currentFlash.video_id]) {\n                            navigateToNext();\n                        }\n                    }\n                }[\"FlashShortsContent.useEffect\"], 30000); // 30 seconds default duration for YouTube videos\n            } else {\n                // Play current video if not manually paused (for non-YouTube videos)\n                const currentVideoId = currentFlash.video_id;\n                const currentVideo = videoRefs.current[currentVideoId];\n                if (currentVideo && !isPaused[currentVideoId]) {\n                    const playPromise = currentVideo.play();\n                    if (playPromise !== undefined) {\n                        playPromise.catch({\n                            \"FlashShortsContent.useEffect\": (error)=>{\n                                console.error(\"Error playing video:\", error);\n                            }\n                        }[\"FlashShortsContent.useEffect\"]);\n                    }\n                }\n            }\n            // Cleanup function\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes,\n        isClient,\n        isPaused\n    ]);\n    // Handle keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"FlashShortsContent.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") {\n                        navigateToPrevious();\n                    } else if (e.key === \"ArrowDown\" || e.key === \"ArrowRight\") {\n                        navigateToNext();\n                    } else if (e.key === \"Escape\") {\n                        router.push(\"/home/<USER>");\n                    } else if (e.key === \" \" || e.key === \"Spacebar\") {\n                        var _flashes_currentIndex;\n                        // Toggle play/pause on spacebar\n                        togglePlayPause((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleKeyDown\"];\n            window.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle touch events for swiping\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            let startY = 0;\n            // let startTime = 0; // Uncomment if needed for timing-based gestures\n            const handleTouchStart = {\n                \"FlashShortsContent.useEffect.handleTouchStart\": (e)=>{\n                    startY = e.touches[0].clientY;\n                // startTime = Date.now(); // Uncomment if needed for timing-based gestures\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchStart\"];\n            const handleTouchEnd = {\n                \"FlashShortsContent.useEffect.handleTouchEnd\": (e)=>{\n                    const deltaY = e.changedTouches[0].clientY - startY;\n                    // const deltaTime = Date.now() - startTime; // Uncomment if needed for timing-based gestures\n                    // Make touch more responsive by reducing the threshold\n                    if (Math.abs(deltaY) > 30) {\n                        if (deltaY > 0) {\n                            // Swipe down - go to previous\n                            navigateToPrevious();\n                        } else {\n                            // Swipe up - go to next\n                            navigateToNext();\n                        }\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchEnd\"];\n            // Add touchmove handler for more responsive scrolling\n            let lastY = 0;\n            let touchMoveThrottle = false;\n            const handleTouchMove = {\n                \"FlashShortsContent.useEffect.handleTouchMove\": (e)=>{\n                    const currentY = e.touches[0].clientY;\n                    // Only process every few pixels of movement to avoid too many updates\n                    if (!touchMoveThrottle && Math.abs(currentY - lastY) > 20) {\n                        lastY = currentY;\n                        touchMoveThrottle = true;\n                        // Schedule reset of throttle\n                        setTimeout({\n                            \"FlashShortsContent.useEffect.handleTouchMove\": ()=>{\n                                touchMoveThrottle = false;\n                            }\n                        }[\"FlashShortsContent.useEffect.handleTouchMove\"], 100);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchMove\"];\n            const container = containerRef.current;\n            container.addEventListener(\"touchstart\", handleTouchStart);\n            container.addEventListener(\"touchmove\", handleTouchMove, {\n                passive: true\n            });\n            container.addEventListener(\"touchend\", handleTouchEnd);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"touchstart\", handleTouchStart);\n                    container.removeEventListener(\"touchmove\", handleTouchMove);\n                    container.removeEventListener(\"touchend\", handleTouchEnd);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle wheel events for touchpad scrolling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            const handleWheel = {\n                \"FlashShortsContent.useEffect.handleWheel\": (e)=>{\n                    // Debounce the wheel event to prevent too many navigations\n                    if (e.deltaY > 50) {\n                        // Scroll down - go to next\n                        navigateToNext();\n                    } else if (e.deltaY < -50) {\n                        // Scroll up - go to previous\n                        navigateToPrevious();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleWheel\"];\n            const container = containerRef.current;\n            container.addEventListener(\"wheel\", handleWheel, {\n                passive: true\n            });\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"wheel\", handleWheel);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    const navigateToNext = ()=>{\n        if (currentIndex < flashes.length - 1) {\n            setCurrentIndex((prevIndex)=>prevIndex + 1);\n        } else {\n            // When reaching the last video, loop back to the first one\n            setCurrentIndex(0);\n        }\n    };\n    const navigateToPrevious = ()=>{\n        if (currentIndex > 0) {\n            setCurrentIndex((prevIndex)=>prevIndex - 1);\n        }\n    };\n    // Auto-advance to next flash when current video ends\n    const handleVideoEnded = (videoId)=>{\n        console.log(\"Video ended: \".concat(videoId, \", auto-advancing to next flash\"));\n        // Only auto-advance if the video wasn't manually paused\n        if (!isPaused[videoId]) {\n            navigateToNext();\n        }\n    };\n    const toggleLike = async (flashId)=>{\n        console.log('🔥 toggleLike called for flashId:', flashId);\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedFlashes.has(flashId);\n            console.log('🔥 isCurrentlyLiked:', isCurrentlyLiked);\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: flashId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🔥 API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const responseData = await response.json();\n            console.log('🔥 API Success Response:', responseData);\n            // Update localStorage to persist like status\n            const likedFlashesData = localStorage.getItem('likedFlashes');\n            const likedFlashesArray = likedFlashesData ? JSON.parse(likedFlashesData) : [];\n            if (isCurrentlyLiked) {\n                // Remove from liked flashes\n                const updatedLikedFlashes = likedFlashesArray.filter((id)=>id !== flashId);\n                localStorage.setItem('likedFlashes', JSON.stringify(updatedLikedFlashes));\n            } else {\n                // Add to liked flashes\n                if (!likedFlashesArray.includes(flashId)) {\n                    likedFlashesArray.push(flashId);\n                    localStorage.setItem('likedFlashes', JSON.stringify(likedFlashesArray));\n                }\n            }\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" flash: \").concat(flashId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedFlashes.has(flashId)) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    // Fetch like status for flashes from localStorage (temporary solution)\n    const fetchLikeStatus = async (flashIds)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token || flashIds.length === 0) return;\n            // Get liked flashes from localStorage as a temporary solution\n            // In a real implementation, this would come from the backend\n            const likedFlashesData = localStorage.getItem('likedFlashes');\n            const likedFlashesArray = likedFlashesData ? JSON.parse(likedFlashesData) : [];\n            // Filter to only include flashes that are currently loaded\n            const currentLikedFlashes = flashIds.filter((id)=>likedFlashesArray.includes(id));\n            console.log('Loaded like status for flashes:', currentLikedFlashes);\n            setLikedFlashes(new Set(currentLikedFlashes));\n        } catch (error) {\n            console.error('Error fetching like status:', error);\n            setLikedFlashes(new Set());\n        }\n    };\n    const togglePlayPause = (videoId)=>{\n        if (!videoId) return;\n        const currentFlash = flashes.find((flash)=>flash.video_id === videoId);\n        if (!currentFlash) return;\n        setIsPaused((prev)=>{\n            const newState = {\n                ...prev\n            };\n            newState[videoId] = !prev[videoId];\n            if (isYoutubeVideo(currentFlash)) {\n                // Handle YouTube video pause/play\n                if (newState[videoId]) {\n                    // Paused - clear the timer\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                } else {\n                    // Resumed - restart the timer\n                    youtubeTimerRef.current = setTimeout(()=>{\n                        console.log(\"YouTube video timer ended: \".concat(videoId, \", auto-advancing to next flash\"));\n                        if (!newState[videoId]) {\n                            navigateToNext();\n                        }\n                    }, 30000); // 30 seconds default duration\n                }\n            } else {\n                // Handle regular video pause/play\n                const videoEl = videoRefs.current[videoId];\n                if (videoEl) {\n                    if (newState[videoId]) {\n                        videoEl.pause();\n                    } else {\n                        videoEl.play().catch((err)=>console.error(\"Error playing video:\", err));\n                    }\n                }\n            }\n            return newState;\n        });\n    };\n    // Extract YouTube video ID from URL\n    const getYoutubeId = (url)=>{\n        if (!url) return \"\";\n        // If it's already just an ID, return it\n        if (url.length < 20 && !url.includes(\"/\")) return url;\n        // Try to extract ID from YouTube URL\n        const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;\n        const match = url.match(regExp);\n        return match && match[2].length === 11 ? match[2] : \"\";\n    };\n    // Get appropriate image source for a flash\n    const getImageSource = (flash)=>{\n        // If we have a thumbnail, use it\n        if (flash.video_thumbnail) {\n            return flash.video_thumbnail;\n        }\n        // If it's a YouTube video, use the YouTube thumbnail\n        if (flash.video_url && flash.video_url.includes(\"youtube\")) {\n            const videoId = getYoutubeId(flash.video_url);\n            if (videoId) {\n                return \"https://img.youtube.com/vi/\".concat(videoId, \"/hqdefault.jpg\");\n            }\n        }\n        // Default fallback - use a local placeholder image\n        return \"/pics/placeholder.svg\";\n    };\n    // Check if the video is from YouTube\n    const isYoutubeVideo = (flash)=>{\n        return typeof flash.video_url === \"string\" && flash.video_url.includes(\"youtube\");\n    };\n    // Format numbers for display (e.g., 1.2K)\n    const formatNumber = (num)=>{\n        if (!num) return \"0\";\n        if (num >= 1000000) {\n            return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        } else if (num >= 1000) {\n            return \"\".concat((num / 1000).toFixed(1), \"K\");\n        }\n        return num.toString();\n    };\n    // Add custom CSS for styling and responsiveness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            // Add a style tag for styling and responsiveness\n            const style = document.createElement(\"style\");\n            style.innerHTML = \"\\n      .shorts-page {\\n        background-color: #f8f8f8;\\n      }\\n\\n      .shorts-container {\\n        background-color: #000;\\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\\n        border-radius: 12px;\\n        overflow: hidden;\\n        position: relative;\\n      }\\n\\n      .shorts-video {\\n        width: 100%;\\n        height: 100%;\\n        object-fit: cover;\\n        background-color: #000;\\n      }\\n\\n      .shorts-controls {\\n        position: absolute;\\n        right: 8px;\\n        bottom: 80px;\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        gap: 16px;\\n        z-index: 20;\\n      }\\n\\n      .shorts-info {\\n        position: absolute;\\n        bottom: 0;\\n        left: 0;\\n        right: 0;\\n        padding: 16px;\\n        background: linear-gradient(transparent, rgba(0,0,0,0.8));\\n        z-index: 10;\\n      }\\n\\n      /* Fixed layout styles for proper centering */\\n      .layout-container {\\n        display: flex;\\n        width: 100%;\\n      }\\n\\n      .left-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .left-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .right-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .right-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .main-content {\\n        flex: 1;\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      /* Mobile responsive styles */\\n      @media (max-width: 768px) {\\n        .shorts-page {\\n          background-color: #000;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n        }\\n\\n        .shorts-container {\\n          width: 100vw !important;\\n          max-width: none !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          max-height: none !important;\\n          border-radius: 0 !important;\\n          border: none !important;\\n          margin: 0 !important;\\n          padding-bottom: 20px !important;\\n        }\\n\\n        .mobile-video-item {\\n          margin-bottom: 10px !important;\\n          border-radius: 8px !important;\\n          overflow: hidden !important;\\n        }\\n\\n        .mobile-nav-buttons {\\n          position: fixed !important;\\n          left: 16px !important;\\n          top: 50% !important;\\n          transform: translateY(-50%) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-nav-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-nav-button:disabled {\\n          background: rgba(255, 255, 255, 0.3) !important;\\n          color: rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-interaction-buttons {\\n          position: fixed !important;\\n          right: 16px !important;\\n          bottom: calc(120px + env(safe-area-inset-bottom, 20px)) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-interaction-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          color: white !important;\\n        }\\n\\n        .mobile-back-button {\\n          position: fixed !important;\\n          top: 20px !important;\\n          left: 16px !important;\\n          z-index: 50 !important;\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-user-info {\\n          position: absolute !important;\\n          top: 20px !important;\\n          left: 80px !important;\\n          right: 16px !important;\\n          z-index: 50 !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 12px !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: space-between !important;\\n        }\\n\\n        .mobile-unlock-vendor {\\n          position: absolute !important;\\n          bottom: calc(20px + env(safe-area-inset-bottom, 10px)) !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n          background: #B31B1E !important;\\n          color: white !important;\\n          border: none !important;\\n          border-radius: 8px !important;\\n          padding: 12px 24px !important;\\n          font-weight: 600 !important;\\n          box-shadow: 0 4px 12px rgba(179, 27, 30, 0.4) !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n        }\\n\\n        .mobile-vendor-details {\\n          position: absolute !important;\\n          bottom: 80px !important;\\n          left: 16px !important;\\n          right: 16px !important;\\n          z-index: 60 !important;\\n          background: rgba(255, 255, 255, 0.95) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 16px !important;\\n          max-height: 50vh !important;\\n          overflow-y: auto !important;\\n          color: black !important;\\n        }\\n\\n        .mobile-progress-indicator {\\n          position: fixed !important;\\n          top: 80px !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n        }\\n\\n        /* Hide desktop navigation buttons on mobile */\\n        .desktop-nav-buttons {\\n          display: none !important;\\n        }\\n\\n        .desktop-interaction-buttons {\\n          display: none !important;\\n        }\\n\\n        /* Ensure main content takes full space on mobile */\\n        .main-content-mobile {\\n          padding: 0 !important;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n          margin: 0 !important;\\n          width: 100vw !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          position: relative !important;\\n        }\\n      }\\n\\n      /* Desktop styles */\\n      @media (min-width: 769px) {\\n        .mobile-nav-buttons,\\n        .mobile-interaction-buttons,\\n        .mobile-back-button,\\n        .mobile-user-info,\\n        .mobile-unlock-vendor,\\n        .mobile-progress-indicator {\\n          display: none !important;\\n        }\\n      }\\n    \";\n            document.head.appendChild(style);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    document.head.removeChild(style);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"opacity-0\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 813,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen w-full shorts-page\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.TopNavigation, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                    lineNumber: 820,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 819,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full h-[calc(100vh-80px)] md:mt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-sidebar \".concat(leftSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.SideNavigation, {\n                            expanded: leftSidebarExpanded,\n                            onExpand: ()=>setLeftSidebarExpanded(true),\n                            onCollapse: ()=>setLeftSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 825,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center px-4 md:px-4 px-0 relative main-content-mobile\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"absolute top-4 left-4 z-50 bg-white rounded-full p-3 text-black shadow-lg hover:bg-gray-200 transition-colors flex items-center justify-center hidden md:flex\",\n                                style: {\n                                    width: \"48px\",\n                                    height: \"48px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 845,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 840,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"mobile-back-button md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center w-full md:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-nav-buttons flex flex-col items-center justify-center space-y-4 mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === 0 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === flashes.length - 1 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-nav-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 907,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: containerRef,\n                                        className: \"shorts-container relative w-full md:w-[400px] h-full md:h-[min(calc(100vh-100px),89vh)]\",\n                                        style: {\n                                            // Desktop styles\n                                            ... true && window.innerWidth >= 768 ? {\n                                                width: \"400px\",\n                                                height: \"min(calc(100vh - 100px), 89vh)\",\n                                                margin: \"0 auto\",\n                                                border: \"2px solid #B31B1E\",\n                                                borderRadius: \"12px\",\n                                                overflow: \"hidden\"\n                                            } : {\n                                                // Mobile styles - full screen with safe area\n                                                width: \"100vw\",\n                                                height: \"calc(100vh - env(safe-area-inset-bottom, 20px))\",\n                                                margin: \"0\",\n                                                border: \"none\",\n                                                borderRadius: \"0\",\n                                                overflow: \"hidden\",\n                                                paddingBottom: \"env(safe-area-inset-bottom, 20px)\"\n                                            }\n                                        },\n                                        children: [\n                                            loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                                children: \"Loading flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 15\n                                            }, this),\n                                            error && !loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-red-500\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 15\n                                            }, this),\n                                            flashes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full w-full transition-transform duration-300 ease-out\",\n                                                style: {\n                                                    transform: \"translateY(-\".concat(currentIndex * 102, \"%)\")\n                                                },\n                                                children: flashes.map((flash, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full w-full flex items-center justify-center relative bg-black \".concat( true && window.innerWidth < 768 ? 'mobile-video-item' : ''),\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(index * 102, \"%\"),\n                                                            left: 0,\n                                                            right: 0,\n                                                            bottom: 0,\n                                                            overflow: \"hidden\",\n                                                            borderRadius: \"8px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-full w-full relative overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 overflow-hidden\",\n                                                                    children: isYoutubeVideo(flash) ? // YouTube iframe for YouTube videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 983,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 984,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 982,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 981,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                                                src: \"https://www.youtube.com/embed/\".concat(getYoutubeId(flash.video_url), \"?autoplay=1&controls=0&rel=0&showinfo=0&mute=0\"),\n                                                                                title: flash.video_name,\n                                                                                className: \"shorts-video\",\n                                                                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                                                                allowFullScreen: true,\n                                                                                onLoad: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 988,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 27\n                                                                    }, this) : // Video player for Cloudfront videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full overflow-hidden\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1008,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1009,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1007,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1006,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                ref: (el)=>{\n                                                                                    videoRefs.current[flash.video_id] = el;\n                                                                                },\n                                                                                src: flash.video_url,\n                                                                                className: \"shorts-video\",\n                                                                                playsInline: true,\n                                                                                muted: false,\n                                                                                controls: false,\n                                                                                poster: getImageSource(flash),\n                                                                                onLoadStart: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: true\n                                                                                        }));\n                                                                                },\n                                                                                onCanPlay: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onError: ()=>{\n                                                                                    console.error(\"Failed to load video: \".concat(flash.video_name));\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onEnded: ()=>handleVideoEnded(flash.video_id)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1013,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    togglePlayPause(flash.video_id);\n                                                                                },\n                                                                                className: \"absolute inset-0 w-full h-full flex items-center justify-center z-10 group\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(isPaused[flash.video_id] ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\", \" transition-opacity duration-200 bg-black/40 rounded-full p-4 shadow-lg\"),\n                                                                                    children: isPaused[flash.video_id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"white\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                                            points: \"5 3 19 12 5 21 5 3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1062,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1051,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"6\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1076,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"14\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1082,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1065,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1043,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1036,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1003,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 left-4 right-4 z-20 flex items-center bg-black/20 backdrop-blur-sm rounded-lg p-2 hidden md:flex\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1100,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1111,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1112,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1110,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1099,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1176,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1117,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1098,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-user-info md:hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1184,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1195,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1196,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1194,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1183,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1260,\n                                                                                columnNumber: 67\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1201,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1182,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-8 left-0 right-0 flex justify-center z-10 hidden md:flex\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-[#B31B1E] text-white text-sm font-medium px-4 py-2 rounded-md flex items-center\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowVendorDetails((prev)=>({\n                                                                                ...prev,\n                                                                                [flash.video_id]: !prev[flash.video_id]\n                                                                            }));\n                                                                    },\n                                                                    children: [\n                                                                        \"Unlock Vendor\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4 ml-1\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1279,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1278,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1266,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mobile-unlock-vendor md:hidden\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    setShowVendorDetails((prev)=>({\n                                                                            ...prev,\n                                                                            [flash.video_id]: !prev[flash.video_id]\n                                                                        }));\n                                                                },\n                                                                children: [\n                                                                    \"Unlock Vendor\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        className: \"h-4 w-4 ml-1\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1298,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1297,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1286,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-20 left-4 right-4 p-4 bg-white/90 rounded-lg text-black max-h-[40%] overflow-y-auto z-30 hidden md:block\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1306,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1305,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === currentIndex && showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-vendor-details md:hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1316,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1315,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(flash.video_id, \"-\").concat(index), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 951,\n                                                columnNumber: 15\n                                            }, this),\n                                            loading && page > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-20 left-0 right-0 text-center text-white bg-black/50 py-2 mx-auto w-48 rounded-full backdrop-blur-sm z-20\",\n                                                children: \"Loading more flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1329,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-14 left-0 right-0 px-4 z-20 hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1338,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1346,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1335,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mobile-progress-indicator md:hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1355,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1363,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1353,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1352,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-interaction-buttons flex flex-col items-center justify-center space-y-6 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100 mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1375,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1373,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1372,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 \".concat(likedFlashes.has(((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id) || '') ? 'bg-red-100 hover:bg-red-200' : 'bg-black/20 hover:bg-black/40'),\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        console.log('Desktop like button clicked for flash:', currentFlash.video_id);\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                title: likedFlashes.has(((_flashes_currentIndex1 = flashes[currentIndex]) === null || _flashes_currentIndex1 === void 0 ? void 0 : _flashes_currentIndex1.video_id) || '') ? 'Unlike' : 'Like',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex2 = flashes[currentIndex]) === null || _flashes_currentIndex2 === void 0 ? void 0 : _flashes_currentIndex2.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex3 = flashes[currentIndex]) === null || _flashes_currentIndex3 === void 0 ? void 0 : _flashes_currentIndex3.video_id) || '') ? \"#B31B1E\" : \"white\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1397,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1396,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1381,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1411,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1412,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1410,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1409,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1370,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-interaction-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash === null || currentFlash === void 0 ? void 0 : currentFlash.user_id) {\n                                                        // Navigate to user profile - you can implement this navigation\n                                                        console.log('Navigate to profile:', currentFlash.user_id);\n                                                    // router.push(`/profile/${currentFlash.user_id}`);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1432,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"7\",\n                                                            r: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1433,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1420,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 \".concat(likedFlashes.has(((_flashes_currentIndex4 = flashes[currentIndex]) === null || _flashes_currentIndex4 === void 0 ? void 0 : _flashes_currentIndex4.video_id) || '') ? 'bg-red-100 hover:bg-red-200' : 'bg-black/20 hover:bg-black/40'),\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        console.log('Mobile like button clicked for flash:', currentFlash.video_id);\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                title: likedFlashes.has(((_flashes_currentIndex5 = flashes[currentIndex]) === null || _flashes_currentIndex5 === void 0 ? void 0 : _flashes_currentIndex5.video_id) || '') ? 'Unlike' : 'Like',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex6 = flashes[currentIndex]) === null || _flashes_currentIndex6 === void 0 ? void 0 : _flashes_currentIndex6.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex7 = flashes[currentIndex]) === null || _flashes_currentIndex7 === void 0 ? void 0 : _flashes_currentIndex7.video_id) || '') ? \"#B31B1E\" : \"white\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1438,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1461,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1460,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1459,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1468,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1469,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1466,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1476,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1477,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1478,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1475,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1474,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1418,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 856,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 838,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-sidebar \".concat(rightSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.RightSidebar, {\n                            expanded: rightSidebarExpanded,\n                            onExpand: ()=>setRightSidebarExpanded(true),\n                            onCollapse: ()=>setRightSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 1491,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 1486,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 823,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 817,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashShortsContent, \"aeQpwdVKjXsnvNtC+OoDi2XkRdk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = FlashShortsContent;\n// Loading fallback component\nfunction FlashShortsLoading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-screen w-full bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-white text-xl\",\n            children: \"Loading flashes...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1506,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1505,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FlashShortsLoading;\n// Main page component with Suspense\nfunction FlashShortsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsLoading, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1514,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1515,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1514,\n        columnNumber: 5\n    }, this);\n}\n_c2 = FlashShortsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FlashShortsContent\");\n$RefreshReg$(_c1, \"FlashShortsLoading\");\n$RefreshReg$(_c2, \"FlashShortsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/shorts/page.tsx\n"));

/***/ })

});