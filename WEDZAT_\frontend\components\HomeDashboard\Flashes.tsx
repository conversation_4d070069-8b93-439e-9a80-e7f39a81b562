"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import UserAvatar from "./UserAvatar";
import axios from "../../services/axiosConfig";
import { useRouter } from "next/navigation";

// Define interface for flash video items
interface FlashVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface ApiResponse {
  flashes: FlashVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

interface FlashesProps {
  shouldLoad?: boolean;
}

const FlashesSection: React.FC<FlashesProps> = ({ shouldLoad = false }) => {
  const router = useRouter();
  const [flashes, setFlashes] = useState<FlashVideo[]>([]);
  const [admiringUsers, setAdmiringUsers] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState<boolean>(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreTriggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string, event?: React.MouseEvent) => {
    // Stop event propagation to prevent triggering parent click events
    if (event) {
      event.stopPropagation();
    }

    // If we have a userId, navigate to that specific user's profile
    if (userId) {
      router.push(`/profile/${userId}`);
    } else if (username) {
      // For now, just use the username as a parameter
      // In a real app, you might want to fetch the user ID first
      router.push(`/profile/${username}`);
    } else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  // User avatar placeholders (using local images instead of external URLs)
  const userAvatarPlaceholders = [
    "/pics/1stim.jfif",
    "/pics/2ndim.jfif",
    "/pics/jpeg3.jfif",
    "/pics/user-profile.png",
    "/pics/user-profile.png",
  ];

  // Fallback data if API fails - using empty array
  const getFallbackFlashes = (): FlashVideo[] => [];

  // Function to fetch flashes from the API
  const fetchFlashes = async (pageNumber: number, isInitialLoad: boolean = false) => {
    // Prevent multiple simultaneous requests
    if ((isInitialLoad && loading) || (!isInitialLoad && loadingMore)) {
      console.log('Request already in progress, skipping');
      return;
    }

    try {
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      console.log(`Fetching flashes for page ${pageNumber}...`);

      // Simple token retrieval
      const token = localStorage.getItem('token');

      console.log(`Auth token found: ${token ? 'Yes' : 'No'}`);

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setFlashes([]);
        return;
      }

      // Make API request
      const response = await axios.get<ApiResponse>(`/flashes?page=${pageNumber}&limit=10`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('API response status:', response.status);
      console.log('Flashes API response:', response.data);

      // Process the data
      if (response.data && response.data.flashes) {
        console.log(`Loaded ${response.data.flashes.length} flashes for page ${pageNumber}`);

        // Log the first item for debugging
        if (response.data.flashes.length > 0) {
          console.log('Sample flash data:', response.data.flashes[0]);
        }

        // Process the response
        const processedFlashes = response.data.flashes.map(flash => {
          if (!flash.video_thumbnail) {
            console.log(`Flash missing thumbnail: ${flash.video_id}`);
          }
          return flash;
        });

        if (pageNumber === 1) {
          setFlashes(processedFlashes);
        } else {
          setFlashes(prev => [...prev, ...processedFlashes]);
        }

        setHasMore(response.data.next_page);
        setPage(pageNumber); // Update the current page
        setError(null); // Clear any previous errors
      } else {
        console.warn('Unexpected response format:', response.data);
        setError('Failed to load flashes - unexpected response format');
        setFlashes([]);
      }
    } catch (err) {
      console.error('API request failed:', err);
      setError('Failed to load flashes');
      setFlashes([]);
    } finally {
      // Always clear loading states
      if (isInitialLoad) {
        setLoading(false);
        console.log('Initial loading complete');
      } else {
        setLoadingMore(false);
        console.log('Loading more complete');
      }
    }
  };

  // Fetch first page of flashes as soon as the component mounts
  useEffect(() => {
    // Only trigger when shouldLoad changes to true
    if (shouldLoad) {
      console.log('Flashes component is now visible and ready to load data');

      // Set a flag to track initial load
      setInitialLoadComplete(true);

      // IMMEDIATE API request with no conditions or delays
      console.log('Triggering initial flashes load IMMEDIATELY...');
      setLoading(true);

      // Skip the fetchFlashes function and make the API call directly here
      const token = localStorage.getItem('token');

      if (token) {
        // Make direct API request
        console.log('Making direct API request for flashes page 1...');
        axios.get<ApiResponse>(`/flashes?page=1&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
          .then(response => {
            console.log('API response received for flashes page 1');
            if (response.data && response.data.flashes) {
              setFlashes(response.data.flashes);
              setHasMore(response.data.next_page);
              setPage(1);
              setError(null);
            } else {
              setFlashes([]);
              setError('No flashes found');
            }
          })
          .catch(err => {
            console.error('Direct API request failed:', err);
            setError('Failed to load flashes');
            setFlashes([]);
          })
          .finally(() => {
            setLoading(false);
            console.log('Initial loading complete');
          });
      } else {
        setLoading(false);
        setError('Authentication required');
      }
    }
  }, [shouldLoad]); // Only depend on shouldLoad

  // Failsafe to ensure content is loaded - only show error after timeout
  useEffect(() => {
    // If we're stuck in loading state for more than 10 seconds, show error
    let timeoutId: NodeJS.Timeout | null = null;

    if (loading && initialLoadComplete) {
      timeoutId = setTimeout(() => {
        console.log('Loading timeout reached - API request may have failed');
        setLoading(false);
        if (flashes.length === 0) {
          setError('Unable to load flashes. Please check your network connection.');
        }
      }, 10000); // 10 second timeout for slow networks
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [loading, initialLoadComplete, flashes.length]);

  // Setup Intersection Observer for horizontal lazy loading
  useEffect(() => {
    // Only set up observer after initial load and if there's more content to fetch
    if (!initialLoadComplete || !hasMore) return;

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
      console.log('Disconnected previous intersection observer');
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        // If the trigger element is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && !loadingMore && hasMore) {
          console.log('Load more trigger is visible, loading next page...');
          console.log('Intersection ratio:', entries[0].intersectionRatio);
          const nextPage = page + 1;
          fetchFlashes(nextPage, false);
        }
      },
      // Use the scroll container as the root for the intersection observer
      {
        root: scrollContainerRef.current,
        threshold: 0.1, // Trigger when 10% of the element is visible
        rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier
      }
    );

    // Start observing the trigger element
    if (loadMoreTriggerRef.current) {
      observerRef.current.observe(loadMoreTriggerRef.current);
      console.log('Now observing load more trigger element');
    } else {
      console.warn('Load more trigger element not found');
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        console.log('Disconnected intersection observer');
      }
    };
  }, [hasMore, loading, loadingMore, page, initialLoadComplete]);

  // Load admiring status from localStorage when component mounts
  useEffect(() => {
    try {
      const admiredUsersJson = localStorage.getItem('admiredUsers');
      if (admiredUsersJson) {
        const admiredUsers = JSON.parse(admiredUsersJson);
        setAdmiringUsers(admiredUsers);
        console.log('Loaded admiring status from localStorage:', admiredUsers);
      }
    } catch (error) {
      console.error('Error loading admiring status from localStorage:', error);
    }
  }, []);

  // Function to handle Admire button click
  const handleAdmire = async (userId?: string, event?: React.MouseEvent) => {
    // Stop event propagation to prevent triggering parent click events
    if (event) {
      event.stopPropagation();
    }

    if (!userId) {
      console.warn('No user ID provided for Admire action');
      return;
    }

    // Check if already admiring this user
    const isCurrentlyAdmiring = admiringUsers[userId] || false;

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Optimistically update UI state
      setAdmiringUsers(prev => ({
        ...prev,
        [userId]: !isCurrentlyAdmiring
      }));

      // Make API call to follow/unfollow user
      const endpoint = isCurrentlyAdmiring
        ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow'
        : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';

      console.log(`Making request to ${endpoint} with user ID: ${userId}`);

      try {
        // Make the API call
        const response = await axios.post(
          endpoint,
          { target_user_id: userId },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('API response:', response.data);

        // Update localStorage with the new state
        try {
          const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
          const admiredUsers = JSON.parse(admiredUsersJson);

          if (!isCurrentlyAdmiring) {
            admiredUsers[userId] = true;
          } else {
            delete admiredUsers[userId];
          }

          localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));
          console.log('Updated admired users in localStorage:', admiredUsers);
        } catch (storageError) {
          console.error('Error updating localStorage:', storageError);
        }
      } catch (apiError: any) {
        console.error(`Error ${isCurrentlyAdmiring ? 'unadmiring' : 'admiring'} user:`, apiError);

        // Revert UI state on error
        setAdmiringUsers(prev => ({
          ...prev,
          [userId]: isCurrentlyAdmiring
        }));
      }
    } catch (error) {
      console.error('Unexpected error in handleAdmire:', error);

      // Revert UI state on unexpected errors
      setAdmiringUsers(prev => ({
        ...prev,
        [userId]: isCurrentlyAdmiring
      }));
    }
  };

  // Get appropriate image source for a flash
  const getImageSource = (flash: FlashVideo): string => {
    if (flash.video_thumbnail) {
      return flash.video_thumbnail;
    }
    return '/pics/placeholder.svg';
  };

  return (
    <section className="w-full mb-8 relative">
      <div className="flex items-center justify-between mb-4">
        <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-black">
          Flashes
        </h2>
        <a
          href="/home/<USER>"
          className="text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]"
        >
          See all
        </a>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="py-10 text-center">
          <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          <span className="text-gray-600">Loading flashes...</span>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="py-10 text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={() => {
              console.log('Retrying flashes load...');
              setError(null);
              fetchFlashes(1, true);
            }}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            Retry
          </button>
        </div>
      )}

      {/* Debug info in development only */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-0 right-0 bg-black/50 text-white text-xs p-2 rounded-bl-md z-50 max-w-[200px]">
          <div>State: {loading ? 'Loading' : error ? 'Error' : flashes.length === 0 ? 'Empty' : 'Loaded'}</div>
          <div>Count: {flashes.length}</div>
          <div>Page: {page}</div>
          <div>Has more: {hasMore ? 'Yes' : 'No'}</div>
        </div>
      )} */}

      {/* Horizontal scroll container */}
      {!loading && (
        <div
          className="overflow-x-auto scrollbar-hide relative w-full"
          ref={scrollContainerRef}
          style={{
            scrollBehavior: 'smooth',
            WebkitOverflowScrolling: 'touch',
            minHeight: flashes.length === 0 && !error ? '220px' : 'auto'
          }}
        >
          {/* Empty state message when no flashes */}
          {flashes.length === 0 && !error && !loading && (
            <div className="flex items-center justify-center h-[220px] w-full">
              <div className="text-gray-400">No flashes available</div>
            </div>
          )}

          <div className="flex gap-3 pb-4 flex-nowrap">
            {flashes.map((flash, index) => (
              <div
                key={`${flash.video_id}-${index}`}
                onClick={() => {
                  // Open in shorts view with current index
                  window.location.href = `/home/<USER>/shorts?index=${index}`;
                }}
                className="min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105"
                style={{
                  background: "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                }}
              >
                {/* User info at top */}
                <div className="absolute top-0 left-0 right-0 z-10 p-2 flex items-center">
                  <div
                    className="flex items-center"
                    onClick={(e) => navigateToUserProfile(flash.user_id, flash.user_name, e)}
                  >
                    <UserAvatar
                      username={flash.user_name || "user"}
                      size="sm"
                      isGradientBorder={true}
                      imageUrl={userAvatarPlaceholders[index % userAvatarPlaceholders.length]}
                      onClick={(e) => navigateToUserProfile(flash.user_id, flash.user_name, e)}
                    />
                    <div className="ml-2 text-white text-sm font-medium">
                      <div>{flash.user_name || "user"}</div>
                      <div className="text-xs opacity-80">{Math.floor(Math.random() * 2) + 0.1}M Admiring</div>
                    </div>
                  </div>

                  {/* Admire button */}
                  <button
                    className="ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center"
                    onClick={(e) => handleAdmire(flash.user_id, e)}
                  >
                    {admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire'}
                    {!admiringUsers[flash.user_id || ''] && <span className="ml-1">+</span>}
                  </button>
                </div>

                {/* Main image */}
                <div className="absolute inset-0 z-0">
                  <div className="relative w-full h-full">
                    <Image
                      src={getImageSource(flash)}
                      alt={flash.video_name || "Flash Video"}
                      fill
                      sizes="(max-width: 480px) 120px, (max-width: 640px) 140px, (max-width: 768px) 180px, 200px"
                      className="object-cover"
                      {...(index < 2 ? { priority: true } : { loading: 'lazy' })}
                      unoptimized={!flash.video_thumbnail}
                      placeholder="blur"
                      blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg=="
                      onError={(e) => {
                        const imgElement = e.target as HTMLImageElement;
                        if (imgElement) {
                          imgElement.src = '/pics/placeholder.svg';
                        }
                      }}
                    />
                  </div>
                </div>

                {/* Interaction buttons on the right side */}
                <div className="absolute right-2 bottom-16 flex flex-col items-center space-y-4 z-10">
                  <button className="text-white opacity-80 hover:opacity-100">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M8 12h8"></path>
                      <path d="M12 8v8"></path>
                    </svg>
                  </button>
                  <button className="text-white opacity-80 hover:opacity-100">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                    </svg>
                  </button>
                  <button className="text-white opacity-80 hover:opacity-100">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="22" y1="2" x2="11" y2="13"></line>
                      <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                    </svg>
                  </button>
                </div>

                {/* Unlock vendor button at bottom */}
                <div className="absolute bottom-4 left-0 right-0 flex justify-center z-10">
                  <button
                    className="bg-[#B31B1E] text-white text-sm font-medium px-3 py-1 rounded-md flex items-center"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('Unlock vendor clicked');
                    }}
                  >
                    Unlock Vendor
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}

            {/* Load more trigger element - this is what IntersectionObserver watches */}
            {hasMore && (
              <div
                ref={loadMoreTriggerRef}
                className="flex-shrink-0 w-10 h-full opacity-0"
                style={{
                  position: 'relative',
                  // Add debug outline in development
                  outline: process.env.NODE_ENV === 'development' ? '1px dashed rgba(255, 0, 0, 0.3)' : 'none'
                }}
                aria-hidden="true"
                data-testid="flashes-load-more-trigger"
              />
            )}

            {/* Loading indicator - only show when loading more */}
            {loadingMore && (
              <div className="flex-shrink-0 flex items-center justify-center min-w-[100px] h-[220px]">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="ml-2 text-sm text-gray-600">Loading more...</span>
              </div>
            )}
          </div>
        </div>
      )}

      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </section>
  );
};

export default FlashesSection;