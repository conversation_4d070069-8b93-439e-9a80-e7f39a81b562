"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import UserAvatar from "./UserAvatar";
import axios from "../../services/axiosConfig";
import { useRouter } from "next/navigation";
import { useLocation } from "../../contexts/LocationContext";

// Define interface for flash video items
interface FlashVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface ApiResponse {
  flashes: FlashVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

interface FlashesProps {
  shouldLoad?: boolean;
}

const FlashesSection: React.FC<FlashesProps> = ({ shouldLoad = false }) => {
  const router = useRouter();
  const { selectedLocation } = useLocation();
  const [flashes, setFlashes] = useState<FlashVideo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState<boolean>(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreTriggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string, event?: React.MouseEvent) => {
    // Stop event propagation to prevent triggering parent click events
    if (event) {
      event.stopPropagation();
    }

    // If we have a userId, navigate to that specific user's profile
    if (userId) {
      router.push(`/profile/${userId}`);
    } else if (username) {
      // For now, just use the username as a parameter
      // In a real app, you might want to fetch the user ID first
      router.push(`/profile/${username}`);
    } else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  // User avatar placeholders (using local images instead of external URLs)
  const userAvatarPlaceholders = [
    "/pics/1stim.jfif",
    "/pics/2ndim.jfif",
    "/pics/jpeg3.jfif",
    "/pics/user-profile.png",
    "/pics/user-profile.png",
  ];

  // Fallback data if API fails - using empty array
  const getFallbackFlashes = (): FlashVideo[] => [];

  // Function to fetch flashes from the API
  const fetchFlashes = async (pageNumber: number, isInitialLoad: boolean = false) => {
    // Prevent multiple simultaneous requests
    if ((isInitialLoad && loading) || (!isInitialLoad && loadingMore)) {
      console.log('Request already in progress, skipping');
      return;
    }

    try {
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      console.log(`Fetching flashes for page ${pageNumber}...`);

      // Simple token retrieval
      const token = localStorage.getItem('token');

      console.log(`Auth token found: ${token ? 'Yes' : 'No'}`);

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setFlashes([]);
        return;
      }

      // Build API URL with location parameter if selected
      let apiUrl = `/flashes?page=${pageNumber}&limit=10`;
      if (selectedLocation) {
        apiUrl += `&location=${encodeURIComponent(selectedLocation)}`;
      }

      // Make API request
      const response = await axios.get<ApiResponse>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('API response status:', response.status);
      console.log('Flashes API response:', response.data);

      // Process the data
      if (response.data && response.data.flashes) {
        console.log(`Loaded ${response.data.flashes.length} flashes for page ${pageNumber}`);

        // Log the first item for debugging
        if (response.data.flashes.length > 0) {
          console.log('Sample flash data:', response.data.flashes[0]);
        }

        // Process the response
        const processedFlashes = response.data.flashes.map(flash => {
          if (!flash.video_thumbnail) {
            console.log(`Flash missing thumbnail: ${flash.video_id}`);
          }
          return flash;
        });

        if (pageNumber === 1) {
          setFlashes(processedFlashes);
        } else {
          setFlashes(prev => [...prev, ...processedFlashes]);
        }

        setHasMore(response.data.next_page);
        setPage(pageNumber); // Update the current page
        setError(null); // Clear any previous errors
      } else {
        console.warn('Unexpected response format:', response.data);
        setError('Failed to load flashes - unexpected response format');
        setFlashes([]);
      }
    } catch (err) {
      console.error('API request failed:', err);
      setError('Failed to load flashes');
      setFlashes([]);
    } finally {
      // Always clear loading states
      if (isInitialLoad) {
        setLoading(false);
        console.log('Initial loading complete');
      } else {
        setLoadingMore(false);
        console.log('Loading more complete');
      }
    }
  };

  // Fetch first page of flashes as soon as the component mounts
  useEffect(() => {
    // Only trigger when shouldLoad changes to true
    if (shouldLoad) {
      console.log('Flashes component is now visible and ready to load data');

      // Set a flag to track initial load
      setInitialLoadComplete(true);

      // IMMEDIATE API request with no conditions or delays
      console.log('Triggering initial flashes load IMMEDIATELY...');
      setLoading(true);

      // Skip the fetchFlashes function and make the API call directly here
      const token = localStorage.getItem('token');

      if (token) {
        // Build API URL with location parameter if selected
        let apiUrl = `/flashes?page=1&limit=10`;
        if (selectedLocation) {
          apiUrl += `&location=${encodeURIComponent(selectedLocation)}`;
        }

        // Make direct API request
        console.log('Making direct API request for flashes page 1...');
        axios.get<ApiResponse>(apiUrl, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
          .then(response => {
            console.log('API response received for flashes page 1');
            if (response.data && response.data.flashes) {
              setFlashes(response.data.flashes);
              setHasMore(response.data.next_page);
              setPage(1);
              setError(null);
            } else {
              setFlashes([]);
              setError('No flashes found');
            }
          })
          .catch(err => {
            console.error('Direct API request failed:', err);
            setError('Failed to load flashes');
            setFlashes([]);
          })
          .finally(() => {
            setLoading(false);
            console.log('Initial loading complete');
          });
      } else {
        setLoading(false);
        setError('Authentication required');
      }
    }
  }, [shouldLoad]); // Only depend on shouldLoad

  // Failsafe to ensure content is loaded - only show error after timeout
  useEffect(() => {
    // If we're stuck in loading state for more than 10 seconds, show error
    let timeoutId: NodeJS.Timeout | null = null;

    if (loading && initialLoadComplete) {
      timeoutId = setTimeout(() => {
        console.log('Loading timeout reached - API request may have failed');
        setLoading(false);
        if (flashes.length === 0) {
          setError('Unable to load flashes. Please check your network connection.');
        }
      }, 10000); // 10 second timeout for slow networks
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [loading, initialLoadComplete, flashes.length]);

  // Setup Intersection Observer for horizontal lazy loading
  useEffect(() => {
    // Only set up observer after initial load and if there's more content to fetch
    if (!initialLoadComplete || !hasMore) return;

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
      console.log('Disconnected previous intersection observer');
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        // If the trigger element is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && !loadingMore && hasMore) {
          console.log('Load more trigger is visible, loading next page...');
          console.log('Intersection ratio:', entries[0].intersectionRatio);
          const nextPage = page + 1;
          fetchFlashes(nextPage, false);
        }
      },
      // Use the scroll container as the root for the intersection observer
      {
        root: scrollContainerRef.current,
        threshold: 0.1, // Trigger when 10% of the element is visible
        rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier
      }
    );

    // Start observing the trigger element
    if (loadMoreTriggerRef.current) {
      observerRef.current.observe(loadMoreTriggerRef.current);
      console.log('Now observing load more trigger element');
    } else {
      console.warn('Load more trigger element not found');
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        console.log('Disconnected intersection observer');
      }
    };
  }, [hasMore, loading, loadingMore, page, initialLoadComplete]);

  // Get appropriate image source for a flash
  const getImageSource = (flash: FlashVideo): string => {
    if (flash.video_thumbnail) {
      return flash.video_thumbnail;
    }
    return '/pics/placeholder.svg';
  };

  return (
    <section className="w-full mb-8 relative">
      <div className="flex items-center justify-between mb-4">
        <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-black">
          Flashes
        </h2>
        <a
          href="/home/<USER>"
          className="text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]"
        >
          See all
        </a>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="py-10 text-center">
          <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          <span className="text-gray-600">Loading flashes...</span>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="py-10 text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={() => {
              console.log('Retrying flashes load...');
              setError(null);
              fetchFlashes(1, true);
            }}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            Retry
          </button>
        </div>
      )}

      {/* Debug info in development only */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-0 right-0 bg-black/50 text-white text-xs p-2 rounded-bl-md z-50 max-w-[200px]">
          <div>State: {loading ? 'Loading' : error ? 'Error' : flashes.length === 0 ? 'Empty' : 'Loaded'}</div>
          <div>Count: {flashes.length}</div>
          <div>Page: {page}</div>
          <div>Has more: {hasMore ? 'Yes' : 'No'}</div>
        </div>
      )} */}

      {/* Horizontal scroll container */}
      {!loading && (
        <div
          className="overflow-x-auto scrollbar-hide relative w-full"
          ref={scrollContainerRef}
          style={{
            scrollBehavior: 'smooth',
            WebkitOverflowScrolling: 'touch',
            minHeight: flashes.length === 0 && !error ? '220px' : 'auto'
          }}
        >
          {/* Empty state message when no flashes */}
          {flashes.length === 0 && !error && !loading && (
            <div className="flex items-center justify-center h-[220px] w-full">
              <div className="text-gray-400">No flashes available</div>
            </div>
          )}

          <div className="flex gap-3 pb-4 flex-nowrap">
            {flashes.map((flash, index) => (
              <div
                key={`${flash.video_id}-${index}`}
                onClick={() => {
                  // Open in shorts view with current index
                  window.location.href = `/home/<USER>/shorts?index=${index}`;
                }}
                className="min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] p-[6px] sm:p-[10px] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105"
                style={{
                  background: "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                }}
              >
                <div
                  style={{
                    position: "absolute",
                    top: "10px",
                    left: "10px",
                    zIndex: 2,
                  }}
                  onClick={(e) => navigateToUserProfile(flash.user_id, flash.user_name, e)}
                >
                  <UserAvatar
                    username={flash.user_name || "user"}
                    size="sm"
                    isGradientBorder={true}
                    imageUrl={userAvatarPlaceholders[index % userAvatarPlaceholders.length]}
                    onClick={() => navigateToUserProfile(flash.user_id, flash.user_name)}
                  />
                </div>

                {/* Add username text that's also clickable */}
                <div
                  style={{
                    position: "absolute",
                    top: "10px",
                    left: "50px",
                    zIndex: 2,
                    color: "white",
                    textShadow: "0px 1px 2px rgba(0,0,0,0.8)"
                  }}
                  onClick={(e) => navigateToUserProfile(flash.user_id, flash.user_name, e)}
                  className="cursor-pointer hover:underline text-sm font-medium"
                >
                  {flash.user_name || "user"}
                </div>

                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 1,
                  }}
                >
                  <div className="relative w-full h-full">
                    {/* Use Next.js Image component with proper error handling */}
                    <Image
                      src={getImageSource(flash)}
                      alt={flash.video_name || "Flash Video"}
                      fill
                      sizes="(max-width: 480px) 120px, (max-width: 640px) 140px, (max-width: 768px) 180px, 200px"
                      className="object-cover"
                      {...(index < 2 ? { priority: true } : { loading: 'lazy' })} // Priority for first two, lazy loading for rest
                      unoptimized={!flash.video_thumbnail} // Skip optimization for fallback images
                      placeholder="blur" // Show blur placeholder while loading
                      blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==" // Base64 encoded SVG loading animation
                      onError={(e) => {
                        const imgElement = e.target as HTMLImageElement;
                        if (imgElement) {
                          imgElement.src = '/pics/placeholder.svg';
                        }
                      }}
                    />
                  </div>
                </div>

                <div
                  style={{
                    position: "absolute",
                    bottom: "10px",
                    left: "10px",
                    right: "10px",
                    zIndex: 2,
                    color: "white",
                  }}
                >
                  <div style={{ fontSize: "14px", fontWeight: 500 }}>
                    {flash.video_name}
                  </div>
                  <div style={{ fontSize: "12px" }}>
                    {flash.video_views ? `${(flash.video_views / 1000).toFixed(1)}K views` : ''}
                    {flash.video_likes ? ` • ${(flash.video_likes / 1000).toFixed(1)}K likes` : ''}
                  </div>
                </div>
              </div>
            ))}

            {/* Load more trigger element - this is what IntersectionObserver watches */}
            {hasMore && (
              <div
                ref={loadMoreTriggerRef}
                className="flex-shrink-0 w-10 h-full opacity-0"
                style={{
                  position: 'relative',
                  // Add debug outline in development
                  outline: process.env.NODE_ENV === 'development' ? '1px dashed rgba(255, 0, 0, 0.3)' : 'none'
                }}
                aria-hidden="true"
                data-testid="flashes-load-more-trigger"
              />
            )}

            {/* Loading indicator - only show when loading more */}
            {loadingMore && (
              <div className="flex-shrink-0 flex items-center justify-center min-w-[100px] h-[220px]">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="ml-2 text-sm text-gray-600">Loading more...</span>
              </div>
            )}
          </div>
        </div>
      )}

      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </section>
  );
};

export default FlashesSection;