import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '10';
    const location = searchParams.get('location');
    const excludeId = searchParams.get('exclude');

    console.log(`Fetching glimpses: page=${page}, limit=${limit}, location=${location}, exclude=${excludeId}`);

    // Build query parameters for backend API
    const queryParams = new URLSearchParams({
      page,
      limit
    });

    if (location) {
      queryParams.append('location', location);
    }

    if (excludeId) {
      queryParams.append('exclude', excludeId);
    }

    // Make API call to backend
    const response = await axios.get(
      `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/glimpses?${queryParams.toString()}`,
      {
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      }
    );

    console.log('Glimpses API response received:', response.status);
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('Error fetching glimpses:', error);

    // Detailed error response
    let errorMessage = 'Failed to fetch glimpses';
    let statusCode = 500;

    if (error.response) {
      console.error('Glimpses API response error:', error.response.data);
      statusCode = error.response.status;
      errorMessage = `Server error: ${error.response.data?.error || error.response.data?.message || error.message}`;
    } else if (error.request) {
      console.error('Glimpses API request error:', error.request);
      errorMessage = 'No response received from server';
    } else {
      console.error('Glimpses API error message:', error.message);
      errorMessage = error.message;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
