// services/api/userService.ts
import authService from './authService';
import { UserProfile } from '../../utils/auth';

// Interface for user update data
interface UserUpdateData {
  name?: string;
  email?: string;
  mobile_number?: string | null;
  dob?: string;
  marital_status?: string;
  place?: string;
  profile_image?: string;
}

const userService = {
  // Get authenticated user's details
  getUserDetails: async (): Promise<UserProfile> => {
    try {
      const token = authService.getToken();

      if (!token) {
        throw new Error('Not authenticated');
      }

      // Use the direct API endpoint instead of the Next.js API route
      const response = await fetch('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get user details');
      }

      console.log('User details from API:', data);

      // Store the avatar in localStorage for quick access
      if (data.user_avatar) {
        localStorage.setItem('user_avatar', data.user_avatar);
      }

      return data;
    } catch (error) {
      console.error('Error getting user details:', error);
      throw error;
    }
  },

  // Update user profile
  updateProfile: async (updateData: UserUpdateData): Promise<UserProfile> => {
    try {
      const token = authService.getToken();

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/user', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update profile');
      }

      return data.user;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  },

  // Get user events or weddings
  getEvents: async (): Promise<any[]> => {
    try {
      const token = authService.getToken();

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/user/events', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get events');
      }

      return data.events;
    } catch (error) {
      console.error('Error getting events:', error);
      throw error;
    }
  },

  // Change password
  changePassword: async (oldPassword: string, newPassword: string): Promise<any> => {
    try {
      const token = authService.getToken();

      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ oldPassword, newPassword }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to change password');
      }

      return data;
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  },

  // Request password reset
  requestPasswordReset: async (email: string): Promise<any> => {
    try {
      const response = await fetch('/api/reset-password/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to request password reset');
      }

      return data;
    } catch (error) {
      console.error('Error requesting password reset:', error);
      throw error;
    }
  },

  // Reset password with token
  resetPassword: async (token: string, newPassword: string): Promise<any> => {
    try {
      const response = await fetch('/api/reset-password/confirm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, newPassword }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to reset password');
      }

      return data;
    } catch (error) {
      console.error('Error resetting password:', error);
      throw error;
    }
  }
};

export default userService;