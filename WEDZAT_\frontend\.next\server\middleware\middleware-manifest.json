{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a1b8b59d._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_321a5c3e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|_vercel|api|.*\\..*|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next|_vercel|api|.*\\..*|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vXEI+55+u50UGkXILCLrp7mHM2CJivIwNmHc0uy//sY=", "__NEXT_PREVIEW_MODE_ID": "627d188ddea1bef3ec38d2cb9a4c4d16", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1374b59e613ea7fc3d17b3d6283690cd5119a95e0518f0a36cf17b8459ac7de7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "943a950c2a470ca9850ae6b31bcb84d9bab562e682973516b8b39e1e5bf81c5e"}}}, "instrumentation": null, "functions": {}}