{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a1b8b59d._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_321a5c3e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|_vercel|api|.*\\..*|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next|_vercel|api|.*\\..*|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vXEI+55+u50UGkXILCLrp7mHM2CJivIwNmHc0uy//sY=", "__NEXT_PREVIEW_MODE_ID": "a1efcf6c50c2b8f837fb1a3e349c7528", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f8819c79d9dd4e39001d0371309a5d2fdfbaaf706392583dbb2c5c5a6c8ba24e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "acb3f468eb4894c305c14e9885ff6ae4863e03d61edc9ef18259ae0c08ff52a3"}}}, "instrumentation": null, "functions": {}}