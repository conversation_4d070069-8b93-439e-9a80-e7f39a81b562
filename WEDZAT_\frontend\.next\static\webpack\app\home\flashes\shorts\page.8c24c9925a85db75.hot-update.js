"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/shorts/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/shorts/page.tsx":
/*!******************************************!*\
  !*** ./app/home/<USER>/shorts/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashShortsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var _components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/FlashVendorDetails */ \"(app-pages-browser)/./components/FlashVendorDetails.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Navigation */ \"(app-pages-browser)/./components/HomeDashboard/Navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Create a Client component that uses useSearchParams\nfunction FlashShortsContent() {\n    var _flashes_currentIndex, _flashes_currentIndex1, _flashes_currentIndex2, _flashes_currentIndex3, _flashes_currentIndex4, _flashes_currentIndex5;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialIndex = parseInt((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"index\")) || \"0\");\n    const [flashes, setFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialIndex);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedFlashes, setLikedFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [leftSidebarExpanded, setLeftSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightSidebarExpanded, setRightSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showVendorDetails, setShowVendorDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [admiringUsers, setAdmiringUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // Track admiring state\n    const [videoLoading, setVideoLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Refs for video elements\n    const videoRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const youtubeTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // User avatar placeholders - using placeholder.svg which exists\n    const userAvatarPlaceholders = [\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>setIsClient(true)\n    }[\"FlashShortsContent.useEffect\"], []);\n    // Load admiring state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!isClient) return;\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                const admiredUsers = JSON.parse(admiredUsersJson);\n                setAdmiringUsers(admiredUsers);\n            } catch (error) {\n                console.error('Error loading admired users from localStorage:', error);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient\n    ]);\n    // Fetch flashes from the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const fetchFlashes = {\n                \"FlashShortsContent.useEffect.fetchFlashes\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Get token from localStorage\n                        const token = localStorage.getItem(\"token\");\n                        if (!token) {\n                            console.warn(\"No authentication token found\");\n                            setError(\"Authentication required\");\n                            return;\n                        }\n                        const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/flashes?page=\".concat(page, \"&limit=10\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.data && response.data.flashes) {\n                            console.log(\"Flashes API response:\", response.data);\n                            if (page === 1) {\n                                setFlashes(response.data.flashes);\n                                // Initialize loading state for all videos\n                                const initialLoadingState = {};\n                                response.data.flashes.forEach({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                        initialLoadingState[flash.video_id] = true;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                setVideoLoading(initialLoadingState);\n                                // Fetch like status for initial flashes\n                                fetchLikeStatus(response.data.flashes.map({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (f)=>f.video_id\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]));\n                            } else {\n                                setFlashes({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>[\n                                            ...prev,\n                                            ...response.data.flashes\n                                        ]\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Initialize loading state for new videos\n                                setVideoLoading({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>{\n                                        const newState = {\n                                            ...prev\n                                        };\n                                        response.data.flashes.forEach({\n                                            \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                                newState[flash.video_id] = true;\n                                            }\n                                        }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                        return newState;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Fetch like status for new flashes\n                                fetchLikeStatus(response.data.flashes.map({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (f)=>f.video_id\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]));\n                            }\n                            setHasMore(response.data.next_page);\n                        } else {\n                            console.warn(\"Unexpected API response format:\", response.data);\n                            setError(\"Failed to load flashes\");\n                        }\n                    } catch (err) {\n                        console.error(\"Error fetching flashes:\", err);\n                        setError(\"Failed to load flashes\");\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.fetchFlashes\"];\n            fetchFlashes();\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        page\n    ]);\n    // Load more flashes when reaching the end\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (currentIndex >= flashes.length - 2 && hasMore && !loading) {\n                setPage({\n                    \"FlashShortsContent.useEffect\": (prevPage)=>prevPage + 1\n                }[\"FlashShortsContent.useEffect\"]);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length,\n        hasMore,\n        loading\n    ]);\n    // Handle video playback when current index changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (flashes.length === 0 || !isClient) return;\n            // Clear any existing YouTube timer\n            if (youtubeTimerRef.current) {\n                clearTimeout(youtubeTimerRef.current);\n                youtubeTimerRef.current = null;\n            }\n            // Pause all videos\n            Object.values(videoRefs.current).forEach({\n                \"FlashShortsContent.useEffect\": (videoEl)=>{\n                    if (videoEl && !videoEl.paused) {\n                        videoEl.pause();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect\"]);\n            const currentFlash = flashes[currentIndex];\n            if (!currentFlash) return;\n            // Handle YouTube videos with timer-based auto-advance\n            if (isYoutubeVideo(currentFlash) && !isPaused[currentFlash.video_id]) {\n                // Set a timer for YouTube videos (assuming average duration of 30 seconds)\n                // In a real implementation, you might want to use YouTube API to get actual duration\n                youtubeTimerRef.current = setTimeout({\n                    \"FlashShortsContent.useEffect\": ()=>{\n                        console.log(\"YouTube video timer ended: \".concat(currentFlash.video_id, \", auto-advancing to next flash\"));\n                        if (!isPaused[currentFlash.video_id]) {\n                            navigateToNext();\n                        }\n                    }\n                }[\"FlashShortsContent.useEffect\"], 30000); // 30 seconds default duration for YouTube videos\n            } else {\n                // Play current video if not manually paused (for non-YouTube videos)\n                const currentVideoId = currentFlash.video_id;\n                const currentVideo = videoRefs.current[currentVideoId];\n                if (currentVideo && !isPaused[currentVideoId]) {\n                    const playPromise = currentVideo.play();\n                    if (playPromise !== undefined) {\n                        playPromise.catch({\n                            \"FlashShortsContent.useEffect\": (error)=>{\n                                console.error(\"Error playing video:\", error);\n                            }\n                        }[\"FlashShortsContent.useEffect\"]);\n                    }\n                }\n            }\n            // Cleanup function\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes,\n        isClient,\n        isPaused\n    ]);\n    // Handle keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"FlashShortsContent.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") {\n                        navigateToPrevious();\n                    } else if (e.key === \"ArrowDown\" || e.key === \"ArrowRight\") {\n                        navigateToNext();\n                    } else if (e.key === \"Escape\") {\n                        router.push(\"/home/<USER>");\n                    } else if (e.key === \" \" || e.key === \"Spacebar\") {\n                        var _flashes_currentIndex;\n                        // Toggle play/pause on spacebar\n                        togglePlayPause((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleKeyDown\"];\n            window.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle touch events for swiping\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            let startY = 0;\n            // let startTime = 0; // Uncomment if needed for timing-based gestures\n            const handleTouchStart = {\n                \"FlashShortsContent.useEffect.handleTouchStart\": (e)=>{\n                    startY = e.touches[0].clientY;\n                // startTime = Date.now(); // Uncomment if needed for timing-based gestures\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchStart\"];\n            const handleTouchEnd = {\n                \"FlashShortsContent.useEffect.handleTouchEnd\": (e)=>{\n                    const deltaY = e.changedTouches[0].clientY - startY;\n                    // const deltaTime = Date.now() - startTime; // Uncomment if needed for timing-based gestures\n                    // Make touch more responsive by reducing the threshold\n                    if (Math.abs(deltaY) > 30) {\n                        if (deltaY > 0) {\n                            // Swipe down - go to previous\n                            navigateToPrevious();\n                        } else {\n                            // Swipe up - go to next\n                            navigateToNext();\n                        }\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchEnd\"];\n            // Add touchmove handler for more responsive scrolling\n            let lastY = 0;\n            let touchMoveThrottle = false;\n            const handleTouchMove = {\n                \"FlashShortsContent.useEffect.handleTouchMove\": (e)=>{\n                    const currentY = e.touches[0].clientY;\n                    // Only process every few pixels of movement to avoid too many updates\n                    if (!touchMoveThrottle && Math.abs(currentY - lastY) > 20) {\n                        lastY = currentY;\n                        touchMoveThrottle = true;\n                        // Schedule reset of throttle\n                        setTimeout({\n                            \"FlashShortsContent.useEffect.handleTouchMove\": ()=>{\n                                touchMoveThrottle = false;\n                            }\n                        }[\"FlashShortsContent.useEffect.handleTouchMove\"], 100);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchMove\"];\n            const container = containerRef.current;\n            container.addEventListener(\"touchstart\", handleTouchStart);\n            container.addEventListener(\"touchmove\", handleTouchMove, {\n                passive: true\n            });\n            container.addEventListener(\"touchend\", handleTouchEnd);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"touchstart\", handleTouchStart);\n                    container.removeEventListener(\"touchmove\", handleTouchMove);\n                    container.removeEventListener(\"touchend\", handleTouchEnd);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle wheel events for touchpad scrolling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            const handleWheel = {\n                \"FlashShortsContent.useEffect.handleWheel\": (e)=>{\n                    // Debounce the wheel event to prevent too many navigations\n                    if (e.deltaY > 50) {\n                        // Scroll down - go to next\n                        navigateToNext();\n                    } else if (e.deltaY < -50) {\n                        // Scroll up - go to previous\n                        navigateToPrevious();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleWheel\"];\n            const container = containerRef.current;\n            container.addEventListener(\"wheel\", handleWheel, {\n                passive: true\n            });\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"wheel\", handleWheel);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    const navigateToNext = ()=>{\n        if (currentIndex < flashes.length - 1) {\n            setCurrentIndex((prevIndex)=>prevIndex + 1);\n        } else {\n            // When reaching the last video, loop back to the first one\n            setCurrentIndex(0);\n        }\n    };\n    const navigateToPrevious = ()=>{\n        if (currentIndex > 0) {\n            setCurrentIndex((prevIndex)=>prevIndex - 1);\n        }\n    };\n    // Auto-advance to next flash when current video ends\n    const handleVideoEnded = (videoId)=>{\n        console.log(\"Video ended: \".concat(videoId, \", auto-advancing to next flash\"));\n        // Only auto-advance if the video wasn't manually paused\n        if (!isPaused[videoId]) {\n            navigateToNext();\n        }\n    };\n    const toggleLike = async (flashId)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedFlashes.has(flashId);\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: flashId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            // Update localStorage to persist like status\n            const likedFlashesData = localStorage.getItem('likedFlashes');\n            const likedFlashesArray = likedFlashesData ? JSON.parse(likedFlashesData) : [];\n            if (isCurrentlyLiked) {\n                // Remove from liked flashes\n                const updatedLikedFlashes = likedFlashesArray.filter((id)=>id !== flashId);\n                localStorage.setItem('likedFlashes', JSON.stringify(updatedLikedFlashes));\n            } else {\n                // Add to liked flashes\n                if (!likedFlashesArray.includes(flashId)) {\n                    likedFlashesArray.push(flashId);\n                    localStorage.setItem('likedFlashes', JSON.stringify(likedFlashesArray));\n                }\n            }\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" flash: \").concat(flashId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedFlashes.has(flashId)) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    // Fetch like status for flashes from localStorage (temporary solution)\n    const fetchLikeStatus = async (flashIds)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token || flashIds.length === 0) return;\n            // Get liked flashes from localStorage as a temporary solution\n            // In a real implementation, this would come from the backend\n            const likedFlashesData = localStorage.getItem('likedFlashes');\n            const likedFlashesArray = likedFlashesData ? JSON.parse(likedFlashesData) : [];\n            // Filter to only include flashes that are currently loaded\n            const currentLikedFlashes = flashIds.filter((id)=>likedFlashesArray.includes(id));\n            console.log('Loaded like status for flashes:', currentLikedFlashes);\n            setLikedFlashes(new Set(currentLikedFlashes));\n        } catch (error) {\n            console.error('Error fetching like status:', error);\n            setLikedFlashes(new Set());\n        }\n    };\n    const togglePlayPause = (videoId)=>{\n        if (!videoId) return;\n        const currentFlash = flashes.find((flash)=>flash.video_id === videoId);\n        if (!currentFlash) return;\n        setIsPaused((prev)=>{\n            const newState = {\n                ...prev\n            };\n            newState[videoId] = !prev[videoId];\n            if (isYoutubeVideo(currentFlash)) {\n                // Handle YouTube video pause/play\n                if (newState[videoId]) {\n                    // Paused - clear the timer\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                } else {\n                    // Resumed - restart the timer\n                    youtubeTimerRef.current = setTimeout(()=>{\n                        console.log(\"YouTube video timer ended: \".concat(videoId, \", auto-advancing to next flash\"));\n                        if (!newState[videoId]) {\n                            navigateToNext();\n                        }\n                    }, 30000); // 30 seconds default duration\n                }\n            } else {\n                // Handle regular video pause/play\n                const videoEl = videoRefs.current[videoId];\n                if (videoEl) {\n                    if (newState[videoId]) {\n                        videoEl.pause();\n                    } else {\n                        videoEl.play().catch((err)=>console.error(\"Error playing video:\", err));\n                    }\n                }\n            }\n            return newState;\n        });\n    };\n    // Extract YouTube video ID from URL\n    const getYoutubeId = (url)=>{\n        if (!url) return \"\";\n        // If it's already just an ID, return it\n        if (url.length < 20 && !url.includes(\"/\")) return url;\n        // Try to extract ID from YouTube URL\n        const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;\n        const match = url.match(regExp);\n        return match && match[2].length === 11 ? match[2] : \"\";\n    };\n    // Get appropriate image source for a flash\n    const getImageSource = (flash)=>{\n        // If we have a thumbnail, use it\n        if (flash.video_thumbnail) {\n            return flash.video_thumbnail;\n        }\n        // If it's a YouTube video, use the YouTube thumbnail\n        if (flash.video_url && flash.video_url.includes(\"youtube\")) {\n            const videoId = getYoutubeId(flash.video_url);\n            if (videoId) {\n                return \"https://img.youtube.com/vi/\".concat(videoId, \"/hqdefault.jpg\");\n            }\n        }\n        // Default fallback - use a local placeholder image\n        return \"/pics/placeholder.svg\";\n    };\n    // Check if the video is from YouTube\n    const isYoutubeVideo = (flash)=>{\n        return typeof flash.video_url === \"string\" && flash.video_url.includes(\"youtube\");\n    };\n    // Format numbers for display (e.g., 1.2K)\n    const formatNumber = (num)=>{\n        if (!num) return \"0\";\n        if (num >= 1000000) {\n            return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        } else if (num >= 1000) {\n            return \"\".concat((num / 1000).toFixed(1), \"K\");\n        }\n        return num.toString();\n    };\n    // Add custom CSS for styling and responsiveness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            // Add a style tag for styling and responsiveness\n            const style = document.createElement(\"style\");\n            style.innerHTML = \"\\n      .shorts-page {\\n        background-color: #f8f8f8;\\n      }\\n\\n      .shorts-container {\\n        background-color: #000;\\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\\n        border-radius: 12px;\\n        overflow: hidden;\\n        position: relative;\\n      }\\n\\n      .shorts-video {\\n        width: 100%;\\n        height: 100%;\\n        object-fit: cover;\\n        background-color: #000;\\n      }\\n\\n      .shorts-controls {\\n        position: absolute;\\n        right: 8px;\\n        bottom: 80px;\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        gap: 16px;\\n        z-index: 20;\\n      }\\n\\n      .shorts-info {\\n        position: absolute;\\n        bottom: 0;\\n        left: 0;\\n        right: 0;\\n        padding: 16px;\\n        background: linear-gradient(transparent, rgba(0,0,0,0.8));\\n        z-index: 10;\\n      }\\n\\n      /* Fixed layout styles for proper centering */\\n      .layout-container {\\n        display: flex;\\n        width: 100%;\\n      }\\n\\n      .left-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .left-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .right-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .right-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .main-content {\\n        flex: 1;\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      /* Mobile responsive styles */\\n      @media (max-width: 768px) {\\n        .shorts-page {\\n          background-color: #000;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n        }\\n\\n        .shorts-container {\\n          width: 100vw !important;\\n          max-width: none !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          max-height: none !important;\\n          border-radius: 0 !important;\\n          border: none !important;\\n          margin: 0 !important;\\n          padding-bottom: 20px !important;\\n        }\\n\\n        .mobile-video-item {\\n          margin-bottom: 10px !important;\\n          border-radius: 8px !important;\\n          overflow: hidden !important;\\n        }\\n\\n        .mobile-nav-buttons {\\n          position: fixed !important;\\n          left: 16px !important;\\n          top: 50% !important;\\n          transform: translateY(-50%) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-nav-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-nav-button:disabled {\\n          background: rgba(255, 255, 255, 0.3) !important;\\n          color: rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-interaction-buttons {\\n          position: fixed !important;\\n          right: 16px !important;\\n          bottom: calc(120px + env(safe-area-inset-bottom, 20px)) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-interaction-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          color: white !important;\\n        }\\n\\n        .mobile-back-button {\\n          position: fixed !important;\\n          top: 20px !important;\\n          left: 16px !important;\\n          z-index: 50 !important;\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-user-info {\\n          position: absolute !important;\\n          top: 20px !important;\\n          left: 80px !important;\\n          right: 16px !important;\\n          z-index: 50 !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 12px !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: space-between !important;\\n        }\\n\\n        .mobile-unlock-vendor {\\n          position: absolute !important;\\n          bottom: calc(20px + env(safe-area-inset-bottom, 10px)) !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n          background: #B31B1E !important;\\n          color: white !important;\\n          border: none !important;\\n          border-radius: 8px !important;\\n          padding: 12px 24px !important;\\n          font-weight: 600 !important;\\n          box-shadow: 0 4px 12px rgba(179, 27, 30, 0.4) !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n        }\\n\\n        .mobile-vendor-details {\\n          position: absolute !important;\\n          bottom: 80px !important;\\n          left: 16px !important;\\n          right: 16px !important;\\n          z-index: 60 !important;\\n          background: rgba(255, 255, 255, 0.95) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 16px !important;\\n          max-height: 50vh !important;\\n          overflow-y: auto !important;\\n          color: black !important;\\n        }\\n\\n        .mobile-progress-indicator {\\n          position: fixed !important;\\n          top: 80px !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n        }\\n\\n        /* Hide desktop navigation buttons on mobile */\\n        .desktop-nav-buttons {\\n          display: none !important;\\n        }\\n\\n        .desktop-interaction-buttons {\\n          display: none !important;\\n        }\\n\\n        /* Ensure main content takes full space on mobile */\\n        .main-content-mobile {\\n          padding: 0 !important;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n          margin: 0 !important;\\n          width: 100vw !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          position: relative !important;\\n        }\\n      }\\n\\n      /* Desktop styles */\\n      @media (min-width: 769px) {\\n        .mobile-nav-buttons,\\n        .mobile-interaction-buttons,\\n        .mobile-back-button,\\n        .mobile-user-info,\\n        .mobile-unlock-vendor,\\n        .mobile-progress-indicator {\\n          display: none !important;\\n        }\\n      }\\n    \";\n            document.head.appendChild(style);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    document.head.removeChild(style);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"opacity-0\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 806,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen w-full shorts-page\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.TopNavigation, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                    lineNumber: 813,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 812,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full h-[calc(100vh-80px)] md:mt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-sidebar \".concat(leftSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.SideNavigation, {\n                            expanded: leftSidebarExpanded,\n                            onExpand: ()=>setLeftSidebarExpanded(true),\n                            onCollapse: ()=>setLeftSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 818,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center px-4 md:px-4 px-0 relative main-content-mobile\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"absolute top-4 left-4 z-50 bg-white rounded-full p-3 text-black shadow-lg hover:bg-gray-200 transition-colors flex items-center justify-center hidden md:flex\",\n                                style: {\n                                    width: \"48px\",\n                                    height: \"48px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 833,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"mobile-back-button md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center w-full md:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-nav-buttons flex flex-col items-center justify-center space-y-4 mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === 0 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === flashes.length - 1 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-nav-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: containerRef,\n                                        className: \"shorts-container relative w-full md:w-[400px] h-full md:h-[min(calc(100vh-100px),89vh)]\",\n                                        style: {\n                                            // Desktop styles\n                                            ... true && window.innerWidth >= 768 ? {\n                                                width: \"400px\",\n                                                height: \"min(calc(100vh - 100px), 89vh)\",\n                                                margin: \"0 auto\",\n                                                border: \"2px solid #B31B1E\",\n                                                borderRadius: \"12px\",\n                                                overflow: \"hidden\"\n                                            } : {\n                                                // Mobile styles - full screen with safe area\n                                                width: \"100vw\",\n                                                height: \"calc(100vh - env(safe-area-inset-bottom, 20px))\",\n                                                margin: \"0\",\n                                                border: \"none\",\n                                                borderRadius: \"0\",\n                                                overflow: \"hidden\",\n                                                paddingBottom: \"env(safe-area-inset-bottom, 20px)\"\n                                            }\n                                        },\n                                        children: [\n                                            loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                                children: \"Loading flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 15\n                                            }, this),\n                                            error && !loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-red-500\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 15\n                                            }, this),\n                                            flashes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full w-full transition-transform duration-300 ease-out\",\n                                                style: {\n                                                    transform: \"translateY(-\".concat(currentIndex * 102, \"%)\")\n                                                },\n                                                children: flashes.map((flash, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full w-full flex items-center justify-center relative bg-black \".concat( true && window.innerWidth < 768 ? 'mobile-video-item' : ''),\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(index * 102, \"%\"),\n                                                            left: 0,\n                                                            right: 0,\n                                                            bottom: 0,\n                                                            overflow: \"hidden\",\n                                                            borderRadius: \"8px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-full w-full relative overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 overflow-hidden\",\n                                                                    children: isYoutubeVideo(flash) ? // YouTube iframe for YouTube videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 976,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 977,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 975,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 974,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                                                src: \"https://www.youtube.com/embed/\".concat(getYoutubeId(flash.video_url), \"?autoplay=1&controls=0&rel=0&showinfo=0&mute=0\"),\n                                                                                title: flash.video_name,\n                                                                                className: \"shorts-video\",\n                                                                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                                                                allowFullScreen: true,\n                                                                                onLoad: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 981,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 971,\n                                                                        columnNumber: 27\n                                                                    }, this) : // Video player for Cloudfront videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full overflow-hidden\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1001,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1002,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1000,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 999,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                ref: (el)=>{\n                                                                                    videoRefs.current[flash.video_id] = el;\n                                                                                },\n                                                                                src: flash.video_url,\n                                                                                className: \"shorts-video\",\n                                                                                playsInline: true,\n                                                                                muted: false,\n                                                                                controls: false,\n                                                                                poster: getImageSource(flash),\n                                                                                onLoadStart: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: true\n                                                                                        }));\n                                                                                },\n                                                                                onCanPlay: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onError: ()=>{\n                                                                                    console.error(\"Failed to load video: \".concat(flash.video_name));\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onEnded: ()=>handleVideoEnded(flash.video_id)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1006,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    togglePlayPause(flash.video_id);\n                                                                                },\n                                                                                className: \"absolute inset-0 w-full h-full flex items-center justify-center z-10 group\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(isPaused[flash.video_id] ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\", \" transition-opacity duration-200 bg-black/40 rounded-full p-4 shadow-lg\"),\n                                                                                    children: isPaused[flash.video_id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"white\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                                            points: \"5 3 19 12 5 21 5 3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1055,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1044,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"6\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1069,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"14\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1075,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1058,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1036,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1029,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 968,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 left-4 right-4 z-20 flex items-center bg-black/20 backdrop-blur-sm rounded-lg p-2 hidden md:flex\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1093,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1104,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1105,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1103,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1169,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1110,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-user-info md:hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1177,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1188,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1189,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1187,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1176,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1253,\n                                                                                columnNumber: 67\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1194,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-8 left-0 right-0 flex justify-center z-10 hidden md:flex\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-[#B31B1E] text-white text-sm font-medium px-4 py-2 rounded-md flex items-center\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowVendorDetails((prev)=>({\n                                                                                ...prev,\n                                                                                [flash.video_id]: !prev[flash.video_id]\n                                                                            }));\n                                                                    },\n                                                                    children: [\n                                                                        \"Unlock Vendor\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4 ml-1\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1272,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1271,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1260,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1259,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mobile-unlock-vendor md:hidden\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    setShowVendorDetails((prev)=>({\n                                                                            ...prev,\n                                                                            [flash.video_id]: !prev[flash.video_id]\n                                                                        }));\n                                                                },\n                                                                children: [\n                                                                    \"Unlock Vendor\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        className: \"h-4 w-4 ml-1\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1291,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1290,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1279,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-20 left-4 right-4 p-4 bg-white/90 rounded-lg text-black max-h-[40%] overflow-y-auto z-30 hidden md:block\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1299,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1298,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === currentIndex && showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-vendor-details md:hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1309,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1308,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(flash.video_id, \"-\").concat(index), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 15\n                                            }, this),\n                                            loading && page > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-20 left-0 right-0 text-center text-white bg-black/50 py-2 mx-auto w-48 rounded-full backdrop-blur-sm z-20\",\n                                                children: \"Loading more flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-14 left-0 right-0 px-4 z-20 hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1331,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1339,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1329,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1328,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mobile-progress-indicator md:hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1356,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1346,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1345,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-interaction-buttons flex flex-col items-center justify-center space-y-6 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100 mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1368,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1369,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1366,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1365,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 \".concat(likedFlashes.has(((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id) || '') ? 'bg-red-100 hover:bg-red-200' : 'bg-black/20 hover:bg-black/40'),\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        console.log('Desktop like button clicked for flash:', currentFlash.video_id);\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                title: likedFlashes.has(((_flashes_currentIndex1 = flashes[currentIndex]) === null || _flashes_currentIndex1 === void 0 ? void 0 : _flashes_currentIndex1.video_id) || '') ? 'Unlike' : 'Like',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex2 = flashes[currentIndex]) === null || _flashes_currentIndex2 === void 0 ? void 0 : _flashes_currentIndex2.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex3 = flashes[currentIndex]) === null || _flashes_currentIndex3 === void 0 ? void 0 : _flashes_currentIndex3.video_id) || '') ? \"#B31B1E\" : \"white\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1390,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1374,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1397,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1396,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1395,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1404,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1405,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-interaction-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash === null || currentFlash === void 0 ? void 0 : currentFlash.user_id) {\n                                                        // Navigate to user profile - you can implement this navigation\n                                                        console.log('Navigate to profile:', currentFlash.user_id);\n                                                    // router.push(`/profile/${currentFlash.user_id}`);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1425,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"7\",\n                                                            r: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1426,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1424,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1413,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex4 = flashes[currentIndex]) === null || _flashes_currentIndex4 === void 0 ? void 0 : _flashes_currentIndex4.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex5 = flashes[currentIndex]) === null || _flashes_currentIndex5 === void 0 ? void 0 : _flashes_currentIndex5.video_id) || '') ? \"#B31B1E\" : \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1441,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1440,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1431,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1448,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1447,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1446,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1455,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1456,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1454,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1453,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1463,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1464,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1465,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1462,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1461,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1411,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 831,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-sidebar \".concat(rightSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.RightSidebar, {\n                            expanded: rightSidebarExpanded,\n                            onExpand: ()=>setRightSidebarExpanded(true),\n                            onCollapse: ()=>setRightSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 1478,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 1473,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 816,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 810,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashShortsContent, \"aeQpwdVKjXsnvNtC+OoDi2XkRdk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = FlashShortsContent;\n// Loading fallback component\nfunction FlashShortsLoading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-screen w-full bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-white text-xl\",\n            children: \"Loading flashes...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1493,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1492,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FlashShortsLoading;\n// Main page component with Suspense\nfunction FlashShortsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsLoading, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1501,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1502,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1501,\n        columnNumber: 5\n    }, this);\n}\n_c2 = FlashShortsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FlashShortsContent\");\n$RefreshReg$(_c1, \"FlashShortsLoading\");\n$RefreshReg$(_c2, \"FlashShortsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/shorts/page.tsx\n"));

/***/ })

});