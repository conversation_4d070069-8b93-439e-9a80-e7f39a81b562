/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.0.12 | MIT License | https://tailwindcss.com */
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: var(--font-geist-sans);
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: var(--font-geist-mono);
    --color-red-50: oklch(0.971 0.013 17.38);
    --color-red-100: oklch(0.936 0.032 17.717);
    --color-red-200: oklch(0.885 0.062 18.334);
    --color-red-300: oklch(0.808 0.114 19.571);
    --color-red-400: oklch(0.704 0.191 22.216);
    --color-red-500: oklch(0.637 0.237 25.331);
    --color-red-600: oklch(0.577 0.245 27.325);
    --color-red-700: oklch(0.505 0.213 27.518);
    --color-red-800: oklch(0.444 0.177 26.899);
    --color-orange-100: oklch(0.954 0.038 75.164);
    --color-orange-600: oklch(0.646 0.222 41.116);
    --color-amber-500: oklch(0.769 0.188 70.08);
    --color-yellow-50: oklch(0.987 0.026 102.212);
    --color-yellow-100: oklch(0.973 0.071 103.193);
    --color-yellow-200: oklch(0.945 0.129 101.54);
    --color-yellow-400: oklch(0.852 0.199 91.936);
    --color-yellow-500: oklch(0.795 0.184 86.047);
    --color-yellow-600: oklch(0.681 0.162 75.834);
    --color-yellow-700: oklch(0.554 0.135 66.442);
    --color-green-50: oklch(0.982 0.018 155.826);
    --color-green-100: oklch(0.962 0.044 156.743);
    --color-green-200: oklch(0.925 0.084 155.995);
    --color-green-400: oklch(0.792 0.209 151.711);
    --color-green-500: oklch(0.723 0.219 149.579);
    --color-green-600: oklch(0.627 0.194 149.214);
    --color-green-700: oklch(0.527 0.154 150.069);
    --color-green-800: oklch(0.448 0.119 151.328);
    --color-cyan-700: oklch(0.52 0.105 223.128);
    --color-cyan-800: oklch(0.45 0.085 224.283);
    --color-blue-100: oklch(0.932 0.032 255.585);
    --color-blue-200: oklch(0.882 0.059 254.128);
    --color-blue-400: oklch(0.707 0.165 254.624);
    --color-blue-500: oklch(0.623 0.214 259.815);
    --color-blue-600: oklch(0.546 0.245 262.881);
    --color-blue-700: oklch(0.488 0.243 264.376);
    --color-blue-800: oklch(0.424 0.199 265.638);
    --color-indigo-600: oklch(0.511 0.262 276.966);
    --color-indigo-900: oklch(0.359 0.144 278.697);
    --color-purple-100: oklch(0.946 0.033 307.174);
    --color-purple-200: oklch(0.902 0.063 306.703);
    --color-purple-400: oklch(0.714 0.203 305.504);
    --color-purple-500: oklch(0.627 0.265 303.9);
    --color-purple-600: oklch(0.558 0.288 302.321);
    --color-purple-700: oklch(0.496 0.265 301.924);
    --color-purple-800: oklch(0.438 0.218 303.724);
    --color-pink-500: oklch(0.656 0.241 354.308);
    --color-gray-50: oklch(0.985 0.002 247.839);
    --color-gray-100: oklch(0.967 0.003 264.542);
    --color-gray-200: oklch(0.928 0.006 264.531);
    --color-gray-300: oklch(0.872 0.01 258.338);
    --color-gray-400: oklch(0.707 0.022 261.325);
    --color-gray-500: oklch(0.551 0.027 264.364);
    --color-gray-600: oklch(0.446 0.03 256.802);
    --color-gray-700: oklch(0.373 0.034 259.733);
    --color-gray-800: oklch(0.278 0.033 256.848);
    --color-gray-900: oklch(0.21 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --leading-tight: 1.25;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --aspect-video: 16 / 9;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-font-feature-settings: var(--font-sans--font-feature-settings);
    --default-font-variation-settings: var(
      --font-sans--font-variation-settings
    );
    --default-mono-font-family: var(--font-mono);
    --default-mono-font-feature-settings: var(
      --font-mono--font-feature-settings
    );
    --default-mono-font-variation-settings: var(
      --font-mono--font-variation-settings
    );
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var( --default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" );
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var( --default-font-variation-settings, normal );
    -webkit-tap-highlight-color: transparent;
  }
  body {
    line-height: inherit;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var( --default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace );
    font-feature-settings: var( --default-mono-font-feature-settings, normal );
    font-variation-settings: var( --default-mono-font-variation-settings, normal );
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
    color: color-mix(in oklab, currentColor 50%, transparent);
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-auto {
    pointer-events: auto;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-top-1 {
    top: calc(var(--spacing) * -1);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-2\.5 {
    top: calc(var(--spacing) * 2.5);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-3\.5 {
    top: calc(var(--spacing) * 3.5);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-14 {
    top: calc(var(--spacing) * 14);
  }
  .top-16 {
    top: calc(var(--spacing) * 16);
  }
  .top-20 {
    top: calc(var(--spacing) * 20);
  }
  .top-24 {
    top: calc(var(--spacing) * 24);
  }
  .top-\[10px\] {
    top: 10px;
  }
  .top-\[80px\] {
    top: 80px;
  }
  .top-full {
    top: 100%;
  }
  .-right-1 {
    right: calc(var(--spacing) * -1);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .right-1\/3 {
    right: calc(1/3 * 100%);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-5 {
    right: calc(var(--spacing) * 5);
  }
  .right-12 {
    right: calc(var(--spacing) * 12);
  }
  .right-\[10px\] {
    right: 10px;
  }
  .-bottom-5 {
    bottom: calc(var(--spacing) * -5);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }
  .bottom-2 {
    bottom: calc(var(--spacing) * 2);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }
  .bottom-14 {
    bottom: calc(var(--spacing) * 14);
  }
  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }
  .bottom-\[10px\] {
    bottom: 10px;
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .left-3 {
    left: calc(var(--spacing) * 3);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-24 {
    left: calc(var(--spacing) * 24);
  }
  .left-52 {
    left: calc(var(--spacing) * 52);
  }
  .left-\[10px\] {
    left: 10px;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-30 {
    z-index: 30;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[1\] {
    z-index: 1;
  }
  .z-\[2\] {
    z-index: 2;
  }
  .z-\[9999\] {
    z-index: 9999;
  }
  .col-span-2 {
    grid-column: span 2 / span 2;
  }
  .col-span-full {
    grid-column: 1 / -1;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }
  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .mt-\[80px\] {
    margin-top: 80px;
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }
  .mr-10 {
    margin-right: calc(var(--spacing) * 10);
  }
  .mr-16 {
    margin-right: calc(var(--spacing) * 16);
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .-ml-8 {
    margin-left: calc(var(--spacing) * -8);
  }
  .ml-0 {
    margin-left: calc(var(--spacing) * 0);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-1\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-8 {
    margin-left: calc(var(--spacing) * 8);
  }
  .ml-auto {
    margin-left: auto;
  }
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .aspect-\[4\/3\] {
    aspect-ratio: 4/3;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-28 {
    height: calc(var(--spacing) * 28);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-60 {
    height: calc(var(--spacing) * 60);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-80 {
    height: calc(var(--spacing) * 80);
  }
  .h-\[16px\] {
    height: 16px;
  }
  .h-\[34px\] {
    height: 34px;
  }
  .h-\[40px\] {
    height: 40px;
  }
  .h-\[56px\] {
    height: 56px;
  }
  .h-\[70px\] {
    height: 70px;
  }
  .h-\[84px\] {
    height: 84px;
  }
  .h-\[86px\] {
    height: 86px;
  }
  .h-\[100px\] {
    height: 100px;
  }
  .h-\[160px\] {
    height: 160px;
  }
  .h-\[180px\] {
    height: 180px;
  }
  .h-\[200px\] {
    height: 200px;
  }
  .h-\[220px\] {
    height: 220px;
  }
  .h-\[300px\] {
    height: 300px;
  }
  .h-\[308px\] {
    height: 308px;
  }
  .h-\[calc\(100vh-80px\)\] {
    height: calc(100vh - 80px);
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }
  .max-h-80 {
    max-height: calc(var(--spacing) * 80);
  }
  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }
  .max-h-\[40\%\] {
    max-height: 40%;
  }
  .max-h-\[50vh\] {
    max-height: 50vh;
  }
  .max-h-\[85vh\] {
    max-height: 85vh;
  }
  .max-h-\[90vh\] {
    max-height: 90vh;
  }
  .max-h-\[100px\] {
    max-height: 100px;
  }
  .max-h-\[200px\] {
    max-height: 200px;
  }
  .min-h-\[300px\] {
    min-height: 300px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-1\/6 {
    width: calc(1/6 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\/3 {
    width: calc(2/3 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\/4 {
    width: calc(3/4 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-28 {
    width: calc(var(--spacing) * 28);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-\[34px\] {
    width: 34px;
  }
  .w-\[40vw\] {
    width: 40vw;
  }
  .w-\[56px\] {
    width: 56px;
  }
  .w-\[57px\] {
    width: 57px;
  }
  .w-\[64px\] {
    width: 64px;
  }
  .w-\[70px\] {
    width: 70px;
  }
  .w-\[80vw\] {
    width: 80vw;
  }
  .w-\[85\%\] {
    width: 85%;
  }
  .w-\[85vw\] {
    width: 85vw;
  }
  .w-\[89px\] {
    width: 89px;
  }
  .w-\[130px\] {
    width: 130px;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-6xl {
    max-width: var(--container-6xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-20 {
    max-width: calc(var(--spacing) * 20);
  }
  .max-w-\[50\%\] {
    max-width: 50%;
  }
  .max-w-\[95\%\] {
    max-width: 95%;
  }
  .max-w-\[100px\] {
    max-width: 100px;
  }
  .max-w-\[150px\] {
    max-width: 150px;
  }
  .max-w-\[200px\] {
    max-width: 200px;
  }
  .max-w-\[365px\] {
    max-width: 365px;
  }
  .max-w-\[380px\] {
    max-width: 380px;
  }
  .max-w-\[676px\] {
    max-width: 676px;
  }
  .max-w-\[800px\] {
    max-width: 800px;
  }
  .max-w-\[1100px\] {
    max-width: 1100px;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }
  .min-w-\[40vw\] {
    min-width: 40vw;
  }
  .min-w-\[100px\] {
    min-width: 100px;
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .origin-top {
    transform-origin: top;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-95 {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .animate-bounce {
    animation: var(--animate-bounce);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-default {
    cursor: default;
  }
  .cursor-help {
    cursor: help;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-\[10px\] {
    gap: 10px;
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .-space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .-space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-0\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .self-start {
    align-self: flex-start;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-visible {
    overflow: visible;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-\[4px\] {
    border-radius: 4px;
  }
  .rounded-\[10px\] {
    border-radius: 10px;
  }
  .rounded-\[12px\] {
    border-radius: 12px;
  }
  .rounded-\[27px\] {
    border-radius: 27px;
  }
  .rounded-\[28px\] {
    border-radius: 28px;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-t-\[10px\] {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .rounded-t-xl {
    border-top-left-radius: var(--radius-xl);
    border-top-right-radius: var(--radius-xl);
  }
  .rounded-l-md {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  .rounded-tl-2xl {
    border-top-left-radius: var(--radius-2xl);
  }
  .rounded-r-md {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }
  .rounded-tr-2xl {
    border-top-right-radius: var(--radius-2xl);
  }
  .rounded-b-\[10px\] {
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
  }
  .rounded-b-xl {
    border-bottom-right-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-xl);
  }
  .rounded-br-2xl {
    border-bottom-right-radius: var(--radius-2xl);
  }
  .rounded-bl-2xl {
    border-bottom-left-radius: var(--radius-2xl);
  }
  .rounded-bl-md {
    border-bottom-left-radius: var(--radius-md);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-t-0 {
    border-top-style: var(--tw-border-style);
    border-top-width: 0px;
  }
  .border-t-2 {
    border-top-style: var(--tw-border-style);
    border-top-width: 2px;
  }
  .border-t-4 {
    border-top-style: var(--tw-border-style);
    border-top-width: 4px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .border-\[\#4D0C0D\] {
    border-color: #4D0C0D;
  }
  .border-\[\#B31B1E\] {
    border-color: #B31B1E;
  }
  .border-\[\#D9D9D9\] {
    border-color: #D9D9D9;
  }
  .border-\[\#DBDBDB\] {
    border-color: #DBDBDB;
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-green-400 {
    border-color: var(--color-green-400);
  }
  .border-green-500 {
    border-color: var(--color-green-500);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-400 {
    border-color: var(--color-red-400);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-red-600 {
    border-color: var(--color-red-600);
  }
  .border-red-700 {
    border-color: var(--color-red-700);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-yellow-400 {
    border-color: var(--color-yellow-400);
  }
  .border-t-red-500 {
    border-top-color: var(--color-red-500);
  }
  .border-t-red-600 {
    border-top-color: var(--color-red-600);
  }
  .border-t-transparent {
    border-top-color: transparent;
  }
  .bg-\[\#B31B1E\] {
    background-color: #B31B1E;
  }
  .bg-\[\#FFF3D8\] {
    background-color: #FFF3D8;
  }
  .bg-\[\#FFF7E8\] {
    background-color: #FFF7E8;
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/20 {
    background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
  }
  .bg-black\/30 {
    background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
  }
  .bg-black\/40 {
    background-color: color-mix(in oklab, var(--color-black) 40%, transparent);
  }
  .bg-black\/50 {
    background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
  }
  .bg-black\/70 {
    background-color: color-mix(in oklab, var(--color-black) 70%, transparent);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-700 {
    background-color: var(--color-blue-700);
  }
  .bg-cyan-700 {
    background-color: var(--color-cyan-700);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-green-700 {
    background-color: var(--color-green-700);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-purple-200 {
    background-color: var(--color-purple-200);
  }
  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }
  .bg-purple-700 {
    background-color: var(--color-purple-700);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-red-700 {
    background-color: var(--color-red-700);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/40 {
    background-color: color-mix(in oklab, var(--color-white) 40%, transparent);
  }
  .bg-white\/80 {
    background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
  }
  .bg-white\/90 {
    background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-yellow-200 {
    background-color: var(--color-yellow-200);
  }
  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-tr {
    --tw-gradient-position: to top right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-\[\#CCAB75\] {
    --tw-gradient-from: #CCAB75;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-\[\#CCAB76\] {
    --tw-gradient-from: #CCAB76;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-black\/50 {
    --tw-gradient-from: color-mix(in oklab, var(--color-black) 50%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-black\/70 {
    --tw-gradient-from: color-mix(in oklab, var(--color-black) 70%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-black\/80 {
    --tw-gradient-from: color-mix(in oklab, var(--color-black) 80%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-400 {
    --tw-gradient-from: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-red-600 {
    --tw-gradient-from: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-100 {
    --tw-gradient-from: var(--color-yellow-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-500 {
    --tw-gradient-from: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-red-500 {
    --tw-gradient-via: var(--color-red-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .to-\[\#000000\] {
    --tw-gradient-to: #000000;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#66563B\] {
    --tw-gradient-to: #66563B;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#CCAB75\] {
    --tw-gradient-to: #CCAB75;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-100 {
    --tw-gradient-to: var(--color-orange-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-500 {
    --tw-gradient-to: var(--color-pink-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-800 {
    --tw-gradient-to: var(--color-red-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-clip-text {
    background-clip: text;
  }
  .bg-center {
    background-position: center;
  }
  .object-contain {
    object-fit: contain;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-10 {
    padding: calc(var(--spacing) * 10);
  }
  .p-\[1px\] {
    padding: 1px;
  }
  .p-\[5px\] {
    padding: 5px;
  }
  .p-\[6px\] {
    padding: 6px;
  }
  .p-\[10px\] {
    padding: 10px;
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }
  .pt-\[7px\] {
    padding-top: 7px;
  }
  .pr-0 {
    padding-right: calc(var(--spacing) * 0);
  }
  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pr-12 {
    padding-right: calc(var(--spacing) * 12);
  }
  .pr-20 {
    padding-right: calc(var(--spacing) * 20);
  }
  .pr-\[50px\] {
    padding-right: 50px;
  }
  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .pb-\[10px\] {
    padding-bottom: 10px;
  }
  .pb-\[15px\] {
    padding-bottom: 15px;
  }
  .pl-0 {
    padding-left: calc(var(--spacing) * 0);
  }
  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }
  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }
  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-middle {
    vertical-align: middle;
  }
  .font-mono {
    font-family: var(--font-geist-mono);
  }
  .font-serif {
    font-family: var(--font-serif);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-\[8px\] {
    font-size: 8px;
  }
  .text-\[10px\] {
    font-size: 10px;
  }
  .text-\[13px\] {
    font-size: 13px;
  }
  .text-\[16px\] {
    font-size: 16px;
  }
  .text-\[20px\] {
    font-size: 20px;
  }
  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }
  .leading-\[18px\] {
    --tw-leading: 18px;
    line-height: 18px;
  }
  .leading-\[100\%\] {
    --tw-leading: 100%;
    line-height: 100%;
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-\[-0\.03em\] {
    --tw-tracking: -0.03em;
    letter-spacing: -0.03em;
  }
  .tracking-\[0\%\] {
    --tw-tracking: 0%;
    letter-spacing: 0%;
  }
  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }
  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre-line {
    white-space: pre-line;
  }
  .text-\[\#000000\] {
    color: #000000;
  }
  .text-\[\#6A39A4\] {
    color: #6A39A4;
  }
  .text-\[\#646B7D\] {
    color: #646B7D;
  }
  .text-\[\#B31B1E\] {
    color: #B31B1E;
  }
  .text-amber-500 {
    color: var(--color-amber-500);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-600 {
    color: var(--color-indigo-600);
  }
  .text-orange-600 {
    color: var(--color-orange-600);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-purple-700 {
    color: var(--color-purple-700);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-transparent {
    color: transparent;
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-600 {
    color: var(--color-yellow-600);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .placeholder-gray-700 {
    &::placeholder {
      color: var(--color-gray-700);
    }
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-30 {
    opacity: 30%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-80 {
    opacity: 80%;
  }
  .opacity-90 {
    opacity: 90%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-sm {
    --tw-blur: blur(var(--blur-sm));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow-md {
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-md));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-brightness-30 {
    --tw-backdrop-brightness: brightness(30%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-150 {
    --tw-duration: 150ms;
    transition-duration: 150ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .group-hover\:block {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        display: block;
      }
    }
  }
  .group-hover\:rotate-90 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        rotate: 90deg;
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .placeholder\:text-xs {
    &::placeholder {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .placeholder\:text-gray-500 {
    &::placeholder {
      color: var(--color-gray-500);
    }
  }
  .last\:border-b-0 {
    &:last-child {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .hover\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:scale-110 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 110%;
        --tw-scale-y: 110%;
        --tw-scale-z: 110%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:scale-\[0\.99\] {
    &:hover {
      @media (hover: hover) {
        scale: 0.99;
      }
    }
  }
  .hover\:bg-\[\#f8f1e4\] {
    &:hover {
      @media (hover: hover) {
        background-color: #f8f1e4;
      }
    }
  }
  .hover\:bg-black\/30 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
      }
    }
  }
  .hover\:bg-black\/40 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, var(--color-black) 40%, transparent);
      }
    }
  }
  .hover\:bg-blue-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-600);
      }
    }
  }
  .hover\:bg-blue-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-800);
      }
    }
  }
  .hover\:bg-cyan-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-cyan-800);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-300 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-300);
      }
    }
  }
  .hover\:bg-gray-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-800);
      }
    }
  }
  .hover\:bg-green-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-600);
      }
    }
  }
  .hover\:bg-green-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-700);
      }
    }
  }
  .hover\:bg-green-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-800);
      }
    }
  }
  .hover\:bg-purple-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-700);
      }
    }
  }
  .hover\:bg-purple-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-800);
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50);
      }
    }
  }
  .hover\:bg-red-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-200);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-red-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-800);
      }
    }
  }
  .hover\:bg-white {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .hover\:text-\[\#8a1416\] {
    &:hover {
      @media (hover: hover) {
        color: #8a1416;
      }
    }
  }
  .hover\:text-\[\#B31B1E\] {
    &:hover {
      @media (hover: hover) {
        color: #B31B1E;
      }
    }
  }
  .hover\:text-\[\#B62E2E\] {
    &:hover {
      @media (hover: hover) {
        color: #B62E2E;
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-indigo-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-indigo-900);
      }
    }
  }
  .hover\:text-purple-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-purple-800);
      }
    }
  }
  .hover\:text-red-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-500);
      }
    }
  }
  .hover\:text-red-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-600);
      }
    }
  }
  .hover\:text-red-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-700);
      }
    }
  }
  .hover\:text-red-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-800);
      }
    }
  }
  .hover\:text-yellow-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-yellow-600);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:opacity-90 {
    &:hover {
      @media (hover: hover) {
        opacity: 90%;
      }
    }
  }
  .hover\:opacity-100 {
    &:hover {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:ring-2 {
    &:hover {
      @media (hover: hover) {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:ring-\[\#B31B1E\] {
    &:hover {
      @media (hover: hover) {
        --tw-ring-color: #B31B1E;
      }
    }
  }
  .hover\:brightness-105 {
    &:hover {
      @media (hover: hover) {
        --tw-brightness: brightness(105%);
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
      }
    }
  }
  .focus\:border-\[\#B31B1E\] {
    &:focus {
      border-color: #B31B1E;
    }
  }
  .focus\:border-red-500 {
    &:focus {
      border-color: var(--color-red-500);
    }
  }
  .focus\:border-transparent {
    &:focus {
      border-color: transparent;
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-\[\#B31B1E\] {
    &:focus {
      --tw-ring-color: #B31B1E;
    }
  }
  .focus\:ring-\[\#B62E2E\] {
    &:focus {
      --tw-ring-color: #B62E2E;
    }
  }
  .focus\:ring-blue-400 {
    &:focus {
      --tw-ring-color: var(--color-blue-400);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:ring-green-500 {
    &:focus {
      --tw-ring-color: var(--color-green-500);
    }
  }
  .focus\:ring-purple-500 {
    &:focus {
      --tw-ring-color: var(--color-purple-500);
    }
  }
  .focus\:ring-red-500 {
    &:focus {
      --tw-ring-color: var(--color-red-500);
    }
  }
  .focus\:ring-red-600 {
    &:focus {
      --tw-ring-color: var(--color-red-600);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .active\:scale-95 {
    &:active {
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .active\:bg-gray-100 {
    &:active {
      background-color: var(--color-gray-100);
    }
  }
  .active\:bg-gray-200 {
    &:active {
      background-color: var(--color-gray-200);
    }
  }
  .active\:bg-gray-900 {
    &:active {
      background-color: var(--color-gray-900);
    }
  }
  .active\:bg-red-800 {
    &:active {
      background-color: var(--color-red-800);
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:bg-gray-100 {
    &:disabled {
      background-color: var(--color-gray-100);
    }
  }
  .disabled\:bg-gray-400 {
    &:disabled {
      background-color: var(--color-gray-400);
    }
  }
  .disabled\:bg-red-300 {
    &:disabled {
      background-color: var(--color-red-300);
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .sm\:top-0 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * 0);
    }
  }
  .sm\:top-2 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * 2);
    }
  }
  .sm\:top-2\.5 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:top-3 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * 3);
    }
  }
  .sm\:top-4 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * 4);
    }
  }
  .sm\:right-0 {
    @media (width >= 40rem) {
      right: calc(var(--spacing) * 0);
    }
  }
  .sm\:right-2 {
    @media (width >= 40rem) {
      right: calc(var(--spacing) * 2);
    }
  }
  .sm\:right-2\.5 {
    @media (width >= 40rem) {
      right: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:right-3 {
    @media (width >= 40rem) {
      right: calc(var(--spacing) * 3);
    }
  }
  .sm\:right-4 {
    @media (width >= 40rem) {
      right: calc(var(--spacing) * 4);
    }
  }
  .sm\:left-3 {
    @media (width >= 40rem) {
      left: calc(var(--spacing) * 3);
    }
  }
  .sm\:mx-2 {
    @media (width >= 40rem) {
      margin-inline: calc(var(--spacing) * 2);
    }
  }
  .sm\:mx-4 {
    @media (width >= 40rem) {
      margin-inline: calc(var(--spacing) * 4);
    }
  }
  .sm\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:mt-4 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 4);
    }
  }
  .sm\:mt-8 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .sm\:mr-2 {
    @media (width >= 40rem) {
      margin-right: calc(var(--spacing) * 2);
    }
  }
  .sm\:mr-3 {
    @media (width >= 40rem) {
      margin-right: calc(var(--spacing) * 3);
    }
  }
  .sm\:mb-0 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .sm\:mb-1 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 1);
    }
  }
  .sm\:mb-2 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 2);
    }
  }
  .sm\:mb-6 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .sm\:ml-1 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 1);
    }
  }
  .sm\:ml-3 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 3);
    }
  }
  .sm\:line-clamp-none {
    @media (width >= 40rem) {
      overflow: visible;
      display: block;
      -webkit-box-orient: horizontal;
      -webkit-line-clamp: unset;
    }
  }
  .sm\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\:inline {
    @media (width >= 40rem) {
      display: inline;
    }
  }
  .sm\:h-3 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 3);
    }
  }
  .sm\:h-4 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 4);
    }
  }
  .sm\:h-5 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 5);
    }
  }
  .sm\:h-6 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 6);
    }
  }
  .sm\:h-8 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 8);
    }
  }
  .sm\:h-10 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 10);
    }
  }
  .sm\:h-12 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 12);
    }
  }
  .sm\:h-16 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 16);
    }
  }
  .sm\:h-18 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 18);
    }
  }
  .sm\:h-24 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 24);
    }
  }
  .sm\:h-40 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 40);
    }
  }
  .sm\:h-\[50px\] {
    @media (width >= 40rem) {
      height: 50px;
    }
  }
  .sm\:h-\[100px\] {
    @media (width >= 40rem) {
      height: 100px;
    }
  }
  .sm\:h-\[180px\] {
    @media (width >= 40rem) {
      height: 180px;
    }
  }
  .sm\:h-\[250px\] {
    @media (width >= 40rem) {
      height: 250px;
    }
  }
  .sm\:h-\[280px\] {
    @media (width >= 40rem) {
      height: 280px;
    }
  }
  .sm\:h-\[400px\] {
    @media (width >= 40rem) {
      height: 400px;
    }
  }
  .sm\:max-h-96 {
    @media (width >= 40rem) {
      max-height: calc(var(--spacing) * 96);
    }
  }
  .sm\:w-1\/3 {
    @media (width >= 40rem) {
      width: calc(1/3 * 100%);
    }
  }
  .sm\:w-1\/4 {
    @media (width >= 40rem) {
      width: calc(1/4 * 100%);
    }
  }
  .sm\:w-2\/5 {
    @media (width >= 40rem) {
      width: calc(2/5 * 100%);
    }
  }
  .sm\:w-3 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 3);
    }
  }
  .sm\:w-4 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 4);
    }
  }
  .sm\:w-5 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 5);
    }
  }
  .sm\:w-6 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 6);
    }
  }
  .sm\:w-8 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 8);
    }
  }
  .sm\:w-10 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 10);
    }
  }
  .sm\:w-12 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 12);
    }
  }
  .sm\:w-24 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 24);
    }
  }
  .sm\:w-32 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 32);
    }
  }
  .sm\:w-40 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 40);
    }
  }
  .sm\:w-48 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 48);
    }
  }
  .sm\:w-80 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 80);
    }
  }
  .sm\:w-\[30vw\] {
    @media (width >= 40rem) {
      width: 30vw;
    }
  }
  .sm\:w-\[40vw\] {
    @media (width >= 40rem) {
      width: 40vw;
    }
  }
  .sm\:w-\[75vw\] {
    @media (width >= 40rem) {
      width: 75vw;
    }
  }
  .sm\:w-\[100px\] {
    @media (width >= 40rem) {
      width: 100px;
    }
  }
  .sm\:w-\[320px\] {
    @media (width >= 40rem) {
      width: 320px;
    }
  }
  .sm\:w-\[400px\] {
    @media (width >= 40rem) {
      width: 400px;
    }
  }
  .sm\:w-auto {
    @media (width >= 40rem) {
      width: auto;
    }
  }
  .sm\:max-w-24 {
    @media (width >= 40rem) {
      max-width: calc(var(--spacing) * 24);
    }
  }
  .sm\:max-w-\[70\%\] {
    @media (width >= 40rem) {
      max-width: 70%;
    }
  }
  .sm\:max-w-\[90\%\] {
    @media (width >= 40rem) {
      max-width: 90%;
    }
  }
  .sm\:max-w-\[200px\] {
    @media (width >= 40rem) {
      max-width: 200px;
    }
  }
  .sm\:max-w-sm {
    @media (width >= 40rem) {
      max-width: var(--container-sm);
    }
  }
  .sm\:min-w-\[30vw\] {
    @media (width >= 40rem) {
      min-width: 30vw;
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\:grid-cols-3 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .sm\:grid-cols-4 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:justify-center {
    @media (width >= 40rem) {
      justify-content: center;
    }
  }
  .sm\:justify-start {
    @media (width >= 40rem) {
      justify-content: flex-start;
    }
  }
  .sm\:gap-2 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 2);
    }
  }
  .sm\:gap-4 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .sm\:gap-6 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .sm\:gap-8 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .sm\:space-y-4 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:space-x-2 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:space-x-3 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:space-x-4 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:space-x-10 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 10) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:p-1\.5 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 1.5);
    }
  }
  .sm\:p-2 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 2);
    }
  }
  .sm\:p-2\.5 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:p-3 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 3);
    }
  }
  .sm\:p-4 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .sm\:p-6 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .sm\:p-\[8px_5px\] {
    @media (width >= 40rem) {
      padding: 8px 5px;
    }
  }
  .sm\:p-\[10px\] {
    @media (width >= 40rem) {
      padding: 10px;
    }
  }
  .sm\:px-0 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .sm\:px-2 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .sm\:px-3 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .sm\:px-4 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .sm\:py-1 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 1);
    }
  }
  .sm\:py-2 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 2);
    }
  }
  .sm\:py-2\.5 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:py-3 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .sm\:py-4 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .sm\:pt-10 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 10);
    }
  }
  .sm\:pr-0 {
    @media (width >= 40rem) {
      padding-right: calc(var(--spacing) * 0);
    }
  }
  .sm\:pr-4 {
    @media (width >= 40rem) {
      padding-right: calc(var(--spacing) * 4);
    }
  }
  .sm\:pr-14 {
    @media (width >= 40rem) {
      padding-right: calc(var(--spacing) * 14);
    }
  }
  .sm\:pr-\[100px\] {
    @media (width >= 40rem) {
      padding-right: 100px;
    }
  }
  .sm\:pb-\[21px\] {
    @media (width >= 40rem) {
      padding-bottom: 21px;
    }
  }
  .sm\:pl-1 {
    @media (width >= 40rem) {
      padding-left: calc(var(--spacing) * 1);
    }
  }
  .sm\:pl-4 {
    @media (width >= 40rem) {
      padding-left: calc(var(--spacing) * 4);
    }
  }
  .sm\:pl-10 {
    @media (width >= 40rem) {
      padding-left: calc(var(--spacing) * 10);
    }
  }
  .sm\:text-base {
    @media (width >= 40rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .sm\:text-lg {
    @media (width >= 40rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .sm\:text-sm {
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .sm\:text-xl {
    @media (width >= 40rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .sm\:text-xs {
    @media (width >= 40rem) {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .sm\:placeholder\:text-sm {
    @media (width >= 40rem) {
      &::placeholder {
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
      }
    }
  }
  .md\:relative {
    @media (width >= 48rem) {
      position: relative;
    }
  }
  .md\:static {
    @media (width >= 48rem) {
      position: static;
    }
  }
  .md\:sticky {
    @media (width >= 48rem) {
      position: sticky;
    }
  }
  .md\:top-20 {
    @media (width >= 48rem) {
      top: calc(var(--spacing) * 20);
    }
  }
  .md\:z-auto {
    @media (width >= 48rem) {
      z-index: auto;
    }
  }
  .md\:mt-20 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 20);
    }
  }
  .md\:mr-8 {
    @media (width >= 48rem) {
      margin-right: calc(var(--spacing) * 8);
    }
  }
  .md\:mb-4 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }
  .md\:ml-12 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 12);
    }
  }
  .md\:ml-20 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 20);
    }
  }
  .md\:ml-24 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 24);
    }
  }
  .md\:ml-48 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 48);
    }
  }
  .md\:ml-60 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 60);
    }
  }
  .md\:ml-64 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 64);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:h-4 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 4);
    }
  }
  .md\:h-5 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 5);
    }
  }
  .md\:h-6 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 6);
    }
  }
  .md\:h-12 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 12);
    }
  }
  .md\:h-14 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 14);
    }
  }
  .md\:h-20 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 20);
    }
  }
  .md\:h-80 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 80);
    }
  }
  .md\:h-\[56px\] {
    @media (width >= 48rem) {
      height: 56px;
    }
  }
  .md\:h-\[200px\] {
    @media (width >= 48rem) {
      height: 200px;
    }
  }
  .md\:h-\[300px\] {
    @media (width >= 48rem) {
      height: 300px;
    }
  }
  .md\:h-\[308px\] {
    @media (width >= 48rem) {
      height: 308px;
    }
  }
  .md\:h-\[450px\] {
    @media (width >= 48rem) {
      height: 450px;
    }
  }
  .md\:h-\[calc\(100vh-80px\)\] {
    @media (width >= 48rem) {
      height: calc(100vh - 80px);
    }
  }
  .md\:h-\[calc\(100vh-140px\)\] {
    @media (width >= 48rem) {
      height: calc(100vh - 140px);
    }
  }
  .md\:h-\[min\(calc\(100vh-100px\)\,89vh\)\] {
    @media (width >= 48rem) {
      height: min(calc(100vh - 100px), 89vh);
    }
  }
  .md\:w-1\/3 {
    @media (width >= 48rem) {
      width: calc(1/3 * 100%);
    }
  }
  .md\:w-1\/4 {
    @media (width >= 48rem) {
      width: calc(1/4 * 100%);
    }
  }
  .md\:w-2\/3 {
    @media (width >= 48rem) {
      width: calc(2/3 * 100%);
    }
  }
  .md\:w-4 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 4);
    }
  }
  .md\:w-5 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 5);
    }
  }
  .md\:w-6 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 6);
    }
  }
  .md\:w-14 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 14);
    }
  }
  .md\:w-20 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 20);
    }
  }
  .md\:w-36 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 36);
    }
  }
  .md\:w-\[22vw\] {
    @media (width >= 48rem) {
      width: 22vw;
    }
  }
  .md\:w-\[30vw\] {
    @media (width >= 48rem) {
      width: 30vw;
    }
  }
  .md\:w-\[65vw\] {
    @media (width >= 48rem) {
      width: 65vw;
    }
  }
  .md\:w-\[320px\] {
    @media (width >= 48rem) {
      width: 320px;
    }
  }
  .md\:w-\[400px\] {
    @media (width >= 48rem) {
      width: 400px;
    }
  }
  .md\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\:w-full {
    @media (width >= 48rem) {
      width: 100%;
    }
  }
  .md\:max-w-32 {
    @media (width >= 48rem) {
      max-width: calc(var(--spacing) * 32);
    }
  }
  .md\:max-w-\[50\%\] {
    @media (width >= 48rem) {
      max-width: 50%;
    }
  }
  .md\:max-w-\[80\%\] {
    @media (width >= 48rem) {
      max-width: 80%;
    }
  }
  .md\:max-w-md {
    @media (width >= 48rem) {
      max-width: var(--container-md);
    }
  }
  .md\:max-w-none {
    @media (width >= 48rem) {
      max-width: none;
    }
  }
  .md\:min-w-\[22vw\] {
    @media (width >= 48rem) {
      min-width: 22vw;
    }
  }
  .md\:flex-shrink-0 {
    @media (width >= 48rem) {
      flex-shrink: 0;
    }
  }
  .md\:flex-grow-0 {
    @media (width >= 48rem) {
      flex-grow: 0;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:gap-6 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .md\:gap-8 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .md\:space-x-3 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:space-x-4 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:bg-transparent {
    @media (width >= 48rem) {
      background-color: transparent;
    }
  }
  .md\:p-2 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 2);
    }
  }
  .md\:p-3\.5 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 3.5);
    }
  }
  .md\:p-4 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .md\:p-6 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:p-8 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .md\:px-4 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .md\:px-5 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .md\:py-3 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .md\:pr-\[150px\] {
    @media (width >= 48rem) {
      padding-right: 150px;
    }
  }
  .md\:pb-4 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 4);
    }
  }
  .md\:text-left {
    @media (width >= 48rem) {
      text-align: left;
    }
  }
  .md\:text-2xl {
    @media (width >= 48rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .md\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .md\:text-7xl {
    @media (width >= 48rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 48rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .lg\:col-span-1 {
    @media (width >= 64rem) {
      grid-column: span 1 / span 1;
    }
  }
  .lg\:col-span-2 {
    @media (width >= 64rem) {
      grid-column: span 2 / span 2;
    }
  }
  .lg\:mr-\[350px\] {
    @media (width >= 64rem) {
      margin-right: 350px;
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:h-13 {
    @media (width >= 64rem) {
      height: calc(var(--spacing) * 13);
    }
  }
  .lg\:h-\[220px\] {
    @media (width >= 64rem) {
      height: 220px;
    }
  }
  .lg\:h-\[350px\] {
    @media (width >= 64rem) {
      height: 350px;
    }
  }
  .lg\:h-\[500px\] {
    @media (width >= 64rem) {
      height: 500px;
    }
  }
  .lg\:w-96 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 96);
    }
  }
  .lg\:w-\[18vw\] {
    @media (width >= 64rem) {
      width: 18vw;
    }
  }
  .lg\:w-\[22vw\] {
    @media (width >= 64rem) {
      width: 22vw;
    }
  }
  .lg\:w-\[55vw\] {
    @media (width >= 64rem) {
      width: 55vw;
    }
  }
  .lg\:w-\[350px\] {
    @media (width >= 64rem) {
      width: 350px;
    }
  }
  .lg\:w-\[400px\] {
    @media (width >= 64rem) {
      width: 400px;
    }
  }
  .lg\:max-w-\[60\%\] {
    @media (width >= 64rem) {
      max-width: 60%;
    }
  }
  .lg\:max-w-xl {
    @media (width >= 64rem) {
      max-width: var(--container-xl);
    }
  }
  .lg\:min-w-\[18vw\] {
    @media (width >= 64rem) {
      min-width: 18vw;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-5 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  .lg\:px-6 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:pr-\[200px\] {
    @media (width >= 64rem) {
      padding-right: 200px;
    }
  }
  .xl\:max-w-\[50\%\] {
    @media (width >= 80rem) {
      max-width: 50%;
    }
  }
  .xl\:grid-cols-5 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  .xl\:grid-cols-6 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }
}
@media (min-width: 480px) {
  .xs\:w-\[140px\] {
    width: 140px;
  }
  .xs\:w-\[260px\] {
    width: 260px;
  }
  .xs\:w-\[350px\] {
    width: 350px;
  }
  .xs\:min-w-\[140px\] {
    min-width: 140px;
  }
  .xs\:h-\[220px\] {
    height: 220px;
  }
  .xs\:h-\[240px\] {
    height: 240px;
  }
  .xs\:h-\[380px\] {
    height: 380px;
  }
  .xs\:gap-3 {
    gap: 0.75rem;
  }
  .xs\:p-\[8px\] {
    padding: 8px;
  }
}
:root {
  --background: #f3f0f0;
  --foreground: #000000;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #000000;
  }
}
body {
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}
* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
*::-webkit-scrollbar {
  display: none;
}
html,
body {
  -webkit-overflow-scrolling: touch;
}
main::-webkit-scrollbar,
main::-webkit-scrollbar-track,
main::-webkit-scrollbar-thumb {
  display: none;
}
main {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

