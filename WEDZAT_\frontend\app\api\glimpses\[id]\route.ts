import { NextResponse } from 'next/server';

// Mock data for glimpses (same as in the main route)
const allGlimpses = Array.from({ length: 40 }, (_, index) => ({
  video_id: `glimpse-${index + 1}`,
  video_name: `Glimpse Video ${index + 1}`,
  video_url: `https://www.youtube.com/watch?v=dQw4w9WgXcQ`,
  video_description: `This is a description for glimpse video ${index + 1}`,
  video_thumbnail: `/pics/placeholder.svg`,
  video_duration: 120 + index * 10,
  video_category: "Wedding",
  created_at: new Date(Date.now() - index * 86400000).toISOString(), // Each video is one day older
  user_name: `User ${Math.floor(index / 5) + 1}`, // Group videos by user
  user_id: `user-${Math.floor(index / 5) + 1}`,
  is_own_content: false,
  video_views: 1000 * (index + 1),
  video_likes: 100 * (index + 1),
  video_comments: 10 * (index + 1)
}));

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  // Get the glimpse ID from the URL
  const resolvedParams = await context.params;
  const glimpseId = resolvedParams.id;

  // Find the glimpse in our mock data
  const glimpse = allGlimpses.find(g => g.video_id === glimpseId);

  // Log for debugging
  console.log(`[Mock API] Fetching glimpse with ID: ${glimpseId}`);

  // If glimpse not found, return 404
  if (!glimpse) {
    return NextResponse.json(
      { error: "Glimpse not found" },
      { status: 404 }
    );
  }

  // Add a delay to simulate network latency (200-500ms)
  await new Promise(resolve => setTimeout(resolve, 20 + Math.random() * 300));

  return NextResponse.json({ glimpse });
}
