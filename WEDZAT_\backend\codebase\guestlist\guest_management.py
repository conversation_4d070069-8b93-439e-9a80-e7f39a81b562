import json
import uuid
import re
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from .utils import get_db_connection, validate_token

def is_valid_uuid(val):
    """Check if a string is a valid UUID"""
    try:
        uuid.UUID(str(val))
        return True
    except ValueError:
        return False

def get_guests(event):
    """Get all guests for a user, with optional filtering by group, attendance status, and search term"""
    # Print the event for debugging
    print(f"get_guests received event: {json.dumps(event, default=str)}")

    # Ensure headers exist
    if 'headers' not in event:
        event['headers'] = {}
        print("Added empty headers to event")

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        print(f"Token validation failed: {json.dumps(error_response, default=str)}")
        return error_response

    print(f"Token validation successful, user_id: {user_id}")

    # Get query parameters safely
    query_params = {}
    if event.get('queryStringParameters'):
        query_params = event.get('queryStringParameters')

    # Extract filter parameters
    group_id = query_params.get('group_id')
    attendance_status = query_params.get('attendance_status')
    search_term = query_params.get('search')

    # Extract pagination parameters
    try:
        limit = int(query_params.get('limit', 100))  # Default to 100 guests per page
        offset = int(query_params.get('offset', 0))  # Default to first page
    except ValueError:
        limit = 100
        offset = 0

    # Validate parameters
    if group_id and not is_valid_uuid(group_id):
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'Invalid group ID format'})
        }

    if attendance_status and attendance_status not in ['attending', 'pending', 'declined']:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'Invalid attendance status. Must be one of: attending, pending, declined'})
        }

    conn = None
    try:
        print("Attempting to establish database connection...")
        conn = get_db_connection()
        print("Database connection established successfully")
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        print("Database cursor created successfully")

        # Base query
        query = '''
            SELECT g.*, gg.name as group_name, mo.name as menu_name
            FROM wedding_guests g
            LEFT JOIN wedding_guest_groups gg ON g.group_id = gg.group_id
            LEFT JOIN wedding_menu_options mo ON g.menu_id = mo.menu_id
            WHERE g.user_id = %s
        '''
        params = [user_id]

        # Add group filter if provided
        if group_id:
            query += " AND g.group_id = %s"
            params.append(group_id)

        # Add attendance status filter if provided
        if attendance_status:
            query += " AND g.attendance_status = %s"
            params.append(attendance_status)

        # Add search filter if provided
        if search_term:
            query += " AND (g.first_name ILIKE %s OR g.last_name ILIKE %s OR g.email ILIKE %s)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])

        # Get total count for pagination
        count_query = f"SELECT COUNT(*) FROM ({query}) AS filtered_guests"
        cursor.execute(count_query, params)
        total_count = cursor.fetchone()['count']

        # Add ordering
        query += " ORDER BY g.last_name, g.first_name"

        # Add pagination
        query += " LIMIT %s OFFSET %s"
        params.extend([limit, offset])

        cursor.execute(query, params)

        guests = cursor.fetchall()

        # Convert date objects to strings for JSON serialization
        serializable_guests = []
        for guest in guests:
            guest_dict = dict(guest)
            for key, value in guest_dict.items():
                if isinstance(value, (datetime, date)):
                    guest_dict[key] = value.isoformat()
            serializable_guests.append(guest_dict)

        # Add pagination metadata
        pagination = {
            'total': total_count,
            'limit': limit,
            'offset': offset,
            'has_more': (offset + limit) < total_count
        }

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({
                'guests': serializable_guests,
                'pagination': pagination
            })
        }
    except Exception as e:
        # Print detailed error for debugging
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in get_guests: {str(e)}\n{error_details}")

        # Check for specific error types
        error_message = str(e)
        if "connection" in error_message.lower():
            print("Database connection error detected")
            error_message = "Database connection error. Please check your database credentials and connection settings."
        elif "permission" in error_message.lower() or "access" in error_message.lower():
            print("Database permission error detected")
            error_message = "Database permission error. Please check your database user permissions."
        elif "table" in error_message.lower() and ("missing" in error_message.lower() or "not exist" in error_message.lower()):
            print("Missing table error detected")
            error_message = "Database table error. The required tables may not exist. Please ensure the wedding_tables.py script has been run."

        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': error_message, 'details': error_details})
        }
    finally:
        if conn:
            conn.close()

def get_guest(event):
    """Get a specific guest's details"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    # Get query parameters safely
    query_params = {}
    if event.get('queryStringParameters'):
        query_params = event.get('queryStringParameters')
    guest_id = query_params.get('guest_id')

    if not guest_id:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'Guest ID is required'})
        }

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        query = '''
            SELECT g.*, gg.name as group_name, mo.name as menu_name
            FROM wedding_guests g
            LEFT JOIN wedding_guest_groups gg ON g.group_id = gg.group_id
            LEFT JOIN wedding_menu_options mo ON g.menu_id = mo.menu_id
            WHERE g.user_id = %s AND g.guest_id = %s
        '''

        cursor.execute(query, (user_id, guest_id))

        guest = cursor.fetchone()

        if not guest:
            return {
                'statusCode': 404,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, GET',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Guest not found'})
            }

        # Convert to a regular Python dict
        guest_dict = dict(guest)

        # Convert date objects to strings
        for key, value in guest_dict.items():
            if isinstance(value, (datetime, date)):
                guest_dict[key] = value.isoformat()

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'guest': guest_dict})
        }
    except Exception as e:
        # Print detailed error for debugging
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in get_guest: {str(e)}\n{error_details}")

        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e), 'details': error_details})
        }
    finally:
        if conn:
            conn.close()

def add_guest(event):
    """Add a new guest"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event['body'])
        first_name = data.get('first_name')
        last_name = data.get('last_name')
        email = data.get('email')
        phone = data.get('phone')
        group_id = data.get('group_id')
        menu_id = data.get('menu_id')
        attendance_status = data.get('attendance_status', 'pending')
        notes = data.get('notes', '')

        # Validate required fields
        if not first_name or not last_name or not group_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'First name, last name, and group ID are required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # First, check if the wedding_guests table has the correct structure
            print("Checking wedding_guests table structure...")
            cursor.execute("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'wedding_guests' AND column_name = 'group_id';
            """)
            has_group_id = cursor.fetchone() is not None

            if not has_group_id:
                print("wedding_guests table is missing the group_id column. Recreating it...")
                # Drop and recreate the table
                cursor.execute("DROP TABLE IF EXISTS wedding_guests CASCADE;")
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS wedding_guests (
                    guest_id UUID PRIMARY KEY,
                    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
                    group_id UUID REFERENCES wedding_guest_groups(group_id) ON DELETE CASCADE,
                    first_name VARCHAR(100) NOT NULL,
                    last_name VARCHAR(100) NOT NULL,
                    email VARCHAR(255),
                    phone VARCHAR(50),
                    menu_id UUID REFERENCES wedding_menu_options(menu_id) ON DELETE SET NULL,
                    attendance_status VARCHAR(20) DEFAULT 'pending' CHECK (attendance_status IN ('attending', 'pending', 'declined')),
                    invitation_sent BOOLEAN DEFAULT FALSE,
                    invitation_sent_date TIMESTAMP,
                    response_date TIMESTAMP,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                ''')
                print("wedding_guests table recreated with correct structure.")
                conn.commit()

            # Now use RealDictCursor for the actual operations
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Verify the group belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_guest_groups
                WHERE group_id = %s AND user_id = %s''',
                (group_id, user_id)
            )

            group = cursor.fetchone()
            if not group:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, POST',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Group not found'})
                }

            # If menu_id is provided, verify it exists
            if menu_id:
                cursor.execute(
                    '''SELECT * FROM wedding_menu_options
                    WHERE menu_id = %s AND (user_id = %s OR user_id IS NULL)''',
                    (menu_id, user_id)
                )

                menu = cursor.fetchone()
                if not menu:
                    return {
                        'statusCode': 404,
                        'headers': {
                            'Access-Control-Allow-Origin': '*',
                            'Access-Control-Allow-Methods': 'OPTIONS, POST',
                            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                        },
                        'body': json.dumps({'error': 'Menu option not found'})
                    }

            guest_id = str(uuid.uuid4())

            cursor.execute(
                '''INSERT INTO wedding_guests
                (guest_id, user_id, group_id, first_name, last_name, email, phone,
                menu_id, attendance_status, notes, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (guest_id, user_id, group_id, first_name, last_name, email, phone,
                menu_id, attendance_status, notes)
            )

            new_guest = cursor.fetchone()

            # Get group name and menu name for the response
            cursor.execute(
                '''SELECT gg.name as group_name, mo.name as menu_name
                FROM wedding_guests g
                LEFT JOIN wedding_guest_groups gg ON g.group_id = gg.group_id
                LEFT JOIN wedding_menu_options mo ON g.menu_id = mo.menu_id
                WHERE g.guest_id = %s''',
                (guest_id,)
            )

            guest_with_names = cursor.fetchone()

            # Combine the data
            guest_dict = dict(new_guest)
            if guest_with_names:
                guest_dict['group_name'] = guest_with_names['group_name']
                guest_dict['menu_name'] = guest_with_names['menu_name']

            # Convert date objects to strings
            for key, value in guest_dict.items():
                if isinstance(value, (datetime, date)):
                    guest_dict[key] = value.isoformat()

            conn.commit()

            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'guest': guest_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def update_guest(event):
    """Update guest details"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event['body'])
        guest_id = data.get('guest_id')
        first_name = data.get('first_name')
        last_name = data.get('last_name')
        email = data.get('email')
        phone = data.get('phone')
        group_id = data.get('group_id')
        menu_id = data.get('menu_id')
        attendance_status = data.get('attendance_status')
        notes = data.get('notes')

        if not guest_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Guest ID is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Check if the guest belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_guests
                WHERE guest_id = %s AND user_id = %s''',
                (guest_id, user_id)
            )

            existing_guest = cursor.fetchone()
            if not existing_guest:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Guest not found'})
                }

            # If group_id is provided, verify it exists
            if group_id:
                cursor.execute(
                    '''SELECT * FROM wedding_guest_groups
                    WHERE group_id = %s AND user_id = %s''',
                    (group_id, user_id)
                )

                group = cursor.fetchone()
                if not group:
                    return {
                        'statusCode': 404,
                        'headers': {
                            'Access-Control-Allow-Origin': '*',
                            'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                        },
                        'body': json.dumps({'error': 'Group not found'})
                    }

            # If menu_id is provided, verify it exists
            if menu_id:
                cursor.execute(
                    '''SELECT * FROM wedding_menu_options
                    WHERE menu_id = %s AND (user_id = %s OR user_id IS NULL)''',
                    (menu_id, user_id)
                )

                menu = cursor.fetchone()
                if not menu:
                    return {
                        'statusCode': 404,
                        'headers': {
                            'Access-Control-Allow-Origin': '*',
                            'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                        },
                        'body': json.dumps({'error': 'Menu option not found'})
                    }

            # Update fields
            cursor.execute(
                '''UPDATE wedding_guests
                SET first_name = COALESCE(%s, first_name),
                    last_name = COALESCE(%s, last_name),
                    email = COALESCE(%s, email),
                    phone = COALESCE(%s, phone),
                    group_id = COALESCE(%s, group_id),
                    menu_id = COALESCE(%s, menu_id),
                    attendance_status = COALESCE(%s, attendance_status),
                    notes = COALESCE(%s, notes),
                    updated_at = NOW()
                WHERE guest_id = %s AND user_id = %s
                RETURNING *''',
                (first_name, last_name, email, phone, group_id, menu_id,
                attendance_status, notes, guest_id, user_id)
            )

            updated_guest = cursor.fetchone()

            # Get group name and menu name for the response
            cursor.execute(
                '''SELECT gg.name as group_name, mo.name as menu_name
                FROM wedding_guests g
                LEFT JOIN wedding_guest_groups gg ON g.group_id = gg.group_id
                LEFT JOIN wedding_menu_options mo ON g.menu_id = mo.menu_id
                WHERE g.guest_id = %s''',
                (guest_id,)
            )

            guest_with_names = cursor.fetchone()

            # Combine the data
            guest_dict = dict(updated_guest)
            if guest_with_names:
                guest_dict['group_name'] = guest_with_names['group_name']
                guest_dict['menu_name'] = guest_with_names['menu_name']

            # Convert date objects to strings
            for key, value in guest_dict.items():
                if isinstance(value, (datetime, date)):
                    guest_dict[key] = value.isoformat()

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'guest': guest_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def delete_guest(event):
    """Delete a guest"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event.get('body', '{}'))
        guest_id = data.get('guest_id')

        if not guest_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Guest ID is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if the guest belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_guests
                WHERE guest_id = %s AND user_id = %s''',
                (guest_id, user_id)
            )

            existing_guest = cursor.fetchone()
            if not existing_guest:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Guest not found'})
                }

            # Delete the guest
            cursor.execute(
                '''DELETE FROM wedding_guests
                WHERE guest_id = %s AND user_id = %s''',
                (guest_id, user_id)
            )

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'message': 'Guest deleted successfully'})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
