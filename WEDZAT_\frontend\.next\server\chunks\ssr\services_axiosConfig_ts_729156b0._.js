module.exports = {

"[project]/services/axiosConfig.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// services/axiosConfig.ts
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
// Create a custom axios instance with default configuration
const instance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub") || 'http://localhost:3000',
    headers: {
        'Content-Type': 'application/json'
    }
});
// Add a request interceptor to include the auth token in all requests
instance.interceptors.request.use((config)=>{
    // Get the token from localStorage if available
    // Try multiple possible token keys for compatibility
    const token = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
    // If token exists, add it to the Authorization header
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Debug API calls
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
        timestamp: new Date().toISOString(),
        caller: new Error().stack?.split('\n')[2]?.trim() || 'unknown'
    });
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add a response interceptor to handle common response issues
instance.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response && error.response.status === 401) {
        // Clear all possible token keys and redirect to login if needed
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        // Optionally redirect to login page
        // window.location.href = '/login';
        }
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = instance;
}}),

};

//# sourceMappingURL=services_axiosConfig_ts_729156b0._.js.map