import os
import json
import uuid
import psycopg2
import smtplib
import hashlib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from .utils import get_db_connection, validate_token

# Email configuration
EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', 587))
EMAIL_USER = os.getenv('EMAIL_USER', '')
EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD', '')
EMAIL_FROM = os.getenv('EMAIL_FROM', '<EMAIL>')

# Alternative email services
# You can use services like SendGrid, Mailgun, etc.
SENDGRID_API_KEY = os.getenv('SENDGRID_API_KEY', '')
MAILGUN_API_KEY = os.getenv('MAILGUN_API_KEY', '')
MAILGUN_DOMAIN = os.getenv('MAILGUN_DOMAIN', '')

def generate_response_token(guest_id, website_id):
    """Generate a unique token for the guest to respond to the invitation"""
    # Create a unique string combining guest_id and website_id
    unique_string = f"{guest_id}:{website_id}:{datetime.now().isoformat()}"

    # Generate a hash of the unique string
    token = hashlib.sha256(unique_string.encode()).hexdigest()

    return token

def create_invitation_html(guest, website_url, response_url):
    """Create HTML content for the invitation email"""
    return f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
            .header {{ text-align: center; margin-bottom: 20px; }}
            .content {{ margin-bottom: 30px; }}
            .button {{ display: inline-block; padding: 10px 20px; margin: 10px; text-decoration: none; border-radius: 5px; font-weight: bold; }}
            .attend {{ background-color: #B31B1E; color: white; }}
            .decline {{ background-color: #f8f9fa; color: #333; border: 1px solid #ddd; }}
            .footer {{ font-size: 12px; color: #777; margin-top: 30px; text-align: center; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>You're Invited!</h1>
            </div>
            <div class="content">
                <p>Dear {guest['first_name']} {guest['last_name']},</p>
                <p>We are delighted to invite you to our wedding celebration!</p>
                <p>Please visit our wedding website to view all the details:</p>
                <p style="text-align: center;">
                    <a href="{website_url}" style="display: inline-block; padding: 10px 20px; background-color: #B31B1E; color: white; text-decoration: none; border-radius: 5px;">View Wedding Website</a>
                </p>
                <p>Will you be able to join us? Please let us know by clicking one of the buttons below:</p>
                <div style="text-align: center;">
                    <a href="{response_url}&response=attending" class="button attend">Yes, I'll be there!</a>
                    <a href="{response_url}&response=declined" class="button decline">Sorry, I can't make it</a>
                </div>
                <p style="font-size: 14px; color: #666; margin-top: 20px; text-align: center;">
                    Note: Clicking either button will record your response immediately. No login required.
                </p>
            </div>
            <div class="footer">
                <p>This is an automated message. Please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    """

def send_invitation_email(guest, website_url, response_url):
    """Send invitation email to a guest using available email services"""
    # Create HTML content for the email
    html_content = create_invitation_html(guest, website_url, response_url)
    subject = f"You're Invited to Our Wedding!"
    recipient_email = guest['email']
    recipient_name = f"{guest['first_name']} {guest['last_name']}"

    # Try different email sending methods in order of preference

    # 1. Try SendGrid if configured
    if SENDGRID_API_KEY:
        try:
            print(f"Attempting to send email via SendGrid to {recipient_email}")
            headers = {
                "Authorization": f"Bearer {SENDGRID_API_KEY}",
                "Content-Type": "application/json"
            }
            data = {
                "personalizations": [{
                    "to": [{"email": recipient_email, "name": recipient_name}],
                    "subject": subject
                }],
                "from": {"email": EMAIL_FROM, "name": "Wedding Invitation"},
                "content": [{"type": "text/html", "value": html_content}]
            }
            response = requests.post(
                "https://api.sendgrid.com/v3/mail/send",
                headers=headers,
                json=data
            )
            if response.status_code == 202:
                print(f"Email sent successfully via SendGrid to {recipient_email}")
                return True
            else:
                print(f"SendGrid API error: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"SendGrid error: {str(e)}")

    # 2. Try Mailgun if configured
    if MAILGUN_API_KEY and MAILGUN_DOMAIN:
        try:
            print(f"Attempting to send email via Mailgun to {recipient_email}")
            response = requests.post(
                f"https://api.mailgun.net/v3/{MAILGUN_DOMAIN}/messages",
                auth=("api", MAILGUN_API_KEY),
                data={
                    "from": f"Wedding Invitation <{EMAIL_FROM}>",
                    "to": [f"{recipient_name} <{recipient_email}>"],
                    "subject": subject,
                    "html": html_content
                }
            )
            if response.status_code == 200:
                print(f"Email sent successfully via Mailgun to {recipient_email}")
                return True
            else:
                print(f"Mailgun API error: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Mailgun error: {str(e)}")

    # 3. Try SMTP as a fallback
    if EMAIL_USER and EMAIL_PASSWORD:
        try:
            print(f"Attempting to send email via SMTP to {recipient_email}")
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = EMAIL_FROM
            msg['To'] = recipient_email

            # Attach HTML version
            part = MIMEText(html_content, 'html')
            msg.attach(part)

            # Send email
            with smtplib.SMTP(EMAIL_HOST, EMAIL_PORT) as server:
                server.starttls()
                server.login(EMAIL_USER, EMAIL_PASSWORD)
                server.send_message(msg)

            print(f"Email sent successfully via SMTP to {recipient_email}")
            return True
        except Exception as e:
            print(f"SMTP error: {str(e)}")

    # If we're in development/testing mode, mark as successful anyway
    if os.getenv('ENVIRONMENT') == 'development' or os.getenv('ENVIRONMENT') == 'testing':
        print(f"Development/testing mode: Marking email as sent to {recipient_email} without actually sending")
        return True

    # If all methods failed and we're not in development mode
    print(f"All email sending methods failed for {recipient_email}. Please configure email credentials.")
    return False

def send_invitation(event):
    """Send invitation to a guest or group of guests"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event['body'])
        guest_ids = data.get('guest_ids', [])
        group_id = data.get('group_id')
        website_id = data.get('website_id')

        if not website_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Website ID is required'})
            }

        if not guest_ids and not group_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Either guest IDs or group ID is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Get the website URL
            cursor.execute(
                '''SELECT deployed_url FROM user_websites
                WHERE website_id = %s AND user_id = %s''',
                (website_id, user_id)
            )

            website = cursor.fetchone()
            if not website or not website['deployed_url']:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, POST',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Website not found or not deployed'})
                }

            website_url = website['deployed_url']

            # Get guests to invite
            guests_to_invite = []

            if group_id:
                # Get all guests in the group
                cursor.execute(
                    '''SELECT * FROM wedding_guests
                    WHERE group_id = %s AND user_id = %s AND email IS NOT NULL''',
                    (group_id, user_id)
                )
                group_guests = cursor.fetchall()
                guests_to_invite.extend(group_guests)

            if guest_ids:
                # Get specific guests
                placeholders = ', '.join(['%s'] * len(guest_ids))
                query = f'''SELECT * FROM wedding_guests
                          WHERE guest_id IN ({placeholders}) AND user_id = %s AND email IS NOT NULL'''
                params = guest_ids + [user_id]

                cursor.execute(query, params)
                individual_guests = cursor.fetchall()
                guests_to_invite.extend(individual_guests)

            if not guests_to_invite:
                return {
                    'statusCode': 400,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, POST',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'No guests with email addresses found'})
                }

            # Send invitations
            successful_invites = []
            failed_invites = []

            for guest in guests_to_invite:
                # Generate a response token
                response_token = generate_response_token(guest['guest_id'], website_id)

                # Create response URL that points directly to the API endpoint
                # Use the API Gateway URL instead of the website URL
                api_base_url = "https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test"
                response_url = f"{api_base_url}/tool/track-invitation?token={response_token}"

                # Store the token in the database
                cursor.execute(
                    '''UPDATE wedding_guests
                    SET invitation_sent = TRUE,
                        invitation_sent_date = NOW(),
                        updated_at = NOW()
                    WHERE guest_id = %s''',
                    (guest['guest_id'],)
                )

                # Send the email
                if send_invitation_email(guest, website_url, response_url):
                    successful_invites.append({
                        'guest_id': guest['guest_id'],
                        'name': f"{guest['first_name']} {guest['last_name']}",
                        'email': guest['email']
                    })
                else:
                    failed_invites.append({
                        'guest_id': guest['guest_id'],
                        'name': f"{guest['first_name']} {guest['last_name']}",
                        'email': guest['email']
                    })

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({
                    'message': f"Invitations sent to {len(successful_invites)} guests",
                    'successful_invites': successful_invites,
                    'failed_invites': failed_invites
                })
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def track_invitation_response(event):
    """Track guest response to invitation"""
    # This is a public endpoint, no authentication required
    try:
        # Get query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        token = query_params.get('token')
        response = query_params.get('response')

        if not token or not response:
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Wedding RSVP Error</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; text-align: center; padding: 20px; }
                    .container { max-width: 600px; margin: 0 auto; padding: 40px 20px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
                    h1 { color: #B31B1E; margin-bottom: 20px; }
                    .emoji { font-size: 72px; margin: 20px 0; }
                    .message { font-size: 20px; margin: 20px 0; }
                    .details { color: #666; margin: 30px 0; }
                    .footer { font-size: 14px; color: #999; margin-top: 40px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="emoji">⚠️</div>
                    <h1>Invalid RSVP Link</h1>
                    <div class="message">The RSVP link you clicked is incomplete or invalid.</div>
                    <div class="details">
                        <p>Please check your email for the correct link or contact the couple directly.</p>
                    </div>
                    <div class="footer">
                        <p>Error: Missing token or response parameter</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, GET',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Content-Type': 'text/html'
                },
                'body': error_html
            }

        if response not in ['attending', 'declined']:
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Wedding RSVP Error</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; text-align: center; padding: 20px; }
                    .container { max-width: 600px; margin: 0 auto; padding: 40px 20px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
                    h1 { color: #B31B1E; margin-bottom: 20px; }
                    .emoji { font-size: 72px; margin: 20px 0; }
                    .message { font-size: 20px; margin: 20px 0; }
                    .details { color: #666; margin: 30px 0; }
                    .footer { font-size: 14px; color: #999; margin-top: 40px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="emoji">⚠️</div>
                    <h1>Invalid Response</h1>
                    <div class="message">The response value is not valid.</div>
                    <div class="details">
                        <p>Please use one of the buttons in your invitation email or contact the couple directly.</p>
                    </div>
                    <div class="footer">
                        <p>Error: Response must be 'attending' or 'declined'</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, GET',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Content-Type': 'text/html'
                },
                'body': error_html
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Find the guest by token
            # In a real implementation, you would store the token in the database
            # For this example, we'll just use the token to extract the guest_id
            # This is a simplified approach and not secure for production

            # Extract guest_id from token (this is just a placeholder)
            # In reality, you would look up the token in a tokens table
            guest_id = token.split(':')[0] if ':' in token else None

            if not guest_id:
                error_html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Wedding RSVP Error</title>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; text-align: center; padding: 20px; }
                        .container { max-width: 600px; margin: 0 auto; padding: 40px 20px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
                        h1 { color: #B31B1E; margin-bottom: 20px; }
                        .emoji { font-size: 72px; margin: 20px 0; }
                        .message { font-size: 20px; margin: 20px 0; }
                        .details { color: #666; margin: 30px 0; }
                        .footer { font-size: 14px; color: #999; margin-top: 40px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="emoji">⚠️</div>
                        <h1>Invalid Invitation Link</h1>
                        <div class="message">The invitation link you clicked is invalid.</div>
                        <div class="details">
                            <p>Please check your email for the correct link or contact the couple directly.</p>
                        </div>
                        <div class="footer">
                            <p>Error: Invalid token format</p>
                        </div>
                    </div>
                </body>
                </html>
                """

                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, GET',
                        'Access-Control-Allow-Headers': 'Content-Type',
                        'Content-Type': 'text/html'
                    },
                    'body': error_html
                }

            # Extract guest_id from token
            # The token could be in different formats:
            # 1. From the database (a UUID)
            # 2. From our frontend (format: guest_id-website_id)
            if '-' in token:
                parts = token.split('-')
                guest_id = parts[0]
                # If there's a website_id part, we could use it for additional validation
                # but we'll just log it for now
                if len(parts) > 1:
                    print(f"Website ID from token: {parts[1]}")
            else:
                # Try to find the guest_id in the database using the token
                cursor.execute(
                    '''SELECT guest_id FROM wedding_invitation_responses WHERE response_token = %s''',
                    (token,)
                )
                invitation_record = cursor.fetchone()
                if invitation_record:
                    guest_id = invitation_record['guest_id']
                else:
                    # If we can't find it, use the token as the guest_id
                    guest_id = token

            # We could get the couple name from the database for personalization
            # but we'll just use a generic message for now

            # Update the guest's attendance status
            cursor.execute(
                '''UPDATE wedding_guests
                SET attendance_status = %s,
                    response_date = NOW(),
                    updated_at = NOW()
                WHERE guest_id = %s
                RETURNING first_name, last_name, email''',
                (response, guest_id)
            )

            guest = cursor.fetchone()

            if not guest:
                error_html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Wedding RSVP Error</title>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; text-align: center; padding: 20px; }
                        .container { max-width: 600px; margin: 0 auto; padding: 40px 20px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
                        h1 { color: #B31B1E; margin-bottom: 20px; }
                        .emoji { font-size: 72px; margin: 20px 0; }
                        .message { font-size: 20px; margin: 20px 0; }
                        .details { color: #666; margin: 30px 0; }
                        .footer { font-size: 14px; color: #999; margin-top: 40px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="emoji">⚠️</div>
                        <h1>Guest Not Found</h1>
                        <div class="message">We couldn't find your guest record in our system.</div>
                        <div class="details">
                            <p>The invitation link may be invalid or your information may have been updated.</p>
                            <p>Please contact the couple directly to confirm your attendance.</p>
                        </div>
                        <div class="footer">
                            <p>Error: Guest record not found</p>
                        </div>
                    </div>
                </body>
                </html>
                """

                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, GET',
                        'Access-Control-Allow-Headers': 'Content-Type',
                        'Content-Type': 'text/html'
                    },
                    'body': error_html
                }

            conn.commit()

            # Create a nice HTML response page
            message = "Thank you for your response!"
            if response == 'attending':
                message = f"Thank you, {guest['first_name']}! We look forward to celebrating with you!"
                color = "#B31B1E"  # Wedding red color
                emoji = "🎉"
            else:
                message = f"Thank you for letting us know, {guest['first_name']}. We're sorry you can't make it."
                color = "#666666"  # Gray color
                emoji = "💌"

            html_response = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Wedding RSVP Response</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; text-align: center; padding: 20px; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 40px 20px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
                    h1 {{ color: {color}; margin-bottom: 20px; }}
                    .emoji {{ font-size: 72px; margin: 20px 0; }}
                    .message {{ font-size: 20px; margin: 20px 0; }}
                    .details {{ color: #666; margin: 30px 0; }}
                    .footer {{ font-size: 14px; color: #999; margin-top: 40px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="emoji">{emoji}</div>
                    <h1>RSVP Response Recorded</h1>
                    <div class="message">{message}</div>
                    <div class="details">
                        <p>Guest: {guest['first_name']} {guest['last_name']}</p>
                        <p>Response: {response.capitalize()}</p>
                    </div>
                    <div class="footer">
                        <p>This response has been recorded in the couple's guest list.</p>
                        <p>You can close this window now.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, GET',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Content-Type': 'text/html'
                },
                'body': html_response
            }
        except Exception as e:
            if conn:
                conn.rollback()
            # Create an error HTML page
            error_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Wedding RSVP Error</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; text-align: center; padding: 20px; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 40px 20px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
                    h1 {{ color: #B31B1E; margin-bottom: 20px; }}
                    .emoji {{ font-size: 72px; margin: 20px 0; }}
                    .message {{ font-size: 20px; margin: 20px 0; }}
                    .details {{ color: #666; margin: 30px 0; }}
                    .footer {{ font-size: 14px; color: #999; margin-top: 40px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="emoji">⚠️</div>
                    <h1>Something Went Wrong</h1>
                    <div class="message">We couldn't process your RSVP response.</div>
                    <div class="details">
                        <p>Please contact the couple directly to confirm your attendance.</p>
                    </div>
                    <div class="footer">
                        <p>Error details: {str(e)}</p>
                        <p>You can close this window now.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, GET',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Content-Type': 'text/html'
                },
                'body': error_html
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        # Create an error HTML page for the outer exception handler
        error_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Wedding RSVP Error</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; text-align: center; padding: 20px; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 40px 20px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
                h1 {{ color: #B31B1E; margin-bottom: 20px; }}
                .emoji {{ font-size: 72px; margin: 20px 0; }}
                .message {{ font-size: 20px; margin: 20px 0; }}
                .details {{ color: #666; margin: 30px 0; }}
                .footer {{ font-size: 14px; color: #999; margin-top: 40px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="emoji">⚠️</div>
                <h1>Something Went Wrong</h1>
                <div class="message">We couldn't process your RSVP response.</div>
                <div class="details">
                    <p>Please contact the couple directly to confirm your attendance.</p>
                </div>
                <div class="footer">
                    <p>Error details: {str(e)}</p>
                    <p>You can close this window now.</p>
                </div>
            </div>
        </body>
        </html>
        """

        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Content-Type': 'text/html'
            },
            'body': error_html
        }
