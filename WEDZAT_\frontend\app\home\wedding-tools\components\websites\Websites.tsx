"use client";
import React, { useState, useEffect } from "react";
import { Plus<PERSON>ir<PERSON>, Edit, Trash2, ExternalLink, Loader2 } from "lucide-react";
import axios from "axios";
// import { useRouter } from "next/navigation";
import WebsiteEditor from "./WebsiteEditor";

interface Website {
  website_id: string;
  title: string;
  couple_names?: string;
  template_id: string;
  wedding_date: string;
  wedding_location: string;
  about_couple: string;
  deployed_url: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
  template_name: string;
  template_thumbnail: string;
  design_settings?: {
    colors?: {
      primary: string;
      secondary: string;
      background: string;
      text: string;
    };
    fonts?: {
      heading: string;
      body: string;
      coupleNames?: string;
      date?: string;
      location?: string;
      aboutCouple?: string;
    };
    customImage?: string;
    couple_names?: string; // Added for backward compatibility
  };
}

interface Template {
  template_id: string;
  name: string;
  thumbnail_url: string;
  description: string;
  default_colors: any;
  default_fonts: any;
}

interface WebsitesProps {
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  setLoading: (loading: boolean) => void;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
}

const Websites: React.FC<WebsitesProps> = ({
  setError,
  setSuccessMessage,
  setLoading,
  loading,
  error,
  successMessage
}) => {
  const [websites, setWebsites] = useState<Website[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [showEditor, setShowEditor] = useState(false);
  const [editingWebsite, setEditingWebsite] = useState<Website | null>(null);
  const [loadingWebsites, setLoadingWebsites] = useState(true);
  const [loadingTemplates, setLoadingTemplates] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // const router = useRouter(); // Uncomment if needed

  useEffect(() => {
    fetchWebsites();
    fetchTemplates();
  }, []);

  const fetchWebsites = async () => {
    try {
      setLoadingWebsites(true);
      const token = localStorage.getItem('token');

      if (!token) {
        setError("Authentication required. Please log in.");
        setLoadingWebsites(false);
        return;
      }

      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites',
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.data && response.data.websites) {
        setWebsites(response.data.websites);
      }
      setLoadingWebsites(false);
    } catch (error) {
      console.error('Error fetching websites:', error);
      setError("Failed to load your websites. Please try again.");
      setLoadingWebsites(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      setLoadingTemplates(true);
      const token = localStorage.getItem('token');

      if (!token) {
        setError("Authentication required. Please log in.");
        setLoadingTemplates(false);
        return;
      }

      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates',
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.data && response.data.templates) {
        console.log('Templates from API:', response.data.templates);

        // Ensure templates have valid image URLs
        const processedTemplates = response.data.templates.map((template: any) => ({
          ...template,
          thumbnail_url: template.thumbnail_url || '/placeholder-template.jpg'
        }));

        console.log('Processed templates:', processedTemplates);
        setTemplates(processedTemplates);
      }
      setLoadingTemplates(false);
    } catch (error) {
      console.error('Error fetching templates:', error);
      setError("Failed to load website templates. Please try again.");
      setLoadingTemplates(false);
    }
  };

  const handleCreateWebsite = () => {
    setEditingWebsite(null);
    setShowEditor(true);
  };

  const handleEditWebsite = (website: Website) => {
    setEditingWebsite(website);
    setShowEditor(true);
  };

  const handleDeleteWebsite = async (websiteId: string) => {
    try {
      setDeletingId(websiteId);
      const token = localStorage.getItem('token');

      if (!token) {
        setError("Authentication required. Please log in.");
        setDeletingId(null);
        return;
      }

      await axios.delete(
        `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website?website_id=${websiteId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      setSuccessMessage("Website deleted successfully");
      setWebsites(websites.filter(website => website.website_id !== websiteId));
      setDeletingId(null);
    } catch (error) {
      console.error('Error deleting website:', error);
      setError("Failed to delete website. Please try again.");
      setDeletingId(null);
    }
  };

  const handleSaveWebsite = async (websiteData: any) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        setError("Authentication required. Please log in.");
        setLoading(false);
        return;
      }

      // Find the selected template to include its thumbnail
      const selectedTemplate = templates.find(t => t.template_id === websiteData.template_id);

      // Prepare the data with template information
      const completeWebsiteData = {
        ...websiteData,
        template_name: selectedTemplate?.name || '',
        template_thumbnail: ''
      };

      // No need to set template thumbnail anymore

      console.log('Saving website with data:', completeWebsiteData);

      if (editingWebsite) {
        // Update existing website
        await axios.put(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-website',
          {
            ...completeWebsiteData,
            website_id: editingWebsite.website_id
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        setSuccessMessage("Website updated successfully");
      } else {
        // Create new website
        await axios.post(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-website',
          completeWebsiteData,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        setSuccessMessage("Website created successfully");
      }

      // Refresh the websites list
      fetchWebsites();

      // Close the editor
      setShowEditor(false);
      setEditingWebsite(null);
      setLoading(false);
    } catch (error) {
      console.error('Error saving website:', error);
      setError("Failed to save website. Please try again.");
      setLoading(false);
    }
  };

  const handleVisitWebsite = (website: Website) => {
    // Always use the website ID to construct the URL to ensure it works
    const url = `/wedding/${website.website_id}`;

    // Make it absolute
    const absoluteUrl = `${window.location.origin}${url}`;

    // Open in a new tab
    window.open(absoluteUrl, '_blank');
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (showEditor) {
    return (
      <WebsiteEditor
        templates={templates}
        website={editingWebsite as any}
        onSave={handleSaveWebsite}
        onCancel={() => {
          setShowEditor(false);
          setEditingWebsite(null);
        }}
        loading={loading}
      />
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-black">Wedding Websites</h2>
        <button
          onClick={handleCreateWebsite}
          className="flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
          disabled={loadingTemplates}
        >
          {loadingTemplates ? (
            <Loader2 size={18} className="animate-spin" />
          ) : (
            <PlusCircle size={18} />
          )}
          <span>Create Website</span>
        </button>
      </div>

      {loadingWebsites ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 size={32} className="animate-spin text-[#B31B1E]" />
        </div>
      ) : websites.length === 0 ? (
        <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg">
          <p className="text-gray-600 mb-4">You haven't created any wedding websites yet.</p>
          <button
            onClick={handleCreateWebsite}
            className="flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors mx-auto"
          >
            <PlusCircle size={18} />
            <span>Create Your First Website</span>
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {websites.map((website) => (
            <div key={website.website_id} className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
              <div className="h-40 bg-gray-200 relative">
                {/* Always show an image, with fallbacks */}
                <img
                  src={website.design_settings?.customImage || website.template_thumbnail || '/placeholder-template.jpg'}
                  alt={website.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // If image fails to load, replace with fallback
                    const target = e.target as HTMLImageElement;
                    target.onerror = null; // Prevent infinite loop
                    target.src = '/placeholder-template.jpg';
                  }}
                />
                {/* Console log for debugging - removed to fix TypeScript error */}
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2">
                  <h3 className="font-semibold truncate">{website.title}</h3>
                  <p className="text-sm opacity-80">{website.template_name}</p>
                </div>
              </div>
              <div className="p-4">
                <div className="mb-3">
                  {/* Always show couple names */}
                  <p className="text-sm text-gray-600 truncate">
                    <span className="font-semibold">Couple:</span> {website.couple_names || website.design_settings?.couple_names || 'Jane & John'}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-semibold">Wedding Date:</span> {formatDate(website.wedding_date)}
                  </p>
                  <p className="text-sm text-gray-600 truncate">
                    <span className="font-semibold">Location:</span> {website.wedding_location || 'Not specified'}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-semibold">Last Updated:</span> {formatDate(website.updated_at)}
                  </p>
                </div>
                <div className="flex justify-between">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEditWebsite(website)}
                      className="p-2 text-gray-600 hover:text-[#B31B1E] hover:bg-gray-100 rounded-md transition-colors"
                      title="Edit Website"
                    >
                      <Edit size={18} />
                    </button>
                    <button
                      onClick={() => handleDeleteWebsite(website.website_id)}
                      className="p-2 text-gray-600 hover:text-red-600 hover:bg-gray-100 rounded-md transition-colors"
                      title="Delete Website"
                      disabled={deletingId === website.website_id}
                    >
                      {deletingId === website.website_id ? (
                        <Loader2 size={18} className="animate-spin" />
                      ) : (
                        <Trash2 size={18} />
                      )}
                    </button>
                  </div>
                  <button
                    onClick={() => handleVisitWebsite(website)}
                    className="flex items-center gap-1 px-3 py-1 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
                    title="Visit Website"
                  >
                    <ExternalLink size={16} />
                    <span>Visit</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mt-4 p-2 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Success message */}
      {successMessage && (
        <div className="mt-4 p-2 bg-green-100 text-green-700 rounded-md">
          {successMessage}
        </div>
      )}
    </div>
  );
};

export default Websites;

