"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import UserAvatar from "./UserAvatar";
import axios from "../../services/axiosConfig";
import { useRouter } from "next/navigation";

// Define interface for photo items
interface Photo {
  photo_id: string;
  photo_name: string;
  photo_url: string;
  photo_description?: string;
  photo_tags?: string[];
  photo_subtype?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  photo_views?: number;
  photo_likes?: number;
  photo_comments?: number;
}

interface ApiResponse {
  photos: Photo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

interface PhotosProps {
  shouldLoad?: boolean;
}

const Photos: React.FC<PhotosProps> = ({ shouldLoad = false }) => {
  const router = useRouter();
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState<boolean>(false);
  const [likedPhotos, setLikedPhotos] = useState<Set<string>>(new Set());
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreTriggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string, event?: React.MouseEvent) => {
    // Stop event propagation to prevent triggering parent click events
    if (event) {
      event.stopPropagation();
    }

    // If we have a userId, navigate to that specific user's profile
    if (userId) {
      router.push(`/profile/${userId}`);
    } else if (username) {
      // For now, just use the username as a parameter
      // In a real app, you might want to fetch the user ID first
      router.push(`/profile/${username}`);
    } else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  // Fallback data if API fails - using empty array
  const getFallbackPhotos = (): Photo[] => [];

  // Function to fetch photos from the API
  const fetchPhotos = async (pageNumber: number, isInitialLoad: boolean = false) => {
    // Prevent multiple simultaneous requests
    if ((isInitialLoad && loading) || (!isInitialLoad && loadingMore)) {
      console.log('Request already in progress, skipping');
      return;
    }

    try {
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      console.log(`Fetching photos for page ${pageNumber}...`);

      // Simple token retrieval
      const token = localStorage.getItem('token');

      console.log(`Auth token found: ${token ? 'Yes' : 'No'}`);

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setPhotos([]);
        return;
      }

      // Make API request
      const response = await axios.get<ApiResponse>(`/photos?page=${pageNumber}&limit=10`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('API response status:', response.status);

      // Process the data
      if (response.data && response.data.photos) {
        console.log(`Loaded ${response.data.photos.length} photos for page ${pageNumber}`);

        // Log the first item for debugging
        if (response.data.photos.length > 0) {
          console.log('Sample photo data:', response.data.photos[0]);
        }

        // Process the response
        const processedPhotos = response.data.photos.map(photo => {
          if (!photo.photo_url) {
            console.log(`Photo missing URL: ${photo.photo_id}`);
          }
          return photo;
        });

        if (pageNumber === 1) {
          setPhotos(processedPhotos);
        } else {
          setPhotos(prev => [...prev, ...processedPhotos]);
        }

        setHasMore(response.data.next_page);
        setPage(pageNumber); // Update the current page
        setError(null); // Clear any previous errors
      } else {
        console.warn('Unexpected response format:', response.data);
        setError('Failed to load photos - unexpected response format');
        setPhotos([]);
      }
    } catch (err) {
      console.error('API request failed:', err);
      setError('Failed to load photos');
      setPhotos([]);
    } finally {
      // Always clear loading states
      if (isInitialLoad) {
        setLoading(false);
        console.log('Initial loading complete');
      } else {
        setLoadingMore(false);
        console.log('Loading more complete');
      }
    }
  };

  // Fetch first page of photos as soon as the component mounts
  useEffect(() => {
    // Only trigger when shouldLoad changes to true
    if (shouldLoad) {
      console.log('Photos component is now visible and ready to load data');

      // Set a flag to track initial load
      setInitialLoadComplete(true);

      // IMMEDIATE API request with no conditions or delays
      console.log('Triggering initial photos load IMMEDIATELY...');
      setLoading(true);

      // Skip the fetchPhotos function and make the API call directly here
      const token = localStorage.getItem('token');

      if (token) {
        // Make direct API request
        console.log('Making direct API request for photos page 1...');
        axios.get<ApiResponse>(`/photos?page=1&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
          .then(response => {
            console.log('API response received for photos page 1');
            if (response.data && response.data.photos) {
              setPhotos(response.data.photos);
              setHasMore(response.data.next_page);
              setPage(1);
              setError(null);
            } else {
              setPhotos([]);
              setError('No photos found');
            }
          })
          .catch(err => {
            console.error('Direct API request failed:', err);
            setError('Failed to load photos');
            setPhotos([]);
          })
          .finally(() => {
            setLoading(false);
            console.log('Initial loading complete');
          });
      } else {
        setLoading(false);
        setError('Authentication required');
      }
    }
  }, [shouldLoad]); // Only depend on shouldLoad

  // Setup Intersection Observer for horizontal lazy loading
  useEffect(() => {
    // Only set up observer after initial load and if there's more content to fetch
    if (!initialLoadComplete || !hasMore) return;

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
      console.log('Disconnected previous intersection observer');
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        // If the trigger element is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && !loadingMore && hasMore) {
          console.log('Load more trigger is visible, loading next page...');
          console.log('Intersection ratio:', entries[0].intersectionRatio);
          const nextPage = page + 1;
          fetchPhotos(nextPage, false);
        }
      },
      // Use the scroll container as the root for the intersection observer
      {
        root: scrollContainerRef.current,
        threshold: 0.1, // Trigger when 10% of the element is visible
        rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier
      }
    );

    // Start observing the trigger element
    if (loadMoreTriggerRef.current) {
      observerRef.current.observe(loadMoreTriggerRef.current);
      console.log('Now observing load more trigger element');
    } else {
      console.warn('Load more trigger element not found');
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        console.log('Disconnected intersection observer');
      }
    };
  }, [hasMore, loading, loadingMore, page, initialLoadComplete]);

  // Get appropriate image source for a photo
  const getImageSource = (photo: Photo): string => {
    if (photo.photo_url) {
      return photo.photo_url;
    }
    return '/pics/placeholder.svg';
  };

  // Toggle like for photos
  const toggleLike = async (photoId: string) => {
    try {
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Optimistically update UI
      const isCurrentlyLiked = likedPhotos.has(photoId);
      setLikedPhotos((prev) => {
        const newLiked = new Set(prev);
        if (isCurrentlyLiked) {
          newLiked.delete(photoId);
        } else {
          newLiked.add(photoId);
        }
        return newLiked;
      });

      // Make API call
      const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';
      await axios.post(endpoint, {
        content_id: photoId,
        content_type: 'photo'
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log(`${isCurrentlyLiked ? 'Unliked' : 'Liked'} photo: ${photoId}`);
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert optimistic update on error
      setLikedPhotos((prev) => {
        const newLiked = new Set(prev);
        if (likedPhotos.has(photoId)) {
          newLiked.delete(photoId);
        } else {
          newLiked.add(photoId);
        }
        return newLiked;
      });
    }
  };

  return (
    <section className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-black">
          Photos
        </h2>
        <a
          href="/home/<USER>"
          className="text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]"
        >
          See all
        </a>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="py-10 text-center">
          <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          <span className="text-gray-600">Loading photos...</span>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="py-10 text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={() => {
              console.log('Retrying photos load...');
              setError(null);
              fetchPhotos(1, true);
            }}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            Retry
          </button>
        </div>
      )}

      {/* Horizontal scroll container */}
      {!loading && (
        <div
          className="overflow-x-auto scrollbar-hide relative"
          ref={scrollContainerRef}
          style={{
            scrollBehavior: 'smooth',
            WebkitOverflowScrolling: 'touch', // For smoother scrolling on iOS
            minHeight: photos.length === 0 && !error ? '220px' : 'auto'
          }}
        >
          {/* Empty state message when no photos */}
          {photos.length === 0 && !error && !loading && (
            <div className="flex items-center justify-center h-[220px] w-full">
              <div className="text-gray-400">No photos available</div>
            </div>
          )}

          <div className="flex gap-4 pb-4 flex-nowrap">
            {photos.map((photo, index) => (
              <div
                key={`${photo.photo_id}-${index}`}
                className="flex-shrink-0 rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer"
                style={{ width: '250px', height: '200px' }}
                onClick={() => {
                  window.location.href = `/home/<USER>/${photo.photo_id}`;
                }}
              >
                {/* User avatar */}
                <div
                  className="absolute top-2 left-2 z-10"
                  onClick={(e) => navigateToUserProfile(photo.user_id, photo.user_name, e)}
                >
                  <UserAvatar
                    username={photo.user_name || "user"}
                    size="sm"
                    isGradientBorder={true}
                    onClick={(e) => navigateToUserProfile(photo.user_id, photo.user_name, e)}
                  />
                </div>

                {/* Photo */}
                <div className="relative w-full h-full">
                  <Image
                    src={getImageSource(photo)}
                    alt={photo.photo_name || "Photo"}
                    fill
                    sizes="(max-width: 640px) 250px, 250px"
                    className="object-cover"
                    {...(index < 4 ? { priority: true } : { loading: 'lazy' })} // Priority for first four, lazy loading for rest
                    placeholder="blur" // Show blur placeholder while loading
                    blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==" // Base64 encoded SVG loading animation
                    onError={(e) => {
                      const imgElement = e.target as HTMLImageElement;
                      if (imgElement) {
                        imgElement.src = '/pics/placeholder.svg';
                      }
                    }}
                  />
                </div>

                {/* Photo info */}
                <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white">
                  <div className="text-sm font-medium truncate">{photo.photo_name}</div>
                  <div className="flex items-center justify-between">
                    <div className="text-xs">
                      <div
                        className="cursor-pointer hover:underline"
                        onClick={(e) => navigateToUserProfile(photo.user_id, photo.user_name, e)}
                      >
                        {photo.user_name}
                      </div>
                      <div>
                        {photo.photo_likes ? `${photo.photo_likes} likes` : ''}
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleLike(photo.photo_id);
                      }}
                      className="flex items-center space-x-1 text-white opacity-80 hover:opacity-100 transition-opacity"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill={likedPhotos.has(photo.photo_id) ? "#B31B1E" : "none"}
                        stroke={likedPhotos.has(photo.photo_id) ? "#B31B1E" : "white"}
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {/* Load more trigger element - this is what IntersectionObserver watches */}
            {hasMore && (
              <div
                ref={loadMoreTriggerRef}
                className="flex-shrink-0 w-10 h-full opacity-0"
                style={{
                  position: 'relative',
                  // Add debug outline in development
                  outline: process.env.NODE_ENV === 'development' ? '1px dashed rgba(255, 0, 0, 0.3)' : 'none'
                }}
                aria-hidden="true"
                data-testid="photos-load-more-trigger"
              />
            )}

            {/* Loading indicator - only show when loading more */}
            {loadingMore && (
              <div className="flex-shrink-0 flex items-center justify-center min-w-[100px] h-[200px]">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="ml-2 text-sm text-gray-600">Loading more...</span>
              </div>
            )}
          </div>
        </div>
      )}

      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </section>
  );
};

export default Photos;