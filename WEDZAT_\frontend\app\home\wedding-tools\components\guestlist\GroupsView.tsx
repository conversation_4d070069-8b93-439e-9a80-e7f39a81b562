"use client";
import React, { useState } from 'react';
import { MoreHorizontal, Check, Clock, X, Mail, Edit, Trash2, Plus } from 'lucide-react';
import axios from 'axios';
import { getAuthToken } from '../../utils';
import { Guest, Group, MenuOption } from './GuestList';
import AddGuestModal from './AddGuestModal';
import AddGroupModal from './AddGroupModal';

interface GroupsViewProps {
  groups: Group[];
  guests: Guest[];
  menuOptions: MenuOption[];
  setGroups: React.Dispatch<React.SetStateAction<Group[]>>;
  setGuests: React.Dispatch<React.SetStateAction<Guest[]>>;
  fetchData: () => Promise<void>;
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  showAddGuestModal: boolean;
  setShowAddGuestModal: React.Dispatch<React.SetStateAction<boolean>>;
  showAddGroupModal: boolean;
  setShowAddGroupModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const GroupsView: React.FC<GroupsViewProps> = ({
  groups,
  guests,
  menuOptions,
  setGroups,
  setGuests,
  fetchData,
  setError,
  setSuccessMessage,
  showAddGuestModal,
  setShowAddGuestModal,
  showAddGroupModal,
  setShowAddGroupModal
}) => {

  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [selectedGuest, setSelectedGuest] = useState<Guest | null>(null);
  const [showDeleteGroupConfirm, setShowDeleteGroupConfirm] = useState(false);
  const [showDeleteGuestConfirm, setShowDeleteGuestConfirm] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);

  // Group guests by group_id
  const guestsByGroup = groups.map(group => {
    // Use string comparison to ensure matching works correctly
    const groupGuests = guests.filter(guest => String(guest.group_id) === String(group.group_id));

    return {
      ...group,
      guests: groupGuests,
      guest_count: groupGuests.length
    };
  });

  // Delete a group
  const handleDeleteGroup = async (groupId: string) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      await axios.delete('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-guest-group', {
        headers: { Authorization: `Bearer ${token}` },
        data: { group_id: groupId }
      });

      setGroups(prevGroups => prevGroups.filter(g => g.group_id !== groupId));
      setSuccessMessage('Group deleted successfully');
      setShowDeleteGroupConfirm(false);

    } catch (err: any) {
      console.error('Error deleting group:', err);
      setError(err.response?.data?.error || 'Failed to delete group');
    }
  };

  // Delete a guest
  const handleDeleteGuest = async (guestId: string) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      await axios.delete('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-guest', {
        headers: { Authorization: `Bearer ${token}` },
        data: { guest_id: guestId }
      });

      setGuests(prevGuests => prevGuests.filter(g => g.guest_id !== guestId));
      setSuccessMessage('Guest deleted successfully');
      setShowDeleteGuestConfirm(false);

    } catch (err: any) {
      console.error('Error deleting guest:', err);
      setError(err.response?.data?.error || 'Failed to delete guest');
    }
  };

  // Update guest attendance status
  const updateGuestAttendance = async (guest: Guest, newStatus: 'attending' | 'pending' | 'declined') => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      await axios.put(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',
        {
          guest_id: guest.guest_id,
          attendance_status: newStatus
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      setGuests(prevGuests =>
        prevGuests.map(g =>
          g.guest_id === guest.guest_id
            ? { ...g, attendance_status: newStatus }
            : g
        )
      );

    } catch (err: any) {
      console.error('Error updating guest attendance:', err);
      setError(err.response?.data?.error || 'Failed to update guest attendance');
    }
  };

  // Update guest menu option
  const updateGuestMenu = async (guest: Guest, menuId: string) => {
    try {
      const token = getAuthToken();
      const selectedMenu = menuOptions.find(m => m.menu_id === menuId);

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      if (!selectedMenu) {
        setError('Menu option not found');
        return;
      }

      await axios.put(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',
        {
          guest_id: guest.guest_id,
          menu_id: menuId
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      setGuests(prevGuests =>
        prevGuests.map(g =>
          g.guest_id === guest.guest_id
            ? { ...g, menu_id: menuId, menu_name: selectedMenu.name }
            : g
        )
      );

    } catch (err: any) {
      console.error('Error updating guest menu:', err);
      setError(err.response?.data?.error || 'Failed to update guest menu');
    }
  };

  // Get couple name for invitation - try to get from website first, then user info
  const getCoupleName = async (): Promise<string> => {
    try {
      // First try to get from website
      const token = getAuthToken();
      if (token) {
        try {
          const websitesResponse = await axios.get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {
            headers: { Authorization: `Bearer ${token}` }
          });

          const websites = websitesResponse.data.websites || [];
          if (websites.length > 0) {
            // Check for couple_names in both places
            const coupleNames = websites[0].couple_names ||
                              (websites[0].design_settings && websites[0].design_settings.couple_names);
            if (coupleNames) {
              console.log('Using couple names from website:', coupleNames);
              return coupleNames;
            }
          }
        } catch (websiteError) {
          console.warn('Could not get website info:', websiteError);
          // Continue to user info fallback
        }
      }

      // Fallback to user info
      const userResponse = await axios.get('/api/user-info');
      const user = userResponse.data.user || {};
      const coupleName = `${user.first_name || 'The'} & ${user.last_name || 'Partner'}`;
      console.log('Using couple name from user info:', coupleName);
      return coupleName;
    } catch (error) {
      console.warn('Could not get user info, using default couple name');
      return "The Couple";
    }
  };

  // Get website information for invitation
  const getWebsiteInfo = async (token: string): Promise<{ websiteId: string, websiteUrl: string } | null> => {
    try {
      const websitesResponse = await axios.get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {
        headers: { Authorization: `Bearer ${token}` }
      });

      const websites = websitesResponse.data.websites || [];

      if (websites.length === 0) {
        setError('You need to create a wedding website first to send invitations');
        return null;
      }

      return {
        websiteId: websites[0].website_id,
        websiteUrl: websites[0].deployed_url
      };
    } catch (error) {
      console.error('Error getting website info:', error);
      setError('Failed to get website information');
      return null;
    }
  };

  // Mark invitation as sent in the backend
  const markInvitationSent = async (token: string, guestId: string, websiteId: string): Promise<any> => {
    try {
      return await axios.post(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/send-invitation',
        {
          guest_ids: [guestId],
          website_id: websiteId
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );
    } catch (error) {
      console.error('Error marking invitation as sent:', error);
      throw error;
    }
  };

  // Send the actual email using our frontend API
  const sendInvitationEmail = async (
    email: string,
    guestName: string,
    coupleName: string,
    websiteUrl: string,
    responseUrl: string,
    guestId: string
  ): Promise<void> => {
    await axios.post('/api/send-wedding-invitation', {
      email,
      guestName,
      coupleName,
      websiteUrl,
      responseUrl,
      guestId
    });
  };

  // Send invitation to a guest
  const sendInvitation = async (guest: Guest) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      // Get website information
      const websiteInfo = await getWebsiteInfo(token);
      if (!websiteInfo) return;

      // Mark invitation as sent in the backend
      const backendResponse = await markInvitationSent(token, guest.guest_id, websiteInfo.websiteId);

      // Get couple name
      const coupleName = await getCoupleName();

      // Create response URL
      const responseToken = backendResponse.data.response_token || `${guest.guest_id}-${websiteInfo.websiteId}`;
      const responseUrl = `${window.location.origin}/wedding-invitation-response?token=${responseToken}`;

      // Send the actual email
      await sendInvitationEmail(
        guest.email,
        `${guest.first_name} ${guest.last_name}`,
        coupleName,
        websiteInfo.websiteUrl,
        responseUrl,
        guest.guest_id
      );

      setSuccessMessage(`Invitation sent to ${guest.first_name} ${guest.last_name}`);
      fetchData(); // Refresh to update invitation status

    } catch (err: any) {
      console.error('Error sending invitation:', err);
      if (err.response?.data?.error?.includes('Email credentials')) {
        setError('Email sending failed. Please configure email credentials in the server environment variables.');
      } else {
        setError(err.response?.data?.error || err.response?.data?.message || 'Failed to send invitation');
      }
    }
  };

  // Render attendance status icon
  const renderAttendanceStatus = (status: string) => {
    switch (status) {
      case 'attending':
        return <Check size={16} className="text-green-500" />;
      case 'pending':
        return <Clock size={16} className="text-amber-500" />;
      case 'declined':
        return <X size={16} className="text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div>
      {/* Groups and their guests */}
      {guestsByGroup.map(group => (
        <div key={group.group_id} className="mb-6">
          {/* Group header */}
          <div className="flex justify-between items-center mb-2 border-b pb-2">
            <div className="flex items-center">
              <h3 className="font-semibold text-lg text-black">{group.name}</h3>
              <span className="ml-2 text-gray-500 text-sm">{group.guest_count}</span>
            </div>
            <div className="flex items-center">
              <button
                onClick={() => {
                  setSelectedGroup(group);
                  setShowAddGuestModal(true);
                }}
                className="text-[#B31B1E] mr-2 p-1 hover:bg-red-50 rounded"
              >
                <Plus size={16} />
              </button>
              <button
                onClick={() => {
                  setEditingGroup(group);
                  setShowAddGroupModal(true);
                }}
                className="text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => {
                  setSelectedGroup(group);
                  setShowDeleteGroupConfirm(true);
                }}
                className="text-gray-500 p-1 hover:bg-gray-100 rounded"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>

          {/* Guest list for this group */}
          {group.guests.length > 0 ? (
            <div className="space-y-2">
              {group.guests.map(guest => (
                <div key={guest.guest_id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                  <div className="flex-1 text-black">
                    {guest.first_name} {guest.last_name}
                  </div>

                  {/* Attendance dropdown */}
                  <div className="flex items-center mx-2">
                    <div className="relative group">
                      <button className="flex items-center space-x-1 p-1 rounded hover:bg-gray-100">
                        {renderAttendanceStatus(guest.attendance_status)}
                        <span className="text-sm text-gray-700 capitalize">{guest.attendance_status}</span>
                      </button>
                      <div className="absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-10 hidden group-hover:block">
                        <button
                          onClick={() => updateGuestAttendance(guest, 'attending')}
                          className="flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100"
                        >
                          <Check size={16} className="text-green-500 mr-2" />
                          <span>Attending</span>
                        </button>
                        <button
                          onClick={() => updateGuestAttendance(guest, 'pending')}
                          className="flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100"
                        >
                          <Clock size={16} className="text-amber-500 mr-2" />
                          <span>Pending</span>
                        </button>
                        <button
                          onClick={() => updateGuestAttendance(guest, 'declined')}
                          className="flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100"
                        >
                          <X size={16} className="text-red-500 mr-2" />
                          <span>Declined</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Menu dropdown */}
                  <div className="flex items-center mx-2">
                    <div className="relative group">
                      <button className="flex items-center space-x-1 p-1 rounded hover:bg-gray-100">
                        <span className="text-sm text-gray-700">{guest.menu_name || 'Select menu'}</span>
                      </button>
                      <div className="absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-10 hidden group-hover:block">
                        {menuOptions.map(menu => (
                          <button
                            key={menu.menu_id}
                            onClick={() => updateGuestMenu(guest, menu.menu_id)}
                            className="w-full px-4 py-2 text-left text-black hover:bg-gray-100"
                          >
                            {menu.name}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center">
                    {guest.email && (
                      <button
                        onClick={() => sendInvitation(guest)}
                        className={`mr-2 p-1 hover:bg-gray-100 rounded relative ${guest.invitation_sent ? 'text-green-500' : 'text-gray-500'}`}
                        title={guest.invitation_sent ? 'Invitation sent - Click to resend' : 'Send invitation'}
                      >
                        <Mail size={16} />
                        {guest.invitation_sent && (
                          <span className="absolute w-2 h-2 bg-green-500 rounded-full top-0 right-0"></span>
                        )}
                      </button>
                    )}
                    <button
                      onClick={() => {
                        setSelectedGuest(guest);
                        setShowAddGuestModal(true);
                      }}
                      className="text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded"
                      title="Edit guest"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => {
                        setSelectedGuest(guest);
                        setShowDeleteGuestConfirm(true);
                      }}
                      className="text-gray-500 p-1 hover:bg-gray-100 rounded"
                      title="Delete guest"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 italic p-2">No guests in this group</div>
          )}
        </div>
      ))}

      {/* Delete Group Confirmation Modal */}
      {showDeleteGroupConfirm && selectedGroup && (
        <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
          <div
            className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
            style={{
              background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
              boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
            }}
          >
            <h3 className="text-xl font-bold mb-4 text-black">Delete Group</h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete the group "{selectedGroup.name}"?
              This will also delete all guests in this group.
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowDeleteGroupConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteGroup(selectedGroup.group_id)}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Guest Confirmation Modal */}
      {showDeleteGuestConfirm && selectedGuest && (
        <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
          <div
            className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
            style={{
              background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
              boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
            }}
          >
            <h3 className="text-xl font-bold mb-4 text-black">Delete Guest</h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete {selectedGuest.first_name} {selectedGuest.last_name}?
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowDeleteGuestConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteGuest(selectedGuest.guest_id)}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add/Edit Guest Modal */}
      {showAddGuestModal && (
        <AddGuestModal
          groups={groups}
          menuOptions={menuOptions}
          onClose={() => {
            setShowAddGuestModal(false);
            setSelectedGuest(null);
            setSelectedGroup(null);
          }}
          onSave={fetchData}
          guest={selectedGuest}
          defaultGroupId={selectedGroup?.group_id}
          setError={setError}
          setSuccessMessage={setSuccessMessage}
        />
      )}

      {/* Add/Edit Group Modal */}
      {showAddGroupModal && (
        <AddGroupModal
          onClose={() => {
            setShowAddGroupModal(false);
            setEditingGroup(null);
          }}
          onSave={fetchData}
          group={editingGroup}
          setError={setError}
          setSuccessMessage={setSuccessMessage}
        />
      )}
    </div>
  );
};

export default GroupsView;
