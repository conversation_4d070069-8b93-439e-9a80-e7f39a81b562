"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/swr";
exports.ids = ["vendor-chunks/swr"];
exports.modules = {

/***/ "(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ noop),\n/* harmony export */   B: () => (/* binding */ isPromiseLike),\n/* harmony export */   I: () => (/* binding */ IS_REACT_LEGACY),\n/* harmony export */   O: () => (/* binding */ OBJECT),\n/* harmony export */   S: () => (/* binding */ SWRConfigContext),\n/* harmony export */   U: () => (/* binding */ UNDEFINED),\n/* harmony export */   a: () => (/* binding */ isFunction),\n/* harmony export */   b: () => (/* binding */ SWRGlobalState),\n/* harmony export */   c: () => (/* binding */ cache),\n/* harmony export */   d: () => (/* binding */ defaultConfig),\n/* harmony export */   e: () => (/* binding */ isUndefined),\n/* harmony export */   f: () => (/* binding */ mergeConfigs),\n/* harmony export */   g: () => (/* binding */ SWRConfig),\n/* harmony export */   h: () => (/* binding */ initCache),\n/* harmony export */   i: () => (/* binding */ isWindowDefined),\n/* harmony export */   j: () => (/* binding */ mutate),\n/* harmony export */   k: () => (/* binding */ compare),\n/* harmony export */   l: () => (/* binding */ stableHash),\n/* harmony export */   m: () => (/* binding */ mergeObjects),\n/* harmony export */   n: () => (/* binding */ internalMutate),\n/* harmony export */   o: () => (/* binding */ getTimestamp),\n/* harmony export */   p: () => (/* binding */ preset),\n/* harmony export */   q: () => (/* binding */ defaultConfigOptions),\n/* harmony export */   r: () => (/* binding */ IS_SERVER),\n/* harmony export */   s: () => (/* binding */ serialize),\n/* harmony export */   t: () => (/* binding */ rAF),\n/* harmony export */   u: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   v: () => (/* binding */ slowConnection),\n/* harmony export */   w: () => (/* binding */ isDocumentDefined),\n/* harmony export */   x: () => (/* binding */ isLegacyDeno),\n/* harmony export */   y: () => (/* binding */ hasRequestAnimationFrame),\n/* harmony export */   z: () => (/* binding */ createCacheHelper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var dequal_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal/lite */ \"(ssr)/./node_modules/dequal/lite/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ A,B,I,O,S,U,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z auto */ \n\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = \"undefined\" != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\nconst IS_REACT_LEGACY = !react__WEBPACK_IMPORTED_MODULE_0__.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (!/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](_events_mjs__WEBPACK_IMPORTED_MODULE_2__.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal_lite__WEBPACK_IMPORTED_MODULE_1__.dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, preset);\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\nconst SWRConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[config]\": ()=>isFunctionalConfig ? value(parentConfig) : value\n    }[\"SWRConfig.useMemo[config]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[extendedConfig]\": ()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config)\n    }[\"SWRConfig.useMemo[extendedConfig]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect({\n        \"SWRConfig.useIsomorphicLayoutEffect\": ()=>{\n            if (cacheContext) {\n                cacheContext[2] && cacheContext[2]();\n                return cacheContext[3];\n            }\n        }\n    }[\"SWRConfig.useIsomorphicLayoutEffect\"], []);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/constants.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/swr/dist/_internal/constants.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* binding */ INFINITE_PREFIX)\n/* harmony export */ });\nconst INFINITE_PREFIX = '$inf$';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2NvbnN0YW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUUyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaXZhc1xcT25lRHJpdmVcXERlc2t0b3BcXGZpbmFsXFxXRURaQVRfXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxzd3JcXGRpc3RcXF9pbnRlcm5hbFxcY29uc3RhbnRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBJTkZJTklURV9QUkVGSVggPSAnJGluZiQnO1xuXG5leHBvcnQgeyBJTkZJTklURV9QUkVGSVggfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/constants.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/events.mjs":
/*!****************************************************!*\
  !*** ./node_modules/swr/dist/_internal/events.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_REVALIDATE_EVENT: () => (/* binding */ ERROR_REVALIDATE_EVENT),\n/* harmony export */   FOCUS_EVENT: () => (/* binding */ FOCUS_EVENT),\n/* harmony export */   MUTATE_EVENT: () => (/* binding */ MUTATE_EVENT),\n/* harmony export */   RECONNECT_EVENT: () => (/* binding */ RECONNECT_EVENT)\n/* harmony export */ });\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2V2ZW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUU4RSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaXZhc1xcT25lRHJpdmVcXERlc2t0b3BcXGZpbmFsXFxXRURaQVRfXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxzd3JcXGRpc3RcXF9pbnRlcm5hbFxcZXZlbnRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBGT0NVU19FVkVOVCA9IDA7XG5jb25zdCBSRUNPTk5FQ1RfRVZFTlQgPSAxO1xuY29uc3QgTVVUQVRFX0VWRU5UID0gMjtcbmNvbnN0IEVSUk9SX1JFVkFMSURBVEVfRVZFTlQgPSAzO1xuXG5leHBvcnQgeyBFUlJPUl9SRVZBTElEQVRFX0VWRU5ULCBGT0NVU19FVkVOVCwgTVVUQVRFX0VWRU5ULCBSRUNPTk5FQ1RfRVZFTlQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/events.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/swr/dist/_internal/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* reexport safe */ _constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX),\n/* harmony export */   IS_REACT_LEGACY: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IS_SERVER: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   OBJECT: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.O),\n/* harmony export */   SWRConfig: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   SWRGlobalState: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   UNDEFINED: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.U),\n/* harmony export */   cache: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   compare: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   createCacheHelper: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   defaultConfig: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   defaultConfigOptions: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   getTimestamp: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   hasRequestAnimationFrame: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   initCache: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   internalMutate: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   isDocumentDefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   isFunction: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   isLegacyDeno: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   isPromiseLike: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.B),\n/* harmony export */   isUndefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   isWindowDefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   mergeConfigs: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   mergeObjects: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   mutate: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   noop: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   preload: () => (/* binding */ preload),\n/* harmony export */   preset: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   rAF: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   revalidateEvents: () => (/* reexport module object */ _events_mjs__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   serialize: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   slowConnection: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   stableHash: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   subscribeCallback: () => (/* binding */ subscribeCallback),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.u),\n/* harmony export */   useSWRConfig: () => (/* binding */ useSWRConfig),\n/* harmony export */   withArgs: () => (/* binding */ withArgs),\n/* harmony export */   withMiddleware: () => (/* binding */ withMiddleware)\n/* harmony export */ });\n/* harmony import */ var _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config-context-client-v7VOFo66.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n\n\n\n// @ts-expect-error\nconst enableDevtools = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.i && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = react__WEBPACK_IMPORTED_MODULE_3__;\n    }\n};\n\nconst normalize = (args)=>{\n    return (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.d, (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.S));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n    const [, , , PRELOAD] = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n            const [, , , PRELOAD] = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n            if (key.startsWith(_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if ((0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/index/index.mjs":
/*!***********************************************!*\
  !*** ./node_modules/swr/dist/index/index.mjs ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   \"default\": () => (/* binding */ useSWR),\n/* harmony export */   mutate: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.j),\n/* harmony export */   preload: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.preload),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize),\n/* harmony export */   useSWRConfig: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.useSWRConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/index.mjs\");\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.b.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.s)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.z)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(fallbackData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(config.fallback) ? _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.m)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? fallback && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.B)(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.o)()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.n)(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.t)(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I && _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.O.defineProperty(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.g, 'defaultValue', {\n    value: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.d\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.withArgs)(useSWRHandler);\n\n// useSWR\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/index/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/infinite/index.mjs":
/*!**************************************************!*\
  !*** ./node_modules/swr/dist/infinite/index.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSWRInfinite),\n/* harmony export */   infinite: () => (/* binding */ infinite),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../index/index.mjs */ \"(ssr)/./node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\");\n/* harmony import */ var _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/constants.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/index.mjs\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst getFirstPageKey = (getKey)=>{\n    return serialize(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// hook where `key` and return type are not like the normal `useSWR` types.\nconst EMPTY_PROMISE = Promise.resolve();\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n        const { cache: cache$1, initialSize = 1, revalidateAll = false, persistSize = false, revalidateFirstPage = true, revalidateOnMount = false, parallel = false } = config;\n        const [, , , PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.b.get(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.c);\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = _internal_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n        const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const size = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache$1,\n            infiniteKey,\n            initialSize\n        ]);\n        (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            cache$1,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const cachedPageSize = get()._l;\n            return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.u)(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache$1\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            const shouldRevalidatePage = get()._r;\n            set({\n                _r: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U\n            });\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.s)(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(pageData) || revalidateFirstPage && !i && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cacheData) || shouldRevalidateOnMount || cacheData && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && (typeof shouldRevalidatePage === 'function' ? shouldRevalidatePage(pageData, pageArg) : shouldFetchPage)) {\n                    const revalidate = async ()=>{\n                        const hasPreloadedRequest = pageKey in PRELOAD;\n                        if (!hasPreloadedRequest) {\n                            pageData = await fn(pageArg);\n                        } else {\n                            const req = PRELOAD[pageKey];\n                            // delete the preload cache key before resolving it\n                            // in case there's an error\n                            delete PRELOAD[pageKey];\n                            // get the page data from the preload cache\n                            pageData = await req;\n                        }\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// eslint-disable-next-line func-names\n        function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === 'boolean' ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false,\n                        _r: options.revalidate\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true,\n                        _r: options.revalidate\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // swr.mutate is always the same reference\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1\n        ]);\n        // Extend the SWR API\n        const setSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n            let size;\n            if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.a)(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == 'number') {\n                size = arg;\n            }\n            if (typeof size != 'number') return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.s)(getKey(i, previousPageData));\n                const [getCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.z)(cache$1, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.U;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.e)(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.withMiddleware)(_index_index_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"], infinite);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/infinite/index.mjs\n");

/***/ })

};
;