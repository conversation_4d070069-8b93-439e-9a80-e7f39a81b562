"use client";

// Metadata is now in a separate file
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON> } from "next/font/google";
import "./globals.css";
import { ClerkProvider } from "@clerk/nextjs";
import { AuthProvider } from "../contexts/AuthContext";
import { UploadProvider } from "../contexts/UploadContexts";
import { GlobalUploadProvider } from "../contexts/GlobalUploadManager";
import { LocationProvider } from "../contexts/LocationContext";
import Script from "next/script";
import { useEffect, useState } from "react";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
// Import camera utils to initialize global camera

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const inter = Inter({ subsets: ["latin"] });

// Metadata is now in metadata.ts

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Create a client
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
        retry: 2,
        refetchOnWindowFocus: true,
      },
    },
  }));

  // Use client-side only rendering for the body content to avoid hydration mismatches
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <ClerkProvider
      appearance={{
        // Add custom appearance options to avoid hydration issues
        variables: {
          colorPrimary: "#b31b1e",
        },
        elements: {
          // Add data-hydration-safe attributes to avoid hydration issues
          rootBox: {
            attributes: {
              "data-hydration-safe": "true",
            },
          },
          card: {
            attributes: {
              "data-hydration-safe": "true",
            },
          },
        },
      }}
      // Use the new redirect props format
      redirectUrl="/auth/callback"
      signInUrl="/"
      signUpUrl="/"
    >
      <html lang="en">
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationWarning // Add this to suppress hydration warnings
        >
          {/* Use a more resilient approach to avoid hydration issues */}
          <div suppressHydrationWarning>
            {!mounted ? (
              <div className="flex flex-col justify-center items-center h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700"></div>
                <div className="text-gray-600 mt-4">
                  Loading your account...
                </div>
              </div>
            ) : null}

            <div style={{ display: mounted ? "block" : "none" }}>
              <QueryClientProvider client={queryClient}>
                <AuthProvider>
                  <LocationProvider>
                    <UploadProvider>
                      <GlobalUploadProvider>
                        {children}
                        <ReactQueryDevtools initialIsOpen={false} />
                      </GlobalUploadProvider>
                    </UploadProvider>
                  </LocationProvider>
                </AuthProvider>
              </QueryClientProvider>
            </div>
          </div>

          {/* Script to sync localStorage token to cookie for middleware */}
          <Script id="sync-token-to-cookie" strategy="afterInteractive">
            {`
              function syncTokenToCookie() {
                try {
                  const token = localStorage.getItem('wedzat_token') || localStorage.getItem('token') || localStorage.getItem('jwt_token');
                  if (token) {
                    document.cookie = 'wedzat_token=' + token + '; path=/; max-age=86400; SameSite=Lax';
                    console.log('Token synced to cookie for middleware');
                  }

                  // Also sync vendor flag
                  const isVendor = localStorage.getItem('is_vendor');
                  if (isVendor === 'true') {
                    document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';
                    console.log('Vendor flag synced to cookie for middleware');
                  }
                } catch (e) {
                  console.error('Error syncing token to cookie:', e);
                }
              }

              // Run on page load
              syncTokenToCookie();

              // Also run when localStorage changes
              window.addEventListener('storage', syncTokenToCookie);
            `}
          </Script>

          {/* Script to clean up browser extension attributes */}
          <Script id="cleanup-extension-attrs" strategy="beforeInteractive">
            {`
              (function() {
                // Run immediately to clean up before React hydration
                function cleanupExtensionAttributes() {
                  // Target common extension attributes
                  const attributesToRemove = [
                    'bis_skin_checked',
                    '__processed_',
                    'data-bis-'
                  ];

                  // Get all elements
                  const allElements = document.querySelectorAll('*');

                  // Remove attributes from each element
                  allElements.forEach(el => {
                    for (let i = 0; i < el.attributes.length; i++) {
                      const attr = el.attributes[i];
                      for (const badAttr of attributesToRemove) {
                        if (attr.name.includes(badAttr)) {
                          el.removeAttribute(attr.name);
                          // Adjust index since we removed an attribute
                          i--;
                          break;
                        }
                      }
                    }
                  });
                }

                // Run immediately
                cleanupExtensionAttributes();

                // Also run after a short delay to catch any late additions
                setTimeout(cleanupExtensionAttributes, 0);
              })();
            `}
          </Script>

          {/* Script to initialize camera and prevent locking */}
          <Script id="init-camera" strategy="afterInteractive">
            {`
              // This script helps ensure the camera is never locked
              // by creating a persistent camera stream
              console.log('Camera initialization script loaded');
            `}
          </Script>

          {/* Botpress Webchat Scripts */}
          <Script src="https://cdn.botpress.cloud/webchat/v2.3/inject.js" strategy="afterInteractive" />
          <Script src="https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js" strategy="afterInteractive" />

          {/* Custom script to handle Botpress webchat interaction */}
          <Script id="botpress-helper" strategy="afterInteractive">
            {`
              // Create a global function to open the Botpress webchat
              window.openBotpressChat = function() {
                // Check if the Botpress webchat is available
                if (window.botpressWebChat) {
                  // Try to send the show event
                  try {
                    window.botpressWebChat.sendEvent({ type: 'show' });
                    console.log('Botpress webchat opened successfully');
                  } catch (error) {
                    console.error('Error opening Botpress webchat:', error);
                    // Fallback: Try to click the webchat button
                    try {
                      const botpressButton = document.querySelector('.bp-widget-button');
                      if (botpressButton) {
                        botpressButton.click();
                        console.log('Clicked Botpress button as fallback');
                      } else {
                        console.error('Could not find Botpress button');
                      }
                    } catch (fallbackError) {
                      console.error('Error with fallback method:', fallbackError);
                    }
                  }
                } else {
                  console.error('Botpress webchat not initialized');
                  // Set a flag to open the chat when it becomes available
                  window.openBotpressChatWhenReady = true;

                  // Check periodically if the webchat becomes available
                  const checkInterval = setInterval(() => {
                    if (window.botpressWebChat) {
                      window.botpressWebChat.sendEvent({ type: 'show' });
                      console.log('Botpress webchat opened after delay');
                      clearInterval(checkInterval);
                      window.openBotpressChatWhenReady = false;
                    }
                  }, 500);

                  // Clear the interval after 10 seconds to avoid infinite checking
                  setTimeout(() => clearInterval(checkInterval), 10000);
                }
              };

              // Check if the webchat is loaded and if we need to open it
              document.addEventListener('DOMContentLoaded', () => {
                // Wait a bit for the webchat to initialize
                setTimeout(() => {
                  if (window.openBotpressChatWhenReady && window.botpressWebChat) {
                    window.botpressWebChat.sendEvent({ type: 'show' });
                    console.log('Botpress webchat opened on load');
                    window.openBotpressChatWhenReady = false;
                  }
                }, 1000);
              });
            `}
          </Script>
        </body>
      </html>
    </ClerkProvider>
  );
}
