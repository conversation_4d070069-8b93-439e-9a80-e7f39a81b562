import os
import json
import jwt
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database and JWT configuration
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
JWT_SECRET = os.getenv('JWT_SECRET')

def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME,
        cursor_factory=RealDictCursor  # Return results as dictionaries
    )

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        token = token.split()[1]
        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

def check_face_verification(event):
    """
    Check if a user has completed face verification
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get face verification status
            cursor.execute("SELECT face_verified FROM users WHERE user_id = %s", (user_id,))
            result = cursor.fetchone()

            if not result:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "face_verified": result['face_verified']
                })
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_photos(event):
    """
    Get photos (photos only, excluding stories) with pagination
    Each page contains 10 items
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM photos
                WHERE photo_subtype = 'post'
            """)
            total_count = cursor.fetchone()['total_count']

            # Get all photos with pagination
            photos_query = """
                SELECT
                    p.photo_id,
                    p.photo_name,
                    p.photo_url,
                    p.photo_description,
                    p.photo_tags,
                    p.photo_subtype,
                    p.created_at,
                    u.name AS user_name,
                    p.user_id,
                    CASE WHEN p.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                    ps.photo_views,
                    ps.photo_likes,
                    ps.photo_comments
                FROM photos p
                JOIN users u ON p.user_id = u.user_id
                LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
                WHERE p.photo_subtype = 'post'
                ORDER BY p.created_at DESC
                LIMIT 10 OFFSET %s
            """

            cursor.execute(photos_query, (user_id, offset))
            photos = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(photos)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "photos": photos,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_photo(event):
    """
    Get individual photo details by photo_id
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get photo ID from path parameters - handle both Lambda and direct calls
        photo_id = None
        if 'pathParameters' in event and event['pathParameters']:
            photo_id = event['pathParameters'].get('photo_id')
        elif 'photo_id' in event:
            photo_id = event['photo_id']

        # Also try to extract from resource path if available
        if not photo_id and 'requestContext' in event:
            resource_path = event['requestContext'].get('resourcePath', '')
            if '/hub/photo/' in resource_path:
                photo_id = resource_path.split('/hub/photo/')[-1]

        if not photo_id:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Photo ID is required"})
            }

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get photo details
            cursor.execute("""
                SELECT
                    p.photo_id,
                    p.photo_name,
                    p.photo_url,
                    p.photo_description,
                    p.photo_tags,
                    p.photo_category,
                    p.created_at,
                    p.user_id,
                    u.name AS username,
                    ps.photo_views,
                    ps.photo_likes,
                    ps.photo_comments,
                    CASE WHEN p.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content
                FROM photos p
                JOIN users u ON p.user_id = u.user_id
                LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
                WHERE p.photo_id = %s
            """, (user_id, photo_id))

            photo = cursor.fetchone()

            if not photo:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Photo not found"})
                }

            # Check if user has liked the photo
            cursor.execute(
                "SELECT 1 FROM likes WHERE content_id = %s AND content_type = 'photo' AND user_id = %s",
                (photo_id, user_id)
            )
            liked = bool(cursor.fetchone())

            # Get recent comments
            cursor.execute("""
                SELECT c.comment_id, c.user_id, u.name AS username, c.comment_text, c.created_at
                FROM comments c
                JOIN users u ON c.user_id = u.user_id
                WHERE c.content_id = %s AND c.content_type = 'photo' AND c.parent_comment_id IS NULL
                ORDER BY c.created_at DESC
                LIMIT 10
            """, (photo_id,))

            comments = [{
                "comment_id": row['comment_id'],
                "user_id": row['user_id'],
                "username": row['username'],
                "comment_text": row['comment_text'],
                "created_at": row['created_at'].isoformat() if row['created_at'] else None
            } for row in cursor.fetchall()]

            # Increment view count if this is not the owner viewing
            if user_id != photo['user_id']:
                try:
                    cursor.execute("BEGIN")

                    # Update view count
                    cursor.execute(
                        "UPDATE photo_stats SET photo_views = photo_views + 1 WHERE photo_id = %s",
                        (photo_id,)
                    )

                    # Record view for analytics
                    cursor.execute(
                        """INSERT INTO views (view_id, content_id, content_type, user_id, view_timestamp, device_info)
                           VALUES (uuid_generate_v4(), %s, 'photo', %s, NOW(), %s)""",
                        (photo_id, user_id, '{}')
                    )

                    cursor.execute("COMMIT")
                except Exception as e:
                    cursor.execute("ROLLBACK")
                    print(f"Error incrementing view count: {str(e)}")

            # Format result using the same pattern as get_photos
            result = {
                "photo_id": photo['photo_id'],
                "photo_name": photo['photo_name'],
                "photo_url": photo['photo_url'],
                "photo_description": photo['photo_description'],
                "photo_tags": photo['photo_tags'],
                "photo_category": photo['photo_category'],
                "created_at": photo['created_at'].isoformat() if photo['created_at'] else None,
                "user_id": photo['user_id'],
                "username": photo['username'],
                "photo_views": photo['photo_views'] or 0,
                "photo_likes": photo['photo_likes'] or 0,
                "photo_comments": photo['photo_comments'] or 0,
                "liked": liked,
                "comments": comments,
                "is_own_content": photo['is_own_content']
            }

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(result, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_my_stories(event):
    """
    Get all stories (videos and photos) for the authenticated user without pagination
    Returns all stories that belong to the user
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get user's own stories (both videos and photos) without pagination
            user_stories_query = """
                (
                    SELECT
                        v.video_id AS content_id,
                        v.video_name AS content_name,
                        v.video_url AS content_url,
                        v.video_description AS content_description,
                        v.video_thumbnail AS thumbnail_url,
                        v.video_duration AS duration,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        'video' AS content_type,
                        TRUE AS is_own_content
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    WHERE v.video_subtype = 'story' AND v.user_id = %s
                )
                UNION ALL
                (
                    SELECT
                        p.photo_id AS content_id,
                        p.photo_name AS content_name,
                        p.photo_url AS content_url,
                        p.photo_description AS content_description,
                        NULL AS thumbnail_url,
                        NULL AS duration,
                        p.created_at,
                        u.name AS user_name,
                        p.user_id,
                        'photo' AS content_type,
                        TRUE AS is_own_content
                    FROM photos p
                    JOIN users u ON p.user_id = u.user_id
                    WHERE p.photo_subtype = 'story' AND p.user_id = %s
                )
                ORDER BY created_at DESC
            """

            # Execute query to get all user's stories
            cursor.execute(user_stories_query, (user_id, user_id))
            user_stories = cursor.fetchall()

            # Get total count of user's stories
            total_count = len(user_stories)

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "stories": user_stories,
                    "total_count": total_count
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_stories(event):
    """
    Get stories (videos or photos) with pagination, showing user's stories first
    Each page contains 10 items
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count of stories for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM (
                    SELECT created_at FROM videos WHERE video_subtype = 'story'
                    UNION ALL
                    SELECT created_at FROM photos WHERE photo_subtype = 'story'
                ) AS combined_stories
            """)
            total_count = cursor.fetchone()['total_count']

            # First get the user's own stories (both videos and photos)
            user_stories_query = """
                (
                    SELECT
                        v.video_id AS content_id,
                        v.video_name AS content_name,
                        v.video_url AS content_url,
                        v.video_description AS content_description,
                        v.video_thumbnail AS thumbnail_url,
                        v.video_duration AS duration,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        'video' AS content_type,
                        TRUE AS is_own_content
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    WHERE v.video_subtype = 'story' AND v.user_id = %s
                )
                UNION ALL
                (
                    SELECT
                        p.photo_id AS content_id,
                        p.photo_name AS content_name,
                        p.photo_url AS content_url,
                        p.photo_description AS content_description,
                        NULL AS thumbnail_url,
                        NULL AS duration,
                        p.created_at,
                        u.name AS user_name,
                        p.user_id,
                        'photo' AS content_type,
                        TRUE AS is_own_content
                    FROM photos p
                    JOIN users u ON p.user_id = u.user_id
                    WHERE p.photo_subtype = 'story' AND p.user_id = %s
                )
                ORDER BY created_at DESC
            """

            # Then get all other stories (both videos and photos)
            other_stories_query = """
                (
                    SELECT
                        v.video_id AS content_id,
                        v.video_name AS content_name,
                        v.video_url AS content_url,
                        v.video_description AS content_description,
                        v.video_thumbnail AS thumbnail_url,
                        v.video_duration AS duration,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        'video' AS content_type,
                        FALSE AS is_own_content
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    WHERE v.video_subtype = 'story' AND v.user_id != %s
                )
                UNION ALL
                (
                    SELECT
                        p.photo_id AS content_id,
                        p.photo_name AS content_name,
                        p.photo_url AS content_url,
                        p.photo_description AS content_description,
                        NULL AS thumbnail_url,
                        NULL AS duration,
                        p.created_at,
                        u.name AS user_name,
                        p.user_id,
                        'photo' AS content_type,
                        FALSE AS is_own_content
                    FROM photos p
                    JOIN users u ON p.user_id = u.user_id
                    WHERE p.photo_subtype = 'story' AND p.user_id != %s
                )
                ORDER BY created_at DESC
                LIMIT 10 OFFSET %s
            """

            # Get user's own stories (no limit or offset for these)
            cursor.execute(user_stories_query, (user_id, user_id))
            user_stories = cursor.fetchall()

            # Determine how many other stories to fetch
            remaining_slots = 10 - len(user_stories)

            other_stories = []
            if remaining_slots > 0:
                # Adjust offset to account for user's own stories
                adjusted_offset = max(0, offset - (total_count - remaining_slots))
                cursor.execute(other_stories_query, (user_id, user_id, adjusted_offset))
                other_stories = cursor.fetchall()

            # Combine the results
            all_stories = list(user_stories) + list(other_stories)

            # Prepare final response
            has_next_page = (offset + len(all_stories)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "stories": all_stories,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

# def get_flashes(event):
#     """
#     Get flashes (videos only) with pagination
#     Each page contains 10 items
#     """
#     try:
#         # Validate token
#         user_id, error = validate_token(event.get('headers', {}))
#         if error:
#             return error

#         # Get page number from query parameters
#         query_params = event.get('queryStringParameters', {}) or {}
#         page = int(query_params.get('page', 1))
#         if page < 1:
#             page = 1

#         # Calculate offset
#         offset = (page - 1) * 10

#         # Connect to database
#         conn = get_db_connection()
#         cursor = conn.cursor()

#         try:
#             # Get total count for pagination
#             cursor.execute("""
#                 SELECT COUNT(*) as total_count
#                 FROM videos
#                 WHERE video_subtype = 'flash'
#             """)
#             total_count = cursor.fetchone()['total_count']

#             # Get all flashes with pagination
#             flashes_query = """
#                 SELECT
#                     v.video_id,
#                     v.video_name,
#                     v.video_url,
#                     v.video_description,
#                     v.video_tags,
#                     v.video_thumbnail,
#                     v.video_duration,
#                     v.video_category,
#                     v.created_at,
#                     u.name AS user_name,
#                     v.user_id,
#                     CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
#                     vs.video_views,
#                     vs.video_likes,
#                     vs.video_comments
#                 FROM videos v
#                 JOIN users u ON v.user_id = u.user_id
#                 LEFT JOIN video_stats vs ON v.video_id = vs.video_id
#                 WHERE v.video_subtype = 'flash'
#                 ORDER BY v.created_at DESC
#                 LIMIT 10 OFFSET %s
#             """

#             cursor.execute(flashes_query, (user_id, offset))
#             flashes = cursor.fetchall()

#             # Prepare final response
#             has_next_page = (offset + len(flashes)) < total_count

#             return {
#                 'statusCode': 200,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({
#                     "flashes": flashes,
#                     "next_page": has_next_page,
#                     "total_count": total_count,
#                     "current_page": page
#                 }, cls=CustomJSONEncoder)
#             }

#         except Exception as e:
#             return {
#                 'statusCode': 500,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({"error": f"Database error: {str(e)}"})
#             }
#         finally:
#             cursor.close()
#             conn.close()

#     except Exception as e:
#         return {
#             'statusCode': 500,
#             'headers': {
#                 "Access-Control-Allow-Origin": "*",
#                 "Access-Control-Allow-Methods": "OPTIONS, GET",
#                 "Access-Control-Allow-Headers": "Content-Type, Authorization"
#             },
#             'body': json.dumps({"error": f"Internal server error: {str(e)}"})
#         }

# def get_glimpses(event):
#     """
#     Get glimpses (videos only) with pagination
#     Each page contains 10 items
#     """
#     try:
#         # Validate token
#         user_id, error = validate_token(event.get('headers', {}))
#         if error:
#             return error

#         # Get page number from query parameters
#         query_params = event.get('queryStringParameters', {}) or {}
#         page = int(query_params.get('page', 1))
#         if page < 1:
#             page = 1

#         # Calculate offset
#         offset = (page - 1) * 10

#         # Connect to database
#         conn = get_db_connection()
#         cursor = conn.cursor()

#         try:
#             # Get total count for pagination
#             cursor.execute("""
#                 SELECT COUNT(*) as total_count
#                 FROM videos
#                 WHERE video_subtype = 'glimpse'
#             """)
#             total_count = cursor.fetchone()['total_count']

#             # Get all glimpses with pagination
#             glimpses_query = """
#                 SELECT
#                     v.video_id,
#                     v.video_name,
#                     v.video_url,
#                     v.video_description,
#                     v.video_tags,
#                     v.video_thumbnail,
#                     v.video_duration,
#                     v.video_category,
#                     v.created_at,
#                     u.name AS user_name,
#                     v.user_id,
#                     CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
#                     vs.video_views,
#                     vs.video_likes,
#                     vs.video_comments
#                 FROM videos v
#                 JOIN users u ON v.user_id = u.user_id
#                 LEFT JOIN video_stats vs ON v.video_id = vs.video_id
#                 WHERE v.video_subtype = 'glimpse'
#                 ORDER BY v.created_at DESC
#                 LIMIT 10 OFFSET %s
#             """

#             cursor.execute(glimpses_query, (user_id, offset))
#             glimpses = cursor.fetchall()

#             # Prepare final response
#             has_next_page = (offset + len(glimpses)) < total_count

#             return {
#                 'statusCode': 200,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({
#                     "glimpses": glimpses,
#                     "next_page": has_next_page,
#                     "total_count": total_count,
#                     "current_page": page
#                 }, cls=CustomJSONEncoder)
#             }

#         except Exception as e:
#             return {
#                 'statusCode': 500,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({"error": f"Database error: {str(e)}"})
#             }
#         finally:
#             cursor.close()
#             conn.close()

#     except Exception as e:
#         return {
#             'statusCode': 500,
#             'headers': {
#                 "Access-Control-Allow-Origin": "*",
#                 "Access-Control-Allow-Methods": "OPTIONS, GET",
#                 "Access-Control-Allow-Headers": "Content-Type, Authorization"
#             },
#             'body': json.dumps({"error": f"Internal server error: {str(e)}"})
#         }

# def get_movies(event):
#     """
#     Get movies (videos only) with pagination
#     Each page contains 10 items
#     """
#     try:
#         # Validate token
#         user_id, error = validate_token(event.get('headers', {}))
#         if error:
#             return error

#         # Get page number from query parameters
#         query_params = event.get('queryStringParameters', {}) or {}
#         page = int(query_params.get('page', 1))
#         if page < 1:
#             page = 1

#         # Calculate offset
#         offset = (page - 1) * 10

#         # Connect to database
#         conn = get_db_connection()
#         cursor = conn.cursor()

#         try:
#             # Get total count for pagination
#             cursor.execute("""
#                 SELECT COUNT(*) as total_count
#                 FROM videos
#                 WHERE video_subtype = 'movie'
#             """)
#             total_count = cursor.fetchone()['total_count']

#             # Get all movies with pagination
#             movies_query = """
#                 SELECT
#                     v.video_id,
#                     v.video_name,
#                     v.video_url,
#                     v.video_description,
#                     v.video_tags,
#                     v.video_thumbnail,
#                     v.video_duration,
#                     v.video_category,
#                     v.created_at,
#                     u.name AS user_name,
#                     v.user_id,
#                     CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
#                     vs.video_views,
#                     vs.video_likes,
#                     vs.video_comments
#                 FROM videos v
#                 JOIN users u ON v.user_id = u.user_id
#                 LEFT JOIN video_stats vs ON v.video_id = vs.video_id
#                 WHERE v.video_subtype = 'movie'
#                 ORDER BY v.created_at DESC
#                 LIMIT 10 OFFSET %s
#             """

#             cursor.execute(movies_query, (user_id, offset))
#             movies = cursor.fetchall()

#             # Prepare final response
#             has_next_page = (offset + len(movies)) < total_count

#             return {
#                 'statusCode': 200,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({
#                     "movies": movies,
#                     "next_page": has_next_page,
#                     "total_count": total_count,
#                     "current_page": page
#                 }, cls=CustomJSONEncoder)
#             }

#         except Exception as e:
#             return {
#                 'statusCode': 500,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({"error": f"Database error: {str(e)}"})
#             }
#         finally:
#             cursor.close()
#             conn.close()

#     except Exception as e:
#         return {
#             'statusCode': 500,
#             'headers': {
#                 "Access-Control-Allow-Origin": "*",
#                 "Access-Control-Allow-Methods": "OPTIONS, GET",
#                 "Access-Control-Allow-Headers": "Content-Type, Authorization"
#             },
#             'body': json.dumps({"error": f"Internal server error: {str(e)}"})
#         }

def get_flashes(event):
    """
    Get flashes (videos only) with pagination
    Each page contains 10 items
    Location-matching videos appear first, then other videos to fill the page
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number and location from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        location = query_params.get('location', '').strip()
        
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count for pagination (all videos regardless of location)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM videos
                WHERE video_subtype = 'flash'
            """)
            total_count = cursor.fetchone()['total_count']

            # Build the main query with location prioritization
            if location:
                flashes_query = """
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments,
                        mpd.place AS location,
                        CASE 
                            WHEN mpd.place IS NOT NULL AND LOWER(mpd.place) LIKE LOWER(%s) THEN 0 
                            ELSE 1 
                        END AS location_priority
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    WHERE v.video_subtype = 'flash'
                    ORDER BY 
                        location_priority ASC,
                        v.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(flashes_query, (user_id, f"%{location}%", offset))
            else:
                # No location filter - standard query
                flashes_query = """
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments,
                        mpd.place AS location
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    WHERE v.video_subtype = 'flash'
                    ORDER BY v.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(flashes_query, (user_id, offset))

            flashes = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(flashes)) < total_count

            response_data = {
                "flashes": flashes,
                "next_page": has_next_page,
                "total_count": total_count,
                "current_page": page
            }
            
            # Add location info if filtered
            if location:
                response_data["filtered_by_location"] = location
                # Count how many location-matching videos are in this page
                location_matches = sum(1 for flash in flashes 
                                     if flash.get('location') and 
                                     location.lower() in flash['location'].lower())
                response_data["location_matches_in_page"] = location_matches

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(response_data, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_glimpses(event):
    """
    Get glimpses (videos only) with pagination
    Each page contains 10 items
    Location-matching videos appear first, then other videos to fill the page
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number and location from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        location = query_params.get('location', '').strip()
        
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count for pagination (all videos regardless of location)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM videos
                WHERE video_subtype = 'glimpse'
            """)
            total_count = cursor.fetchone()['total_count']

            # Build the main query with location prioritization
            if location:
                glimpses_query = """
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments,
                        mpd.place AS location,
                        CASE 
                            WHEN mpd.place IS NOT NULL AND LOWER(mpd.place) LIKE LOWER(%s) THEN 0 
                            ELSE 1 
                        END AS location_priority
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    WHERE v.video_subtype = 'glimpse'
                    ORDER BY 
                        location_priority ASC,
                        v.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(glimpses_query, (user_id, f"%{location}%", offset))
            else:
                # No location filter - standard query
                glimpses_query = """
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments,
                        mpd.place AS location
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    WHERE v.video_subtype = 'glimpse'
                    ORDER BY v.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(glimpses_query, (user_id, offset))

            glimpses = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(glimpses)) < total_count

            response_data = {
                "glimpses": glimpses,
                "next_page": has_next_page,
                "total_count": total_count,
                "current_page": page
            }
            
            # Add location info if filtered
            if location:
                response_data["filtered_by_location"] = location
                # Count how many location-matching videos are in this page
                location_matches = sum(1 for glimpse in glimpses 
                                     if glimpse.get('location') and 
                                     location.lower() in glimpse['location'].lower())
                response_data["location_matches_in_page"] = location_matches

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(response_data, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_movies(event):
    """
    Get movies (videos only) with pagination
    Each page contains 10 items
    Location-matching videos appear first, then other videos to fill the page
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number and location from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        location = query_params.get('location', '').strip()
        
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count for pagination (all videos regardless of location)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM videos
                WHERE video_subtype = 'movie'
            """)
            total_count = cursor.fetchone()['total_count']

            # Build the main query with location prioritization
            if location:
                movies_query = """
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments,
                        mpd.place AS location,
                        CASE 
                            WHEN mpd.place IS NOT NULL AND LOWER(mpd.place) LIKE LOWER(%s) THEN 0 
                            ELSE 1 
                        END AS location_priority
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    WHERE v.video_subtype = 'movie'
                    ORDER BY 
                        location_priority ASC,
                        v.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(movies_query, (user_id, f"%{location}%", offset))
            else:
                # No location filter - standard query
                movies_query = """
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments,
                        mpd.place AS location
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    WHERE v.video_subtype = 'movie'
                    ORDER BY v.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(movies_query, (user_id, offset))

            movies = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(movies)) < total_count

            response_data = {
                "movies": movies,
                "next_page": has_next_page,
                "total_count": total_count,
                "current_page": page
            }
            
            # Add location info if filtered
            if location:
                response_data["filtered_by_location"] = location
                # Count how many location-matching videos are in this page
                location_matches = sum(1 for movie in movies 
                                     if movie.get('location') and 
                                     location.lower() in movie['location'].lower())
                response_data["location_matches_in_page"] = location_matches

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(response_data, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_vendor_details(event):
    """
    Get vendor details for a specific video
    Returns vendor information that was entered during upload
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get video_id from path parameters
        path_params = event.get('pathParameters', {}) or {}
        video_id = path_params.get('video_id')

        if not video_id:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "video_id is required"})
            }

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get video metadata including vendor details
            video_query = """
                SELECT
                    v.video_id,
                    v.video_name,
                    v.video_description,
                    mvd.venue_name,
                    mvd.venue_contact,
                    mvd.photographer_name,
                    mvd.photographer_contact,
                    mvd.makeup_artist_name,
                    mvd.makeup_artist_contact,
                    mvd.decoration_name,
                    mvd.decoration_contact,
                    mvd.caterer_name,
                    mvd.caterer_contact,
                    mvd.additional_vendors,
                    v.created_at,
                    u.name AS user_name,
                    v.user_id
                FROM videos v
                JOIN users u ON v.user_id = u.user_id
                LEFT JOIN media_vendor_details mvd ON v.video_id = mvd.media_id AND mvd.media_type = 'video'
                WHERE v.video_id = %s
            """

            cursor.execute(video_query, (video_id,))
            video_data = cursor.fetchone()

            if not video_data:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Video not found"})
                }

            # Format vendor details
            vendor_details = {
                "venue": {
                    "name": video_data.get("venue_name"),
                    "contact": video_data.get("venue_contact")
                },
                "photographer": {
                    "name": video_data.get("photographer_name"),
                    "contact": video_data.get("photographer_contact")
                },
                "makeup_artist": {
                    "name": video_data.get("makeup_artist_name"),
                    "contact": video_data.get("makeup_artist_contact")
                },
                "decoration": {
                    "name": video_data.get("decoration_name"),
                    "contact": video_data.get("decoration_contact")
                },
                "caterer": {
                    "name": video_data.get("caterer_name"),
                    "contact": video_data.get("caterer_contact")
                },
                "additional_vendors": video_data.get("additional_vendors")
            }

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "vendor_details": vendor_details,
                    "video_id": video_id,
                    "video_name": video_data.get("video_name"),
                    "user_name": video_data.get("user_name"),
                    "user_id": video_data.get("user_id")
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

# Custom JSON encoder to handle date objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        from datetime import date, datetime
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)