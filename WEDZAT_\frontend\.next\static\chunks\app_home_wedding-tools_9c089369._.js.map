{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/types.ts"], "sourcesContent": ["// Define interfaces for checklist items\r\nexport interface ChecklistItem {\r\n  item_id: string; // API uses item_id instead of id\r\n  task: string;\r\n  status: string; // API uses status instead of completed\r\n  category: string;\r\n  due_date?: string;\r\n  user_id?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\n// Define interfaces for budget items\r\nexport interface BudgetCategory {\r\n  category_id: string;\r\n  name: string;\r\n  user_id?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\nexport interface BudgetExpense {\r\n  expense_id: string;\r\n  category_id: string;\r\n  name: string;\r\n  estimated_budget: number;\r\n  final_cost: number;\r\n  amount_paid: number;\r\n  notes?: string;\r\n  user_id?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n  category_name?: string; // Added by API response\r\n}\r\n\r\nexport interface BudgetPayment {\r\n  payment_id: string;\r\n  expense_id: string;\r\n  amount: number;\r\n  payment_date: string;\r\n  payment_method?: string;\r\n  notes?: string;\r\n  user_id?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n  expense_name?: string; // Added by API response\r\n  category_name?: string; // Added by API response\r\n  paid_by: string;\r\n}\r\n\r\nexport interface BudgetTotals {\r\n  total_estimated: number;\r\n  total_final: number;\r\n  total_paid: number;\r\n}\r\n\r\n// Define interfaces for guestlist items\r\nexport interface GuestGroup {\r\n  group_id: string;\r\n  name: string;\r\n  guest_count?: number;\r\n  user_id?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\nexport interface Guest {\r\n  guest_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email?: string;\r\n  phone?: string;\r\n  address?: string;\r\n  group_id?: string;\r\n  group_name?: string;\r\n  age_category: 'adult' | 'child' | 'baby';\r\n  attendance_status: 'attending' | 'pending' | 'declined';\r\n  menu_choice?: string;\r\n  notes?: string;\r\n  user_id?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\nexport interface GuestStatistics {\r\n  total_guests: number;\r\n  attending: number;\r\n  pending: number;\r\n  declined: number;\r\n  adults: number;\r\n  children: number;\r\n  babies: number;\r\n}\r\n\r\nexport interface Invitation {\r\n  invitation_id: string;\r\n  title: string;\r\n  message?: string;\r\n  wedding_date?: string;\r\n  wedding_location?: string;\r\n  image_url?: string;\r\n  website_url?: string;\r\n  rsvp_enabled: boolean;\r\n  user_id?: string;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\n// Helper functions\r\nexport const isCompleted = (status: string): boolean => {\r\n  return status === 'completed';\r\n};\r\n\r\nexport const getStatus = (completed: boolean): string => {\r\n  return completed ? 'completed' : 'pending';\r\n};\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AA6GjC,MAAM,cAAc,CAAC;IAC1B,OAAO,WAAW;AACpB;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,YAAY,cAAc;AACnC", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/utils.ts"], "sourcesContent": ["// Get authentication token\r\nexport const getAuthToken = (): string | null => {\r\n  if (typeof window !== 'undefined') {\r\n    // Try multiple possible keys\r\n    const token = localStorage.getItem('token') ||\r\n                 localStorage.getItem('jwt_token') ||\r\n                 localStorage.getItem('auth_token') ||\r\n                 localStorage.getItem('wedzat_token');\r\n\r\n    // Log token for debugging\r\n    console.log('Auth token retrieved:', token ? `${token.substring(0, 10)}...` : 'null');\r\n\r\n    return token;\r\n  }\r\n  return null;\r\n};\r\n\r\n// Format currency\r\nexport const formatCurrency = (amount: number): string => {\r\n  return `₹ ${amount.toLocaleString('en-IN')}`;\r\n};\r\n\r\n// Format date\r\nexport const formatDate = (dateString: string): string => {\r\n  if (!dateString) return '';\r\n\r\n  const date = new Date(dateString);\r\n  return date.toLocaleDateString('en-IN', {\r\n    day: '2-digit',\r\n    month: '2-digit',\r\n    year: 'numeric'\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;AACpB,MAAM,eAAe;IAC1B,wCAAmC;QACjC,6BAA6B;QAC7B,MAAM,QAAQ,aAAa,OAAO,CAAC,YACtB,aAAa,OAAO,CAAC,gBACrB,aAAa,OAAO,CAAC,iBACrB,aAAa,OAAO,CAAC;QAElC,0BAA0B;QAC1B,QAAQ,GAAG,CAAC,yBAAyB,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;QAE9E,OAAO;IACT;;AAEF;AAGO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAC,EAAE,EAAE,OAAO,cAAc,CAAC,UAAU;AAC9C;AAGO,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/checklist/Checklist.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { Plus, Trash, Edit, Check, X, Download, Printer } from \"lucide-react\";\r\nimport { ChecklistItem, isCompleted } from \"../../types\";\r\nimport { getAuthToken } from \"../../utils\";\r\nimport Image from \"next/image\";\r\n\r\ninterface ChecklistProps {\r\n  setError: (error: string | null) => void;\r\n  setSuccessMessage: (message: string | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  loading: boolean;\r\n  error: string | null;\r\n  successMessage: string | null;\r\n}\r\n\r\nconst Checklist: React.FC<ChecklistProps> = ({\r\n  setError,\r\n  setSuccessMessage,\r\n  setLoading,\r\n  loading,\r\n  error,\r\n  successMessage\r\n}) => {\r\n  // Checklist states\r\n  const [checklistItems, setChecklistItems] = useState<ChecklistItem[]>([]);\r\n  const [editingItem, setEditingItem] = useState<string | null>(null);\r\n  const [editTask, setEditTask] = useState<string>(\"\");\r\n  const [editCategory, setEditCategory] = useState<string>(\"\");\r\n  const [availableCategories, setAvailableCategories] = useState<string[]>([]);\r\n\r\n  // Modal state\r\n  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState<boolean>(false);\r\n  const [newItemTask, setNewItemTask] = useState<string>(\"\");\r\n  const [newItemCategory, setNewItemCategory] = useState<string>(\"general\");\r\n  const [description, setDescription] = useState<string>(\"\");\r\n  const [newCustomCategory, setNewCustomCategory] = useState<string>(\"\");\r\n  const [showCustomCategoryInput, setShowCustomCategoryInput] = useState<boolean>(false);\r\n\r\n  // Filter states\r\n  const [statusFilter, setStatusFilter] = useState<string>(\"all\");\r\n  const [categoryFilter, setCategoryFilter] = useState<string>(\"all\");\r\n  const [completedCount, setCompletedCount] = useState<number>(0);\r\n  const [totalCount, setTotalCount] = useState<number>(0);\r\n\r\n  // Fetch checklist items from the API\r\n  useEffect(() => {\r\n    fetchChecklistItems();\r\n  }, []);\r\n\r\n  const fetchChecklistItems = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const token = getAuthToken();\r\n\r\n      if (!token) {\r\n        console.warn('No authentication token found');\r\n        setError('Authentication required');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      console.log('Fetching checklist items with token:', token);\r\n\r\n      const response = await axios.get(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/checklist',\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          }\r\n        }\r\n      );\r\n\r\n      console.log('API response for checklist items:', response.data);\r\n\r\n      if (response.data && response.data.checklist && Array.isArray(response.data.checklist)) {\r\n        console.log('Found items in response:', response.data.checklist.length);\r\n        const items = response.data.checklist;\r\n        setChecklistItems(items);\r\n\r\n        // Update counts\r\n        const total = items.length;\r\n        const completed = items.filter((item: ChecklistItem) => isCompleted(item.status)).length;\r\n        setTotalCount(total);\r\n        setCompletedCount(completed);\r\n\r\n        // Get categories from API response\r\n        if (response.data.categories && Array.isArray(response.data.categories)) {\r\n          setAvailableCategories(response.data.categories);\r\n        }\r\n\r\n        setError(null); // Clear any previous errors\r\n      } else if (response.data && response.data.checklist === null) {\r\n        // API returned null items, which means no items exist yet\r\n        console.log('API returned null checklist');\r\n        setChecklistItems([]);\r\n        setTotalCount(0);\r\n        setCompletedCount(0);\r\n        setError(null); // Clear any previous errors\r\n      } else if (response.data && response.data.error) {\r\n        // API returned an error message\r\n        console.warn('API returned error:', response.data.error);\r\n        setError(`Failed to load checklist items: ${response.data.error}`);\r\n        setChecklistItems([]);\r\n        setTotalCount(0);\r\n        setCompletedCount(0);\r\n      } else {\r\n        console.warn('Unexpected API response format:', response.data);\r\n        setError('Failed to load checklist items');\r\n        setChecklistItems([]);\r\n        setTotalCount(0);\r\n        setCompletedCount(0);\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error fetching checklist items:', err);\r\n      // Check if the error is about empty items\r\n      if (err.response && err.response.data && err.response.data.error === \"No items found\") {\r\n        setChecklistItems([]);\r\n        setError(null); // This is not really an error, just an empty state\r\n      } else {\r\n        setError('Failed to load checklist items');\r\n        setChecklistItems([]);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add a new checklist item\r\n  const addChecklistItem = async (task: string, description: string, category: string) => {\r\n    if (!task.trim()) return;\r\n\r\n    try {\r\n      const token = getAuthToken();\r\n\r\n      if (!token) {\r\n        console.warn('No authentication token found');\r\n        setError('Authentication required');\r\n        return;\r\n      }\r\n\r\n      // Create a new item with the required fields\r\n      const newItem = {\r\n        task: task,\r\n        category: category,\r\n        status: 'pending', // Initial status is pending\r\n        description: description // Add description if API supports it\r\n      };\r\n\r\n      console.log('Sending new item to API:', newItem);\r\n\r\n      const response = await axios.post(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-checklist-item',\r\n        newItem,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          }\r\n        }\r\n      );\r\n\r\n      console.log('API response for add item:', response.data);\r\n\r\n      if (response.data && response.data.item) {\r\n        // Add the new item to the list\r\n        const updatedItems = [...checklistItems, response.data.item];\r\n        setChecklistItems(updatedItems);\r\n\r\n        // Update counts\r\n        setTotalCount(updatedItems.length);\r\n        setCompletedCount(updatedItems.filter((item: ChecklistItem) => isCompleted(item.status)).length);\r\n\r\n        setError(null); // Clear any previous errors\r\n        setSuccessMessage('Task added successfully'); // Show success message\r\n\r\n        // Clear success message after 3 seconds\r\n        setTimeout(() => {\r\n          setSuccessMessage(null);\r\n        }, 3000);\r\n      } else {\r\n        console.warn('Unexpected API response format:', response.data);\r\n        setError('Failed to add checklist item');\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error adding checklist item:', err);\r\n      if (err.response && err.response.data && err.response.data.error) {\r\n        setError(`Failed to add checklist item: ${err.response.data.error}`);\r\n      } else {\r\n        setError('Failed to add checklist item');\r\n      }\r\n      // Don't update UI on failure\r\n    }\r\n  };\r\n\r\n  // Toggle checklist item completion status\r\n  const toggleChecklistItem = async (id: string) => {\r\n    try {\r\n      const token = getAuthToken();\r\n\r\n      if (!token) {\r\n        console.warn('No authentication token found');\r\n        setError('Authentication required');\r\n        return;\r\n      }\r\n\r\n      const item = checklistItems.find(item => item.item_id === id);\r\n      if (!item) {\r\n        console.warn('Item not found in checklist:', id);\r\n        return;\r\n      }\r\n\r\n      // Create the update payload\r\n      const updatedItem = {\r\n        item_id: id, // Ensure ID is included\r\n        status: isCompleted(item.status) ? 'pending' : 'completed' // Toggle status\r\n      };\r\n\r\n      console.log('Sending update to API:', updatedItem);\r\n\r\n      const response = await axios.put(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-checklist-item',\r\n        updatedItem,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          }\r\n        }\r\n      );\r\n\r\n      console.log('API response for update item:', response.data);\r\n\r\n      if (response.data && response.data.item) {\r\n        // Update the item in the list\r\n        const updatedItems = checklistItems.map((item: ChecklistItem) =>\r\n          item.item_id === id ? { ...item, status: isCompleted(item.status) ? 'pending' : 'completed' } : item\r\n        );\r\n\r\n        setChecklistItems(updatedItems);\r\n\r\n        // Update counts\r\n        setTotalCount(updatedItems.length);\r\n        setCompletedCount(updatedItems.filter((item: ChecklistItem) => isCompleted(item.status)).length);\r\n\r\n        setError(null); // Clear any previous errors\r\n        setSuccessMessage(`Task ${isCompleted(item.status) ? 'marked as pending' : 'marked as completed'}`); // Show success message\r\n\r\n        // Clear success message after 3 seconds\r\n        setTimeout(() => {\r\n          setSuccessMessage(null);\r\n        }, 3000);\r\n      } else {\r\n        console.warn('Unexpected API response format:', response.data);\r\n        setError('Failed to update checklist item');\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error updating checklist item:', err);\r\n      if (err.response && err.response.data && err.response.data.error) {\r\n        setError(`Failed to update checklist item: ${err.response.data.error}`);\r\n      } else {\r\n        setError('Failed to update checklist item');\r\n      }\r\n      // Don't update UI on failure\r\n    }\r\n  };\r\n\r\n  // Delete a checklist item\r\n  const deleteChecklistItem = async (id: string) => {\r\n    try {\r\n      const token = getAuthToken();\r\n\r\n      if (!token) {\r\n        console.warn('No authentication token found');\r\n        setError('Authentication required');\r\n        return;\r\n      }\r\n\r\n      if (!id) {\r\n        console.warn('No item ID provided for deletion');\r\n        setError('Item ID is required for deletion');\r\n        return;\r\n      }\r\n\r\n      console.log('Deleting item with ID:', id);\r\n\r\n      const response = await axios.delete(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-checklist-item',\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          },\r\n          data: { item_id: id }\r\n        }\r\n      );\r\n\r\n      console.log('API response for delete item:', response.data);\r\n\r\n      // The API might return different response formats for success\r\n      // Let's check all possible success indicators\r\n      if (\r\n        (response.data && response.data.success) || // Standard success format\r\n        (response.data && response.data.message && response.data.message.includes('success')) || // Message contains 'success'\r\n        (response.status >= 200 && response.status < 300) // HTTP success status code\r\n      ) {\r\n        // Remove the item from the list\r\n        const updatedItems = checklistItems.filter((item: ChecklistItem) => item.item_id !== id);\r\n        setChecklistItems(updatedItems);\r\n\r\n        // Update counts\r\n        setTotalCount(updatedItems.length);\r\n        setCompletedCount(updatedItems.filter((item: ChecklistItem) => isCompleted(item.status)).length);\r\n\r\n        setError(null); // Clear any previous errors\r\n        setSuccessMessage('Task deleted successfully'); // Show success message\r\n\r\n        // Clear success message after 3 seconds\r\n        setTimeout(() => {\r\n          setSuccessMessage(null);\r\n        }, 3000);\r\n      } else {\r\n        console.warn('Unexpected API response format:', response.data);\r\n        setError('Failed to delete checklist item');\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error deleting checklist item:', err);\r\n      if (err.response && err.response.data && err.response.data.error) {\r\n        setError(`Failed to delete checklist item: ${err.response.data.error}`);\r\n      } else {\r\n        setError('Failed to delete checklist item');\r\n      }\r\n      // Don't update UI on failure\r\n    }\r\n  };\r\n\r\n  // Start editing a checklist item\r\n  const startEditingItem = (item: ChecklistItem) => {\r\n    setEditingItem(item.item_id);\r\n    setEditTask(item.task);\r\n    setEditCategory(item.category);\r\n  };\r\n\r\n  // Save edited checklist item\r\n  const saveEditedItem = async (id: string) => {\r\n    try {\r\n      const token = getAuthToken();\r\n\r\n      if (!token) {\r\n        console.warn('No authentication token found');\r\n        setError('Authentication required');\r\n        return;\r\n      }\r\n\r\n      if (!id) {\r\n        console.warn('No item ID provided for update');\r\n        setError('Item ID is required for update');\r\n        setEditingItem(null);\r\n        return;\r\n      }\r\n\r\n      const item = checklistItems.find(item => item.item_id === id);\r\n      if (!item) {\r\n        console.warn('Item not found in checklist:', id);\r\n        setEditingItem(null);\r\n        return;\r\n      }\r\n\r\n      // Create the update payload with only the necessary fields\r\n      const updatedItem = {\r\n        item_id: id, // Ensure ID is included\r\n        task: editTask,\r\n        category: editCategory\r\n      };\r\n\r\n      console.log('Sending edit to API:', updatedItem);\r\n\r\n      const response = await axios.put(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-checklist-item',\r\n        updatedItem,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          }\r\n        }\r\n      );\r\n\r\n      console.log('API response for edit item:', response.data);\r\n\r\n      if (response.data && response.data.item) {\r\n        // Update the item in the list\r\n        const updatedItems = checklistItems.map((item: ChecklistItem) =>\r\n          item.item_id === id ? { ...item, task: editTask, category: editCategory } : item\r\n        );\r\n\r\n        setChecklistItems(updatedItems);\r\n        setEditingItem(null);\r\n\r\n        // No need to update counts as we're just changing text, not status\r\n\r\n        setError(null); // Clear any previous errors\r\n        setSuccessMessage('Task updated successfully'); // Show success message\r\n\r\n        // Clear success message after 3 seconds\r\n        setTimeout(() => {\r\n          setSuccessMessage(null);\r\n        }, 3000);\r\n      } else {\r\n        console.warn('Unexpected API response format:', response.data);\r\n        setError('Failed to update checklist item');\r\n        setEditingItem(null);\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error updating checklist item:', err);\r\n      if (err.response && err.response.data && err.response.data.error) {\r\n        setError(`Failed to update checklist item: ${err.response.data.error}`);\r\n      } else {\r\n        setError('Failed to update checklist item');\r\n      }\r\n      // Don't update UI on failure\r\n      setEditingItem(null);\r\n    }\r\n  };\r\n\r\n  // Cancel editing\r\n  const cancelEditing = () => {\r\n    setEditingItem(null);\r\n  };\r\n\r\n  // Filter checklist items based on status and category\r\n  const getFilteredItems = () => {\r\n    return checklistItems.filter((item: ChecklistItem) => {\r\n      // Filter by status\r\n      if (statusFilter !== 'all') {\r\n        if (statusFilter === 'completed' && !isCompleted(item.status)) return false;\r\n        if (statusFilter === 'pending' && isCompleted(item.status)) return false;\r\n      }\r\n\r\n      // Filter by category (case-insensitive)\r\n      if (categoryFilter !== 'all' && item.category.toLowerCase() !== categoryFilter.toLowerCase()) return false;\r\n\r\n      return true;\r\n    });\r\n  };\r\n\r\n  // Get all unique categories from items and available categories\r\n  const getCategories = () => {\r\n    const categories = new Set<string>();\r\n\r\n    // Add categories from checklist items\r\n    checklistItems.forEach((item: ChecklistItem) => {\r\n      if (item.category) categories.add(item.category.toLowerCase());\r\n    });\r\n\r\n    // Add categories from API\r\n    availableCategories.forEach(category => {\r\n      if (category) categories.add(category.toLowerCase());\r\n    });\r\n\r\n    return Array.from(categories);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <h2 className=\"text-2xl font-bold text-black\">Checklist</h2>\r\n        <div className=\"flex items-center gap-2\">\r\n          <button\r\n            onClick={() => window.print()}\r\n            className=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-black hover:bg-gray-50 flex items-center gap-1\"\r\n          >\r\n            <Printer size={14} />\r\n            <span>Print</span>\r\n          </button>\r\n          <button\r\n            onClick={() => alert('Download functionality would go here')}\r\n            className=\"px-3 py-1 border border-gray-300 rounded-md text-sm text-black hover:bg-gray-50 flex items-center gap-1\"\r\n          >\r\n            <Download size={14} />\r\n            <span>Download</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Status section */}\r\n      <div className=\"mb-6\">\r\n        <h3 className=\"text-lg font-semibold mb-2 text-black\">Status</h3>\r\n        <div className=\"bg-gray-100 p-3 rounded-md\">\r\n          <div className=\"text-[#B31B1E] font-medium mb-1\">Completed {completedCount} out of {totalCount}</div>\r\n          <div className=\"h-2 w-full bg-gray-200 rounded-full overflow-hidden\">\r\n            <div\r\n              className=\"h-full bg-[#B31B1E] rounded-full\"\r\n              style={{ width: totalCount > 0 ? `${(completedCount / totalCount) * 100}%` : '0%' }}\r\n            ></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content with categories and checklist */}\r\n      <div className=\"flex flex-col md:flex-row gap-6\">\r\n        {/* Categories section */}\r\n        <div className=\"w-full md:w-1/4\">\r\n          <h3 className=\"text-lg font-semibold mb-4 text-black\">Category</h3>\r\n          <div className=\"space-y-2\">\r\n            <div\r\n              onClick={() => setCategoryFilter('all')}\r\n              className={`p-2 rounded-md cursor-pointer flex justify-between items-center ${categoryFilter === 'all' ? 'bg-[#B31B1E] text-white' : 'bg-gray-100 text-black hover:bg-gray-200'}`}\r\n            >\r\n              <span>All</span>\r\n              <span className=\"text-sm\">{totalCount}</span>\r\n            </div>\r\n\r\n            {getCategories().map(category => (\r\n              <div\r\n                key={category}\r\n                onClick={() => setCategoryFilter(category)}\r\n                className={`p-2 rounded-md cursor-pointer flex justify-between items-center ${categoryFilter === category ? 'bg-[#B31B1E] text-white' : 'bg-gray-100 text-black hover:bg-gray-200'}`}\r\n              >\r\n                <span className=\"capitalize\">{category.charAt(0).toUpperCase() + category.slice(1)}</span>\r\n                <span className=\"text-sm\">\r\n                  {checklistItems.filter(item => item.category.toLowerCase() === category.toLowerCase()).length}\r\n                </span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Checklist section */}\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-black\">Checklist</h3>\r\n            <button\r\n              onClick={() => {\r\n                setIsAddTaskModalOpen(true);\r\n                setNewItemTask(\"\");\r\n                setDescription(\"\");\r\n                setNewItemCategory(\"general\");\r\n                setShowCustomCategoryInput(false);\r\n              }}\r\n              className=\"px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-[#B31B1E] flex items-center gap-1\"\r\n            >\r\n              <Plus size={16} />\r\n              <span>Add New Task</span>\r\n            </button>\r\n          </div>\r\n\r\n      {/* Filter options for status */}\r\n      <div className=\"mb-4 flex flex-wrap gap-2\">\r\n        <div className=\"flex border border-gray-300 rounded-md overflow-hidden\">\r\n          <button\r\n            onClick={() => setStatusFilter('all')}\r\n            className={`px-3 py-1 text-sm ${statusFilter === 'all' ? 'bg-[#B31B1E] text-white' : 'bg-white text-black'}`}\r\n          >\r\n            All\r\n          </button>\r\n          <button\r\n            onClick={() => setStatusFilter('pending')}\r\n            className={`px-3 py-1 text-sm ${statusFilter === 'pending' ? 'bg-[#B31B1E] text-white' : 'bg-white text-black'}`}\r\n          >\r\n            Pending\r\n          </button>\r\n          <button\r\n            onClick={() => setStatusFilter('completed')}\r\n            className={`px-3 py-1 text-sm ${statusFilter === 'completed' ? 'bg-[#B31B1E] text-white' : 'bg-white text-black'}`}\r\n          >\r\n            Completed\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n          {/* Error message */}\r\n          {error && (\r\n            <div className=\"mb-4 p-2 bg-red-100 text-red-700 rounded-md\">\r\n              {error}\r\n            </div>\r\n          )}\r\n\r\n          {/* Success message */}\r\n          {successMessage && (\r\n            <div className=\"mb-4 p-2 bg-green-100 text-green-700 rounded-md\">\r\n              {successMessage}\r\n            </div>\r\n          )}\r\n\r\n          {/* Loading state */}\r\n          {loading ? (\r\n            <div className=\"flex justify-center items-center h-40\">\r\n              <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#B31B1E]\"></div>\r\n            </div>\r\n          ) : (\r\n            /* Checklist items */\r\n            <div className=\"space-y-2\">\r\n              {getFilteredItems().length === 0 ? (\r\n                <p className=\"text-gray-500 text-center py-4\">\r\n                  {checklistItems.length === 0\r\n                    ? \"No items in your checklist yet. Add some tasks above!\"\r\n                    : \"No items match your current filters.\"}\r\n                </p>\r\n              ) : (\r\n                getFilteredItems().map((item) => (\r\n                  <div\r\n                    key={item.item_id}\r\n                    className={`p-3 border rounded-md flex items-center justify-between ${\r\n                      isCompleted(item.status) ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-300'\r\n                    }`}\r\n                  >\r\n                {editingItem === item.item_id ? (\r\n                  /* Editing mode */\r\n                  <div className=\"flex-1 flex items-center gap-2\">\r\n                    <input\r\n                      type=\"text\"\r\n                      value={editTask}\r\n                      onChange={(e) => setEditTask(e.target.value)}\r\n                      className=\"flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black placeholder-gray-700\"\r\n                    />\r\n                    <select\r\n                      value={editCategory}\r\n                      onChange={(e) => {\r\n                        if (e.target.value === \"custom\") {\r\n                          // Handle custom category input\r\n                          const customCategory = prompt(\"Enter custom category name:\");\r\n                          if (customCategory && customCategory.trim()) {\r\n                            setEditCategory(customCategory.trim());\r\n                            if (!availableCategories.includes(customCategory.trim())) {\r\n                              setAvailableCategories([...availableCategories, customCategory.trim()]);\r\n                            }\r\n                          }\r\n                        } else {\r\n                          setEditCategory(e.target.value);\r\n                        }\r\n                      }}\r\n                      className=\"p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                    >\r\n                      {/* All categories from API */}\r\n                      {availableCategories.length > 0 ? (\r\n                        availableCategories.map(category => (\r\n                          <option key={category} value={category} className=\"text-black\">\r\n                            {category.charAt(0).toUpperCase() + category.slice(1)}\r\n                          </option>\r\n                        ))\r\n                      ) : (\r\n                        // Fallback categories if API hasn't loaded yet\r\n                        <>\r\n                          <option value=\"general\" className=\"text-black\">General</option>\r\n                          <option value=\"planning\" className=\"text-black\">Planning</option>\r\n                          <option value=\"venue\" className=\"text-black\">Venue</option>\r\n                          <option value=\"vendors\" className=\"text-black\">Vendors</option>\r\n                          <option value=\"food & drink\" className=\"text-black\">Food & Drink</option>\r\n                          <option value=\"entertainment\" className=\"text-black\">Entertainment</option>\r\n                          <option value=\"attire\" className=\"text-black\">Attire</option>\r\n                          <option value=\"stationery\" className=\"text-black\">Stationery</option>\r\n                          <option value=\"gifts & favors\" className=\"text-black\">Gifts & Favors</option>\r\n                          <option value=\"ceremony\" className=\"text-black\">Ceremony</option>\r\n                          <option value=\"transportation\" className=\"text-black\">Transportation</option>\r\n                          <option value=\"honeymoon\" className=\"text-black\">Honeymoon</option>\r\n                        </>\r\n                      )}\r\n\r\n                      {/* Custom category option */}\r\n                      <option value=\"custom\" className=\"text-black\">+ Add Custom Category</option>\r\n                    </select>\r\n                    <button\r\n                      onClick={() => saveEditedItem(item.item_id)}\r\n                      className=\"p-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500\"\r\n                    >\r\n                      <Check size={16} />\r\n                    </button>\r\n                    <button\r\n                      onClick={cancelEditing}\r\n                      className=\"p-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500\"\r\n                    >\r\n                      <X size={16} />\r\n                    </button>\r\n                  </div>\r\n                ) : (\r\n                  /* Display mode */\r\n                  <>\r\n                    <div className=\"flex items-center gap-3 flex-1\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={isCompleted(item.status)}\r\n                        onChange={() => toggleChecklistItem(item.item_id)}\r\n                        className=\"h-5 w-5 text-[#B31B1E] rounded focus:ring-[#B31B1E]\"\r\n                      />\r\n                      <div className=\"flex flex-col\">\r\n                        <span className={`text-black ${isCompleted(item.status) ? 'line-through text-gray-500' : ''}`}>\r\n                          {item.task}\r\n                        </span>\r\n                        <span className=\"text-xs text-gray-500 capitalize\">{item.category}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <button\r\n                        onClick={() => startEditingItem(item)}\r\n                        className=\"p-1 text-gray-500 hover:text-[#B31B1E] focus:outline-none\"\r\n                      >\r\n                        <Edit size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => deleteChecklistItem(item.item_id)}\r\n                        className=\"p-1 text-gray-500 hover:text-[#B31B1E] focus:outline-none\"\r\n                      >\r\n                        <Trash size={16} />\r\n                      </button>\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </div>\r\n            ))\r\n          )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Add Task Modal */}\r\n      {isAddTaskModalOpen && (\r\n        <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\r\n          <div\r\n            className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\r\n            style={{\r\n              background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\r\n              boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\r\n            }}\r\n          >\r\n            <button\r\n              onClick={() => setIsAddTaskModalOpen(false)}\r\n              className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n\r\n            {/* Logo */}\r\n            <div className=\"flex justify-center pt-6\">\r\n              <div className=\"text-red-600\">\r\n                <Image\r\n                  src=\"/pics/logo.png\"\r\n                  alt=\"Wedzat logo\"\r\n                  width={40}\r\n                  height={40}\r\n                  className=\"object-cover\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"px-6 py-4\">\r\n              <h2 className=\"text-2xl font-bold mb-2 text-center\" style={{ color: \"#B31B1E\" }}>\r\n                Add New Task\r\n              </h2>\r\n              <p className=\"text-sm text-center text-gray-600 mb-6\">\r\n                Add a task to your wedding checklist\r\n              </p>\r\n\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <div className=\"mb-1\">\r\n                    <label htmlFor=\"taskName\" className=\"text-sm font-medium text-gray-700\">\r\n                      Task Name\r\n                    </label>\r\n                  </div>\r\n                  <input\r\n                    id=\"taskName\"\r\n                    type=\"text\"\r\n                    value={newItemTask}\r\n                    onChange={(e) => setNewItemTask(e.target.value)}\r\n                    className=\"w-full p-3 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:border-[#B31B1E]\"\r\n                    placeholder=\"Enter task name\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <div className=\"mb-1\">\r\n                    <label htmlFor=\"description\" className=\"text-sm font-medium text-gray-700\">\r\n                      Description of task\r\n                    </label>\r\n                  </div>\r\n                  <textarea\r\n                    id=\"description\"\r\n                    value={description || \"\"}\r\n                    onChange={(e) => setDescription(e.target.value)}\r\n                    className=\"w-full p-3 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:border-[#B31B1E]\"\r\n                    placeholder=\"Enter task description\"\r\n                    rows={3}\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <div className=\"mb-1\">\r\n                    <label htmlFor=\"category\" className=\"text-sm font-medium text-gray-700\">\r\n                      Category\r\n                    </label>\r\n                  </div>\r\n                  {showCustomCategoryInput ? (\r\n                    <div className=\"flex items-center\">\r\n                      <input\r\n                        type=\"text\"\r\n                        value={newCustomCategory}\r\n                        onChange={(e) => setNewCustomCategory(e.target.value)}\r\n                        className=\"flex-1 p-3 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:border-[#B31B1E]\"\r\n                        placeholder=\"Enter new category\"\r\n                      />\r\n                      <button\r\n                        onClick={() => setShowCustomCategoryInput(false)}\r\n                        className=\"ml-2 p-2 text-gray-500 hover:text-gray-700\"\r\n                      >\r\n                        <X size={16} />\r\n                      </button>\r\n                    </div>\r\n                  ) : (\r\n                    <select\r\n                      id=\"category\"\r\n                      value={newItemCategory}\r\n                      onChange={(e) => {\r\n                        if (e.target.value === \"custom\") {\r\n                          setShowCustomCategoryInput(true);\r\n                          setNewCustomCategory(\"\");\r\n                        } else {\r\n                          setNewItemCategory(e.target.value);\r\n                        }\r\n                      }}\r\n                      className=\"w-full p-3 border border-gray-300 rounded-lg bg-white text-black focus:outline-none focus:border-[#B31B1E]\"\r\n                    >\r\n                      {/* All categories from API */}\r\n                      {availableCategories.length > 0 ? (\r\n                        availableCategories.map(category => (\r\n                          <option key={category} value={category} className=\"text-black\">\r\n                            {category.charAt(0).toUpperCase() + category.slice(1)}\r\n                          </option>\r\n                        ))\r\n                      ) : (\r\n                        // Fallback categories if API hasn't loaded yet\r\n                        <>\r\n                          <option value=\"general\" className=\"text-black\">General</option>\r\n                          <option value=\"planning\" className=\"text-black\">Planning</option>\r\n                          <option value=\"venue\" className=\"text-black\">Venue</option>\r\n                          <option value=\"vendors\" className=\"text-black\">Vendors</option>\r\n                          <option value=\"food & drink\" className=\"text-black\">Food & Drink</option>\r\n                          <option value=\"entertainment\" className=\"text-black\">Entertainment</option>\r\n                          <option value=\"attire\" className=\"text-black\">Attire</option>\r\n                          <option value=\"stationery\" className=\"text-black\">Stationery</option>\r\n                          <option value=\"gifts & favors\" className=\"text-black\">Gifts & Favors</option>\r\n                          <option value=\"ceremony\" className=\"text-black\">Ceremony</option>\r\n                          <option value=\"transportation\" className=\"text-black\">Transportation</option>\r\n                          <option value=\"honeymoon\" className=\"text-black\">Honeymoon</option>\r\n                        </>\r\n                      )}\r\n\r\n                      {/* Custom category option */}\r\n                      <option value=\"custom\" className=\"text-black\">+ Add Custom Category</option>\r\n                    </select>\r\n                  )}\r\n                </div>\r\n\r\n                <button\r\n                  onClick={() => {\r\n                    const finalCategory = showCustomCategoryInput ? newCustomCategory : newItemCategory;\r\n                    addChecklistItem(newItemTask, description || \"\", finalCategory);\r\n                    setIsAddTaskModalOpen(false);\r\n                  }}\r\n                  disabled={!newItemTask.trim()}\r\n                  className=\"w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6 disabled:bg-red-300 disabled:cursor-not-allowed\"\r\n                >\r\n                  Save Task\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Checklist;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;;AAiBA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,iBAAiB,EACjB,UAAU,EACV,OAAO,EACP,KAAK,EACL,cAAc,EACf;;IACC,mBAAmB;IACnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE3E,cAAc;IACd,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEhF,gBAAgB;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,QAAQ,GAAG,CAAC,wCAAwC;YAEpD,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,+EACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAGF,QAAQ,GAAG,CAAC,qCAAqC,SAAS,IAAI;YAE9D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,SAAS,GAAG;gBACtF,QAAQ,GAAG,CAAC,4BAA4B,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM;gBACtE,MAAM,QAAQ,SAAS,IAAI,CAAC,SAAS;gBACrC,kBAAkB;gBAElB,gBAAgB;gBAChB,MAAM,QAAQ,MAAM,MAAM;gBAC1B,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,OAAwB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,GAAG,MAAM;gBACxF,cAAc;gBACd,kBAAkB;gBAElB,mCAAmC;gBACnC,IAAI,SAAS,IAAI,CAAC,UAAU,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,UAAU,GAAG;oBACvE,uBAAuB,SAAS,IAAI,CAAC,UAAU;gBACjD;gBAEA,SAAS,OAAO,4BAA4B;YAC9C,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,SAAS,KAAK,MAAM;gBAC5D,0DAA0D;gBAC1D,QAAQ,GAAG,CAAC;gBACZ,kBAAkB,EAAE;gBACpB,cAAc;gBACd,kBAAkB;gBAClB,SAAS,OAAO,4BAA4B;YAC9C,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;gBAC/C,gCAAgC;gBAChC,QAAQ,IAAI,CAAC,uBAAuB,SAAS,IAAI,CAAC,KAAK;gBACvD,SAAS,CAAC,gCAAgC,EAAE,SAAS,IAAI,CAAC,KAAK,EAAE;gBACjE,kBAAkB,EAAE;gBACpB,cAAc;gBACd,kBAAkB;YACpB,OAAO;gBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;gBAC7D,SAAS;gBACT,kBAAkB,EAAE;gBACpB,cAAc;gBACd,kBAAkB;YACpB;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,0CAA0C;YAC1C,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,kBAAkB;gBACrF,kBAAkB,EAAE;gBACpB,SAAS,OAAO,mDAAmD;YACrE,OAAO;gBACL,SAAS;gBACT,kBAAkB,EAAE;YACtB;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,OAAO,MAAc,aAAqB;QACjE,IAAI,CAAC,KAAK,IAAI,IAAI;QAElB,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,6CAA6C;YAC7C,MAAM,UAAU;gBACd,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,aAAa,YAAY,qCAAqC;YAChE;YAEA,QAAQ,GAAG,CAAC,4BAA4B;YAExC,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,wFACA,SACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAGF,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,+BAA+B;gBAC/B,MAAM,eAAe;uBAAI;oBAAgB,SAAS,IAAI,CAAC,IAAI;iBAAC;gBAC5D,kBAAkB;gBAElB,gBAAgB;gBAChB,cAAc,aAAa,MAAM;gBACjC,kBAAkB,aAAa,MAAM,CAAC,CAAC,OAAwB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,GAAG,MAAM;gBAE/F,SAAS,OAAO,4BAA4B;gBAC5C,kBAAkB,4BAA4B,uBAAuB;gBAErE,wCAAwC;gBACxC,WAAW;oBACT,kBAAkB;gBACpB,GAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;gBAC7D,SAAS;YACX;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;gBAChE,SAAS,CAAC,8BAA8B,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;YACrE,OAAO;gBACL,SAAS;YACX;QACA,6BAA6B;QAC/B;IACF;IAEA,0CAA0C;IAC1C,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,MAAM,OAAO,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;YAC1D,IAAI,CAAC,MAAM;gBACT,QAAQ,IAAI,CAAC,gCAAgC;gBAC7C;YACF;YAEA,4BAA4B;YAC5B,MAAM,cAAc;gBAClB,SAAS;gBACT,QAAQ,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,IAAI,YAAY,YAAY,gBAAgB;YAC7E;YAEA,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,2FACA,aACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAGF,QAAQ,GAAG,CAAC,iCAAiC,SAAS,IAAI;YAE1D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,8BAA8B;gBAC9B,MAAM,eAAe,eAAe,GAAG,CAAC,CAAC,OACvC,KAAK,OAAO,KAAK,KAAK;wBAAE,GAAG,IAAI;wBAAE,QAAQ,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,IAAI,YAAY;oBAAY,IAAI;gBAGlG,kBAAkB;gBAElB,gBAAgB;gBAChB,cAAc,aAAa,MAAM;gBACjC,kBAAkB,aAAa,MAAM,CAAC,CAAC,OAAwB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,GAAG,MAAM;gBAE/F,SAAS,OAAO,4BAA4B;gBAC5C,kBAAkB,CAAC,KAAK,EAAE,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,IAAI,sBAAsB,uBAAuB,GAAG,uBAAuB;gBAE5H,wCAAwC;gBACxC,WAAW;oBACT,kBAAkB;gBACpB,GAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;gBAC7D,SAAS;YACX;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;gBAChE,SAAS,CAAC,iCAAiC,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;YACxE,OAAO;gBACL,SAAS;YACX;QACA,6BAA6B;QAC/B;IACF;IAEA,0BAA0B;IAC1B,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,IAAI;gBACP,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CACjC,2FACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;gBACA,MAAM;oBAAE,SAAS;gBAAG;YACtB;YAGF,QAAQ,GAAG,CAAC,iCAAiC,SAAS,IAAI;YAE1D,8DAA8D;YAC9D,8CAA8C;YAC9C,IACE,AAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IACtC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,cACzE,SAAS,MAAM,IAAI,OAAO,SAAS,MAAM,GAAG,IAAK,2BAA2B;cAC7E;gBACA,gCAAgC;gBAChC,MAAM,eAAe,eAAe,MAAM,CAAC,CAAC,OAAwB,KAAK,OAAO,KAAK;gBACrF,kBAAkB;gBAElB,gBAAgB;gBAChB,cAAc,aAAa,MAAM;gBACjC,kBAAkB,aAAa,MAAM,CAAC,CAAC,OAAwB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,GAAG,MAAM;gBAE/F,SAAS,OAAO,4BAA4B;gBAC5C,kBAAkB,8BAA8B,uBAAuB;gBAEvE,wCAAwC;gBACxC,WAAW;oBACT,kBAAkB;gBACpB,GAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;gBAC7D,SAAS;YACX;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;gBAChE,SAAS,CAAC,iCAAiC,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;YACxE,OAAO;gBACL,SAAS;YACX;QACA,6BAA6B;QAC/B;IACF;IAEA,iCAAiC;IACjC,MAAM,mBAAmB,CAAC;QACxB,eAAe,KAAK,OAAO;QAC3B,YAAY,KAAK,IAAI;QACrB,gBAAgB,KAAK,QAAQ;IAC/B;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,IAAI;gBACP,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT,eAAe;gBACf;YACF;YAEA,MAAM,OAAO,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;YAC1D,IAAI,CAAC,MAAM;gBACT,QAAQ,IAAI,CAAC,gCAAgC;gBAC7C,eAAe;gBACf;YACF;YAEA,2DAA2D;YAC3D,MAAM,cAAc;gBAClB,SAAS;gBACT,MAAM;gBACN,UAAU;YACZ;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,2FACA,aACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAGF,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI;YAExD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,8BAA8B;gBAC9B,MAAM,eAAe,eAAe,GAAG,CAAC,CAAC,OACvC,KAAK,OAAO,KAAK,KAAK;wBAAE,GAAG,IAAI;wBAAE,MAAM;wBAAU,UAAU;oBAAa,IAAI;gBAG9E,kBAAkB;gBAClB,eAAe;gBAEf,mEAAmE;gBAEnE,SAAS,OAAO,4BAA4B;gBAC5C,kBAAkB,8BAA8B,uBAAuB;gBAEvE,wCAAwC;gBACxC,WAAW;oBACT,kBAAkB;gBACpB,GAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;gBAC7D,SAAS;gBACT,eAAe;YACjB;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;gBAChE,SAAS,CAAC,iCAAiC,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;YACxE,OAAO;gBACL,SAAS;YACX;YACA,6BAA6B;YAC7B,eAAe;QACjB;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,eAAe;IACjB;IAEA,sDAAsD;IACtD,MAAM,mBAAmB;QACvB,OAAO,eAAe,MAAM,CAAC,CAAC;YAC5B,mBAAmB;YACnB,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,iBAAiB,eAAe,CAAC,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,GAAG,OAAO;gBACtE,IAAI,iBAAiB,aAAa,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,GAAG,OAAO;YACrE;YAEA,wCAAwC;YACxC,IAAI,mBAAmB,SAAS,KAAK,QAAQ,CAAC,WAAW,OAAO,eAAe,WAAW,IAAI,OAAO;YAErG,OAAO;QACT;IACF;IAEA,gEAAgE;IAChE,MAAM,gBAAgB;QACpB,MAAM,aAAa,IAAI;QAEvB,sCAAsC;QACtC,eAAe,OAAO,CAAC,CAAC;YACtB,IAAI,KAAK,QAAQ,EAAE,WAAW,GAAG,CAAC,KAAK,QAAQ,CAAC,WAAW;QAC7D;QAEA,0BAA0B;QAC1B,oBAAoB,OAAO,CAAC,CAAA;YAC1B,IAAI,UAAU,WAAW,GAAG,CAAC,SAAS,WAAW;QACnD;QAEA,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,OAAO,KAAK;gCAC3B,WAAU;;kDAEV,6LAAC,2MAAA,CAAA,UAAO;wCAAC,MAAM;;;;;;kDACf,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,SAAS,IAAM,MAAM;gCACrB,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAkC;oCAAW;oCAAe;oCAAS;;;;;;;0CACpF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,aAAa,IAAI,GAAG,AAAC,iBAAiB,aAAc,IAAI,CAAC,CAAC,GAAG;oCAAK;;;;;;;;;;;;;;;;;;;;;;;0BAO1F,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,CAAC,gEAAgE,EAAE,mBAAmB,QAAQ,4BAA4B,4CAA4C;;0DAEjL,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;;oCAG5B,gBAAgB,GAAG,CAAC,CAAA,yBACnB,6LAAC;4CAEC,SAAS,IAAM,kBAAkB;4CACjC,WAAW,CAAC,gEAAgE,EAAE,mBAAmB,WAAW,4BAA4B,4CAA4C;;8DAEpL,6LAAC;oDAAK,WAAU;8DAAc,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;;;;;;8DAChF,6LAAC;oDAAK,WAAU;8DACb,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,WAAW,OAAO,SAAS,WAAW,IAAI,MAAM;;;;;;;2CAN1F;;;;;;;;;;;;;;;;;kCAcb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCACC,SAAS;4CACP,sBAAsB;4CACtB,eAAe;4CACf,eAAe;4CACf,mBAAmB;4CACnB,2BAA2B;wCAC7B;wCACA,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;0DACZ,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,4BAA4B,uBAAuB;sDAC7G;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,kBAAkB,EAAE,iBAAiB,YAAY,4BAA4B,uBAAuB;sDACjH;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,kBAAkB,EAAE,iBAAiB,cAAc,4BAA4B,uBAAuB;sDACnH;;;;;;;;;;;;;;;;;4BAOA,uBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;4BAKJ,gCACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;4BAKJ,wBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;uCAGjB,mBAAmB,iBACnB,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,MAAM,KAAK,kBAC7B,6LAAC;oCAAE,WAAU;8CACV,eAAe,MAAM,KAAK,IACvB,0DACA;;;;;2CAGN,mBAAmB,GAAG,CAAC,CAAC,qBACtB,6LAAC;wCAEC,WAAW,CAAC,wDAAwD,EAClE,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,IAAI,+BAA+B,4BAC1D;kDAEL,gBAAgB,KAAK,OAAO,GAC3B,gBAAgB,iBAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;;;;;;8DAEZ,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC;wDACT,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,UAAU;4DAC/B,+BAA+B;4DAC/B,MAAM,iBAAiB,OAAO;4DAC9B,IAAI,kBAAkB,eAAe,IAAI,IAAI;gEAC3C,gBAAgB,eAAe,IAAI;gEACnC,IAAI,CAAC,oBAAoB,QAAQ,CAAC,eAAe,IAAI,KAAK;oEACxD,uBAAuB;2EAAI;wEAAqB,eAAe,IAAI;qEAAG;gEACxE;4DACF;wDACF,OAAO;4DACL,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAChC;oDACF;oDACA,WAAU;;wDAGT,oBAAoB,MAAM,GAAG,IAC5B,oBAAoB,GAAG,CAAC,CAAA,yBACtB,6LAAC;gEAAsB,OAAO;gEAAU,WAAU;0EAC/C,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;+DADxC;;;;wEAKf,+CAA+C;sEAC/C;;8EACE,6LAAC;oEAAO,OAAM;oEAAU,WAAU;8EAAa;;;;;;8EAC/C,6LAAC;oEAAO,OAAM;oEAAW,WAAU;8EAAa;;;;;;8EAChD,6LAAC;oEAAO,OAAM;oEAAQ,WAAU;8EAAa;;;;;;8EAC7C,6LAAC;oEAAO,OAAM;oEAAU,WAAU;8EAAa;;;;;;8EAC/C,6LAAC;oEAAO,OAAM;oEAAe,WAAU;8EAAa;;;;;;8EACpD,6LAAC;oEAAO,OAAM;oEAAgB,WAAU;8EAAa;;;;;;8EACrD,6LAAC;oEAAO,OAAM;oEAAS,WAAU;8EAAa;;;;;;8EAC9C,6LAAC;oEAAO,OAAM;oEAAa,WAAU;8EAAa;;;;;;8EAClD,6LAAC;oEAAO,OAAM;oEAAiB,WAAU;8EAAa;;;;;;8EACtD,6LAAC;oEAAO,OAAM;oEAAW,WAAU;8EAAa;;;;;;8EAChD,6LAAC;oEAAO,OAAM;oEAAiB,WAAU;8EAAa;;;;;;8EACtD,6LAAC;oEAAO,OAAM;oEAAY,WAAU;8EAAa;;;;;;;;sEAKrD,6LAAC;4DAAO,OAAM;4DAAS,WAAU;sEAAa;;;;;;;;;;;;8DAEhD,6LAAC;oDACC,SAAS,IAAM,eAAe,KAAK,OAAO;oDAC1C,WAAU;8DAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,MAAM;;;;;;;;;;;8DAEf,6LAAC;oDACC,SAAS;oDACT,WAAU;8DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wDAAC,MAAM;;;;;;;;;;;;;;;;mDAIb,gBAAgB,iBAChB;;8DACE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,SAAS,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM;4DAChC,UAAU,IAAM,oBAAoB,KAAK,OAAO;4DAChD,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,WAAW,EAAE,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,IAAI,+BAA+B,IAAI;8EAC1F,KAAK,IAAI;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAoC,KAAK,QAAQ;;;;;;;;;;;;;;;;;;8DAGrE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,IAAM,iBAAiB;4DAChC,WAAU;sEAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;sEAEd,6LAAC;4DACC,SAAS,IAAM,oBAAoB,KAAK,OAAO;4DAC/C,WAAU;sEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;uCArGZ,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;YAmH9B,oCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,WAAW;oBACb;;sCAEA,6LAAC;4BACC,SAAS,IAAM,sBAAsB;4BACrC,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;oCAAsC,OAAO;wCAAE,OAAO;oCAAU;8CAAG;;;;;;8CAGjF,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAItD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAoC;;;;;;;;;;;8DAI1E,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAI7E,6LAAC;oDACC,IAAG;oDACH,OAAO,eAAe;oDACtB,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,aAAY;oDACZ,MAAM;;;;;;;;;;;;sDAIV,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAoC;;;;;;;;;;;gDAIzE,wCACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;4DACpD,WAAU;4DACV,aAAY;;;;;;sEAEd,6LAAC;4DACC,SAAS,IAAM,2BAA2B;4DAC1C,WAAU;sEAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,MAAM;;;;;;;;;;;;;;;;yEAIb,6LAAC;oDACC,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC;wDACT,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,UAAU;4DAC/B,2BAA2B;4DAC3B,qBAAqB;wDACvB,OAAO;4DACL,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACnC;oDACF;oDACA,WAAU;;wDAGT,oBAAoB,MAAM,GAAG,IAC5B,oBAAoB,GAAG,CAAC,CAAA,yBACtB,6LAAC;gEAAsB,OAAO;gEAAU,WAAU;0EAC/C,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;+DADxC;;;;wEAKf,+CAA+C;sEAC/C;;8EACE,6LAAC;oEAAO,OAAM;oEAAU,WAAU;8EAAa;;;;;;8EAC/C,6LAAC;oEAAO,OAAM;oEAAW,WAAU;8EAAa;;;;;;8EAChD,6LAAC;oEAAO,OAAM;oEAAQ,WAAU;8EAAa;;;;;;8EAC7C,6LAAC;oEAAO,OAAM;oEAAU,WAAU;8EAAa;;;;;;8EAC/C,6LAAC;oEAAO,OAAM;oEAAe,WAAU;8EAAa;;;;;;8EACpD,6LAAC;oEAAO,OAAM;oEAAgB,WAAU;8EAAa;;;;;;8EACrD,6LAAC;oEAAO,OAAM;oEAAS,WAAU;8EAAa;;;;;;8EAC9C,6LAAC;oEAAO,OAAM;oEAAa,WAAU;8EAAa;;;;;;8EAClD,6LAAC;oEAAO,OAAM;oEAAiB,WAAU;8EAAa;;;;;;8EACtD,6LAAC;oEAAO,OAAM;oEAAW,WAAU;8EAAa;;;;;;8EAChD,6LAAC;oEAAO,OAAM;oEAAiB,WAAU;8EAAa;;;;;;8EACtD,6LAAC;oEAAO,OAAM;oEAAY,WAAU;8EAAa;;;;;;;;sEAKrD,6LAAC;4DAAO,OAAM;4DAAS,WAAU;sEAAa;;;;;;;;;;;;;;;;;;sDAKpD,6LAAC;4CACC,SAAS;gDACP,MAAM,gBAAgB,0BAA0B,oBAAoB;gDACpE,iBAAiB,aAAa,eAAe,IAAI;gDACjD,sBAAsB;4CACxB;4CACA,UAAU,CAAC,YAAY,IAAI;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAp1BM;KAAA;uCAs1BS", "debugId": null}}, {"offset": {"line": 1512, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1518, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/budget/EditableAmount.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport { formatCurrency } from '../../utils';\n\ninterface EditableAmountProps {\n  initialValue: number;\n  onSave: (value: number) => Promise<boolean>;\n}\n\nconst EditableAmount: React.FC<EditableAmountProps> = ({ initialValue, onSave }) => {\n  const [isEditing, setIsEditing] = useState(false);\n  const [value, setValue] = useState(initialValue);\n  const [displayValue, setDisplayValue] = useState(formatCurrency(initialValue));\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // Update display value when initialValue changes\n  useEffect(() => {\n    setValue(initialValue);\n    setDisplayValue(formatCurrency(initialValue));\n  }, [initialValue]);\n\n  const handleClick = () => {\n    setIsEditing(true);\n    // Focus the input after rendering\n    setTimeout(() => {\n      if (inputRef.current) {\n        inputRef.current.focus();\n        inputRef.current.select();\n      }\n    }, 0);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setDisplayValue(e.target.value);\n  };\n\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveError, setSaveError] = useState(false);\n\n  const handleBlur = async () => {\n    // Parse the value (remove currency symbol and commas)\n    const numericValue = parseFloat(displayValue.replace(/[^0-9.]/g, ''));\n\n    if (!isNaN(numericValue) && numericValue !== value) {\n      setIsSaving(true);\n      try {\n        const success = await onSave(numericValue);\n        if (success) {\n          setValue(numericValue);\n          setDisplayValue(formatCurrency(numericValue));\n          setSaveError(false);\n        } else {\n          // If save failed, revert to the previous value\n          setDisplayValue(formatCurrency(value));\n          setSaveError(true);\n          // Auto-hide error after 3 seconds\n          setTimeout(() => setSaveError(false), 3000);\n        }\n      } catch (error) {\n        console.error('Error saving value:', error);\n        setDisplayValue(formatCurrency(value));\n        setSaveError(true);\n        // Auto-hide error after 3 seconds\n        setTimeout(() => setSaveError(false), 3000);\n      } finally {\n        setIsSaving(false);\n        setIsEditing(false);\n      }\n    } else {\n      // If invalid or unchanged, revert to the previous value\n      setDisplayValue(formatCurrency(value));\n      setIsEditing(false);\n    }\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter') {\n      e.currentTarget.blur(); // Trigger the blur event\n    } else if (e.key === 'Escape') {\n      setIsEditing(false);\n      setDisplayValue(formatCurrency(value)); // Revert to the previous value\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      {isEditing ? (\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={displayValue}\n          onChange={handleChange}\n          onBlur={handleBlur}\n          onKeyDown={handleKeyDown}\n          className=\"w-full text-right bg-transparent border-b border-[#B31B1E] focus:outline-none\"\n          disabled={isSaving}\n          autoFocus\n        />\n      ) : (\n        <div\n          onClick={handleClick}\n          className={`cursor-pointer w-full text-right ${saveError ? 'text-red-500' : 'hover:text-[#B31B1E]'}`}\n        >\n          {isSaving ? (\n            <span className=\"text-gray-500\">Saving...</span>\n          ) : (\n            formatCurrency(value)\n          )}\n          {saveError && (\n            <span className=\"text-xs text-red-500 absolute -bottom-5 right-0\">Failed to save</span>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default EditableAmount;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AASA,MAAM,iBAAgD,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE;;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,SAAS;YACT,gBAAgB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;QACjC;mCAAG;QAAC;KAAa;IAEjB,MAAM,cAAc;QAClB,aAAa;QACb,kCAAkC;QAClC,WAAW;YACT,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,KAAK;gBACtB,SAAS,OAAO,CAAC,MAAM;YACzB;QACF,GAAG;IACL;IAEA,MAAM,eAAe,CAAC;QACpB,gBAAgB,EAAE,MAAM,CAAC,KAAK;IAChC;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB,sDAAsD;QACtD,MAAM,eAAe,WAAW,aAAa,OAAO,CAAC,YAAY;QAEjE,IAAI,CAAC,MAAM,iBAAiB,iBAAiB,OAAO;YAClD,YAAY;YACZ,IAAI;gBACF,MAAM,UAAU,MAAM,OAAO;gBAC7B,IAAI,SAAS;oBACX,SAAS;oBACT,gBAAgB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;oBAC/B,aAAa;gBACf,OAAO;oBACL,+CAA+C;oBAC/C,gBAAgB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;oBAC/B,aAAa;oBACb,kCAAkC;oBAClC,WAAW,IAAM,aAAa,QAAQ;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,gBAAgB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;gBAC/B,aAAa;gBACb,kCAAkC;gBAClC,WAAW,IAAM,aAAa,QAAQ;YACxC,SAAU;gBACR,YAAY;gBACZ,aAAa;YACf;QACF,OAAO;YACL,wDAAwD;YACxD,gBAAgB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;YAC/B,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,aAAa,CAAC,IAAI,IAAI,yBAAyB;QACnD,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,aAAa;YACb,gBAAgB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,+BAA+B;QACzE;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,0BACC,6LAAC;YACC,KAAK;YACL,MAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ;YACR,WAAW;YACX,WAAU;YACV,UAAU;YACV,SAAS;;;;;iCAGX,6LAAC;YACC,SAAS;YACT,WAAW,CAAC,iCAAiC,EAAE,YAAY,iBAAiB,wBAAwB;;gBAEnG,yBACC,6LAAC;oBAAK,WAAU;8BAAgB;;;;;2BAEhC,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;gBAEhB,2BACC,6LAAC;oBAAK,WAAU;8BAAkD;;;;;;;;;;;;;;;;;AAM9E;GA1GM;KAAA;uCA4GS", "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1663, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/budget/CategoryModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { X } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\n\r\ninterface CategoryModalProps {\r\n  onClose: () => void;\r\n  onSave: (name: string) => void;\r\n}\r\n\r\nconst CategoryModal: React.FC<CategoryModalProps> = ({ onClose, onSave }) => {\r\n  const [categoryName, setCategoryName] = useState<string>(\"\");\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (categoryName.trim()) {\r\n      onSave(categoryName.trim());\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\r\n        style={{\r\n          background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\r\n          boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\r\n        }}\r\n      >\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\"\r\n        >\r\n          <X size={20} />\r\n        </button>\r\n\r\n        {/* Logo */}\r\n        <div className=\"flex justify-center pt-6\">\r\n          <div className=\"text-red-600\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={40}\r\n              height={40}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"px-6 py-4\">\r\n          <h3 className=\"text-2xl font-bold mb-2 text-center\" style={{ color: \"#B31B1E\" }}>Add Category</h3>\r\n\r\n        <form onSubmit={handleSubmit}>\r\n          <div className=\"mb-4\">\r\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"username\">\r\n              USERNAME\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              value={categoryName}\r\n              onChange={(e) => setCategoryName(e.target.value)}\r\n              placeholder=\"Category\"\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex justify-center\">\r\n            <button\r\n              type=\"submit\"\r\n              className=\"w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6\"\r\n            >\r\n              Save\r\n            </button>\r\n          </div>\r\n        </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CategoryModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAUA,MAAM,gBAA8C,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE;;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,aAAa,IAAI,IAAI;YACvB,OAAO,aAAa,IAAI;QAC1B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;;8BAEA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;4BAAsC,OAAO;gCAAE,OAAO;4BAAU;sCAAG;;;;;;sCAEnF,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAW;;;;;;sDAGjF,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,aAAY;4CACZ,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASb;GAtEM;KAAA;uCAwES", "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/budget/ExpenseModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { X } from \"lucide-react\";\r\nimport { BudgetCategory } from \"../../types\";\r\nimport Image from \"next/image\";\r\n\r\ninterface ExpenseModalProps {\r\n  categories: BudgetCategory[];\r\n  selectedCategory: BudgetCategory | null;\r\n  onClose: () => void;\r\n  onSave: (expenseData: {\r\n    name: string;\r\n    category_id: string;\r\n    estimated_budget: number;\r\n    final_cost: number;\r\n    notes: string;\r\n  }) => void;\r\n}\r\n\r\nconst ExpenseModal: React.FC<ExpenseModalProps> = ({\r\n  categories,\r\n  selectedCategory,\r\n  onClose,\r\n  onSave\r\n}) => {\r\n  const [expenseData, setExpenseData] = useState({\r\n    name: \"\",\r\n    category_id: selectedCategory ? selectedCategory.category_id : \"\",\r\n    estimated_budget: \"0\",\r\n    final_cost: \"0\",\r\n    notes: \"\"\r\n  });\r\n\r\n  // Update category_id when selectedCategory changes\r\n  useEffect(() => {\r\n    if (selectedCategory) {\r\n      setExpenseData(prev => ({\r\n        ...prev,\r\n        category_id: selectedCategory.category_id\r\n      }));\r\n    }\r\n  }, [selectedCategory]);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n    const { name, value } = e.target;\r\n    setExpenseData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    onSave({\r\n      name: expenseData.name,\r\n      category_id: expenseData.category_id,\r\n      estimated_budget: parseFloat(expenseData.estimated_budget) || 0,\r\n      final_cost: parseFloat(expenseData.final_cost) || 0,\r\n      notes: expenseData.notes\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\r\n        style={{\r\n          background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\r\n          boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\r\n        }}\r\n      >\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\"\r\n        >\r\n          <X size={20} />\r\n        </button>\r\n\r\n        {/* Logo */}\r\n        <div className=\"flex justify-center pt-6\">\r\n          <div className=\"text-red-600\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={40}\r\n              height={40}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"px-6 py-4\">\r\n          <h3 className=\"text-2xl font-bold mb-2 text-center\" style={{ color: \"#B31B1E\" }}>Add Expense</h3>\r\n\r\n        <form onSubmit={handleSubmit}>\r\n          <div className=\"mb-4\">\r\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"name\">\r\n              Expense Name\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"name\"\r\n              name=\"name\"\r\n              value={expenseData.name}\r\n              onChange={handleChange}\r\n              placeholder=\"Expense name\"\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mb-4\">\r\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"category_id\">\r\n              Category\r\n            </label>\r\n            <select\r\n              id=\"category_id\"\r\n              name=\"category_id\"\r\n              value={expenseData.category_id}\r\n              onChange={handleChange}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n              required\r\n            >\r\n              <option value=\"\" disabled>Select a category</option>\r\n              {categories.map(category => (\r\n                <option key={category.category_id} value={category.category_id}>\r\n                  {category.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"mb-4\">\r\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"estimated_budget\">\r\n              Estimated Budget\r\n            </label>\r\n            <input\r\n              type=\"number\"\r\n              id=\"estimated_budget\"\r\n              name=\"estimated_budget\"\r\n              value={expenseData.estimated_budget}\r\n              onChange={handleChange}\r\n              placeholder=\"0\"\r\n              min=\"0\"\r\n              step=\"0.01\"\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mb-4\">\r\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"final_cost\">\r\n              Final Cost\r\n            </label>\r\n            <input\r\n              type=\"number\"\r\n              id=\"final_cost\"\r\n              name=\"final_cost\"\r\n              value={expenseData.final_cost}\r\n              onChange={handleChange}\r\n              placeholder=\"0\"\r\n              min=\"0\"\r\n              step=\"0.01\"\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mb-4\">\r\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"notes\">\r\n              Notes\r\n            </label>\r\n            <textarea\r\n              id=\"notes\"\r\n              name=\"notes\"\r\n              value={expenseData.notes}\r\n              onChange={handleChange}\r\n              placeholder=\"Add notes...\"\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n              rows={3}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex justify-center\">\r\n            <button\r\n              type=\"submit\"\r\n              className=\"w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6\"\r\n            >\r\n              Save\r\n            </button>\r\n          </div>\r\n        </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExpenseModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;;;AAJA;;;;AAmBA,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,MAAM,EACP;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,aAAa,mBAAmB,iBAAiB,WAAW,GAAG;QAC/D,kBAAkB;QAClB,YAAY;QACZ,OAAO;IACT;IAEA,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,kBAAkB;gBACpB;8CAAe,CAAA,OAAQ,CAAC;4BACtB,GAAG,IAAI;4BACP,aAAa,iBAAiB,WAAW;wBAC3C,CAAC;;YACH;QACF;iCAAG;QAAC;KAAiB;IAErB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;YACL,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW;YACpC,kBAAkB,WAAW,YAAY,gBAAgB,KAAK;YAC9D,YAAY,WAAW,YAAY,UAAU,KAAK;YAClD,OAAO,YAAY,KAAK;QAC1B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;;8BAEA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;4BAAsC,OAAO;gCAAE,OAAO;4BAAU;sCAAG;;;;;;sCAEnF,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAO;;;;;;sDAG7E,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,IAAI;4CACvB,UAAU;4CACV,aAAY;4CACZ,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAc;;;;;;sDAGpF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,WAAW;4CAC9B,UAAU;4CACV,WAAU;4CACV,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;oDAAG,QAAQ;8DAAC;;;;;;gDACzB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;wDAAkC,OAAO,SAAS,WAAW;kEAC3D,SAAS,IAAI;uDADH,SAAS,WAAW;;;;;;;;;;;;;;;;;8CAOvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAmB;;;;;;sDAGzF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,gBAAgB;4CACnC,UAAU;4CACV,aAAY;4CACZ,KAAI;4CACJ,MAAK;4CACL,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAa;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,UAAU;4CAC7B,UAAU;4CACV,aAAY;4CACZ,KAAI;4CACJ,MAAK;4CACL,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAQ;;;;;;sDAG9E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,KAAK;4CACxB,UAAU;4CACV,aAAY;4CACZ,WAAU;4CACV,MAAM;;;;;;;;;;;;8CAIV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASb;GA/KM;KAAA;uCAiLS", "debugId": null}}, {"offset": {"line": 2181, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2187, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/budget/PaymentModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { X, Plus } from \"lucide-react\";\r\nimport { BudgetExpense } from \"../../types\";\r\nimport Image from \"next/image\";\r\n\r\ninterface PaymentModalProps {\r\n  expenses: BudgetExpense[];\r\n  selectedExpenseId?: string; // Optional selected expense ID\r\n  onClose: () => void;\r\n  onSave: (paymentData: {\r\n    expense_id: string;\r\n    amount: number;\r\n    payment_date: string;\r\n    payment_method: string;\r\n    notes: string;\r\n    paid_by: string;\r\n  }) => void;\r\n}\r\n\r\nconst PaymentModal: React.FC<PaymentModalProps> = ({ expenses, selectedExpenseId, onClose, onSave }) => {\r\n  const [paymentData, setPaymentData] = useState({\r\n    expense_id: selectedExpenseId || \"\",\r\n    amount: \"0\",\r\n    payment_date: new Date().toISOString().split('T')[0],\r\n    payment_method: \"\",\r\n    notes: \"\",\r\n    paid_by: \"\"\r\n  });\r\n\r\n  const [isPaid, setIsPaid] = useState(false);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n    const { name, value } = e.target;\r\n    setPaymentData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    onSave({\r\n      expense_id: paymentData.expense_id,\r\n      amount: parseFloat(paymentData.amount) || 0,\r\n      payment_date: paymentData.payment_date,\r\n      payment_method: paymentData.payment_method,\r\n      notes: paymentData.notes,\r\n      paid_by: paymentData.paid_by\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\r\n        style={{\r\n          background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\r\n          boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\r\n        }}\r\n      >\r\n        <button\r\n          onClick={onClose}\r\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\"\r\n        >\r\n          <X size={20} />\r\n        </button>\r\n\r\n        {/* Logo */}\r\n        <div className=\"flex justify-center pt-6\">\r\n          <div className=\"text-red-600\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={40}\r\n              height={40}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"px-6 py-4\">\r\n          <h3 className=\"text-2xl font-bold mb-2 text-center\" style={{ color: \"#B31B1E\" }}>\r\n            Add Payment\r\n          </h3>\r\n          <p className=\"text-sm text-center text-gray-600 mb-6\">\r\n            {expenses.find(e => e.expense_id === paymentData.expense_id)?.name || 'Select an expense'}\r\n          </p>\r\n\r\n        <form onSubmit={handleSubmit}>\r\n          {/* Expense Selection - only show if no expense is pre-selected */}\r\n          {!selectedExpenseId && (\r\n            <div className=\"mb-4\">\r\n              <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"expense_id\">\r\n                EXPENSE\r\n              </label>\r\n              <select\r\n                id=\"expense_id\"\r\n                name=\"expense_id\"\r\n                value={paymentData.expense_id}\r\n                onChange={handleChange}\r\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n                required\r\n              >\r\n                <option value=\"\">Select an expense</option>\r\n                {expenses.map(expense => (\r\n                  <option key={expense.expense_id} value={expense.expense_id}>\r\n                    {expense.name} ({expense.category_name})\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"mb-4\">\r\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"amount\">\r\n              AMOUNT\r\n            </label>\r\n            <input\r\n              type=\"number\"\r\n              id=\"amount\"\r\n              name=\"amount\"\r\n              value={paymentData.amount}\r\n              onChange={handleChange}\r\n              placeholder=\"0\"\r\n              min=\"0\"\r\n              step=\"0.01\"\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mb-4 flex items-center\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"isPaid\"\r\n              checked={isPaid}\r\n              onChange={() => setIsPaid(!isPaid)}\r\n              className=\"mr-2 h-5 w-5\"\r\n            />\r\n            <label htmlFor=\"isPaid\" className=\"text-gray-700\">\r\n              Paid\r\n            </label>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-2 gap-4 mb-4\">\r\n            <div>\r\n              <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"payment_date\">\r\n                DATE OF PAYMENT\r\n              </label>\r\n              <input\r\n                type=\"date\"\r\n                id=\"payment_date\"\r\n                name=\"payment_date\"\r\n                value={paymentData.payment_date}\r\n                onChange={handleChange}\r\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            <div>\r\n              <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"payment_due_by\">\r\n                PAYMENT DUE BY\r\n              </label>\r\n              <input\r\n                type=\"date\"\r\n                id=\"payment_due_by\"\r\n                name=\"payment_due_by\"\r\n                className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mb-4\">\r\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"paid_by\">\r\n              PAID BY (NAME)\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"paid_by\"\r\n              name=\"paid_by\"\r\n              value={paymentData.paid_by}\r\n              onChange={handleChange}\r\n              placeholder=\"Username\"\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mb-4\">\r\n            <div className=\"flex justify-between items-center\">\r\n              <label className=\"block text-gray-700 text-sm font-bold\" htmlFor=\"payment_method\">\r\n                Payment method\r\n              </label>\r\n              <button\r\n                type=\"button\"\r\n                className=\"text-red-500 flex items-center text-sm\"\r\n                onClick={() => console.log('Add payment method')}\r\n              >\r\n                <Plus size={16} className=\"mr-1\" />\r\n                Payment method\r\n              </button>\r\n            </div>\r\n            <input\r\n              type=\"text\"\r\n              id=\"payment_method\"\r\n              name=\"payment_method\"\r\n              value={paymentData.payment_method}\r\n              onChange={handleChange}\r\n              placeholder=\"Cash, Card, etc.\"\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black mt-2\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex justify-center\">\r\n            <button\r\n              type=\"submit\"\r\n              className=\"w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6\"\r\n            >\r\n              Save\r\n            </button>\r\n          </div>\r\n        </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PaymentModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAEA;;;AAJA;;;;AAoBA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,iBAAiB,EAAE,OAAO,EAAE,MAAM,EAAE;;IACjG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,YAAY,qBAAqB;QACjC,QAAQ;QACR,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD,gBAAgB;QAChB,OAAO;QACP,SAAS;IACX;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;YACL,YAAY,YAAY,UAAU;YAClC,QAAQ,WAAW,YAAY,MAAM,KAAK;YAC1C,cAAc,YAAY,YAAY;YACtC,gBAAgB,YAAY,cAAc;YAC1C,OAAO,YAAY,KAAK;YACxB,SAAS,YAAY,OAAO;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;;8BAEA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;4BAAsC,OAAO;gCAAE,OAAO;4BAAU;sCAAG;;;;;;sCAGjF,6LAAC;4BAAE,WAAU;sCACV,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,YAAY,UAAU,GAAG,QAAQ;;;;;;sCAG1E,6LAAC;4BAAK,UAAU;;gCAEb,CAAC,mCACA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAa;;;;;;sDAGnF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,UAAU;4CAC7B,UAAU;4CACV,WAAU;4CACV,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,SAAS,GAAG,CAAC,CAAA,wBACZ,6LAAC;wDAAgC,OAAO,QAAQ,UAAU;;4DACvD,QAAQ,IAAI;4DAAC;4DAAG,QAAQ,aAAa;4DAAC;;uDAD5B,QAAQ,UAAU;;;;;;;;;;;;;;;;;8CAQvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAS;;;;;;sDAG/E,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,MAAM;4CACzB,UAAU;4CACV,aAAY;4CACZ,KAAI;4CACJ,MAAK;4CACL,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,SAAS;4CACT,UAAU,IAAM,UAAU,CAAC;4CAC3B,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAS,WAAU;sDAAgB;;;;;;;;;;;;8CAKpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;oDAA6C,SAAQ;8DAAe;;;;;;8DAGrF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,YAAY,YAAY;oDAC/B,UAAU;oDACV,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;oDAA6C,SAAQ;8DAAiB;;;;;;8DAGvF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;4CAA6C,SAAQ;sDAAU;;;;;;sDAGhF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,OAAO;4CAC1B,UAAU;4CACV,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;oDAAwC,SAAQ;8DAAiB;;;;;;8DAGlF,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,QAAQ,GAAG,CAAC;;sEAE3B,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;;;;;;wDAAS;;;;;;;;;;;;;sDAIvC,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,cAAc;4CACjC,UAAU;4CACV,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASb;GA/MM;KAAA;uCAiNS", "debugId": null}}, {"offset": {"line": 2625, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2631, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/budget/ExpenseDetails.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { X, Plus, Edit } from \"lucide-react\";\r\nimport { BudgetExpense, BudgetPayment } from \"../../types\";\r\nimport { formatCurrency, formatDate } from \"../../utils\";\r\nimport Image from \"next/image\";\r\n\r\ninterface ExpenseDetailsProps {\r\n  expense: BudgetExpense;\r\n  payments: BudgetPayment[];\r\n  onClose: () => void;\r\n  onAddPayment: () => void;\r\n  onUpdateExpense: (expenseId: string, updatedData: Partial<BudgetExpense>) => void;\r\n}\r\n\r\nconst ExpenseDetails: React.FC<ExpenseDetailsProps> = ({\r\n  expense,\r\n  payments,\r\n  onClose,\r\n  onAddPayment,\r\n  onUpdateExpense\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState<boolean>(false);\r\n  const [editedExpense, setEditedExpense] = useState<{\r\n    name: string;\r\n    estimated_budget: number;\r\n    final_cost: number;\r\n    notes: string;\r\n  }>({\r\n    name: expense.name,\r\n    estimated_budget: expense.estimated_budget,\r\n    final_cost: expense.final_cost,\r\n    notes: expense.notes || ''\r\n  });\r\n\r\n  const handleSaveEdit = () => {\r\n    onUpdateExpense(expense.expense_id, editedExpense);\r\n    setIsEditing(false);\r\n  };\r\n  return (\r\n    <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"rounded-lg shadow-lg w-full max-w-2xl relative overflow-hidden\"\r\n        style={{\r\n          background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\r\n          boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\r\n        }}\r\n      >\r\n        <div className=\"flex justify-between items-center p-4\">\r\n          <div className=\"flex items-center\">\r\n            <h3 className=\"text-xl font-semibold text-black\">{expense.name}</h3>\r\n            <span className=\"ml-2 text-gray-500\">{expense.category_name}</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-4\">\r\n            <button\r\n              onClick={() => setIsEditing(true)}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              <Edit size={18} />\r\n            </button>\r\n            <button onClick={onClose} className=\"text-gray-500 hover:text-gray-700\">\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Logo */}\r\n        <div className=\"flex justify-center\">\r\n          <div className=\"text-red-600\">\r\n            <Image\r\n              src=\"/pics/logo.png\"\r\n              alt=\"Wedzat logo\"\r\n              width={40}\r\n              height={40}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"px-6 py-4\">\r\n\r\n        {isEditing ? (\r\n          <div className=\"mb-6\">\r\n            <div className=\"grid grid-cols-1 gap-4\">\r\n              <div>\r\n                <label className=\"block text-gray-600 text-sm font-medium mb-2\">NAME</label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={editedExpense.name}\r\n                  onChange={(e) => setEditedExpense({...editedExpense, name: e.target.value})}\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-gray-600 text-sm font-medium mb-2\">ESTIMATED BUDGET</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    value={editedExpense.estimated_budget}\r\n                    onChange={(e) => setEditedExpense({...editedExpense, estimated_budget: parseFloat(e.target.value) || 0})}\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-gray-600 text-sm font-medium mb-2\">FINAL COST</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    value={editedExpense.final_cost}\r\n                    onChange={(e) => setEditedExpense({...editedExpense, final_cost: parseFloat(e.target.value) || 0})}\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-gray-600 text-sm font-medium mb-2\">NOTES</label>\r\n                <textarea\r\n                  value={editedExpense.notes}\r\n                  onChange={(e) => setEditedExpense({...editedExpense, notes: e.target.value})}\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black\"\r\n                  rows={3}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex justify-end gap-2 mt-2\">\r\n                <button\r\n                  onClick={() => setIsEditing(false)}\r\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={handleSaveEdit}\r\n                  className=\"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700\"\r\n                >\r\n                  Save Changes\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\r\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                <h4 className=\"text-gray-600 text-sm font-medium mb-2\">ESTIMATED BUDGET</h4>\r\n                <p className=\"text-2xl font-bold text-black\">{formatCurrency(expense.estimated_budget)}</p>\r\n              </div>\r\n\r\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                <h4 className=\"text-gray-600 text-sm font-medium mb-2\">FINAL COST</h4>\r\n                <p className=\"text-2xl font-bold text-black\">{formatCurrency(expense.final_cost)}</p>\r\n                <div className=\"flex text-sm mt-2\">\r\n                  <p className=\"text-gray-600\">Paid: <span className=\"text-green-600\">{formatCurrency(expense.amount_paid)}</span></p>\r\n                  <p className=\"text-gray-600 ml-4\">Pending: <span className=\"text-blue-600\">{formatCurrency(expense.final_cost - expense.amount_paid)}</span></p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {expense.notes && (\r\n              <div className=\"mb-6\">\r\n                <h4 className=\"text-gray-600 text-sm font-medium mb-2\">NOTES</h4>\r\n                <p className=\"text-black\">{expense.notes}</p>\r\n              </div>\r\n            )}\r\n          </>\r\n        )}\r\n\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <h4 className=\"text-gray-600 text-sm font-medium\">PAYMENTS</h4>\r\n            <button\r\n              onClick={onAddPayment}\r\n              className=\"flex items-center gap-1 text-purple-600 hover:text-purple-800\"\r\n            >\r\n              <Plus size={16} />\r\n              <span>Add payment</span>\r\n            </button>\r\n          </div>\r\n\r\n          {payments.length === 0 ? (\r\n            <p className=\"text-gray-500 text-center py-4\">No payments recorded yet.</p>\r\n          ) : (\r\n            <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\r\n              <table className=\"min-w-full divide-y divide-gray-200\">\r\n                <thead className=\"bg-gray-50\">\r\n                  <tr>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      Date\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      Amount\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      Method\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      Paid By\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                  {payments.map((payment) => (\r\n                    <tr key={payment.payment_id}>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                        {formatDate(payment.payment_date)}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                        {formatCurrency(payment.amount)}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                        {payment.payment_method || '-'}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                        {payment.paid_by || '-'}\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          )}\r\n        </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExpenseDetails;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAEA;AACA;;;AALA;;;;;AAeA,MAAM,iBAAgD,CAAC,EACrD,OAAO,EACP,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,eAAe,EAChB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAK9C;QACD,MAAM,QAAQ,IAAI;QAClB,kBAAkB,QAAQ,gBAAgB;QAC1C,YAAY,QAAQ,UAAU;QAC9B,OAAO,QAAQ,KAAK,IAAI;IAC1B;IAEA,MAAM,iBAAiB;QACrB,gBAAgB,QAAQ,UAAU,EAAE;QACpC,aAAa;IACf;IACA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;;8BAEA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC,QAAQ,IAAI;;;;;;8CAC9D,6LAAC;oCAAK,WAAU;8CAAsB,QAAQ,aAAa;;;;;;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,6LAAC;oCAAO,SAAS;oCAAS,WAAU;8CAClC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAMf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;wBAEd,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,MAAK;gDACL,OAAO,cAAc,IAAI;gDACzB,UAAU,CAAC,IAAM,iBAAiB;wDAAC,GAAG,aAAa;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAA;gDACzE,WAAU;;;;;;;;;;;;kDAId,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,6LAAC;wDACC,MAAK;wDACL,OAAO,cAAc,gBAAgB;wDACrC,UAAU,CAAC,IAAM,iBAAiB;gEAAC,GAAG,aAAa;gEAAE,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4DAAC;wDACtG,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,6LAAC;wDACC,MAAK;wDACL,OAAO,cAAc,UAAU;wDAC/B,UAAU,CAAC,IAAM,iBAAiB;gEAAC,GAAG,aAAa;gEAAE,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4DAAC;wDAChG,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACC,OAAO,cAAc,KAAK;gDAC1B,UAAU,CAAC,IAAM,iBAAiB;wDAAC,GAAG,aAAa;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAC1E,WAAU;gDACV,MAAM;;;;;;;;;;;;kDAIV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;iDAOP;;8CACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAiC,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,gBAAgB;;;;;;;;;;;;sDAGvF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAiC,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,UAAU;;;;;;8DAC/E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;gEAAgB;8EAAM,6LAAC;oEAAK,WAAU;8EAAkB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,WAAW;;;;;;;;;;;;sEACvG,6LAAC;4DAAE,WAAU;;gEAAqB;8EAAS,6LAAC;oEAAK,WAAU;8EAAiB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,UAAU,GAAG,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAKxI,QAAQ,KAAK,kBACZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDAAc,QAAQ,KAAK;;;;;;;;;;;;;;sCAMhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;8DACZ,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;gCAIT,SAAS,MAAM,KAAK,kBACnB,6LAAC;oCAAE,WAAU;8CAAiC;;;;;yDAE9C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAKnG,6LAAC;gDAAM,WAAU;0DACd,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,YAAY;;;;;;0EAElC,6LAAC;gEAAG,WAAU;0EACX,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;0EAEhC,6LAAC;gEAAG,WAAU;0EACX,QAAQ,cAAc,IAAI;;;;;;0EAE7B,6LAAC;gEAAG,WAAU;0EACX,QAAQ,OAAO,IAAI;;;;;;;uDAXf,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB/C;GArNM;KAAA;uCAuNS", "debugId": null}}, {"offset": {"line": 3264, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3270, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/budget/PaymentsList.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from \"react\";\nimport { Edit } from \"lucide-react\";\nimport { BudgetPayment, BudgetExpense } from \"../../types\";\nimport { formatCurrency, formatDate } from \"../../utils\";\n\ninterface PaymentsListProps {\n  payments: BudgetPayment[];\n  expenses: BudgetExpense[];\n}\n\nconst PaymentsList: React.FC<PaymentsListProps> = ({ payments, expenses }) => {\n  const [filter, setFilter] = useState<'all' | 'paid' | 'pending'>('all');\n  const [sortBy, setSortBy] = useState<string>('date');\n\n  // Get expense name by ID\n  const getExpenseName = (expenseId: string): string => {\n    const expense = expenses.find(e => e.expense_id === expenseId);\n    return expense ? expense.name : 'Unknown';\n  };\n\n  // Get category name by expense ID\n  const getCategoryName = (expenseId: string): string => {\n    const expense = expenses.find(e => e.expense_id === expenseId);\n    return expense ? (expense.category_name || 'Unknown') : 'Unknown';\n  };\n\n  // Filter payments based on selected filter\n  const filteredPayments = payments.filter(() => {\n    if (filter === 'all') return true;\n    if (filter === 'paid') return true; // All payments in this list are paid\n    return false;\n  });\n\n  // Sort payments based on selected sort option\n  const sortedPayments = [...filteredPayments].sort((a, b) => {\n    if (sortBy === 'date') {\n      return new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime();\n    }\n    return 0;\n  });\n\n  return (\n    <div>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div className=\"flex space-x-4\">\n          <button\n            onClick={() => setFilter('all')}\n            className={`px-4 py-1 rounded-md ${\n              filter === 'all'\n                ? 'bg-gray-200 text-black font-medium'\n                : 'bg-white text-gray-600 border border-gray-300'\n            }`}\n          >\n            All\n          </button>\n          <button\n            onClick={() => setFilter('paid')}\n            className={`px-4 py-1 rounded-md ${\n              filter === 'paid'\n                ? 'bg-gray-200 text-black font-medium'\n                : 'bg-white text-gray-600 border border-gray-300'\n            }`}\n          >\n            Paid\n          </button>\n          <button\n            onClick={() => setFilter('pending')}\n            className={`px-4 py-1 rounded-md ${\n              filter === 'pending'\n                ? 'bg-gray-200 text-black font-medium'\n                : 'bg-white text-gray-600 border border-gray-300'\n            }`}\n          >\n            Pending\n          </button>\n        </div>\n\n        <div className=\"flex items-center\">\n          <span className=\"text-gray-600 mr-2\">Sort by:</span>\n          <select\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value)}\n            className=\"p-1 border border-gray-300 rounded-md text-black\"\n          >\n            <option value=\"date\">Date of payment</option>\n            <option value=\"amount\">Amount</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Payment button removed - payments should be added from expense details */}\n\n      {sortedPayments.length === 0 ? (\n        <p className=\"text-gray-500 text-center py-8\">No payments recorded yet.</p>\n      ) : (\n        <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Expense\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Details\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Amount\n                </th>\n                <th className=\"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {sortedPayments.map((payment) => (\n                <tr key={payment.payment_id}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800\">\n                      PAID\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">{getExpenseName(payment.expense_id)}</div>\n                    <div className=\"text-sm text-gray-500\">{getCategoryName(payment.expense_id)}</div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900\">Date paid {formatDate(payment.payment_date)}</div>\n                    <div className=\"text-sm text-gray-500\">Paid by {payment.paid_by || 'Unknown'}</div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {formatCurrency(payment.amount)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-center text-sm font-medium\">\n                    <button className=\"text-indigo-600 hover:text-indigo-900\">\n                      <Edit size={18} />\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PaymentsList;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;;;AAJA;;;;AAWA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE;;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,yBAAyB;IACzB,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;QACpD,OAAO,UAAU,QAAQ,IAAI,GAAG;IAClC;IAEA,kCAAkC;IAClC,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;QACpD,OAAO,UAAW,QAAQ,aAAa,IAAI,YAAa;IAC1D;IAEA,2CAA2C;IAC3C,MAAM,mBAAmB,SAAS,MAAM,CAAC;QACvC,IAAI,WAAW,OAAO,OAAO;QAC7B,IAAI,WAAW,QAAQ,OAAO,MAAM,qCAAqC;QACzE,OAAO;IACT;IAEA,8CAA8C;IAC9C,MAAM,iBAAiB;WAAI;KAAiB,CAAC,IAAI,CAAC,CAAC,GAAG;QACpD,IAAI,WAAW,QAAQ;YACrB,OAAO,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;QAC9E;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,qBAAqB,EAC/B,WAAW,QACP,uCACA,iDACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,qBAAqB,EAC/B,WAAW,SACP,uCACA,iDACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,qBAAqB,EAC/B,WAAW,YACP,uCACA,iDACJ;0CACH;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;0CACrC,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;YAO5B,eAAe,MAAM,KAAK,kBACzB,6LAAC;gBAAE,WAAU;0BAAiC;;;;;qCAE9C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAmF;;;;;;;;;;;;;;;;;sCAKrG,6LAAC;4BAAM,WAAU;sCACd,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAU;0DAA4F;;;;;;;;;;;sDAI9G,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DAAqC,eAAe,QAAQ,UAAU;;;;;;8DACrF,6LAAC;oDAAI,WAAU;8DAAyB,gBAAgB,QAAQ,UAAU;;;;;;;;;;;;sDAE5E,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;;wDAAwB;wDAAW,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,YAAY;;;;;;;8DACjF,6LAAC;oDAAI,WAAU;;wDAAwB;wDAAS,QAAQ,OAAO,IAAI;;;;;;;;;;;;;sDAErE,6LAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;sDAEhC,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;;;;;;;;;;;;mCAnBT,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8B3C;GA1IM;KAAA;uCA4IS", "debugId": null}}, {"offset": {"line": 3611, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3617, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/budget/Budget.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { DollarSign, Download, Printer, Plus, Edit, Trash, X } from \"lucide-react\";\r\nimport EditableAmount from \"./EditableAmount\";\r\nimport { BudgetCategory, BudgetExpense, BudgetPayment, BudgetTotals } from \"../../types\";\r\nimport { getAuthToken, formatCurrency } from \"../../utils\";\r\nimport CategoryModal from \"./CategoryModal\";\r\nimport ExpenseModal from \"./ExpenseModal\";\r\nimport PaymentModal from \"./PaymentModal\";\r\nimport ExpenseDetails from \"./ExpenseDetails\";\r\nimport PaymentsList from \"./PaymentsList\";\r\n\r\ninterface BudgetProps {\r\n  setError: (error: string | null) => void;\r\n  setSuccessMessage: (message: string | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  loading: boolean;\r\n  error: string | null;\r\n  successMessage: string | null;\r\n}\r\n\r\nconst Budget: React.FC<BudgetProps> = ({\r\n  setError,\r\n  setSuccessMessage,\r\n  setLoading,\r\n  loading,\r\n  error,\r\n  successMessage\r\n}) => {\r\n  // Budget states\r\n  const [budgetCategories, setBudgetCategories] = useState<BudgetCategory[]>([]);\r\n  const [budgetExpenses, setBudgetExpenses] = useState<BudgetExpense[]>([]);\r\n  const [budgetPayments, setBudgetPayments] = useState<BudgetPayment[]>([]);\r\n  const [budgetTotals, setBudgetTotals] = useState<BudgetTotals>({\r\n    total_estimated: 0,\r\n    total_final: 0,\r\n    total_paid: 0\r\n  });\r\n  const [activeBudgetTab, setActiveBudgetTab] = useState<string>(\"budget\");\r\n\r\n  // Modal states\r\n  const [showCategoryModal, setShowCategoryModal] = useState<boolean>(false);\r\n  const [showExpenseModal, setShowExpenseModal] = useState<boolean>(false);\r\n  const [showPaymentModal, setShowPaymentModal] = useState<boolean>(false);\r\n  const [showExpenseDetails, setShowExpenseDetails] = useState<boolean>(false);\r\n  const [selectedCategory, setSelectedCategory] = useState<BudgetCategory | null>(null);\r\n  const [selectedExpense, setSelectedExpense] = useState<BudgetExpense | null>(null);\r\n\r\n  // Fetch budget data on component mount\r\n  useEffect(() => {\r\n    fetchBudgetData();\r\n  }, []);\r\n\r\n  // Budget API functions\r\n  const fetchBudgetData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const token = getAuthToken();\r\n\r\n      if (!token) {\r\n        console.warn('No authentication token found');\r\n        setError('Authentication required');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      const response = await axios.get(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/budget',\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          }\r\n        }\r\n      );\r\n\r\n      console.log('API response for budget data:', response.data);\r\n\r\n      // Process categories\r\n      if (response.data && response.data.categories && Array.isArray(response.data.categories)) {\r\n        setBudgetCategories(response.data.categories);\r\n      } else {\r\n        setBudgetCategories([]);\r\n      }\r\n\r\n      // Process expenses\r\n      if (response.data && response.data.expenses && Array.isArray(response.data.expenses)) {\r\n        setBudgetExpenses(response.data.expenses);\r\n\r\n        // Calculate budget totals\r\n        const totals = response.data.expenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {\r\n          acc.total_estimated += expense.estimated_budget || 0;\r\n          acc.total_final += expense.final_cost || 0;\r\n          acc.total_paid += expense.amount_paid || 0;\r\n          return acc;\r\n        }, { total_estimated: 0, total_final: 0, total_paid: 0 });\r\n\r\n        setBudgetTotals(totals);\r\n      } else {\r\n        setBudgetExpenses([]);\r\n        setBudgetTotals({ total_estimated: 0, total_final: 0, total_paid: 0 });\r\n      }\r\n\r\n      // Process payments\r\n      if (response.data && response.data.payments && Array.isArray(response.data.payments)) {\r\n        setBudgetPayments(response.data.payments);\r\n      } else {\r\n        setBudgetPayments([]);\r\n      }\r\n\r\n      setError(null);\r\n    } catch (err: any) {\r\n      console.error('Error fetching budget data:', err);\r\n      setError('Failed to load budget data');\r\n      setBudgetCategories([]);\r\n      setBudgetExpenses([]);\r\n      setBudgetPayments([]);\r\n      setBudgetTotals({ total_estimated: 0, total_final: 0, total_paid: 0 });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Delete a category\r\n  // Fix for the deleteCategory and deleteExpense functions\r\n\r\n\r\n\r\n// 1. Modified deleteCategory function\r\n// Delete a category function - adapted from the working checklist example\r\nconst deleteCategory = async (categoryId: string) => {\r\n  try {\r\n    setLoading(true);\r\n    const token = getAuthToken();\r\n\r\n    if (!token) {\r\n      console.warn('No authentication token found');\r\n      setError('Authentication required');\r\n      return;\r\n    }\r\n\r\n    if (!categoryId) {\r\n      console.warn('No category ID provided for deletion');\r\n      setError('Category ID is required for deletion');\r\n      return;\r\n    }\r\n\r\n    console.log('Deleting category with ID:', categoryId);\r\n\r\n    // Include the data in the request body as the server expects it\r\n    const response = await axios.delete(\r\n      'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-budget-category',\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        data: { category_id: categoryId } // Send the ID in the request body\r\n      }\r\n    );\r\n\r\n    console.log('API response for delete category:', response.data);\r\n\r\n    // Check for success using multiple indicators\r\n    if (\r\n      (response.data && response.data.success) || // Standard success format\r\n      (response.data && response.data.message && response.data.message.includes('success')) || // Message contains 'success'\r\n      (response.status >= 200 && response.status < 300) // HTTP success status code\r\n    ) {\r\n      // Remove the category from state\r\n      setBudgetCategories(budgetCategories.filter(cat => cat.category_id !== categoryId));\r\n      // Also remove all expenses associated with this category\r\n      const filteredExpenses = budgetExpenses.filter(exp => exp.category_id !== categoryId);\r\n      setBudgetExpenses(filteredExpenses);\r\n\r\n      // Recalculate totals after removing expenses\r\n      const totals = filteredExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {\r\n        acc.total_estimated += expense.estimated_budget || 0;\r\n        acc.total_final += expense.final_cost || 0;\r\n        acc.total_paid += expense.amount_paid || 0;\r\n        return acc;\r\n      }, { total_estimated: 0, total_final: 0, total_paid: 0 });\r\n\r\n      setBudgetTotals(totals);\r\n      setError(null); // Clear any previous errors\r\n      setSuccessMessage('Category deleted successfully');\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccessMessage(null);\r\n      }, 3000);\r\n    } else {\r\n      console.warn('Unexpected API response format:', response.data);\r\n      setError('Failed to delete category');\r\n    }\r\n  } catch (err: any) {\r\n    console.error('Error deleting category:', err);\r\n    if (err.response && err.response.data && err.response.data.error) {\r\n      setError(`Failed to delete category: ${err.response.data.error}`);\r\n    } else {\r\n      setError('Failed to delete category');\r\n    }\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n};\r\n\r\n// Delete an expense function - adapted from the working checklist example\r\nconst deleteExpense = async (expenseId: string) => {\r\n  try {\r\n    setLoading(true);\r\n    const token = getAuthToken();\r\n\r\n    if (!token) {\r\n      console.warn('No authentication token found');\r\n      setError('Authentication required');\r\n      return;\r\n    }\r\n\r\n    if (!expenseId) {\r\n      console.warn('No expense ID provided for deletion');\r\n      setError('Expense ID is required for deletion');\r\n      return;\r\n    }\r\n\r\n    console.log('Deleting expense with ID:', expenseId);\r\n\r\n    // Include the data in the request body as the server expects it\r\n    const response = await axios.delete(\r\n      'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-budget-expense',\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        data: { expense_id: expenseId } // Send the ID in the request body\r\n      }\r\n    );\r\n\r\n    console.log('API response for delete expense:', response.data);\r\n\r\n    // Check for success using multiple indicators\r\n    if (\r\n      (response.data && response.data.success) || // Standard success format\r\n      (response.data && response.data.message && response.data.message.includes('success')) || // Message contains 'success'\r\n      (response.status >= 200 && response.status < 300) // HTTP success status code\r\n    ) {\r\n      // Remove the expense from state\r\n      const filteredExpenses = budgetExpenses.filter(exp => exp.expense_id !== expenseId);\r\n      setBudgetExpenses(filteredExpenses);\r\n\r\n      // Recalculate totals\r\n      const totals = filteredExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {\r\n        acc.total_estimated += expense.estimated_budget || 0;\r\n        acc.total_final += expense.final_cost || 0;\r\n        acc.total_paid += expense.amount_paid || 0;\r\n        return acc;\r\n      }, { total_estimated: 0, total_final: 0, total_paid: 0 });\r\n\r\n      setBudgetTotals(totals);\r\n      setError(null); // Clear any previous errors\r\n      setSuccessMessage('Expense deleted successfully');\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccessMessage(null);\r\n      }, 3000);\r\n    } else {\r\n      console.warn('Unexpected API response format:', response.data);\r\n      setError('Failed to delete expense');\r\n    }\r\n  } catch (err: any) {\r\n    console.error('Error deleting expense:', err);\r\n    if (err.response && err.response.data && err.response.data.error) {\r\n      setError(`Failed to delete expense: ${err.response.data.error}`);\r\n    } else {\r\n      setError('Failed to delete expense');\r\n    }\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n};\r\n\r\n  // Update an expense - returns true if successful, false otherwise\r\n  const updateExpense = async (expenseId: string, updatedData: Partial<BudgetExpense>): Promise<boolean> => {\r\n    try {\r\n      setLoading(true);\r\n      const token = getAuthToken();\r\n\r\n      if (!token) {\r\n        setError('Authentication required');\r\n        return false;\r\n      }\r\n\r\n      console.log('Updating expense with ID:', expenseId, 'Data:', updatedData);\r\n\r\n      // Use PUT method since CORS is enabled\r\n      const response = await axios({\r\n        method: 'put',\r\n        url: 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-budget-expense',\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n          expense_id: expenseId,\r\n          name: updatedData.name,\r\n          estimated_budget: updatedData.estimated_budget,\r\n          final_cost: updatedData.final_cost,\r\n          notes: updatedData.notes\r\n        }\r\n      });\r\n\r\n      console.log('Update expense response:', response.data);\r\n      console.log('Response status:', response.status);\r\n      console.log('Response success indicator:', response.data?.success);\r\n      console.log('Response message:', response.data?.message);\r\n\r\n      // Check for success using multiple indicators since the API might return different formats\r\n      if (\r\n        (response.data && response.data.success) || // Standard success format\r\n        (response.data && response.data.message && response.data.message.includes('success')) || // Message contains 'success'\r\n        (response.status >= 200 && response.status < 300) // HTTP success status code\r\n      ) {\r\n        // Update the expense in state\r\n        const updatedExpenses = budgetExpenses.map(exp => {\r\n          if (exp.expense_id === expenseId) {\r\n            return { ...exp, ...updatedData };\r\n          }\r\n          return exp;\r\n        });\r\n\r\n        setBudgetExpenses(updatedExpenses);\r\n\r\n        // Recalculate totals\r\n        const totals = updatedExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {\r\n          acc.total_estimated += expense.estimated_budget || 0;\r\n          acc.total_final += expense.final_cost || 0;\r\n          acc.total_paid += expense.amount_paid || 0;\r\n          return acc;\r\n        }, { total_estimated: 0, total_final: 0, total_paid: 0 });\r\n\r\n        setBudgetTotals(totals);\r\n        setSuccessMessage('Expense updated successfully');\r\n        setTimeout(() => setSuccessMessage(null), 3000);\r\n        return true;\r\n      } else {\r\n        setError('Failed to update expense in database');\r\n        setTimeout(() => setError(null), 3000);\r\n        return false;\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error updating expense:', err);\r\n      setError(err.response?.data?.error || 'Failed to update expense');\r\n\r\n      // We'll still update the UI optimistically\r\n      const updatedExpenses = budgetExpenses.map(exp => {\r\n        if (exp.expense_id === expenseId) {\r\n          return { ...exp, ...updatedData };\r\n        }\r\n        return exp;\r\n      });\r\n\r\n      setBudgetExpenses(updatedExpenses);\r\n\r\n      // Recalculate totals\r\n      const totals = updatedExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {\r\n        acc.total_estimated += expense.estimated_budget || 0;\r\n        acc.total_final += expense.final_cost || 0;\r\n        acc.total_paid += expense.amount_paid || 0;\r\n        return acc;\r\n      }, { total_estimated: 0, total_final: 0, total_paid: 0 });\r\n\r\n      setBudgetTotals(totals);\r\n      return false;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Get expenses for a specific category\r\n  const getExpensesByCategory = (categoryId: string) => {\r\n    return budgetExpenses.filter(expense => expense.category_id === categoryId);\r\n  };\r\n\r\n  // Calculate total for a category\r\n  const getCategoryTotal = (categoryId: string, field: 'estimated_budget' | 'final_cost' | 'amount_paid') => {\r\n    return getExpensesByCategory(categoryId).reduce((total, expense) => total + (expense[field] || 0), 0);\r\n  };\r\n\r\n  // State for selected category to show details\r\n  const [selectedCategoryForDetails, setSelectedCategoryForDetails] = useState<BudgetCategory | null>(null);\r\n\r\n  // Function to get category color for chart\r\n  const getCategoryColor = (index: number) => {\r\n    const colors = [\r\n      '#F7A74F', // Orange for Events\r\n      '#4F7BF7', // Blue for Catering\r\n      '#4FE8B7', // Teal for Photography\r\n      '#9D4FF7', // Purple for Planning\r\n      '#F74F4F', // Red for Jewellery\r\n      '#4FC3F7', // Light Blue for Health & Beauty\r\n      '#F7E84F', // Yellow for Entertainment\r\n      '#F74F9D', // Pink for Guests\r\n      '#4FF77B', // Green for Honeymoon\r\n      '#F7874F', // Coral for Transportation\r\n      '#4F8CF7', // Sky Blue for Gifts\r\n      '#C44FF7', // Lavender for Miscellaneous\r\n    ];\r\n    return colors[index % colors.length];\r\n  };\r\n\r\n  // State for hover information on pie chart\r\n  const [hoveredCategory, setHoveredCategory] = useState<{name: string, value: number} | null>(null);\r\n\r\n  // We're using SVG for the chart, so no need for chart library data structure\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n      {/* Tabs and Actions */}\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <div className=\"border border-gray-300 rounded-md overflow-hidden\">\r\n          <button\r\n            onClick={() => setActiveBudgetTab(\"budget\")}\r\n            className={`px-4 py-2 ${\r\n              activeBudgetTab === \"budget\"\r\n                ? \"bg-gray-200 text-black font-medium\"\r\n                : \"bg-white text-black\"\r\n            }`}\r\n          >\r\n            Budget\r\n          </button>\r\n          <button\r\n            onClick={() => setActiveBudgetTab(\"payments\")}\r\n            className={`px-4 py-2 ${\r\n              activeBudgetTab === \"payments\"\r\n                ? \"bg-gray-200 text-black font-medium\"\r\n                : \"bg-white text-black\"\r\n            }`}\r\n          >\r\n            Payments\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"flex\">\r\n          <button\r\n            className=\"flex items-center gap-1 text-gray-600 hover:text-gray-800 mr-3\"\r\n            onClick={() => console.log('Download budget')}\r\n          >\r\n            <Download size={18} />\r\n            <span>Download</span>\r\n          </button>\r\n          <button\r\n            className=\"flex items-center gap-1 text-gray-600 hover:text-gray-800\"\r\n            onClick={() => window.print()}\r\n          >\r\n            <Printer size={18} />\r\n            <span>Print</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <div className=\"mb-4 p-2 bg-red-100 text-red-700 rounded-md\">\r\n          {error}\r\n        </div>\r\n      )}\r\n\r\n      {/* Success message */}\r\n      {successMessage && (\r\n        <div className=\"mb-4 p-2 bg-green-100 text-green-700 rounded-md\">\r\n          {successMessage}\r\n        </div>\r\n      )}\r\n\r\n      {/* Loading state */}\r\n      {loading ? (\r\n        <div className=\"flex justify-center items-center h-40\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#B31B1E]\"></div>\r\n        </div>\r\n      ) : (\r\n        <>\r\n          {activeBudgetTab === \"budget\" ? (\r\n            <div className=\"flex flex-col md:flex-row gap-6\">\r\n              {/* Left Column - Categories List */}\r\n              <div className=\"w-full md:w-1/3 border border-gray-200 rounded-lg overflow-hidden\">\r\n                {/* Add Category Button */}\r\n                <div className=\"p-3 border-b border-gray-200 bg-gray-50\">\r\n                  <button\r\n                    onClick={() => setShowCategoryModal(true)}\r\n                    className=\"flex items-center gap-2 text-[#B31B1E] hover:text-[#8a1416]\"\r\n                  >\r\n                    <Plus size={18} className=\"text-[#B31B1E]\" />\r\n                    <span>New category</span>\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Categories List */}\r\n                <div className=\"divide-y divide-gray-200\">\r\n                  {budgetCategories.length === 0 ? (\r\n                    <p className=\"text-gray-500 text-center py-4\">No budget categories yet. Add a category to get started!</p>\r\n                  ) : (\r\n                    budgetCategories.map((category) => (\r\n                      <div\r\n                        key={category.category_id}\r\n                        className={`flex justify-between items-center p-4 hover:bg-gray-50 cursor-pointer ${selectedCategoryForDetails?.category_id === category.category_id ? 'border-l-4 border-[#B31B1E]' : ''}`}\r\n                      >\r\n                        <div\r\n                          className=\"flex items-center gap-3 flex-grow\"\r\n                          onClick={() => setSelectedCategoryForDetails(category)}\r\n                        >\r\n                          <span className=\"text-black\">{category.name}</span>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <span className=\"text-black font-medium\">{formatCurrency(getCategoryTotal(category.category_id, 'estimated_budget'))}</span>\r\n                          <button\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              if (window.confirm(`Are you sure you want to delete the category \"${category.name}\"? This will also delete all expenses in this category.`)) {\r\n                                deleteCategory(category.category_id);\r\n                              }\r\n                            }}\r\n                            className=\"text-gray-500 hover:text-[#B31B1E] ml-2\"\r\n                          >\r\n                            <Trash size={16} />\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Right Column - Budget Details or Category Details */}\r\n              <div className=\"w-full md:w-2/3\">\r\n                {selectedCategoryForDetails ? (\r\n                  /* Category Details View */\r\n                  <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\r\n                    {/* Category Header */}\r\n                    <div className=\"flex justify-between items-center p-4 bg-white border-b border-gray-200\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <div className=\"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center\">\r\n                          <DollarSign size={20} className=\"text-gray-600\" />\r\n                        </div>\r\n                        <h3 className=\"text-xl font-semibold text-black\">{selectedCategoryForDetails.name}</h3>\r\n                      </div>\r\n                      <button onClick={() => setSelectedCategoryForDetails(null)} className=\"text-gray-500 hover:text-gray-700\">\r\n                        <X size={20} />\r\n                      </button>\r\n                    </div>\r\n\r\n                    {/* Category Budget Info */}\r\n                    <div className=\"p-4 border-b border-gray-200\">\r\n                      <div className=\"flex justify-between items-center mb-2\">\r\n                        <span className=\"text-gray-600\">Estimated budget:</span>\r\n                        <span className=\"text-black font-medium\">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'estimated_budget'))}</span>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center\">\r\n                        <span className=\"text-gray-600\">Final Cost:</span>\r\n                        <span className=\"text-black font-medium\">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'final_cost'))}</span>\r\n                      </div>\r\n\r\n                      {/* Progress bar */}\r\n                      <div className=\"mt-3 bg-gray-200 rounded-full h-2.5 overflow-hidden\">\r\n                        <div\r\n                          className=\"bg-green-500 h-2.5\"\r\n                          style={{ width: `${Math.min(100, (getCategoryTotal(selectedCategoryForDetails.category_id, 'estimated_budget') / budgetTotals.total_estimated) * 100)}%` }}\r\n                        ></div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Expenses Table */}\r\n                    <div className=\"divide-y divide-gray-200\">\r\n                      <div className=\"grid grid-cols-4 p-3 bg-gray-50 text-xs font-medium text-gray-500 uppercase\">\r\n                        <div>EXPENSE</div>\r\n                        <div className=\"text-right\">ESTIMATED BUDGET</div>\r\n                        <div className=\"text-right\">FINAL COST</div>\r\n                        <div className=\"text-right\">PAID</div>\r\n                      </div>\r\n\r\n                      {getExpensesByCategory(selectedCategoryForDetails.category_id).length === 0 ? (\r\n                        <p className=\"text-gray-500 text-center py-4\">No expenses in this category yet.</p>\r\n                      ) : (\r\n                        getExpensesByCategory(selectedCategoryForDetails.category_id).map((expense) => (\r\n                          <div key={expense.expense_id} className=\"grid grid-cols-4 p-4 hover:bg-gray-50 relative group\">\r\n                            <div className=\"text-black flex items-center\">\r\n                              {expense.name}\r\n                              <button\r\n                                onClick={() => {\r\n                                  if (window.confirm(`Are you sure you want to delete the expense \"${expense.name}\"?`)) {\r\n                                    deleteExpense(expense.expense_id);\r\n                                  }\r\n                                }}\r\n                                className=\"text-gray-400 hover:text-[#B31B1E] ml-2 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                                title=\"Delete expense\"\r\n                              >\r\n                                <Trash size={14} />\r\n                              </button>\r\n                            </div>\r\n                            <div className=\"text-black text-right\">\r\n                              <EditableAmount\r\n                                initialValue={expense.estimated_budget}\r\n                                onSave={async (newValue) => {\r\n                                  if (newValue !== expense.estimated_budget) {\r\n                                    const updatedExpense = {\r\n                                      ...expense,\r\n                                      estimated_budget: newValue,\r\n                                      name: expense.name,\r\n                                      final_cost: expense.final_cost\r\n                                    };\r\n                                    try {\r\n                                      await updateExpense(updatedExpense.expense_id, updatedExpense);\r\n                                      return true; // Success\r\n                                    } catch (error) {\r\n                                      console.error('Error updating estimated budget:', error);\r\n                                      return false; // Failed\r\n                                    }\r\n                                  }\r\n                                  return true; // No change needed\r\n                                }}\r\n                              />\r\n                            </div>\r\n                            <div className=\"text-black text-right\">\r\n                              <EditableAmount\r\n                                initialValue={expense.final_cost}\r\n                                onSave={async (newValue) => {\r\n                                  if (newValue !== expense.final_cost) {\r\n                                    const updatedExpense = {\r\n                                      ...expense,\r\n                                      final_cost: newValue,\r\n                                      name: expense.name,\r\n                                      estimated_budget: expense.estimated_budget\r\n                                    };\r\n                                    try {\r\n                                      await updateExpense(updatedExpense.expense_id, updatedExpense);\r\n                                      return true; // Success\r\n                                    } catch (error) {\r\n                                      console.error('Error updating final cost:', error);\r\n                                      return false; // Failed\r\n                                    }\r\n                                  }\r\n                                  return true; // No change needed\r\n                                }}\r\n                              />\r\n                            </div>\r\n                            <div\r\n                              className=\"text-black text-right cursor-pointer hover:text-[#B31B1E]\"\r\n                              onClick={() => {\r\n                                setSelectedExpense(expense);\r\n                                setShowPaymentModal(true);\r\n                              }}\r\n                              title=\"Click to add payment\"\r\n                            >\r\n                              {formatCurrency(expense.amount_paid)}\r\n                            </div>\r\n                          </div>\r\n                        ))\r\n                      )}\r\n\r\n                      {/* Add Expense Button */}\r\n                      <div className=\"p-4\">\r\n                        <button\r\n                          onClick={() => {\r\n                            setSelectedCategory(selectedCategoryForDetails);\r\n                            setShowExpenseModal(true);\r\n                          }}\r\n                          className=\"flex items-center gap-1 text-[#B31B1E] hover:text-[#8a1416]\"\r\n                        >\r\n                          <Plus size={16} />\r\n                          <span>Add new expense</span>\r\n                        </button>\r\n                      </div>\r\n\r\n                      {/* Total Row */}\r\n                      <div className=\"grid grid-cols-4 p-4 bg-gray-50 font-medium\">\r\n                        <div className=\"text-black\">Total:</div>\r\n                        <div className=\"text-black text-right\">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'estimated_budget'))}</div>\r\n                        <div className=\"text-black text-right\">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'final_cost'))}</div>\r\n                        <div className=\"text-black text-right\">{formatCurrency(getCategoryTotal(selectedCategoryForDetails.category_id, 'amount_paid'))}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  /* Budget Overview */\r\n                  <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\r\n                    <div className=\"p-6 border-b border-gray-200\">\r\n                      <h2 className=\"text-2xl font-bold text-center text-black mb-6\">Budget</h2>\r\n\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                        {/* Estimated Budget */}\r\n                        <div className=\"flex flex-col items-center\">\r\n                          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2\">\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"text-gray-600\">\r\n                              <path d=\"M12.5 16.8c-1.3-1-2.5-2.1-3.5-3.4C8 12 7.5 10.6 7.5 9c0-1.4.5-2.7 1.5-3.6 1-1 2.3-1.4 3.8-1.4 1.5 0 2.8.5 3.8 1.4 1 1 1.5 2.2 1.5 3.6 0 1.6-.5 3-1.5 4.4-1 1.3-2.2 2.4-3.5 3.4h-.6z\"/>\r\n                              <path d=\"M17.5 22h-11c-1.5 0-2.8-.5-3.8-1.5-1-1-1.5-2.3-1.5-3.8 0-1.5.5-2.8 1.5-3.8 1-1 2.3-1.5 3.8-1.5h11c1.5 0 2.8.5 3.8 1.5 1 1 1.5 2.3 1.5 3.8 0 1.5-.5 2.8-1.5 3.8-1 1-2.3 1.5-3.8 1.5z\"/>\r\n                            </svg>\r\n                          </div>\r\n                          <h3 className=\"text-center text-gray-600 uppercase text-sm font-medium\">ESTIMATED BUDGET</h3>\r\n                          <p className=\"text-3xl font-bold text-black mt-1\">{formatCurrency(budgetTotals.total_estimated)}</p>\r\n                          <button\r\n                            className=\"text-[#B31B1E] text-sm mt-2\"\r\n                            onClick={() => console.log('Edit budget')}\r\n                          >\r\n                            Edit budget\r\n                          </button>\r\n                        </div>\r\n\r\n                        {/* Final Cost */}\r\n                        <div className=\"flex flex-col items-center\">\r\n                          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2\">\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"text-gray-600\">\r\n                              <rect x=\"2\" y=\"5\" width=\"20\" height=\"14\" rx=\"2\"/>\r\n                              <line x1=\"2\" y1=\"10\" x2=\"22\" y2=\"10\"/>\r\n                            </svg>\r\n                          </div>\r\n                          <h3 className=\"text-center text-gray-600 uppercase text-sm font-medium\">FINAL COST</h3>\r\n                          <p className=\"text-3xl font-bold text-black mt-1\">{formatCurrency(budgetTotals.total_final)}</p>\r\n                          <div className=\"flex text-sm mt-2\">\r\n                            <p className=\"text-gray-600\">Paid: <span className=\"text-green-600\">{formatCurrency(budgetTotals.total_paid)}</span></p>\r\n                            <p className=\"text-gray-600 ml-4\">Pending: <span className=\"text-blue-600\">{formatCurrency(budgetTotals.total_final - budgetTotals.total_paid)}</span></p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Expenses Chart */}\r\n                    <div className=\"p-6\">\r\n                      <h3 className=\"text-xl font-semibold mb-4 text-black\">Expenses</h3>\r\n\r\n                      {budgetCategories.length === 0 ? (\r\n                        <p className=\"text-gray-500 text-center py-4\">No budget categories yet. Add a category to get started!</p>\r\n                      ) : (\r\n                        <div>\r\n                          {/* Circular Progress Chart - Representing different categories with their colors */}\r\n                          <div className=\"flex justify-center mb-6\">\r\n                            <div className=\"w-80 h-80 relative flex items-center justify-center\">\r\n                              {/* Hover tooltip */}\r\n                              {hoveredCategory && (\r\n                                <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full bg-white p-2 rounded shadow-md z-10 border border-gray-200\">\r\n                                  <p className=\"text-sm font-medium\">{hoveredCategory.name}</p>\r\n                                  <p className=\"text-sm\">{formatCurrency(hoveredCategory.value)}</p>\r\n                                </div>\r\n                              )}\r\n                              {/* Create circular progress chart */}\r\n                              <svg viewBox=\"0 0 200 200\" className=\"w-full h-full\">\r\n                                {/* Background circles with increased spacing between them */}\r\n                                <circle cx=\"100\" cy=\"100\" r=\"80\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n                                <circle cx=\"100\" cy=\"100\" r=\"72\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n                                <circle cx=\"100\" cy=\"100\" r=\"64\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n                                <circle cx=\"100\" cy=\"100\" r=\"56\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n                                <circle cx=\"100\" cy=\"100\" r=\"48\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n                                <circle cx=\"100\" cy=\"100\" r=\"40\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n                                <circle cx=\"100\" cy=\"100\" r=\"32\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n                                <circle cx=\"100\" cy=\"100\" r=\"24\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n                                <circle cx=\"100\" cy=\"100\" r=\"16\" fill=\"none\" stroke=\"#f0f0f0\" strokeWidth=\"6\" strokeLinecap=\"round\" />\r\n\r\n                                {/* Monetary value labels positioned in the top-right quadrant */}\r\n                                {(() => {\r\n                                  // Find the maximum budget value for scaling\r\n                                  const sortedCategories = [...budgetCategories].sort((a, b) => {\r\n                                    const aValue = getCategoryTotal(a.category_id, 'estimated_budget');\r\n                                    const bValue = getCategoryTotal(b.category_id, 'estimated_budget');\r\n                                    return bValue - aValue; // Descending order\r\n                                  });\r\n\r\n                                  const maxBudgetValue = sortedCategories.length > 0\r\n                                    ? getCategoryTotal(sortedCategories[0].category_id, 'estimated_budget')\r\n                                    : 0;\r\n\r\n                                  // Maximum value for the chart (rounded up to nearest 20k)\r\n                                  const chartMaxValue = Math.ceil(maxBudgetValue / 20000) * 20000;\r\n\r\n                                  // Generate 6 labels for the top-right quadrant (0 to 180 degrees)\r\n                                  return Array.from({ length: 7 }).map((_, i) => {\r\n                                    // Calculate angle (0 to 180 degrees)\r\n                                    const angle = (i * 30) * Math.PI / 180;\r\n\r\n                                    // Calculate value (0 to chartMaxValue)\r\n                                    const value = Math.round((i / 6) * chartMaxValue / 1000);\r\n\r\n                                    // Position the label\r\n                                    const x = 100 + 90 * Math.cos(angle - Math.PI/2);\r\n                                    const y = 100 + 90 * Math.sin(angle - Math.PI/2);\r\n\r\n                                    // Determine text anchor based on position\r\n                                    let textAnchor = \"middle\";\r\n                                    if (angle < Math.PI/4) textAnchor = \"start\";\r\n                                    if (angle > 3*Math.PI/4) textAnchor = \"end\";\r\n\r\n                                    return (\r\n                                      <text\r\n                                        key={i}\r\n                                        x={x}\r\n                                        y={y}\r\n                                        fontSize=\"8\"\r\n                                        fill=\"#888\"\r\n                                        textAnchor={textAnchor}\r\n                                        dominantBaseline=\"middle\"\r\n                                      >\r\n                                        {value > 0 ? `${value}k` : '0'}\r\n                                      </text>\r\n                                    );\r\n                                  });\r\n                                })()}\r\n\r\n                                {budgetCategories.length > 0 && (\r\n                                  <>\r\n                                    {/* Calculate and render circular progress arcs */}\r\n                                    {(() => {\r\n                                      // Sort categories by estimated budget (descending) for better visualization\r\n                                      const sortedCategories = [...budgetCategories].sort((a, b) => {\r\n                                        const aValue = getCategoryTotal(a.category_id, 'estimated_budget');\r\n                                        const bValue = getCategoryTotal(b.category_id, 'estimated_budget');\r\n                                        return bValue - aValue; // Descending order\r\n                                      });\r\n\r\n                                      // Find the maximum budget value for scaling\r\n                                      const maxBudgetValue = sortedCategories.length > 0\r\n                                        ? getCategoryTotal(sortedCategories[0].category_id, 'estimated_budget')\r\n                                        : 0;\r\n\r\n                                      // Maximum value for the chart (rounded up to nearest 20k)\r\n                                      const chartMaxValue = Math.ceil(maxBudgetValue / 20000) * 20000;\r\n\r\n                                      return sortedCategories.map((category, index) => {\r\n                                        const categoryValue = getCategoryTotal(category.category_id, 'estimated_budget');\r\n                                        if (categoryValue === 0) return null; // Skip zero-value categories\r\n\r\n                                        // Calculate the percentage of the max value\r\n                                        const percentage = categoryValue / chartMaxValue;\r\n\r\n                                        // Calculate the angle size based on the percentage (180 degrees is half circle)\r\n                                        // We use 180 degrees to show the arcs in the top-right quadrant\r\n                                        const angleSize = percentage * 180;\r\n\r\n                                        // All arcs start from the same angle (0 degrees, which is at the right)\r\n                                        const startAngle = 0; // 0 degrees is at the right (3 o'clock position)\r\n\r\n                                        // Calculate radius with gaps between circles\r\n                                        const radius = 80 - (index * 8);\r\n                                        if (radius < 12) return null; // Skip if we run out of space\r\n\r\n                                        // Convert angles to radians (subtract 90 to start at the top)\r\n                                        const startRad = (startAngle - 90) * Math.PI / 180;\r\n                                        const endRad = (startAngle + angleSize - 90) * Math.PI / 180;\r\n\r\n                                        // Calculate arc points\r\n                                        const x1 = 100 + radius * Math.cos(startRad);\r\n                                        const y1 = 100 + radius * Math.sin(startRad);\r\n                                        const x2 = 100 + radius * Math.cos(endRad);\r\n                                        const y2 = 100 + radius * Math.sin(endRad);\r\n\r\n                                        // Determine if the arc should be drawn as a large arc\r\n                                        const largeArcFlag = angleSize > 180 ? 1 : 0;\r\n\r\n                                        // Create the SVG path for the arc\r\n                                        const path = [\r\n                                          `M ${x1} ${y1}`,\r\n                                          `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`\r\n                                        ].join(' ');\r\n\r\n                                        return (\r\n                                          <path\r\n                                            key={category.category_id}\r\n                                            d={path}\r\n                                            fill=\"none\"\r\n                                            stroke={getCategoryColor(index)}\r\n                                            strokeWidth=\"6\"\r\n                                            strokeLinecap=\"round\"\r\n                                            strokeLinejoin=\"round\"\r\n                                            onMouseEnter={() => setHoveredCategory({name: category.name, value: categoryValue})}\r\n                                            onMouseLeave={() => setHoveredCategory(null)}\r\n                                            style={{ cursor: 'pointer' }}\r\n                                          />\r\n                                        );\r\n                                      });\r\n                                    })()}\r\n                                    {/* Center white circle */}\r\n                                    <circle cx=\"100\" cy=\"100\" r=\"10\" fill=\"white\" stroke=\"#f0f0f0\" strokeWidth=\"1\" />\r\n                                  </>\r\n                                )}\r\n                              </svg>\r\n                            </div>\r\n                          </div>\r\n\r\n                          {/* Chart Legend - Two columns with category names and values */}\r\n                          <div className=\"flex justify-between\">\r\n                            {/* Left column */}\r\n                            <div className=\"w-1/2 pr-4\">\r\n                              {budgetCategories.slice(0, Math.ceil(budgetCategories.length / 2)).map((category, index) => (\r\n                                <div key={category.category_id} className=\"flex items-center justify-between mb-2\">\r\n                                  <div className=\"flex items-center gap-2\">\r\n                                    <div\r\n                                      className=\"w-3 h-5 rounded-sm\"\r\n                                      style={{ backgroundColor: getCategoryColor(index) }}\r\n                                    ></div>\r\n                                    <span className=\"text-sm font-medium\" style={{ color: getCategoryColor(index) }}>{category.name}</span>\r\n                                  </div>\r\n                                  <span className=\"text-sm font-medium\" style={{ color: getCategoryColor(index) }}>\r\n                                    ₹ {getCategoryTotal(category.category_id, 'estimated_budget').toLocaleString()}\r\n                                  </span>\r\n                                </div>\r\n                              ))}\r\n                            </div>\r\n\r\n                            {/* Right column */}\r\n                            <div className=\"w-1/2 pl-4\">\r\n                              {budgetCategories.slice(Math.ceil(budgetCategories.length / 2)).map((category, index) => (\r\n                                <div key={category.category_id} className=\"flex items-center justify-between mb-2\">\r\n                                  <div className=\"flex items-center gap-2\">\r\n                                    <div\r\n                                      className=\"w-3 h-5 rounded-sm\"\r\n                                      style={{ backgroundColor: getCategoryColor(index + Math.ceil(budgetCategories.length / 2)) }}\r\n                                    ></div>\r\n                                    <span className=\"text-sm font-medium\" style={{ color: getCategoryColor(index + Math.ceil(budgetCategories.length / 2)) }}>\r\n                                      {category.name}\r\n                                    </span>\r\n                                  </div>\r\n                                  <span className=\"text-sm font-medium\" style={{ color: getCategoryColor(index + Math.ceil(budgetCategories.length / 2)) }}>\r\n                                    ₹ {getCategoryTotal(category.category_id, 'estimated_budget').toLocaleString()}\r\n                                  </span>\r\n                                </div>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <PaymentsList\r\n              payments={budgetPayments}\r\n              expenses={budgetExpenses}\r\n            />\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {/* Modals */}\r\n      {showCategoryModal && (\r\n        <CategoryModal\r\n          onClose={() => setShowCategoryModal(false)}\r\n          onSave={async (name) => {\r\n            try {\r\n              const token = getAuthToken();\r\n\r\n              if (!token) {\r\n                setError('Authentication required');\r\n                return;\r\n              }\r\n\r\n              const response = await axios({\r\n                method: 'post',\r\n                url: 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-budget-item',\r\n                headers: {\r\n                  'Authorization': `Bearer ${token}`,\r\n                  'Content-Type': 'application/json'\r\n                },\r\n                data: { name }\r\n              });\r\n\r\n              if (response.data && response.data.category) {\r\n                setBudgetCategories([...budgetCategories, response.data.category]);\r\n                setSuccessMessage('Category added successfully');\r\n                setTimeout(() => setSuccessMessage(null), 3000);\r\n                setShowCategoryModal(false);\r\n              } else {\r\n                setError('Failed to add category');\r\n              }\r\n            } catch (err: any) {\r\n              console.error('Error adding category:', err);\r\n              setError(err.response?.data?.error || 'Failed to add category');\r\n            }\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {showExpenseModal && (\r\n        <ExpenseModal\r\n          categories={budgetCategories}\r\n          selectedCategory={selectedCategory}\r\n          onClose={() => {\r\n            setShowExpenseModal(false);\r\n            setSelectedCategory(null);\r\n          }}\r\n          onSave={async (expenseData) => {\r\n            try {\r\n              const token = getAuthToken();\r\n\r\n              if (!token) {\r\n                setError('Authentication required');\r\n                return;\r\n              }\r\n\r\n              const response = await axios({\r\n                method: 'post',\r\n                url: 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-budget-expense',\r\n                headers: {\r\n                  'Authorization': `Bearer ${token}`,\r\n                  'Content-Type': 'application/json'\r\n                },\r\n                data: expenseData\r\n              });\r\n\r\n              if (response.data && response.data.expense) {\r\n                setBudgetExpenses([...budgetExpenses, response.data.expense]);\r\n                // Recalculate totals\r\n                const updatedExpenses = [...budgetExpenses, response.data.expense];\r\n                const totals = updatedExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {\r\n                  acc.total_estimated += expense.estimated_budget || 0;\r\n                  acc.total_final += expense.final_cost || 0;\r\n                  acc.total_paid += expense.amount_paid || 0;\r\n                  return acc;\r\n                }, { total_estimated: 0, total_final: 0, total_paid: 0 });\r\n\r\n                setBudgetTotals(totals);\r\n                setSuccessMessage('Expense added successfully');\r\n                setTimeout(() => setSuccessMessage(null), 3000);\r\n                setShowExpenseModal(false);\r\n                setSelectedCategory(null);\r\n              } else {\r\n                setError('Failed to add expense');\r\n              }\r\n            } catch (err: any) {\r\n              console.error('Error adding expense:', err);\r\n              setError(err.response?.data?.error || 'Failed to add expense');\r\n            }\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {showPaymentModal && (\r\n        <PaymentModal\r\n          expenses={budgetExpenses}\r\n          selectedExpenseId={selectedExpense?.expense_id}\r\n          onClose={() => setShowPaymentModal(false)}\r\n          onSave={async (paymentData) => {\r\n            try {\r\n              const token = getAuthToken();\r\n\r\n              if (!token) {\r\n                setError('Authentication required');\r\n                return;\r\n              }\r\n\r\n              const response = await axios({\r\n                method: 'post',\r\n                url: 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-budget-payment',\r\n                headers: {\r\n                  'Authorization': `Bearer ${token}`,\r\n                  'Content-Type': 'application/json'\r\n                },\r\n                data: paymentData\r\n              });\r\n\r\n              if (response.data && response.data.payment) {\r\n                setBudgetPayments([...budgetPayments, response.data.payment]);\r\n                // Update the expense's amount_paid\r\n                const updatedExpenses = budgetExpenses.map(expense => {\r\n                  if (expense.expense_id === paymentData.expense_id) {\r\n                    return {\r\n                      ...expense,\r\n                      amount_paid: (expense.amount_paid || 0) + paymentData.amount\r\n                    };\r\n                  }\r\n                  return expense;\r\n                });\r\n\r\n                setBudgetExpenses(updatedExpenses);\r\n\r\n                // Recalculate totals\r\n                const totals = updatedExpenses.reduce((acc: BudgetTotals, expense: BudgetExpense) => {\r\n                  acc.total_estimated += expense.estimated_budget || 0;\r\n                  acc.total_final += expense.final_cost || 0;\r\n                  acc.total_paid += expense.amount_paid || 0;\r\n                  return acc;\r\n                }, { total_estimated: 0, total_final: 0, total_paid: 0 });\r\n\r\n                setBudgetTotals(totals);\r\n                setSuccessMessage('Payment added successfully');\r\n                setTimeout(() => setSuccessMessage(null), 3000);\r\n                setShowPaymentModal(false);\r\n              } else {\r\n                setError('Failed to add payment');\r\n              }\r\n            } catch (err: any) {\r\n              console.error('Error adding payment:', err);\r\n              setError(err.response?.data?.error || 'Failed to add payment');\r\n            }\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {showExpenseDetails && selectedExpense && (\r\n        <ExpenseDetails\r\n          expense={selectedExpense}\r\n          payments={budgetPayments.filter(p => p.expense_id === selectedExpense.expense_id)}\r\n          onClose={() => {\r\n            setShowExpenseDetails(false);\r\n            setSelectedExpense(null);\r\n          }}\r\n          onAddPayment={() => {\r\n            // Keep the selected expense when opening the payment modal\r\n            setShowExpenseDetails(false);\r\n            setShowPaymentModal(true);\r\n          }}\r\n          onUpdateExpense={updateExpense}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Budget;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAsBA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,iBAAiB,EACjB,UAAU,EACV,OAAO,EACP,KAAK,EACL,cAAc,EACf;;IACC,gBAAgB;IAChB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,iBAAiB;QACjB,aAAa;QACb,YAAY;IACd;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,eAAe;IACf,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAChF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAE7E,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,4EACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAGF,QAAQ,GAAG,CAAC,iCAAiC,SAAS,IAAI;YAE1D,qBAAqB;YACrB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,UAAU,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,UAAU,GAAG;gBACxF,oBAAoB,SAAS,IAAI,CAAC,UAAU;YAC9C,OAAO;gBACL,oBAAoB,EAAE;YACxB;YAEA,mBAAmB;YACnB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,QAAQ,GAAG;gBACpF,kBAAkB,SAAS,IAAI,CAAC,QAAQ;gBAExC,0BAA0B;gBAC1B,MAAM,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAmB;oBAC/D,IAAI,eAAe,IAAI,QAAQ,gBAAgB,IAAI;oBACnD,IAAI,WAAW,IAAI,QAAQ,UAAU,IAAI;oBACzC,IAAI,UAAU,IAAI,QAAQ,WAAW,IAAI;oBACzC,OAAO;gBACT,GAAG;oBAAE,iBAAiB;oBAAG,aAAa;oBAAG,YAAY;gBAAE;gBAEvD,gBAAgB;YAClB,OAAO;gBACL,kBAAkB,EAAE;gBACpB,gBAAgB;oBAAE,iBAAiB;oBAAG,aAAa;oBAAG,YAAY;gBAAE;YACtE;YAEA,mBAAmB;YACnB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,QAAQ,GAAG;gBACpF,kBAAkB,SAAS,IAAI,CAAC,QAAQ;YAC1C,OAAO;gBACL,kBAAkB,EAAE;YACtB;YAEA,SAAS;QACX,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;YACT,oBAAoB,EAAE;YACtB,kBAAkB,EAAE;YACpB,kBAAkB,EAAE;YACpB,gBAAgB;gBAAE,iBAAiB;gBAAG,aAAa;gBAAG,YAAY;YAAE;QACtE,SAAU;YACR,WAAW;QACb;IACF;IAEA,oBAAoB;IACpB,yDAAyD;IAI3D,sCAAsC;IACtC,0EAA0E;IAC1E,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,YAAY;gBACf,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,gEAAgE;YAChE,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CACjC,4FACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;oBAChC,gBAAgB;gBAClB;gBACA,MAAM;oBAAE,aAAa;gBAAW,EAAE,kCAAkC;YACtE;YAGF,QAAQ,GAAG,CAAC,qCAAqC,SAAS,IAAI;YAE9D,8CAA8C;YAC9C,IACE,AAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IACtC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,cACzE,SAAS,MAAM,IAAI,OAAO,SAAS,MAAM,GAAG,IAAK,2BAA2B;cAC7E;gBACA,iCAAiC;gBACjC,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,WAAW,KAAK;gBACvE,yDAAyD;gBACzD,MAAM,mBAAmB,eAAe,MAAM,CAAC,CAAA,MAAO,IAAI,WAAW,KAAK;gBAC1E,kBAAkB;gBAElB,6CAA6C;gBAC7C,MAAM,SAAS,iBAAiB,MAAM,CAAC,CAAC,KAAmB;oBACzD,IAAI,eAAe,IAAI,QAAQ,gBAAgB,IAAI;oBACnD,IAAI,WAAW,IAAI,QAAQ,UAAU,IAAI;oBACzC,IAAI,UAAU,IAAI,QAAQ,WAAW,IAAI;oBACzC,OAAO;gBACT,GAAG;oBAAE,iBAAiB;oBAAG,aAAa;oBAAG,YAAY;gBAAE;gBAEvD,gBAAgB;gBAChB,SAAS,OAAO,4BAA4B;gBAC5C,kBAAkB;gBAElB,wCAAwC;gBACxC,WAAW;oBACT,kBAAkB;gBACpB,GAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;gBAC7D,SAAS;YACX;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;gBAChE,SAAS,CAAC,2BAA2B,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;YAClE,OAAO;gBACL,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,0EAA0E;IAC1E,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,WAAW;gBACd,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,gEAAgE;YAChE,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CACjC,2FACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;oBAChC,gBAAgB;gBAClB;gBACA,MAAM;oBAAE,YAAY;gBAAU,EAAE,kCAAkC;YACpE;YAGF,QAAQ,GAAG,CAAC,oCAAoC,SAAS,IAAI;YAE7D,8CAA8C;YAC9C,IACE,AAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IACtC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,cACzE,SAAS,MAAM,IAAI,OAAO,SAAS,MAAM,GAAG,IAAK,2BAA2B;cAC7E;gBACA,gCAAgC;gBAChC,MAAM,mBAAmB,eAAe,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,KAAK;gBACzE,kBAAkB;gBAElB,qBAAqB;gBACrB,MAAM,SAAS,iBAAiB,MAAM,CAAC,CAAC,KAAmB;oBACzD,IAAI,eAAe,IAAI,QAAQ,gBAAgB,IAAI;oBACnD,IAAI,WAAW,IAAI,QAAQ,UAAU,IAAI;oBACzC,IAAI,UAAU,IAAI,QAAQ,WAAW,IAAI;oBACzC,OAAO;gBACT,GAAG;oBAAE,iBAAiB;oBAAG,aAAa;oBAAG,YAAY;gBAAE;gBAEvD,gBAAgB;gBAChB,SAAS,OAAO,4BAA4B;gBAC5C,kBAAkB;gBAElB,wCAAwC;gBACxC,WAAW;oBACT,kBAAkB;gBACpB,GAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;gBAC7D,SAAS;YACX;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;gBAChE,SAAS,CAAC,0BAA0B,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;YACjE,OAAO;gBACL,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEE,kEAAkE;IAClE,MAAM,gBAAgB,OAAO,WAAmB;QAC9C,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,SAAS;gBACT,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,6BAA6B,WAAW,SAAS;YAE7D,uCAAuC;YACvC,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE;gBAC3B,QAAQ;gBACR,KAAK;gBACL,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;gBACA,MAAM;oBACJ,YAAY;oBACZ,MAAM,YAAY,IAAI;oBACtB,kBAAkB,YAAY,gBAAgB;oBAC9C,YAAY,YAAY,UAAU;oBAClC,OAAO,YAAY,KAAK;gBAC1B;YACF;YAEA,QAAQ,GAAG,CAAC,4BAA4B,SAAS,IAAI;YACrD,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;YAC/C,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI,EAAE;YAC1D,QAAQ,GAAG,CAAC,qBAAqB,SAAS,IAAI,EAAE;YAEhD,2FAA2F;YAC3F,IACE,AAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IACtC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,cACzE,SAAS,MAAM,IAAI,OAAO,SAAS,MAAM,GAAG,IAAK,2BAA2B;cAC7E;gBACA,8BAA8B;gBAC9B,MAAM,kBAAkB,eAAe,GAAG,CAAC,CAAA;oBACzC,IAAI,IAAI,UAAU,KAAK,WAAW;wBAChC,OAAO;4BAAE,GAAG,GAAG;4BAAE,GAAG,WAAW;wBAAC;oBAClC;oBACA,OAAO;gBACT;gBAEA,kBAAkB;gBAElB,qBAAqB;gBACrB,MAAM,SAAS,gBAAgB,MAAM,CAAC,CAAC,KAAmB;oBACxD,IAAI,eAAe,IAAI,QAAQ,gBAAgB,IAAI;oBACnD,IAAI,WAAW,IAAI,QAAQ,UAAU,IAAI;oBACzC,IAAI,UAAU,IAAI,QAAQ,WAAW,IAAI;oBACzC,OAAO;gBACT,GAAG;oBAAE,iBAAiB;oBAAG,aAAa;oBAAG,YAAY;gBAAE;gBAEvD,gBAAgB;gBAChB,kBAAkB;gBAClB,WAAW,IAAM,kBAAkB,OAAO;gBAC1C,OAAO;YACT,OAAO;gBACL,SAAS;gBACT,WAAW,IAAM,SAAS,OAAO;gBACjC,OAAO;YACT;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;YAEtC,2CAA2C;YAC3C,MAAM,kBAAkB,eAAe,GAAG,CAAC,CAAA;gBACzC,IAAI,IAAI,UAAU,KAAK,WAAW;oBAChC,OAAO;wBAAE,GAAG,GAAG;wBAAE,GAAG,WAAW;oBAAC;gBAClC;gBACA,OAAO;YACT;YAEA,kBAAkB;YAElB,qBAAqB;YACrB,MAAM,SAAS,gBAAgB,MAAM,CAAC,CAAC,KAAmB;gBACxD,IAAI,eAAe,IAAI,QAAQ,gBAAgB,IAAI;gBACnD,IAAI,WAAW,IAAI,QAAQ,UAAU,IAAI;gBACzC,IAAI,UAAU,IAAI,QAAQ,WAAW,IAAI;gBACzC,OAAO;YACT,GAAG;gBAAE,iBAAiB;gBAAG,aAAa;gBAAG,YAAY;YAAE;YAEvD,gBAAgB;YAChB,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF;IAIA,uCAAuC;IACvC,MAAM,wBAAwB,CAAC;QAC7B,OAAO,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,WAAW,KAAK;IAClE;IAEA,iCAAiC;IACjC,MAAM,mBAAmB,CAAC,YAAoB;QAC5C,OAAO,sBAAsB,YAAY,MAAM,CAAC,CAAC,OAAO,UAAY,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,GAAG;IACrG;IAEA,8CAA8C;IAC9C,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAEpG,2CAA2C;IAC3C,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;IACtC;IAEA,2CAA2C;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwC;IAE7F,6EAA6E;IAE7E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAW,CAAC,UAAU,EACpB,oBAAoB,WAChB,uCACA,uBACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAW,CAAC,UAAU,EACpB,oBAAoB,aAChB,uCACA,uBACJ;0CACH;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,QAAQ,GAAG,CAAC;;kDAE3B,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,OAAO,KAAK;;kDAE3B,6LAAC,2MAAA,CAAA,UAAO;wCAAC,MAAM;;;;;;kDACf,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAMX,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAKJ,gCACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAKJ,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;qCAGjB;0BACG,oBAAoB,yBACnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC1B,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAKV,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;wCAAE,WAAU;kDAAiC;;;;;+CAE9C,iBAAiB,GAAG,CAAC,CAAC,yBACpB,6LAAC;4CAEC,WAAW,CAAC,sEAAsE,EAAE,4BAA4B,gBAAgB,SAAS,WAAW,GAAG,gCAAgC,IAAI;;8DAE3L,6LAAC;oDACC,WAAU;oDACV,SAAS,IAAM,8BAA8B;8DAE7C,cAAA,6LAAC;wDAAK,WAAU;kEAAc,SAAS,IAAI;;;;;;;;;;;8DAE7C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA0B,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,SAAS,WAAW,EAAE;;;;;;sEAChG,6LAAC;4DACC,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,IAAI,OAAO,OAAO,CAAC,CAAC,8CAA8C,EAAE,SAAS,IAAI,CAAC,uDAAuD,CAAC,GAAG;oEAC3I,eAAe,SAAS,WAAW;gEACrC;4DACF;4DACA,WAAU;sEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,MAAM;;;;;;;;;;;;;;;;;;2CApBZ,SAAS,WAAW;;;;;;;;;;;;;;;;sCA8BnC,6LAAC;4BAAI,WAAU;sCACZ,6BACC,yBAAyB,iBACzB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;kEAElC,6LAAC;wDAAG,WAAU;kEAAoC,2BAA2B,IAAI;;;;;;;;;;;;0DAEnF,6LAAC;gDAAO,SAAS,IAAM,8BAA8B;gDAAO,WAAU;0DACpE,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAKb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA0B,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,2BAA2B,WAAW,EAAE;;;;;;;;;;;;0DAEpH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA0B,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,2BAA2B,WAAW,EAAE;;;;;;;;;;;;0DAIpH,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,iBAAiB,2BAA2B,WAAW,EAAE,sBAAsB,aAAa,eAAe,GAAI,KAAK,CAAC,CAAC;oDAAC;;;;;;;;;;;;;;;;;kDAM/J,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAI;;;;;;kEACL,6LAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,6LAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,6LAAC;wDAAI,WAAU;kEAAa;;;;;;;;;;;;4CAG7B,sBAAsB,2BAA2B,WAAW,EAAE,MAAM,KAAK,kBACxE,6LAAC;gDAAE,WAAU;0DAAiC;;;;;uDAE9C,sBAAsB,2BAA2B,WAAW,EAAE,GAAG,CAAC,CAAC,wBACjE,6LAAC;oDAA6B,WAAU;;sEACtC,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,IAAI;8EACb,6LAAC;oEACC,SAAS;wEACP,IAAI,OAAO,OAAO,CAAC,CAAC,6CAA6C,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG;4EACpF,cAAc,QAAQ,UAAU;wEAClC;oEACF;oEACA,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,MAAM;;;;;;;;;;;;;;;;;sEAGjB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,6KAAA,CAAA,UAAc;gEACb,cAAc,QAAQ,gBAAgB;gEACtC,QAAQ,OAAO;oEACb,IAAI,aAAa,QAAQ,gBAAgB,EAAE;wEACzC,MAAM,iBAAiB;4EACrB,GAAG,OAAO;4EACV,kBAAkB;4EAClB,MAAM,QAAQ,IAAI;4EAClB,YAAY,QAAQ,UAAU;wEAChC;wEACA,IAAI;4EACF,MAAM,cAAc,eAAe,UAAU,EAAE;4EAC/C,OAAO,MAAM,UAAU;wEACzB,EAAE,OAAO,OAAO;4EACd,QAAQ,KAAK,CAAC,oCAAoC;4EAClD,OAAO,OAAO,SAAS;wEACzB;oEACF;oEACA,OAAO,MAAM,mBAAmB;gEAClC;;;;;;;;;;;sEAGJ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,6KAAA,CAAA,UAAc;gEACb,cAAc,QAAQ,UAAU;gEAChC,QAAQ,OAAO;oEACb,IAAI,aAAa,QAAQ,UAAU,EAAE;wEACnC,MAAM,iBAAiB;4EACrB,GAAG,OAAO;4EACV,YAAY;4EACZ,MAAM,QAAQ,IAAI;4EAClB,kBAAkB,QAAQ,gBAAgB;wEAC5C;wEACA,IAAI;4EACF,MAAM,cAAc,eAAe,UAAU,EAAE;4EAC/C,OAAO,MAAM,UAAU;wEACzB,EAAE,OAAO,OAAO;4EACd,QAAQ,KAAK,CAAC,8BAA8B;4EAC5C,OAAO,OAAO,SAAS;wEACzB;oEACF;oEACA,OAAO,MAAM,mBAAmB;gEAClC;;;;;;;;;;;sEAGJ,6LAAC;4DACC,WAAU;4DACV,SAAS;gEACP,mBAAmB;gEACnB,oBAAoB;4DACtB;4DACA,OAAM;sEAEL,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,WAAW;;;;;;;mDArE7B,QAAQ,UAAU;;;;;0DA4EhC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;oDACA,WAAU;;sEAEV,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;sEACZ,6LAAC;sEAAK;;;;;;;;;;;;;;;;;0DAKV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,6LAAC;wDAAI,WAAU;kEAAyB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,2BAA2B,WAAW,EAAE;;;;;;kEAChH,6LAAC;wDAAI,WAAU;kEAAyB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,2BAA2B,WAAW,EAAE;;;;;;kEAChH,6LAAC;wDAAI,WAAU;kEAAyB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,2BAA2B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;uCAKtH,mBAAmB,iBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAE/D,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,OAAM;oEAA6B,OAAM;oEAAK,QAAO;oEAAK,SAAQ;oEAAY,MAAK;oEAAO,QAAO;oEAAe,aAAY;oEAAI,eAAc;oEAAQ,gBAAe;oEAAQ,WAAU;;sFAC1L,6LAAC;4EAAK,GAAE;;;;;;sFACR,6LAAC;4EAAK,GAAE;;;;;;;;;;;;;;;;;0EAGZ,6LAAC;gEAAG,WAAU;0EAA0D;;;;;;0EACxE,6LAAC;gEAAE,WAAU;0EAAsC,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,eAAe;;;;;;0EAC9F,6LAAC;gEACC,WAAU;gEACV,SAAS,IAAM,QAAQ,GAAG,CAAC;0EAC5B;;;;;;;;;;;;kEAMH,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,OAAM;oEAA6B,OAAM;oEAAK,QAAO;oEAAK,SAAQ;oEAAY,MAAK;oEAAO,QAAO;oEAAe,aAAY;oEAAI,eAAc;oEAAQ,gBAAe;oEAAQ,WAAU;;sFAC1L,6LAAC;4EAAK,GAAE;4EAAI,GAAE;4EAAI,OAAM;4EAAK,QAAO;4EAAK,IAAG;;;;;;sFAC5C,6LAAC;4EAAK,IAAG;4EAAI,IAAG;4EAAK,IAAG;4EAAK,IAAG;;;;;;;;;;;;;;;;;0EAGpC,6LAAC;gEAAG,WAAU;0EAA0D;;;;;;0EACxE,6LAAC;gEAAE,WAAU;0EAAsC,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,WAAW;;;;;;0EAC1F,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;;4EAAgB;0FAAM,6LAAC;gFAAK,WAAU;0FAAkB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,UAAU;;;;;;;;;;;;kFAC3G,6LAAC;wEAAE,WAAU;;4EAAqB;0FAAS,6LAAC;gFAAK,WAAU;0FAAiB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,WAAW,GAAG,aAAa,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOrJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;4CAErD,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;gDAAE,WAAU;0DAAiC;;;;;qEAE9C,6LAAC;;kEAEC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;gEAEZ,iCACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAuB,gBAAgB,IAAI;;;;;;sFACxD,6LAAC;4EAAE,WAAU;sFAAW,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,KAAK;;;;;;;;;;;;8EAIhE,6LAAC;oEAAI,SAAQ;oEAAc,WAAU;;sFAEnC,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;sFAC5F,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;sFAC5F,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;sFAC5F,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;sFAC5F,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;sFAC5F,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;sFAC5F,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;sFAC5F,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;sFAC5F,6LAAC;4EAAO,IAAG;4EAAM,IAAG;4EAAM,GAAE;4EAAK,MAAK;4EAAO,QAAO;4EAAU,aAAY;4EAAI,eAAc;;;;;;wEAG3F,CAAC;4EACA,4CAA4C;4EAC5C,MAAM,mBAAmB;mFAAI;6EAAiB,CAAC,IAAI,CAAC,CAAC,GAAG;gFACtD,MAAM,SAAS,iBAAiB,EAAE,WAAW,EAAE;gFAC/C,MAAM,SAAS,iBAAiB,EAAE,WAAW,EAAE;gFAC/C,OAAO,SAAS,QAAQ,mBAAmB;4EAC7C;4EAEA,MAAM,iBAAiB,iBAAiB,MAAM,GAAG,IAC7C,iBAAiB,gBAAgB,CAAC,EAAE,CAAC,WAAW,EAAE,sBAClD;4EAEJ,0DAA0D;4EAC1D,MAAM,gBAAgB,KAAK,IAAI,CAAC,iBAAiB,SAAS;4EAE1D,kEAAkE;4EAClE,OAAO,MAAM,IAAI,CAAC;gFAAE,QAAQ;4EAAE,GAAG,GAAG,CAAC,CAAC,GAAG;gFACvC,qCAAqC;gFACrC,MAAM,QAAQ,AAAC,IAAI,KAAM,KAAK,EAAE,GAAG;gFAEnC,uCAAuC;gFACvC,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,IAAI,IAAK,gBAAgB;gFAEnD,qBAAqB;gFACrB,MAAM,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,GAAC;gFAC9C,MAAM,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,GAAC;gFAE9C,0CAA0C;gFAC1C,IAAI,aAAa;gFACjB,IAAI,QAAQ,KAAK,EAAE,GAAC,GAAG,aAAa;gFACpC,IAAI,QAAQ,IAAE,KAAK,EAAE,GAAC,GAAG,aAAa;gFAEtC,qBACE,6LAAC;oFAEC,GAAG;oFACH,GAAG;oFACH,UAAS;oFACT,MAAK;oFACL,YAAY;oFACZ,kBAAiB;8FAEhB,QAAQ,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG;mFARtB;;;;;4EAWX;wEACF,CAAC;wEAEA,iBAAiB,MAAM,GAAG,mBACzB;;gFAEG,CAAC;oFACA,4EAA4E;oFAC5E,MAAM,mBAAmB;2FAAI;qFAAiB,CAAC,IAAI,CAAC,CAAC,GAAG;wFACtD,MAAM,SAAS,iBAAiB,EAAE,WAAW,EAAE;wFAC/C,MAAM,SAAS,iBAAiB,EAAE,WAAW,EAAE;wFAC/C,OAAO,SAAS,QAAQ,mBAAmB;oFAC7C;oFAEA,4CAA4C;oFAC5C,MAAM,iBAAiB,iBAAiB,MAAM,GAAG,IAC7C,iBAAiB,gBAAgB,CAAC,EAAE,CAAC,WAAW,EAAE,sBAClD;oFAEJ,0DAA0D;oFAC1D,MAAM,gBAAgB,KAAK,IAAI,CAAC,iBAAiB,SAAS;oFAE1D,OAAO,iBAAiB,GAAG,CAAC,CAAC,UAAU;wFACrC,MAAM,gBAAgB,iBAAiB,SAAS,WAAW,EAAE;wFAC7D,IAAI,kBAAkB,GAAG,OAAO,MAAM,6BAA6B;wFAEnE,4CAA4C;wFAC5C,MAAM,aAAa,gBAAgB;wFAEnC,gFAAgF;wFAChF,gEAAgE;wFAChE,MAAM,YAAY,aAAa;wFAE/B,wEAAwE;wFACxE,MAAM,aAAa,GAAG,iDAAiD;wFAEvE,6CAA6C;wFAC7C,MAAM,SAAS,KAAM,QAAQ;wFAC7B,IAAI,SAAS,IAAI,OAAO,MAAM,8BAA8B;wFAE5D,8DAA8D;wFAC9D,MAAM,WAAW,CAAC,aAAa,EAAE,IAAI,KAAK,EAAE,GAAG;wFAC/C,MAAM,SAAS,CAAC,aAAa,YAAY,EAAE,IAAI,KAAK,EAAE,GAAG;wFAEzD,uBAAuB;wFACvB,MAAM,KAAK,MAAM,SAAS,KAAK,GAAG,CAAC;wFACnC,MAAM,KAAK,MAAM,SAAS,KAAK,GAAG,CAAC;wFACnC,MAAM,KAAK,MAAM,SAAS,KAAK,GAAG,CAAC;wFACnC,MAAM,KAAK,MAAM,SAAS,KAAK,GAAG,CAAC;wFAEnC,sDAAsD;wFACtD,MAAM,eAAe,YAAY,MAAM,IAAI;wFAE3C,kCAAkC;wFAClC,MAAM,OAAO;4FACX,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI;4FACf,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,EAAE,aAAa,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI;yFACxD,CAAC,IAAI,CAAC;wFAEP,qBACE,6LAAC;4FAEC,GAAG;4FACH,MAAK;4FACL,QAAQ,iBAAiB;4FACzB,aAAY;4FACZ,eAAc;4FACd,gBAAe;4FACf,cAAc,IAAM,mBAAmB;oGAAC,MAAM,SAAS,IAAI;oGAAE,OAAO;gGAAa;4FACjF,cAAc,IAAM,mBAAmB;4FACvC,OAAO;gGAAE,QAAQ;4FAAU;2FATtB,SAAS,WAAW;;;;;oFAY/B;gFACF,CAAC;8FAED,6LAAC;oFAAO,IAAG;oFAAM,IAAG;oFAAM,GAAE;oFAAK,MAAK;oFAAQ,QAAO;oFAAU,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;kEAQrF,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;0EACZ,iBAAiB,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,sBAChF,6LAAC;wEAA+B,WAAU;;0FACxC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFACC,WAAU;wFACV,OAAO;4FAAE,iBAAiB,iBAAiB;wFAAO;;;;;;kGAEpD,6LAAC;wFAAK,WAAU;wFAAsB,OAAO;4FAAE,OAAO,iBAAiB;wFAAO;kGAAI,SAAS,IAAI;;;;;;;;;;;;0FAEjG,6LAAC;gFAAK,WAAU;gFAAsB,OAAO;oFAAE,OAAO,iBAAiB;gFAAO;;oFAAG;oFAC5E,iBAAiB,SAAS,WAAW,EAAE,oBAAoB,cAAc;;;;;;;;uEATtE,SAAS,WAAW;;;;;;;;;;0EAgBlC,6LAAC;gEAAI,WAAU;0EACZ,iBAAiB,KAAK,CAAC,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,sBAC7E,6LAAC;wEAA+B,WAAU;;0FACxC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFACC,WAAU;wFACV,OAAO;4FAAE,iBAAiB,iBAAiB,QAAQ,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;wFAAI;;;;;;kGAE7F,6LAAC;wFAAK,WAAU;wFAAsB,OAAO;4FAAE,OAAO,iBAAiB,QAAQ,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;wFAAI;kGACpH,SAAS,IAAI;;;;;;;;;;;;0FAGlB,6LAAC;gFAAK,WAAU;gFAAsB,OAAO;oFAAE,OAAO,iBAAiB,QAAQ,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;gFAAI;;oFAAG;oFACrH,iBAAiB,SAAS,WAAW,EAAE,oBAAoB,cAAc;;;;;;;;uEAXtE,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAyBlD,6LAAC,2KAAA,CAAA,UAAY;oBACX,UAAU;oBACV,UAAU;;;;;;;YAOjB,mCACC,6LAAC,4KAAA,CAAA,UAAa;gBACZ,SAAS,IAAM,qBAAqB;gBACpC,QAAQ,OAAO;oBACb,IAAI;wBACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;wBAEzB,IAAI,CAAC,OAAO;4BACV,SAAS;4BACT;wBACF;wBAEA,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE;4BAC3B,QAAQ;4BACR,KAAK;4BACL,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gCAClC,gBAAgB;4BAClB;4BACA,MAAM;gCAAE;4BAAK;wBACf;wBAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;4BAC3C,oBAAoB;mCAAI;gCAAkB,SAAS,IAAI,CAAC,QAAQ;6BAAC;4BACjE,kBAAkB;4BAClB,WAAW,IAAM,kBAAkB,OAAO;4BAC1C,qBAAqB;wBACvB,OAAO;4BACL,SAAS;wBACX;oBACF,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,0BAA0B;wBACxC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;oBACxC;gBACF;;;;;;YAIH,kCACC,6LAAC,2KAAA,CAAA,UAAY;gBACX,YAAY;gBACZ,kBAAkB;gBAClB,SAAS;oBACP,oBAAoB;oBACpB,oBAAoB;gBACtB;gBACA,QAAQ,OAAO;oBACb,IAAI;wBACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;wBAEzB,IAAI,CAAC,OAAO;4BACV,SAAS;4BACT;wBACF;wBAEA,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE;4BAC3B,QAAQ;4BACR,KAAK;4BACL,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gCAClC,gBAAgB;4BAClB;4BACA,MAAM;wBACR;wBAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;4BAC1C,kBAAkB;mCAAI;gCAAgB,SAAS,IAAI,CAAC,OAAO;6BAAC;4BAC5D,qBAAqB;4BACrB,MAAM,kBAAkB;mCAAI;gCAAgB,SAAS,IAAI,CAAC,OAAO;6BAAC;4BAClE,MAAM,SAAS,gBAAgB,MAAM,CAAC,CAAC,KAAmB;gCACxD,IAAI,eAAe,IAAI,QAAQ,gBAAgB,IAAI;gCACnD,IAAI,WAAW,IAAI,QAAQ,UAAU,IAAI;gCACzC,IAAI,UAAU,IAAI,QAAQ,WAAW,IAAI;gCACzC,OAAO;4BACT,GAAG;gCAAE,iBAAiB;gCAAG,aAAa;gCAAG,YAAY;4BAAE;4BAEvD,gBAAgB;4BAChB,kBAAkB;4BAClB,WAAW,IAAM,kBAAkB,OAAO;4BAC1C,oBAAoB;4BACpB,oBAAoB;wBACtB,OAAO;4BACL,SAAS;wBACX;oBACF,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;oBACxC;gBACF;;;;;;YAIH,kCACC,6LAAC,2KAAA,CAAA,UAAY;gBACX,UAAU;gBACV,mBAAmB,iBAAiB;gBACpC,SAAS,IAAM,oBAAoB;gBACnC,QAAQ,OAAO;oBACb,IAAI;wBACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;wBAEzB,IAAI,CAAC,OAAO;4BACV,SAAS;4BACT;wBACF;wBAEA,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE;4BAC3B,QAAQ;4BACR,KAAK;4BACL,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gCAClC,gBAAgB;4BAClB;4BACA,MAAM;wBACR;wBAEA,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;4BAC1C,kBAAkB;mCAAI;gCAAgB,SAAS,IAAI,CAAC,OAAO;6BAAC;4BAC5D,mCAAmC;4BACnC,MAAM,kBAAkB,eAAe,GAAG,CAAC,CAAA;gCACzC,IAAI,QAAQ,UAAU,KAAK,YAAY,UAAU,EAAE;oCACjD,OAAO;wCACL,GAAG,OAAO;wCACV,aAAa,CAAC,QAAQ,WAAW,IAAI,CAAC,IAAI,YAAY,MAAM;oCAC9D;gCACF;gCACA,OAAO;4BACT;4BAEA,kBAAkB;4BAElB,qBAAqB;4BACrB,MAAM,SAAS,gBAAgB,MAAM,CAAC,CAAC,KAAmB;gCACxD,IAAI,eAAe,IAAI,QAAQ,gBAAgB,IAAI;gCACnD,IAAI,WAAW,IAAI,QAAQ,UAAU,IAAI;gCACzC,IAAI,UAAU,IAAI,QAAQ,WAAW,IAAI;gCACzC,OAAO;4BACT,GAAG;gCAAE,iBAAiB;gCAAG,aAAa;gCAAG,YAAY;4BAAE;4BAEvD,gBAAgB;4BAChB,kBAAkB;4BAClB,WAAW,IAAM,kBAAkB,OAAO;4BAC1C,oBAAoB;wBACtB,OAAO;4BACL,SAAS;wBACX;oBACF,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;oBACxC;gBACF;;;;;;YAIH,sBAAsB,iCACrB,6LAAC,6KAAA,CAAA,UAAc;gBACb,SAAS;gBACT,UAAU,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,gBAAgB,UAAU;gBAChF,SAAS;oBACP,sBAAsB;oBACtB,mBAAmB;gBACrB;gBACA,cAAc;oBACZ,2DAA2D;oBAC3D,sBAAsB;oBACtB,oBAAoB;gBACtB;gBACA,iBAAiB;;;;;;;;;;;;AAK3B;GAvkCM;KAAA;uCAykCS", "debugId": null}}, {"offset": {"line": 5532, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5538, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/guestlist/AddGuestModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect } from 'react';\nimport { X } from 'lucide-react';\nimport axios from 'axios';\nimport { getAuthToken } from '../../utils';\nimport { Guest, Group, MenuOption } from './GuestList';\nimport Image from \"next/image\";\n\ninterface AddGuestModalProps {\n  onClose: () => void;\n  onSave: () => void;\n  guest: Guest | null;\n  groups: Group[];\n  menuOptions: MenuOption[];\n  defaultGroupId?: string;\n  setError: (error: string | null) => void;\n  setSuccessMessage: (message: string | null) => void;\n}\n\nconst AddGuestModal: React.FC<AddGuestModalProps> = ({\n  onClose,\n  onSave,\n  guest,\n  groups,\n  menuOptions,\n  defaultGroupId,\n  setError,\n  setSuccessMessage\n}) => {\n\n  const [firstName, setFirstName] = useState('');\n  const [lastName, setLastName] = useState('');\n  const [email, setEmail] = useState('');\n  const [phone, setPhone] = useState('');\n  const [groupId, setGroupId] = useState('');\n  const [menuId, setMenuId] = useState('');\n  const [attendanceStatus, setAttendanceStatus] = useState<'attending' | 'pending' | 'declined'>('pending');\n  const [notes, setNotes] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  // Initialize form with guest data if editing\n  useEffect(() => {\n    if (guest) {\n      setFirstName(guest.first_name || '');\n      setLastName(guest.last_name || '');\n      setEmail(guest.email || '');\n      setPhone(guest.phone || '');\n      setGroupId(guest.group_id || '');\n      setMenuId(guest.menu_id || '');\n      setAttendanceStatus(guest.attendance_status || 'pending');\n      setNotes(guest.notes || '');\n    } else if (defaultGroupId) {\n      setGroupId(defaultGroupId);\n    }\n  }, [guest, defaultGroupId]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!firstName || !lastName || !groupId) {\n      setError('First name, last name, and group are required');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        setLoading(false);\n        return;\n      }\n\n      const guestData = {\n        first_name: firstName,\n        last_name: lastName,\n        email,\n        phone,\n        group_id: groupId,\n        menu_id: menuId || null,\n        attendance_status: attendanceStatus,\n        notes\n      };\n\n      if (guest) {\n        // Update existing guest\n        const response = await axios.put(\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',\n          {\n            guest_id: guest.guest_id,\n            ...guestData\n          },\n          { headers: { Authorization: `Bearer ${token}` } }\n        );\n\n        console.log('Guest updated successfully:', response.data);\n        setSuccessMessage('Guest updated successfully');\n      } else {\n        // Add new guest\n        const response = await axios.post(\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-guest',\n          guestData,\n          { headers: { Authorization: `Bearer ${token}` } }\n        );\n\n        console.log('Guest added successfully:', response.data);\n        setSuccessMessage('Guest added successfully');\n      }\n\n      // Directly fetch the guest list from the API to verify database state\n      try {\n        console.log('Directly fetching guest list from API to verify database state...');\n        const directGuestListResponse = await axios.get(\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-list',\n          {\n            headers: { Authorization: `Bearer ${token}` },\n            params: { _t: new Date().getTime() } // Cache busting\n          }\n        );\n        console.log('DIRECT API CALL - All guests in database:', directGuestListResponse.data.guests);\n\n        // Also fetch groups to verify\n        const directGroupsResponse = await axios.get(\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-groups',\n          {\n            headers: { Authorization: `Bearer ${token}` },\n            params: { _t: new Date().getTime() } // Cache busting\n          }\n        );\n        console.log('DIRECT API CALL - All groups in database:', directGroupsResponse.data.groups);\n\n        // Now call the regular onSave function\n        await onSave();\n\n        // Close the modal immediately\n        onClose();\n      } catch (error) {\n        console.error('Error during direct API verification:', error);\n        // Still try the regular onSave as fallback\n        try {\n          await onSave();\n        } catch (saveError) {\n          console.error('Error in fallback onSave:', saveError);\n        }\n        onClose();\n      }\n\n    } catch (err: any) {\n      console.error('Error saving guest:', err);\n      setError(err.response?.data?.error || 'Failed to save guest');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\n      <div\n        className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden max-h-[90vh] overflow-y-auto\"\n        style={{\n          background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\n          boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\n        }}\n      >\n        <button\n          onClick={onClose}\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\"\n        >\n          <X size={20} />\n        </button>\n\n        {/* Logo */}\n        <div className=\"flex justify-center pt-6\">\n          <div className=\"text-red-600\">\n            <Image\n              src=\"/pics/logo.png\"\n              alt=\"Wedzat logo\"\n              width={40}\n              height={40}\n              className=\"object-cover\"\n            />\n          </div>\n        </div>\n\n        <div className=\"px-6 py-4\">\n          <h3 className=\"text-2xl font-bold mb-2 text-center\" style={{ color: \"#B31B1E\" }}>\n            {guest ? 'Edit Guest' : 'Add Guest'}\n          </h3>\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            {/* Guest Name */}\n            <div className=\"flex space-x-2\">\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  First Name *\n                </label>\n                <input\n                  type=\"text\"\n                  value={firstName}\n                  onChange={(e) => setFirstName(e.target.value)}\n                  className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n                  required\n                />\n              </div>\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Last Name *\n                </label>\n                <input\n                  type=\"text\"\n                  value={lastName}\n                  onChange={(e) => setLastName(e.target.value)}\n                  className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Contact Information */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Email is required to send invitations\n              </p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Phone\n              </label>\n              <input\n                type=\"tel\"\n                value={phone}\n                onChange={(e) => setPhone(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n              />\n            </div>\n\n            {/* Group */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Group *\n              </label>\n              <select\n                value={groupId}\n                onChange={(e) => setGroupId(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n                required\n              >\n                <option value=\"\">Select a group</option>\n                {groups.map(group => (\n                  <option key={group.group_id} value={group.group_id}>\n                    {group.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Menu */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Menu\n              </label>\n              <select\n                value={menuId}\n                onChange={(e) => setMenuId(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n              >\n                <option value=\"\">Select a menu</option>\n                {menuOptions.map(menu => (\n                  <option key={menu.menu_id} value={menu.menu_id}>\n                    {menu.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Attendance Status */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Attendance Status\n              </label>\n              <select\n                value={attendanceStatus}\n                onChange={(e) => setAttendanceStatus(e.target.value as 'attending' | 'pending' | 'declined')}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n              >\n                <option value=\"attending\">Attending</option>\n                <option value=\"pending\">Pending</option>\n                <option value=\"declined\">Declined</option>\n              </select>\n            </div>\n\n            {/* Notes */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Notes\n              </label>\n              <textarea\n                value={notes}\n                onChange={(e) => setNotes(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n                rows={3}\n              />\n            </div>\n          </div>\n\n          <div className=\"mt-6 flex justify-end space-x-2\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50\"\n              disabled={loading}\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"px-4 py-2 bg-[#B31B1E] text-white rounded hover:bg-red-700 disabled:bg-red-300\"\n              disabled={loading}\n            >\n              {loading ? 'Saving...' : (guest ? 'Update' : 'Add')}\n            </button>\n          </div>\n        </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddGuestModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;;;AANA;;;;;;AAmBA,MAAM,gBAA8C,CAAC,EACnD,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,WAAW,EACX,cAAc,EACd,QAAQ,EACR,iBAAiB,EAClB;;IAEC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwC;IAC/F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,OAAO;gBACT,aAAa,MAAM,UAAU,IAAI;gBACjC,YAAY,MAAM,SAAS,IAAI;gBAC/B,SAAS,MAAM,KAAK,IAAI;gBACxB,SAAS,MAAM,KAAK,IAAI;gBACxB,WAAW,MAAM,QAAQ,IAAI;gBAC7B,UAAU,MAAM,OAAO,IAAI;gBAC3B,oBAAoB,MAAM,iBAAiB,IAAI;gBAC/C,SAAS,MAAM,KAAK,IAAI;YAC1B,OAAO,IAAI,gBAAgB;gBACzB,WAAW;YACb;QACF;kCAAG;QAAC;QAAO;KAAe;IAE1B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS;YACvC,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,MAAM,YAAY;gBAChB,YAAY;gBACZ,WAAW;gBACX;gBACA;gBACA,UAAU;gBACV,SAAS,UAAU;gBACnB,mBAAmB;gBACnB;YACF;YAEA,IAAI,OAAO;gBACT,wBAAwB;gBACxB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,kFACA;oBACE,UAAU,MAAM,QAAQ;oBACxB,GAAG,SAAS;gBACd,GACA;oBAAE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAAE;gBAGlD,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI;gBACxD,kBAAkB;YACpB,OAAO;gBACL,gBAAgB;gBAChB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,+EACA,WACA;oBAAE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAAE;gBAGlD,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;gBACtD,kBAAkB;YACpB;YAEA,sEAAsE;YACtE,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,0BAA0B,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC7C,gFACA;oBACE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;oBAC5C,QAAQ;wBAAE,IAAI,IAAI,OAAO,OAAO;oBAAG,EAAE,gBAAgB;gBACvD;gBAEF,QAAQ,GAAG,CAAC,6CAA6C,wBAAwB,IAAI,CAAC,MAAM;gBAE5F,8BAA8B;gBAC9B,MAAM,uBAAuB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC1C,kFACA;oBACE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;oBAC5C,QAAQ;wBAAE,IAAI,IAAI,OAAO,OAAO;oBAAG,EAAE,gBAAgB;gBACvD;gBAEF,QAAQ,GAAG,CAAC,6CAA6C,qBAAqB,IAAI,CAAC,MAAM;gBAEzF,uCAAuC;gBACvC,MAAM;gBAEN,8BAA8B;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,2CAA2C;gBAC3C,IAAI;oBACF,MAAM;gBACR,EAAE,OAAO,WAAW;oBAClB,QAAQ,KAAK,CAAC,6BAA6B;gBAC7C;gBACA;YACF;QAEF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;;8BAEA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;4BAAsC,OAAO;gCAAE,OAAO;4BAAU;sCAC3E,QAAQ,eAAe;;;;;;sCAG5B,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAGZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAMd,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;;;;;;;;;;;;sDAKd,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC1C,WAAU;oDACV,QAAQ;;sEAER,6LAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,OAAO,GAAG,CAAC,CAAA,sBACV,6LAAC;gEAA4B,OAAO,MAAM,QAAQ;0EAC/C,MAAM,IAAI;+DADA,MAAM,QAAQ;;;;;;;;;;;;;;;;;sDAQjC,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oDACzC,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;gEAA0B,OAAO,KAAK,OAAO;0EAC3C,KAAK,IAAI;+DADC,KAAK,OAAO;;;;;;;;;;;;;;;;;sDAQ/B,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oDACnD,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAW;;;;;;;;;;;;;;;;;;sDAK7B,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,MAAM;;;;;;;;;;;;;;;;;;8CAKZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,UAAU,cAAe,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GAnUM;KAAA;uCAqUS", "debugId": null}}, {"offset": {"line": 6103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/guestlist/AddGroupModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect } from 'react';\nimport { X } from 'lucide-react';\nimport axios from 'axios';\nimport { getAuthToken } from '../../utils';\nimport { Group } from './GuestList';\nimport Image from \"next/image\";\n\ninterface AddGroupModalProps {\n  onClose: () => void;\n  onSave: () => void;\n  group: Group | null;\n  setError: (error: string | null) => void;\n  setSuccessMessage: (message: string | null) => void;\n}\n\nconst AddGroupModal: React.FC<AddGroupModalProps> = ({\n  onClose,\n  onSave,\n  group,\n  setError,\n  setSuccessMessage\n}) => {\n\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  // Initialize form with group data if editing\n  useEffect(() => {\n    if (group) {\n      setName(group.name || '');\n      setDescription(group.description || '');\n    }\n  }, [group]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!name) {\n      setError('Group name is required');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        setLoading(false);\n        return;\n      }\n\n      if (group) {\n        // Update existing group\n        await axios.put(\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest-group',\n          {\n            group_id: group.group_id,\n            name,\n            description\n          },\n          { headers: { Authorization: `Bearer ${token}` } }\n        );\n\n        setSuccessMessage('Group updated successfully');\n      } else {\n        // Add new group\n        await axios.post(\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-guest-group',\n          {\n            name,\n            description\n          },\n          { headers: { Authorization: `Bearer ${token}` } }\n        );\n\n        setSuccessMessage('Group added successfully');\n      }\n\n      onSave();\n      onClose();\n\n    } catch (err: any) {\n      console.error('Error saving group:', err);\n      setError(err.response?.data?.error || 'Failed to save group');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\n      <div\n        className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\n        style={{\n          background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\n          boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\n        }}\n      >\n        <button\n          onClick={onClose}\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\"\n        >\n          <X size={20} />\n        </button>\n\n        {/* Logo */}\n        <div className=\"flex justify-center pt-6\">\n          <div className=\"text-red-600\">\n            <Image\n              src=\"/pics/logo.png\"\n              alt=\"Wedzat logo\"\n              width={40}\n              height={40}\n              className=\"object-cover\"\n            />\n          </div>\n        </div>\n\n        <div className=\"px-6 py-4\">\n          <h3 className=\"text-2xl font-bold mb-2 text-center\" style={{ color: \"#B31B1E\" }}>\n            {group ? 'Edit Group' : 'Add Group'}\n          </h3>\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            {/* Group Name */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Group Name *\n              </label>\n              <input\n                type=\"text\"\n                value={name}\n                onChange={(e) => setName(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n                required\n              />\n            </div>\n\n            {/* Description */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Description\n              </label>\n              <textarea\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n                rows={3}\n              />\n            </div>\n          </div>\n\n          <div className=\"mt-6 flex justify-end space-x-2\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50\"\n              disabled={loading}\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"px-4 py-2 bg-[#B31B1E] text-white rounded hover:bg-red-700 disabled:bg-red-300\"\n              disabled={loading}\n            >\n              {loading ? 'Saving...' : (group ? 'Update' : 'Add')}\n            </button>\n          </div>\n        </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddGroupModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;;;AANA;;;;;;AAgBA,MAAM,gBAA8C,CAAC,EACnD,OAAO,EACP,MAAM,EACN,KAAK,EACL,QAAQ,EACR,iBAAiB,EAClB;;IAEC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,OAAO;gBACT,QAAQ,MAAM,IAAI,IAAI;gBACtB,eAAe,MAAM,WAAW,IAAI;YACtC;QACF;kCAAG;QAAC;KAAM;IAEV,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM;YACT,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,IAAI,OAAO;gBACT,wBAAwB;gBACxB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACb,wFACA;oBACE,UAAU,MAAM,QAAQ;oBACxB;oBACA;gBACF,GACA;oBAAE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAAE;gBAGlD,kBAAkB;YACpB,OAAO;gBACL,gBAAgB;gBAChB,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CACd,wFACA;oBACE;oBACA;gBACF,GACA;oBAAE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAAE;gBAGlD,kBAAkB;YACpB;YAEA;YACA;QAEF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;;8BAEA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;4BAAsC,OAAO;gCAAE,OAAO;4BAAU;sCAC3E,QAAQ,eAAe;;;;;;sCAG5B,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACvC,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAKZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,MAAM;;;;;;;;;;;;;;;;;;8CAKZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,UAAU,cAAe,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GArKM;KAAA;uCAuKS", "debugId": null}}, {"offset": {"line": 6381, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/guestlist/GroupsView.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport { MoreHorizontal, Check, Clock, X, Mail, Edit, Trash2, Plus } from 'lucide-react';\nimport axios from 'axios';\nimport { getAuthToken } from '../../utils';\nimport { Guest, Group, MenuOption } from './GuestList';\nimport AddGuestModal from './AddGuestModal';\nimport AddGroupModal from './AddGroupModal';\n\ninterface GroupsViewProps {\n  groups: Group[];\n  guests: Guest[];\n  menuOptions: MenuOption[];\n  setGroups: React.Dispatch<React.SetStateAction<Group[]>>;\n  setGuests: React.Dispatch<React.SetStateAction<Guest[]>>;\n  fetchData: () => Promise<void>;\n  setError: (error: string | null) => void;\n  setSuccessMessage: (message: string | null) => void;\n  showAddGuestModal: boolean;\n  setShowAddGuestModal: React.Dispatch<React.SetStateAction<boolean>>;\n  showAddGroupModal: boolean;\n  setShowAddGroupModal: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nconst GroupsView: React.FC<GroupsViewProps> = ({\n  groups,\n  guests,\n  menuOptions,\n  setGroups,\n  setGuests,\n  fetchData,\n  setError,\n  setSuccessMessage,\n  showAddGuestModal,\n  setShowAddGuestModal,\n  showAddGroupModal,\n  setShowAddGroupModal\n}) => {\n\n  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);\n  const [selectedGuest, setSelectedGuest] = useState<Guest | null>(null);\n  const [showDeleteGroupConfirm, setShowDeleteGroupConfirm] = useState(false);\n  const [showDeleteGuestConfirm, setShowDeleteGuestConfirm] = useState(false);\n  const [editingGroup, setEditingGroup] = useState<Group | null>(null);\n\n  // Group guests by group_id\n  const guestsByGroup = groups.map(group => {\n    // Use string comparison to ensure matching works correctly\n    const groupGuests = guests.filter(guest => String(guest.group_id) === String(group.group_id));\n\n    return {\n      ...group,\n      guests: groupGuests,\n      guest_count: groupGuests.length\n    };\n  });\n\n  // Delete a group\n  const handleDeleteGroup = async (groupId: string) => {\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      await axios.delete('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-guest-group', {\n        headers: { Authorization: `Bearer ${token}` },\n        data: { group_id: groupId }\n      });\n\n      setGroups(prevGroups => prevGroups.filter(g => g.group_id !== groupId));\n      setSuccessMessage('Group deleted successfully');\n      setShowDeleteGroupConfirm(false);\n\n    } catch (err: any) {\n      console.error('Error deleting group:', err);\n      setError(err.response?.data?.error || 'Failed to delete group');\n    }\n  };\n\n  // Delete a guest\n  const handleDeleteGuest = async (guestId: string) => {\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      await axios.delete('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-guest', {\n        headers: { Authorization: `Bearer ${token}` },\n        data: { guest_id: guestId }\n      });\n\n      setGuests(prevGuests => prevGuests.filter(g => g.guest_id !== guestId));\n      setSuccessMessage('Guest deleted successfully');\n      setShowDeleteGuestConfirm(false);\n\n    } catch (err: any) {\n      console.error('Error deleting guest:', err);\n      setError(err.response?.data?.error || 'Failed to delete guest');\n    }\n  };\n\n  // Update guest attendance status\n  const updateGuestAttendance = async (guest: Guest, newStatus: 'attending' | 'pending' | 'declined') => {\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      await axios.put(\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',\n        {\n          guest_id: guest.guest_id,\n          attendance_status: newStatus\n        },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      setGuests(prevGuests =>\n        prevGuests.map(g =>\n          g.guest_id === guest.guest_id\n            ? { ...g, attendance_status: newStatus }\n            : g\n        )\n      );\n\n    } catch (err: any) {\n      console.error('Error updating guest attendance:', err);\n      setError(err.response?.data?.error || 'Failed to update guest attendance');\n    }\n  };\n\n  // Update guest menu option\n  const updateGuestMenu = async (guest: Guest, menuId: string) => {\n    try {\n      const token = getAuthToken();\n      const selectedMenu = menuOptions.find(m => m.menu_id === menuId);\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      if (!selectedMenu) {\n        setError('Menu option not found');\n        return;\n      }\n\n      await axios.put(\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',\n        {\n          guest_id: guest.guest_id,\n          menu_id: menuId\n        },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      setGuests(prevGuests =>\n        prevGuests.map(g =>\n          g.guest_id === guest.guest_id\n            ? { ...g, menu_id: menuId, menu_name: selectedMenu.name }\n            : g\n        )\n      );\n\n    } catch (err: any) {\n      console.error('Error updating guest menu:', err);\n      setError(err.response?.data?.error || 'Failed to update guest menu');\n    }\n  };\n\n  // Get couple name for invitation - try to get from website first, then user info\n  const getCoupleName = async (): Promise<string> => {\n    try {\n      // First try to get from website\n      const token = getAuthToken();\n      if (token) {\n        try {\n          const websitesResponse = await axios.get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {\n            headers: { Authorization: `Bearer ${token}` }\n          });\n\n          const websites = websitesResponse.data.websites || [];\n          if (websites.length > 0) {\n            // Check for couple_names in both places\n            const coupleNames = websites[0].couple_names ||\n                              (websites[0].design_settings && websites[0].design_settings.couple_names);\n            if (coupleNames) {\n              console.log('Using couple names from website:', coupleNames);\n              return coupleNames;\n            }\n          }\n        } catch (websiteError) {\n          console.warn('Could not get website info:', websiteError);\n          // Continue to user info fallback\n        }\n      }\n\n      // Fallback to user info\n      const userResponse = await axios.get('/api/user-info');\n      const user = userResponse.data.user || {};\n      const coupleName = `${user.first_name || 'The'} & ${user.last_name || 'Partner'}`;\n      console.log('Using couple name from user info:', coupleName);\n      return coupleName;\n    } catch (error) {\n      console.warn('Could not get user info, using default couple name');\n      return \"The Couple\";\n    }\n  };\n\n  // Get website information for invitation\n  const getWebsiteInfo = async (token: string): Promise<{ websiteId: string, websiteUrl: string } | null> => {\n    try {\n      const websitesResponse = await axios.get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      const websites = websitesResponse.data.websites || [];\n\n      if (websites.length === 0) {\n        setError('You need to create a wedding website first to send invitations');\n        return null;\n      }\n\n      return {\n        websiteId: websites[0].website_id,\n        websiteUrl: websites[0].deployed_url\n      };\n    } catch (error) {\n      console.error('Error getting website info:', error);\n      setError('Failed to get website information');\n      return null;\n    }\n  };\n\n  // Mark invitation as sent in the backend\n  const markInvitationSent = async (token: string, guestId: string, websiteId: string): Promise<any> => {\n    try {\n      return await axios.post(\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/send-invitation',\n        {\n          guest_ids: [guestId],\n          website_id: websiteId\n        },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n    } catch (error) {\n      console.error('Error marking invitation as sent:', error);\n      throw error;\n    }\n  };\n\n  // Send the actual email using our frontend API\n  const sendInvitationEmail = async (\n    email: string,\n    guestName: string,\n    coupleName: string,\n    websiteUrl: string,\n    responseUrl: string,\n    guestId: string\n  ): Promise<void> => {\n    await axios.post('/api/send-wedding-invitation', {\n      email,\n      guestName,\n      coupleName,\n      websiteUrl,\n      responseUrl,\n      guestId\n    });\n  };\n\n  // Send invitation to a guest\n  const sendInvitation = async (guest: Guest) => {\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      // Get website information\n      const websiteInfo = await getWebsiteInfo(token);\n      if (!websiteInfo) return;\n\n      // Mark invitation as sent in the backend\n      const backendResponse = await markInvitationSent(token, guest.guest_id, websiteInfo.websiteId);\n\n      // Get couple name\n      const coupleName = await getCoupleName();\n\n      // Create response URL\n      const responseToken = backendResponse.data.response_token || `${guest.guest_id}-${websiteInfo.websiteId}`;\n      const responseUrl = `${window.location.origin}/wedding-invitation-response?token=${responseToken}`;\n\n      // Send the actual email\n      await sendInvitationEmail(\n        guest.email,\n        `${guest.first_name} ${guest.last_name}`,\n        coupleName,\n        websiteInfo.websiteUrl,\n        responseUrl,\n        guest.guest_id\n      );\n\n      setSuccessMessage(`Invitation sent to ${guest.first_name} ${guest.last_name}`);\n      fetchData(); // Refresh to update invitation status\n\n    } catch (err: any) {\n      console.error('Error sending invitation:', err);\n      if (err.response?.data?.error?.includes('Email credentials')) {\n        setError('Email sending failed. Please configure email credentials in the server environment variables.');\n      } else {\n        setError(err.response?.data?.error || err.response?.data?.message || 'Failed to send invitation');\n      }\n    }\n  };\n\n  // Render attendance status icon\n  const renderAttendanceStatus = (status: string) => {\n    switch (status) {\n      case 'attending':\n        return <Check size={16} className=\"text-green-500\" />;\n      case 'pending':\n        return <Clock size={16} className=\"text-amber-500\" />;\n      case 'declined':\n        return <X size={16} className=\"text-red-500\" />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div>\n      {/* Groups and their guests */}\n      {guestsByGroup.map(group => (\n        <div key={group.group_id} className=\"mb-6\">\n          {/* Group header */}\n          <div className=\"flex justify-between items-center mb-2 border-b pb-2\">\n            <div className=\"flex items-center\">\n              <h3 className=\"font-semibold text-lg text-black\">{group.name}</h3>\n              <span className=\"ml-2 text-gray-500 text-sm\">{group.guest_count}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => {\n                  setSelectedGroup(group);\n                  setShowAddGuestModal(true);\n                }}\n                className=\"text-[#B31B1E] mr-2 p-1 hover:bg-red-50 rounded\"\n              >\n                <Plus size={16} />\n              </button>\n              <button\n                onClick={() => {\n                  setEditingGroup(group);\n                  setShowAddGroupModal(true);\n                }}\n                className=\"text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded\"\n              >\n                <Edit size={16} />\n              </button>\n              <button\n                onClick={() => {\n                  setSelectedGroup(group);\n                  setShowDeleteGroupConfirm(true);\n                }}\n                className=\"text-gray-500 p-1 hover:bg-gray-100 rounded\"\n              >\n                <Trash2 size={16} />\n              </button>\n            </div>\n          </div>\n\n          {/* Guest list for this group */}\n          {group.guests.length > 0 ? (\n            <div className=\"space-y-2\">\n              {group.guests.map(guest => (\n                <div key={guest.guest_id} className=\"flex items-center justify-between p-2 hover:bg-gray-50 rounded\">\n                  <div className=\"flex-1 text-black\">\n                    {guest.first_name} {guest.last_name}\n                  </div>\n\n                  {/* Attendance dropdown */}\n                  <div className=\"flex items-center mx-2\">\n                    <div className=\"relative group\">\n                      <button className=\"flex items-center space-x-1 p-1 rounded hover:bg-gray-100\">\n                        {renderAttendanceStatus(guest.attendance_status)}\n                        <span className=\"text-sm text-gray-700 capitalize\">{guest.attendance_status}</span>\n                      </button>\n                      <div className=\"absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-10 hidden group-hover:block\">\n                        <button\n                          onClick={() => updateGuestAttendance(guest, 'attending')}\n                          className=\"flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100\"\n                        >\n                          <Check size={16} className=\"text-green-500 mr-2\" />\n                          <span>Attending</span>\n                        </button>\n                        <button\n                          onClick={() => updateGuestAttendance(guest, 'pending')}\n                          className=\"flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100\"\n                        >\n                          <Clock size={16} className=\"text-amber-500 mr-2\" />\n                          <span>Pending</span>\n                        </button>\n                        <button\n                          onClick={() => updateGuestAttendance(guest, 'declined')}\n                          className=\"flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100\"\n                        >\n                          <X size={16} className=\"text-red-500 mr-2\" />\n                          <span>Declined</span>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Menu dropdown */}\n                  <div className=\"flex items-center mx-2\">\n                    <div className=\"relative group\">\n                      <button className=\"flex items-center space-x-1 p-1 rounded hover:bg-gray-100\">\n                        <span className=\"text-sm text-gray-700\">{guest.menu_name || 'Select menu'}</span>\n                      </button>\n                      <div className=\"absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-10 hidden group-hover:block\">\n                        {menuOptions.map(menu => (\n                          <button\n                            key={menu.menu_id}\n                            onClick={() => updateGuestMenu(guest, menu.menu_id)}\n                            className=\"w-full px-4 py-2 text-left text-black hover:bg-gray-100\"\n                          >\n                            {menu.name}\n                          </button>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Actions */}\n                  <div className=\"flex items-center\">\n                    {guest.email && (\n                      <button\n                        onClick={() => sendInvitation(guest)}\n                        className={`mr-2 p-1 hover:bg-gray-100 rounded relative ${guest.invitation_sent ? 'text-green-500' : 'text-gray-500'}`}\n                        title={guest.invitation_sent ? 'Invitation sent - Click to resend' : 'Send invitation'}\n                      >\n                        <Mail size={16} />\n                        {guest.invitation_sent && (\n                          <span className=\"absolute w-2 h-2 bg-green-500 rounded-full top-0 right-0\"></span>\n                        )}\n                      </button>\n                    )}\n                    <button\n                      onClick={() => {\n                        setSelectedGuest(guest);\n                        setShowAddGuestModal(true);\n                      }}\n                      className=\"text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded\"\n                      title=\"Edit guest\"\n                    >\n                      <Edit size={16} />\n                    </button>\n                    <button\n                      onClick={() => {\n                        setSelectedGuest(guest);\n                        setShowDeleteGuestConfirm(true);\n                      }}\n                      className=\"text-gray-500 p-1 hover:bg-gray-100 rounded\"\n                      title=\"Delete guest\"\n                    >\n                      <Trash2 size={16} />\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-gray-500 italic p-2\">No guests in this group</div>\n          )}\n        </div>\n      ))}\n\n      {/* Delete Group Confirmation Modal */}\n      {showDeleteGroupConfirm && selectedGroup && (\n        <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\n          <div\n            className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\n            style={{\n              background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\n              boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\n            }}\n          >\n            <h3 className=\"text-xl font-bold mb-4 text-black\">Delete Group</h3>\n            <p className=\"mb-6 text-gray-700\">\n              Are you sure you want to delete the group \"{selectedGroup.name}\"?\n              This will also delete all guests in this group.\n            </p>\n            <div className=\"flex justify-end space-x-2\">\n              <button\n                onClick={() => setShowDeleteGroupConfirm(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => handleDeleteGroup(selectedGroup.group_id)}\n                className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\n              >\n                Delete\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Delete Guest Confirmation Modal */}\n      {showDeleteGuestConfirm && selectedGuest && (\n        <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\n          <div\n            className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\n            style={{\n              background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\n              boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\n            }}\n          >\n            <h3 className=\"text-xl font-bold mb-4 text-black\">Delete Guest</h3>\n            <p className=\"mb-6 text-gray-700\">\n              Are you sure you want to delete {selectedGuest.first_name} {selectedGuest.last_name}?\n            </p>\n            <div className=\"flex justify-end space-x-2\">\n              <button\n                onClick={() => setShowDeleteGuestConfirm(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => handleDeleteGuest(selectedGuest.guest_id)}\n                className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\n              >\n                Delete\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Add/Edit Guest Modal */}\n      {showAddGuestModal && (\n        <AddGuestModal\n          groups={groups}\n          menuOptions={menuOptions}\n          onClose={() => {\n            setShowAddGuestModal(false);\n            setSelectedGuest(null);\n            setSelectedGroup(null);\n          }}\n          onSave={fetchData}\n          guest={selectedGuest}\n          defaultGroupId={selectedGroup?.group_id}\n          setError={setError}\n          setSuccessMessage={setSuccessMessage}\n        />\n      )}\n\n      {/* Add/Edit Group Modal */}\n      {showAddGroupModal && (\n        <AddGroupModal\n          onClose={() => {\n            setShowAddGroupModal(false);\n            setEditingGroup(null);\n          }}\n          onSave={fetchData}\n          group={editingGroup}\n          setError={setError}\n          setSuccessMessage={setSuccessMessage}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default GroupsView;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;;AAwBA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,MAAM,EACN,WAAW,EACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,iBAAiB,EACjB,iBAAiB,EACjB,oBAAoB,EACpB,iBAAiB,EACjB,oBAAoB,EACrB;;IAEC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAE/D,2BAA2B;IAC3B,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA;QAC/B,2DAA2D;QAC3D,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,QAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,MAAM,QAAQ;QAE3F,OAAO;YACL,GAAG,KAAK;YACR,QAAQ;YACR,aAAa,YAAY,MAAM;QACjC;IACF;IAEA,iBAAiB;IACjB,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,wFAAwF;gBACzG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;gBAC5C,MAAM;oBAAE,UAAU;gBAAQ;YAC5B;YAEA,UAAU,CAAA,aAAc,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YAC9D,kBAAkB;YAClB,0BAA0B;QAE5B,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,iBAAiB;IACjB,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,kFAAkF;gBACnG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;gBAC5C,MAAM;oBAAE,UAAU;gBAAQ;YAC5B;YAEA,UAAU,CAAA,aAAc,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YAC9D,kBAAkB;YAClB,0BAA0B;QAE5B,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,iCAAiC;IACjC,MAAM,wBAAwB,OAAO,OAAc;QACjD,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACb,kFACA;gBACE,UAAU,MAAM,QAAQ;gBACxB,mBAAmB;YACrB,GACA;gBAAE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAAE;YAGlD,UAAU,CAAA,aACR,WAAW,GAAG,CAAC,CAAA,IACb,EAAE,QAAQ,KAAK,MAAM,QAAQ,GACzB;wBAAE,GAAG,CAAC;wBAAE,mBAAmB;oBAAU,IACrC;QAIV,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,OAAO,OAAc;QAC3C,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YACzB,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;YAEzD,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,cAAc;gBACjB,SAAS;gBACT;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACb,kFACA;gBACE,UAAU,MAAM,QAAQ;gBACxB,SAAS;YACX,GACA;gBAAE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAAE;YAGlD,UAAU,CAAA,aACR,WAAW,GAAG,CAAC,CAAA,IACb,EAAE,QAAQ,KAAK,MAAM,QAAQ,GACzB;wBAAE,GAAG,CAAC;wBAAE,SAAS;wBAAQ,WAAW,aAAa,IAAI;oBAAC,IACtD;QAIV,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,iFAAiF;IACjF,MAAM,gBAAgB;QACpB,IAAI;YACF,gCAAgC;YAChC,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YACzB,IAAI,OAAO;gBACT,IAAI;oBACF,MAAM,mBAAmB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,mFAAmF;wBAC1H,SAAS;4BAAE,eAAe,CAAC,OAAO,EAAE,OAAO;wBAAC;oBAC9C;oBAEA,MAAM,WAAW,iBAAiB,IAAI,CAAC,QAAQ,IAAI,EAAE;oBACrD,IAAI,SAAS,MAAM,GAAG,GAAG;wBACvB,wCAAwC;wBACxC,MAAM,cAAc,QAAQ,CAAC,EAAE,CAAC,YAAY,IACzB,QAAQ,CAAC,EAAE,CAAC,eAAe,IAAI,QAAQ,CAAC,EAAE,CAAC,eAAe,CAAC,YAAY;wBAC1F,IAAI,aAAa;4BACf,QAAQ,GAAG,CAAC,oCAAoC;4BAChD,OAAO;wBACT;oBACF;gBACF,EAAE,OAAO,cAAc;oBACrB,QAAQ,IAAI,CAAC,+BAA+B;gBAC5C,iCAAiC;gBACnC;YACF;YAEA,wBAAwB;YACxB,MAAM,eAAe,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,aAAa,IAAI,CAAC,IAAI,IAAI,CAAC;YACxC,MAAM,aAAa,GAAG,KAAK,UAAU,IAAI,MAAM,GAAG,EAAE,KAAK,SAAS,IAAI,WAAW;YACjF,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;IACF;IAEA,yCAAyC;IACzC,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,mBAAmB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,mFAAmF;gBAC1H,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,MAAM,WAAW,iBAAiB,IAAI,CAAC,QAAQ,IAAI,EAAE;YAErD,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,SAAS;gBACT,OAAO;YACT;YAEA,OAAO;gBACL,WAAW,QAAQ,CAAC,EAAE,CAAC,UAAU;gBACjC,YAAY,QAAQ,CAAC,EAAE,CAAC,YAAY;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;YACT,OAAO;QACT;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAqB,OAAO,OAAe,SAAiB;QAChE,IAAI;YACF,OAAO,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CACrB,qFACA;gBACE,WAAW;oBAAC;iBAAQ;gBACpB,YAAY;YACd,GACA;gBAAE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAAE;QAEpD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACR;IACF;IAEA,+CAA+C;IAC/C,MAAM,sBAAsB,OAC1B,OACA,WACA,YACA,YACA,aACA;QAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,gCAAgC;YAC/C;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,0BAA0B;YAC1B,MAAM,cAAc,MAAM,eAAe;YACzC,IAAI,CAAC,aAAa;YAElB,yCAAyC;YACzC,MAAM,kBAAkB,MAAM,mBAAmB,OAAO,MAAM,QAAQ,EAAE,YAAY,SAAS;YAE7F,kBAAkB;YAClB,MAAM,aAAa,MAAM;YAEzB,sBAAsB;YACtB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,cAAc,IAAI,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,YAAY,SAAS,EAAE;YACzG,MAAM,cAAc,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,mCAAmC,EAAE,eAAe;YAElG,wBAAwB;YACxB,MAAM,oBACJ,MAAM,KAAK,EACX,GAAG,MAAM,UAAU,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE,EACxC,YACA,YAAY,UAAU,EACtB,aACA,MAAM,QAAQ;YAGhB,kBAAkB,CAAC,mBAAmB,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE;YAC7E,aAAa,sCAAsC;QAErD,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,IAAI,IAAI,QAAQ,EAAE,MAAM,OAAO,SAAS,sBAAsB;gBAC5D,SAAS;YACX,OAAO;gBACL,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;YACvE;QACF;IACF;IAEA,gCAAgC;IAChC,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,MAAM;oBAAI,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,MAAM;oBAAI,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,+LAAA,CAAA,IAAC;oBAAC,MAAM;oBAAI,WAAU;;;;;;YAChC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;;YAEE,cAAc,GAAG,CAAC,CAAA,sBACjB,6LAAC;oBAAyB,WAAU;;sCAElC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC,MAAM,IAAI;;;;;;sDAC5D,6LAAC;4CAAK,WAAU;sDAA8B,MAAM,WAAW;;;;;;;;;;;;8CAEjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;gDACP,iBAAiB;gDACjB,qBAAqB;4CACvB;4CACA,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAEd,6LAAC;4CACC,SAAS;gDACP,gBAAgB;gDAChB,qBAAqB;4CACvB;4CACA,WAAU;sDAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAEd,6LAAC;4CACC,SAAS;gDACP,iBAAiB;gDACjB,0BAA0B;4CAC5B;4CACA,WAAU;sDAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;wBAMnB,MAAM,MAAM,CAAC,MAAM,GAAG,kBACrB,6LAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,sBAChB,6LAAC;oCAAyB,WAAU;;sDAClC,6LAAC;4CAAI,WAAU;;gDACZ,MAAM,UAAU;gDAAC;gDAAE,MAAM,SAAS;;;;;;;sDAIrC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;;4DACf,uBAAuB,MAAM,iBAAiB;0EAC/C,6LAAC;gEAAK,WAAU;0EAAoC,MAAM,iBAAiB;;;;;;;;;;;;kEAE7E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,sBAAsB,OAAO;gEAC5C,WAAU;;kFAEV,6LAAC,uMAAA,CAAA,QAAK;wEAAC,MAAM;wEAAI,WAAU;;;;;;kFAC3B,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEACC,SAAS,IAAM,sBAAsB,OAAO;gEAC5C,WAAU;;kFAEV,6LAAC,uMAAA,CAAA,QAAK;wEAAC,MAAM;wEAAI,WAAU;;;;;;kFAC3B,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEACC,SAAS,IAAM,sBAAsB,OAAO;gEAC5C,WAAU;;kFAEV,6LAAC,+LAAA,CAAA,IAAC;wEAAC,MAAM;wEAAI,WAAU;;;;;;kFACvB,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC;4DAAK,WAAU;sEAAyB,MAAM,SAAS,IAAI;;;;;;;;;;;kEAE9D,6LAAC;wDAAI,WAAU;kEACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;gEAEC,SAAS,IAAM,gBAAgB,OAAO,KAAK,OAAO;gEAClD,WAAU;0EAET,KAAK,IAAI;+DAJL,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;sDAY3B,6LAAC;4CAAI,WAAU;;gDACZ,MAAM,KAAK,kBACV,6LAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,WAAW,CAAC,4CAA4C,EAAE,MAAM,eAAe,GAAG,mBAAmB,iBAAiB;oDACtH,OAAO,MAAM,eAAe,GAAG,sCAAsC;;sEAErE,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;wDACX,MAAM,eAAe,kBACpB,6LAAC;4DAAK,WAAU;;;;;;;;;;;;8DAItB,6LAAC;oDACC,SAAS;wDACP,iBAAiB;wDACjB,qBAAqB;oDACvB;oDACA,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;8DAEd,6LAAC;oDACC,SAAS;wDACP,iBAAiB;wDACjB,0BAA0B;oDAC5B;oDACA,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;;;;;;;;;;;;;mCA1FV,MAAM,QAAQ;;;;;;;;;iDAiG5B,6LAAC;4BAAI,WAAU;sCAA2B;;;;;;;mBA3IpC,MAAM,QAAQ;;;;;YAiJzB,0BAA0B,+BACzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,WAAW;oBACb;;sCAEA,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;;gCAAqB;gCACY,cAAc,IAAI;gCAAC;;;;;;;sCAGjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,0BAA0B;oCACzC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,kBAAkB,cAAc,QAAQ;oCACvD,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,0BAA0B,+BACzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,WAAW;oBACb;;sCAEA,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;;gCAAqB;gCACC,cAAc,UAAU;gCAAC;gCAAE,cAAc,SAAS;gCAAC;;;;;;;sCAEtF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,0BAA0B;oCACzC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,kBAAkB,cAAc,QAAQ;oCACvD,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,mCACC,6LAAC,+KAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,aAAa;gBACb,SAAS;oBACP,qBAAqB;oBACrB,iBAAiB;oBACjB,iBAAiB;gBACnB;gBACA,QAAQ;gBACR,OAAO;gBACP,gBAAgB,eAAe;gBAC/B,UAAU;gBACV,mBAAmB;;;;;;YAKtB,mCACC,6LAAC,+KAAA,CAAA,UAAa;gBACZ,SAAS;oBACP,qBAAqB;oBACrB,gBAAgB;gBAClB;gBACA,QAAQ;gBACR,OAAO;gBACP,UAAU;gBACV,mBAAmB;;;;;;;;;;;;AAK7B;GAvjBM;KAAA;uCAyjBS", "debugId": null}}, {"offset": {"line": 7255, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7261, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/guestlist/AttendanceView.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport { Check, Clock, X, Edit, Trash2, Mail } from 'lucide-react';\nimport axios from 'axios';\nimport { getAuthToken } from '../../utils';\nimport { Guest, Group, MenuOption } from './GuestList';\nimport AddGuestModal from './AddGuestModal';\n\ninterface AttendanceViewProps {\n  guests: Guest[];\n  groups: Group[];\n  menuOptions: MenuOption[];\n  setGuests: React.Dispatch<React.SetStateAction<Guest[]>>;\n  fetchData: () => Promise<void>;\n  setError: (error: string | null) => void;\n  setSuccessMessage: (message: string | null) => void;\n}\n\nconst AttendanceView: React.FC<AttendanceViewProps> = ({\n  guests,\n  groups,\n  menuOptions,\n  setGuests,\n  fetchData,\n  setError,\n  setSuccessMessage\n}) => {\n\n  const [selectedGuest, setSelectedGuest] = useState<Guest | null>(null);\n  const [showAddGuestModal, setShowAddGuestModal] = useState(false);\n  const [showDeleteGuestConfirm, setShowDeleteGuestConfirm] = useState(false);\n\n  // Group guests by attendance status - force string comparison\n  const attending = guests.filter(guest => String(guest.attendance_status) === 'attending');\n  const pending = guests.filter(guest => String(guest.attendance_status) === 'pending');\n  const declined = guests.filter(guest => String(guest.attendance_status) === 'declined');\n\n  // Debug logs\n  console.log('All guests:', guests);\n  console.log('Attending guests:', attending);\n  console.log('Pending guests:', pending);\n  console.log('Declined guests:', declined);\n\n  // Check for any guests with unexpected attendance_status values\n  const unexpectedStatus = guests.filter(guest =>\n    !['attending', 'pending', 'declined'].includes(String(guest.attendance_status)));\n  if (unexpectedStatus.length > 0) {\n    console.log('Guests with unexpected attendance status:', unexpectedStatus);\n  }\n\n  // Update guest attendance status\n  const updateGuestAttendance = async (guest: Guest, newStatus: 'attending' | 'pending' | 'declined') => {\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      await axios.put(\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',\n        {\n          guest_id: guest.guest_id,\n          attendance_status: newStatus\n        },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      setGuests(prevGuests =>\n        prevGuests.map(g =>\n          g.guest_id === guest.guest_id\n            ? { ...g, attendance_status: newStatus }\n            : g\n        )\n      );\n\n    } catch (err: any) {\n      console.error('Error updating guest attendance:', err);\n      setError(err.response?.data?.error || 'Failed to update guest attendance');\n    }\n  };\n\n  // Update guest menu option\n  const updateGuestMenu = async (guest: Guest, menuId: string) => {\n    try {\n      const token = getAuthToken();\n      const selectedMenu = menuOptions.find(m => m.menu_id === menuId);\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      if (!selectedMenu) {\n        setError('Menu option not found');\n        return;\n      }\n\n      await axios.put(\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',\n        {\n          guest_id: guest.guest_id,\n          menu_id: menuId\n        },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      setGuests(prevGuests =>\n        prevGuests.map(g =>\n          g.guest_id === guest.guest_id\n            ? { ...g, menu_id: menuId, menu_name: selectedMenu.name }\n            : g\n        )\n      );\n\n    } catch (err: any) {\n      console.error('Error updating guest menu:', err);\n      setError(err.response?.data?.error || 'Failed to update guest menu');\n    }\n  };\n\n  // Delete a guest\n  const handleDeleteGuest = async (guestId: string) => {\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      await axios.delete('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-guest', {\n        headers: { Authorization: `Bearer ${token}` },\n        data: { guest_id: guestId }\n      });\n\n      setGuests(prevGuests => prevGuests.filter(g => g.guest_id !== guestId));\n      setSuccessMessage('Guest deleted successfully');\n      setShowDeleteGuestConfirm(false);\n\n    } catch (err: any) {\n      console.error('Error deleting guest:', err);\n      setError(err.response?.data?.error || 'Failed to delete guest');\n    }\n  };\n\n  // Send invitation to a guest\n  const sendInvitation = async (guest: Guest) => {\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      // Get the first website (in a real app, you'd let the user choose which website to send)\n      const websitesResponse = await axios.get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      const websites = websitesResponse.data.websites || [];\n\n      if (websites.length === 0) {\n        setError('You need to create a wedding website first to send invitations');\n        return;\n      }\n\n      const websiteId = websites[0].website_id;\n\n      await axios.post(\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/send-invitation',\n        {\n          guest_ids: [guest.guest_id],\n          website_id: websiteId\n        },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      setSuccessMessage(`Invitation sent to ${guest.first_name} ${guest.last_name}`);\n      fetchData(); // Refresh to update invitation status\n\n    } catch (err: any) {\n      console.error('Error sending invitation:', err);\n      setError(err.response?.data?.error || 'Failed to send invitation');\n    }\n  };\n\n  // Render a guest list section\n  const renderGuestSection = (title: string, icon: React.ReactNode, guestList: Guest[], count: number) => (\n    <div className=\"mb-8\">\n      <div className=\"flex items-center mb-4\">\n        <div className=\"flex items-center\">\n          {icon}\n          <h3 className=\"font-semibold text-lg ml-2 text-black\">{title}</h3>\n          <span className=\"ml-2 text-gray-500 text-sm\">{count}</span>\n        </div>\n      </div>\n\n      {guestList.length > 0 ? (\n        <div className=\"space-y-2\">\n          {guestList.map(guest => (\n            <div key={guest.guest_id} className=\"flex items-center justify-between p-2 hover:bg-gray-50 rounded\">\n              <div className=\"flex-1 text-black\">\n                {guest.first_name} {guest.last_name}\n              </div>\n\n              {/* Group */}\n              <div className=\"flex-1 text-gray-600 text-sm\">\n                {guest.group_name}\n              </div>\n\n              {/* Menu dropdown */}\n              <div className=\"flex items-center mx-2\">\n                <div className=\"relative group\">\n                  <button className=\"flex items-center space-x-1 p-1 rounded hover:bg-gray-100\">\n                    <span className=\"text-sm text-gray-700\">{guest.menu_name || 'Select menu'}</span>\n                  </button>\n                  <div className=\"absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-10 hidden group-hover:block\">\n                    {menuOptions.map(menu => (\n                      <button\n                        key={menu.menu_id}\n                        onClick={() => updateGuestMenu(guest, menu.menu_id)}\n                        className=\"w-full px-4 py-2 text-left text-black hover:bg-gray-100\"\n                      >\n                        {menu.name}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Attendance dropdown */}\n              <div className=\"flex items-center mx-2\">\n                <div className=\"relative group\">\n                  <button className=\"flex items-center space-x-1 p-1 rounded hover:bg-gray-100\">\n                    <span className=\"text-sm text-gray-700 capitalize\">{guest.attendance_status}</span>\n                  </button>\n                  <div className=\"absolute right-0 mt-2 w-40 bg-white border rounded shadow-lg z-10 hidden group-hover:block\">\n                    <button\n                      onClick={() => updateGuestAttendance(guest, 'attending')}\n                      className=\"flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100\"\n                    >\n                      <Check size={16} className=\"text-green-500 mr-2\" />\n                      <span>Attending</span>\n                    </button>\n                    <button\n                      onClick={() => updateGuestAttendance(guest, 'pending')}\n                      className=\"flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100\"\n                    >\n                      <Clock size={16} className=\"text-amber-500 mr-2\" />\n                      <span>Pending</span>\n                    </button>\n                    <button\n                      onClick={() => updateGuestAttendance(guest, 'declined')}\n                      className=\"flex items-center w-full px-4 py-2 text-left text-black hover:bg-gray-100\"\n                    >\n                      <X size={16} className=\"text-red-500 mr-2\" />\n                      <span>Declined</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Actions */}\n              <div className=\"flex items-center\">\n                {guest.email && !guest.invitation_sent && (\n                  <button\n                    onClick={() => sendInvitation(guest)}\n                    className=\"text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded\"\n                    title=\"Send invitation\"\n                  >\n                    <Mail size={16} />\n                  </button>\n                )}\n                <button\n                  onClick={() => {\n                    setSelectedGuest(guest);\n                    setShowAddGuestModal(true);\n                  }}\n                  className=\"text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded\"\n                  title=\"Edit guest\"\n                >\n                  <Edit size={16} />\n                </button>\n                <button\n                  onClick={() => {\n                    setSelectedGuest(guest);\n                    setShowDeleteGuestConfirm(true);\n                  }}\n                  className=\"text-gray-500 p-1 hover:bg-gray-100 rounded\"\n                  title=\"Delete guest\"\n                >\n                  <Trash2 size={16} />\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"text-gray-500 italic p-2\">No guests</div>\n      )}\n    </div>\n  );\n\n  return (\n    <div>\n      {renderGuestSection(\n        'Attending',\n        <Check size={20} className=\"text-green-500\" />,\n        attending,\n        attending.length\n      )}\n\n      {renderGuestSection(\n        'Pending',\n        <Clock size={20} className=\"text-amber-500\" />,\n        pending,\n        pending.length\n      )}\n\n      {renderGuestSection(\n        'Declined',\n        <X size={20} className=\"text-red-500\" />,\n        declined,\n        declined.length\n      )}\n\n      {/* Delete Guest Confirmation Modal */}\n      {showDeleteGuestConfirm && selectedGuest && (\n        <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\n          <div\n            className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\n            style={{\n              background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\n              boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\n            }}\n          >\n            <h3 className=\"text-xl font-bold mb-4 text-black\">Delete Guest</h3>\n            <p className=\"mb-6 text-gray-700\">\n              Are you sure you want to delete {selectedGuest.first_name} {selectedGuest.last_name}?\n            </p>\n            <div className=\"flex justify-end space-x-2\">\n              <button\n                onClick={() => setShowDeleteGuestConfirm(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => handleDeleteGuest(selectedGuest.guest_id)}\n                className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\n              >\n                Delete\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Add/Edit Guest Modal */}\n      {showAddGuestModal && (\n        <AddGuestModal\n          groups={groups}\n          menuOptions={menuOptions}\n          onClose={() => {\n            setShowAddGuestModal(false);\n            setSelectedGuest(null);\n          }}\n          onSave={fetchData}\n          guest={selectedGuest}\n          setError={setError}\n          setSuccessMessage={setSuccessMessage}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default AttendanceView;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;AANA;;;;;;AAkBA,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,MAAM,EACN,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,iBAAiB,EAClB;;IAEC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,8DAA8D;IAC9D,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,QAAS,OAAO,MAAM,iBAAiB,MAAM;IAC7E,MAAM,UAAU,OAAO,MAAM,CAAC,CAAA,QAAS,OAAO,MAAM,iBAAiB,MAAM;IAC3E,MAAM,WAAW,OAAO,MAAM,CAAC,CAAA,QAAS,OAAO,MAAM,iBAAiB,MAAM;IAE5E,aAAa;IACb,QAAQ,GAAG,CAAC,eAAe;IAC3B,QAAQ,GAAG,CAAC,qBAAqB;IACjC,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,gEAAgE;IAChE,MAAM,mBAAmB,OAAO,MAAM,CAAC,CAAA,QACrC,CAAC;YAAC;YAAa;YAAW;SAAW,CAAC,QAAQ,CAAC,OAAO,MAAM,iBAAiB;IAC/E,IAAI,iBAAiB,MAAM,GAAG,GAAG;QAC/B,QAAQ,GAAG,CAAC,6CAA6C;IAC3D;IAEA,iCAAiC;IACjC,MAAM,wBAAwB,OAAO,OAAc;QACjD,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACb,kFACA;gBACE,UAAU,MAAM,QAAQ;gBACxB,mBAAmB;YACrB,GACA;gBAAE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAAE;YAGlD,UAAU,CAAA,aACR,WAAW,GAAG,CAAC,CAAA,IACb,EAAE,QAAQ,KAAK,MAAM,QAAQ,GACzB;wBAAE,GAAG,CAAC;wBAAE,mBAAmB;oBAAU,IACrC;QAIV,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,OAAO,OAAc;QAC3C,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YACzB,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;YAEzD,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,cAAc;gBACjB,SAAS;gBACT;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACb,kFACA;gBACE,UAAU,MAAM,QAAQ;gBACxB,SAAS;YACX,GACA;gBAAE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAAE;YAGlD,UAAU,CAAA,aACR,WAAW,GAAG,CAAC,CAAA,IACb,EAAE,QAAQ,KAAK,MAAM,QAAQ,GACzB;wBAAE,GAAG,CAAC;wBAAE,SAAS;wBAAQ,WAAW,aAAa,IAAI;oBAAC,IACtD;QAIV,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,iBAAiB;IACjB,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,kFAAkF;gBACnG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;gBAC5C,MAAM;oBAAE,UAAU;gBAAQ;YAC5B;YAEA,UAAU,CAAA,aAAc,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YAC9D,kBAAkB;YAClB,0BAA0B;QAE5B,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,yFAAyF;YACzF,MAAM,mBAAmB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,mFAAmF;gBAC1H,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,MAAM,WAAW,iBAAiB,IAAI,CAAC,QAAQ,IAAI,EAAE;YAErD,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,SAAS;gBACT;YACF;YAEA,MAAM,YAAY,QAAQ,CAAC,EAAE,CAAC,UAAU;YAExC,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CACd,qFACA;gBACE,WAAW;oBAAC,MAAM,QAAQ;iBAAC;gBAC3B,YAAY;YACd,GACA;gBAAE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAAE;YAGlD,kBAAkB,CAAC,mBAAmB,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE;YAC7E,aAAa,sCAAsC;QAErD,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,CAAC,OAAe,MAAuB,WAAoB,sBACpF,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ;0CACD,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;gBAIjD,UAAU,MAAM,GAAG,kBAClB,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAA,sBACb,6LAAC;4BAAyB,WAAU;;8CAClC,6LAAC;oCAAI,WAAU;;wCACZ,MAAM,UAAU;wCAAC;wCAAE,MAAM,SAAS;;;;;;;8CAIrC,6LAAC;oCAAI,WAAU;8CACZ,MAAM,UAAU;;;;;;8CAInB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC;oDAAK,WAAU;8DAAyB,MAAM,SAAS,IAAI;;;;;;;;;;;0DAE9D,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;wDAEC,SAAS,IAAM,gBAAgB,OAAO,KAAK,OAAO;wDAClD,WAAU;kEAET,KAAK,IAAI;uDAJL,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;8CAY3B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC;oDAAK,WAAU;8DAAoC,MAAM,iBAAiB;;;;;;;;;;;0DAE7E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,sBAAsB,OAAO;wDAC5C,WAAU;;0EAEV,6LAAC,uMAAA,CAAA,QAAK;gEAAC,MAAM;gEAAI,WAAU;;;;;;0EAC3B,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDACC,SAAS,IAAM,sBAAsB,OAAO;wDAC5C,WAAU;;0EAEV,6LAAC,uMAAA,CAAA,QAAK;gEAAC,MAAM;gEAAI,WAAU;;;;;;0EAC3B,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDACC,SAAS,IAAM,sBAAsB,OAAO;wDAC5C,WAAU;;0EAEV,6LAAC,+LAAA,CAAA,IAAC;gEAAC,MAAM;gEAAI,WAAU;;;;;;0EACvB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOd,6LAAC;oCAAI,WAAU;;wCACZ,MAAM,KAAK,IAAI,CAAC,MAAM,eAAe,kBACpC,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAGhB,6LAAC;4CACC,SAAS;gDACP,iBAAiB;gDACjB,qBAAqB;4CACvB;4CACA,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAEd,6LAAC;4CACC,SAAS;gDACP,iBAAiB;gDACjB,0BAA0B;4CAC5B;4CACA,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;;;;;;;;;;;;;2BA3FV,MAAM,QAAQ;;;;;;;;;yCAkG5B,6LAAC;oBAAI,WAAU;8BAA2B;;;;;;;;;;;;IAKhD,qBACE,6LAAC;;YACE,mBACC,2BACA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;gBAAI,WAAU;;;;;sBAC3B,WACA,UAAU,MAAM;YAGjB,mBACC,yBACA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;gBAAI,WAAU;;;;;sBAC3B,SACA,QAAQ,MAAM;YAGf,mBACC,0BACA,6LAAC,+LAAA,CAAA,IAAC;gBAAC,MAAM;gBAAI,WAAU;;;;;sBACvB,UACA,SAAS,MAAM;YAIhB,0BAA0B,+BACzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,WAAW;oBACb;;sCAEA,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;;gCAAqB;gCACC,cAAc,UAAU;gCAAC;gCAAE,cAAc,SAAS;gCAAC;;;;;;;sCAEtF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,0BAA0B;oCACzC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,kBAAkB,cAAc,QAAQ;oCACvD,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,mCACC,6LAAC,+KAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,aAAa;gBACb,SAAS;oBACP,qBAAqB;oBACrB,iBAAiB;gBACnB;gBACA,QAAQ;gBACR,OAAO;gBACP,UAAU;gBACV,mBAAmB;;;;;;;;;;;;AAK7B;GA5WM;KAAA;uCA8WS", "debugId": null}}, {"offset": {"line": 7864, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7870, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/guestlist/AddMenuModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect } from 'react';\nimport { X } from 'lucide-react';\nimport axios from 'axios';\nimport { getAuthToken } from '../../utils';\nimport { MenuOption } from './GuestList';\nimport Image from \"next/image\";\n\ninterface AddMenuModalProps {\n  onClose: () => void;\n  onSave: () => void;\n  menu: MenuOption | null;\n  setError: (error: string | null) => void;\n  setSuccessMessage: (message: string | null) => void;\n}\n\nconst AddMenuModal: React.FC<AddMenuModalProps> = ({\n  onClose,\n  onSave,\n  menu,\n  setError,\n  setSuccessMessage\n}) => {\n\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  // Initialize form with menu data if editing\n  useEffect(() => {\n    if (menu) {\n      setName(menu.name || '');\n      setDescription(menu.description || '');\n    }\n  }, [menu]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!name) {\n      setError('Menu name is required');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        setLoading(false);\n        return;\n      }\n\n      if (menu) {\n        // Update existing menu\n        await axios.put(\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-menu-option',\n          {\n            menu_id: menu.menu_id,\n            name,\n            description\n          },\n          { headers: { Authorization: `Bearer ${token}` } }\n        );\n\n        setSuccessMessage('Menu option updated successfully');\n      } else {\n        // Add new menu\n        await axios.post(\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-menu-option',\n          {\n            name,\n            description\n          },\n          { headers: { Authorization: `Bearer ${token}` } }\n        );\n\n        setSuccessMessage('Menu option added successfully');\n      }\n\n      onSave();\n      onClose();\n\n    } catch (err: any) {\n      console.error('Error saving menu option:', err);\n      setError(err.response?.data?.error || 'Failed to save menu option');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\n      <div\n        className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\n        style={{\n          background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\n          boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\n        }}\n      >\n        <button\n          onClick={onClose}\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\"\n        >\n          <X size={20} />\n        </button>\n\n        {/* Logo */}\n        <div className=\"flex justify-center pt-6\">\n          <div className=\"text-red-600\">\n            <Image\n              src=\"/pics/logo.png\"\n              alt=\"Wedzat logo\"\n              width={40}\n              height={40}\n              className=\"object-cover\"\n            />\n          </div>\n        </div>\n\n        <div className=\"px-6 py-4\">\n          <h3 className=\"text-2xl font-bold mb-2 text-center\" style={{ color: \"#B31B1E\" }}>\n            {menu ? 'Edit Menu Option' : 'Add Menu Option'}\n          </h3>\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            {/* Menu Name */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Menu Name *\n              </label>\n              <input\n                type=\"text\"\n                value={name}\n                onChange={(e) => setName(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n                required\n              />\n            </div>\n\n            {/* Description */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Description\n              </label>\n              <textarea\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\n                rows={3}\n                placeholder=\"e.g., Vegetarian, Gluten-free, etc.\"\n              />\n            </div>\n          </div>\n\n          <div className=\"mt-6 flex justify-end space-x-2\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50\"\n              disabled={loading}\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"px-4 py-2 bg-[#B31B1E] text-white rounded hover:bg-red-700 disabled:bg-red-300\"\n              disabled={loading}\n            >\n              {loading ? 'Saving...' : (menu ? 'Update' : 'Add')}\n            </button>\n          </div>\n        </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddMenuModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;;;AANA;;;;;;AAgBA,MAAM,eAA4C,CAAC,EACjD,OAAO,EACP,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,iBAAiB,EAClB;;IAEC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM;gBACR,QAAQ,KAAK,IAAI,IAAI;gBACrB,eAAe,KAAK,WAAW,IAAI;YACrC;QACF;iCAAG;QAAC;KAAK;IAET,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM;YACT,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,IAAI,MAAM;gBACR,uBAAuB;gBACvB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACb,wFACA;oBACE,SAAS,KAAK,OAAO;oBACrB;oBACA;gBACF,GACA;oBAAE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAAE;gBAGlD,kBAAkB;YACpB,OAAO;gBACL,eAAe;gBACf,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CACd,qFACA;oBACE;oBACA;gBACF,GACA;oBAAE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;gBAAE;gBAGlD,kBAAkB;YACpB;YAEA;YACA;QAEF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;;8BAEA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;4BAAsC,OAAO;gCAAE,OAAO;4BAAU;sCAC3E,OAAO,qBAAqB;;;;;;sCAGjC,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACvC,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAKZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,UAAU,cAAe,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GAtKM;KAAA;uCAwKS", "debugId": null}}, {"offset": {"line": 8143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/guestlist/MenusView.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport { Edit, Trash2, Plus } from 'lucide-react';\nimport axios from 'axios';\nimport { getAuthToken } from '../../utils';\nimport { Guest, MenuOption } from './GuestList';\nimport AddMenuModal from './AddMenuModal';\n\ninterface MenusViewProps {\n  guests: Guest[];\n  menuOptions: MenuOption[];\n  setMenuOptions: React.Dispatch<React.SetStateAction<MenuOption[]>>;\n  setGuests: React.Dispatch<React.SetStateAction<Guest[]>>;\n  fetchData: () => Promise<void>;\n  setError: (error: string | null) => void;\n  setSuccessMessage: (message: string | null) => void;\n}\n\nconst MenusView: React.FC<MenusViewProps> = ({\n  guests,\n  menuOptions,\n  setMenuOptions,\n  setGuests,\n  fetchData,\n  setError,\n  setSuccessMessage\n}) => {\n\n  const [selectedMenu, setSelectedMenu] = useState<MenuOption | null>(null);\n  const [showAddMenuModal, setShowAddMenuModal] = useState(false);\n  const [showDeleteMenuConfirm, setShowDeleteMenuConfirm] = useState(false);\n\n  // Group guests by menu - use string comparison to ensure matching works correctly\n  const guestsByMenu = menuOptions.map(menu => {\n    const menuGuests = guests.filter(guest => String(guest.menu_id) === String(menu.menu_id));\n    console.log(`Menu ${menu.name} (${menu.menu_id}) has ${menuGuests.length} guests`);\n    if (menuGuests.length > 0) {\n      console.log('First guest in menu:', menuGuests[0]);\n    }\n\n    // Check for any guests with this menu_id using partial matching\n    const partialMatches = guests.filter(g => g.menu_id && g.menu_id.includes(menu.menu_id.substring(0, 8)));\n    if (partialMatches.length > 0 && menuGuests.length === 0) {\n      console.log('Found guests with partial menu_id match:', partialMatches);\n    }\n\n    return {\n      ...menu,\n      guests: menuGuests,\n      guest_count: menuGuests.length\n    };\n  });\n\n  // Debug logs\n  console.log('All menu options:', menuOptions);\n  console.log('All guests:', guests);\n\n  // Check for any guests with menu_id that doesn't match any menu option\n  const orphanedGuests = guests.filter(guest =>\n    !menuOptions.some(menu => String(guest.menu_id) === String(menu.menu_id)));\n  if (orphanedGuests.length > 0) {\n    console.log('Guests with menu_id not matching any menu option:', orphanedGuests);\n  }\n\n  // Add a new menu option\n  const handleAddMenu = () => {\n    setSelectedMenu(null);\n    setShowAddMenuModal(true);\n  };\n\n  // Edit a menu option\n  const handleEditMenu = (menu: MenuOption) => {\n    setSelectedMenu(menu);\n    setShowAddMenuModal(true);\n  };\n\n  // Delete a menu option\n  const handleDeleteMenu = async (menuId: string) => {\n    try {\n      const token = getAuthToken();\n\n      if (!token) {\n        console.warn('No authentication token found');\n        setError('Authentication required');\n        return;\n      }\n\n      await axios.delete('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-menu-option', {\n        headers: { Authorization: `Bearer ${token}` },\n        data: { menu_id: menuId }\n      });\n\n      setMenuOptions(prevMenus => prevMenus.filter(m => m.menu_id !== menuId));\n      setSuccessMessage('Menu option deleted successfully');\n      setShowDeleteMenuConfirm(false);\n\n    } catch (err: any) {\n      console.error('Error deleting menu option:', err);\n      setError(err.response?.data?.error || 'Failed to delete menu option');\n    }\n  };\n\n  return (\n    <div>\n      {/* Add Menu Button */}\n      <div className=\"mb-4\">\n        <button\n          onClick={handleAddMenu}\n          className=\"flex items-center text-[#B31B1E] hover:bg-red-50 px-3 py-2 rounded\"\n        >\n          <Plus size={16} className=\"mr-1\" />\n          Add Menu Option\n        </button>\n      </div>\n\n      {/* Menu Options and their guests */}\n      {guestsByMenu.map(menu => (\n        <div key={menu.menu_id} className=\"mb-6\">\n          {/* Menu header */}\n          <div className=\"flex justify-between items-center mb-2 border-b pb-2\">\n            <div className=\"flex items-center\">\n              <h3 className=\"font-semibold text-lg text-black\">{menu.name}</h3>\n              <span className=\"ml-2 text-gray-500 text-sm\">{menu.guest_count}</span>\n            </div>\n            <div className=\"flex items-center\">\n              {!menu.is_default && (\n                <>\n                  <button\n                    onClick={() => handleEditMenu(menu)}\n                    className=\"text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded\"\n                  >\n                    <Edit size={16} />\n                  </button>\n                  <button\n                    onClick={() => {\n                      setSelectedMenu(menu);\n                      setShowDeleteMenuConfirm(true);\n                    }}\n                    className=\"text-gray-500 p-1 hover:bg-gray-100 rounded\"\n                  >\n                    <Trash2 size={16} />\n                  </button>\n                </>\n              )}\n              {menu.is_default && (\n                <span className=\"text-xs text-gray-500 italic\">Default</span>\n              )}\n            </div>\n          </div>\n\n          {/* Guest list for this menu */}\n          {menu.guests.length > 0 ? (\n            <div className=\"space-y-2\">\n              {menu.guests.map(guest => (\n                <div key={guest.guest_id} className=\"flex items-center justify-between p-2 hover:bg-gray-50 rounded\">\n                  <div className=\"flex-1 text-black\">\n                    {guest.first_name} {guest.last_name}\n                  </div>\n                  <div className=\"text-gray-600 text-sm\">\n                    {guest.group_name}\n                  </div>\n                  <div className=\"text-gray-600 text-sm capitalize\">\n                    {guest.attendance_status}\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-gray-500 italic p-2\">No guests using this menu</div>\n          )}\n        </div>\n      ))}\n\n      {/* Delete Menu Confirmation Modal */}\n      {showDeleteMenuConfirm && selectedMenu && (\n        <div className=\"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4\">\n          <div\n            className=\"rounded-lg shadow-lg w-full max-w-md relative overflow-hidden\"\n            style={{\n              background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\n              boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\n            }}\n          >\n            <h3 className=\"text-xl font-bold mb-4 text-black\">Delete Menu Option</h3>\n            <p className=\"mb-6 text-gray-700\">\n              Are you sure you want to delete the menu option \"{selectedMenu.name}\"?\n              {selectedMenu.guests && selectedMenu.guests.length > 0 && (\n                <span className=\"block mt-2 text-red-600\">\n                  This will remove the menu selection from {selectedMenu.guests.length} guests.\n                </span>\n              )}\n            </p>\n            <div className=\"flex justify-end space-x-2\">\n              <button\n                onClick={() => setShowDeleteMenuConfirm(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => handleDeleteMenu(selectedMenu.menu_id)}\n                className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\n              >\n                Delete\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Add/Edit Menu Modal */}\n      {showAddMenuModal && (\n        <AddMenuModal\n          onClose={() => {\n            setShowAddMenuModal(false);\n            setSelectedMenu(null);\n          }}\n          onSave={fetchData}\n          menu={selectedMenu}\n          setError={setError}\n          setSuccessMessage={setSuccessMessage}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default MenusView;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAEA;;;AANA;;;;;;AAkBA,MAAM,YAAsC,CAAC,EAC3C,MAAM,EACN,WAAW,EACX,cAAc,EACd,SAAS,EACT,SAAS,EACT,QAAQ,EACR,iBAAiB,EAClB;;IAEC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,kFAAkF;IAClF,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA;QACnC,MAAM,aAAa,OAAO,MAAM,CAAC,CAAA,QAAS,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK,OAAO;QACvF,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,MAAM,EAAE,WAAW,MAAM,CAAC,OAAO,CAAC;QACjF,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,QAAQ,GAAG,CAAC,wBAAwB,UAAU,CAAC,EAAE;QACnD;QAEA,gEAAgE;QAChE,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG;QACpG,IAAI,eAAe,MAAM,GAAG,KAAK,WAAW,MAAM,KAAK,GAAG;YACxD,QAAQ,GAAG,CAAC,4CAA4C;QAC1D;QAEA,OAAO;YACL,GAAG,IAAI;YACP,QAAQ;YACR,aAAa,WAAW,MAAM;QAChC;IACF;IAEA,aAAa;IACb,QAAQ,GAAG,CAAC,qBAAqB;IACjC,QAAQ,GAAG,CAAC,eAAe;IAE3B,uEAAuE;IACvE,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QACnC,CAAC,YAAY,IAAI,CAAC,CAAA,OAAQ,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK,OAAO;IACzE,IAAI,eAAe,MAAM,GAAG,GAAG;QAC7B,QAAQ,GAAG,CAAC,qDAAqD;IACnE;IAEA,wBAAwB;IACxB,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,qBAAqB;IACrB,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;YAEzB,IAAI,CAAC,OAAO;gBACV,QAAQ,IAAI,CAAC;gBACb,SAAS;gBACT;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,wFAAwF;gBACzG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;gBAC5C,MAAM;oBAAE,SAAS;gBAAO;YAC1B;YAEA,eAAe,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;YAChE,kBAAkB;YAClB,yBAAyB;QAE3B,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;IAEA,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAS;;;;;;;;;;;;YAMtC,aAAa,GAAG,CAAC,CAAA,qBAChB,6LAAC;oBAAuB,WAAU;;sCAEhC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC,KAAK,IAAI;;;;;;sDAC3D,6LAAC;4CAAK,WAAU;sDAA8B,KAAK,WAAW;;;;;;;;;;;;8CAEhE,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,KAAK,UAAU,kBACf;;8DACE,6LAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,WAAU;8DAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;8DAEd,6LAAC;oDACC,SAAS;wDACP,gBAAgB;wDAChB,yBAAyB;oDAC3B;oDACA,WAAU;8DAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;;;;;;;;wCAInB,KAAK,UAAU,kBACd,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;wBAMpD,KAAK,MAAM,CAAC,MAAM,GAAG,kBACpB,6LAAC;4BAAI,WAAU;sCACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA,sBACf,6LAAC;oCAAyB,WAAU;;sDAClC,6LAAC;4CAAI,WAAU;;gDACZ,MAAM,UAAU;gDAAC;gDAAE,MAAM,SAAS;;;;;;;sDAErC,6LAAC;4CAAI,WAAU;sDACZ,MAAM,UAAU;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;sDACZ,MAAM,iBAAiB;;;;;;;mCARlB,MAAM,QAAQ;;;;;;;;;iDAc5B,6LAAC;4BAAI,WAAU;sCAA2B;;;;;;;mBAnDpC,KAAK,OAAO;;;;;YAyDvB,yBAAyB,8BACxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,WAAW;oBACb;;sCAEA,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;;gCAAqB;gCACkB,aAAa,IAAI;gCAAC;gCACnE,aAAa,MAAM,IAAI,aAAa,MAAM,CAAC,MAAM,GAAG,mBACnD,6LAAC;oCAAK,WAAU;;wCAA0B;wCACE,aAAa,MAAM,CAAC,MAAM;wCAAC;;;;;;;;;;;;;sCAI3E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,yBAAyB;oCACxC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB,aAAa,OAAO;oCACpD,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,kCACC,6LAAC,8KAAA,CAAA,UAAY;gBACX,SAAS;oBACP,oBAAoB;oBACpB,gBAAgB;gBAClB;gBACA,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,mBAAmB;;;;;;;;;;;;AAK7B;GA/MM;KAAA;uCAiNS", "debugId": null}}, {"offset": {"line": 8518, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8524, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/guestlist/GuestList.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Users, UserPlus, Clock, Check, X, Search, Download, Printer, Plus } from \"lucide-react\";\r\nimport axios from \"axios\";\r\nimport { getAuthToken } from \"../../utils\";\r\n\r\n// Import view components\r\nimport GroupsView from './GroupsView';\r\nimport AttendanceView from './AttendanceView';\r\nimport MenusView from './MenusView';\r\n\r\n// Types\r\nexport interface Guest {\r\n  guest_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  phone: string;\r\n  group_id: string;\r\n  group_name?: string;\r\n  menu_id: string;\r\n  menu_name?: string;\r\n  attendance_status: 'attending' | 'pending' | 'declined';\r\n  invitation_sent: boolean;\r\n  invitation_sent_date?: string;\r\n  response_date?: string;\r\n  notes?: string;\r\n}\r\n\r\nexport interface Group {\r\n  group_id: string;\r\n  name: string;\r\n  description?: string;\r\n  guest_count?: number;\r\n}\r\n\r\nexport interface MenuOption {\r\n  menu_id: string;\r\n  name: string;\r\n  description?: string;\r\n  is_default: boolean;\r\n}\r\n\r\ninterface GuestListProps {\r\n  setError: (error: string | null) => void;\r\n  setSuccessMessage: (message: string | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  loading: boolean;\r\n  error: string | null;\r\n  successMessage: string | null;\r\n}\r\n\r\ntype TabType = 'groups' | 'attendance' | 'menus';\r\n\r\nconst GuestList: React.FC<GuestListProps> = ({\r\n  setError: setParentError,\r\n  setSuccessMessage: setParentSuccessMessage,\r\n  setLoading: setParentLoading\r\n}) => {\r\n  const [activeTab, setActiveTab] = useState<TabType>('groups');\r\n  const [groups, setGroups] = useState<Group[]>([]);\r\n  const [guests, setGuests] = useState<Guest[]>([]);\r\n  const [menuOptions, setMenuOptions] = useState<MenuOption[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\r\n\r\n  // Update parent state when local state changes\r\n  useEffect(() => {\r\n    setParentLoading(loading);\r\n  }, [loading, setParentLoading]);\r\n\r\n  useEffect(() => {\r\n    setParentError(error);\r\n  }, [error, setParentError]);\r\n\r\n  // Add effect to log guests whenever they change\r\n  useEffect(() => {\r\n    console.log('GUESTS STATE CHANGED - Current guests in state:', guests);\r\n  }, [guests]);\r\n\r\n  // Add effect to log groups whenever they change\r\n  useEffect(() => {\r\n    console.log('GROUPS STATE CHANGED - Current groups in state:', groups);\r\n  }, [groups]);\r\n\r\n  // Add effect to log menu options whenever they change\r\n  useEffect(() => {\r\n    console.log('MENU OPTIONS STATE CHANGED - Current menu options in state:', menuOptions);\r\n  }, [menuOptions]);\r\n\r\n  useEffect(() => {\r\n    setParentSuccessMessage(successMessage);\r\n  }, [successMessage, setParentSuccessMessage]);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [showAddGuestModal, setShowAddGuestModal] = useState(false);\r\n  const [showAddGroupModal, setShowAddGroupModal] = useState(false);\r\n  // Removed couple names and first-time setup state\r\n\r\n  // Stats\r\n  const [stats, setStats] = useState({\r\n    totalGuests: 0,\r\n    attending: 0,\r\n    pending: 0,\r\n    declined: 0,\r\n    adults: 0,\r\n    children: 0\r\n  });\r\n\r\n  // Fetch data\r\n  // Make fetchData return a Promise that resolves when all data is fetched\r\n  const fetchData = async () => {\r\n    return new Promise<void>(async (resolve) => {\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const token = getAuthToken();\r\n\r\n      if (!token) {\r\n        console.warn('No authentication token found');\r\n        setError('Authentication required');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      console.log('Fetching guest list data...');\r\n\r\n      // Fetch guests first to ensure we have the latest data\r\n      const guestsResponse = await axios.get(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-list',\r\n        {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n          // Add cache-busting parameter to prevent caching\r\n          params: { _t: new Date().getTime() }\r\n        }\r\n      );\r\n\r\n      const fetchedGuests = guestsResponse.data.guests || [];\r\n      console.log('Fetched guests:', fetchedGuests);\r\n\r\n      // Force a deep copy to ensure React detects the state change\r\n      const guestsCopy = JSON.parse(JSON.stringify(fetchedGuests));\r\n      setGuests(guestsCopy);\r\n\r\n      // Fetch groups with retry logic for new users\r\n      let fetchedGroups = [];\r\n      try {\r\n        const groupsResponse = await axios.get(\r\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-groups',\r\n          {\r\n            headers: { Authorization: `Bearer ${token}` },\r\n            params: { _t: new Date().getTime() }\r\n          }\r\n        );\r\n        fetchedGroups = groupsResponse.data.groups || [];\r\n      } catch (groupError: any) {\r\n        console.warn('Error fetching groups on first attempt:', groupError);\r\n\r\n        // If we get the specific \"'id'\" error for new users, wait and retry\r\n        if (groupError.response?.data?.error === \"'id'\") {\r\n          console.log('Detected new user, waiting 1 second and retrying...');\r\n\r\n          // Wait 1 second before retrying\r\n          await new Promise(r => setTimeout(r, 1000));\r\n\r\n          try {\r\n            // Retry the request\r\n            const retryResponse = await axios.get(\r\n              'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-groups',\r\n              {\r\n                headers: { Authorization: `Bearer ${token}` },\r\n                params: { _t: new Date().getTime() }\r\n              }\r\n            );\r\n            fetchedGroups = retryResponse.data.groups || [];\r\n            console.log('Retry successful, fetched groups:', fetchedGroups);\r\n          } catch (retryError: any) {\r\n            console.error('Error on retry attempt:', retryError);\r\n            // Continue with empty groups, will be handled by backend on next request\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('Fetched groups:', fetchedGroups);\r\n\r\n      // Force a deep copy to ensure React detects the state change\r\n      const groupsCopy = JSON.parse(JSON.stringify(fetchedGroups));\r\n      setGroups(groupsCopy);\r\n\r\n      // Fetch menu options\r\n      const menuResponse = await axios.get(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/menu-options',\r\n        {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n          // Add cache-busting parameter to prevent caching\r\n          params: { _t: new Date().getTime() }\r\n        }\r\n      );\r\n\r\n      const fetchedMenuOptions = menuResponse.data.menu_options || [];\r\n      console.log('Fetched menu options:', fetchedMenuOptions);\r\n\r\n      // Force a deep copy to ensure React detects the state change\r\n      const menuOptionsCopy = JSON.parse(JSON.stringify(fetchedMenuOptions));\r\n      setMenuOptions(menuOptionsCopy);\r\n\r\n      // Calculate stats\r\n      calculateStats(guestsCopy);\r\n\r\n    } catch (err: any) {\r\n      console.error('Error fetching guest list data:', err);\r\n      setError(err.response?.data?.error || 'Failed to load guest list data');\r\n      // Still resolve the promise even if there's an error, so the UI can update\r\n    } finally {\r\n      setLoading(false);\r\n      resolve();\r\n    }\r\n    });\r\n  };\r\n\r\n  // Calculate statistics\r\n  const calculateStats = (guestList: Guest[]) => {\r\n    const stats = {\r\n      totalGuests: guestList.length,\r\n      attending: guestList.filter(g => String(g.attendance_status) === 'attending').length,\r\n      pending: guestList.filter(g => String(g.attendance_status) === 'pending').length,\r\n      declined: guestList.filter(g => String(g.attendance_status) === 'declined').length,\r\n      adults: guestList.filter(g => g.menu_name?.toLowerCase().includes('adult')).length,\r\n      children: guestList.filter(g => g.menu_name?.toLowerCase().includes('children')).length\r\n    };\r\n\r\n    console.log('Calculated stats:', stats);\r\n    setStats(stats);\r\n  };\r\n\r\n  // Debug function removed\r\n\r\n  // First-time setup is now handled automatically by the backend\r\n\r\n  // Load data on component mount\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, []);\r\n\r\n  // Filter guests based on search query\r\n  const filteredGuests = guests.filter(guest => {\r\n    const fullName = `${guest.first_name} ${guest.last_name}`.toLowerCase();\r\n    return fullName.includes(searchQuery.toLowerCase()) ||\r\n           (guest.email && guest.email.toLowerCase().includes(searchQuery.toLowerCase()));\r\n  });\r\n\r\n  // Show loading indicator while fetching initial data\r\n  if (loading && groups.length === 0) {\r\n    return (\r\n      <div className=\"bg-white rounded-lg shadow p-6 max-w-md mx-auto mt-10\">\r\n        <div className=\"flex flex-col items-center justify-center py-8\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#B31B1E]\"></div>\r\n          <p className=\"mt-4 text-gray-600\">Loading guest list...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Import components dynamically based on active tab\r\n  const renderTabContent = () => {\r\n    if (loading) {\r\n      return (\r\n        <div className=\"flex justify-center items-center h-40\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[#B31B1E]\"></div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (error) {\r\n      return <div className=\"text-red-500 text-center p-4\">{error}</div>;\r\n    }\r\n\r\n    switch (activeTab) {\r\n      case 'groups':\r\n        return (\r\n          <div className=\"p-4\">\r\n            <GroupsView\r\n              groups={groups}\r\n              guests={guests}\r\n              setGroups={setGroups}\r\n              setGuests={setGuests}\r\n              fetchData={fetchData}\r\n              menuOptions={menuOptions}\r\n              setError={setError}\r\n              setSuccessMessage={setSuccessMessage}\r\n              showAddGroupModal={showAddGroupModal}\r\n              setShowAddGroupModal={setShowAddGroupModal}\r\n              showAddGuestModal={showAddGuestModal}\r\n              setShowAddGuestModal={setShowAddGuestModal}\r\n            />\r\n          </div>\r\n        );\r\n      case 'attendance':\r\n        return (\r\n          <div className=\"p-4\">\r\n            <AttendanceView\r\n              guests={filteredGuests}\r\n              groups={groups}\r\n              menuOptions={menuOptions}\r\n              setGuests={setGuests}\r\n              fetchData={fetchData}\r\n              setError={setError}\r\n              setSuccessMessage={setSuccessMessage}\r\n            />\r\n          </div>\r\n        );\r\n      case 'menus':\r\n        return (\r\n          <div className=\"p-4\">\r\n            <MenusView\r\n              guests={filteredGuests}\r\n              menuOptions={menuOptions}\r\n              setMenuOptions={setMenuOptions}\r\n              setGuests={setGuests}\r\n              fetchData={fetchData}\r\n              setError={setError}\r\n              setSuccessMessage={setSuccessMessage}\r\n            />\r\n          </div>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"bg-white rounded-lg shadow p-8 flex justify-center items-center min-h-[300px]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-red-700 mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading guest list...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow\">\r\n      {/* Stats Bar */}\r\n      <div className=\"flex items-center justify-between p-4 border-b\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"bg-red-100 rounded-full p-2 mr-2\">\r\n              <Users size={20} className=\"text-[#B31B1E]\" />\r\n            </div>\r\n            <div>\r\n              <div className=\"text-sm text-gray-500\">Guests</div>\r\n              <div className=\"font-semibold text-black\">{stats.totalGuests} Total</div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center\">\r\n            <div className=\"bg-red-100 rounded-full p-2 mr-2\">\r\n              <UserPlus size={20} className=\"text-[#B31B1E]\" />\r\n            </div>\r\n            <div>\r\n              <div className=\"text-sm text-gray-500\">Menu</div>\r\n              <div className=\"font-semibold text-black\">{stats.adults} Adults, {stats.children} Children</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-4\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"flex items-center\">\r\n              <Check size={16} className=\"text-green-500 mr-1\" />\r\n              <span className=\"text-black\">{stats.attending}</span>\r\n            </div>\r\n            <div className=\"flex items-center\">\r\n              <Clock size={16} className=\"text-amber-500 mr-1\" />\r\n              <span className=\"text-black\">{stats.pending}</span>\r\n            </div>\r\n            <div className=\"flex items-center\">\r\n              <X size={16} className=\"text-red-500 mr-1\" />\r\n              <span className=\"text-black\">{stats.declined}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex justify-between p-4\">\r\n        <div className=\"flex space-x-2\">\r\n          <button\r\n            onClick={() => setShowAddGuestModal(true)}\r\n            className=\"flex items-center bg-[#B31B1E] text-white px-4 py-2 rounded hover:bg-red-700 transition-colors\"\r\n          >\r\n            <Plus size={16} className=\"mr-1\" />\r\n            Guest\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setShowAddGroupModal(true)}\r\n            className=\"flex items-center border border-[#B31B1E] text-[#B31B1E] px-4 py-2 rounded hover:bg-red-50 transition-colors\"\r\n          >\r\n            <Plus size={16} className=\"mr-1\" />\r\n            Group\r\n          </button>\r\n\r\n          {/* Removed redundant buttons */}\r\n        </div>\r\n\r\n        <div className=\"flex space-x-2\">\r\n          <button className=\"flex items-center border border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-50 transition-colors\">\r\n            <Download size={16} className=\"mr-1\" />\r\n            Download\r\n          </button>\r\n\r\n          <button className=\"flex items-center border border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-50 transition-colors\">\r\n            <Printer size={16} className=\"mr-1\" />\r\n            Print\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tabs */}\r\n      <div className=\"border-b\">\r\n        <div className=\"flex\">\r\n          <button\r\n            className={`px-6 py-3 font-medium ${activeTab === 'groups' ? 'border-b-2 border-[#B31B1E] text-[#B31B1E]' : 'text-gray-500'}`}\r\n            onClick={() => setActiveTab('groups')}\r\n          >\r\n            GROUPS\r\n          </button>\r\n          <button\r\n            className={`px-6 py-3 font-medium ${activeTab === 'attendance' ? 'border-b-2 border-[#B31B1E] text-[#B31B1E]' : 'text-gray-500'}`}\r\n            onClick={() => setActiveTab('attendance')}\r\n          >\r\n            ATTENDANCE\r\n          </button>\r\n          <button\r\n            className={`px-6 py-3 font-medium ${activeTab === 'menus' ? 'border-b-2 border-[#B31B1E] text-[#B31B1E]' : 'text-gray-500'}`}\r\n            onClick={() => setActiveTab('menus')}\r\n          >\r\n            MENUS\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Search Bar */}\r\n      <div className=\"p-4 border-b\">\r\n        <div className=\"relative\">\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search guests...\"\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n          />\r\n          <Search className=\"absolute left-3 top-2.5 text-gray-400\" size={18} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      {renderTabContent()}\r\n\r\n      {/* Error and Success Messages */}\r\n      {error && (\r\n        <div className=\"mt-4 p-2 bg-red-100 text-red-700 rounded-md mx-4 mb-4\">\r\n          {error}\r\n        </div>\r\n      )}\r\n\r\n      {successMessage && (\r\n        <div className=\"mt-4 p-2 bg-green-100 text-green-700 rounded-md mx-4 mb-4\">\r\n          {successMessage}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\n\r\n\r\nexport default GuestList;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA,yBAAyB;AACzB;AACA;AACA;;;AATA;;;;;;;;AAsDA,MAAM,YAAsC,CAAC,EAC3C,UAAU,cAAc,EACxB,mBAAmB,uBAAuB,EAC1C,YAAY,gBAAgB,EAC7B;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,iBAAiB;QACnB;8BAAG;QAAC;QAAS;KAAiB;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,eAAe;QACjB;8BAAG;QAAC;QAAO;KAAe;IAE1B,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,QAAQ,GAAG,CAAC,mDAAmD;QACjE;8BAAG;QAAC;KAAO;IAEX,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,QAAQ,GAAG,CAAC,mDAAmD;QACjE;8BAAG;QAAC;KAAO;IAEX,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,QAAQ,GAAG,CAAC,+DAA+D;QAC7E;8BAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,wBAAwB;QAC1B;8BAAG;QAAC;QAAgB;KAAwB;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,kDAAkD;IAElD,QAAQ;IACR,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,aAAa;QACb,WAAW;QACX,SAAS;QACT,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IAEA,aAAa;IACb,yEAAyE;IACzE,MAAM,YAAY;QAChB,OAAO,IAAI,QAAc,OAAO;YAChC,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD;gBAEzB,IAAI,CAAC,OAAO;oBACV,QAAQ,IAAI,CAAC;oBACb,SAAS;oBACT,WAAW;oBACX;gBACF;gBAEA,QAAQ,GAAG,CAAC;gBAEZ,uDAAuD;gBACvD,MAAM,iBAAiB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACpC,gFACA;oBACE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;oBAC5C,iDAAiD;oBACjD,QAAQ;wBAAE,IAAI,IAAI,OAAO,OAAO;oBAAG;gBACrC;gBAGF,MAAM,gBAAgB,eAAe,IAAI,CAAC,MAAM,IAAI,EAAE;gBACtD,QAAQ,GAAG,CAAC,mBAAmB;gBAE/B,6DAA6D;gBAC7D,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;gBAC7C,UAAU;gBAEV,8CAA8C;gBAC9C,IAAI,gBAAgB,EAAE;gBACtB,IAAI;oBACF,MAAM,iBAAiB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACpC,kFACA;wBACE,SAAS;4BAAE,eAAe,CAAC,OAAO,EAAE,OAAO;wBAAC;wBAC5C,QAAQ;4BAAE,IAAI,IAAI,OAAO,OAAO;wBAAG;oBACrC;oBAEF,gBAAgB,eAAe,IAAI,CAAC,MAAM,IAAI,EAAE;gBAClD,EAAE,OAAO,YAAiB;oBACxB,QAAQ,IAAI,CAAC,2CAA2C;oBAExD,oEAAoE;oBACpE,IAAI,WAAW,QAAQ,EAAE,MAAM,UAAU,QAAQ;wBAC/C,QAAQ,GAAG,CAAC;wBAEZ,gCAAgC;wBAChC,MAAM,IAAI,QAAQ,CAAA,IAAK,WAAW,GAAG;wBAErC,IAAI;4BACF,oBAAoB;4BACpB,MAAM,gBAAgB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACnC,kFACA;gCACE,SAAS;oCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gCAAC;gCAC5C,QAAQ;oCAAE,IAAI,IAAI,OAAO,OAAO;gCAAG;4BACrC;4BAEF,gBAAgB,cAAc,IAAI,CAAC,MAAM,IAAI,EAAE;4BAC/C,QAAQ,GAAG,CAAC,qCAAqC;wBACnD,EAAE,OAAO,YAAiB;4BACxB,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,yEAAyE;wBAC3E;oBACF;gBACF;gBAEA,QAAQ,GAAG,CAAC,mBAAmB;gBAE/B,6DAA6D;gBAC7D,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;gBAC7C,UAAU;gBAEV,qBAAqB;gBACrB,MAAM,eAAe,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAClC,kFACA;oBACE,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC;oBAC5C,iDAAiD;oBACjD,QAAQ;wBAAE,IAAI,IAAI,OAAO,OAAO;oBAAG;gBACrC;gBAGF,MAAM,qBAAqB,aAAa,IAAI,CAAC,YAAY,IAAI,EAAE;gBAC/D,QAAQ,GAAG,CAAC,yBAAyB;gBAErC,6DAA6D;gBAC7D,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;gBAClD,eAAe;gBAEf,kBAAkB;gBAClB,eAAe;YAEjB,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,SAAS,IAAI,QAAQ,EAAE,MAAM,SAAS;YACtC,2EAA2E;YAC7E,SAAU;gBACR,WAAW;gBACX;YACF;QACA;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ;YACZ,aAAa,UAAU,MAAM;YAC7B,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,OAAO,EAAE,iBAAiB,MAAM,aAAa,MAAM;YACpF,SAAS,UAAU,MAAM,CAAC,CAAA,IAAK,OAAO,EAAE,iBAAiB,MAAM,WAAW,MAAM;YAChF,UAAU,UAAU,MAAM,CAAC,CAAA,IAAK,OAAO,EAAE,iBAAiB,MAAM,YAAY,MAAM;YAClF,QAAQ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,cAAc,SAAS,UAAU,MAAM;YAClF,UAAU,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,cAAc,SAAS,aAAa,MAAM;QACzF;QAEA,QAAQ,GAAG,CAAC,qBAAqB;QACjC,SAAS;IACX;IAEA,yBAAyB;IAEzB,+DAA+D;IAE/D,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE,CAAC,WAAW;QACrE,OAAO,SAAS,QAAQ,CAAC,YAAY,WAAW,OACxC,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IACnF;IAEA,qDAAqD;IACrD,IAAI,WAAW,OAAO,MAAM,KAAK,GAAG;QAClC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,oDAAoD;IACpD,MAAM,mBAAmB;QACvB,IAAI,SAAS;YACX,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;QAGrB;QAEA,IAAI,OAAO;YACT,qBAAO,6LAAC;gBAAI,WAAU;0BAAgC;;;;;;QACxD;QAEA,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,4KAAA,CAAA,UAAU;wBACT,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,aAAa;wBACb,UAAU;wBACV,mBAAmB;wBACnB,mBAAmB;wBACnB,sBAAsB;wBACtB,mBAAmB;wBACnB,sBAAsB;;;;;;;;;;;YAI9B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gLAAA,CAAA,UAAc;wBACb,QAAQ;wBACR,QAAQ;wBACR,aAAa;wBACb,WAAW;wBACX,WAAW;wBACX,UAAU;wBACV,mBAAmB;;;;;;;;;;;YAI3B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2KAAA,CAAA,UAAS;wBACR,QAAQ;wBACR,aAAa;wBACb,gBAAgB;wBAChB,WAAW;wBACX,WAAW;wBACX,UAAU;wBACV,mBAAmB;;;;;;;;;;;YAI3B;gBACE,OAAO;QACX;IACF;IAEA,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAE7B,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;oDAA4B,MAAM,WAAW;oDAAC;;;;;;;;;;;;;;;;;;;0CAIjE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAEhC,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;oDAA4B,MAAM,MAAM;oDAAC;oDAAU,MAAM,QAAQ;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAKvF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC3B,6LAAC;4CAAK,WAAU;sDAAc,MAAM,SAAS;;;;;;;;;;;;8CAE/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC3B,6LAAC;4CAAK,WAAU;sDAAc,MAAM,OAAO;;;;;;;;;;;;8CAE7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+LAAA,CAAA,IAAC;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAc,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;0CAIrC,6LAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;;;;;;;kCAOvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;0CAIzC,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,2MAAA,CAAA,UAAO;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;0BAO5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAW,CAAC,sBAAsB,EAAE,cAAc,WAAW,+CAA+C,iBAAiB;4BAC7H,SAAS,IAAM,aAAa;sCAC7B;;;;;;sCAGD,6LAAC;4BACC,WAAW,CAAC,sBAAsB,EAAE,cAAc,eAAe,+CAA+C,iBAAiB;4BACjI,SAAS,IAAM,aAAa;sCAC7B;;;;;;sCAGD,6LAAC;4BACC,WAAW,CAAC,sBAAsB,EAAE,cAAc,UAAU,+CAA+C,iBAAiB;4BAC5H,SAAS,IAAM,aAAa;sCAC7B;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;sCAEZ,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;4BAAwC,MAAM;;;;;;;;;;;;;;;;;YAKnE;YAGA,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIJ,gCACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;GAvaM;KAAA;uCA2aS", "debugId": null}}, {"offset": {"line": 9345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9351, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/vendors/Vendors.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\n\ninterface VendorsProps {\n  setError: (error: string | null) => void;\n  setSuccessMessage: (message: string | null) => void;\n  setLoading: (loading: boolean) => void;\n  loading: boolean;\n  error: string | null;\n  successMessage: string | null;\n}\n\nconst Vendors: React.FC<VendorsProps> = ({\n  setError,\n  setSuccessMessage,\n  setLoading,\n  loading,\n  error,\n  successMessage\n}) => {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h2 className=\"text-2xl font-bold mb-6 text-black\">Vendors</h2>\n      <p className=\"text-gray-600\">This feature is coming soon. Stay tuned!</p>\n      \n      {/* Error message */}\n      {error && (\n        <div className=\"mt-4 p-2 bg-red-100 text-red-700 rounded-md\">\n          {error}\n        </div>\n      )}\n\n      {/* Success message */}\n      {successMessage && (\n        <div className=\"mt-4 p-2 bg-green-100 text-green-700 rounded-md\">\n          {successMessage}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Vendors;\n"], "names": [], "mappings": ";;;;AAAA;;AAYA,MAAM,UAAkC,CAAC,EACvC,QAAQ,EACR,iBAAiB,EACjB,UAAU,EACV,OAAO,EACP,KAAK,EACL,cAAc,EACf;IACC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,6LAAC;gBAAE,WAAU;0BAAgB;;;;;;YAG5B,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAKJ,gCACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;KA5BM;uCA8BS", "debugId": null}}, {"offset": {"line": 9407, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9413, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/websites/WebsiteEditor.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { ArrowLeft, Save, Loader2, Upload, Image as ImageIcon, Search } from \"lucide-react\";\r\nimport { ChromePicker } from \"react-color\";\r\nimport axios from \"axios\";\r\n\r\ninterface Template {\r\n  template_id: string;\r\n  name: string;\r\n  thumbnail_url: string;\r\n  description: string;\r\n  default_colors: any;\r\n  default_fonts: any;\r\n}\r\n\r\ninterface Website {\r\n  website_id: string;\r\n  title: string;\r\n  couple_names?: string;\r\n  template_id: string;\r\n  wedding_date: string;\r\n  wedding_location: string;\r\n  about_couple: string;\r\n  design_settings: {\r\n    colors?: {\r\n      primary?: string;\r\n      secondary?: string;\r\n      background?: string;\r\n      text?: string;\r\n      [key: string]: string | undefined;\r\n    };\r\n    fonts?: {\r\n      heading?: string;\r\n      body?: string;\r\n      coupleNames?: string;\r\n      date?: string;\r\n      location?: string;\r\n      aboutCouple?: string;\r\n      [key: string]: string | undefined;\r\n    };\r\n    customImage?: string;\r\n    couple_names?: string; // Added for backward compatibility\r\n  };\r\n  deployed_url: string;\r\n  is_published: boolean;\r\n}\r\n\r\ninterface WebsiteEditorProps {\r\n  templates: Template[];\r\n  website: Website | null;\r\n  onSave: (websiteData: any) => void;\r\n  onCancel: () => void;\r\n  loading: boolean;\r\n}\r\n\r\nconst WebsiteEditor: React.FC<WebsiteEditorProps> = ({\r\n  templates,\r\n  website,\r\n  onSave,\r\n  onCancel,\r\n  loading\r\n}) => {\r\n  const [title, setTitle] = useState(website?.title || \"Our Wedding\");\r\n  // No need for template selection anymore\r\n  const [weddingDate, setWeddingDate] = useState(website?.wedding_date || \"\");\r\n  const [weddingLocation, setWeddingLocation] = useState(website?.wedding_location || \"\");\r\n  const [aboutCouple, setAboutCouple] = useState(website?.about_couple || \"\");\r\n  const [coupleNames, setCoupleNames] = useState(website?.couple_names || website?.design_settings?.couple_names || \"\");\r\n  const [designSettings, setDesignSettings] = useState(website?.design_settings || {\r\n    colors: {\r\n      primary: \"#B31B1E\",\r\n      secondary: \"#333333\",\r\n      background: \"#FFFFFF\",\r\n      text: \"#000000\"\r\n    },\r\n    fonts: {\r\n      heading: \"Playfair Display\",\r\n      body: \"Roboto\",\r\n      coupleNames: \"Playfair Display\",\r\n      date: \"Roboto\",\r\n      location: \"Roboto\",\r\n      aboutCouple: \"Roboto\"\r\n    },\r\n    customImage: website?.design_settings?.customImage || \"\"\r\n  });\r\n\r\n  const [showColorPicker, setShowColorPicker] = useState<string | null>(null);\r\n  const [uploadingImage, setUploadingImage] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [searchResults, setSearchResults] = useState<any[]>([]);\r\n  const [searching, setSearching] = useState(false);\r\n  const [showImageSearch, setShowImageSearch] = useState(false);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // No need for template-related effects anymore\r\n\r\n  // We'll handle template image in the handleSave function instead\r\n\r\n  const handleColorChange = (color: any, colorKey: string) => {\r\n    setDesignSettings({\r\n      ...designSettings,\r\n      colors: {\r\n        ...designSettings.colors,\r\n        [colorKey]: color.hex\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleFontChange = (e: React.ChangeEvent<HTMLSelectElement>, fontKey: string) => {\r\n    // Get the selected font name\r\n    const selectedFontName = e.target.value;\r\n\r\n    // Find the selected font object from availableFonts\r\n    const selectedFont = availableFonts.find(font => font.name === selectedFontName);\r\n\r\n    // If we found the font, use its name\r\n    const fontValue = selectedFont ? selectedFont.name : selectedFontName;\r\n\r\n    // Update the design settings with the selected font\r\n    setDesignSettings({\r\n      ...designSettings,\r\n      fonts: {\r\n        ...designSettings.fonts,\r\n        [fontKey]: fontValue\r\n      }\r\n    });\r\n\r\n    console.log(`Font changed: ${fontKey} = ${fontValue}`);\r\n  };\r\n\r\n  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    try {\r\n      setUploadingImage(true);\r\n\r\n      // For simplicity, let's use a direct image URL instead of uploading\r\n      // This is a temporary solution until the upload API is fixed\r\n      // Create a temporary URL for the uploaded file to show a preview\r\n      // This allows the user to see their actual uploaded image\r\n      const imageUrl = URL.createObjectURL(file);\r\n\r\n      // In a real implementation, you would upload the file to a server\r\n      // and get back a permanent URL\r\n\r\n      // Update design settings with the new image URL\r\n      setDesignSettings({\r\n        ...designSettings,\r\n        customImage: imageUrl\r\n      });\r\n\r\n      setUploadingImage(false);\r\n    } catch (error) {\r\n      console.error('Error handling image:', error);\r\n      alert(\"Failed to process image. Please try again.\");\r\n      setUploadingImage(false);\r\n    }\r\n  };\r\n\r\n  // Function to search for images using our proxy API\r\n  const searchImages = async () => {\r\n    if (!searchQuery.trim()) return;\r\n\r\n    setSearching(true);\r\n    setSearchResults([]);\r\n\r\n    try {\r\n      // Use our Next.js API route which generates Unsplash image URLs\r\n      const response = await axios.get(\r\n        `/api/pexels?query=${encodeURIComponent(searchQuery)}`\r\n      );\r\n\r\n      if (response.data && response.data.photos) {\r\n        setSearchResults(response.data.photos);\r\n      } else {\r\n        setSearchResults([]);\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error searching for images:', error);\r\n      setSearchResults([]);\r\n      // Show a message to the user\r\n      alert('Could not load images. Please try a different search term.');\r\n    } finally {\r\n      setSearching(false);\r\n    }\r\n  };\r\n\r\n  // Function to select an image from search results\r\n  const selectImage = (imageUrl: string) => {\r\n    setDesignSettings({\r\n      ...designSettings,\r\n      customImage: imageUrl\r\n    });\r\n    setShowImageSearch(false);\r\n  };\r\n\r\n  const handleSave = () => {\r\n    // Ensure we have the required font fields with default values\r\n    const processedFonts = {\r\n      heading: designSettings.fonts?.heading || 'Playfair Display',\r\n      body: designSettings.fonts?.body || 'Roboto',\r\n      coupleNames: designSettings.fonts?.coupleNames || designSettings.fonts?.heading || 'Playfair Display',\r\n      date: designSettings.fonts?.date || designSettings.fonts?.body || 'Roboto',\r\n      location: designSettings.fonts?.location || designSettings.fonts?.body || 'Roboto',\r\n      aboutCouple: designSettings.fonts?.aboutCouple || designSettings.fonts?.body || 'Roboto'\r\n    };\r\n\r\n    // Ensure we have the required color fields with default values\r\n    const processedColors = {\r\n      primary: designSettings.colors?.primary || '#B31B1E',\r\n      secondary: designSettings.colors?.secondary || '#333333',\r\n      background: designSettings.colors?.background || '#FFFFFF',\r\n      text: designSettings.colors?.text || '#000000'\r\n    };\r\n\r\n    // Prepare the data, ensuring wedding_date is properly formatted\r\n    const websiteData = {\r\n      title,\r\n      template_id: website?.template_id || templates[0]?.template_id, // Use existing template or first available\r\n      wedding_date: weddingDate || null, // Ensure empty string is sent as null\r\n      wedding_location: weddingLocation,\r\n      about_couple: aboutCouple,\r\n      couple_names: coupleNames,\r\n      design_settings: {\r\n        colors: processedColors,\r\n        fonts: processedFonts,\r\n        // Use only the custom image from upload or search\r\n        customImage: designSettings.customImage || '',\r\n        // Store couple_names in design_settings for backward compatibility\r\n        couple_names: coupleNames\r\n      },\r\n      is_published: true\r\n    };\r\n\r\n    console.log('Saving website with data:', websiteData);\r\n    onSave(websiteData);\r\n  };\r\n\r\n  // Enhanced font list with categories\r\n  const availableFonts = [\r\n    // Elegant/Script fonts (good for headings and couple names)\r\n    { name: \"Dancing Script\", category: \"script\", style: \"cursive\" },\r\n    { name: \"Great Vibes\", category: \"script\", style: \"cursive\" },\r\n    { name: \"Parisienne\", category: \"script\", style: \"cursive\" },\r\n    { name: \"Tangerine\", category: \"script\", style: \"cursive\" },\r\n    { name: \"Alex Brush\", category: \"script\", style: \"cursive\" },\r\n    { name: \"Allura\", category: \"script\", style: \"cursive\" },\r\n\r\n    // Serif fonts (elegant, traditional)\r\n    { name: \"Playfair Display\", category: \"serif\", style: \"serif\" },\r\n    { name: \"Cormorant Garamond\", category: \"serif\", style: \"serif\" },\r\n    { name: \"Lora\", category: \"serif\", style: \"serif\" },\r\n    { name: \"Baskerville\", category: \"serif\", style: \"serif\" },\r\n    { name: \"Libre Baskerville\", category: \"serif\", style: \"serif\" },\r\n    { name: \"Crimson Text\", category: \"serif\", style: \"serif\" },\r\n\r\n    // Sans-serif fonts (modern, clean)\r\n    { name: \"Montserrat\", category: \"sans-serif\", style: \"sans-serif\" },\r\n    { name: \"Roboto\", category: \"sans-serif\", style: \"sans-serif\" },\r\n    { name: \"Open Sans\", category: \"sans-serif\", style: \"sans-serif\" },\r\n    { name: \"Lato\", category: \"sans-serif\", style: \"sans-serif\" },\r\n    { name: \"Poppins\", category: \"sans-serif\", style: \"sans-serif\" },\r\n    { name: \"Raleway\", category: \"sans-serif\", style: \"sans-serif\" },\r\n\r\n    // Classic fonts\r\n    { name: \"Times New Roman\", category: \"classic\", style: \"serif\" },\r\n    { name: \"Georgia\", category: \"classic\", style: \"serif\" },\r\n    { name: \"Arial\", category: \"classic\", style: \"sans-serif\" },\r\n    { name: \"Helvetica\", category: \"classic\", style: \"sans-serif\" }\r\n  ];\r\n\r\n  // Load Google Fonts for the preview\r\n  useEffect(() => {\r\n    // Create a link element for Google Fonts\r\n    const link = document.createElement('link');\r\n    link.rel = 'stylesheet';\r\n\r\n    // Get unique fonts from the available fonts list\r\n    const fontNames = availableFonts.map(font => font.name.replace(/ /g, '+'));\r\n\r\n    // Create the Google Fonts URL\r\n    link.href = `https://fonts.googleapis.com/css2?family=${fontNames.join('&family=')}&display=swap`;\r\n\r\n    // Add the link to the document head\r\n    document.head.appendChild(link);\r\n\r\n    // Clean up when component unmounts\r\n    return () => {\r\n      document.head.removeChild(link);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <button\r\n            onClick={onCancel}\r\n            className=\"p-2 text-gray-600 hover:text-[#B31B1E] hover:bg-gray-100 rounded-md transition-colors\"\r\n          >\r\n            <ArrowLeft size={20} />\r\n          </button>\r\n          <h2 className=\"text-2xl font-bold text-black\">\r\n            {website ? \"Edit Website\" : \"Create New Website\"}\r\n          </h2>\r\n        </div>\r\n        <button\r\n          onClick={handleSave}\r\n          className=\"flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors\"\r\n          disabled={loading || !title}\r\n        >\r\n          {loading ? (\r\n            <Loader2 size={18} className=\"animate-spin\" />\r\n          ) : (\r\n            <Save size={18} />\r\n          )}\r\n          <span>Save Website</span>\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n        {/* Left Column - Form */}\r\n        <div>\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n              Couple Names\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              value={coupleNames}\r\n              onChange={(e) => setCoupleNames(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n              placeholder=\"Jane & John\"\r\n            />\r\n            <p className=\"text-xs text-gray-500 mt-1\">Example: \"Jane & John\" or \"Jane weds John\"</p>\r\n          </div>\r\n\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n              Website Title\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              value={title}\r\n              onChange={(e) => setTitle(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n              placeholder=\"Our Wedding\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n              Background Image\r\n            </label>\r\n            <div className=\"mt-1 flex items-center\">\r\n                {designSettings.customImage ? (\r\n                  <div className=\"relative w-full h-32 bg-gray-100 rounded-md overflow-hidden\">\r\n                    <img\r\n                      src={designSettings.customImage}\r\n                      alt=\"Custom background\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      onError={(e) => {\r\n                        // If image fails to load, show a placeholder\r\n                        const target = e.target as HTMLImageElement;\r\n                        target.onerror = null; // Prevent infinite loop\r\n                        target.src = 'https://via.placeholder.com/800x400?text=Wedding+Background';\r\n                        // Update the design settings with the placeholder\r\n                        setDesignSettings({\r\n                          ...designSettings,\r\n                          customImage: 'https://via.placeholder.com/800x400?text=Wedding+Background'\r\n                        });\r\n                      }}\r\n                    />\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setDesignSettings({...designSettings, customImage: \"\"})}\r\n                      className=\"absolute top-2 right-2 bg-red-600 text-white p-1 rounded-full hover:bg-red-700\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"w-full\">\r\n                    <div className=\"flex gap-2 mb-3\">\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => fileInputRef.current?.click()}\r\n                        className=\"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#B31B1E]\"\r\n                        disabled={uploadingImage}\r\n                      >\r\n                        {uploadingImage ? (\r\n                          <>\r\n                            <Loader2 className=\"animate-spin -ml-1 mr-2 h-5 w-5 text-gray-500\" />\r\n                            <span>Uploading...</span>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <Upload className=\"-ml-1 mr-2 h-5 w-5 text-gray-500\" />\r\n                            <span>Upload Image</span>\r\n                          </>\r\n                        )}\r\n                      </button>\r\n\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          const newState = !showImageSearch;\r\n                          setShowImageSearch(newState);\r\n                          // If opening the search and no results yet, set a default search\r\n                          if (newState && searchResults.length === 0 && !searchQuery) {\r\n                            setSearchQuery('wedding');\r\n                            setTimeout(() => searchImages(), 100);\r\n                          }\r\n                        }}\r\n                        className=\"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#B31B1E]\"\r\n                      >\r\n                        <Search className=\"-ml-1 mr-2 h-5 w-5 text-gray-500\" />\r\n                        <span>Search Images</span>\r\n                      </button>\r\n                    </div>\r\n\r\n                    {showImageSearch && (\r\n                      <div className=\"mb-4 border rounded-md p-3\">\r\n                        <div className=\"flex flex-col gap-2\">\r\n                          <div className=\"flex mb-1\">\r\n                            <input\r\n                              type=\"text\"\r\n                              value={searchQuery}\r\n                              onChange={(e) => setSearchQuery(e.target.value)}\r\n                              placeholder=\"Search for wedding images...\"\r\n                              className=\"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                              onKeyDown={(e) => e.key === 'Enter' && searchImages()}\r\n                            />\r\n                            <button\r\n                              onClick={searchImages}\r\n                              disabled={searching || !searchQuery.trim()}\r\n                              className=\"px-4 py-2 bg-[#B31B1E] text-white rounded-r-md hover:bg-red-700 transition-colors disabled:bg-gray-400\"\r\n                            >\r\n                              {searching ? <Loader2 size={18} className=\"animate-spin\" /> : 'Search'}\r\n                            </button>\r\n                          </div>\r\n                          <div className=\"flex flex-wrap gap-1 text-xs\">\r\n                            <span className=\"text-gray-500\">Try:</span>\r\n                            {['wedding', 'beach wedding', 'wedding flowers', 'wedding venue', 'rustic wedding', 'elegant wedding'].map(term => (\r\n                              <button\r\n                                key={term}\r\n                                onClick={() => {\r\n                                  setSearchQuery(term);\r\n                                  setTimeout(() => searchImages(), 100);\r\n                                }}\r\n                                className=\"px-2 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors\"\r\n                              >\r\n                                {term}\r\n                              </button>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n\r\n                        {searchResults.length > 0 && (\r\n                          <div className=\"grid grid-cols-3 gap-2 max-h-60 overflow-y-auto\">\r\n                            {searchResults.map((photo: any) => (\r\n                              <div\r\n                                key={photo.id}\r\n                                className=\"cursor-pointer border rounded-md overflow-hidden hover:ring-2 hover:ring-[#B31B1E] transition-all\"\r\n                                onClick={() => selectImage(photo.src.medium)}\r\n                              >\r\n                                <img\r\n                                  src={photo.src.medium}\r\n                                  alt={`Photo by ${photo.photographer}`}\r\n                                  className=\"w-full h-24 object-cover\"\r\n                                  onError={(e) => {\r\n                                    // If image fails to load, show a placeholder\r\n                                    const target = e.target as HTMLImageElement;\r\n                                    target.onerror = null; // Prevent infinite loop\r\n                                    target.src = 'https://via.placeholder.com/300x200?text=Wedding+Image';\r\n                                  }}\r\n                                />\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        )}\r\n\r\n                        {searching && (\r\n                          <div className=\"flex justify-center items-center py-4\">\r\n                            <Loader2 size={24} className=\"animate-spin text-[#B31B1E]\" />\r\n                          </div>\r\n                        )}\r\n\r\n                        {!searching && searchResults.length === 0 && (\r\n                          <p className=\"text-center text-gray-500 py-4\">\r\n                            {searchQuery.trim()\r\n                              ? 'No images found. Try a different search term.'\r\n                              : 'Enter a search term or click one of the suggestions above.'}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n\r\n                    <input\r\n                      type=\"file\"\r\n                      ref={fileInputRef}\r\n                      onChange={handleImageUpload}\r\n                      accept=\"image/*\"\r\n                      className=\"hidden\"\r\n                    />\r\n                    <p className=\"mt-1 text-xs text-gray-500\">\r\n                      Upload an image or search for beautiful wedding images online\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n              Wedding Date\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              value={weddingDate}\r\n              onChange={(e) => setWeddingDate(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n              Wedding Location\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              value={weddingLocation}\r\n              onChange={(e) => setWeddingLocation(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n              placeholder=\"City, Country\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n              About the Couple\r\n            </label>\r\n            <textarea\r\n              value={aboutCouple}\r\n              onChange={(e) => setAboutCouple(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n              rows={4}\r\n              placeholder=\"Tell your story...\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Column - Design Settings */}\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold mb-4 text-black\">Design Settings</h3>\r\n\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n              Colors\r\n            </label>\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              {Object.entries(designSettings.colors || {}).map(([key, value]) => (\r\n                <div key={key} className=\"mb-3\">\r\n                  <label className=\"block text-sm text-gray-600 capitalize mb-1\">\r\n                    {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                  </label>\r\n                  <div className=\"flex items-center\">\r\n                    <div\r\n                      className=\"w-10 h-10 rounded-md border border-gray-300 cursor-pointer mr-2\"\r\n                      style={{ backgroundColor: value as string }}\r\n                      onClick={() => setShowColorPicker(showColorPicker === key ? null : key)}\r\n                    />\r\n                    <input\r\n                      type=\"text\"\r\n                      value={value as string}\r\n                      onChange={(e) => handleColorChange({ hex: e.target.value }, key)}\r\n                      className=\"flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                    />\r\n                  </div>\r\n                  {showColorPicker === key && (\r\n                    <div className=\"absolute z-10 mt-2\">\r\n                      <div\r\n                        className=\"fixed inset-0\"\r\n                        onClick={() => setShowColorPicker(null)}\r\n                      />\r\n                      <ChromePicker\r\n                        color={value as string}\r\n                        onChange={(color) => handleColorChange(color, key)}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n              Fonts\r\n            </label>\r\n            <div className=\"grid grid-cols-1 gap-4\">\r\n              {/* Main Heading Font */}\r\n              <div className=\"mb-3\">\r\n                <label className=\"block text-sm text-gray-600 mb-1\">\r\n                  Main Heading Font\r\n                </label>\r\n                <select\r\n                  value={designSettings.fonts?.heading || \"Playfair Display\"}\r\n                  onChange={(e) => handleFontChange(e, \"heading\")}\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                >\r\n                  <optgroup label=\"Script Fonts (Elegant)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"script\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Serif Fonts (Traditional)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Sans-Serif Fonts (Modern)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"sans-serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Classic Fonts\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"classic\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                </select>\r\n              </div>\r\n\r\n              {/* Couple Names Font */}\r\n              <div className=\"mb-3\">\r\n                <label className=\"block text-sm text-gray-600 mb-1\">\r\n                  Couple Names Font\r\n                </label>\r\n                <select\r\n                  value={designSettings.fonts?.coupleNames || designSettings.fonts?.heading || \"Playfair Display\"}\r\n                  onChange={(e) => handleFontChange(e, \"coupleNames\")}\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                >\r\n                  <optgroup label=\"Script Fonts (Elegant)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"script\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Serif Fonts (Traditional)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Sans-Serif Fonts (Modern)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"sans-serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Classic Fonts\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"classic\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                </select>\r\n              </div>\r\n\r\n              {/* Date Font */}\r\n              <div className=\"mb-3\">\r\n                <label className=\"block text-sm text-gray-600 mb-1\">\r\n                  Date Font\r\n                </label>\r\n                <select\r\n                  value={designSettings.fonts?.date || designSettings.fonts?.body || \"Roboto\"}\r\n                  onChange={(e) => handleFontChange(e, \"date\")}\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                >\r\n                  <optgroup label=\"Script Fonts (Elegant)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"script\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Serif Fonts (Traditional)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Sans-Serif Fonts (Modern)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"sans-serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Classic Fonts\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"classic\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                </select>\r\n              </div>\r\n\r\n              {/* Location Font */}\r\n              <div className=\"mb-3\">\r\n                <label className=\"block text-sm text-gray-600 mb-1\">\r\n                  Location Font\r\n                </label>\r\n                <select\r\n                  value={designSettings.fonts?.location || designSettings.fonts?.body || \"Roboto\"}\r\n                  onChange={(e) => handleFontChange(e, \"location\")}\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                >\r\n                  <optgroup label=\"Script Fonts (Elegant)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"script\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Serif Fonts (Traditional)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Sans-Serif Fonts (Modern)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"sans-serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Classic Fonts\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"classic\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                </select>\r\n              </div>\r\n\r\n              {/* About Couple Font */}\r\n              <div className=\"mb-3\">\r\n                <label className=\"block text-sm text-gray-600 mb-1\">\r\n                  About Couple Font\r\n                </label>\r\n                <select\r\n                  value={designSettings.fonts?.aboutCouple || designSettings.fonts?.body || \"Roboto\"}\r\n                  onChange={(e) => handleFontChange(e, \"aboutCouple\")}\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                >\r\n                  <optgroup label=\"Script Fonts (Elegant)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"script\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Serif Fonts (Traditional)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Sans-Serif Fonts (Modern)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"sans-serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Classic Fonts\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"classic\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                </select>\r\n              </div>\r\n\r\n              {/* General Body Font */}\r\n              <div className=\"mb-3\">\r\n                <label className=\"block text-sm text-gray-600 mb-1\">\r\n                  General Body Font\r\n                </label>\r\n                <select\r\n                  value={designSettings.fonts?.body || \"Roboto\"}\r\n                  onChange={(e) => handleFontChange(e, \"body\")}\r\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black\"\r\n                >\r\n                  <optgroup label=\"Script Fonts (Elegant)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"script\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Serif Fonts (Traditional)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Sans-Serif Fonts (Modern)\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"sans-serif\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                  <optgroup label=\"Classic Fonts\">\r\n                    {availableFonts\r\n                      .filter(font => font.category === \"classic\")\r\n                      .map((font) => (\r\n                        <option\r\n                          key={font.name}\r\n                          value={font.name}\r\n                          style={{ fontFamily: font.name }}\r\n                        >\r\n                          {font.name}\r\n                        </option>\r\n                      ))}\r\n                  </optgroup>\r\n                </select>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-8 border rounded-lg p-4\">\r\n            <h3 className=\"text-lg font-semibold mb-2 text-black\">Preview</h3>\r\n            <div className=\"relative overflow-hidden rounded-lg border shadow-md\" style={{ height: '500px' }}>\r\n              {/* Background Image */}\r\n              <div\r\n                className=\"absolute inset-0 bg-cover bg-center\"\r\n                style={{\r\n                  backgroundImage: designSettings.customImage ?\r\n                    `url(${designSettings.customImage})` :\r\n                    (selectImage ? `url(${selectImage})` : 'none'),\r\n                  opacity: 0.7,\r\n                  backgroundColor: designSettings.colors?.background || '#FFFFFF',\r\n                  backgroundSize: 'cover',\r\n                  backgroundPosition: 'center',\r\n                }}\r\n              />\r\n\r\n              {/* Content Container */}\r\n              <div className=\"relative z-10 h-full flex flex-col items-center justify-center p-8 text-center\">\r\n                {/* Header Section */}\r\n                <div className=\"mb-8\">\r\n                  {/* Always show couple names in preview */}\r\n                <h2\r\n                  className=\"text-3xl mb-2\"\r\n                  style={{\r\n                    color: designSettings.colors?.primary || '#B31B1E',\r\n                    fontFamily: designSettings.fonts?.coupleNames || designSettings.fonts?.heading || 'Playfair Display',\r\n                    textShadow: '0 1px 2px rgba(0,0,0,0.1)'\r\n                  }}\r\n                >\r\n                  {coupleNames || 'Jane & John'}\r\n                </h2>\r\n                  <h1\r\n                    className=\"text-4xl mb-4\"\r\n                    style={{\r\n                      color: designSettings.colors?.primary || '#B31B1E',\r\n                      fontFamily: designSettings.fonts?.heading || 'Playfair Display',\r\n                      textShadow: '0 1px 2px rgba(0,0,0,0.1)'\r\n                    }}\r\n                  >\r\n                    {title || \"Our Wedding\"}\r\n                  </h1>\r\n                  <p\r\n                    className=\"text-xl mb-2\"\r\n                    style={{\r\n                      color: designSettings.colors?.text || '#000000',\r\n                      fontFamily: designSettings.fonts?.date || designSettings.fonts?.body || 'Roboto'\r\n                    }}\r\n                  >\r\n                    {weddingDate ? new Date(weddingDate).toLocaleDateString('en-US', {\r\n                      year: 'numeric',\r\n                      month: 'long',\r\n                      day: 'numeric'\r\n                    }) : \"Wedding Date\"}\r\n                  </p>\r\n                  <p\r\n                    className=\"text-lg\"\r\n                    style={{\r\n                      color: designSettings.colors?.text || '#000000',\r\n                      fontFamily: designSettings.fonts?.location || designSettings.fonts?.body || 'Roboto'\r\n                    }}\r\n                  >\r\n                    {weddingLocation || \"Wedding Location\"}\r\n                  </p>\r\n                </div>\r\n\r\n                {/* Story Section */}\r\n                {aboutCouple && (\r\n                  <div className=\"mb-8 max-w-lg mx-auto bg-white bg-opacity-80 p-4 rounded-lg shadow-sm\">\r\n                    <h3\r\n                      className=\"text-2xl font-semibold mb-3\"\r\n                      style={{\r\n                        color: designSettings.colors?.primary || '#B31B1E',\r\n                        fontFamily: designSettings.fonts?.heading || 'Playfair Display'\r\n                      }}\r\n                    >\r\n                      Our Story\r\n                    </h3>\r\n                    <p\r\n                      className=\"text-base\"\r\n                      style={{\r\n                        color: designSettings.colors?.text || '#000000',\r\n                        fontFamily: designSettings.fonts?.aboutCouple || designSettings.fonts?.body || 'Roboto'\r\n                      }}\r\n                    >\r\n                      {aboutCouple.length > 100 ? aboutCouple.substring(0, 100) + '...' : aboutCouple}\r\n                    </p>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Button */}\r\n                <div\r\n                  className=\"px-6 py-3 rounded-lg shadow-sm transition-transform hover:scale-105\"\r\n                  style={{\r\n                    backgroundColor: designSettings.colors?.secondary || '#333333',\r\n                    color: '#FFFFFF',\r\n                    fontFamily: designSettings.fonts?.body || 'Roboto',\r\n                    cursor: 'pointer'\r\n                  }}\r\n                >\r\n                  we are getting married\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Preview Note */}\r\n            <p className=\"text-xs text-gray-500 mt-2 text-center\">\r\n              This is a preview of how your wedding website will look. The actual website will be fully responsive and optimized for all devices.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WebsiteEditor;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAJA;;;;;AAuDA,MAAM,gBAA8C,CAAC,EACnD,SAAS,EACT,OAAO,EACP,MAAM,EACN,QAAQ,EACR,OAAO,EACR;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,SAAS;IACrD,yCAAyC;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,gBAAgB;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,oBAAoB;IACpF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,gBAAgB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,gBAAgB,SAAS,iBAAiB,gBAAgB;IAClH,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,mBAAmB;QAC/E,QAAQ;YACN,SAAS;YACT,WAAW;YACX,YAAY;YACZ,MAAM;QACR;QACA,OAAO;YACL,SAAS;YACT,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,aAAa;QACf;QACA,aAAa,SAAS,iBAAiB,eAAe;IACxD;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,+CAA+C;IAE/C,iEAAiE;IAEjE,MAAM,oBAAoB,CAAC,OAAY;QACrC,kBAAkB;YAChB,GAAG,cAAc;YACjB,QAAQ;gBACN,GAAG,eAAe,MAAM;gBACxB,CAAC,SAAS,EAAE,MAAM,GAAG;YACvB;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC,GAAyC;QACjE,6BAA6B;QAC7B,MAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;QAEvC,oDAAoD;QACpD,MAAM,eAAe,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QAE/D,qCAAqC;QACrC,MAAM,YAAY,eAAe,aAAa,IAAI,GAAG;QAErD,oDAAoD;QACpD,kBAAkB;YAChB,GAAG,cAAc;YACjB,OAAO;gBACL,GAAG,eAAe,KAAK;gBACvB,CAAC,QAAQ,EAAE;YACb;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,GAAG,EAAE,WAAW;IACvD;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,kBAAkB;YAElB,oEAAoE;YACpE,6DAA6D;YAC7D,iEAAiE;YACjE,0DAA0D;YAC1D,MAAM,WAAW,IAAI,eAAe,CAAC;YAErC,kEAAkE;YAClE,+BAA+B;YAE/B,gDAAgD;YAChD,kBAAkB;gBAChB,GAAG,cAAc;gBACjB,aAAa;YACf;YAEA,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;YACN,kBAAkB;QACpB;IACF;IAEA,oDAAoD;IACpD,MAAM,eAAe;QACnB,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,aAAa;QACb,iBAAiB,EAAE;QAEnB,IAAI;YACF,gEAAgE;YAChE,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,CAAC,kBAAkB,EAAE,mBAAmB,cAAc;YAGxD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;gBACzC,iBAAiB,SAAS,IAAI,CAAC,MAAM;YACvC,OAAO;gBACL,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,iBAAiB,EAAE;YACnB,6BAA6B;YAC7B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,kDAAkD;IAClD,MAAM,cAAc,CAAC;QACnB,kBAAkB;YAChB,GAAG,cAAc;YACjB,aAAa;QACf;QACA,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,8DAA8D;QAC9D,MAAM,iBAAiB;YACrB,SAAS,eAAe,KAAK,EAAE,WAAW;YAC1C,MAAM,eAAe,KAAK,EAAE,QAAQ;YACpC,aAAa,eAAe,KAAK,EAAE,eAAe,eAAe,KAAK,EAAE,WAAW;YACnF,MAAM,eAAe,KAAK,EAAE,QAAQ,eAAe,KAAK,EAAE,QAAQ;YAClE,UAAU,eAAe,KAAK,EAAE,YAAY,eAAe,KAAK,EAAE,QAAQ;YAC1E,aAAa,eAAe,KAAK,EAAE,eAAe,eAAe,KAAK,EAAE,QAAQ;QAClF;QAEA,+DAA+D;QAC/D,MAAM,kBAAkB;YACtB,SAAS,eAAe,MAAM,EAAE,WAAW;YAC3C,WAAW,eAAe,MAAM,EAAE,aAAa;YAC/C,YAAY,eAAe,MAAM,EAAE,cAAc;YACjD,MAAM,eAAe,MAAM,EAAE,QAAQ;QACvC;QAEA,gEAAgE;QAChE,MAAM,cAAc;YAClB;YACA,aAAa,SAAS,eAAe,SAAS,CAAC,EAAE,EAAE;YACnD,cAAc,eAAe;YAC7B,kBAAkB;YAClB,cAAc;YACd,cAAc;YACd,iBAAiB;gBACf,QAAQ;gBACR,OAAO;gBACP,kDAAkD;gBAClD,aAAa,eAAe,WAAW,IAAI;gBAC3C,mEAAmE;gBACnE,cAAc;YAChB;YACA,cAAc;QAChB;QAEA,QAAQ,GAAG,CAAC,6BAA6B;QACzC,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,iBAAiB;QACrB,4DAA4D;QAC5D;YAAE,MAAM;YAAkB,UAAU;YAAU,OAAO;QAAU;QAC/D;YAAE,MAAM;YAAe,UAAU;YAAU,OAAO;QAAU;QAC5D;YAAE,MAAM;YAAc,UAAU;YAAU,OAAO;QAAU;QAC3D;YAAE,MAAM;YAAa,UAAU;YAAU,OAAO;QAAU;QAC1D;YAAE,MAAM;YAAc,UAAU;YAAU,OAAO;QAAU;QAC3D;YAAE,MAAM;YAAU,UAAU;YAAU,OAAO;QAAU;QAEvD,qCAAqC;QACrC;YAAE,MAAM;YAAoB,UAAU;YAAS,OAAO;QAAQ;QAC9D;YAAE,MAAM;YAAsB,UAAU;YAAS,OAAO;QAAQ;QAChE;YAAE,MAAM;YAAQ,UAAU;YAAS,OAAO;QAAQ;QAClD;YAAE,MAAM;YAAe,UAAU;YAAS,OAAO;QAAQ;QACzD;YAAE,MAAM;YAAqB,UAAU;YAAS,OAAO;QAAQ;QAC/D;YAAE,MAAM;YAAgB,UAAU;YAAS,OAAO;QAAQ;QAE1D,mCAAmC;QACnC;YAAE,MAAM;YAAc,UAAU;YAAc,OAAO;QAAa;QAClE;YAAE,MAAM;YAAU,UAAU;YAAc,OAAO;QAAa;QAC9D;YAAE,MAAM;YAAa,UAAU;YAAc,OAAO;QAAa;QACjE;YAAE,MAAM;YAAQ,UAAU;YAAc,OAAO;QAAa;QAC5D;YAAE,MAAM;YAAW,UAAU;YAAc,OAAO;QAAa;QAC/D;YAAE,MAAM;YAAW,UAAU;YAAc,OAAO;QAAa;QAE/D,gBAAgB;QAChB;YAAE,MAAM;YAAmB,UAAU;YAAW,OAAO;QAAQ;QAC/D;YAAE,MAAM;YAAW,UAAU;YAAW,OAAO;QAAQ;QACvD;YAAE,MAAM;YAAS,UAAU;YAAW,OAAO;QAAa;QAC1D;YAAE,MAAM;YAAa,UAAU;YAAW,OAAO;QAAa;KAC/D;IAED,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,yCAAyC;YACzC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,GAAG,GAAG;YAEX,iDAAiD;YACjD,MAAM,YAAY,eAAe,GAAG;qDAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM;;YAErE,8BAA8B;YAC9B,KAAK,IAAI,GAAG,CAAC,yCAAyC,EAAE,UAAU,IAAI,CAAC,YAAY,aAAa,CAAC;YAEjG,oCAAoC;YACpC,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,mCAAmC;YACnC;2CAAO;oBACL,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;;QACF;kCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,MAAM;;;;;;;;;;;0CAEnB,6LAAC;gCAAG,WAAU;0CACX,UAAU,iBAAiB;;;;;;;;;;;;kCAGhC,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,UAAU,WAAW,CAAC;;4BAErB,wBACC,6LAAC,oNAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;qDAE7B,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CAEd,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAIV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,aAAY;;;;;;kDAEd,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAG5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;kDACV,eAAe,WAAW,iBACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK,eAAe,WAAW;oDAC/B,KAAI;oDACJ,WAAU;oDACV,SAAS,CAAC;wDACR,6CAA6C;wDAC7C,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,OAAO,GAAG,MAAM,wBAAwB;wDAC/C,OAAO,GAAG,GAAG;wDACb,kDAAkD;wDAClD,kBAAkB;4DAChB,GAAG,cAAc;4DACjB,aAAa;wDACf;oDACF;;;;;;8DAEF,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,kBAAkB;4DAAC,GAAG,cAAc;4DAAE,aAAa;wDAAE;oDACpE,WAAU;8DACX;;;;;;;;;;;iEAKH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,aAAa,OAAO,EAAE;4DACrC,WAAU;4DACV,UAAU;sEAET,+BACC;;kFACE,6LAAC,oNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;kFACnB,6LAAC;kFAAK;;;;;;;6FAGR;;kFACE,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6LAAC;kFAAK;;;;;;;;;;;;;sEAKZ,6LAAC;4DACC,MAAK;4DACL,SAAS;gEACP,MAAM,WAAW,CAAC;gEAClB,mBAAmB;gEACnB,iEAAiE;gEACjE,IAAI,YAAY,cAAc,MAAM,KAAK,KAAK,CAAC,aAAa;oEAC1D,eAAe;oEACf,WAAW,IAAM,gBAAgB;gEACnC;4DACF;4DACA,WAAU;;8EAEV,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;gDAIT,iCACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4EAC9C,aAAY;4EACZ,WAAU;4EACV,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;sFAEzC,6LAAC;4EACC,SAAS;4EACT,UAAU,aAAa,CAAC,YAAY,IAAI;4EACxC,WAAU;sFAET,0BAAY,6LAAC,oNAAA,CAAA,UAAO;gFAAC,MAAM;gFAAI,WAAU;;;;;uFAAoB;;;;;;;;;;;;8EAGlE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;wEAC/B;4EAAC;4EAAW;4EAAiB;4EAAmB;4EAAiB;4EAAkB;yEAAkB,CAAC,GAAG,CAAC,CAAA,qBACzG,6LAAC;gFAEC,SAAS;oFACP,eAAe;oFACf,WAAW,IAAM,gBAAgB;gFACnC;gFACA,WAAU;0FAET;+EAPI;;;;;;;;;;;;;;;;;wDAaZ,cAAc,MAAM,GAAG,mBACtB,6LAAC;4DAAI,WAAU;sEACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;oEAEC,WAAU;oEACV,SAAS,IAAM,YAAY,MAAM,GAAG,CAAC,MAAM;8EAE3C,cAAA,6LAAC;wEACC,KAAK,MAAM,GAAG,CAAC,MAAM;wEACrB,KAAK,CAAC,SAAS,EAAE,MAAM,YAAY,EAAE;wEACrC,WAAU;wEACV,SAAS,CAAC;4EACR,6CAA6C;4EAC7C,MAAM,SAAS,EAAE,MAAM;4EACvB,OAAO,OAAO,GAAG,MAAM,wBAAwB;4EAC/C,OAAO,GAAG,GAAG;wEACf;;;;;;mEAbG,MAAM,EAAE;;;;;;;;;;wDAoBpB,2BACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gEAAC,MAAM;gEAAI,WAAU;;;;;;;;;;;wDAIhC,CAAC,aAAa,cAAc,MAAM,KAAK,mBACtC,6LAAC;4DAAE,WAAU;sEACV,YAAY,IAAI,KACb,kDACA;;;;;;;;;;;;8DAMZ,6LAAC;oDACC,MAAK;oDACL,KAAK;oDACL,UAAU;oDACV,QAAO;oDACP,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;0CAQpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,MAAM;wCACN,aAAY;;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,eAAe,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC5D,6LAAC;gDAAc,WAAU;;kEACvB,6LAAC;wDAAM,WAAU;kEACd,IAAI,OAAO,CAAC,YAAY,OAAO,IAAI;;;;;;kEAEtC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB;gEAAgB;gEAC1C,SAAS,IAAM,mBAAmB,oBAAoB,MAAM,OAAO;;;;;;0EAErE,6LAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,kBAAkB;wEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;oEAAC,GAAG;gEAC5D,WAAU;;;;;;;;;;;;oDAGb,oBAAoB,qBACnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,SAAS,IAAM,mBAAmB;;;;;;0EAEpC,6LAAC,oNAAA,CAAA,eAAY;gEACX,OAAO;gEACP,UAAU,CAAC,QAAU,kBAAkB,OAAO;;;;;;;;;;;;;+CAzB5C;;;;;;;;;;;;;;;;0CAkChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAmC;;;;;;kEAGpD,6LAAC;wDACC,OAAO,eAAe,KAAK,EAAE,WAAW;wDACxC,UAAU,CAAC,IAAM,iBAAiB,GAAG;wDACrC,WAAU;;0EAEV,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,SACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,cACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,WACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0DAY1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAmC;;;;;;kEAGpD,6LAAC;wDACC,OAAO,eAAe,KAAK,EAAE,eAAe,eAAe,KAAK,EAAE,WAAW;wDAC7E,UAAU,CAAC,IAAM,iBAAiB,GAAG;wDACrC,WAAU;;0EAEV,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,SACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,cACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,WACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0DAY1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAmC;;;;;;kEAGpD,6LAAC;wDACC,OAAO,eAAe,KAAK,EAAE,QAAQ,eAAe,KAAK,EAAE,QAAQ;wDACnE,UAAU,CAAC,IAAM,iBAAiB,GAAG;wDACrC,WAAU;;0EAEV,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,SACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,cACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,WACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0DAY1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAmC;;;;;;kEAGpD,6LAAC;wDACC,OAAO,eAAe,KAAK,EAAE,YAAY,eAAe,KAAK,EAAE,QAAQ;wDACvE,UAAU,CAAC,IAAM,iBAAiB,GAAG;wDACrC,WAAU;;0EAEV,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,SACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,cACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,WACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0DAY1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAmC;;;;;;kEAGpD,6LAAC;wDACC,OAAO,eAAe,KAAK,EAAE,eAAe,eAAe,KAAK,EAAE,QAAQ;wDAC1E,UAAU,CAAC,IAAM,iBAAiB,GAAG;wDACrC,WAAU;;0EAEV,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,SACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,cACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,WACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0DAY1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAmC;;;;;;kEAGpD,6LAAC;wDACC,OAAO,eAAe,KAAK,EAAE,QAAQ;wDACrC,UAAU,CAAC,IAAM,iBAAiB,GAAG;wDACrC,WAAU;;0EAEV,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,SACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,cACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;0EAQtB,6LAAC;gEAAS,OAAM;0EACb,eACE,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,WACjC,GAAG,CAAC,CAAC,qBACJ,6LAAC;wEAEC,OAAO,KAAK,IAAI;wEAChB,OAAO;4EAAE,YAAY,KAAK,IAAI;wEAAC;kFAE9B,KAAK,IAAI;uEAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAa9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,6LAAC;wCAAI,WAAU;wCAAuD,OAAO;4CAAE,QAAQ;wCAAQ;;0DAE7F,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB,eAAe,WAAW,GACzC,CAAC,IAAI,EAAE,eAAe,WAAW,CAAC,CAAC,CAAC,GACnC,uCAAc,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;oDACtC,SAAS;oDACT,iBAAiB,eAAe,MAAM,EAAE,cAAc;oDACtD,gBAAgB;oDAChB,oBAAoB;gDACtB;;;;;;0DAIF,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EAEf,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,eAAe,MAAM,EAAE,WAAW;oEACzC,YAAY,eAAe,KAAK,EAAE,eAAe,eAAe,KAAK,EAAE,WAAW;oEAClF,YAAY;gEACd;0EAEC,eAAe;;;;;;0EAEhB,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,eAAe,MAAM,EAAE,WAAW;oEACzC,YAAY,eAAe,KAAK,EAAE,WAAW;oEAC7C,YAAY;gEACd;0EAEC,SAAS;;;;;;0EAEZ,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,eAAe,MAAM,EAAE,QAAQ;oEACtC,YAAY,eAAe,KAAK,EAAE,QAAQ,eAAe,KAAK,EAAE,QAAQ;gEAC1E;0EAEC,cAAc,IAAI,KAAK,aAAa,kBAAkB,CAAC,SAAS;oEAC/D,MAAM;oEACN,OAAO;oEACP,KAAK;gEACP,KAAK;;;;;;0EAEP,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,eAAe,MAAM,EAAE,QAAQ;oEACtC,YAAY,eAAe,KAAK,EAAE,YAAY,eAAe,KAAK,EAAE,QAAQ;gEAC9E;0EAEC,mBAAmB;;;;;;;;;;;;oDAKvB,6BACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,eAAe,MAAM,EAAE,WAAW;oEACzC,YAAY,eAAe,KAAK,EAAE,WAAW;gEAC/C;0EACD;;;;;;0EAGD,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,eAAe,MAAM,EAAE,QAAQ;oEACtC,YAAY,eAAe,KAAK,EAAE,eAAe,eAAe,KAAK,EAAE,QAAQ;gEACjF;0EAEC,YAAY,MAAM,GAAG,MAAM,YAAY,SAAS,CAAC,GAAG,OAAO,QAAQ;;;;;;;;;;;;kEAM1E,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,iBAAiB,eAAe,MAAM,EAAE,aAAa;4DACrD,OAAO;4DACP,YAAY,eAAe,KAAK,EAAE,QAAQ;4DAC1C,QAAQ;wDACV;kEACD;;;;;;;;;;;;;;;;;;kDAOL,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlE;GA9hCM;KAAA;uCAgiCS", "debugId": null}}, {"offset": {"line": 11215, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/components/websites/Websites.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Plus<PERSON>ir<PERSON>, Edit, Trash2, ExternalLink, Loader2 } from \"lucide-react\";\r\nimport axios from \"axios\";\r\n// import { useRouter } from \"next/navigation\";\r\nimport WebsiteEditor from \"./WebsiteEditor\";\r\n\r\ninterface Website {\r\n  website_id: string;\r\n  title: string;\r\n  couple_names?: string;\r\n  template_id: string;\r\n  wedding_date: string;\r\n  wedding_location: string;\r\n  about_couple: string;\r\n  deployed_url: string;\r\n  is_published: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n  template_name: string;\r\n  template_thumbnail: string;\r\n  design_settings?: {\r\n    colors?: {\r\n      primary: string;\r\n      secondary: string;\r\n      background: string;\r\n      text: string;\r\n    };\r\n    fonts?: {\r\n      heading: string;\r\n      body: string;\r\n      coupleNames?: string;\r\n      date?: string;\r\n      location?: string;\r\n      aboutCouple?: string;\r\n    };\r\n    customImage?: string;\r\n    couple_names?: string; // Added for backward compatibility\r\n  };\r\n}\r\n\r\ninterface Template {\r\n  template_id: string;\r\n  name: string;\r\n  thumbnail_url: string;\r\n  description: string;\r\n  default_colors: any;\r\n  default_fonts: any;\r\n}\r\n\r\ninterface WebsitesProps {\r\n  setError: (error: string | null) => void;\r\n  setSuccessMessage: (message: string | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  loading: boolean;\r\n  error: string | null;\r\n  successMessage: string | null;\r\n}\r\n\r\nconst Websites: React.FC<WebsitesProps> = ({\r\n  setError,\r\n  setSuccessMessage,\r\n  setLoading,\r\n  loading,\r\n  error,\r\n  successMessage\r\n}) => {\r\n  const [websites, setWebsites] = useState<Website[]>([]);\r\n  const [templates, setTemplates] = useState<Template[]>([]);\r\n  const [showEditor, setShowEditor] = useState(false);\r\n  const [editingWebsite, setEditingWebsite] = useState<Website | null>(null);\r\n  const [loadingWebsites, setLoadingWebsites] = useState(true);\r\n  const [loadingTemplates, setLoadingTemplates] = useState(true);\r\n  const [deletingId, setDeletingId] = useState<string | null>(null);\r\n\r\n  // const router = useRouter(); // Uncomment if needed\r\n\r\n  useEffect(() => {\r\n    fetchWebsites();\r\n    fetchTemplates();\r\n  }, []);\r\n\r\n  const fetchWebsites = async () => {\r\n    try {\r\n      setLoadingWebsites(true);\r\n      const token = localStorage.getItem('token');\r\n\r\n      if (!token) {\r\n        setError(\"Authentication required. Please log in.\");\r\n        setLoadingWebsites(false);\r\n        return;\r\n      }\r\n\r\n      const response = await axios.get(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites',\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          }\r\n        }\r\n      );\r\n\r\n      if (response.data && response.data.websites) {\r\n        setWebsites(response.data.websites);\r\n      }\r\n      setLoadingWebsites(false);\r\n    } catch (error) {\r\n      console.error('Error fetching websites:', error);\r\n      setError(\"Failed to load your websites. Please try again.\");\r\n      setLoadingWebsites(false);\r\n    }\r\n  };\r\n\r\n  const fetchTemplates = async () => {\r\n    try {\r\n      setLoadingTemplates(true);\r\n      const token = localStorage.getItem('token');\r\n\r\n      if (!token) {\r\n        setError(\"Authentication required. Please log in.\");\r\n        setLoadingTemplates(false);\r\n        return;\r\n      }\r\n\r\n      const response = await axios.get(\r\n        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates',\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          }\r\n        }\r\n      );\r\n\r\n      if (response.data && response.data.templates) {\r\n        console.log('Templates from API:', response.data.templates);\r\n\r\n        // Ensure templates have valid image URLs\r\n        const processedTemplates = response.data.templates.map((template: any) => ({\r\n          ...template,\r\n          thumbnail_url: template.thumbnail_url || '/placeholder-template.jpg'\r\n        }));\r\n\r\n        console.log('Processed templates:', processedTemplates);\r\n        setTemplates(processedTemplates);\r\n      }\r\n      setLoadingTemplates(false);\r\n    } catch (error) {\r\n      console.error('Error fetching templates:', error);\r\n      setError(\"Failed to load website templates. Please try again.\");\r\n      setLoadingTemplates(false);\r\n    }\r\n  };\r\n\r\n  const handleCreateWebsite = () => {\r\n    setEditingWebsite(null);\r\n    setShowEditor(true);\r\n  };\r\n\r\n  const handleEditWebsite = (website: Website) => {\r\n    setEditingWebsite(website);\r\n    setShowEditor(true);\r\n  };\r\n\r\n  const handleDeleteWebsite = async (websiteId: string) => {\r\n    try {\r\n      setDeletingId(websiteId);\r\n      const token = localStorage.getItem('token');\r\n\r\n      if (!token) {\r\n        setError(\"Authentication required. Please log in.\");\r\n        setDeletingId(null);\r\n        return;\r\n      }\r\n\r\n      await axios.delete(\r\n        `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website?website_id=${websiteId}`,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`\r\n          }\r\n        }\r\n      );\r\n\r\n      setSuccessMessage(\"Website deleted successfully\");\r\n      setWebsites(websites.filter(website => website.website_id !== websiteId));\r\n      setDeletingId(null);\r\n    } catch (error) {\r\n      console.error('Error deleting website:', error);\r\n      setError(\"Failed to delete website. Please try again.\");\r\n      setDeletingId(null);\r\n    }\r\n  };\r\n\r\n  const handleSaveWebsite = async (websiteData: any) => {\r\n    try {\r\n      setLoading(true);\r\n      const token = localStorage.getItem('token');\r\n\r\n      if (!token) {\r\n        setError(\"Authentication required. Please log in.\");\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Find the selected template to include its thumbnail\r\n      const selectedTemplate = templates.find(t => t.template_id === websiteData.template_id);\r\n\r\n      // Prepare the data with template information\r\n      const completeWebsiteData = {\r\n        ...websiteData,\r\n        template_name: selectedTemplate?.name || '',\r\n        template_thumbnail: ''\r\n      };\r\n\r\n      // No need to set template thumbnail anymore\r\n\r\n      console.log('Saving website with data:', completeWebsiteData);\r\n\r\n      if (editingWebsite) {\r\n        // Update existing website\r\n        await axios.put(\r\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-website',\r\n          {\r\n            ...completeWebsiteData,\r\n            website_id: editingWebsite.website_id\r\n          },\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              'Content-Type': 'application/json'\r\n            }\r\n          }\r\n        );\r\n\r\n        setSuccessMessage(\"Website updated successfully\");\r\n      } else {\r\n        // Create new website\r\n        await axios.post(\r\n          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-website',\r\n          completeWebsiteData,\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              'Content-Type': 'application/json'\r\n            }\r\n          }\r\n        );\r\n\r\n        setSuccessMessage(\"Website created successfully\");\r\n      }\r\n\r\n      // Refresh the websites list\r\n      fetchWebsites();\r\n\r\n      // Close the editor\r\n      setShowEditor(false);\r\n      setEditingWebsite(null);\r\n      setLoading(false);\r\n    } catch (error) {\r\n      console.error('Error saving website:', error);\r\n      setError(\"Failed to save website. Please try again.\");\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleVisitWebsite = (website: Website) => {\r\n    // Always use the website ID to construct the URL to ensure it works\r\n    const url = `/wedding/${website.website_id}`;\r\n\r\n    // Make it absolute\r\n    const absoluteUrl = `${window.location.origin}${url}`;\r\n\r\n    // Open in a new tab\r\n    window.open(absoluteUrl, '_blank');\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    if (!dateString) return 'N/A';\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  if (showEditor) {\r\n    return (\r\n      <WebsiteEditor\r\n        templates={templates}\r\n        website={editingWebsite as any}\r\n        onSave={handleSaveWebsite}\r\n        onCancel={() => {\r\n          setShowEditor(false);\r\n          setEditingWebsite(null);\r\n        }}\r\n        loading={loading}\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <h2 className=\"text-2xl font-bold text-black\">Wedding Websites</h2>\r\n        <button\r\n          onClick={handleCreateWebsite}\r\n          className=\"flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors\"\r\n          disabled={loadingTemplates}\r\n        >\r\n          {loadingTemplates ? (\r\n            <Loader2 size={18} className=\"animate-spin\" />\r\n          ) : (\r\n            <PlusCircle size={18} />\r\n          )}\r\n          <span>Create Website</span>\r\n        </button>\r\n      </div>\r\n\r\n      {loadingWebsites ? (\r\n        <div className=\"flex justify-center items-center py-8\">\r\n          <Loader2 size={32} className=\"animate-spin text-[#B31B1E]\" />\r\n        </div>\r\n      ) : websites.length === 0 ? (\r\n        <div className=\"text-center py-8 border border-dashed border-gray-300 rounded-lg\">\r\n          <p className=\"text-gray-600 mb-4\">You haven't created any wedding websites yet.</p>\r\n          <button\r\n            onClick={handleCreateWebsite}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors mx-auto\"\r\n          >\r\n            <PlusCircle size={18} />\r\n            <span>Create Your First Website</span>\r\n          </button>\r\n        </div>\r\n      ) : (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {websites.map((website) => (\r\n            <div key={website.website_id} className=\"border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow\">\r\n              <div className=\"h-40 bg-gray-200 relative\">\r\n                {/* Always show an image, with fallbacks */}\r\n                <img\r\n                  src={website.design_settings?.customImage || website.template_thumbnail || '/placeholder-template.jpg'}\r\n                  alt={website.title}\r\n                  className=\"w-full h-full object-cover\"\r\n                  onError={(e) => {\r\n                    // If image fails to load, replace with fallback\r\n                    const target = e.target as HTMLImageElement;\r\n                    target.onerror = null; // Prevent infinite loop\r\n                    target.src = '/placeholder-template.jpg';\r\n                  }}\r\n                />\r\n                {/* Console log for debugging - removed to fix TypeScript error */}\r\n                <div className=\"absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2\">\r\n                  <h3 className=\"font-semibold truncate\">{website.title}</h3>\r\n                  <p className=\"text-sm opacity-80\">{website.template_name}</p>\r\n                </div>\r\n              </div>\r\n              <div className=\"p-4\">\r\n                <div className=\"mb-3\">\r\n                  {/* Always show couple names */}\r\n                  <p className=\"text-sm text-gray-600 truncate\">\r\n                    <span className=\"font-semibold\">Couple:</span> {website.couple_names || website.design_settings?.couple_names || 'Jane & John'}\r\n                  </p>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    <span className=\"font-semibold\">Wedding Date:</span> {formatDate(website.wedding_date)}\r\n                  </p>\r\n                  <p className=\"text-sm text-gray-600 truncate\">\r\n                    <span className=\"font-semibold\">Location:</span> {website.wedding_location || 'Not specified'}\r\n                  </p>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    <span className=\"font-semibold\">Last Updated:</span> {formatDate(website.updated_at)}\r\n                  </p>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <div className=\"flex space-x-2\">\r\n                    <button\r\n                      onClick={() => handleEditWebsite(website)}\r\n                      className=\"p-2 text-gray-600 hover:text-[#B31B1E] hover:bg-gray-100 rounded-md transition-colors\"\r\n                      title=\"Edit Website\"\r\n                    >\r\n                      <Edit size={18} />\r\n                    </button>\r\n                    <button\r\n                      onClick={() => handleDeleteWebsite(website.website_id)}\r\n                      className=\"p-2 text-gray-600 hover:text-red-600 hover:bg-gray-100 rounded-md transition-colors\"\r\n                      title=\"Delete Website\"\r\n                      disabled={deletingId === website.website_id}\r\n                    >\r\n                      {deletingId === website.website_id ? (\r\n                        <Loader2 size={18} className=\"animate-spin\" />\r\n                      ) : (\r\n                        <Trash2 size={18} />\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                  <button\r\n                    onClick={() => handleVisitWebsite(website)}\r\n                    className=\"flex items-center gap-1 px-3 py-1 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors\"\r\n                    title=\"Visit Website\"\r\n                  >\r\n                    <ExternalLink size={16} />\r\n                    <span>Visit</span>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <div className=\"mt-4 p-2 bg-red-100 text-red-700 rounded-md\">\r\n          {error}\r\n        </div>\r\n      )}\r\n\r\n      {/* Success message */}\r\n      {successMessage && (\r\n        <div className=\"mt-4 p-2 bg-green-100 text-green-700 rounded-md\">\r\n          {successMessage}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Websites;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,+CAA+C;AAC/C;;;AALA;;;;;AA2DA,MAAM,WAAoC,CAAC,EACzC,QAAQ,EACR,iBAAiB,EACjB,UAAU,EACV,OAAO,EACP,KAAK,EACL,cAAc,EACf;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,qDAAqD;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;YACA;QACF;6BAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,mBAAmB;YACnB,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,IAAI,CAAC,OAAO;gBACV,SAAS;gBACT,mBAAmB;gBACnB;YACF;YAEA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,mFACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAGF,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;gBAC3C,YAAY,SAAS,IAAI,CAAC,QAAQ;YACpC;YACA,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;YACT,mBAAmB;QACrB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,oBAAoB;YACpB,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,IAAI,CAAC,OAAO;gBACV,SAAS;gBACT,oBAAoB;gBACpB;YACF;YAEA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,uFACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAGF,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;gBAC5C,QAAQ,GAAG,CAAC,uBAAuB,SAAS,IAAI,CAAC,SAAS;gBAE1D,yCAAyC;gBACzC,MAAM,qBAAqB,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,WAAkB,CAAC;wBACzE,GAAG,QAAQ;wBACX,eAAe,SAAS,aAAa,IAAI;oBAC3C,CAAC;gBAED,QAAQ,GAAG,CAAC,wBAAwB;gBACpC,aAAa;YACf;YACA,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;YACT,oBAAoB;QACtB;IACF;IAEA,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,cAAc;IAChB;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,cAAc;YACd,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,IAAI,CAAC,OAAO;gBACV,SAAS;gBACT,cAAc;gBACd;YACF;YAEA,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAChB,CAAC,4FAA4F,EAAE,WAAW,EAC1G;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAGF,kBAAkB;YAClB,YAAY,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,UAAU,KAAK;YAC9D,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;YACT,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,IAAI,CAAC,OAAO;gBACV,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,sDAAsD;YACtD,MAAM,mBAAmB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,YAAY,WAAW;YAEtF,6CAA6C;YAC7C,MAAM,sBAAsB;gBAC1B,GAAG,WAAW;gBACd,eAAe,kBAAkB,QAAQ;gBACzC,oBAAoB;YACtB;YAEA,4CAA4C;YAE5C,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,IAAI,gBAAgB;gBAClB,0BAA0B;gBAC1B,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CACb,oFACA;oBACE,GAAG,mBAAmB;oBACtB,YAAY,eAAe,UAAU;gBACvC,GACA;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,OAAO;wBAChC,gBAAgB;oBAClB;gBACF;gBAGF,kBAAkB;YACpB,OAAO;gBACL,qBAAqB;gBACrB,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CACd,oFACA,qBACA;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,OAAO;wBAChC,gBAAgB;oBAClB;gBACF;gBAGF,kBAAkB;YACpB;YAEA,4BAA4B;YAC5B;YAEA,mBAAmB;YACnB,cAAc;YACd,kBAAkB;YAClB,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS;YACT,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oEAAoE;QACpE,MAAM,MAAM,CAAC,SAAS,EAAE,QAAQ,UAAU,EAAE;QAE5C,mBAAmB;QACnB,MAAM,cAAc,GAAG,OAAO,QAAQ,CAAC,MAAM,GAAG,KAAK;QAErD,oBAAoB;QACpB,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,YAAY;QACd,qBACE,6LAAC,8KAAA,CAAA,UAAa;YACZ,WAAW;YACX,SAAS;YACT,QAAQ;YACR,UAAU;gBACR,cAAc;gBACd,kBAAkB;YACpB;YACA,SAAS;;;;;;IAGf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,UAAU;;4BAET,iCACC,6LAAC,oNAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;qDAE7B,6LAAC,qNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;0CAEpB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;YAIT,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;oBAAC,MAAM;oBAAI,WAAU;;;;;;;;;;uBAE7B,SAAS,MAAM,KAAK,kBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,qNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;0CAClB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;qCAIV,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wBAA6B,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,KAAK,QAAQ,eAAe,EAAE,eAAe,QAAQ,kBAAkB,IAAI;wCAC3E,KAAK,QAAQ,KAAK;wCAClB,WAAU;wCACV,SAAS,CAAC;4CACR,gDAAgD;4CAChD,MAAM,SAAS,EAAE,MAAM;4CACvB,OAAO,OAAO,GAAG,MAAM,wBAAwB;4CAC/C,OAAO,GAAG,GAAG;wCACf;;;;;;kDAGF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA0B,QAAQ,KAAK;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAAsB,QAAQ,aAAa;;;;;;;;;;;;;;;;;;0CAG5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAc;oDAAE,QAAQ,YAAY,IAAI,QAAQ,eAAe,EAAE,gBAAgB;;;;;;;0DAEnH,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAoB;oDAAE,WAAW,QAAQ,YAAY;;;;;;;0DAEvF,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAgB;oDAAE,QAAQ,gBAAgB,IAAI;;;;;;;0DAEhF,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAoB;oDAAE,WAAW,QAAQ,UAAU;;;;;;;;;;;;;kDAGvF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,kBAAkB;wDACjC,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;;;;;;kEAEd,6LAAC;wDACC,SAAS,IAAM,oBAAoB,QAAQ,UAAU;wDACrD,WAAU;wDACV,OAAM;wDACN,UAAU,eAAe,QAAQ,UAAU;kEAE1C,eAAe,QAAQ,UAAU,iBAChC,6LAAC,oNAAA,CAAA,UAAO;4DAAC,MAAM;4DAAI,WAAU;;;;;iFAE7B,6LAAC,6MAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;;;;;;;;;;;;0DAIpB,6LAAC;gDACC,SAAS,IAAM,mBAAmB;gDAClC,WAAU;gDACV,OAAM;;kEAEN,6LAAC,yNAAA,CAAA,eAAY;wDAAC,MAAM;;;;;;kEACpB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;uBAhEJ,QAAQ,UAAU;;;;;;;;;;YA0EjC,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAKJ,gCACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;GA9WM;KAAA;uCAgXS", "debugId": null}}, {"offset": {"line": 11808, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11814, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/final/WEDZAT_/frontend/app/home/<USER>/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, Suspense } from \"react\";\r\nimport {\r\n  TopNavigation,\r\n  SideNavigation,\r\n} from \"../../../components/HomeDashboard/Navigation\";\r\nimport { useSearchParams, useRouter } from \"next/navigation\";\r\nimport {\r\n  CheckSquare,\r\n  DollarSign,\r\n  Users,\r\n  Globe,\r\n  Image,\r\n} from \"lucide-react\";\r\n\r\n// Import components\r\nimport Checklist from \"./components/checklist/Checklist\";\r\nimport Budget from \"./components/budget/Budget\";\r\nimport GuestList from \"./components/guestlist/GuestList\";\r\nimport Vendors from \"./components/vendors/Vendors\";\r\nimport Websites from \"./components/websites/Websites\";\r\n\r\n// Loading fallback component\r\nfunction TabsLoading() {\r\n  return <div className=\"text-center py-4\">Loading tools...</div>;\r\n}\r\n\r\n// Create a client component that safely uses useSearchParams\r\nfunction TabContent() {\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const tabParam = searchParams?.get(\"tab\");\r\n  const [activeTab, setActiveTab] = useState<string>(tabParam || \"checklist\");\r\n\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\r\n\r\n  // Update active tab when URL parameter changes\r\n  useEffect(() => {\r\n    if (tabParam) {\r\n      setActiveTab(tabParam);\r\n    }\r\n  }, [tabParam]);\r\n\r\n\r\n\r\n  const handleTabChange = (tab: string) => {\r\n    setActiveTab(tab);\r\n    router.push(`/home/<USER>\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col w-full\">\r\n      <h2 className=\"text-xl font-semibold mb-4 text-black\">Planning Tools</h2>\r\n\r\n      {/* Tool Icons Row */}\r\n      <div className=\"grid grid-cols-5 gap-2 mb-6 w-full\">\r\n        <div\r\n          onClick={() => handleTabChange(\"checklist\")}\r\n          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === \"checklist\" ? \"bg-gray-100\" : \"bg-white border border-gray-200\"}`}\r\n        >\r\n          <div className=\"w-10 h-10 flex items-center justify-center mb-2\">\r\n            <CheckSquare size={24} className=\"text-[#B31B1E]\" />\r\n          </div>\r\n          <span className=\"text-sm font-medium text-black\">Checklist</span>\r\n        </div>\r\n\r\n        <div\r\n          onClick={() => handleTabChange(\"guestlist\")}\r\n          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === \"guestlist\" ? \"bg-gray-100\" : \"bg-white border border-gray-200\"}`}\r\n        >\r\n          <div className=\"w-10 h-10 flex items-center justify-center mb-2\">\r\n            <Users size={24} className=\"text-[#B31B1E]\" />\r\n          </div>\r\n          <span className=\"text-sm font-medium text-black\">Guest list</span>\r\n        </div>\r\n\r\n        <div\r\n          onClick={() => handleTabChange(\"budget\")}\r\n          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === \"budget\" ? \"bg-gray-100\" : \"bg-white border border-gray-200\"}`}\r\n        >\r\n          <div className=\"w-10 h-10 flex items-center justify-center mb-2\">\r\n            <DollarSign size={24} className=\"text-[#B31B1E]\" />\r\n          </div>\r\n          <span className=\"text-sm font-medium text-black\">Budget</span>\r\n        </div>\r\n\r\n        <div\r\n          onClick={() => handleTabChange(\"websites\")}\r\n          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === \"websites\" ? \"bg-gray-100\" : \"bg-white border border-gray-200\"}`}\r\n        >\r\n          <div className=\"w-10 h-10 flex items-center justify-center mb-2\">\r\n            <Globe size={24} className=\"text-[#B31B1E]\" />\r\n          </div>\r\n          <span className=\"text-sm font-medium text-black\">Websites</span>\r\n        </div>\r\n\r\n        <div\r\n          onClick={() => handleTabChange(\"digialbum\")}\r\n          className={`flex flex-col items-center justify-center p-4 rounded-md cursor-pointer ${activeTab === \"digialbum\" ? \"bg-gray-100\" : \"bg-white border border-gray-200\"}`}\r\n        >\r\n          <div className=\"w-10 h-10 flex items-center justify-center mb-2\">\r\n            <Image size={24} className=\"text-[#B31B1E]\" />\r\n          </div>\r\n          <span className=\"text-sm font-medium text-black\">Digi Album</span>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      {/* Active Tab Content */}\r\n      <div className=\"mb-8 w-full\">\r\n        {activeTab === \"checklist\" && (\r\n          <Checklist\r\n            setError={setError}\r\n            setSuccessMessage={setSuccessMessage}\r\n            setLoading={setLoading}\r\n            loading={loading}\r\n            error={error}\r\n            successMessage={successMessage}\r\n          />\r\n        )}\r\n        {activeTab === \"budget\" && (\r\n          <Budget\r\n            setError={setError}\r\n            setSuccessMessage={setSuccessMessage}\r\n            setLoading={setLoading}\r\n            loading={loading}\r\n            error={error}\r\n            successMessage={successMessage}\r\n          />\r\n        )}\r\n        {activeTab === \"guestlist\" && (\r\n          <GuestList\r\n            setError={setError}\r\n            setSuccessMessage={setSuccessMessage}\r\n            setLoading={setLoading}\r\n            loading={loading}\r\n            error={error}\r\n            successMessage={successMessage}\r\n          />\r\n        )}\r\n        {activeTab === \"vendors\" && (\r\n          <Vendors\r\n            setError={setError}\r\n            setSuccessMessage={setSuccessMessage}\r\n            setLoading={setLoading}\r\n            loading={loading}\r\n            error={error}\r\n            successMessage={successMessage}\r\n          />\r\n        )}\r\n        {activeTab === \"websites\" && (\r\n          <Websites\r\n            setError={setError}\r\n            setSuccessMessage={setSuccessMessage}\r\n            setLoading={setLoading}\r\n            loading={loading}\r\n            error={error}\r\n            successMessage={successMessage}\r\n          />\r\n        )}\r\n        {activeTab === \"digialbum\" && (\r\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n            <h2 className=\"text-2xl font-bold mb-6 text-black\">Digital Album</h2>\r\n            <p className=\"text-gray-600\">This feature is coming soon. Stay tuned!</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function WeddingToolsPage() {\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [sidebarExpanded, setSidebarExpanded] = useState(false);\r\n\r\n  useEffect(() => setIsClient(true), []);\r\n\r\n  return (\r\n    <div\r\n      className={\r\n        isClient ? \"flex flex-col min-h-screen bg-white w-full\" : \"opacity-0\"\r\n      }\r\n    >\r\n      {/* Top Navigation Bar */}\r\n      <TopNavigation />\r\n\r\n      <div className=\"flex flex-1 bg-white\">\r\n        <SideNavigation\r\n          expanded={sidebarExpanded}\r\n          onExpand={() => setSidebarExpanded(true)}\r\n          onCollapse={() => setSidebarExpanded(false)}\r\n        />\r\n\r\n        {/* Main Content */}\r\n        <main\r\n          className={`flex-1 p-4 bg-white mt-20 ${\r\n            sidebarExpanded ? \"md:ml-48\" : \"md:ml-20\"\r\n          }`}\r\n          style={{\r\n            transition: \"all 300ms ease-in-out\",\r\n            overflowY: \"auto\",\r\n            overflowX: \"hidden\",\r\n            width: \"100%\"\r\n          }}\r\n        >\r\n          <div className=\"w-full mx-auto max-w-full\">\r\n            <Suspense fallback={<TabsLoading />}>\r\n              <TabContent />\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;AAsBA,6BAA6B;AAC7B,SAAS;IACP,qBAAO,6LAAC;QAAI,WAAU;kBAAmB;;;;;;AAC3C;KAFS;AAIT,6DAA6D;AAC7D,SAAS;;IACP,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,cAAc,IAAI;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,YAAY;IAE/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,UAAU;gBACZ,aAAa;YACf;QACF;+BAAG;QAAC;KAAS;IAIb,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,KAAK;IAC9C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAGtD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,cAAc,gBAAgB,mCAAmC;;0CAErK,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAEnC,6LAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;;kCAGnD,6LAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,cAAc,gBAAgB,mCAAmC;;0CAErK,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAE7B,6LAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;;kCAGnD,6LAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,WAAW,gBAAgB,mCAAmC;;0CAElK,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAElC,6LAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;;kCAGnD,6LAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,aAAa,gBAAgB,mCAAmC;;0CAEpK,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAE7B,6LAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;;kCAGnD,6LAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,cAAc,gBAAgB,mCAAmC;;0CAErK,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAE7B,6LAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;0BAOrD,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,6BACb,6LAAC,2KAAA,CAAA,UAAS;wBACR,UAAU;wBACV,mBAAmB;wBACnB,YAAY;wBACZ,SAAS;wBACT,OAAO;wBACP,gBAAgB;;;;;;oBAGnB,cAAc,0BACb,6LAAC,qKAAA,CAAA,UAAM;wBACL,UAAU;wBACV,mBAAmB;wBACnB,YAAY;wBACZ,SAAS;wBACT,OAAO;wBACP,gBAAgB;;;;;;oBAGnB,cAAc,6BACb,6LAAC,2KAAA,CAAA,UAAS;wBACR,UAAU;wBACV,mBAAmB;wBACnB,YAAY;wBACZ,SAAS;wBACT,OAAO;wBACP,gBAAgB;;;;;;oBAGnB,cAAc,2BACb,6LAAC,uKAAA,CAAA,UAAO;wBACN,UAAU;wBACV,mBAAmB;wBACnB,YAAY;wBACZ,SAAS;wBACT,OAAO;wBACP,gBAAgB;;;;;;oBAGnB,cAAc,4BACb,6LAAC,yKAAA,CAAA,UAAQ;wBACP,UAAU;wBACV,mBAAmB;wBACnB,YAAY;wBACZ,SAAS;wBACT,OAAO;wBACP,gBAAgB;;;;;;oBAGnB,cAAc,6BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAhJS;;QACc,qIAAA,CAAA,kBAAe;QACrB,qIAAA,CAAA,YAAS;;;MAFjB;AAkJM,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE,IAAM,YAAY;qCAAO,EAAE;IAErC,qBACE,6LAAC;QACC,WACE,WAAW,+CAA+C;;0BAI5D,6LAAC,6IAAA,CAAA,gBAAa;;;;;0BAEd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6IAAA,CAAA,iBAAc;wBACb,UAAU;wBACV,UAAU,IAAM,mBAAmB;wBACnC,YAAY,IAAM,mBAAmB;;;;;;kCAIvC,6LAAC;wBACC,WAAW,CAAC,0BAA0B,EACpC,kBAAkB,aAAa,YAC/B;wBACF,OAAO;4BACL,YAAY;4BACZ,WAAW;4BACX,WAAW;4BACX,OAAO;wBACT;kCAEA,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6JAAA,CAAA,WAAQ;gCAAC,wBAAU,6LAAC;;;;;0CACnB,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;IA3CwB;MAAA", "debugId": null}}, {"offset": {"line": 12258, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}