"use client";
import React, { useState, useEffect, useCallback, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import {
  TopNavigation,
  SideNavigation,
} from "../../../../components/HomeDashboard/Navigation";
import { ArrowLeft } from "lucide-react";
import searchService, { SearchResults, SearchParams } from "../../../../services/search-api";
import SearchResultsDisplay from "../../../../components/Search/SearchResultsDisplay";

function CategorySearchContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  // Get search parameters
  const query = searchParams?.get("q") || "";
  const types = searchParams?.get("types") || "";
  const username = searchParams?.get("username") || "";
  const tags = searchParams?.get("tags") || "";
  const place = searchParams?.get("place") || "";
  const sortBy = searchParams?.get("sort_by") || "";
  const sortOrder = searchParams?.get("sort_order") || "";
  
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  
  // State for lazy loading
  const [allResults, setAllResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Get category title
  const getCategoryTitle = (type: string) => {
    switch (type) {
      case 'flashes': return 'Flashes';
      case 'glimpses': return 'Glimpses';
      case 'movies': return 'Movies';
      case 'photos': return 'Photos';
      default: return 'Search Results';
    }
  };

  // Load search results
  const loadResults = useCallback(async (page: number = 1, append: boolean = false) => {
    if (!types) return;

    try {
      if (page === 1) {
        setLoading(true);
        setError(null);
      } else {
        setLoadingMore(true);
      }

      const searchParams: SearchParams = {
        q: query || undefined,
        types: types,
        username: username || undefined,
        tags: tags || undefined,
        place: place || undefined,
        sort_by: sortBy || undefined,
        sort_order: (sortOrder as 'asc' | 'desc') || undefined,
        page: page,
        limit: 20 // Load more items per page for category view
      };

      console.log('Loading category results with params:', searchParams);
      const results = await searchService.searchContent(searchParams);
      console.log('Category search results received:', results);
      
      // Get results for the specific category
      const categoryResults = results[types as keyof SearchResults] || [];
      const paginationInfo = results.pagination?.[types];

      if (append && page > 1) {
        setAllResults(prev => [
          ...prev,
          ...(Array.isArray(categoryResults) ? categoryResults : []),
        ]);
      } else {
        setAllResults(Array.isArray(categoryResults) ? categoryResults : []);
      }

      setHasNextPage(paginationInfo?.has_next_page || false);
      setTotalCount(paginationInfo?.total_count || 0);
      setCurrentPage(page);
      
    } catch (err: any) {
      console.error('Category search failed:', err);
      setError(err.message || 'Failed to load search results');
      if (page === 1) {
        setAllResults([]);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [query, types, username, tags, place, sortBy, sortOrder]);

  // Initial load
  useEffect(() => {
    setIsClient(true);
    loadResults(1, false);
  }, [loadResults]);

  // Load more function
  const loadMore = useCallback(() => {
    if (!loadingMore && hasNextPage) {
      loadResults(currentPage + 1, true);
    }
  }, [loadResults, currentPage, hasNextPage, loadingMore]);

  // Infinite scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + document.documentElement.scrollTop
        >= document.documentElement.offsetHeight - 1000 // Load when 1000px from bottom
      ) {
        loadMore();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loadMore]);

  if (!isClient) {
    return <div>Loading...</div>;
  }

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        <main
          className={`flex-1 p-4 bg-white mt-20 ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            
            {/* Header */}
            <div className="mb-6">
              <div className="flex items-center mb-4">
                <button
                  onClick={() => router.back()}
                  className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors mr-4"
                >
                  <ArrowLeft size={20} />
                  <span>Back to Search</span>
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-800">
                    {getCategoryTitle(types)}
                  </h1>
                  {query && (
                    <p className="text-gray-600 mt-1">
                      Search results for "{query}"
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Results */}
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
                <span className="ml-3 text-gray-600">Loading {getCategoryTitle(types).toLowerCase()}...</span>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="text-5xl mb-4">⚠️</div>
                <h2 className="text-xl font-semibold mb-2 text-red-600">Search Error</h2>
                <p className="text-gray-600 mb-4">{error}</p>
                <button
                  onClick={() => loadResults(1, false)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            ) : allResults.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-5xl mb-4">🔍</div>
                <h2 className="text-xl font-semibold mb-2">No {getCategoryTitle(types)} Found</h2>
                <p className="text-gray-600">
                  No {getCategoryTitle(types).toLowerCase()} match your search criteria.
                </p>
              </div>
            ) : (
              <div className="space-y-8">
                {/* Display results using the same component but without show more button */}
                <SearchResultsDisplay
                  title=""
                  items={allResults}
                  contentType={types as 'flashes' | 'glimpses' | 'movies' | 'photos'}
                  hasNextPage={false} // Don't show "Show More" button in category view
                  isGridView={true} // Use grid layout for category view
                />
                
                {/* Loading more indicator */}
                {loadingMore && (
                  <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-600"></div>
                    <span className="ml-3 text-gray-600">Loading more...</span>
                  </div>
                )}
                
                {/* End of results indicator */}
                {!hasNextPage && allResults.length > 0 && (
                  <div className="text-center py-8">
                    <p className="text-gray-500">You've reached the end of the results</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

export default function CategorySearchPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CategorySearchContent />
    </Suspense>
  );
}
