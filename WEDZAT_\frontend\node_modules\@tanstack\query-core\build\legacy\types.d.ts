export { P as AnyDataTag, b5 as CancelOptions, T as DataTag, F as DefaultError, b4 as DefaultOptions, aj as DefaultedInfiniteQueryObserverOptions, ah as DefaultedQueryObserverOptions, aO as DefinedInfiniteQueryObserverResult, aF as DefinedQueryObserverResult, A as DistributiveOmit, _ as Enabled, am as EnsureInfiniteQueryDataOptions, al as EnsureQueryDataOptions, an as FetchInfiniteQueryOptions, au as FetchNextPageOptions, av as FetchPreviousPageOptions, ak as FetchQueryOptions, ax as FetchStatus, a6 as GetNextPageParamFunction, a5 as GetPreviousPageParamFunction, V as InferDataFromTag, W as InferErrorFromTag, a7 as InfiniteData, aH as InfiniteQueryObserverBaseResult, aK as InfiniteQueryObserverLoadingErrorResult, aJ as InfiniteQueryObserverLoadingResult, ai as InfiniteQueryObserverOptions, aI as InfiniteQueryObserverPendingResult, aN as InfiniteQueryObserverPlaceholderResult, aL as InfiniteQueryObserverRefetchErrorResult, aP as InfiniteQueryObserverResult, aM as InfiniteQueryObserverSuccessResult, ad as InfiniteQueryPageParamsOptions, a1 as InitialDataFunction, ac as InitialPageParam, as as InvalidateOptions, aq as InvalidateQueryFilters, aY as MutateFunction, aX as MutateOptions, aU as MutationFunction, aQ as MutationKey, aT as MutationMeta, aZ as MutationObserverBaseResult, b0 as MutationObserverErrorResult, a_ as MutationObserverIdleResult, a$ as MutationObserverLoadingResult, aW as MutationObserverOptions, b2 as MutationObserverResult, b1 as MutationObserverSuccessResult, aV as MutationOptions, aS as MutationScope, aR as MutationStatus, a9 as NetworkMode, E as NoInfer, N as NonUndefinedGuard, b8 as NotifyEvent, b7 as NotifyEventType, aa as NotifyOnChangeProps, O as OmitKeyof, B as Override, a2 as PlaceholderDataFunction, a3 as QueriesPlaceholderDataFunction, b3 as QueryClientConfig, X as QueryFunction, a0 as QueryFunctionContext, G as QueryKey, a4 as QueryKeyHashFunction, a8 as QueryMeta, ay as QueryObserverBaseResult, aB as QueryObserverLoadingErrorResult, aA as QueryObserverLoadingResult, af as QueryObserverOptions, az as QueryObserverPendingResult, aE as QueryObserverPlaceholderResult, aC as QueryObserverRefetchErrorResult, aG as QueryObserverResult, aD as QueryObserverSuccessResult, ab as QueryOptions, $ as QueryPersister, aw as QueryStatus, ap as RefetchOptions, ar as RefetchQueryFilters, R as Register, at as ResetOptions, ao as ResultOptions, b6 as SetDataOptions, Y as StaleTime, Z as StaleTimeFunction, ae as ThrowOnError, L as UnsetMarker, ag as WithRequired, J as dataTagErrorSymbol, I as dataTagSymbol, K as unsetMarker } from './hydration-DYrnn9Jo.js';
import './removable.js';
import './subscribable.js';
