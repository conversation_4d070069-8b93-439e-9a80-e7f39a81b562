@import "tailwindcss";
@import "./custom-breakpoints.css";

:root {
  --background: #f3f0f0;
  --foreground: #000000;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #000000;
  }
}

body {
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

/* Global scrollbar hiding for all elements */
* {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Hide webkit scrollbars */
*::-webkit-scrollbar {
  display: none;
}

/* Ensure smooth scrolling behavior */
html,
body {
  -webkit-overflow-scrolling: touch;
}

/* Remove the previous scrollbar styles okay*/
main::-webkit-scrollbar,
main::-webkit-scrollbar-track,
main::-webkit-scrollbar-thumb {
  display: none;
}

main {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
