import os
import psycopg2
from dotenv import load_dotenv
from Wedzat.codebase.wedding_tables import create_wedding_tables, get_db_connection

# Create the wedding tables (including default budget categories)
print("Creating wedding tables...")
create_wedding_tables()

# Connect to the database to check the results
print("\nChecking default budget categories...")
conn = get_db_connection()
cursor = conn.cursor()

# Get all default budget categories
cursor.execute(
    '''SELECT * FROM wedding_budget_categories
    WHERE user_id IS NULL
    ORDER BY name ASC'''
)
categories = cursor.fetchall()

print(f"\nFound {len(categories)} default budget categories:")
for category in categories:
    print(f"- {category[2]}")  # category name is at index 2

    # Get expenses for this category
    cursor.execute(
        '''SELECT * FROM wedding_budget_expenses
        WHERE category_id = %s AND user_id IS NULL
        ORDER BY name ASC''',
        (category[0],)  # category_id is at index 0
    )
    expenses = cursor.fetchall()
    
    print(f"  Expenses ({len(expenses)}):")
    for expense in expenses:
        print(f"  - {expense[3]}")  # expense name is at index 3

# Close the connection
conn.close()
