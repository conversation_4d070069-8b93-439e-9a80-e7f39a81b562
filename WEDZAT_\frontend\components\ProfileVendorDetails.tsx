'use client';

import React, { useState, useEffect } from 'react';
import axios from '../services/axiosConfig';
import VendorDetailsVerification from './VendorDetailsVerification';

interface VendorDetail {
  name: string;
  mobileNumber: string;
}

interface ProfileVendorDetailsProps {
  userId: string;
}

const ProfileVendorDetails: React.FC<ProfileVendorDetailsProps> = ({ userId }) => {
  const [vendorDetails, setVendorDetails] = useState<Record<string, VendorDetail>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendorDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get token from localStorage - try multiple possible keys
        const tokenKey = localStorage.getItem('token') ? 'token' :
                        localStorage.getItem('jwt_token') ? 'jwt_token' :
                        localStorage.getItem('auth_token') ? 'auth_token' : null;

        const token = tokenKey ? localStorage.getItem(tokenKey) : null;

        if (!token) {
          console.warn('No authentication token found in any storage key');
          setError('Authentication required');
          return;
        }

        // Fetch vendor details from API
        const response = await axios.get(`/users/${userId}/vendor-details`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.vendorDetails) {
          setVendorDetails(response.data.vendorDetails);
        } else {
          // If no vendor details found, use mock data for development
          if (process.env.NODE_ENV === 'development') {
            const mockVendorDetails = {
              venue: { name: 'Taj Palace Hotel', mobileNumber: '9876543210' },
              photographer: { name: 'Pixel Perfect Studios', mobileNumber: '8765432109' },
              makeupArtist: { name: 'Glamour Touch', mobileNumber: '7654321098' },
              decorations: { name: 'Dream Decors', mobileNumber: '6543210987' },
              caterer: { name: 'Royal Feast Caterers', mobileNumber: '5432109876' }
            };
            setVendorDetails(mockVendorDetails);
          } else {
            setError('No vendor details found');
          }
        }
      } catch (err) {
        console.error('Error fetching vendor details:', err);
        setError('Failed to load vendor details');

        // Use mock data for development
        if (process.env.NODE_ENV === 'development') {
          const mockVendorDetails = {
            venue: { name: 'Taj Palace Hotel', mobileNumber: '9876543210' },
            photographer: { name: 'Pixel Perfect Studios', mobileNumber: '8765432109' },
            makeupArtist: { name: 'Glamour Touch', mobileNumber: '7654321098' },
            decorations: { name: 'Dream Decors', mobileNumber: '6543210987' },
            caterer: { name: 'Royal Feast Caterers', mobileNumber: '5432109876' }
          };
          setVendorDetails(mockVendorDetails);
        }
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchVendorDetails();
    }
  }, [userId]);

  if (loading) {
    return null; // Don't show loading state
  }

  if (error) {
    return null; // Don't show error message
  }

  if (Object.keys(vendorDetails).length === 0) {
    return null; // Don't show empty state
  }

  return <VendorDetailsVerification vendorDetails={vendorDetails} />;
};

export default ProfileVendorDetails;
