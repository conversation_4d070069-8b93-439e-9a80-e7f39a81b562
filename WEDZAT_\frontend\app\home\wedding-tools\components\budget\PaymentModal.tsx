"use client";
import React, { useState } from "react";
import { X, Plus } from "lucide-react";
import { BudgetExpense } from "../../types";
import Image from "next/image";

interface PaymentModalProps {
  expenses: BudgetExpense[];
  selectedExpenseId?: string; // Optional selected expense ID
  onClose: () => void;
  onSave: (paymentData: {
    expense_id: string;
    amount: number;
    payment_date: string;
    payment_method: string;
    notes: string;
    paid_by: string;
  }) => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({ expenses, selectedExpenseId, onClose, onSave }) => {
  const [paymentData, setPaymentData] = useState({
    expense_id: selectedExpenseId || "",
    amount: "0",
    payment_date: new Date().toISOString().split('T')[0],
    payment_method: "",
    notes: "",
    paid_by: ""
  });

  const [isPaid, setIsPaid] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPaymentData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      expense_id: paymentData.expense_id,
      amount: parseFloat(paymentData.amount) || 0,
      payment_date: paymentData.payment_date,
      payment_method: paymentData.payment_method,
      notes: paymentData.notes,
      paid_by: paymentData.paid_by
    });
  };

  return (
    <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
      <div
        className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
        style={{
          background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
        }}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
        >
          <X size={20} />
        </button>

        {/* Logo */}
        <div className="flex justify-center pt-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        <div className="px-6 py-4">
          <h3 className="text-2xl font-bold mb-2 text-center" style={{ color: "#B31B1E" }}>
            Add Payment
          </h3>
          <p className="text-sm text-center text-gray-600 mb-6">
            {expenses.find(e => e.expense_id === paymentData.expense_id)?.name || 'Select an expense'}
          </p>

        <form onSubmit={handleSubmit}>
          {/* Expense Selection - only show if no expense is pre-selected */}
          {!selectedExpenseId && (
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="expense_id">
                EXPENSE
              </label>
              <select
                id="expense_id"
                name="expense_id"
                value={paymentData.expense_id}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
                required
              >
                <option value="">Select an expense</option>
                {expenses.map(expense => (
                  <option key={expense.expense_id} value={expense.expense_id}>
                    {expense.name} ({expense.category_name})
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="amount">
              AMOUNT
            </label>
            <input
              type="number"
              id="amount"
              name="amount"
              value={paymentData.amount}
              onChange={handleChange}
              placeholder="0"
              min="0"
              step="0.01"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
              required
            />
          </div>

          <div className="mb-4 flex items-center">
            <input
              type="checkbox"
              id="isPaid"
              checked={isPaid}
              onChange={() => setIsPaid(!isPaid)}
              className="mr-2 h-5 w-5"
            />
            <label htmlFor="isPaid" className="text-gray-700">
              Paid
            </label>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="payment_date">
                DATE OF PAYMENT
              </label>
              <input
                type="date"
                id="payment_date"
                name="payment_date"
                value={paymentData.payment_date}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="payment_due_by">
                PAYMENT DUE BY
              </label>
              <input
                type="date"
                id="payment_due_by"
                name="payment_due_by"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="paid_by">
              PAID BY (NAME)
            </label>
            <input
              type="text"
              id="paid_by"
              name="paid_by"
              value={paymentData.paid_by}
              onChange={handleChange}
              placeholder="Username"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
            />
          </div>

          <div className="mb-4">
            <div className="flex justify-between items-center">
              <label className="block text-gray-700 text-sm font-bold" htmlFor="payment_method">
                Payment method
              </label>
              <button
                type="button"
                className="text-red-500 flex items-center text-sm"
                onClick={() => console.log('Add payment method')}
              >
                <Plus size={16} className="mr-1" />
                Payment method
              </button>
            </div>
            <input
              type="text"
              id="payment_method"
              name="payment_method"
              value={paymentData.payment_method}
              onChange={handleChange}
              placeholder="Cash, Card, etc."
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-black mt-2"
            />
          </div>

          <div className="flex justify-center">
            <button
              type="submit"
              className="w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6"
            >
              Save
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
