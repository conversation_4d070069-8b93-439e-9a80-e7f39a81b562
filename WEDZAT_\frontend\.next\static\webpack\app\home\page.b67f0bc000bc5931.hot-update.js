"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./components/HomeDashboard/WeddingVideos.tsx":
/*!****************************************************!*\
  !*** ./components/HomeDashboard/WeddingVideos.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var _barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MdChevronLeft,MdChevronRight,MdFavorite,MdFavoriteBorder,MdOutlineModeComment,MdOutlineShare!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst WeddingVideosSection = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [likedVideos, setLikedVideos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [admiringUsers, setAdmiringUsers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [weddingVideos, setWeddingVideos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Fallback data if API fails - using empty array\n    const getFallbackMovies = ()=>[];\n    // Function to fetch movies from the API\n    const fetchMovies = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching movies for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setWeddingVideos([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/movies?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.movies) {\n                console.log(\"Loaded \".concat(response.data.movies.length, \" movies for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.movies.length > 0) {\n                    console.log('Sample movie data:', response.data.movies[0]);\n                }\n                // Process the response\n                const processedMovies = response.data.movies.map((movie)=>{\n                    if (!movie.video_thumbnail) {\n                        console.log(\"Movie missing thumbnail: \".concat(movie.video_id));\n                    }\n                    // Ensure user_id is set - in case the API doesn't provide it\n                    if (!movie.user_id && movie.user_name) {\n                        // Create a temporary user_id based on username if not provided\n                        // This is a fallback and should be replaced with actual user IDs from the API\n                        console.log(\"Movie missing user_id, creating temporary one from username: \".concat(movie.user_name));\n                        movie.user_id = \"user-\".concat(movie.user_name.toLowerCase().replace(/\\s+/g, '-'));\n                    }\n                    return movie;\n                });\n                if (pageNumber === 1) {\n                    setWeddingVideos(processedMovies);\n                } else {\n                    setWeddingVideos((prev)=>[\n                            ...prev,\n                            ...processedMovies\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load wedding videos - unexpected response format');\n                setWeddingVideos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load wedding videos');\n            setWeddingVideos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of movies as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Wedding Videos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial movies load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchMovies function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for movies page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/movies?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"WeddingVideosSection.useEffect\": (response)=>{\n                            console.log('API response received for movies page 1');\n                            if (response.data && response.data.movies) {\n                                setWeddingVideos(response.data.movies);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setWeddingVideos([]);\n                                setError('No wedding videos found');\n                            }\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]).catch({\n                        \"WeddingVideosSection.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load wedding videos');\n                            setWeddingVideos([]);\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]).finally({\n                        \"WeddingVideosSection.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Failsafe to ensure content is loaded - only show error after timeout\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // If we're stuck in loading state for more than 10 seconds, show error\n            let timeoutId = null;\n            if (loading && initialLoadComplete) {\n                timeoutId = setTimeout({\n                    \"WeddingVideosSection.useEffect\": ()=>{\n                        console.log('Loading timeout reached - API request may have failed');\n                        setLoading(false);\n                        if (weddingVideos.length === 0) {\n                            setError('Unable to load wedding videos. Please check your network connection.');\n                        }\n                    }\n                }[\"WeddingVideosSection.useEffect\"], 10000); // 10 second timeout for slow networks\n            }\n            return ({\n                \"WeddingVideosSection.useEffect\": ()=>{\n                    if (timeoutId) clearTimeout(timeoutId);\n                }\n            })[\"WeddingVideosSection.useEffect\"];\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        loading,\n        initialLoadComplete,\n        weddingVideos.length\n    ]);\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"WeddingVideosSection.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchMovies(nextPage, false);\n                    }\n                }\n            }[\"WeddingVideosSection.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"WeddingVideosSection.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"WeddingVideosSection.useEffect\"];\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Load admiring status from localStorage when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers');\n                if (admiredUsersJson) {\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    setAdmiringUsers(admiredUsers);\n                    console.log('Loaded admiring status from localStorage:', admiredUsers);\n                }\n            } catch (error) {\n                console.error('Error loading admiring status from localStorage:', error);\n            }\n        }\n    }[\"WeddingVideosSection.useEffect\"], []);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username)=>{\n        // If we have a userId, make a direct API call to the user profile endpoint\n        if (userId) {\n            // Get the token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Navigate to the profile page with the userId\n            router.push(\"/profile/\".concat(userId));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    const toggleLike = async (videoId)=>{\n        try {\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedVideos.includes(videoId);\n            setLikedVideos((prev)=>isCurrentlyLiked ? prev.filter((id)=>id !== videoId) : [\n                    ...prev,\n                    videoId\n                ]);\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: videoId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" wedding video: \").concat(videoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            const wasLiked = likedVideos.includes(videoId);\n            setLikedVideos((prev)=>wasLiked ? prev.filter((id)=>id !== videoId) : [\n                    ...prev,\n                    videoId\n                ]);\n        }\n    };\n    // Function to handle Admire button click\n    const handleAdmire = async (userId)=>{\n        if (!userId) {\n            console.warn('No user ID provided for Admire action');\n            return;\n        }\n        // Check if already admiring this user\n        const isCurrentlyAdmiring = admiringUsers[userId] || false;\n        try {\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI state\n            setAdmiringUsers((prev)=>({\n                    ...prev,\n                    [userId]: !isCurrentlyAdmiring\n                }));\n            // Make API call to follow/unfollow user\n            const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n            console.log(\"Making request to \".concat(endpoint, \" with user ID: \").concat(userId));\n            try {\n                // Make the API call\n                const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(endpoint, {\n                    target_user_id: userId\n                }, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('API response:', response.data);\n                // Update localStorage with the new state\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (!isCurrentlyAdmiring) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                    console.log('Updated admired users in localStorage:', admiredUsers);\n                } catch (storageError) {\n                    console.error('Error updating localStorage:', storageError);\n                }\n            } catch (apiError) {\n                console.error(\"Error \".concat(isCurrentlyAdmiring ? 'unadmiring' : 'admiring', \" user:\"), apiError);\n                // Revert UI state on error\n                setAdmiringUsers((prev)=>({\n                        ...prev,\n                        [userId]: isCurrentlyAdmiring\n                    }));\n            }\n        } catch (error) {\n            console.error('Unexpected error in handleAdmire:', error);\n            // Revert UI state on unexpected errors\n            setAdmiringUsers((prev)=>({\n                    ...prev,\n                    [userId]: isCurrentlyAdmiring\n                }));\n        }\n    };\n    const handleManualScroll = (direction)=>{\n        if (scrollContainerRef.current) {\n            const scrollAmount = direction === \"left\" ? -694 : 694; // Width + gap\n            scrollContainerRef.current.scrollBy({\n                left: scrollAmount,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Get appropriate image source for a movie\n    const getImageSource = (video)=>{\n        if (video.video_thumbnail) {\n            return video.video_thumbnail;\n        }\n        return '/pics/placeholder.svg';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-[#000000]\",\n                        children: \"MOVIES\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading wedding videos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 478,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying wedding videos load...');\n                            setError(null);\n                            fetchMovies(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 486,\n                columnNumber: 9\n            }, undefined),\n            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    weddingVideos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No wedding videos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 13\n                    }, undefined),\n                    weddingVideos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleManualScroll(\"left\"),\n                                style: {\n                                    marginLeft: \"-12px\"\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdChevronLeft, {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: scrollContainerRef,\n                                style: {\n                                    WebkitOverflowScrolling: \"touch\",\n                                    scrollbarWidth: \"none\",\n                                    msOverflowStyle: \"none\",\n                                    scrollBehavior: 'smooth'\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-3 pb-5 flex-nowrap w-full\",\n                                    children: [\n                                        weddingVideos.map((video, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px] max-w-[676px] border-b border-[#DBDBDB] pb-[15px] sm:pb-[21px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-full h-[40px] sm:h-[50px] md:h-[56px] flex justify-between items-center p-[5px] sm:p-[8px_5px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center gap-[10px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        onClick: ()=>navigateToUserProfile(video.user_id, video.user_name),\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-[34px] h-[34px] rounded-[27px] border cursor-pointer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            username: video.user_name || \"user\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>navigateToUserProfile(video.user_id, video.user_name)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 555,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-full max-w-[380px] h-[40px] pt-[7px] pr-[50px] sm:pr-[100px] md:pr-[150px] lg:pr-[200px] pb-[10px]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontFamily: \"Inter\",\n                                                                                fontWeight: 600,\n                                                                                fontSize: \"14px\",\n                                                                                lineHeight: \"18px\",\n                                                                                letterSpacing: \"0%\",\n                                                                                verticalAlign: \"middle\",\n                                                                                cursor: \"pointer\"\n                                                                            },\n                                                                            onClick: ()=>navigateToUserProfile(video.user_id, video.user_name),\n                                                                            className: \"jsx-83037452c623c470\" + \" \" + \"hover:underline\",\n                                                                            children: video.user_name || \"user\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleAdmire(video.user_id),\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center bg-[#B31B1E] text-white px-3 py-1 rounded-full text-sm font-medium hover:bg-red-700 transition-colors\",\n                                                                        children: [\n                                                                            admiringUsers[video.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[video.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-83037452c623c470\" + \" \" + \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 69\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: \"•••\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[180px] sm:h-[250px] md:h-[300px] lg:h-[350px] max-w-[676px] rounded-[10px] border p-[1px] relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full rounded-[10px] overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: getImageSource(video),\n                                                                    alt: video.video_name || \"Wedding Video\",\n                                                                    fill: true,\n                                                                    sizes: \"(max-width: 480px) 300px, (max-width: 640px) 350px, (max-width: 768px) 450px, (max-width: 1024px) 550px, 676px\",\n                                                                    className: \"object-cover\",\n                                                                    ...index < 2 ? {\n                                                                        priority: true\n                                                                    } : {\n                                                                        loading: 'lazy'\n                                                                    },\n                                                                    unoptimized: true,\n                                                                    placeholder: \"blur\" // Show blur placeholder while loading\n                                                                    ,\n                                                                    blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                                    ,\n                                                                    onError: (e)=>{\n                                                                        console.error(\"Failed to load image for video: \".concat(video.video_name));\n                                                                        // Use placeholder as fallback\n                                                                        const imgElement = e.target;\n                                                                        if (imgElement) {\n                                                                            imgElement.src = '/pics/placeholder.svg';\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: \"absolute\",\n                                                                    top: 0,\n                                                                    left: 0,\n                                                                    right: 0,\n                                                                    bottom: 0,\n                                                                    zIndex: 2,\n                                                                    borderRadius: \"10px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"center\",\n                                                                    cursor: \"pointer\",\n                                                                    backgroundColor: \"rgba(0, 0, 0, 0.2)\"\n                                                                },\n                                                                onClick: ()=>{\n                                                                    // Navigate to the movie detail page\n                                                                    if (video.video_id) {\n                                                                        console.log(\"Navigating to movie detail page: \".concat(video.video_id));\n                                                                        window.location.href = \"/home/<USER>/\".concat(video.video_id);\n                                                                    }\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-83037452c623c470\" + \" \" + \"w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"28\",\n                                                                        height: \"28\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"white\",\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M8 5v14l11-7z\",\n                                                                            className: \"jsx-83037452c623c470\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            padding: \"12px 0\",\n                                                            display: \"flex\",\n                                                            flexDirection: \"column\",\n                                                            gap: \"8px\"\n                                                        },\n                                                        className: \"jsx-83037452c623c470\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    gap: \"16px\",\n                                                                    alignItems: \"center\"\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>toggleLike(video.video_id),\n                                                                        title: likedVideos.includes(video.video_id) ? 'Unlike' : 'Like',\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 \".concat(likedVideos.includes(video.video_id) ? 'bg-red-100 hover:bg-red-200' : 'bg-gray-100 hover:bg-gray-200'),\n                                                                        children: likedVideos.includes(video.video_id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdFavorite, {\n                                                                            size: 24,\n                                                                            color: \"#B31B1E\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 31\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdFavoriteBorder, {\n                                                                            size: 24,\n                                                                            color: \"#666\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdOutlineModeComment, {\n                                                                            size: 24,\n                                                                            color: \"#666\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 696,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdOutlineShare, {\n                                                                            size: 24,\n                                                                            color: \"#666\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 698,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    gap: \"16px\",\n                                                                    fontSize: \"14px\",\n                                                                    color: \"#666\"\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: likedVideos.includes(video.video_id) ? \"#B31B1E\" : \"#666\"\n                                                                        },\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: [\n                                                                            video.video_likes ? \"\".concat((video.video_likes / 1000).toFixed(1), \"K\") : '0',\n                                                                            \" likes\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: [\n                                                                            video.video_views ? \"\".concat((video.video_views / 1000).toFixed(1), \"K\") : '0',\n                                                                            \" views\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"14px\",\n                                                                    display: \"flex\",\n                                                                    gap: \"4px\"\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-83037452c623c470\",\n                                                                    children: video.video_description || video.video_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, \"\".concat(video.video_id, \"-\").concat(index), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 21\n                                            }, undefined)),\n                                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: loadMoreTriggerRef,\n                                            style: {\n                                                position: 'relative',\n                                                // Add debug outline in development\n                                                outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                            },\n                                            \"aria-hidden\": \"true\",\n                                            \"data-testid\": \"wedding-videos-load-more-trigger\",\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                                    children: \"Loading more...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleManualScroll(\"right\"),\n                                style: {\n                                    marginRight: \"-12px\"\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdChevronRight, {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 762,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n        lineNumber: 463,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WeddingVideosSection, \"Xsa2w61/PqrUJlvDwsTfmOHtabU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = WeddingVideosSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WeddingVideosSection);\nvar _c;\n$RefreshReg$(_c, \"WeddingVideosSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/WeddingVideos.tsx\n"));

/***/ })

});