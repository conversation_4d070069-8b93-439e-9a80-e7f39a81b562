"use client";
import React, { useState, useEffect } from "react";
import {
  TopNavigation,
  SideNavigation,
} from "../../../components/HomeDashboard/Navigation";
import Image from "next/image";
import { Calendar, Clock, MapPin, Users, Video } from "lucide-react";

export default function WeddingLivePage() {
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [upcomingLiveEvents, setUpcomingLiveEvents] = useState([
    {
      id: 1,
      title: "<PERSON> <PERSON> <PERSON><PERSON>'s Wedding",
      date: "2023-12-15",
      time: "11:00 AM",
      location: "Grand Hyatt, Mumbai",
      thumbnail: "/pics/wedding1.jpg",
      attendees: 245,
    },
    {
      id: 2,
      title: "<PERSON><PERSON><PERSON> & <PERSON><PERSON>'s Reception",
      date: "2023-12-18",
      time: "7:00 PM",
      location: "Taj Palace, Delhi",
      thumbnail: "/pics/wedding2.jpg",
      attendees: 320,
    },
    {
      id: 3,
      title: "<PERSON><PERSON><PERSON> & <PERSON><PERSON>'s Engagement",
      date: "2023-12-22",
      time: "6:30 PM",
      location: "ITC Gardenia, Bangalore",
      thumbnail: "/pics/wedding3.jpg",
      attendees: 180,
    },
  ]);

  const [liveBroadcasts, setLiveBroadcasts] = useState([
    {
      id: 1,
      title: "Anand & Sunita's Wedding",
      viewers: 1245,
      thumbnail: "/pics/live1.jpg",
    },
    {
      id: 2,
      title: "Karan & Deepika's Sangeet",
      viewers: 876,
      thumbnail: "/pics/live2.jpg",
    },
  ]);

  const [pastEvents, setPastEvents] = useState([
    {
      id: 1,
      title: "Rahul & Anjali's Wedding",
      date: "2023-11-28",
      views: 4567,
      thumbnail: "/pics/past1.jpg",
    },
    {
      id: 2,
      title: "Siddharth & Kiara's Reception",
      date: "2023-11-20",
      views: 3298,
      thumbnail: "/pics/past2.jpg",
    },
    {
      id: 3,
      title: "Varun & Alia's Mehendi",
      date: "2023-11-15",
      views: 2876,
      thumbnail: "/pics/past3.jpg",
    },
    {
      id: 4,
      title: "Ranbir & Shraddha's Haldi",
      date: "2023-11-10",
      views: 3421,
      thumbnail: "/pics/past4.jpg",
    },
  ]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      <div className="flex">
        <SideNavigation
          expanded={sidebarExpanded}
          setExpanded={setSidebarExpanded}
          activePage="Wedzat Live"
        />
        <main
          className={`flex-1 p-4 md:p-6 transition-all duration-300 ${
            sidebarExpanded ? "md:ml-64" : "md:ml-24"
          }`}
        >
          <div className="max-w-7xl mx-auto">
            <h1 className="text-3xl font-bold text-gray-800 mb-6">
              Wedding LIVE
            </h1>

            {/* Hero Section */}
            <div className="relative w-full h-64 md:h-80 rounded-xl overflow-hidden mb-8">
              <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-red-800 opacity-90"></div>
              <div className="absolute inset-0 flex flex-col justify-center items-center text-white p-6 text-center">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  Stream Your Wedding Live
                </h2>
                <p className="text-lg md:text-xl mb-6 max-w-2xl">
                  Share your special moments with friends and family who couldn't
                  make it in person.
                </p>
                <button className="bg-white text-red-600 font-semibold px-6 py-3 rounded-lg shadow-lg hover:bg-gray-100 transition-colors">
                  Start a Live Stream
                </button>
              </div>
            </div>

            {/* Live Now Section */}
            {liveBroadcasts.length > 0 && (
              <section className="mb-10">
                <div className="flex items-center mb-4">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse mr-2"></div>
                  <h2 className="text-2xl font-bold text-gray-800">Live Now</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {liveBroadcasts.map((broadcast) => (
                    <div
                      key={broadcast.id}
                      className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                    >
                      <div className="relative h-48">
                        <div className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 rounded-lg text-sm font-semibold flex items-center">
                          <div className="w-2 h-2 bg-white rounded-full animate-pulse mr-1"></div>
                          LIVE
                        </div>
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded-lg text-sm font-semibold flex items-center">
                          <Users size={14} className="mr-1" />
                          {broadcast.viewers.toLocaleString()}
                        </div>
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <Video size={48} className="text-gray-400" />
                        </div>
                      </div>
                      <div className="p-4">
                        <h3 className="font-semibold text-lg mb-2">
                          {broadcast.title}
                        </h3>
                        <button className="w-full bg-red-600 text-white font-semibold py-2 rounded-lg hover:bg-red-700 transition-colors">
                          Join Stream
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Upcoming Events Section */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                Upcoming Live Events
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {upcomingLiveEvents.map((event) => (
                  <div
                    key={event.id}
                    className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                  >
                    <div className="relative h-48">
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <Calendar size={48} className="text-gray-400" />
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-lg mb-2">
                        {event.title}
                      </h3>
                      <div className="flex items-center text-gray-600 mb-1">
                        <Calendar size={16} className="mr-2" />
                        <span>{new Date(event.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center text-gray-600 mb-1">
                        <Clock size={16} className="mr-2" />
                        <span>{event.time}</span>
                      </div>
                      <div className="flex items-center text-gray-600 mb-3">
                        <MapPin size={16} className="mr-2" />
                        <span>{event.location}</span>
                      </div>
                      <button className="w-full bg-red-600 text-white font-semibold py-2 rounded-lg hover:bg-red-700 transition-colors">
                        Set Reminder
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Past Events Section */}
            <section>
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                Past Live Events
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {pastEvents.map((event) => (
                  <div
                    key={event.id}
                    className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                  >
                    <div className="relative h-40">
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <Video size={36} className="text-gray-400" />
                      </div>
                      <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded-lg text-xs font-semibold flex items-center">
                        <Users size={12} className="mr-1" />
                        {event.views.toLocaleString()}
                      </div>
                    </div>
                    <div className="p-3">
                      <h3 className="font-semibold text-md mb-1">
                        {event.title}
                      </h3>
                      <div className="flex items-center text-gray-600 text-sm">
                        <Calendar size={12} className="mr-1" />
                        <span>{new Date(event.date).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>
  );
}
