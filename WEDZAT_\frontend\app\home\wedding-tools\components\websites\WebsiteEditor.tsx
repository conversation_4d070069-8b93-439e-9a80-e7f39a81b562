"use client";
import React, { useState, useEffect, useRef } from "react";
import { ArrowLeft, Save, Loader2, Upload, Image as ImageIcon, Search } from "lucide-react";
import { ChromePicker } from "react-color";
import axios from "axios";

interface Template {
  template_id: string;
  name: string;
  thumbnail_url: string;
  description: string;
  default_colors: any;
  default_fonts: any;
}

interface Website {
  website_id: string;
  title: string;
  couple_names?: string;
  template_id: string;
  wedding_date: string;
  wedding_location: string;
  about_couple: string;
  design_settings: {
    colors?: {
      primary?: string;
      secondary?: string;
      background?: string;
      text?: string;
      [key: string]: string | undefined;
    };
    fonts?: {
      heading?: string;
      body?: string;
      coupleNames?: string;
      date?: string;
      location?: string;
      aboutCouple?: string;
      [key: string]: string | undefined;
    };
    customImage?: string;
  };
  deployed_url: string;
  is_published: boolean;
}

interface WebsiteEditorProps {
  templates: Template[];
  website: Website | null;
  onSave: (websiteData: any) => void;
  onCancel: () => void;
  loading: boolean;
}

const WebsiteEditor: React.FC<WebsiteEditorProps> = ({
  templates,
  website,
  onSave,
  onCancel,
  loading
}) => {
  const [title, setTitle] = useState(website?.title || "Our Wedding");
  // No need for template selection anymore
  const [weddingDate, setWeddingDate] = useState(website?.wedding_date || "");
  const [weddingLocation, setWeddingLocation] = useState(website?.wedding_location || "");
  const [aboutCouple, setAboutCouple] = useState(website?.about_couple || "");
  const [coupleNames, setCoupleNames] = useState(website?.couple_names || "");
  const [designSettings, setDesignSettings] = useState(website?.design_settings || {
    colors: {
      primary: "#B31B1E",
      secondary: "#333333",
      background: "#FFFFFF",
      text: "#000000"
    },
    fonts: {
      heading: "Playfair Display",
      body: "Roboto",
      coupleNames: "Playfair Display",
      date: "Roboto",
      location: "Roboto",
      aboutCouple: "Roboto"
    },
    customImage: website?.design_settings?.customImage || ""
  });

  const [showColorPicker, setShowColorPicker] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searching, setSearching] = useState(false);
  const [showImageSearch, setShowImageSearch] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // No need for template-related effects anymore

  // We'll handle template image in the handleSave function instead

  const handleColorChange = (color: any, colorKey: string) => {
    setDesignSettings({
      ...designSettings,
      colors: {
        ...designSettings.colors,
        [colorKey]: color.hex
      }
    });
  };

  const handleFontChange = (e: React.ChangeEvent<HTMLSelectElement>, fontKey: string) => {
    // Get the selected font name
    const selectedFontName = e.target.value;

    // Find the selected font object from availableFonts
    const selectedFont = availableFonts.find(font => font.name === selectedFontName);

    // If we found the font, use its name
    const fontValue = selectedFont ? selectedFont.name : selectedFontName;

    // Update the design settings with the selected font
    setDesignSettings({
      ...designSettings,
      fonts: {
        ...designSettings.fonts,
        [fontKey]: fontValue
      }
    });

    console.log(`Font changed: ${fontKey} = ${fontValue}`);
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setUploadingImage(true);

      // For simplicity, let's use a direct image URL instead of uploading
      // This is a temporary solution until the upload API is fixed
      // Create a temporary URL for the uploaded file to show a preview
      // This allows the user to see their actual uploaded image
      const imageUrl = URL.createObjectURL(file);

      // In a real implementation, you would upload the file to a server
      // and get back a permanent URL

      // Update design settings with the new image URL
      setDesignSettings({
        ...designSettings,
        customImage: imageUrl
      });

      setUploadingImage(false);
    } catch (error) {
      console.error('Error handling image:', error);
      alert("Failed to process image. Please try again.");
      setUploadingImage(false);
    }
  };

  // Function to search for images using our proxy API
  const searchImages = async () => {
    if (!searchQuery.trim()) return;

    setSearching(true);
    setSearchResults([]);

    try {
      // Use our Next.js API route which generates Unsplash image URLs
      const response = await axios.get(
        `/api/pexels?query=${encodeURIComponent(searchQuery)}`
      );

      if (response.data && response.data.photos) {
        setSearchResults(response.data.photos);
      } else {
        setSearchResults([]);
      }
    } catch (error: any) {
      console.error('Error searching for images:', error);
      setSearchResults([]);
      // Show a message to the user
      alert('Could not load images. Please try a different search term.');
    } finally {
      setSearching(false);
    }
  };

  // Function to select an image from search results
  const selectImage = (imageUrl: string) => {
    setDesignSettings({
      ...designSettings,
      customImage: imageUrl
    });
    setShowImageSearch(false);
  };

  const handleSave = () => {
    // Ensure we have the required font fields with default values
    const processedFonts = {
      heading: designSettings.fonts?.heading || 'Playfair Display',
      body: designSettings.fonts?.body || 'Roboto',
      coupleNames: designSettings.fonts?.coupleNames || designSettings.fonts?.heading || 'Playfair Display',
      date: designSettings.fonts?.date || designSettings.fonts?.body || 'Roboto',
      location: designSettings.fonts?.location || designSettings.fonts?.body || 'Roboto',
      aboutCouple: designSettings.fonts?.aboutCouple || designSettings.fonts?.body || 'Roboto'
    };

    // Ensure we have the required color fields with default values
    const processedColors = {
      primary: designSettings.colors?.primary || '#B31B1E',
      secondary: designSettings.colors?.secondary || '#333333',
      background: designSettings.colors?.background || '#FFFFFF',
      text: designSettings.colors?.text || '#000000'
    };

    // Prepare the data, ensuring wedding_date is properly formatted
    const websiteData = {
      title,
      template_id: website?.template_id || templates[0]?.template_id, // Use existing template or first available
      wedding_date: weddingDate || null, // Ensure empty string is sent as null
      wedding_location: weddingLocation,
      about_couple: aboutCouple,
      couple_names: coupleNames,
      design_settings: {
        colors: processedColors,
        fonts: processedFonts,
        // Use only the custom image from upload or search
        customImage: designSettings.customImage || ''
      },
      is_published: true
    };

    console.log('Saving website with data:', websiteData);
    onSave(websiteData);
  };

  // Enhanced font list with categories
  const availableFonts = [
    // Elegant/Script fonts (good for headings and couple names)
    { name: "Dancing Script", category: "script", style: "cursive" },
    { name: "Great Vibes", category: "script", style: "cursive" },
    { name: "Parisienne", category: "script", style: "cursive" },
    { name: "Tangerine", category: "script", style: "cursive" },
    { name: "Alex Brush", category: "script", style: "cursive" },
    { name: "Allura", category: "script", style: "cursive" },

    // Serif fonts (elegant, traditional)
    { name: "Playfair Display", category: "serif", style: "serif" },
    { name: "Cormorant Garamond", category: "serif", style: "serif" },
    { name: "Lora", category: "serif", style: "serif" },
    { name: "Baskerville", category: "serif", style: "serif" },
    { name: "Libre Baskerville", category: "serif", style: "serif" },
    { name: "Crimson Text", category: "serif", style: "serif" },

    // Sans-serif fonts (modern, clean)
    { name: "Montserrat", category: "sans-serif", style: "sans-serif" },
    { name: "Roboto", category: "sans-serif", style: "sans-serif" },
    { name: "Open Sans", category: "sans-serif", style: "sans-serif" },
    { name: "Lato", category: "sans-serif", style: "sans-serif" },
    { name: "Poppins", category: "sans-serif", style: "sans-serif" },
    { name: "Raleway", category: "sans-serif", style: "sans-serif" },

    // Classic fonts
    { name: "Times New Roman", category: "classic", style: "serif" },
    { name: "Georgia", category: "classic", style: "serif" },
    { name: "Arial", category: "classic", style: "sans-serif" },
    { name: "Helvetica", category: "classic", style: "sans-serif" }
  ];

  // Load Google Fonts for the preview
  useEffect(() => {
    // Create a link element for Google Fonts
    const link = document.createElement('link');
    link.rel = 'stylesheet';

    // Get unique fonts from the available fonts list
    const fontNames = availableFonts.map(font => font.name.replace(/ /g, '+'));

    // Create the Google Fonts URL
    link.href = `https://fonts.googleapis.com/css2?family=${fontNames.join('&family=')}&display=swap`;

    // Add the link to the document head
    document.head.appendChild(link);

    // Clean up when component unmounts
    return () => {
      document.head.removeChild(link);
    };
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <button
            onClick={onCancel}
            className="p-2 text-gray-600 hover:text-[#B31B1E] hover:bg-gray-100 rounded-md transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <h2 className="text-2xl font-bold text-black">
            {website ? "Edit Website" : "Create New Website"}
          </h2>
        </div>
        <button
          onClick={handleSave}
          className="flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
          disabled={loading || !title}
        >
          {loading ? (
            <Loader2 size={18} className="animate-spin" />
          ) : (
            <Save size={18} />
          )}
          <span>Save Website</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Form */}
        <div>
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Couple Names
            </label>
            <input
              type="text"
              value={coupleNames}
              onChange={(e) => setCoupleNames(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
              placeholder="Jane & John"
            />
            <p className="text-xs text-gray-500 mt-1">Example: "Jane & John" or "Jane weds John"</p>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Website Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
              placeholder="Our Wedding"
              required
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Background Image
            </label>
            <div className="mt-1 flex items-center">
                {designSettings.customImage ? (
                  <div className="relative w-full h-32 bg-gray-100 rounded-md overflow-hidden">
                    <img
                      src={designSettings.customImage}
                      alt="Custom background"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // If image fails to load, show a placeholder
                        const target = e.target as HTMLImageElement;
                        target.onerror = null; // Prevent infinite loop
                        target.src = 'https://via.placeholder.com/800x400?text=Wedding+Background';
                        // Update the design settings with the placeholder
                        setDesignSettings({
                          ...designSettings,
                          customImage: 'https://via.placeholder.com/800x400?text=Wedding+Background'
                        });
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => setDesignSettings({...designSettings, customImage: ""})}
                      className="absolute top-2 right-2 bg-red-600 text-white p-1 rounded-full hover:bg-red-700"
                    >
                      ×
                    </button>
                  </div>
                ) : (
                  <div className="w-full">
                    <div className="flex gap-2 mb-3">
                      <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#B31B1E]"
                        disabled={uploadingImage}
                      >
                        {uploadingImage ? (
                          <>
                            <Loader2 className="animate-spin -ml-1 mr-2 h-5 w-5 text-gray-500" />
                            <span>Uploading...</span>
                          </>
                        ) : (
                          <>
                            <Upload className="-ml-1 mr-2 h-5 w-5 text-gray-500" />
                            <span>Upload Image</span>
                          </>
                        )}
                      </button>

                      <button
                        type="button"
                        onClick={() => {
                          const newState = !showImageSearch;
                          setShowImageSearch(newState);
                          // If opening the search and no results yet, set a default search
                          if (newState && searchResults.length === 0 && !searchQuery) {
                            setSearchQuery('wedding');
                            setTimeout(() => searchImages(), 100);
                          }
                        }}
                        className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#B31B1E]"
                      >
                        <Search className="-ml-1 mr-2 h-5 w-5 text-gray-500" />
                        <span>Search Images</span>
                      </button>
                    </div>

                    {showImageSearch && (
                      <div className="mb-4 border rounded-md p-3">
                        <div className="flex flex-col gap-2">
                          <div className="flex mb-1">
                            <input
                              type="text"
                              value={searchQuery}
                              onChange={(e) => setSearchQuery(e.target.value)}
                              placeholder="Search for wedding images..."
                              className="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                              onKeyDown={(e) => e.key === 'Enter' && searchImages()}
                            />
                            <button
                              onClick={searchImages}
                              disabled={searching || !searchQuery.trim()}
                              className="px-4 py-2 bg-[#B31B1E] text-white rounded-r-md hover:bg-red-700 transition-colors disabled:bg-gray-400"
                            >
                              {searching ? <Loader2 size={18} className="animate-spin" /> : 'Search'}
                            </button>
                          </div>
                          <div className="flex flex-wrap gap-1 text-xs">
                            <span className="text-gray-500">Try:</span>
                            {['wedding', 'beach wedding', 'wedding flowers', 'wedding venue', 'rustic wedding', 'elegant wedding'].map(term => (
                              <button
                                key={term}
                                onClick={() => {
                                  setSearchQuery(term);
                                  setTimeout(() => searchImages(), 100);
                                }}
                                className="px-2 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                              >
                                {term}
                              </button>
                            ))}
                          </div>
                        </div>

                        {searchResults.length > 0 && (
                          <div className="grid grid-cols-3 gap-2 max-h-60 overflow-y-auto">
                            {searchResults.map((photo: any) => (
                              <div
                                key={photo.id}
                                className="cursor-pointer border rounded-md overflow-hidden hover:ring-2 hover:ring-[#B31B1E] transition-all"
                                onClick={() => selectImage(photo.src.medium)}
                              >
                                <img
                                  src={photo.src.medium}
                                  alt={`Photo by ${photo.photographer}`}
                                  className="w-full h-24 object-cover"
                                  onError={(e) => {
                                    // If image fails to load, show a placeholder
                                    const target = e.target as HTMLImageElement;
                                    target.onerror = null; // Prevent infinite loop
                                    target.src = 'https://via.placeholder.com/300x200?text=Wedding+Image';
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        )}

                        {searching && (
                          <div className="flex justify-center items-center py-4">
                            <Loader2 size={24} className="animate-spin text-[#B31B1E]" />
                          </div>
                        )}

                        {!searching && searchResults.length === 0 && (
                          <p className="text-center text-gray-500 py-4">
                            {searchQuery.trim()
                              ? 'No images found. Try a different search term.'
                              : 'Enter a search term or click one of the suggestions above.'}
                          </p>
                        )}
                      </div>
                    )}

                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleImageUpload}
                      accept="image/*"
                      className="hidden"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Upload an image or search for beautiful wedding images online
                    </p>
                  </div>
                )}
              </div>
            </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Wedding Date
            </label>
            <input
              type="date"
              value={weddingDate}
              onChange={(e) => setWeddingDate(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Wedding Location
            </label>
            <input
              type="text"
              value={weddingLocation}
              onChange={(e) => setWeddingLocation(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
              placeholder="City, Country"
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              About the Couple
            </label>
            <textarea
              value={aboutCouple}
              onChange={(e) => setAboutCouple(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
              rows={4}
              placeholder="Tell your story..."
            />
          </div>
        </div>

        {/* Right Column - Design Settings */}
        <div>
          <h3 className="text-lg font-semibold mb-4 text-black">Design Settings</h3>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Colors
            </label>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(designSettings.colors || {}).map(([key, value]) => (
                <div key={key} className="mb-3">
                  <label className="block text-sm text-gray-600 capitalize mb-1">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </label>
                  <div className="flex items-center">
                    <div
                      className="w-10 h-10 rounded-md border border-gray-300 cursor-pointer mr-2"
                      style={{ backgroundColor: value as string }}
                      onClick={() => setShowColorPicker(showColorPicker === key ? null : key)}
                    />
                    <input
                      type="text"
                      value={value as string}
                      onChange={(e) => handleColorChange({ hex: e.target.value }, key)}
                      className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                    />
                  </div>
                  {showColorPicker === key && (
                    <div className="absolute z-10 mt-2">
                      <div
                        className="fixed inset-0"
                        onClick={() => setShowColorPicker(null)}
                      />
                      <ChromePicker
                        color={value as string}
                        onChange={(color) => handleColorChange(color, key)}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Fonts
            </label>
            <div className="grid grid-cols-1 gap-4">
              {/* Main Heading Font */}
              <div className="mb-3">
                <label className="block text-sm text-gray-600 mb-1">
                  Main Heading Font
                </label>
                <select
                  value={designSettings.fonts?.heading || "Playfair Display"}
                  onChange={(e) => handleFontChange(e, "heading")}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                >
                  <optgroup label="Script Fonts (Elegant)">
                    {availableFonts
                      .filter(font => font.category === "script")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Serif Fonts (Traditional)">
                    {availableFonts
                      .filter(font => font.category === "serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Sans-Serif Fonts (Modern)">
                    {availableFonts
                      .filter(font => font.category === "sans-serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Classic Fonts">
                    {availableFonts
                      .filter(font => font.category === "classic")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>

              {/* Couple Names Font */}
              <div className="mb-3">
                <label className="block text-sm text-gray-600 mb-1">
                  Couple Names Font
                </label>
                <select
                  value={designSettings.fonts?.coupleNames || designSettings.fonts?.heading || "Playfair Display"}
                  onChange={(e) => handleFontChange(e, "coupleNames")}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                >
                  <optgroup label="Script Fonts (Elegant)">
                    {availableFonts
                      .filter(font => font.category === "script")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Serif Fonts (Traditional)">
                    {availableFonts
                      .filter(font => font.category === "serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Sans-Serif Fonts (Modern)">
                    {availableFonts
                      .filter(font => font.category === "sans-serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Classic Fonts">
                    {availableFonts
                      .filter(font => font.category === "classic")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>

              {/* Date Font */}
              <div className="mb-3">
                <label className="block text-sm text-gray-600 mb-1">
                  Date Font
                </label>
                <select
                  value={designSettings.fonts?.date || designSettings.fonts?.body || "Roboto"}
                  onChange={(e) => handleFontChange(e, "date")}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                >
                  <optgroup label="Script Fonts (Elegant)">
                    {availableFonts
                      .filter(font => font.category === "script")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Serif Fonts (Traditional)">
                    {availableFonts
                      .filter(font => font.category === "serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Sans-Serif Fonts (Modern)">
                    {availableFonts
                      .filter(font => font.category === "sans-serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Classic Fonts">
                    {availableFonts
                      .filter(font => font.category === "classic")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>

              {/* Location Font */}
              <div className="mb-3">
                <label className="block text-sm text-gray-600 mb-1">
                  Location Font
                </label>
                <select
                  value={designSettings.fonts?.location || designSettings.fonts?.body || "Roboto"}
                  onChange={(e) => handleFontChange(e, "location")}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                >
                  <optgroup label="Script Fonts (Elegant)">
                    {availableFonts
                      .filter(font => font.category === "script")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Serif Fonts (Traditional)">
                    {availableFonts
                      .filter(font => font.category === "serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Sans-Serif Fonts (Modern)">
                    {availableFonts
                      .filter(font => font.category === "sans-serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Classic Fonts">
                    {availableFonts
                      .filter(font => font.category === "classic")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>

              {/* About Couple Font */}
              <div className="mb-3">
                <label className="block text-sm text-gray-600 mb-1">
                  About Couple Font
                </label>
                <select
                  value={designSettings.fonts?.aboutCouple || designSettings.fonts?.body || "Roboto"}
                  onChange={(e) => handleFontChange(e, "aboutCouple")}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                >
                  <optgroup label="Script Fonts (Elegant)">
                    {availableFonts
                      .filter(font => font.category === "script")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Serif Fonts (Traditional)">
                    {availableFonts
                      .filter(font => font.category === "serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Sans-Serif Fonts (Modern)">
                    {availableFonts
                      .filter(font => font.category === "sans-serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Classic Fonts">
                    {availableFonts
                      .filter(font => font.category === "classic")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>

              {/* General Body Font */}
              <div className="mb-3">
                <label className="block text-sm text-gray-600 mb-1">
                  General Body Font
                </label>
                <select
                  value={designSettings.fonts?.body || "Roboto"}
                  onChange={(e) => handleFontChange(e, "body")}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                >
                  <optgroup label="Script Fonts (Elegant)">
                    {availableFonts
                      .filter(font => font.category === "script")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Serif Fonts (Traditional)">
                    {availableFonts
                      .filter(font => font.category === "serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Sans-Serif Fonts (Modern)">
                    {availableFonts
                      .filter(font => font.category === "sans-serif")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="Classic Fonts">
                    {availableFonts
                      .filter(font => font.category === "classic")
                      .map((font) => (
                        <option
                          key={font.name}
                          value={font.name}
                          style={{ fontFamily: font.name }}
                        >
                          {font.name}
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>
            </div>
          </div>

          <div className="mt-8 border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-2 text-black">Preview</h3>
            <div className="relative overflow-hidden rounded-lg border shadow-md" style={{ height: '500px' }}>
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{
                  backgroundImage: designSettings.customImage ?
                    `url(${designSettings.customImage})` :
                    (selectImage ? `url(${selectImage})` : 'none'),
                  opacity: 0.7,
                  backgroundColor: designSettings.colors?.background || '#FFFFFF',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }}
              />

              {/* Content Container */}
              <div className="relative z-10 h-full flex flex-col items-center justify-center p-8 text-center">
                {/* Header Section */}
                <div className="mb-8">
                  {/* Always show couple names in preview */}
                <h2
                  className="text-3xl mb-2"
                  style={{
                    color: designSettings.colors?.primary || '#B31B1E',
                    fontFamily: designSettings.fonts?.coupleNames || designSettings.fonts?.heading || 'Playfair Display',
                    textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }}
                >
                  {coupleNames || 'Jane & John'}
                </h2>
                  <h1
                    className="text-4xl mb-4"
                    style={{
                      color: designSettings.colors?.primary || '#B31B1E',
                      fontFamily: designSettings.fonts?.heading || 'Playfair Display',
                      textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                    }}
                  >
                    {title || "Our Wedding"}
                  </h1>
                  <p
                    className="text-xl mb-2"
                    style={{
                      color: designSettings.colors?.text || '#000000',
                      fontFamily: designSettings.fonts?.date || designSettings.fonts?.body || 'Roboto'
                    }}
                  >
                    {weddingDate ? new Date(weddingDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) : "Wedding Date"}
                  </p>
                  <p
                    className="text-lg"
                    style={{
                      color: designSettings.colors?.text || '#000000',
                      fontFamily: designSettings.fonts?.location || designSettings.fonts?.body || 'Roboto'
                    }}
                  >
                    {weddingLocation || "Wedding Location"}
                  </p>
                </div>

                {/* Story Section */}
                {aboutCouple && (
                  <div className="mb-8 max-w-lg mx-auto bg-white bg-opacity-80 p-4 rounded-lg shadow-sm">
                    <h3
                      className="text-2xl font-semibold mb-3"
                      style={{
                        color: designSettings.colors?.primary || '#B31B1E',
                        fontFamily: designSettings.fonts?.heading || 'Playfair Display'
                      }}
                    >
                      Our Story
                    </h3>
                    <p
                      className="text-base"
                      style={{
                        color: designSettings.colors?.text || '#000000',
                        fontFamily: designSettings.fonts?.aboutCouple || designSettings.fonts?.body || 'Roboto'
                      }}
                    >
                      {aboutCouple.length > 100 ? aboutCouple.substring(0, 100) + '...' : aboutCouple}
                    </p>
                  </div>
                )}

                {/* Button */}
                <div
                  className="px-6 py-3 rounded-lg shadow-sm transition-transform hover:scale-105"
                  style={{
                    backgroundColor: designSettings.colors?.secondary || '#333333',
                    color: '#FFFFFF',
                    fontFamily: designSettings.fonts?.body || 'Roboto',
                    cursor: 'pointer'
                  }}
                >
                  we are getting married
                </div>
              </div>
            </div>

            {/* Preview Note */}
            <p className="text-xs text-gray-500 mt-2 text-center">
              This is a preview of how your wedding website will look. The actual website will be fully responsive and optimized for all devices.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WebsiteEditor;

