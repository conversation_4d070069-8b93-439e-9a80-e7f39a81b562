// Define interfaces for checklist items
export interface ChecklistItem {
  item_id: string; // API uses item_id instead of id
  task: string;
  status: string; // API uses status instead of completed
  category: string;
  due_date?: string;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
}

// Define interfaces for budget items
export interface BudgetCategory {
  category_id: string;
  name: string;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface BudgetExpense {
  expense_id: string;
  category_id: string;
  name: string;
  estimated_budget: number;
  final_cost: number;
  amount_paid: number;
  notes?: string;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
  category_name?: string; // Added by API response
}

export interface BudgetPayment {
  payment_id: string;
  expense_id: string;
  amount: number;
  payment_date: string;
  payment_method?: string;
  notes?: string;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
  expense_name?: string; // Added by API response
  category_name?: string; // Added by API response
  paid_by: string;
}

export interface BudgetTotals {
  total_estimated: number;
  total_final: number;
  total_paid: number;
}

// Define interfaces for guestlist items
export interface GuestGroup {
  group_id: string;
  name: string;
  guest_count?: number;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Guest {
  guest_id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  address?: string;
  group_id?: string;
  group_name?: string;
  age_category: 'adult' | 'child' | 'baby';
  attendance_status: 'attending' | 'pending' | 'declined';
  menu_choice?: string;
  notes?: string;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface GuestStatistics {
  total_guests: number;
  attending: number;
  pending: number;
  declined: number;
  adults: number;
  children: number;
  babies: number;
}

export interface Invitation {
  invitation_id: string;
  title: string;
  message?: string;
  wedding_date?: string;
  wedding_location?: string;
  image_url?: string;
  website_url?: string;
  rsvp_enabled: boolean;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
}

// Helper functions
export const isCompleted = (status: string): boolean => {
  return status === 'completed';
};

export const getStatus = (completed: boolean): string => {
  return completed ? 'completed' : 'pending';
};
