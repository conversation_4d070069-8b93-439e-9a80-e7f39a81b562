{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@botpress/webchat": "2.3", "@clerk/nextjs": "^6.12.5", "@types/uuid": "^10.0.0", "axios": "^1.6.7", "emoji-picker-react": "^4.12.2", "firebase": "^11.6.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.479.0", "next": "15.2.1", "nodemailer": "^6.10.0", "react": "^19.0.0", "react-color": "^2.19.3", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "sib-api-v3-sdk": "^8.5.0", "twilio": "^5.5.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jwt-decode": "^2.2.1", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-color": "^3.0.10", "@types/react-dom": "^19", "@types/twilio": "^3.19.2", "eslint": "^9", "eslint-config-next": "15.2.1", "tailwindcss": "^4", "typescript": "^5"}}