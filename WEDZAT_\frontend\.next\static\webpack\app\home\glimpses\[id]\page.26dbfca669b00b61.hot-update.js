"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./components/VideoInteractionBar.tsx":
/*!********************************************!*\
  !*** ./components/VideoInteractionBar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst VideoInteractionBar = (param)=>{\n    let { username, uploadDate, viewCount, description, userId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isCreatingChat, setIsCreatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiring, setIsAdmiring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiringLoading, setIsAdmiringLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Format view count\n    const formatViewCount = (count)=>{\n        if (count >= 1000000) {\n            return \"\".concat((count / 1000000).toFixed(1), \"M\");\n        } else if (count >= 1000) {\n            return \"\".concat((count / 1000).toFixed(1), \"K\");\n        }\n        return count.toString();\n    };\n    // Handle message button click\n    const handleMessageClick = async ()=>{\n        if (isCreatingChat) return;\n        try {\n            setIsCreatingChat(true);\n            // Get token from localStorage - use userToken as in the chat page\n            const token = localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n            if (!token) {\n                console.error(\"No authentication token found\");\n                alert(\"Please log in to send messages\");\n                setIsCreatingChat(false);\n                return;\n            }\n            // Use the hardcoded URL from the messages page\n            const apiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\";\n            // If userId is 'default' or missing, use a fallback ID for testing\n            // In a real app, you would handle this differently\n            const participantId = !userId || userId === \"default\" ? username.toLowerCase().replace(/\\s+/g, \"_\") + \"_id\" : userId;\n            console.log(\"Creating conversation with user ID: \".concat(participantId));\n            console.log(\"Using API URL: \".concat(apiUrl, \"/conversations\"));\n            console.log(\"Authorization token: \".concat(token.substring(0, 10), \"...\"));\n            try {\n                // Create or get existing conversation\n                console.log(\"Request payload:\", JSON.stringify({\n                    participants: [\n                        participantId\n                    ]\n                }));\n                const response = await fetch(\"\".concat(apiUrl, \"/conversations\"), {\n                    method: \"POST\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        participants: [\n                            participantId\n                        ]\n                    }),\n                    // Add these options to help with CORS issues\n                    mode: \"cors\",\n                    credentials: \"same-origin\"\n                });\n                console.log(\"Response status:\", response.status);\n                console.log(\"Response headers:\", [\n                    ...response.headers.entries()\n                ]);\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"Conversation created/retrieved:\", data);\n                // Handle different response formats\n                let conversationId = null;\n                // Check if the response has a direct conversation_id property\n                if (data.conversation_id) {\n                    conversationId = data.conversation_id;\n                } else if (data.body && typeof data.body === \"string\") {\n                    try {\n                        const bodyData = JSON.parse(data.body);\n                        if (bodyData.conversation_id) {\n                            conversationId = bodyData.conversation_id;\n                            console.log(\"Found conversation ID in body:\", conversationId);\n                        }\n                    } catch (e) {\n                        console.error(\"Error parsing body data:\", e);\n                    }\n                }\n                if (!conversationId) {\n                    console.error(\"No conversation ID found in any format\", data);\n                    throw new Error(\"Invalid response from server\");\n                }\n                // Navigate to the chat page\n                router.push(\"/messages/\".concat(conversationId));\n            } catch (innerError) {\n                console.error(\"Inner fetch error:\", innerError);\n                throw innerError;\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            // Provide more specific error messages based on the error\n            if (error instanceof TypeError && error.message.includes(\"Failed to fetch\")) {\n                alert(\"Network error: Please check your internet connection and try again.\");\n            } else if (error instanceof Error && error.message.includes(\"401\")) {\n                alert(\"Authentication error: Please log in again.\");\n            } else if (error instanceof Error && error.message.includes(\"403\")) {\n                alert(\"Permission denied: You don't have permission to message this user.\");\n            } else {\n                alert(\"Failed to start conversation. Please try again.\");\n            }\n        } finally{\n            setIsCreatingChat(false);\n        }\n    };\n    // Function to navigate to user profile\n    const navigateToUserProfile = ()=>{\n        if (userId) {\n            // Get the token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Navigate to the profile page with the userId\n            router.push(\"/profile/\".concat(userId));\n        }\n    };\n    // Function to handle admire/unadmire\n    const handleAdmireToggle = async ()=>{\n        if (isAdmiringLoading || !userId) return;\n        try {\n            setIsAdmiringLoading(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to admire this user');\n                setIsAdmiringLoading(false);\n                return;\n            }\n            // Optimistically update UI state immediately for better user experience\n            const newAdmiringState = !isAdmiring;\n            setIsAdmiring(newAdmiringState);\n            // Make API call to follow/unfollow user\n            const endpoint = isAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n            console.log(\"Making request to \".concat(endpoint, \" with user ID: \").concat(userId));\n            try {\n                // Make the API call\n                const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(endpoint, {\n                    target_user_id: userId\n                }, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('API response:', response.data);\n                console.log(\"Successfully \".concat(isAdmiring ? 'unadmired' : 'admired', \" user\"));\n                // Update localStorage with the new state\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    let admiredUsers = JSON.parse(admiredUsersJson);\n                    if (newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                    console.log('Updated admired users in localStorage:', admiredUsers);\n                    // Force a refresh of the following list to ensure it's up to date\n                    const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        }\n                    });\n                    console.log('Updated following list:', followingResponse.data);\n                    // Double-check our state is correct\n                    if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                        const isActuallyFollowing = followingResponse.data.following.some((user)=>user.user_id === userId || user === userId);\n                        if (isActuallyFollowing !== newAdmiringState) {\n                            console.log('State mismatch detected, correcting...');\n                            setIsAdmiring(isActuallyFollowing);\n                            // Update localStorage again with the correct state\n                            admiredUsers = JSON.parse(localStorage.getItem('admiredUsers') || '{}');\n                            if (isActuallyFollowing) {\n                                admiredUsers[userId] = true;\n                            } else {\n                                delete admiredUsers[userId];\n                            }\n                            localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                        }\n                    }\n                } catch (storageError) {\n                    console.error('Error updating localStorage:', storageError);\n                }\n            } catch (apiError) {\n                console.error(\"Error \".concat(isAdmiring ? 'unadmiring' : 'admiring', \" user:\"), apiError);\n                if (apiError.response) {\n                    console.log('Error response data:', apiError.response.data);\n                    console.log('Error response status:', apiError.response.status);\n                    // If the error is that the user is already following/not following, the UI state is already correct\n                    if (apiError.response.status === 400 && apiError.response.data && (apiError.response.data.error === \"Already following this user\" || apiError.response.data.error === \"Not following this user\")) {\n                        console.log('Already in desired state, keeping UI updated');\n                        return;\n                    }\n                }\n                // If there was an error that wasn't just \"already in desired state\", revert the UI\n                setIsAdmiring(!newAdmiringState);\n                // Also update localStorage to match\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (!newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                } catch (storageError) {\n                    console.error('Error updating localStorage after API error:', storageError);\n                }\n            }\n        } catch (error) {\n            console.error('Unexpected error in handleAdmireToggle:', error);\n            // Revert UI state on unexpected errors\n            setIsAdmiring(!isAdmiring);\n        } finally{\n            setIsAdmiringLoading(false);\n        }\n    };\n    // Check if user is already admiring on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!userId) return;\n            // First check localStorage for cached admiring state\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers');\n                if (admiredUsersJson) {\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (admiredUsers[userId]) {\n                        console.log('Found admiring status in localStorage:', true);\n                        setIsAdmiring(true);\n                    // Continue with API check to verify\n                    }\n                }\n            } catch (storageError) {\n                console.error('Error reading from localStorage:', storageError);\n            }\n            const checkAdmiringStatus = {\n                \"VideoInteractionBar.useEffect.checkAdmiringStatus\": async ()=>{\n                    console.log('Checking admiring status for user ID:', userId);\n                    // Get token from localStorage\n                    const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                    if (!token) {\n                        console.warn('No authentication token found');\n                        return;\n                    }\n                    // Check if this is the current user's content - no need to check admiring status\n                    try {\n                        const tokenParts = token.split('.');\n                        if (tokenParts.length === 3) {\n                            const payload = JSON.parse(atob(tokenParts[1]));\n                            if (payload.user_id === userId) {\n                                console.log('This is the current user\\'s content, skipping admiring status check');\n                                setIsAdmiring(false); // User can't admire themselves\n                                return;\n                            }\n                        }\n                    } catch (tokenError) {\n                        console.log('Could not decode token to check current user, proceeding with API calls');\n                    }\n                    try {\n                        // Direct API call to check following status - most reliable method\n                        const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('Following response:', followingResponse.data);\n                        if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                            // Check if userId is in the following array\n                            const isFollowing = followingResponse.data.following.some({\n                                \"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\": (user)=>user.user_id === userId || user === userId\n                            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\"]);\n                            console.log(\"User \".concat(isFollowing ? 'is' : 'is not', \" in following list:\"), userId);\n                            setIsAdmiring(isFollowing);\n                            // Update localStorage for future reference\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (isFollowing) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                            return;\n                        }\n                    } catch (followingError) {\n                        console.log('Error fetching following list, trying alternative method');\n                    }\n                    // Fallback: Try to get user profile\n                    try {\n                        const profileResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-profile?user_id=\".concat(userId), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('User profile response:', profileResponse.data);\n                        if (profileResponse.data && profileResponse.data.is_following !== undefined) {\n                            console.log('Setting admiring status from profile:', profileResponse.data.is_following);\n                            setIsAdmiring(profileResponse.data.is_following);\n                            // Update localStorage\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (profileResponse.data.is_following) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                        }\n                    } catch (profileError) {\n                        console.log('Error fetching user profile, trying another method');\n                        // Last resort: Try the user-follow-stats endpoint\n                        try {\n                            const statsResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-follow-stats?target_user_id=\".concat(userId), {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token),\n                                    'Content-Type': 'application/json'\n                                }\n                            });\n                            console.log('User follow stats response:', statsResponse.data);\n                            if (statsResponse.data && statsResponse.data.is_following !== undefined) {\n                                setIsAdmiring(statsResponse.data.is_following);\n                                // Update localStorage\n                                try {\n                                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                    const admiredUsers = JSON.parse(admiredUsersJson);\n                                    if (statsResponse.data.is_following) {\n                                        admiredUsers[userId] = true;\n                                    } else {\n                                        delete admiredUsers[userId];\n                                    }\n                                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                } catch (storageError) {\n                                    console.error('Error updating localStorage:', storageError);\n                                }\n                            }\n                        } catch (statsError) {\n                            console.log('All API methods failed to check admiring status, using localStorage fallback');\n                            // Fallback to localStorage if all API calls fail\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                const isAdmiringFromStorage = admiredUsers[userId] === true;\n                                setIsAdmiring(isAdmiringFromStorage);\n                                console.log(\"Using localStorage fallback: \".concat(isAdmiringFromStorage ? 'admiring' : 'not admiring'));\n                            } catch (storageError) {\n                                console.log('localStorage fallback also failed, defaulting to not admiring');\n                                setIsAdmiring(false);\n                            }\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus\"];\n            // Call the API check function\n            checkAdmiringStatus();\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        userId\n    ]);\n    // Add click outside handler to close menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"VideoInteractionBar.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.menu-container')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"VideoInteractionBar.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"VideoInteractionBar.useEffect\"];\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        showMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-white p-2 sm:p-3 md:p-4 rounded-b-xl border border-gray-200 border-t-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 sm:space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-red-100 hover:bg-red-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-red-600\",\n                                        fill: \"#dc2626\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    onClick: handleMessageClick,\n                                    disabled: isCreatingChat,\n                                    title: \"Send a message\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: formatViewCount(viewCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"views\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative menu-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMenu(!showMenu),\n                                        className: \"p-1.5 rounded-full hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 18,\n                                            className: \"text-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: \"Report content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"cursor-pointer\",\n                                        onClick: navigateToUserProfile,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            username: username || \"Anonymous\",\n                                            size: \"md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-black cursor-pointer hover:underline\",\n                                                onClick: navigateToUserProfile,\n                                                children: username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Uploaded \",\n                                                    uploadDate || 'recently'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMessageClick,\n                                        disabled: isCreatingChat,\n                                        className: \"flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-full text-sm transition-colors\",\n                                        title: \"Send a message\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isCreatingChat ? \"Opening...\" : \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAdmireToggle,\n                                        disabled: isAdmiringLoading,\n                                        className: \"flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors bg-[#B31B1E] text-white hover:bg-red-700\",\n                                        title: isAdmiring ? \"Admiring\" : \"Admire this user\",\n                                        children: isAdmiringLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                isAdmiring ? 'Admiring' : 'Admire',\n                                                !isAdmiring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 35\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, undefined),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black mt-2 text-xs sm:text-sm line-clamp-3 sm:line-clamp-none\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 571,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n        lineNumber: 523,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoInteractionBar, \"Yu43+IzKlIkJZU7l83msx1eRLMs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = VideoInteractionBar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VideoInteractionBar);\nvar _c;\n$RefreshReg$(_c, \"VideoInteractionBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/VideoInteractionBar.tsx\n"));

/***/ })

});