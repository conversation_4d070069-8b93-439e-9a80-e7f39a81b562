(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/services_axiosConfig_ts_a1bdd108._.js", {

"[project]/services/axiosConfig.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// services/axiosConfig.ts
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// Create a custom axios instance with default configuration
const instance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub") || 'http://localhost:3000',
    headers: {
        'Content-Type': 'application/json'
    }
});
// Add a request interceptor to include the auth token in all requests
instance.interceptors.request.use((config)=>{
    // Get the token from localStorage if available
    // Try multiple possible token keys for compatibility
    const token = ("TURBOPACK compile-time truthy", 1) ? localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('auth_token') : ("TURBOPACK unreachable", undefined);
    // If token exists, add it to the Authorization header
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Debug API calls
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
        timestamp: new Date().toISOString(),
        caller: new Error().stack?.split('\n')[2]?.trim() || 'unknown'
    });
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add a response interceptor to handle common response issues
instance.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response && error.response.status === 401) {
        // Clear all possible token keys and redirect to login if needed
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.removeItem('token');
            localStorage.removeItem('jwt_token');
            localStorage.removeItem('auth_token');
            console.log('Authentication error: Cleared all tokens');
        // Optionally redirect to login page
        // window.location.href = '/login';
        }
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=services_axiosConfig_ts_a1bdd108._.js.map