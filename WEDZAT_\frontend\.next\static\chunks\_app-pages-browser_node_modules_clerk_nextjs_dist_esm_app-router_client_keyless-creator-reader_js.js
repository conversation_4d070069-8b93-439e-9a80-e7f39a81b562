"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_js"],{

/***/ "(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeylessCreatorOrReader: () => (/* binding */ KeylessCreatorOrReader)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _keyless_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../keyless-actions */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n\n\n\n\nconst KeylessCreatorOrReader = (props) => {\n  var _a;\n  const { children } = props;\n  const segments = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useSelectedLayoutSegments)();\n  const isNotFoundRoute = ((_a = segments[0]) == null ? void 0 : _a.startsWith(\"/_not-found\")) || false;\n  const [state, fetchKeys] = react__WEBPACK_IMPORTED_MODULE_1___default().useActionState(_keyless_actions__WEBPACK_IMPORTED_MODULE_2__.createOrReadKeylessAction, null);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (isNotFoundRoute) {\n      return;\n    }\n    react__WEBPACK_IMPORTED_MODULE_1___default().startTransition(() => {\n      fetchKeys();\n    });\n  }, [isNotFoundRoute]);\n  if (!react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(children)) {\n    return children;\n  }\n  return react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, {\n    key: state == null ? void 0 : state.publishableKey,\n    publishableKey: state == null ? void 0 : state.publishableKey,\n    __internal_keyless_claimKeylessApplicationUrl: state == null ? void 0 : state.claimUrl,\n    __internal_keyless_copyInstanceKeysUrl: state == null ? void 0 : state.apiKeysUrl,\n    __internal_bypassMissingPublishableKey: true\n  });\n};\n\n//# sourceMappingURL=keyless-creator-reader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AY2xlcmsvbmV4dGpzL2Rpc3QvZXNtL2FwcC1yb3V0ZXIvY2xpZW50L2tleWxlc3MtY3JlYXRvci1yZWFkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDMkI7QUFDbkI7QUFDc0I7QUFDL0Q7QUFDQTtBQUNBLFVBQVUsV0FBVztBQUNyQixtQkFBbUIsMEVBQXlCO0FBQzVDO0FBQ0EsNkJBQTZCLDJEQUFvQixDQUFDLHVFQUF5QjtBQUMzRSxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsSUFBSSw0REFBcUI7QUFDekI7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILE9BQU8sMkRBQW9CO0FBQzNCO0FBQ0E7QUFDQSxTQUFTLHlEQUFrQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaXZhc1xcT25lRHJpdmVcXERlc2t0b3BcXGZpbmFsXFxXRURaQVRfXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAY2xlcmtcXG5leHRqc1xcZGlzdFxcZXNtXFxhcHAtcm91dGVyXFxjbGllbnRcXGtleWxlc3MtY3JlYXRvci1yZWFkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwiLi4vLi4vY2h1bmstQlVTWUEyQjQuanNcIjtcbmltcG9ydCB7IHVzZVNlbGVjdGVkTGF5b3V0U2VnbWVudHMgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBjcmVhdGVPclJlYWRLZXlsZXNzQWN0aW9uIH0gZnJvbSBcIi4uL2tleWxlc3MtYWN0aW9uc1wiO1xuY29uc3QgS2V5bGVzc0NyZWF0b3JPclJlYWRlciA9IChwcm9wcykgPT4ge1xuICB2YXIgX2E7XG4gIGNvbnN0IHsgY2hpbGRyZW4gfSA9IHByb3BzO1xuICBjb25zdCBzZWdtZW50cyA9IHVzZVNlbGVjdGVkTGF5b3V0U2VnbWVudHMoKTtcbiAgY29uc3QgaXNOb3RGb3VuZFJvdXRlID0gKChfYSA9IHNlZ21lbnRzWzBdKSA9PSBudWxsID8gdm9pZCAwIDogX2Euc3RhcnRzV2l0aChcIi9fbm90LWZvdW5kXCIpKSB8fCBmYWxzZTtcbiAgY29uc3QgW3N0YXRlLCBmZXRjaEtleXNdID0gUmVhY3QudXNlQWN0aW9uU3RhdGUoY3JlYXRlT3JSZWFkS2V5bGVzc0FjdGlvbiwgbnVsbCk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzTm90Rm91bmRSb3V0ZSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBSZWFjdC5zdGFydFRyYW5zaXRpb24oKCkgPT4ge1xuICAgICAgZmV0Y2hLZXlzKCk7XG4gICAgfSk7XG4gIH0sIFtpc05vdEZvdW5kUm91dGVdKTtcbiAgaWYgKCFSZWFjdC5pc1ZhbGlkRWxlbWVudChjaGlsZHJlbikpIHtcbiAgICByZXR1cm4gY2hpbGRyZW47XG4gIH1cbiAgcmV0dXJuIFJlYWN0LmNsb25lRWxlbWVudChjaGlsZHJlbiwge1xuICAgIGtleTogc3RhdGUgPT0gbnVsbCA/IHZvaWQgMCA6IHN0YXRlLnB1Ymxpc2hhYmxlS2V5LFxuICAgIHB1Ymxpc2hhYmxlS2V5OiBzdGF0ZSA9PSBudWxsID8gdm9pZCAwIDogc3RhdGUucHVibGlzaGFibGVLZXksXG4gICAgX19pbnRlcm5hbF9rZXlsZXNzX2NsYWltS2V5bGVzc0FwcGxpY2F0aW9uVXJsOiBzdGF0ZSA9PSBudWxsID8gdm9pZCAwIDogc3RhdGUuY2xhaW1VcmwsXG4gICAgX19pbnRlcm5hbF9rZXlsZXNzX2NvcHlJbnN0YW5jZUtleXNVcmw6IHN0YXRlID09IG51bGwgPyB2b2lkIDAgOiBzdGF0ZS5hcGlLZXlzVXJsLFxuICAgIF9faW50ZXJuYWxfYnlwYXNzTWlzc2luZ1B1Ymxpc2hhYmxlS2V5OiB0cnVlXG4gIH0pO1xufTtcbmV4cG9ydCB7XG4gIEtleWxlc3NDcmVhdG9yT3JSZWFkZXJcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1rZXlsZXNzLWNyZWF0b3ItcmVhZGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOrReadKeylessAction: () => (/* binding */ createOrReadKeylessAction),\n/* harmony export */   deleteKeylessAction: () => (/* binding */ deleteKeylessAction),\n/* harmony export */   syncKeylessConfigAction: () => (/* binding */ syncKeylessConfigAction)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"7f9c875515fc1ea696b6367e4cde05e2f7b9f31040\":\"createOrReadKeylessAction\",\"7f9cdf2d5df654d5952abb8b624fdd122273b42576\":\"syncKeylessConfigAction\",\"7fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574\":\"deleteKeylessAction\"} */ \nvar createOrReadKeylessAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7f9c875515fc1ea696b6367e4cde05e2f7b9f31040\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createOrReadKeylessAction\");\nvar deleteKeylessAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteKeylessAction\");\nvar syncKeylessConfigAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7f9cdf2d5df654d5952abb8b624fdd122273b42576\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"syncKeylessConfigAction\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\n"));

/***/ })

}]);