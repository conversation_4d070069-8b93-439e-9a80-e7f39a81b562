"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./components/HomeDashboard/Flashes.tsx":
/*!**********************************************!*\
  !*** ./components/HomeDashboard/Flashes.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst FlashesSection = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [flashes, setFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // User avatar placeholders (using local images instead of external URLs)\n    const userAvatarPlaceholders = [\n        \"/pics/1stim.jfif\",\n        \"/pics/2ndim.jfif\",\n        \"/pics/jpeg3.jfif\",\n        \"/pics/user-profile.png\",\n        \"/pics/user-profile.png\"\n    ];\n    // Fallback data if API fails - using empty array\n    const getFallbackFlashes = ()=>[];\n    // Function to fetch flashes from the API\n    const fetchFlashes = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching flashes for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setFlashes([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/flashes?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            console.log('Flashes API response:', response.data);\n            // Process the data\n            if (response.data && response.data.flashes) {\n                console.log(\"Loaded \".concat(response.data.flashes.length, \" flashes for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.flashes.length > 0) {\n                    console.log('Sample flash data:', response.data.flashes[0]);\n                }\n                // Process the response\n                const processedFlashes = response.data.flashes.map((flash)=>{\n                    if (!flash.video_thumbnail) {\n                        console.log(\"Flash missing thumbnail: \".concat(flash.video_id));\n                    }\n                    return flash;\n                });\n                if (pageNumber === 1) {\n                    setFlashes(processedFlashes);\n                } else {\n                    setFlashes((prev)=>[\n                            ...prev,\n                            ...processedFlashes\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load flashes - unexpected response format');\n                setFlashes([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load flashes');\n            setFlashes([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of flashes as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FlashesSection.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Flashes component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial flashes load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchFlashes function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for flashes page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/flashes?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"FlashesSection.useEffect\": (response)=>{\n                            console.log('API response received for flashes page 1');\n                            if (response.data && response.data.flashes) {\n                                setFlashes(response.data.flashes);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setFlashes([]);\n                                setError('No flashes found');\n                            }\n                        }\n                    }[\"FlashesSection.useEffect\"]).catch({\n                        \"FlashesSection.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load flashes');\n                            setFlashes([]);\n                        }\n                    }[\"FlashesSection.useEffect\"]).finally({\n                        \"FlashesSection.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"FlashesSection.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"FlashesSection.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Failsafe to ensure content is loaded - only show error after timeout\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FlashesSection.useEffect\": ()=>{\n            // If we're stuck in loading state for more than 10 seconds, show error\n            let timeoutId = null;\n            if (loading && initialLoadComplete) {\n                timeoutId = setTimeout({\n                    \"FlashesSection.useEffect\": ()=>{\n                        console.log('Loading timeout reached - API request may have failed');\n                        setLoading(false);\n                        if (flashes.length === 0) {\n                            setError('Unable to load flashes. Please check your network connection.');\n                        }\n                    }\n                }[\"FlashesSection.useEffect\"], 10000); // 10 second timeout for slow networks\n            }\n            return ({\n                \"FlashesSection.useEffect\": ()=>{\n                    if (timeoutId) clearTimeout(timeoutId);\n                }\n            })[\"FlashesSection.useEffect\"];\n        }\n    }[\"FlashesSection.useEffect\"], [\n        loading,\n        initialLoadComplete,\n        flashes.length\n    ]);\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FlashesSection.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"FlashesSection.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchFlashes(nextPage, false);\n                    }\n                }\n            }[\"FlashesSection.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"FlashesSection.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"FlashesSection.useEffect\"];\n        }\n    }[\"FlashesSection.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a flash\n    const getImageSource = (flash)=>{\n        if (flash.video_thumbnail) {\n            return flash.video_thumbnail;\n        }\n        return '/pics/placeholder.svg';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"w-full mb-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-black\",\n                        children: \"Flashes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading flashes...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying flashes load...');\n                            setError(null);\n                            fetchFlashes(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: 'smooth',\n                    WebkitOverflowScrolling: 'touch',\n                    minHeight: flashes.length === 0 && !error ? '220px' : 'auto'\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative w-full\",\n                children: [\n                    flashes.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No flashes available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-3 pb-4 flex-nowrap\",\n                        children: [\n                            flashes.map((flash, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>{\n                                        // Open in shorts view with current index\n                                        window.location.href = \"/home/<USER>/shorts?index=\".concat(index);\n                                    },\n                                    style: {\n                                        background: \"linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)\"\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] p-[6px] sm:p-[10px] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                top: \"10px\",\n                                                left: \"10px\",\n                                                zIndex: 2\n                                            },\n                                            onClick: (e)=>navigateToUserProfile(flash.user_id, flash.user_name, e),\n                                            className: \"jsx-83037452c623c470\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                username: flash.user_name || \"user\",\n                                                size: \"sm\",\n                                                isGradientBorder: true,\n                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length],\n                                                onClick: ()=>navigateToUserProfile(flash.user_id, flash.user_name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                top: \"10px\",\n                                                left: \"50px\",\n                                                zIndex: 2,\n                                                color: \"white\",\n                                                textShadow: \"0px 1px 2px rgba(0,0,0,0.8)\"\n                                            },\n                                            onClick: (e)=>navigateToUserProfile(flash.user_id, flash.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"cursor-pointer hover:underline text-sm font-medium\",\n                                            children: flash.user_name || \"user\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                top: 0,\n                                                left: 0,\n                                                right: 0,\n                                                bottom: 0,\n                                                zIndex: 1\n                                            },\n                                            className: \"jsx-83037452c623c470\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: getImageSource(flash),\n                                                    alt: flash.video_name || \"Flash Video\",\n                                                    fill: true,\n                                                    sizes: \"(max-width: 480px) 120px, (max-width: 640px) 140px, (max-width: 768px) 180px, 200px\",\n                                                    className: \"object-cover\",\n                                                    ...index < 2 ? {\n                                                        priority: true\n                                                    } : {\n                                                        loading: 'lazy'\n                                                    },\n                                                    unoptimized: !flash.video_thumbnail,\n                                                    placeholder: \"blur\" // Show blur placeholder while loading\n                                                    ,\n                                                    blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                    ,\n                                                    onError: (e)=>{\n                                                        const imgElement = e.target;\n                                                        if (imgElement) {\n                                                            imgElement.src = '/pics/placeholder.svg';\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"10px\",\n                                                left: \"10px\",\n                                                right: \"10px\",\n                                                zIndex: 2,\n                                                color: \"white\"\n                                            },\n                                            className: \"jsx-83037452c623c470\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: 500\n                                                    },\n                                                    className: \"jsx-83037452c623c470\",\n                                                    children: flash.video_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\"\n                                                    },\n                                                    className: \"jsx-83037452c623c470\",\n                                                    children: [\n                                                        flash.video_views ? \"\".concat((flash.video_views / 1000).toFixed(1), \"K views\") : '',\n                                                        flash.video_likes ? \" • \".concat((flash.video_likes / 1000).toFixed(1), \"K likes\") : ''\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(flash.video_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: 'relative',\n                                    // Add debug outline in development\n                                    outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"flashes-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[220px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Flashes.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashesSection, \"/g2coJEd3Jzc6hrl4a1IEgaNNaQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = FlashesSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashesSection);\nvar _c;\n$RefreshReg$(_c, \"FlashesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Flashes.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/HomeDashboard/Photos.tsx":
/*!*********************************************!*\
  !*** ./components/HomeDashboard/Photos.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Photos = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [photos, setPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [likedPhotos, setLikedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set());\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // Fallback data if API fails - using empty array\n    const getFallbackPhotos = ()=>[];\n    // Function to fetch photos from the API\n    const fetchPhotos = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching photos for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setPhotos([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.photos) {\n                console.log(\"Loaded \".concat(response.data.photos.length, \" photos for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.photos.length > 0) {\n                    console.log('Sample photo data:', response.data.photos[0]);\n                }\n                // Process the response\n                const processedPhotos = response.data.photos.map((photo)=>{\n                    if (!photo.photo_url) {\n                        console.log(\"Photo missing URL: \".concat(photo.photo_id));\n                    }\n                    return photo;\n                });\n                if (pageNumber === 1) {\n                    setPhotos(processedPhotos);\n                } else {\n                    setPhotos((prev)=>[\n                            ...prev,\n                            ...processedPhotos\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load photos - unexpected response format');\n                setPhotos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load photos');\n            setPhotos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of photos as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Photos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial photos load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchPhotos function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for photos page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"Photos.useEffect\": (response)=>{\n                            console.log('API response received for photos page 1');\n                            if (response.data && response.data.photos) {\n                                setPhotos(response.data.photos);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setPhotos([]);\n                                setError('No photos found');\n                            }\n                        }\n                    }[\"Photos.useEffect\"]).catch({\n                        \"Photos.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load photos');\n                            setPhotos([]);\n                        }\n                    }[\"Photos.useEffect\"]).finally({\n                        \"Photos.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"Photos.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"Photos.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"Photos.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchPhotos(nextPage, false);\n                    }\n                }\n            }[\"Photos.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"Photos.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"Photos.useEffect\"];\n        }\n    }[\"Photos.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a photo\n    const getImageSource = (photo)=>{\n        if (photo.photo_url) {\n            return photo.photo_url;\n        }\n        return '/pics/placeholder.svg';\n    };\n    // Toggle like for photos\n    const toggleLike = async (photoId)=>{\n        try {\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedPhotos.has(photoId);\n            setLikedPhotos((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(photoId);\n                } else {\n                    newLiked.add(photoId);\n                }\n                return newLiked;\n            });\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: photoId,\n                    content_type: 'photo'\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" photo: \").concat(photoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedPhotos((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedPhotos.has(photoId)) {\n                    newLiked.delete(photoId);\n                } else {\n                    newLiked.add(photoId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-black\",\n                        children: \"Photos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading photos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying photos load...');\n                            setError(null);\n                            fetchPhotos(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: 'smooth',\n                    WebkitOverflowScrolling: 'touch',\n                    minHeight: photos.length === 0 && !error ? '220px' : 'auto'\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative\",\n                children: [\n                    photos.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No photos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-4 pb-4 flex-nowrap\",\n                        children: [\n                            photos.map((photo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: '250px',\n                                        height: '200px'\n                                    },\n                                    onClick: ()=>{\n                                        window.location.href = \"/home/<USER>/\".concat(photo.photo_id);\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute top-2 left-2 z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                username: photo.user_name || \"user\",\n                                                size: \"sm\",\n                                                isGradientBorder: true,\n                                                onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: getImageSource(photo),\n                                                alt: photo.photo_name || \"Photo\",\n                                                fill: true,\n                                                sizes: \"(max-width: 640px) 250px, 250px\",\n                                                className: \"object-cover\",\n                                                ...index < 4 ? {\n                                                    priority: true\n                                                } : {\n                                                    loading: 'lazy'\n                                                },\n                                                placeholder: \"blur\" // Show blur placeholder while loading\n                                                ,\n                                                blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                ,\n                                                onError: (e)=>{\n                                                    const imgElement = e.target;\n                                                    if (imgElement) {\n                                                        imgElement.src = '/pics/placeholder.svg';\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-sm font-medium truncate\",\n                                                    children: photo.photo_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                                            className: \"jsx-83037452c623c470\" + \" \" + \"cursor-pointer hover:underline\",\n                                                            children: photo.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-83037452c623c470\",\n                                                            children: photo.photo_likes ? \"\".concat(photo.photo_likes, \" likes\") : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(photo.photo_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: 'relative',\n                                    // Add debug outline in development\n                                    outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"photos-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[200px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 362,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Photos, \"0D7xLpRSc97qwaKU4HSyafwIrS4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = Photos;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Photos);\nvar _c;\n$RefreshReg$(_c, \"Photos\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Photos.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/HomeDashboard/WeddingVideos.tsx":
/*!****************************************************!*\
  !*** ./components/HomeDashboard/WeddingVideos.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var _barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MdChevronLeft,MdChevronRight,MdFavorite,MdFavoriteBorder,MdOutlineModeComment,MdOutlineShare!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst WeddingVideosSection = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [likedVideos, setLikedVideos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [admiringUsers, setAdmiringUsers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [weddingVideos, setWeddingVideos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Fallback data if API fails - using empty array\n    const getFallbackMovies = ()=>[];\n    // Function to fetch movies from the API\n    const fetchMovies = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching movies for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setWeddingVideos([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/movies?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.movies) {\n                console.log(\"Loaded \".concat(response.data.movies.length, \" movies for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.movies.length > 0) {\n                    console.log('Sample movie data:', response.data.movies[0]);\n                }\n                // Process the response\n                const processedMovies = response.data.movies.map((movie)=>{\n                    if (!movie.video_thumbnail) {\n                        console.log(\"Movie missing thumbnail: \".concat(movie.video_id));\n                    }\n                    // Ensure user_id is set - in case the API doesn't provide it\n                    if (!movie.user_id && movie.user_name) {\n                        // Create a temporary user_id based on username if not provided\n                        // This is a fallback and should be replaced with actual user IDs from the API\n                        console.log(\"Movie missing user_id, creating temporary one from username: \".concat(movie.user_name));\n                        movie.user_id = \"user-\".concat(movie.user_name.toLowerCase().replace(/\\s+/g, '-'));\n                    }\n                    return movie;\n                });\n                if (pageNumber === 1) {\n                    setWeddingVideos(processedMovies);\n                    // Load like status for initial videos\n                    loadLikeStatus(processedMovies.map((video)=>video.video_id));\n                } else {\n                    setWeddingVideos((prev)=>[\n                            ...prev,\n                            ...processedMovies\n                        ]);\n                    // Load like status for new videos (append to existing)\n                    const newVideoIds = processedMovies.map((video)=>video.video_id);\n                    const existingLikedMoviesData = localStorage.getItem('likedMovies');\n                    const existingLikedMovies = existingLikedMoviesData ? JSON.parse(existingLikedMoviesData) : [];\n                    const newLikedVideos = newVideoIds.filter((id)=>existingLikedMovies.includes(id));\n                    setLikedVideos((prev)=>[\n                            ...prev,\n                            ...newLikedVideos\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load wedding videos - unexpected response format');\n                setWeddingVideos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load wedding videos');\n            setWeddingVideos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of movies as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Wedding Videos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial movies load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchMovies function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for movies page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/movies?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"WeddingVideosSection.useEffect\": (response)=>{\n                            console.log('API response received for movies page 1');\n                            if (response.data && response.data.movies) {\n                                setWeddingVideos(response.data.movies);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                                // Load like status for the fetched videos\n                                loadLikeStatus(response.data.movies.map({\n                                    \"WeddingVideosSection.useEffect\": (video)=>video.video_id\n                                }[\"WeddingVideosSection.useEffect\"]));\n                            } else {\n                                setWeddingVideos([]);\n                                setError('No wedding videos found');\n                            }\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]).catch({\n                        \"WeddingVideosSection.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load wedding videos');\n                            setWeddingVideos([]);\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]).finally({\n                        \"WeddingVideosSection.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"WeddingVideosSection.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Failsafe to ensure content is loaded - only show error after timeout\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // If we're stuck in loading state for more than 10 seconds, show error\n            let timeoutId = null;\n            if (loading && initialLoadComplete) {\n                timeoutId = setTimeout({\n                    \"WeddingVideosSection.useEffect\": ()=>{\n                        console.log('Loading timeout reached - API request may have failed');\n                        setLoading(false);\n                        if (weddingVideos.length === 0) {\n                            setError('Unable to load wedding videos. Please check your network connection.');\n                        }\n                    }\n                }[\"WeddingVideosSection.useEffect\"], 10000); // 10 second timeout for slow networks\n            }\n            return ({\n                \"WeddingVideosSection.useEffect\": ()=>{\n                    if (timeoutId) clearTimeout(timeoutId);\n                }\n            })[\"WeddingVideosSection.useEffect\"];\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        loading,\n        initialLoadComplete,\n        weddingVideos.length\n    ]);\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"WeddingVideosSection.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchMovies(nextPage, false);\n                    }\n                }\n            }[\"WeddingVideosSection.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"WeddingVideosSection.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"WeddingVideosSection.useEffect\"];\n        }\n    }[\"WeddingVideosSection.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Load admiring status from localStorage when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WeddingVideosSection.useEffect\": ()=>{\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers');\n                if (admiredUsersJson) {\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    setAdmiringUsers(admiredUsers);\n                    console.log('Loaded admiring status from localStorage:', admiredUsers);\n                }\n            } catch (error) {\n                console.error('Error loading admiring status from localStorage:', error);\n            }\n        }\n    }[\"WeddingVideosSection.useEffect\"], []);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username)=>{\n        // If we have a userId, make a direct API call to the user profile endpoint\n        if (userId) {\n            // Get the token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Navigate to the profile page with the userId\n            router.push(\"/profile/\".concat(userId));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    const toggleLike = async (videoId)=>{\n        console.log('🏠 Home page movie like button clicked for ID:', videoId);\n        try {\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('🏠 No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedVideos.includes(videoId);\n            console.log('🏠 Current like status:', isCurrentlyLiked);\n            setLikedVideos((prev)=>isCurrentlyLiked ? prev.filter((id)=>id !== videoId) : [\n                    ...prev,\n                    videoId\n                ]);\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            console.log('🏠 Making API call to:', endpoint);\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: videoId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🏠 API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const responseData = await response.json();\n            console.log('🏠 API Success Response:', responseData);\n            // Update localStorage for persistence\n            const likedMoviesData = localStorage.getItem('likedMovies');\n            const likedMoviesArray = likedMoviesData ? JSON.parse(likedMoviesData) : [];\n            if (isCurrentlyLiked) {\n                // Remove from localStorage\n                const updatedArray = likedMoviesArray.filter((id)=>id !== videoId);\n                localStorage.setItem('likedMovies', JSON.stringify(updatedArray));\n                console.log('🏠 Removed from localStorage');\n            } else {\n                // Add to localStorage\n                if (!likedMoviesArray.includes(videoId)) {\n                    likedMoviesArray.push(videoId);\n                    localStorage.setItem('likedMovies', JSON.stringify(likedMoviesArray));\n                    console.log('🏠 Added to localStorage');\n                }\n            }\n            console.log(\"\\uD83C\\uDFE0 Successfully \".concat(isCurrentlyLiked ? 'unliked' : 'liked', \" wedding video: \").concat(videoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            const wasLiked = likedVideos.includes(videoId);\n            setLikedVideos((prev)=>wasLiked ? prev.filter((id)=>id !== videoId) : [\n                    ...prev,\n                    videoId\n                ]);\n        }\n    };\n    // Load like status from localStorage when videos are loaded\n    const loadLikeStatus = (videoIds)=>{\n        try {\n            const likedMoviesData = localStorage.getItem('likedMovies');\n            const likedMoviesArray = likedMoviesData ? JSON.parse(likedMoviesData) : [];\n            // Filter to only include videos that are currently loaded\n            const currentLikedVideos = videoIds.filter((id)=>likedMoviesArray.includes(id));\n            console.log('🏠 Loaded like status for home page movies:', currentLikedVideos);\n            setLikedVideos(currentLikedVideos);\n        } catch (error) {\n            console.error('🏠 Error loading like status:', error);\n            setLikedVideos([]);\n        }\n    };\n    // Function to handle Admire button click\n    const handleAdmire = async (userId)=>{\n        if (!userId) {\n            console.warn('No user ID provided for Admire action');\n            return;\n        }\n        // Check if already admiring this user\n        const isCurrentlyAdmiring = admiringUsers[userId] || false;\n        try {\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI state\n            setAdmiringUsers((prev)=>({\n                    ...prev,\n                    [userId]: !isCurrentlyAdmiring\n                }));\n            // Make API call to follow/unfollow user\n            const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n            console.log(\"Making request to \".concat(endpoint, \" with user ID: \").concat(userId));\n            try {\n                // Make the API call\n                const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(endpoint, {\n                    target_user_id: userId\n                }, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('API response:', response.data);\n                // Update localStorage with the new state\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (!isCurrentlyAdmiring) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                    console.log('Updated admired users in localStorage:', admiredUsers);\n                } catch (storageError) {\n                    console.error('Error updating localStorage:', storageError);\n                }\n            } catch (apiError) {\n                console.error(\"Error \".concat(isCurrentlyAdmiring ? 'unadmiring' : 'admiring', \" user:\"), apiError);\n                // Revert UI state on error\n                setAdmiringUsers((prev)=>({\n                        ...prev,\n                        [userId]: isCurrentlyAdmiring\n                    }));\n            }\n        } catch (error) {\n            console.error('Unexpected error in handleAdmire:', error);\n            // Revert UI state on unexpected errors\n            setAdmiringUsers((prev)=>({\n                    ...prev,\n                    [userId]: isCurrentlyAdmiring\n                }));\n        }\n    };\n    const handleManualScroll = (direction)=>{\n        if (scrollContainerRef.current) {\n            const scrollAmount = direction === \"left\" ? -694 : 694; // Width + gap\n            scrollContainerRef.current.scrollBy({\n                left: scrollAmount,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Get appropriate image source for a movie\n    const getImageSource = (video)=>{\n        if (video.video_thumbnail) {\n            return video.video_thumbnail;\n        }\n        return '/pics/placeholder.svg';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-[#000000]\",\n                        children: \"MOVIES\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-4 md:mr-8 lg:mr-[350px]\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 535,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading wedding videos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 534,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying wedding videos load...');\n                            setError(null);\n                            fetchMovies(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                lineNumber: 542,\n                columnNumber: 9\n            }, undefined),\n            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    weddingVideos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No wedding videos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 13\n                    }, undefined),\n                    weddingVideos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleManualScroll(\"left\"),\n                                style: {\n                                    marginLeft: \"-12px\"\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdChevronLeft, {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: scrollContainerRef,\n                                style: {\n                                    WebkitOverflowScrolling: \"touch\",\n                                    scrollbarWidth: \"none\",\n                                    msOverflowStyle: \"none\",\n                                    scrollBehavior: 'smooth'\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-3 pb-5 flex-nowrap w-full\",\n                                    children: [\n                                        weddingVideos.map((video, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px] max-w-[676px] border-b border-[#DBDBDB] pb-[15px] sm:pb-[21px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-full h-[40px] sm:h-[50px] md:h-[56px] flex justify-between items-center p-[5px] sm:p-[8px_5px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center gap-[10px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        onClick: ()=>navigateToUserProfile(video.user_id, video.user_name),\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-[34px] h-[34px] rounded-[27px] border cursor-pointer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            username: video.user_name || \"user\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>navigateToUserProfile(video.user_id, video.user_name)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 615,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-full max-w-[380px] h-[40px] pt-[7px] pr-[50px] sm:pr-[100px] md:pr-[150px] lg:pr-[200px] pb-[10px]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontFamily: \"Inter\",\n                                                                                fontWeight: 600,\n                                                                                fontSize: \"14px\",\n                                                                                lineHeight: \"18px\",\n                                                                                letterSpacing: \"0%\",\n                                                                                verticalAlign: \"middle\",\n                                                                                cursor: \"pointer\"\n                                                                            },\n                                                                            onClick: ()=>navigateToUserProfile(video.user_id, video.user_name),\n                                                                            className: \"jsx-83037452c623c470\" + \" \" + \"hover:underline\",\n                                                                            children: video.user_name || \"user\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 625,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 622,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleAdmire(video.user_id),\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center bg-[#B31B1E] text-white px-3 py-1 rounded-full text-sm font-medium hover:bg-red-700 transition-colors\",\n                                                                        children: [\n                                                                            admiringUsers[video.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[video.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-83037452c623c470\" + \" \" + \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 69\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 644,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: \"•••\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[180px] sm:h-[250px] md:h-[300px] lg:h-[350px] max-w-[676px] rounded-[10px] border p-[1px] relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full rounded-[10px] overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: getImageSource(video),\n                                                                    alt: video.video_name || \"Wedding Video\",\n                                                                    fill: true,\n                                                                    sizes: \"(max-width: 480px) 300px, (max-width: 640px) 350px, (max-width: 768px) 450px, (max-width: 1024px) 550px, 676px\",\n                                                                    className: \"object-cover\",\n                                                                    ...index < 2 ? {\n                                                                        priority: true\n                                                                    } : {\n                                                                        loading: 'lazy'\n                                                                    },\n                                                                    unoptimized: true,\n                                                                    placeholder: \"blur\" // Show blur placeholder while loading\n                                                                    ,\n                                                                    blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                                    ,\n                                                                    onError: (e)=>{\n                                                                        console.error(\"Failed to load image for video: \".concat(video.video_name));\n                                                                        // Use placeholder as fallback\n                                                                        const imgElement = e.target;\n                                                                        if (imgElement) {\n                                                                            imgElement.src = '/pics/placeholder.svg';\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 660,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: \"absolute\",\n                                                                    top: 0,\n                                                                    left: 0,\n                                                                    right: 0,\n                                                                    bottom: 0,\n                                                                    zIndex: 2,\n                                                                    borderRadius: \"10px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"center\",\n                                                                    cursor: \"pointer\",\n                                                                    backgroundColor: \"rgba(0, 0, 0, 0.2)\"\n                                                                },\n                                                                onClick: ()=>{\n                                                                    // Navigate to the movie detail page\n                                                                    if (video.video_id) {\n                                                                        console.log(\"Navigating to movie detail page: \".concat(video.video_id));\n                                                                        window.location.href = \"/home/<USER>/\".concat(video.video_id);\n                                                                    }\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-83037452c623c470\" + \" \" + \"w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"28\",\n                                                                        height: \"28\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"white\",\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M8 5v14l11-7z\",\n                                                                            className: \"jsx-83037452c623c470\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            padding: \"12px 0\",\n                                                            display: \"flex\",\n                                                            flexDirection: \"column\",\n                                                            gap: \"8px\"\n                                                        },\n                                                        className: \"jsx-83037452c623c470\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    gap: \"16px\",\n                                                                    alignItems: \"center\"\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>toggleLike(video.video_id),\n                                                                        title: likedVideos.includes(video.video_id) ? 'Unlike' : 'Like',\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 \".concat(likedVideos.includes(video.video_id) ? 'bg-red-100 hover:bg-red-200' : 'bg-gray-100 hover:bg-gray-200'),\n                                                                        children: likedVideos.includes(video.video_id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdFavorite, {\n                                                                            size: 24,\n                                                                            color: \"#B31B1E\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 31\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdFavoriteBorder, {\n                                                                            size: 24,\n                                                                            color: \"#666\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 736,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdOutlineModeComment, {\n                                                                            size: 24,\n                                                                            color: \"#666\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 752,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdOutlineShare, {\n                                                                            size: 24,\n                                                                            color: \"#666\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                            lineNumber: 755,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    gap: \"16px\",\n                                                                    fontSize: \"14px\",\n                                                                    color: \"#666\"\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: likedVideos.includes(video.video_id) ? \"#B31B1E\" : \"#666\"\n                                                                        },\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: [\n                                                                            video.video_likes ? \"\".concat((video.video_likes / 1000).toFixed(1), \"K\") : '0',\n                                                                            \" likes\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 767,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-83037452c623c470\",\n                                                                        children: [\n                                                                            video.video_views ? \"\".concat((video.video_views / 1000).toFixed(1), \"K\") : '0',\n                                                                            \" views\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                        lineNumber: 776,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"14px\",\n                                                                    display: \"flex\",\n                                                                    gap: \"4px\"\n                                                                },\n                                                                className: \"jsx-83037452c623c470\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-83037452c623c470\",\n                                                                    children: video.video_description || video.video_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, \"\".concat(video.video_id, \"-\").concat(index), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 21\n                                            }, undefined)),\n                                        hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: loadMoreTriggerRef,\n                                            style: {\n                                                position: 'relative',\n                                                // Add debug outline in development\n                                                outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                            },\n                                            \"aria-hidden\": \"true\",\n                                            \"data-testid\": \"wedding-videos-load-more-trigger\",\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                                    children: \"Loading more...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleManualScroll(\"right\"),\n                                style: {\n                                    marginRight: \"-12px\"\n                                },\n                                className: \"jsx-83037452c623c470\" + \" \" + \"absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdChevronLeft_MdChevronRight_MdFavorite_MdFavoriteBorder_MdOutlineModeComment_MdOutlineShare_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdChevronRight, {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                    lineNumber: 823,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\WeddingVideos.tsx\",\n        lineNumber: 519,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WeddingVideosSection, \"Xsa2w61/PqrUJlvDwsTfmOHtabU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = WeddingVideosSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WeddingVideosSection);\nvar _c;\n$RefreshReg$(_c, \"WeddingVideosSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvSG9tZURhc2hib2FyZC9XZWRkaW5nVmlkZW9zLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUMyRDtBQUM1QjtBQUNhO0FBQ047QUFDUztBQVF2QjtBQStCeEIsTUFBTWMsdUJBQXFEO1FBQUMsRUFBRUMsYUFBYSxLQUFLLEVBQUU7O0lBQ2hGLE1BQU1DLFNBQVNYLDBEQUFTQTtJQUN4QixNQUFNLENBQUNZLGFBQWFDLGVBQWUsR0FBR2pCLCtDQUFRQSxDQUFXLEVBQUU7SUFDM0QsTUFBTSxDQUFDa0IsZUFBZUMsaUJBQWlCLEdBQUduQiwrQ0FBUUEsQ0FBMEIsQ0FBQztJQUM3RSxNQUFNLENBQUNvQixlQUFlQyxpQkFBaUIsR0FBR3JCLCtDQUFRQSxDQUFlLEVBQUU7SUFDbkUsTUFBTSxDQUFDc0IsU0FBU0MsV0FBVyxHQUFHdkIsK0NBQVFBLENBQVU7SUFDaEQsTUFBTSxDQUFDd0IsT0FBT0MsU0FBUyxHQUFHekIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQzBCLE1BQU1DLFFBQVEsR0FBRzNCLCtDQUFRQSxDQUFTO0lBQ3pDLE1BQU0sQ0FBQzRCLFNBQVNDLFdBQVcsR0FBRzdCLCtDQUFRQSxDQUFVO0lBQ2hELE1BQU0sQ0FBQzhCLGFBQWFDLGVBQWUsR0FBRy9CLCtDQUFRQSxDQUFVO0lBQ3hELE1BQU0sQ0FBQ2dDLHFCQUFxQkMsdUJBQXVCLEdBQUdqQywrQ0FBUUEsQ0FBVTtJQUN4RSxNQUFNa0MscUJBQXFCakMsNkNBQU1BLENBQWlCO0lBQ2xELE1BQU1rQyxxQkFBcUJsQyw2Q0FBTUEsQ0FBaUI7SUFDbEQsTUFBTW1DLGNBQWNuQyw2Q0FBTUEsQ0FBOEI7SUFFeEQsaURBQWlEO0lBQ2pELE1BQU1vQyxvQkFBb0IsSUFBb0IsRUFBRTtJQUVoRCx3Q0FBd0M7SUFDeEMsTUFBTUMsY0FBYyxlQUFPQztZQUFvQkMsaUZBQXlCO1FBQ3RFLHlDQUF5QztRQUN6QyxJQUFJLGlCQUFrQmxCLFdBQWEsQ0FBQ2tCLGlCQUFpQlYsYUFBYztZQUNqRVcsUUFBUUMsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBLElBQUk7WUFDRixJQUFJRixlQUFlO2dCQUNqQmpCLFdBQVc7WUFDYixPQUFPO2dCQUNMUSxlQUFlO1lBQ2pCO1lBRUFVLFFBQVFDLEdBQUcsQ0FBQyw0QkFBdUMsT0FBWEgsWUFBVztZQUVuRCx5QkFBeUI7WUFDekIsTUFBTUksUUFBUUMsYUFBYUMsT0FBTyxDQUFDO1lBRW5DSixRQUFRQyxHQUFHLENBQUMscUJBQTBDLE9BQXJCQyxRQUFRLFFBQVE7WUFFakQsSUFBSSxDQUFDQSxPQUFPO2dCQUNWRixRQUFRSyxJQUFJLENBQUM7Z0JBQ2JyQixTQUFTO2dCQUNUSixpQkFBaUIsRUFBRTtnQkFDbkI7WUFDRjtZQUVBLG1CQUFtQjtZQUNuQixNQUFNMEIsV0FBVyxNQUFNekMsNkRBQUtBLENBQUMwQyxHQUFHLENBQWMsZ0JBQTJCLE9BQVhULFlBQVcsY0FBWTtnQkFDbkZVLFNBQVM7b0JBQ1BDLGVBQWUsVUFBZ0IsT0FBTlA7Z0JBQzNCO1lBQ0Y7WUFFQUYsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QkssU0FBU0ksTUFBTTtZQUVuRCxtQkFBbUI7WUFDbkIsSUFBSUosU0FBU0ssSUFBSSxJQUFJTCxTQUFTSyxJQUFJLENBQUNDLE1BQU0sRUFBRTtnQkFDekNaLFFBQVFDLEdBQUcsQ0FBQyxVQUF5REgsT0FBL0NRLFNBQVNLLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxNQUFNLEVBQUMscUJBQThCLE9BQVhmO2dCQUVyRSxtQ0FBbUM7Z0JBQ25DLElBQUlRLFNBQVNLLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxNQUFNLEdBQUcsR0FBRztvQkFDbkNiLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0JLLFNBQVNLLElBQUksQ0FBQ0MsTUFBTSxDQUFDLEVBQUU7Z0JBQzNEO2dCQUVBLHVCQUF1QjtnQkFDdkIsTUFBTUUsa0JBQWtCUixTQUFTSyxJQUFJLENBQUNDLE1BQU0sQ0FBQ0csR0FBRyxDQUFDQyxDQUFBQTtvQkFDL0MsSUFBSSxDQUFDQSxNQUFNQyxlQUFlLEVBQUU7d0JBQzFCakIsUUFBUUMsR0FBRyxDQUFDLDRCQUEyQyxPQUFmZSxNQUFNRSxRQUFRO29CQUN4RDtvQkFDQSw2REFBNkQ7b0JBQzdELElBQUksQ0FBQ0YsTUFBTUcsT0FBTyxJQUFJSCxNQUFNSSxTQUFTLEVBQUU7d0JBQ3JDLCtEQUErRDt3QkFDL0QsOEVBQThFO3dCQUM5RXBCLFFBQVFDLEdBQUcsQ0FBQyxnRUFBZ0YsT0FBaEJlLE1BQU1JLFNBQVM7d0JBQzNGSixNQUFNRyxPQUFPLEdBQUcsUUFBMkQsT0FBbkRILE1BQU1JLFNBQVMsQ0FBQ0MsV0FBVyxHQUFHQyxPQUFPLENBQUMsUUFBUTtvQkFDeEU7b0JBQ0EsT0FBT047Z0JBQ1Q7Z0JBRUEsSUFBSWxCLGVBQWUsR0FBRztvQkFDcEJsQixpQkFBaUJrQztvQkFDakIsc0NBQXNDO29CQUN0Q1MsZUFBZVQsZ0JBQWdCQyxHQUFHLENBQUMsQ0FBQ1MsUUFBZUEsTUFBTU4sUUFBUTtnQkFDbkUsT0FBTztvQkFDTHRDLGlCQUFpQjZDLENBQUFBLE9BQVE7K0JBQUlBOytCQUFTWDt5QkFBZ0I7b0JBQ3RELHVEQUF1RDtvQkFDdkQsTUFBTVksY0FBY1osZ0JBQWdCQyxHQUFHLENBQUMsQ0FBQ1MsUUFBZUEsTUFBTU4sUUFBUTtvQkFDdEUsTUFBTVMsMEJBQTBCeEIsYUFBYUMsT0FBTyxDQUFDO29CQUNyRCxNQUFNd0Isc0JBQXNCRCwwQkFBMEJFLEtBQUtDLEtBQUssQ0FBQ0gsMkJBQTJCLEVBQUU7b0JBQzlGLE1BQU1JLGlCQUFpQkwsWUFBWU0sTUFBTSxDQUFDLENBQUNDLEtBQWVMLG9CQUFvQk0sUUFBUSxDQUFDRDtvQkFDdkZ6RCxlQUFlaUQsQ0FBQUEsT0FBUTsrQkFBSUE7K0JBQVNNO3lCQUFlO2dCQUNyRDtnQkFFQTNDLFdBQVdrQixTQUFTSyxJQUFJLENBQUN3QixTQUFTO2dCQUNsQ2pELFFBQVFZLGFBQWEsMEJBQTBCO2dCQUMvQ2QsU0FBUyxPQUFPLDRCQUE0QjtZQUM5QyxPQUFPO2dCQUNMZ0IsUUFBUUssSUFBSSxDQUFDLCtCQUErQkMsU0FBU0ssSUFBSTtnQkFDekQzQixTQUFTO2dCQUNUSixpQkFBaUIsRUFBRTtZQUNyQjtRQUNGLEVBQUUsT0FBT3dELEtBQUs7WUFDWnBDLFFBQVFqQixLQUFLLENBQUMsdUJBQXVCcUQ7WUFDckNwRCxTQUFTO1lBQ1RKLGlCQUFpQixFQUFFO1FBQ3JCLFNBQVU7WUFDUiw4QkFBOEI7WUFDOUIsSUFBSW1CLGVBQWU7Z0JBQ2pCakIsV0FBVztnQkFDWGtCLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0xYLGVBQWU7Z0JBQ2ZVLFFBQVFDLEdBQUcsQ0FBQztZQUNkO1FBQ0Y7SUFDRjtJQUVBLDZEQUE2RDtJQUM3RHhDLGdEQUFTQTswQ0FBQztZQUNSLCtDQUErQztZQUMvQyxJQUFJWSxZQUFZO2dCQUNkMkIsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLG1DQUFtQztnQkFDbkNULHVCQUF1QjtnQkFFdkIscURBQXFEO2dCQUNyRFEsUUFBUUMsR0FBRyxDQUFDO2dCQUNabkIsV0FBVztnQkFFWCxvRUFBb0U7Z0JBQ3BFLE1BQU1vQixRQUFRQyxhQUFhQyxPQUFPLENBQUM7Z0JBRW5DLElBQUlGLE9BQU87b0JBQ1QsMEJBQTBCO29CQUMxQkYsUUFBUUMsR0FBRyxDQUFDO29CQUNacEMsNkRBQUtBLENBQUMwQyxHQUFHLENBQWUsMkJBQTBCO3dCQUNoREMsU0FBUzs0QkFDUEMsZUFBZSxVQUFnQixPQUFOUDt3QkFDM0I7b0JBQ0YsR0FDR21DLElBQUk7MERBQUMvQixDQUFBQTs0QkFDSk4sUUFBUUMsR0FBRyxDQUFDOzRCQUNaLElBQUlLLFNBQVNLLElBQUksSUFBSUwsU0FBU0ssSUFBSSxDQUFDQyxNQUFNLEVBQUU7Z0NBQ3pDaEMsaUJBQWlCMEIsU0FBU0ssSUFBSSxDQUFDQyxNQUFNO2dDQUNyQ3hCLFdBQVdrQixTQUFTSyxJQUFJLENBQUN3QixTQUFTO2dDQUNsQ2pELFFBQVE7Z0NBQ1JGLFNBQVM7Z0NBRVQsMENBQTBDO2dDQUMxQ3VDLGVBQWVqQixTQUFTSyxJQUFJLENBQUNDLE1BQU0sQ0FBQ0csR0FBRztzRUFBQyxDQUFDUyxRQUFlQSxNQUFNTixRQUFROzs0QkFDeEUsT0FBTztnQ0FDTHRDLGlCQUFpQixFQUFFO2dDQUNuQkksU0FBUzs0QkFDWDt3QkFDRjt5REFDQ3NELEtBQUs7MERBQUNGLENBQUFBOzRCQUNMcEMsUUFBUWpCLEtBQUssQ0FBQyw4QkFBOEJxRDs0QkFDNUNwRCxTQUFTOzRCQUNUSixpQkFBaUIsRUFBRTt3QkFDckI7eURBQ0MyRCxPQUFPOzBEQUFDOzRCQUNQekQsV0FBVzs0QkFDWGtCLFFBQVFDLEdBQUcsQ0FBQzt3QkFDZDs7Z0JBQ0osT0FBTztvQkFDTG5CLFdBQVc7b0JBQ1hFLFNBQVM7Z0JBQ1g7WUFDRjtRQUNGO3lDQUFHO1FBQUNYO0tBQVcsR0FBRyw0QkFBNEI7SUFFOUMsdUVBQXVFO0lBQ3ZFWixnREFBU0E7MENBQUM7WUFDUix1RUFBdUU7WUFDdkUsSUFBSStFLFlBQW1DO1lBRXZDLElBQUkzRCxXQUFXVSxxQkFBcUI7Z0JBQ2xDaUQsWUFBWUM7c0RBQVc7d0JBQ3JCekMsUUFBUUMsR0FBRyxDQUFDO3dCQUNabkIsV0FBVzt3QkFDWCxJQUFJSCxjQUFja0MsTUFBTSxLQUFLLEdBQUc7NEJBQzlCN0IsU0FBUzt3QkFDWDtvQkFDRjtxREFBRyxRQUFRLHNDQUFzQztZQUNuRDtZQUVBO2tEQUFPO29CQUNMLElBQUl3RCxXQUFXRSxhQUFhRjtnQkFDOUI7O1FBQ0Y7eUNBQUc7UUFBQzNEO1FBQVNVO1FBQXFCWixjQUFja0MsTUFBTTtLQUFDO0lBRXZELDBEQUEwRDtJQUMxRHBELGdEQUFTQTswQ0FBQztZQUNSLCtFQUErRTtZQUMvRSxJQUFJLENBQUM4Qix1QkFBdUIsQ0FBQ0osU0FBUztZQUV0Qyw0Q0FBNEM7WUFDNUMsSUFBSVEsWUFBWWdELE9BQU8sRUFBRTtnQkFDdkJoRCxZQUFZZ0QsT0FBTyxDQUFDQyxVQUFVO2dCQUM5QjVDLFFBQVFDLEdBQUcsQ0FBQztZQUNkO1lBRUEsc0JBQXNCO1lBQ3RCTixZQUFZZ0QsT0FBTyxHQUFHLElBQUlFO2tEQUN4QixDQUFDQzt3QkFFS0E7b0JBREosa0VBQWtFO29CQUNsRSxJQUFJQSxFQUFBQSxZQUFBQSxPQUFPLENBQUMsRUFBRSxjQUFWQSxnQ0FBQUEsVUFBWUMsY0FBYyxLQUFJLENBQUNsRSxXQUFXLENBQUNRLGVBQWVGLFNBQVM7d0JBQ3JFYSxRQUFRQyxHQUFHLENBQUM7d0JBQ1pELFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUI2QyxPQUFPLENBQUMsRUFBRSxDQUFDRSxpQkFBaUI7d0JBQy9ELE1BQU1DLFdBQVdoRSxPQUFPO3dCQUN4QlksWUFBWW9ELFVBQVU7b0JBQ3hCO2dCQUNGO2lEQUNBLHFFQUFxRTtZQUNyRTtnQkFDRUMsTUFBTXpELG1CQUFtQmtELE9BQU87Z0JBQ2hDUSxXQUFXO2dCQUNYQyxZQUFZLG9CQUFvQiwwQ0FBMEM7WUFDNUU7WUFHRixzQ0FBc0M7WUFDdEMsSUFBSTFELG1CQUFtQmlELE9BQU8sRUFBRTtnQkFDOUJoRCxZQUFZZ0QsT0FBTyxDQUFDVSxPQUFPLENBQUMzRCxtQkFBbUJpRCxPQUFPO2dCQUN0RDNDLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0xELFFBQVFLLElBQUksQ0FBQztZQUNmO1lBRUE7a0RBQU87b0JBQ0wsSUFBSVYsWUFBWWdELE9BQU8sRUFBRTt3QkFDdkJoRCxZQUFZZ0QsT0FBTyxDQUFDQyxVQUFVO3dCQUM5QjVDLFFBQVFDLEdBQUcsQ0FBQztvQkFDZDtnQkFDRjs7UUFDRjt5Q0FBRztRQUFDZDtRQUFTTjtRQUFTUTtRQUFhSjtRQUFNTTtLQUFvQjtJQUU3RCwrREFBK0Q7SUFDL0Q5QixnREFBU0E7MENBQUM7WUFDUixJQUFJO2dCQUNGLE1BQU02RixtQkFBbUJuRCxhQUFhQyxPQUFPLENBQUM7Z0JBQzlDLElBQUlrRCxrQkFBa0I7b0JBQ3BCLE1BQU1DLGVBQWUxQixLQUFLQyxLQUFLLENBQUN3QjtvQkFDaEM1RSxpQkFBaUI2RTtvQkFDakJ2RCxRQUFRQyxHQUFHLENBQUMsNkNBQTZDc0Q7Z0JBQzNEO1lBQ0YsRUFBRSxPQUFPeEUsT0FBTztnQkFDZGlCLFFBQVFqQixLQUFLLENBQUMsb0RBQW9EQTtZQUNwRTtRQUNGO3lDQUFHLEVBQUU7SUFFTCwyQ0FBMkM7SUFDM0MsTUFBTXlFLHdCQUF3QixDQUFDQyxRQUFpQkM7UUFDOUMsMkVBQTJFO1FBQzNFLElBQUlELFFBQVE7WUFDVixrQ0FBa0M7WUFDbEMsTUFBTXZELFFBQVFDLGFBQWFDLE9BQU8sQ0FBQyxZQUNqQ0QsYUFBYUMsT0FBTyxDQUFDLGdCQUNyQkQsYUFBYUMsT0FBTyxDQUFDO1lBRXZCLElBQUksQ0FBQ0YsT0FBTztnQkFDVkYsUUFBUUssSUFBSSxDQUFDO2dCQUNiO1lBQ0Y7WUFFQSwrQ0FBK0M7WUFDL0MvQixPQUFPcUYsSUFBSSxDQUFDLFlBQW1CLE9BQVBGO1FBQzFCLE9BQ0s7WUFDSHpELFFBQVFLLElBQUksQ0FBQztRQUNmO0lBQ0Y7SUFFQSxNQUFNdUQsYUFBYSxPQUFPQztRQUN4QjdELFFBQVFDLEdBQUcsQ0FBQyxrREFBa0Q0RDtRQUM5RCxJQUFJO1lBQ0YsTUFBTTNELFFBQVFDLGFBQWFDLE9BQU8sQ0FBQyxZQUNqQ0QsYUFBYUMsT0FBTyxDQUFDLGdCQUNyQkQsYUFBYUMsT0FBTyxDQUFDO1lBRXZCLElBQUksQ0FBQ0YsT0FBTztnQkFDVkYsUUFBUUssSUFBSSxDQUFDO2dCQUNiO1lBQ0Y7WUFFQSwyQkFBMkI7WUFDM0IsTUFBTXlELG1CQUFtQnZGLFlBQVkyRCxRQUFRLENBQUMyQjtZQUM5QzdELFFBQVFDLEdBQUcsQ0FBQywyQkFBMkI2RDtZQUV2Q3RGLGVBQWUsQ0FBQ2lELE9BQ2RxQyxtQkFDSXJDLEtBQUtPLE1BQU0sQ0FBQyxDQUFDQyxLQUFPQSxPQUFPNEIsV0FDM0I7dUJBQUlwQztvQkFBTW9DO2lCQUFRO1lBR3hCLHNDQUFzQztZQUN0QyxNQUFNRSxXQUFXRCxtQkFBbUIsZ0JBQWdCO1lBQ3BEOUQsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQjhEO1lBRXRDLE1BQU16RCxXQUFXLE1BQU0wRCxNQUFNRCxVQUFVO2dCQUNyQ0UsUUFBUTtnQkFDUnpELFNBQVM7b0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU5OO29CQUMzQixnQkFBZ0I7Z0JBQ2xCO2dCQUNBZ0UsTUFBTXJDLEtBQUtzQyxTQUFTLENBQUM7b0JBQ25CQyxZQUFZUDtvQkFDWlEsY0FBYztnQkFDaEI7WUFDRjtZQUVBLElBQUksQ0FBQy9ELFNBQVNnRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1DLFlBQVksTUFBTWpFLFNBQVNrRSxJQUFJO2dCQUNyQ3hFLFFBQVFqQixLQUFLLENBQUMsMEJBQTBCd0Y7Z0JBQ3hDLE1BQU0sSUFBSUUsTUFBTSx1QkFBdUMsT0FBaEJuRSxTQUFTSSxNQUFNO1lBQ3hEO1lBRUEsTUFBTWdFLGVBQWUsTUFBTXBFLFNBQVNxRSxJQUFJO1lBQ3hDM0UsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QnlFO1lBRXhDLHNDQUFzQztZQUN0QyxNQUFNRSxrQkFBa0J6RSxhQUFhQyxPQUFPLENBQUM7WUFDN0MsTUFBTXlFLG1CQUFtQkQsa0JBQWtCL0MsS0FBS0MsS0FBSyxDQUFDOEMsbUJBQW1CLEVBQUU7WUFFM0UsSUFBSWQsa0JBQWtCO2dCQUNwQiwyQkFBMkI7Z0JBQzNCLE1BQU1nQixlQUFlRCxpQkFBaUI3QyxNQUFNLENBQUMsQ0FBQ0MsS0FBZUEsT0FBTzRCO2dCQUNwRTFELGFBQWE0RSxPQUFPLENBQUMsZUFBZWxELEtBQUtzQyxTQUFTLENBQUNXO2dCQUNuRDlFLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0wsc0JBQXNCO2dCQUN0QixJQUFJLENBQUM0RSxpQkFBaUIzQyxRQUFRLENBQUMyQixVQUFVO29CQUN2Q2dCLGlCQUFpQmxCLElBQUksQ0FBQ0U7b0JBQ3RCMUQsYUFBYTRFLE9BQU8sQ0FBQyxlQUFlbEQsS0FBS3NDLFNBQVMsQ0FBQ1U7b0JBQ25EN0UsUUFBUUMsR0FBRyxDQUFDO2dCQUNkO1lBQ0Y7WUFFQUQsUUFBUUMsR0FBRyxDQUFDLDZCQUE0RTRELE9BQXpEQyxtQkFBbUIsWUFBWSxTQUFRLG9CQUEwQixPQUFSRDtRQUMxRixFQUFFLE9BQU85RSxPQUFPO1lBQ2RpQixRQUFRakIsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdEMsb0NBQW9DO1lBQ3BDLE1BQU1pRyxXQUFXekcsWUFBWTJELFFBQVEsQ0FBQzJCO1lBQ3RDckYsZUFBZSxDQUFDaUQsT0FDZHVELFdBQ0l2RCxLQUFLTyxNQUFNLENBQUMsQ0FBQ0MsS0FBT0EsT0FBTzRCLFdBQzNCO3VCQUFJcEM7b0JBQU1vQztpQkFBUTtRQUUxQjtJQUNGO0lBRUEsNERBQTREO0lBQzVELE1BQU10QyxpQkFBaUIsQ0FBQzBEO1FBQ3RCLElBQUk7WUFDRixNQUFNTCxrQkFBa0J6RSxhQUFhQyxPQUFPLENBQUM7WUFDN0MsTUFBTXlFLG1CQUFtQkQsa0JBQWtCL0MsS0FBS0MsS0FBSyxDQUFDOEMsbUJBQW1CLEVBQUU7WUFFM0UsMERBQTBEO1lBQzFELE1BQU1NLHFCQUFxQkQsU0FBU2pELE1BQU0sQ0FBQ0MsQ0FBQUEsS0FBTTRDLGlCQUFpQjNDLFFBQVEsQ0FBQ0Q7WUFFM0VqQyxRQUFRQyxHQUFHLENBQUMsK0NBQStDaUY7WUFDM0QxRyxlQUFlMEc7UUFDakIsRUFBRSxPQUFPbkcsT0FBTztZQUNkaUIsUUFBUWpCLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DUCxlQUFlLEVBQUU7UUFDbkI7SUFDRjtJQUVBLHlDQUF5QztJQUN6QyxNQUFNMkcsZUFBZSxPQUFPMUI7UUFDMUIsSUFBSSxDQUFDQSxRQUFRO1lBQ1h6RCxRQUFRSyxJQUFJLENBQUM7WUFDYjtRQUNGO1FBRUEsc0NBQXNDO1FBQ3RDLE1BQU0rRSxzQkFBc0IzRyxhQUFhLENBQUNnRixPQUFPLElBQUk7UUFFckQsSUFBSTtZQUNGLDhCQUE4QjtZQUM5QixNQUFNdkQsUUFBUUMsYUFBYUMsT0FBTyxDQUFDLFlBQ2pDRCxhQUFhQyxPQUFPLENBQUMsZ0JBQ3JCRCxhQUFhQyxPQUFPLENBQUM7WUFFdkIsSUFBSSxDQUFDRixPQUFPO2dCQUNWRixRQUFRSyxJQUFJLENBQUM7Z0JBQ2I7WUFDRjtZQUVBLGlDQUFpQztZQUNqQzNCLGlCQUFpQitDLENBQUFBLE9BQVM7b0JBQ3hCLEdBQUdBLElBQUk7b0JBQ1AsQ0FBQ2dDLE9BQU8sRUFBRSxDQUFDMkI7Z0JBQ2I7WUFFQSx3Q0FBd0M7WUFDeEMsTUFBTXJCLFdBQVdxQixzQkFDYiw4RUFDQTtZQUVKcEYsUUFBUUMsR0FBRyxDQUFDLHFCQUErQ3dELE9BQTFCTSxVQUFTLG1CQUF3QixPQUFQTjtZQUUzRCxJQUFJO2dCQUNGLG9CQUFvQjtnQkFDcEIsTUFBTW5ELFdBQVcsTUFBTXpDLDZEQUFLQSxDQUFDd0gsSUFBSSxDQUMvQnRCLFVBQ0E7b0JBQUV1QixnQkFBZ0I3QjtnQkFBTyxHQUN6QjtvQkFDRWpELFNBQVM7d0JBQ1BDLGVBQWUsVUFBZ0IsT0FBTlA7d0JBQ3pCLGdCQUFnQjtvQkFDbEI7Z0JBQ0Y7Z0JBR0ZGLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJLLFNBQVNLLElBQUk7Z0JBRTFDLHlDQUF5QztnQkFDekMsSUFBSTtvQkFDRixNQUFNMkMsbUJBQW1CbkQsYUFBYUMsT0FBTyxDQUFDLG1CQUFtQjtvQkFDakUsTUFBTW1ELGVBQWUxQixLQUFLQyxLQUFLLENBQUN3QjtvQkFFaEMsSUFBSSxDQUFDOEIscUJBQXFCO3dCQUN4QjdCLFlBQVksQ0FBQ0UsT0FBTyxHQUFHO29CQUN6QixPQUFPO3dCQUNMLE9BQU9GLFlBQVksQ0FBQ0UsT0FBTztvQkFDN0I7b0JBRUF0RCxhQUFhNEUsT0FBTyxDQUFDLGdCQUFnQmxELEtBQUtzQyxTQUFTLENBQUNaO29CQUNwRHZELFFBQVFDLEdBQUcsQ0FBQywwQ0FBMENzRDtnQkFDeEQsRUFBRSxPQUFPZ0MsY0FBYztvQkFDckJ2RixRQUFRakIsS0FBSyxDQUFDLGdDQUFnQ3dHO2dCQUNoRDtZQUNGLEVBQUUsT0FBT0MsVUFBZTtnQkFDdEJ4RixRQUFRakIsS0FBSyxDQUFDLFNBQXlELE9BQWhEcUcsc0JBQXNCLGVBQWUsWUFBVyxXQUFTSTtnQkFFaEYsMkJBQTJCO2dCQUMzQjlHLGlCQUFpQitDLENBQUFBLE9BQVM7d0JBQ3hCLEdBQUdBLElBQUk7d0JBQ1AsQ0FBQ2dDLE9BQU8sRUFBRTJCO29CQUNaO1lBQ0Y7UUFDRixFQUFFLE9BQU9yRyxPQUFPO1lBQ2RpQixRQUFRakIsS0FBSyxDQUFDLHFDQUFxQ0E7WUFFbkQsdUNBQXVDO1lBQ3ZDTCxpQkFBaUIrQyxDQUFBQSxPQUFTO29CQUN4QixHQUFHQSxJQUFJO29CQUNQLENBQUNnQyxPQUFPLEVBQUUyQjtnQkFDWjtRQUNGO0lBQ0Y7SUFFQSxNQUFNSyxxQkFBcUIsQ0FBQ0M7UUFDMUIsSUFBSWpHLG1CQUFtQmtELE9BQU8sRUFBRTtZQUM5QixNQUFNZ0QsZUFBZUQsY0FBYyxTQUFTLENBQUMsTUFBTSxLQUFLLGNBQWM7WUFDdEVqRyxtQkFBbUJrRCxPQUFPLENBQUNpRCxRQUFRLENBQUM7Z0JBQ2xDQyxNQUFNRjtnQkFDTkcsVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUVBLDJDQUEyQztJQUMzQyxNQUFNQyxpQkFBaUIsQ0FBQ3ZFO1FBQ3RCLElBQUlBLE1BQU1QLGVBQWUsRUFBRTtZQUN6QixPQUFPTyxNQUFNUCxlQUFlO1FBQzlCO1FBQ0EsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUMrRTtrREFBa0I7OzBCQUNqQiw4REFBQ0M7MERBQWM7O2tDQUNiLDhEQUFDQztrRUFBYTtrQ0FBcUU7Ozs7OztrQ0FHbkYsOERBQUNDO3dCQUNDQyxNQUFLO2tFQUNLO2tDQUNYOzs7Ozs7Ozs7Ozs7WUFNRnZILHlCQUNDLDhEQUFDb0g7MERBQWM7O2tDQUNiLDhEQUFDQTtrRUFBYzs7Ozs7O2tDQUNmLDhEQUFDSTtrRUFBZTtrQ0FBZ0I7Ozs7Ozs7Ozs7OztZQUtuQ3RILFNBQVMsQ0FBQ0YseUJBQ1QsOERBQUNvSDswREFBYzs7a0NBQ2IsOERBQUNBO2tFQUFjO2tDQUFxQmxIOzs7Ozs7a0NBQ3BDLDhEQUFDdUg7d0JBQ0NDLFNBQVM7NEJBQ1B2RyxRQUFRQyxHQUFHLENBQUM7NEJBQ1pqQixTQUFTOzRCQUNUYSxZQUFZLEdBQUc7d0JBQ2pCO2tFQUNVO2tDQUNYOzs7Ozs7Ozs7Ozs7WUFpQkosQ0FBQ2hCLFdBQVcsQ0FBQ0UsdUJBQ1o7O29CQUVHSixjQUFja0MsTUFBTSxLQUFLLG1CQUN4Qiw4REFBQ29GO2tFQUFjO2tDQUNiLDRFQUFDQTtzRUFBYztzQ0FBZ0I7Ozs7Ozs7Ozs7O29CQUlsQ3RILGNBQWNrQyxNQUFNLEdBQUcsbUJBQ3RCOzswQ0FFRSw4REFBQ3lGO2dDQUNDQyxTQUFTLElBQU1kLG1CQUFtQjtnQ0FFbENlLE9BQU87b0NBQUVDLFlBQVk7Z0NBQVE7MEVBRG5COzBDQUdWLDRFQUFDdkksNktBQWFBO29DQUFDd0ksTUFBTTs7Ozs7Ozs7Ozs7MENBR3ZCLDhEQUFDVDtnQ0FDQ1UsS0FBS2xIO2dDQUVMK0csT0FBTztvQ0FDTEkseUJBQXlCO29DQUN6QkMsZ0JBQWdCO29DQUNoQkMsaUJBQWlCO29DQUNqQkMsZ0JBQWdCO2dDQUNsQjswRUFOVTswQ0FRViw0RUFBQ2Q7OEVBQWM7O3dDQUNadEgsY0FBY29DLEdBQUcsQ0FBQyxDQUFDUyxPQUFPd0Ysc0JBQ3pCLDhEQUFDZjswRkFFVzs7a0VBR1YsOERBQUNBO2tHQUNXOzswRUFFViw4REFBQ0E7MEdBQ1c7O2tGQUVWLDhEQUFDQTt3RUFFQ00sU0FBUyxJQUFNL0Msc0JBQXNCaEMsTUFBTUwsT0FBTyxFQUFFSyxNQUFNSixTQUFTO2tIQUR6RDtrRkFHViw0RUFBQ3hELG1EQUFVQTs0RUFDVDhGLFVBQVVsQyxNQUFNSixTQUFTLElBQUk7NEVBQzdCc0YsTUFBSzs0RUFDTEgsU0FBUyxJQUFNL0Msc0JBQXNCaEMsTUFBTUwsT0FBTyxFQUFFSyxNQUFNSixTQUFTOzs7Ozs7Ozs7OztrRkFJdkUsOERBQUM2RTtrSEFDVztrRkFFViw0RUFBQ0k7NEVBQ0NHLE9BQU87Z0ZBQ0xTLFlBQVk7Z0ZBQ1pDLFlBQVk7Z0ZBQ1pDLFVBQVU7Z0ZBQ1ZDLFlBQVk7Z0ZBQ1pDLGVBQWU7Z0ZBQ2ZDLGVBQWU7Z0ZBQ2ZDLFFBQVE7NEVBQ1Y7NEVBQ0FoQixTQUFTLElBQU0vQyxzQkFBc0JoQyxNQUFNTCxPQUFPLEVBQUVLLE1BQU1KLFNBQVM7c0hBQ3pEO3NGQUVUSSxNQUFNSixTQUFTLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUsxQiw4REFBQzZFOzBHQUFjOztrRkFDYiw4REFBQ0s7d0VBQ0NDLFNBQVMsSUFBTXBCLGFBQWEzRCxNQUFNTCxPQUFPO2tIQUMvQjs7NEVBRVQxQyxhQUFhLENBQUMrQyxNQUFNTCxPQUFPLElBQUksR0FBRyxHQUFHLGFBQWE7NEVBQ2xELENBQUMxQyxhQUFhLENBQUMrQyxNQUFNTCxPQUFPLElBQUksR0FBRyxrQkFBSSw4REFBQ2tGOzBIQUFlOzBGQUFPOzs7Ozs7Ozs7Ozs7a0ZBRWpFLDhEQUFDQzs7a0ZBQU87Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLWiw4REFBQ0w7a0dBQ1c7OzBFQUdWLDhEQUFDQTswR0FBYzswRUFDYiw0RUFBQ3ZJLGtEQUFLQTtvRUFDSjhKLEtBQUt6QixlQUFldkU7b0VBQ3BCaUcsS0FBS2pHLE1BQU1rRyxVQUFVLElBQUk7b0VBQ3pCQyxJQUFJO29FQUNKQyxPQUFNO29FQUNOQyxXQUFVO29FQUNULEdBQUliLFFBQVEsSUFBSTt3RUFBRWMsVUFBVTtvRUFBSyxJQUFJO3dFQUFFakosU0FBUztvRUFBTyxDQUFDO29FQUN6RGtKLGFBQWE7b0VBQ2JDLGFBQVksT0FBTyxzQ0FBc0M7O29FQUN6REMsYUFBWSx5ckJBQXlyQix1Q0FBdUM7O29FQUM1dUJDLFNBQVMsQ0FBQ0M7d0VBQ1JuSSxRQUFRakIsS0FBSyxDQUFDLG1DQUFvRCxPQUFqQnlDLE1BQU1rRyxVQUFVO3dFQUNqRSw4QkFBOEI7d0VBQzlCLE1BQU1VLGFBQWFELEVBQUVFLE1BQU07d0VBQzNCLElBQUlELFlBQVk7NEVBQ2RBLFdBQVdaLEdBQUcsR0FBRzt3RUFDbkI7b0VBQ0Y7Ozs7Ozs7Ozs7OzBFQUtKLDhEQUFDdkI7Z0VBQ0NPLE9BQU87b0VBQ0w4QixVQUFVO29FQUNWQyxLQUFLO29FQUNMMUMsTUFBTTtvRUFDTjJDLE9BQU87b0VBQ1BDLFFBQVE7b0VBQ1JDLFFBQVE7b0VBQ1JDLGNBQWM7b0VBQ2RDLFNBQVM7b0VBQ1RDLFlBQVk7b0VBQ1pDLGdCQUFnQjtvRUFDaEJ2QixRQUFRO29FQUNSd0IsaUJBQWlCO2dFQUNuQjtnRUFDQXhDLFNBQVM7b0VBQ1Asb0NBQW9DO29FQUNwQyxJQUFJL0UsTUFBTU4sUUFBUSxFQUFFO3dFQUNsQmxCLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBbUQsT0FBZnVCLE1BQU1OLFFBQVE7d0VBQzlEOEgsT0FBT0MsUUFBUSxDQUFDN0MsSUFBSSxHQUFHLGdCQUErQixPQUFmNUUsTUFBTU4sUUFBUTtvRUFDdkQ7Z0VBQ0Y7OzBFQUVBLDRFQUFDK0U7OEdBQWM7OEVBQ2IsNEVBQUNpRDt3RUFDQ0MsT0FBTTt3RUFDTkMsT0FBTTt3RUFDTkMsUUFBTzt3RUFDUEMsU0FBUTt3RUFDUjNCLE1BQUs7O2tGQUVMLDRFQUFDNEI7NEVBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBT2hCLDhEQUFDdkQ7d0RBQ0NPLE9BQU87NERBQ0xpRCxTQUFTOzREQUNUYixTQUFTOzREQUNUYyxlQUFlOzREQUNmQyxLQUFLO3dEQUNQOzs7MEVBRUEsOERBQUMxRDtnRUFDQ08sT0FBTztvRUFDTG9DLFNBQVM7b0VBQ1RlLEtBQUs7b0VBQ0xkLFlBQVk7Z0VBQ2Q7OztrRkFFQSw4REFBQ3ZDO3dFQUNDQyxTQUFTLElBQU0zQyxXQUFXcEMsTUFBTU4sUUFBUTt3RUFNeEMwSSxPQUFPckwsWUFBWTJELFFBQVEsQ0FBQ1YsTUFBTU4sUUFBUSxJQUFJLFdBQVc7a0hBTDlDLHVGQUlWLE9BSEMzQyxZQUFZMkQsUUFBUSxDQUFDVixNQUFNTixRQUFRLElBQy9CLGdDQUNBO2tGQUlMM0MsWUFBWTJELFFBQVEsQ0FBQ1YsTUFBTU4sUUFBUSxrQkFDbEMsOERBQUNwRCwwS0FBVUE7NEVBQUM0SSxNQUFNOzRFQUFJbUQsT0FBTTs7Ozs7c0dBRTVCLDhEQUFDOUwsZ0xBQWdCQTs0RUFBQzJJLE1BQU07NEVBQUltRCxPQUFNOzs7Ozs7Ozs7OztrRkFHdEMsOERBQUN2RDtrSEFBaUI7a0ZBQ2hCLDRFQUFDdEksb0xBQW9CQTs0RUFBQzBJLE1BQU07NEVBQUltRCxPQUFNOzs7Ozs7Ozs7OztrRkFFeEMsOERBQUN2RDtrSEFBaUI7a0ZBQ2hCLDRFQUFDckksOEtBQWNBOzRFQUFDeUksTUFBTTs0RUFBSW1ELE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUlwQyw4REFBQzVEO2dFQUNDTyxPQUFPO29FQUNMb0MsU0FBUztvRUFDVGUsS0FBSztvRUFDTHhDLFVBQVU7b0VBQ1YwQyxPQUFPO2dFQUNUOzs7a0ZBRUEsOERBQUN4RDt3RUFDQ0csT0FBTzs0RUFDTHFELE9BQU90TCxZQUFZMkQsUUFBUSxDQUFDVixNQUFNTixRQUFRLElBQ3RDLFlBQ0E7d0VBQ047Ozs0RUFFQ00sTUFBTXNJLFdBQVcsR0FBRyxHQUF5QyxPQUF0QyxDQUFDdEksTUFBTXNJLFdBQVcsR0FBRyxJQUFHLEVBQUdDLE9BQU8sQ0FBQyxJQUFHLE9BQUs7NEVBQUk7Ozs7Ozs7a0ZBRXpFLDhEQUFDMUQ7Ozs0RUFBTTdFLE1BQU13SSxXQUFXLEdBQUcsR0FBeUMsT0FBdEMsQ0FBQ3hJLE1BQU13SSxXQUFXLEdBQUcsSUFBRyxFQUFHRCxPQUFPLENBQUMsSUFBRyxPQUFLOzRFQUFJOzs7Ozs7Ozs7Ozs7OzBFQUcvRSw4REFBQzlEO2dFQUNDTyxPQUFPO29FQUNMVyxVQUFVO29FQUNWeUIsU0FBUztvRUFDVGUsS0FBSztnRUFDUDs7MEVBRUEsNEVBQUN0RDs7OEVBQU03RSxNQUFNeUksaUJBQWlCLElBQUl6SSxNQUFNa0csVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OytDQXpMakQsR0FBcUJWLE9BQWxCeEYsTUFBTU4sUUFBUSxFQUFDLEtBQVMsT0FBTjhGOzs7Ozt3Q0FnTTdCN0gseUJBQ0MsOERBQUM4Rzs0Q0FDQ1UsS0FBS2pIOzRDQUVMOEcsT0FBTztnREFDTDhCLFVBQVU7Z0RBQ1YsbUNBQW1DO2dEQUNuQzRCLFNBQVNDLEtBQXNDLEdBQUcsb0NBQW9DLENBQU07NENBQzlGOzRDQUNBQyxlQUFZOzRDQUNaQyxlQUFZO3NGQVBGOzs7Ozs7d0NBWWJoTCw2QkFDQyw4REFBQzRHO3NGQUFjOzs4REFDYiw4REFBQ0E7OEZBQWM7Ozs7Ozs4REFDZiw4REFBQ0k7OEZBQWU7OERBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPckQsOERBQUNDO2dDQUNDQyxTQUFTLElBQU1kLG1CQUFtQjtnQ0FFbENlLE9BQU87b0NBQUU4RCxhQUFhO2dDQUFROzBFQURwQjswQ0FHViw0RUFBQ25NLDhLQUFjQTtvQ0FBQ3VJLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFrQnRDO0dBNXhCTXRJOztRQUNXVCxzREFBU0E7OztLQURwQlM7QUE4eEJOLGlFQUFlQSxvQkFBb0JBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2l2YXNcXE9uZURyaXZlXFxEZXNrdG9wXFxmaW5hbFxcV0VEWkFUX1xcZnJvbnRlbmRcXGNvbXBvbmVudHNcXEhvbWVEYXNoYm9hcmRcXFdlZGRpbmdWaWRlb3MudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcbmltcG9ydCBVc2VyQXZhdGFyIGZyb20gXCIuL1VzZXJBdmF0YXJcIjtcclxuaW1wb3J0IGF4aW9zIGZyb20gXCIuLi8uLi9zZXJ2aWNlcy9heGlvc0NvbmZpZ1wiO1xyXG5pbXBvcnQge1xyXG4gIE1kRmF2b3JpdGUsXHJcbiAgTWRGYXZvcml0ZUJvcmRlcixcclxuICBNZE91dGxpbmVNb2RlQ29tbWVudCxcclxuICBNZE91dGxpbmVTaGFyZSxcclxuICBNZENoZXZyb25MZWZ0LFxyXG4gIE1kQ2hldnJvblJpZ2h0LFxyXG59IGZyb20gXCJyZWFjdC1pY29ucy9tZFwiO1xyXG5cclxuLy8gRGVmaW5lIGludGVyZmFjZSBmb3IgbW92aWUgdmlkZW8gaXRlbXNcclxuaW50ZXJmYWNlIE1vdmllVmlkZW8ge1xyXG4gIHZpZGVvX2lkOiBzdHJpbmc7XHJcbiAgdmlkZW9fbmFtZTogc3RyaW5nO1xyXG4gIHZpZGVvX3VybDogc3RyaW5nO1xyXG4gIHZpZGVvX2Rlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIHZpZGVvX3RodW1ibmFpbD86IHN0cmluZztcclxuICB2aWRlb19kdXJhdGlvbj86IG51bWJlcjtcclxuICB2aWRlb19jYXRlZ29yeT86IHN0cmluZztcclxuICBjcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgdXNlcl9uYW1lPzogc3RyaW5nO1xyXG4gIHVzZXJfaWQ/OiBzdHJpbmc7IC8vIEFkZGVkIHVzZXJfaWQgZmllbGRcclxuICBpc19vd25fY29udGVudD86IGJvb2xlYW47XHJcbiAgdmlkZW9fdmlld3M/OiBudW1iZXI7XHJcbiAgdmlkZW9fbGlrZXM/OiBudW1iZXI7XHJcbiAgdmlkZW9fY29tbWVudHM/OiBudW1iZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBBcGlSZXNwb25zZSB7XHJcbiAgbW92aWVzOiBNb3ZpZVZpZGVvW107XHJcbiAgbmV4dF9wYWdlOiBib29sZWFuO1xyXG4gIHRvdGFsX2NvdW50OiBudW1iZXI7XHJcbiAgY3VycmVudF9wYWdlOiBudW1iZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBXZWRkaW5nVmlkZW9zUHJvcHMge1xyXG4gIHNob3VsZExvYWQ/OiBib29sZWFuO1xyXG59XHJcblxyXG5jb25zdCBXZWRkaW5nVmlkZW9zU2VjdGlvbjogUmVhY3QuRkM8V2VkZGluZ1ZpZGVvc1Byb3BzPiA9ICh7IHNob3VsZExvYWQgPSBmYWxzZSB9KSA9PiB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgW2xpa2VkVmlkZW9zLCBzZXRMaWtlZFZpZGVvc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pO1xyXG4gIGNvbnN0IFthZG1pcmluZ1VzZXJzLCBzZXRBZG1pcmluZ1VzZXJzXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIGJvb2xlYW4+Pih7fSk7XHJcbiAgY29uc3QgW3dlZGRpbmdWaWRlb3MsIHNldFdlZGRpbmdWaWRlb3NdID0gdXNlU3RhdGU8TW92aWVWaWRlb1tdPihbXSk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGU8Ym9vbGVhbj4odHJ1ZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbcGFnZSwgc2V0UGFnZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDEpO1xyXG4gIGNvbnN0IFtoYXNNb3JlLCBzZXRIYXNNb3JlXSA9IHVzZVN0YXRlPGJvb2xlYW4+KHRydWUpO1xyXG4gIGNvbnN0IFtsb2FkaW5nTW9yZSwgc2V0TG9hZGluZ01vcmVdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xyXG4gIGNvbnN0IFtpbml0aWFsTG9hZENvbXBsZXRlLCBzZXRJbml0aWFsTG9hZENvbXBsZXRlXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcclxuICBjb25zdCBzY3JvbGxDb250YWluZXJSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IGxvYWRNb3JlVHJpZ2dlclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcbiAgY29uc3Qgb2JzZXJ2ZXJSZWYgPSB1c2VSZWY8SW50ZXJzZWN0aW9uT2JzZXJ2ZXIgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gRmFsbGJhY2sgZGF0YSBpZiBBUEkgZmFpbHMgLSB1c2luZyBlbXB0eSBhcnJheVxyXG4gIGNvbnN0IGdldEZhbGxiYWNrTW92aWVzID0gKCk6IE1vdmllVmlkZW9bXSA9PiBbXTtcclxuXHJcbiAgLy8gRnVuY3Rpb24gdG8gZmV0Y2ggbW92aWVzIGZyb20gdGhlIEFQSVxyXG4gIGNvbnN0IGZldGNoTW92aWVzID0gYXN5bmMgKHBhZ2VOdW1iZXI6IG51bWJlciwgaXNJbml0aWFsTG9hZDogYm9vbGVhbiA9IGZhbHNlKSA9PiB7XHJcbiAgICAvLyBQcmV2ZW50IG11bHRpcGxlIHNpbXVsdGFuZW91cyByZXF1ZXN0c1xyXG4gICAgaWYgKChpc0luaXRpYWxMb2FkICYmIGxvYWRpbmcpIHx8ICghaXNJbml0aWFsTG9hZCAmJiBsb2FkaW5nTW9yZSkpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1JlcXVlc3QgYWxyZWFkeSBpbiBwcm9ncmVzcywgc2tpcHBpbmcnKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGlmIChpc0luaXRpYWxMb2FkKSB7XHJcbiAgICAgICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRMb2FkaW5nTW9yZSh0cnVlKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc29sZS5sb2coYEZldGNoaW5nIG1vdmllcyBmb3IgcGFnZSAke3BhZ2VOdW1iZXJ9Li4uYCk7XHJcblxyXG4gICAgICAvLyBTaW1wbGUgdG9rZW4gcmV0cmlldmFsXHJcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhgQXV0aCB0b2tlbiBmb3VuZDogJHt0b2tlbiA/ICdZZXMnIDogJ05vJ31gKTtcclxuXHJcbiAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oJ05vIGF1dGhlbnRpY2F0aW9uIHRva2VuIGZvdW5kJyk7XHJcbiAgICAgICAgc2V0RXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyk7XHJcbiAgICAgICAgc2V0V2VkZGluZ1ZpZGVvcyhbXSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBNYWtlIEFQSSByZXF1ZXN0XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0PEFwaVJlc3BvbnNlPihgL21vdmllcz9wYWdlPSR7cGFnZU51bWJlcn0mbGltaXQ9MTBgLCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ0FQSSByZXNwb25zZSBzdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcclxuXHJcbiAgICAgIC8vIFByb2Nlc3MgdGhlIGRhdGFcclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5tb3ZpZXMpIHtcclxuICAgICAgICBjb25zb2xlLmxvZyhgTG9hZGVkICR7cmVzcG9uc2UuZGF0YS5tb3ZpZXMubGVuZ3RofSBtb3ZpZXMgZm9yIHBhZ2UgJHtwYWdlTnVtYmVyfWApO1xyXG5cclxuICAgICAgICAvLyBMb2cgdGhlIGZpcnN0IGl0ZW0gZm9yIGRlYnVnZ2luZ1xyXG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhLm1vdmllcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnU2FtcGxlIG1vdmllIGRhdGE6JywgcmVzcG9uc2UuZGF0YS5tb3ZpZXNbMF0pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gUHJvY2VzcyB0aGUgcmVzcG9uc2VcclxuICAgICAgICBjb25zdCBwcm9jZXNzZWRNb3ZpZXMgPSByZXNwb25zZS5kYXRhLm1vdmllcy5tYXAobW92aWUgPT4ge1xyXG4gICAgICAgICAgaWYgKCFtb3ZpZS52aWRlb190aHVtYm5haWwpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYE1vdmllIG1pc3NpbmcgdGh1bWJuYWlsOiAke21vdmllLnZpZGVvX2lkfWApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgLy8gRW5zdXJlIHVzZXJfaWQgaXMgc2V0IC0gaW4gY2FzZSB0aGUgQVBJIGRvZXNuJ3QgcHJvdmlkZSBpdFxyXG4gICAgICAgICAgaWYgKCFtb3ZpZS51c2VyX2lkICYmIG1vdmllLnVzZXJfbmFtZSkge1xyXG4gICAgICAgICAgICAvLyBDcmVhdGUgYSB0ZW1wb3JhcnkgdXNlcl9pZCBiYXNlZCBvbiB1c2VybmFtZSBpZiBub3QgcHJvdmlkZWRcclxuICAgICAgICAgICAgLy8gVGhpcyBpcyBhIGZhbGxiYWNrIGFuZCBzaG91bGQgYmUgcmVwbGFjZWQgd2l0aCBhY3R1YWwgdXNlciBJRHMgZnJvbSB0aGUgQVBJXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBNb3ZpZSBtaXNzaW5nIHVzZXJfaWQsIGNyZWF0aW5nIHRlbXBvcmFyeSBvbmUgZnJvbSB1c2VybmFtZTogJHttb3ZpZS51c2VyX25hbWV9YCk7XHJcbiAgICAgICAgICAgIG1vdmllLnVzZXJfaWQgPSBgdXNlci0ke21vdmllLnVzZXJfbmFtZS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xccysvZywgJy0nKX1gO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgcmV0dXJuIG1vdmllO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAocGFnZU51bWJlciA9PT0gMSkge1xyXG4gICAgICAgICAgc2V0V2VkZGluZ1ZpZGVvcyhwcm9jZXNzZWRNb3ZpZXMpO1xyXG4gICAgICAgICAgLy8gTG9hZCBsaWtlIHN0YXR1cyBmb3IgaW5pdGlhbCB2aWRlb3NcclxuICAgICAgICAgIGxvYWRMaWtlU3RhdHVzKHByb2Nlc3NlZE1vdmllcy5tYXAoKHZpZGVvOiBhbnkpID0+IHZpZGVvLnZpZGVvX2lkKSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIHNldFdlZGRpbmdWaWRlb3MocHJldiA9PiBbLi4ucHJldiwgLi4ucHJvY2Vzc2VkTW92aWVzXSk7XHJcbiAgICAgICAgICAvLyBMb2FkIGxpa2Ugc3RhdHVzIGZvciBuZXcgdmlkZW9zIChhcHBlbmQgdG8gZXhpc3RpbmcpXHJcbiAgICAgICAgICBjb25zdCBuZXdWaWRlb0lkcyA9IHByb2Nlc3NlZE1vdmllcy5tYXAoKHZpZGVvOiBhbnkpID0+IHZpZGVvLnZpZGVvX2lkKTtcclxuICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTGlrZWRNb3ZpZXNEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2xpa2VkTW92aWVzJyk7XHJcbiAgICAgICAgICBjb25zdCBleGlzdGluZ0xpa2VkTW92aWVzID0gZXhpc3RpbmdMaWtlZE1vdmllc0RhdGEgPyBKU09OLnBhcnNlKGV4aXN0aW5nTGlrZWRNb3ZpZXNEYXRhKSA6IFtdO1xyXG4gICAgICAgICAgY29uc3QgbmV3TGlrZWRWaWRlb3MgPSBuZXdWaWRlb0lkcy5maWx0ZXIoKGlkOiBzdHJpbmcpID0+IGV4aXN0aW5nTGlrZWRNb3ZpZXMuaW5jbHVkZXMoaWQpKTtcclxuICAgICAgICAgIHNldExpa2VkVmlkZW9zKHByZXYgPT4gWy4uLnByZXYsIC4uLm5ld0xpa2VkVmlkZW9zXSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzZXRIYXNNb3JlKHJlc3BvbnNlLmRhdGEubmV4dF9wYWdlKTtcclxuICAgICAgICBzZXRQYWdlKHBhZ2VOdW1iZXIpOyAvLyBVcGRhdGUgdGhlIGN1cnJlbnQgcGFnZVxyXG4gICAgICAgIHNldEVycm9yKG51bGwpOyAvLyBDbGVhciBhbnkgcHJldmlvdXMgZXJyb3JzXHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKCdVbmV4cGVjdGVkIHJlc3BvbnNlIGZvcm1hdDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGxvYWQgd2VkZGluZyB2aWRlb3MgLSB1bmV4cGVjdGVkIHJlc3BvbnNlIGZvcm1hdCcpO1xyXG4gICAgICAgIHNldFdlZGRpbmdWaWRlb3MoW10pO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQVBJIHJlcXVlc3QgZmFpbGVkOicsIGVycik7XHJcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCB3ZWRkaW5nIHZpZGVvcycpO1xyXG4gICAgICBzZXRXZWRkaW5nVmlkZW9zKFtdKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIC8vIEFsd2F5cyBjbGVhciBsb2FkaW5nIHN0YXRlc1xyXG4gICAgICBpZiAoaXNJbml0aWFsTG9hZCkge1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdJbml0aWFsIGxvYWRpbmcgY29tcGxldGUnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRMb2FkaW5nTW9yZShmYWxzZSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgbW9yZSBjb21wbGV0ZScpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gRmV0Y2ggZmlyc3QgcGFnZSBvZiBtb3ZpZXMgYXMgc29vbiBhcyB0aGUgY29tcG9uZW50IG1vdW50c1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBPbmx5IHRyaWdnZXIgd2hlbiBzaG91bGRMb2FkIGNoYW5nZXMgdG8gdHJ1ZVxyXG4gICAgaWYgKHNob3VsZExvYWQpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1dlZGRpbmcgVmlkZW9zIGNvbXBvbmVudCBpcyBub3cgdmlzaWJsZSBhbmQgcmVhZHkgdG8gbG9hZCBkYXRhJyk7XHJcblxyXG4gICAgICAvLyBTZXQgYSBmbGFnIHRvIHRyYWNrIGluaXRpYWwgbG9hZFxyXG4gICAgICBzZXRJbml0aWFsTG9hZENvbXBsZXRlKHRydWUpO1xyXG5cclxuICAgICAgLy8gSU1NRURJQVRFIEFQSSByZXF1ZXN0IHdpdGggbm8gY29uZGl0aW9ucyBvciBkZWxheXNcclxuICAgICAgY29uc29sZS5sb2coJ1RyaWdnZXJpbmcgaW5pdGlhbCBtb3ZpZXMgbG9hZCBJTU1FRElBVEVMWS4uLicpO1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG5cclxuICAgICAgLy8gU2tpcCB0aGUgZmV0Y2hNb3ZpZXMgZnVuY3Rpb24gYW5kIG1ha2UgdGhlIEFQSSBjYWxsIGRpcmVjdGx5IGhlcmVcclxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuXHJcbiAgICAgIGlmICh0b2tlbikge1xyXG4gICAgICAgIC8vIE1ha2UgZGlyZWN0IEFQSSByZXF1ZXN0XHJcbiAgICAgICAgY29uc29sZS5sb2coJ01ha2luZyBkaXJlY3QgQVBJIHJlcXVlc3QgZm9yIG1vdmllcyBwYWdlIDEuLi4nKTtcclxuICAgICAgICBheGlvcy5nZXQ8QXBpUmVzcG9uc2U+KGAvbW92aWVzP3BhZ2U9MSZsaW1pdD0xMGAsIHtcclxuICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KVxyXG4gICAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnQVBJIHJlc3BvbnNlIHJlY2VpdmVkIGZvciBtb3ZpZXMgcGFnZSAxJyk7XHJcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEubW92aWVzKSB7XHJcbiAgICAgICAgICAgICAgc2V0V2VkZGluZ1ZpZGVvcyhyZXNwb25zZS5kYXRhLm1vdmllcyk7XHJcbiAgICAgICAgICAgICAgc2V0SGFzTW9yZShyZXNwb25zZS5kYXRhLm5leHRfcGFnZSk7XHJcbiAgICAgICAgICAgICAgc2V0UGFnZSgxKTtcclxuICAgICAgICAgICAgICBzZXRFcnJvcihudWxsKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gTG9hZCBsaWtlIHN0YXR1cyBmb3IgdGhlIGZldGNoZWQgdmlkZW9zXHJcbiAgICAgICAgICAgICAgbG9hZExpa2VTdGF0dXMocmVzcG9uc2UuZGF0YS5tb3ZpZXMubWFwKCh2aWRlbzogYW55KSA9PiB2aWRlby52aWRlb19pZCkpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIHNldFdlZGRpbmdWaWRlb3MoW10pO1xyXG4gICAgICAgICAgICAgIHNldEVycm9yKCdObyB3ZWRkaW5nIHZpZGVvcyBmb3VuZCcpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgICAgLmNhdGNoKGVyciA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0RpcmVjdCBBUEkgcmVxdWVzdCBmYWlsZWQ6JywgZXJyKTtcclxuICAgICAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHdlZGRpbmcgdmlkZW9zJyk7XHJcbiAgICAgICAgICAgIHNldFdlZGRpbmdWaWRlb3MoW10pO1xyXG4gICAgICAgICAgfSlcclxuICAgICAgICAgIC5maW5hbGx5KCgpID0+IHtcclxuICAgICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdJbml0aWFsIGxvYWRpbmcgY29tcGxldGUnKTtcclxuICAgICAgICAgIH0pO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgIHNldEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSwgW3Nob3VsZExvYWRdKTsgLy8gT25seSBkZXBlbmQgb24gc2hvdWxkTG9hZFxyXG5cclxuICAvLyBGYWlsc2FmZSB0byBlbnN1cmUgY29udGVudCBpcyBsb2FkZWQgLSBvbmx5IHNob3cgZXJyb3IgYWZ0ZXIgdGltZW91dFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBJZiB3ZSdyZSBzdHVjayBpbiBsb2FkaW5nIHN0YXRlIGZvciBtb3JlIHRoYW4gMTAgc2Vjb25kcywgc2hvdyBlcnJvclxyXG4gICAgbGV0IHRpbWVvdXRJZDogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbDtcclxuXHJcbiAgICBpZiAobG9hZGluZyAmJiBpbml0aWFsTG9hZENvbXBsZXRlKSB7XHJcbiAgICAgIHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdMb2FkaW5nIHRpbWVvdXQgcmVhY2hlZCAtIEFQSSByZXF1ZXN0IG1heSBoYXZlIGZhaWxlZCcpO1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgIGlmICh3ZWRkaW5nVmlkZW9zLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgICAgc2V0RXJyb3IoJ1VuYWJsZSB0byBsb2FkIHdlZGRpbmcgdmlkZW9zLiBQbGVhc2UgY2hlY2sgeW91ciBuZXR3b3JrIGNvbm5lY3Rpb24uJyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LCAxMDAwMCk7IC8vIDEwIHNlY29uZCB0aW1lb3V0IGZvciBzbG93IG5ldHdvcmtzXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaWYgKHRpbWVvdXRJZCkgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XHJcbiAgICB9O1xyXG4gIH0sIFtsb2FkaW5nLCBpbml0aWFsTG9hZENvbXBsZXRlLCB3ZWRkaW5nVmlkZW9zLmxlbmd0aF0pO1xyXG5cclxuICAvLyBTZXR1cCBJbnRlcnNlY3Rpb24gT2JzZXJ2ZXIgZm9yIGhvcml6b250YWwgbGF6eSBsb2FkaW5nXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIE9ubHkgc2V0IHVwIG9ic2VydmVyIGFmdGVyIGluaXRpYWwgbG9hZCBhbmQgaWYgdGhlcmUncyBtb3JlIGNvbnRlbnQgdG8gZmV0Y2hcclxuICAgIGlmICghaW5pdGlhbExvYWRDb21wbGV0ZSB8fCAhaGFzTW9yZSkgcmV0dXJuO1xyXG5cclxuICAgIC8vIERpc2Nvbm5lY3QgcHJldmlvdXMgb2JzZXJ2ZXIgaWYgaXQgZXhpc3RzXHJcbiAgICBpZiAob2JzZXJ2ZXJSZWYuY3VycmVudCkge1xyXG4gICAgICBvYnNlcnZlclJlZi5jdXJyZW50LmRpc2Nvbm5lY3QoKTtcclxuICAgICAgY29uc29sZS5sb2coJ0Rpc2Nvbm5lY3RlZCBwcmV2aW91cyBpbnRlcnNlY3Rpb24gb2JzZXJ2ZXInKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDcmVhdGUgbmV3IG9ic2VydmVyXHJcbiAgICBvYnNlcnZlclJlZi5jdXJyZW50ID0gbmV3IEludGVyc2VjdGlvbk9ic2VydmVyKFxyXG4gICAgICAoZW50cmllcykgPT4ge1xyXG4gICAgICAgIC8vIElmIHRoZSB0cmlnZ2VyIGVsZW1lbnQgaXMgdmlzaWJsZSBhbmQgd2UncmUgbm90IGFscmVhZHkgbG9hZGluZ1xyXG4gICAgICAgIGlmIChlbnRyaWVzWzBdPy5pc0ludGVyc2VjdGluZyAmJiAhbG9hZGluZyAmJiAhbG9hZGluZ01vcmUgJiYgaGFzTW9yZSkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0xvYWQgbW9yZSB0cmlnZ2VyIGlzIHZpc2libGUsIGxvYWRpbmcgbmV4dCBwYWdlLi4uJyk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnSW50ZXJzZWN0aW9uIHJhdGlvOicsIGVudHJpZXNbMF0uaW50ZXJzZWN0aW9uUmF0aW8pO1xyXG4gICAgICAgICAgY29uc3QgbmV4dFBhZ2UgPSBwYWdlICsgMTtcclxuICAgICAgICAgIGZldGNoTW92aWVzKG5leHRQYWdlLCBmYWxzZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LFxyXG4gICAgICAvLyBVc2UgdGhlIHNjcm9sbCBjb250YWluZXIgYXMgdGhlIHJvb3QgZm9yIHRoZSBpbnRlcnNlY3Rpb24gb2JzZXJ2ZXJcclxuICAgICAge1xyXG4gICAgICAgIHJvb3Q6IHNjcm9sbENvbnRhaW5lclJlZi5jdXJyZW50LFxyXG4gICAgICAgIHRocmVzaG9sZDogMC4xLCAvLyBUcmlnZ2VyIHdoZW4gMTAlIG9mIHRoZSBlbGVtZW50IGlzIHZpc2libGVcclxuICAgICAgICByb290TWFyZ2luOiAnMHB4IDE1MHB4IDBweCAwcHgnIC8vIEFkZCBtYXJnaW4gdG8gdGhlIHJpZ2h0IHRvIGxvYWQgZWFybGllclxyXG4gICAgICB9XHJcbiAgICApO1xyXG5cclxuICAgIC8vIFN0YXJ0IG9ic2VydmluZyB0aGUgdHJpZ2dlciBlbGVtZW50XHJcbiAgICBpZiAobG9hZE1vcmVUcmlnZ2VyUmVmLmN1cnJlbnQpIHtcclxuICAgICAgb2JzZXJ2ZXJSZWYuY3VycmVudC5vYnNlcnZlKGxvYWRNb3JlVHJpZ2dlclJlZi5jdXJyZW50KTtcclxuICAgICAgY29uc29sZS5sb2coJ05vdyBvYnNlcnZpbmcgbG9hZCBtb3JlIHRyaWdnZXIgZWxlbWVudCcpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS53YXJuKCdMb2FkIG1vcmUgdHJpZ2dlciBlbGVtZW50IG5vdCBmb3VuZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGlmIChvYnNlcnZlclJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgb2JzZXJ2ZXJSZWYuY3VycmVudC5kaXNjb25uZWN0KCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0Rpc2Nvbm5lY3RlZCBpbnRlcnNlY3Rpb24gb2JzZXJ2ZXInKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9LCBbaGFzTW9yZSwgbG9hZGluZywgbG9hZGluZ01vcmUsIHBhZ2UsIGluaXRpYWxMb2FkQ29tcGxldGVdKTtcclxuXHJcbiAgLy8gTG9hZCBhZG1pcmluZyBzdGF0dXMgZnJvbSBsb2NhbFN0b3JhZ2Ugd2hlbiBjb21wb25lbnQgbW91bnRzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGFkbWlyZWRVc2Vyc0pzb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWRtaXJlZFVzZXJzJyk7XHJcbiAgICAgIGlmIChhZG1pcmVkVXNlcnNKc29uKSB7XHJcbiAgICAgICAgY29uc3QgYWRtaXJlZFVzZXJzID0gSlNPTi5wYXJzZShhZG1pcmVkVXNlcnNKc29uKTtcclxuICAgICAgICBzZXRBZG1pcmluZ1VzZXJzKGFkbWlyZWRVc2Vycyk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0xvYWRlZCBhZG1pcmluZyBzdGF0dXMgZnJvbSBsb2NhbFN0b3JhZ2U6JywgYWRtaXJlZFVzZXJzKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBhZG1pcmluZyBzdGF0dXMgZnJvbSBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gRnVuY3Rpb24gdG8gbmF2aWdhdGUgdG8gYSB1c2VyJ3MgcHJvZmlsZVxyXG4gIGNvbnN0IG5hdmlnYXRlVG9Vc2VyUHJvZmlsZSA9ICh1c2VySWQ/OiBzdHJpbmcsIHVzZXJuYW1lPzogc3RyaW5nKSA9PiB7XHJcbiAgICAvLyBJZiB3ZSBoYXZlIGEgdXNlcklkLCBtYWtlIGEgZGlyZWN0IEFQSSBjYWxsIHRvIHRoZSB1c2VyIHByb2ZpbGUgZW5kcG9pbnRcclxuICAgIGlmICh1c2VySWQpIHtcclxuICAgICAgLy8gR2V0IHRoZSB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2p3dF90b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dlZHphdF90b2tlbicpO1xyXG5cclxuICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE5hdmlnYXRlIHRvIHRoZSBwcm9maWxlIHBhZ2Ugd2l0aCB0aGUgdXNlcklkXHJcbiAgICAgIHJvdXRlci5wdXNoKGAvcHJvZmlsZS8ke3VzZXJJZH1gKTtcclxuICAgIH1cclxuICAgIGVsc2Uge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ0Nhbm5vdCBuYXZpZ2F0ZSB0byBwcm9maWxlOiBtaXNzaW5nIGJvdGggdXNlcklkIGFuZCB1c2VybmFtZScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHRvZ2dsZUxpa2UgPSBhc3luYyAodmlkZW9JZDogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygn8J+PoCBIb21lIHBhZ2UgbW92aWUgbGlrZSBidXR0b24gY2xpY2tlZCBmb3IgSUQ6JywgdmlkZW9JZCk7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2p3dF90b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dlZHphdF90b2tlbicpO1xyXG5cclxuICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUud2Fybign8J+PoCBObyBhdXRoZW50aWNhdGlvbiB0b2tlbiBmb3VuZCcpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gT3B0aW1pc3RpY2FsbHkgdXBkYXRlIFVJXHJcbiAgICAgIGNvbnN0IGlzQ3VycmVudGx5TGlrZWQgPSBsaWtlZFZpZGVvcy5pbmNsdWRlcyh2aWRlb0lkKTtcclxuICAgICAgY29uc29sZS5sb2coJ/Cfj6AgQ3VycmVudCBsaWtlIHN0YXR1czonLCBpc0N1cnJlbnRseUxpa2VkKTtcclxuXHJcbiAgICAgIHNldExpa2VkVmlkZW9zKChwcmV2KSA9PlxyXG4gICAgICAgIGlzQ3VycmVudGx5TGlrZWRcclxuICAgICAgICAgID8gcHJldi5maWx0ZXIoKGlkKSA9PiBpZCAhPT0gdmlkZW9JZClcclxuICAgICAgICAgIDogWy4uLnByZXYsIHZpZGVvSWRdXHJcbiAgICAgICk7XHJcblxyXG4gICAgICAvLyBNYWtlIEFQSSBjYWxsIHRvIE5leHQuanMgQVBJIHJvdXRlc1xyXG4gICAgICBjb25zdCBlbmRwb2ludCA9IGlzQ3VycmVudGx5TGlrZWQgPyAnL2FwaS91bmxpa2UnIDogJy9hcGkvbGlrZSc7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn4+gIE1ha2luZyBBUEkgY2FsbCB0bzonLCBlbmRwb2ludCk7XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGVuZHBvaW50LCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICBjb250ZW50X2lkOiB2aWRlb0lkLFxyXG4gICAgICAgICAgY29udGVudF90eXBlOiAndmlkZW8nXHJcbiAgICAgICAgfSlcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/Cfj6AgQVBJIEVycm9yIFJlc3BvbnNlOicsIGVycm9yVGV4dCk7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yISBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZURhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn4+gIEFQSSBTdWNjZXNzIFJlc3BvbnNlOicsIHJlc3BvbnNlRGF0YSk7XHJcblxyXG4gICAgICAvLyBVcGRhdGUgbG9jYWxTdG9yYWdlIGZvciBwZXJzaXN0ZW5jZVxyXG4gICAgICBjb25zdCBsaWtlZE1vdmllc0RhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnbGlrZWRNb3ZpZXMnKTtcclxuICAgICAgY29uc3QgbGlrZWRNb3ZpZXNBcnJheSA9IGxpa2VkTW92aWVzRGF0YSA/IEpTT04ucGFyc2UobGlrZWRNb3ZpZXNEYXRhKSA6IFtdO1xyXG5cclxuICAgICAgaWYgKGlzQ3VycmVudGx5TGlrZWQpIHtcclxuICAgICAgICAvLyBSZW1vdmUgZnJvbSBsb2NhbFN0b3JhZ2VcclxuICAgICAgICBjb25zdCB1cGRhdGVkQXJyYXkgPSBsaWtlZE1vdmllc0FycmF5LmZpbHRlcigoaWQ6IHN0cmluZykgPT4gaWQgIT09IHZpZGVvSWQpO1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdsaWtlZE1vdmllcycsIEpTT04uc3RyaW5naWZ5KHVwZGF0ZWRBcnJheSkpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn4+gIFJlbW92ZWQgZnJvbSBsb2NhbFN0b3JhZ2UnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBBZGQgdG8gbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgaWYgKCFsaWtlZE1vdmllc0FycmF5LmluY2x1ZGVzKHZpZGVvSWQpKSB7XHJcbiAgICAgICAgICBsaWtlZE1vdmllc0FycmF5LnB1c2godmlkZW9JZCk7XHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnbGlrZWRNb3ZpZXMnLCBKU09OLnN0cmluZ2lmeShsaWtlZE1vdmllc0FycmF5KSk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+PoCBBZGRlZCB0byBsb2NhbFN0b3JhZ2UnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKGDwn4+gIFN1Y2Nlc3NmdWxseSAke2lzQ3VycmVudGx5TGlrZWQgPyAndW5saWtlZCcgOiAnbGlrZWQnfSB3ZWRkaW5nIHZpZGVvOiAke3ZpZGVvSWR9YCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB0b2dnbGluZyBsaWtlOicsIGVycm9yKTtcclxuICAgICAgLy8gUmV2ZXJ0IG9wdGltaXN0aWMgdXBkYXRlIG9uIGVycm9yXHJcbiAgICAgIGNvbnN0IHdhc0xpa2VkID0gbGlrZWRWaWRlb3MuaW5jbHVkZXModmlkZW9JZCk7XHJcbiAgICAgIHNldExpa2VkVmlkZW9zKChwcmV2KSA9PlxyXG4gICAgICAgIHdhc0xpa2VkXHJcbiAgICAgICAgICA/IHByZXYuZmlsdGVyKChpZCkgPT4gaWQgIT09IHZpZGVvSWQpXHJcbiAgICAgICAgICA6IFsuLi5wcmV2LCB2aWRlb0lkXVxyXG4gICAgICApO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIExvYWQgbGlrZSBzdGF0dXMgZnJvbSBsb2NhbFN0b3JhZ2Ugd2hlbiB2aWRlb3MgYXJlIGxvYWRlZFxyXG4gIGNvbnN0IGxvYWRMaWtlU3RhdHVzID0gKHZpZGVvSWRzOiBzdHJpbmdbXSkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgbGlrZWRNb3ZpZXNEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2xpa2VkTW92aWVzJyk7XHJcbiAgICAgIGNvbnN0IGxpa2VkTW92aWVzQXJyYXkgPSBsaWtlZE1vdmllc0RhdGEgPyBKU09OLnBhcnNlKGxpa2VkTW92aWVzRGF0YSkgOiBbXTtcclxuXHJcbiAgICAgIC8vIEZpbHRlciB0byBvbmx5IGluY2x1ZGUgdmlkZW9zIHRoYXQgYXJlIGN1cnJlbnRseSBsb2FkZWRcclxuICAgICAgY29uc3QgY3VycmVudExpa2VkVmlkZW9zID0gdmlkZW9JZHMuZmlsdGVyKGlkID0+IGxpa2VkTW92aWVzQXJyYXkuaW5jbHVkZXMoaWQpKTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn4+gIExvYWRlZCBsaWtlIHN0YXR1cyBmb3IgaG9tZSBwYWdlIG1vdmllczonLCBjdXJyZW50TGlrZWRWaWRlb3MpO1xyXG4gICAgICBzZXRMaWtlZFZpZGVvcyhjdXJyZW50TGlrZWRWaWRlb3MpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign8J+PoCBFcnJvciBsb2FkaW5nIGxpa2Ugc3RhdHVzOicsIGVycm9yKTtcclxuICAgICAgc2V0TGlrZWRWaWRlb3MoW10pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIGhhbmRsZSBBZG1pcmUgYnV0dG9uIGNsaWNrXHJcbiAgY29uc3QgaGFuZGxlQWRtaXJlID0gYXN5bmMgKHVzZXJJZD86IHN0cmluZykgPT4ge1xyXG4gICAgaWYgKCF1c2VySWQpIHtcclxuICAgICAgY29uc29sZS53YXJuKCdObyB1c2VyIElEIHByb3ZpZGVkIGZvciBBZG1pcmUgYWN0aW9uJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDaGVjayBpZiBhbHJlYWR5IGFkbWlyaW5nIHRoaXMgdXNlclxyXG4gICAgY29uc3QgaXNDdXJyZW50bHlBZG1pcmluZyA9IGFkbWlyaW5nVXNlcnNbdXNlcklkXSB8fCBmYWxzZTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBHZXQgdG9rZW4gZnJvbSBsb2NhbFN0b3JhZ2VcclxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKSB8fFxyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdqd3RfdG9rZW4nKSB8fFxyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd3ZWR6YXRfdG9rZW4nKTtcclxuXHJcbiAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oJ05vIGF1dGhlbnRpY2F0aW9uIHRva2VuIGZvdW5kJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBPcHRpbWlzdGljYWxseSB1cGRhdGUgVUkgc3RhdGVcclxuICAgICAgc2V0QWRtaXJpbmdVc2VycyhwcmV2ID0+ICh7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICBbdXNlcklkXTogIWlzQ3VycmVudGx5QWRtaXJpbmdcclxuICAgICAgfSkpO1xyXG5cclxuICAgICAgLy8gTWFrZSBBUEkgY2FsbCB0byBmb2xsb3cvdW5mb2xsb3cgdXNlclxyXG4gICAgICBjb25zdCBlbmRwb2ludCA9IGlzQ3VycmVudGx5QWRtaXJpbmdcclxuICAgICAgICA/ICdodHRwczovL3VueTRjZHN3a2EuZXhlY3V0ZS1hcGkuYXAtc291dGgtMS5hbWF6b25hd3MuY29tL3Rlc3QvaHViL3VuZm9sbG93J1xyXG4gICAgICAgIDogJ2h0dHBzOi8vdW55NGNkc3drYS5leGVjdXRlLWFwaS5hcC1zb3V0aC0xLmFtYXpvbmF3cy5jb20vdGVzdC9odWIvZm9sbG93JztcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKGBNYWtpbmcgcmVxdWVzdCB0byAke2VuZHBvaW50fSB3aXRoIHVzZXIgSUQ6ICR7dXNlcklkfWApO1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICAvLyBNYWtlIHRoZSBBUEkgY2FsbFxyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdChcclxuICAgICAgICAgIGVuZHBvaW50LFxyXG4gICAgICAgICAgeyB0YXJnZXRfdXNlcl9pZDogdXNlcklkIH0sXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygnQVBJIHJlc3BvbnNlOicsIHJlc3BvbnNlLmRhdGEpO1xyXG5cclxuICAgICAgICAvLyBVcGRhdGUgbG9jYWxTdG9yYWdlIHdpdGggdGhlIG5ldyBzdGF0ZVxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCBhZG1pcmVkVXNlcnNKc29uID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FkbWlyZWRVc2VycycpIHx8ICd7fSc7XHJcbiAgICAgICAgICBjb25zdCBhZG1pcmVkVXNlcnMgPSBKU09OLnBhcnNlKGFkbWlyZWRVc2Vyc0pzb24pO1xyXG5cclxuICAgICAgICAgIGlmICghaXNDdXJyZW50bHlBZG1pcmluZykge1xyXG4gICAgICAgICAgICBhZG1pcmVkVXNlcnNbdXNlcklkXSA9IHRydWU7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBkZWxldGUgYWRtaXJlZFVzZXJzW3VzZXJJZF07XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FkbWlyZWRVc2VycycsIEpTT04uc3RyaW5naWZ5KGFkbWlyZWRVc2VycykpO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1VwZGF0ZWQgYWRtaXJlZCB1c2VycyBpbiBsb2NhbFN0b3JhZ2U6JywgYWRtaXJlZFVzZXJzKTtcclxuICAgICAgICB9IGNhdGNoIChzdG9yYWdlRXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGxvY2FsU3RvcmFnZTonLCBzdG9yYWdlRXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoYXBpRXJyb3I6IGFueSkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yICR7aXNDdXJyZW50bHlBZG1pcmluZyA/ICd1bmFkbWlyaW5nJyA6ICdhZG1pcmluZyd9IHVzZXI6YCwgYXBpRXJyb3IpO1xyXG5cclxuICAgICAgICAvLyBSZXZlcnQgVUkgc3RhdGUgb24gZXJyb3JcclxuICAgICAgICBzZXRBZG1pcmluZ1VzZXJzKHByZXYgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICBbdXNlcklkXTogaXNDdXJyZW50bHlBZG1pcmluZ1xyXG4gICAgICAgIH0pKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignVW5leHBlY3RlZCBlcnJvciBpbiBoYW5kbGVBZG1pcmU6JywgZXJyb3IpO1xyXG5cclxuICAgICAgLy8gUmV2ZXJ0IFVJIHN0YXRlIG9uIHVuZXhwZWN0ZWQgZXJyb3JzXHJcbiAgICAgIHNldEFkbWlyaW5nVXNlcnMocHJldiA9PiAoe1xyXG4gICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgW3VzZXJJZF06IGlzQ3VycmVudGx5QWRtaXJpbmdcclxuICAgICAgfSkpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZU1hbnVhbFNjcm9sbCA9IChkaXJlY3Rpb246IFwibGVmdFwiIHwgXCJyaWdodFwiKSA9PiB7XHJcbiAgICBpZiAoc2Nyb2xsQ29udGFpbmVyUmVmLmN1cnJlbnQpIHtcclxuICAgICAgY29uc3Qgc2Nyb2xsQW1vdW50ID0gZGlyZWN0aW9uID09PSBcImxlZnRcIiA/IC02OTQgOiA2OTQ7IC8vIFdpZHRoICsgZ2FwXHJcbiAgICAgIHNjcm9sbENvbnRhaW5lclJlZi5jdXJyZW50LnNjcm9sbEJ5KHtcclxuICAgICAgICBsZWZ0OiBzY3JvbGxBbW91bnQsXHJcbiAgICAgICAgYmVoYXZpb3I6IFwic21vb3RoXCIsXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEdldCBhcHByb3ByaWF0ZSBpbWFnZSBzb3VyY2UgZm9yIGEgbW92aWVcclxuICBjb25zdCBnZXRJbWFnZVNvdXJjZSA9ICh2aWRlbzogTW92aWVWaWRlbyk6IHN0cmluZyA9PiB7XHJcbiAgICBpZiAodmlkZW8udmlkZW9fdGh1bWJuYWlsKSB7XHJcbiAgICAgIHJldHVybiB2aWRlby52aWRlb190aHVtYm5haWw7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gJy9waWNzL3BsYWNlaG9sZGVyLnN2Zyc7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cIm1iLTggcmVsYXRpdmVcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxyXG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LWludGVyIHRleHQtWzIwcHhdIGxlYWRpbmctWzE4cHhdIGZvbnQtc2VtaWJvbGQgdGV4dC1bIzAwMDAwMF1cIj5cclxuICAgICAgICAgIE1PVklFU1xyXG4gICAgICAgIDwvaDI+XHJcbiAgICAgICAgPGFcclxuICAgICAgICAgIGhyZWY9XCIvaG9tZS9tb3ZpZXNcIlxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIHRleHQtc20gZm9udC1tZWRpdW0gaG92ZXI6dW5kZXJsaW5lIHotMTAgcmVsYXRpdmUgbXItNCBtZDptci04IGxnOm1yLVszNTBweF1cIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIFNlZSBhbGxcclxuICAgICAgICA8L2E+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIExvYWRpbmcgc3RhdGUgKi99XHJcbiAgICAgIHtsb2FkaW5nICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTEwIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1ibG9jayB3LTYgaC02IGJvcmRlci0yIGJvcmRlci1yZWQtNTAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbXItMlwiPjwvZGl2PlxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgd2VkZGluZyB2aWRlb3MuLi48L3NwYW4+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogRXJyb3Igc3RhdGUgKi99XHJcbiAgICAgIHtlcnJvciAmJiAhbG9hZGluZyAmJiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0xMCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgbWItNFwiPntlcnJvcn08L2Rpdj5cclxuICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdSZXRyeWluZyB3ZWRkaW5nIHZpZGVvcyBsb2FkLi4uJyk7XHJcbiAgICAgICAgICAgICAgc2V0RXJyb3IobnVsbCk7XHJcbiAgICAgICAgICAgICAgZmV0Y2hNb3ZpZXMoMSwgdHJ1ZSk7XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1yZWQtNTAwIHRleHQtd2hpdGUgcm91bmRlZCBob3ZlcjpiZy1yZWQtNjAwIHRleHQtc21cIlxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICBSZXRyeVxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogRGVidWcgaW5mbyBpbiBkZXZlbG9wbWVudCBvbmx5ICovfVxyXG4gICAgICB7Lyoge3Byb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIHJpZ2h0LTAgYmctYmxhY2svNTAgdGV4dC13aGl0ZSB0ZXh0LXhzIHAtMiByb3VuZGVkLWJsLW1kIHotNTAgbWF4LXctWzIwMHB4XVwiPlxyXG4gICAgICAgICAgPGRpdj5TdGF0ZToge2xvYWRpbmcgPyAnTG9hZGluZycgOiBlcnJvciA/ICdFcnJvcicgOiB3ZWRkaW5nVmlkZW9zLmxlbmd0aCA9PT0gMCA/ICdFbXB0eScgOiAnTG9hZGVkJ308L2Rpdj5cclxuICAgICAgICAgIDxkaXY+Q291bnQ6IHt3ZWRkaW5nVmlkZW9zLmxlbmd0aH08L2Rpdj5cclxuICAgICAgICAgIDxkaXY+UGFnZToge3BhZ2V9PC9kaXY+XHJcbiAgICAgICAgICA8ZGl2PkhhcyBtb3JlOiB7aGFzTW9yZSA/ICdZZXMnIDogJ05vJ308L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX0gKi99XHJcblxyXG4gICAgICB7LyogQ29udGVudCAqL31cclxuICAgICAgeyFsb2FkaW5nICYmICFlcnJvciAmJiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgIHsvKiBFbXB0eSBzdGF0ZSBtZXNzYWdlIHdoZW4gbm8gdmlkZW9zICovfVxyXG4gICAgICAgICAge3dlZGRpbmdWaWRlb3MubGVuZ3RoID09PSAwICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLVsyMjBweF0gdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+Tm8gd2VkZGluZyB2aWRlb3MgYXZhaWxhYmxlPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICB7d2VkZGluZ1ZpZGVvcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICB7LyogTGVmdCBzY3JvbGwgYnV0dG9uICovfVxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU1hbnVhbFNjcm9sbChcImxlZnRcIil9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTAgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB6LTEwIGJnLXdoaXRlLzgwIHJvdW5kZWQtZnVsbCBwLTIgc2hhZG93LW1kIGhvdmVyOmJnLXdoaXRlXCJcclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IG1hcmdpbkxlZnQ6IFwiLTEycHhcIiB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxNZENoZXZyb25MZWZ0IHNpemU9ezI0fSAvPlxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICByZWY9e3Njcm9sbENvbnRhaW5lclJlZn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0byBzY3JvbGxiYXItaGlkZSByZWxhdGl2ZSB3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgV2Via2l0T3ZlcmZsb3dTY3JvbGxpbmc6IFwidG91Y2hcIixcclxuICAgICAgICAgICAgICAgICAgc2Nyb2xsYmFyV2lkdGg6IFwibm9uZVwiLFxyXG4gICAgICAgICAgICAgICAgICBtc092ZXJmbG93U3R5bGU6IFwibm9uZVwiLFxyXG4gICAgICAgICAgICAgICAgICBzY3JvbGxCZWhhdmlvcjogJ3Ntb290aCdcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0zIHBiLTUgZmxleC1ub3dyYXAgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgIHt3ZWRkaW5nVmlkZW9zLm1hcCgodmlkZW8sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAga2V5PXtgJHt2aWRlby52aWRlb19pZH0tJHtpbmRleH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCB3LVs4NXZ3XSBzbTp3LVs3NXZ3XSBtZDp3LVs2NXZ3XSBsZzp3LVs1NXZ3XSBoLVszMDBweF0gc206aC1bNDAwcHhdIG1kOmgtWzQ1MHB4XSBsZzpoLVs1MDBweF0gbWF4LXctWzY3NnB4XSBib3JkZXItYiBib3JkZXItWyNEQkRCREJdIHBiLVsxNXB4XSBzbTpwYi1bMjFweF1cIlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBTZWN0aW9uIDEgLSBVc2VyIEluZm8gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLVs0MHB4XSBzbTpoLVs1MHB4XSBtZDpoLVs1NnB4XSBmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcC1bNXB4XSBzbTpwLVs4cHhfNXB4XVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtWzEwcHhdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctWzM0cHhdIGgtWzM0cHhdIHJvdW5kZWQtWzI3cHhdIGJvcmRlciBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZVRvVXNlclByb2ZpbGUodmlkZW8udXNlcl9pZCwgdmlkZW8udXNlcl9uYW1lKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VXNlckF2YXRhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VybmFtZT17dmlkZW8udXNlcl9uYW1lIHx8IFwidXNlclwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZVRvVXNlclByb2ZpbGUodmlkZW8udXNlcl9pZCwgdmlkZW8udXNlcl9uYW1lKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtYXgtdy1bMzgwcHhdIGgtWzQwcHhdIHB0LVs3cHhdIHByLVs1MHB4XSBzbTpwci1bMTAwcHhdIG1kOnByLVsxNTBweF0gbGc6cHItWzIwMHB4XSBwYi1bMTBweF1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJJbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogXCIxOHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogXCIwJVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlcnRpY2FsQWxpZ246IFwibWlkZGxlXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBcInBvaW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gbmF2aWdhdGVUb1VzZXJQcm9maWxlKHZpZGVvLnVzZXJfaWQsIHZpZGVvLnVzZXJfbmFtZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOnVuZGVybGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2aWRlby51c2VyX25hbWUgfHwgXCJ1c2VyXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUFkbWlyZSh2aWRlby51c2VyX2lkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGJnLVsjQjMxQjFFXSB0ZXh0LXdoaXRlIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bSBob3ZlcjpiZy1yZWQtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YWRtaXJpbmdVc2Vyc1t2aWRlby51c2VyX2lkIHx8ICcnXSA/ICdBZG1pcmluZycgOiAnQWRtaXJlJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHshYWRtaXJpbmdVc2Vyc1t2aWRlby51c2VyX2lkIHx8ICcnXSAmJiA8c3BhbiBjbGFzc05hbWU9XCJtbC0xXCI+Kzwvc3Bhbj59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbj7igKLigKLigKI8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICB7LyogU2VjdGlvbiAyIC0gVmlkZW8gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctWzg1dnddIHNtOnctWzc1dnddIG1kOnctWzY1dnddIGxnOnctWzU1dnddIGgtWzE4MHB4XSBzbTpoLVsyNTBweF0gbWQ6aC1bMzAwcHhdIGxnOmgtWzM1MHB4XSBtYXgtdy1bNjc2cHhdIHJvdW5kZWQtWzEwcHhdIGJvcmRlciBwLVsxcHhdIHJlbGF0aXZlXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFZpZGVvIFRodW1ibmFpbCAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGwgaC1mdWxsIHJvdW5kZWQtWzEwcHhdIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtnZXRJbWFnZVNvdXJjZSh2aWRlbyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e3ZpZGVvLnZpZGVvX25hbWUgfHwgXCJXZWRkaW5nIFZpZGVvXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplcz1cIihtYXgtd2lkdGg6IDQ4MHB4KSAzMDBweCwgKG1heC13aWR0aDogNjQwcHgpIDM1MHB4LCAobWF4LXdpZHRoOiA3NjhweCkgNDUwcHgsIChtYXgtd2lkdGg6IDEwMjRweCkgNTUwcHgsIDY3NnB4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9iamVjdC1jb3ZlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Li4uKGluZGV4IDwgMiA/IHsgcHJpb3JpdHk6IHRydWUgfSA6IHsgbG9hZGluZzogJ2xhenknIH0pfSAvLyBQcmlvcml0eSBmb3IgZmlyc3QgdHdvLCBsYXp5IGxvYWRpbmcgZm9yIHJlc3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVub3B0aW1pemVkPXt0cnVlfSAvLyBTa2lwIG9wdGltaXphdGlvbiBmb3IgYWxsIGltYWdlcyB0byBhdm9pZCBpc3N1ZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiYmx1clwiIC8vIFNob3cgYmx1ciBwbGFjZWhvbGRlciB3aGlsZSBsb2FkaW5nXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBibHVyRGF0YVVSTD1cImRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QjNhV1IwYUQwaU56QXdJaUJvWldsbmFIUTlJalEzTlNJZ2RtVnljMmx2YmowaU1TNHhJaUI0Yld4dWN6MGlhSFIwY0RvdkwzZDNkeTUzTXk1dmNtY3ZNakF3TUM5emRtY2lJSGh0Ykc1ek9uaHNhVzVyUFNKb2RIUndPaTh2ZDNkM0xuY3pMbTl5Wnk4eE9UazVMM2hzYVc1cklqNDhaR1ZtY3o0OGJHbHVaV0Z5UjNKaFpHbGxiblFnYVdROUltY2lQanh6ZEc5d0lITjBiM0F0WTI5c2IzSTlJaU5sWldVaUlHOW1abk5sZEQwaU1qQWxJaUF2UGp4emRHOXdJSE4wYjNBdFkyOXNiM0k5SWlObVptWWlJRzltWm5ObGREMGlOVEFsSWlBdlBqeHpkRzl3SUhOMGIzQXRZMjlzYjNJOUlpTmxaV1VpSUc5bVpuTmxkRDBpTnpBbElpQXZQand2YkdsdVpXRnlSM0poWkdsbGJuUStQQzlrWldaelBqeHlaV04wSUhkcFpIUm9QU0kzTURBaUlHaGxhV2RvZEQwaU5EYzFJaUJtYVd4c1BTSWpaV1ZsSWlBdlBqeHlaV04wSUdsa1BTSnlJaUIzYVdSMGFEMGlOekF3SWlCb1pXbG5hSFE5SWpRM05TSWdabWxzYkQwaWRYSnNLQ05uS1NJZ0x6NDhZVzVwYldGMFpTQjRiR2x1YXpwb2NtVm1QU0lqY2lJZ1lYUjBjbWxpZFhSbFRtRnRaVDBpZUNJZ1puSnZiVDBpTFRjd01DSWdkRzg5SWpjd01DSWdaSFZ5UFNJeGN5SWdjbVZ3WldGMFEyOTFiblE5SW1sdVpHVm1hVzVwZEdVaUlDQXZQand2YzNablBnPT1cIiAvLyBCYXNlNjQgZW5jb2RlZCBTVkcgbG9hZGluZyBhbmltYXRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBsb2FkIGltYWdlIGZvciB2aWRlbzogJHt2aWRlby52aWRlb19uYW1lfWApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBVc2UgcGxhY2Vob2xkZXIgYXMgZmFsbGJhY2tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaW1nRWxlbWVudCA9IGUudGFyZ2V0IGFzIEhUTUxJbWFnZUVsZW1lbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbWdFbGVtZW50KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5zcmMgPSAnL3BpY3MvcGxhY2Vob2xkZXIuc3ZnJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBQbGF5IEJ1dHRvbiBPdmVybGF5ICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b3A6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0OiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmlnaHQ6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3R0b206IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB6SW5kZXg6IDIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFwiMTBweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6IFwicG9pbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInJnYmEoMCwgMCwgMCwgMC4yKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gTmF2aWdhdGUgdG8gdGhlIG1vdmllIGRldGFpbCBwYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodmlkZW8udmlkZW9faWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYE5hdmlnYXRpbmcgdG8gbW92aWUgZGV0YWlsIHBhZ2U6ICR7dmlkZW8udmlkZW9faWR9YCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gYC9ob21lL21vdmllcy8ke3ZpZGVvLnZpZGVvX2lkfWA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIHNtOnctMTIgc206aC0xMiBtZDp3LTE0IG1kOmgtMTQgcm91bmRlZC1mdWxsIGJnLXJlZC01MDAgYmctb3BhY2l0eS04MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9XCIyOFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodD1cIjI4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJ3aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNOCA1djE0bDExLTd6XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBTZWN0aW9uIDMgLSBJbnRlcmFjdGlvbnMgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogXCIxMnB4IDBcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBmbGV4RGlyZWN0aW9uOiBcImNvbHVtblwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogXCI4cHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogXCIxNnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0b2dnbGVMaWtlKHZpZGVvLnZpZGVvX2lkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctMTAgaC0xMCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxpa2VkVmlkZW9zLmluY2x1ZGVzKHZpZGVvLnZpZGVvX2lkKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC0xMDAgaG92ZXI6YmctcmVkLTIwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTIwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e2xpa2VkVmlkZW9zLmluY2x1ZGVzKHZpZGVvLnZpZGVvX2lkKSA/ICdVbmxpa2UnIDogJ0xpa2UnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsaWtlZFZpZGVvcy5pbmNsdWRlcyh2aWRlby52aWRlb19pZCkgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNZEZhdm9yaXRlIHNpemU9ezI0fSBjb2xvcj1cIiNCMzFCMUVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1kRmF2b3JpdGVCb3JkZXIgc2l6ZT17MjR9IGNvbG9yPVwiIzY2NlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy0xMCBoLTEwIHJvdW5kZWQtZnVsbCBiZy1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTIwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNZE91dGxpbmVNb2RlQ29tbWVudCBzaXplPXsyNH0gY29sb3I9XCIjNjY2XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctMTAgaC0xMCByb3VuZGVkLWZ1bGwgYmctZ3JheS0xMDAgaG92ZXI6YmctZ3JheS0yMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TWRPdXRsaW5lU2hhcmUgc2l6ZT17MjR9IGNvbG9yPVwiIzY2NlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogXCIxNnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjNjY2XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogbGlrZWRWaWRlb3MuaW5jbHVkZXModmlkZW8udmlkZW9faWQpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIiNCMzFCMUVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIjNjY2XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2aWRlby52aWRlb19saWtlcyA/IGAkeyh2aWRlby52aWRlb19saWtlcyAvIDEwMDApLnRvRml4ZWQoMSl9S2AgOiAnMCd9IGxpa2VzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPnt2aWRlby52aWRlb192aWV3cyA/IGAkeyh2aWRlby52aWRlb192aWV3cyAvIDEwMDApLnRvRml4ZWQoMSl9S2AgOiAnMCd9IHZpZXdzPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTRweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBnYXA6IFwiNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPnt2aWRlby52aWRlb19kZXNjcmlwdGlvbiB8fCB2aWRlby52aWRlb19uYW1lfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgKSl9XHJcblxyXG4gICAgICAgICAgICAgICAgICB7LyogTG9hZCBtb3JlIHRyaWdnZXIgZWxlbWVudCAtIHRoaXMgaXMgd2hhdCBJbnRlcnNlY3Rpb25PYnNlcnZlciB3YXRjaGVzICovfVxyXG4gICAgICAgICAgICAgICAgICB7aGFzTW9yZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgcmVmPXtsb2FkTW9yZVRyaWdnZXJSZWZ9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIHctMTAgaC1mdWxsIG9wYWNpdHktMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQWRkIGRlYnVnIG91dGxpbmUgaW4gZGV2ZWxvcG1lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgb3V0bGluZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgPyAnMXB4IGRhc2hlZCByZ2JhKDI1NSwgMCwgMCwgMC4zKScgOiAnbm9uZSdcclxuICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICBhcmlhLWhpZGRlbj1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgZGF0YS10ZXN0aWQ9XCJ3ZWRkaW5nLXZpZGVvcy1sb2FkLW1vcmUtdHJpZ2dlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgIHsvKiBMb2FkaW5nIGluZGljYXRvciAtIG9ubHkgc2hvdyB3aGVuIGxvYWRpbmcgbW9yZSAqL31cclxuICAgICAgICAgICAgICAgICAge2xvYWRpbmdNb3JlICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLXctWzEwMHB4XSBoLVszMDBweF0gc206aC1bNDAwcHhdIG1kOmgtWzQ1MHB4XSBsZzpoLVs1MDBweF1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIHctNiBoLTYgYm9yZGVyLTIgYm9yZGVyLXJlZC01MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIG1vcmUuLi48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgey8qIFJpZ2h0IHNjcm9sbCBidXR0b24gKi99XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTWFudWFsU2Nyb2xsKFwicmlnaHRcIil9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgei0xMCBiZy13aGl0ZS84MCByb3VuZGVkLWZ1bGwgcC0yIHNoYWRvdy1tZCBob3ZlcjpiZy13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBtYXJnaW5SaWdodDogXCItMTJweFwiIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPE1kQ2hldnJvblJpZ2h0IHNpemU9ezI0fSAvPlxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC8+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICA8c3R5bGUganN4PntgXHJcbiAgICAgICAgLnNjcm9sbGJhci1oaWRlOjotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgICAgIH1cclxuICAgICAgICAuc2Nyb2xsYmFyLWhpZGUge1xyXG4gICAgICAgICAgLW1zLW92ZXJmbG93LXN0eWxlOiBub25lO1xyXG4gICAgICAgICAgc2Nyb2xsYmFyLXdpZHRoOiBub25lO1xyXG4gICAgICAgIH1cclxuICAgICAgYH08L3N0eWxlPlxyXG4gICAgPC9zZWN0aW9uPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBXZWRkaW5nVmlkZW9zU2VjdGlvbjsiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsIkltYWdlIiwidXNlUm91dGVyIiwiVXNlckF2YXRhciIsImF4aW9zIiwiTWRGYXZvcml0ZSIsIk1kRmF2b3JpdGVCb3JkZXIiLCJNZE91dGxpbmVNb2RlQ29tbWVudCIsIk1kT3V0bGluZVNoYXJlIiwiTWRDaGV2cm9uTGVmdCIsIk1kQ2hldnJvblJpZ2h0IiwiV2VkZGluZ1ZpZGVvc1NlY3Rpb24iLCJzaG91bGRMb2FkIiwicm91dGVyIiwibGlrZWRWaWRlb3MiLCJzZXRMaWtlZFZpZGVvcyIsImFkbWlyaW5nVXNlcnMiLCJzZXRBZG1pcmluZ1VzZXJzIiwid2VkZGluZ1ZpZGVvcyIsInNldFdlZGRpbmdWaWRlb3MiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJwYWdlIiwic2V0UGFnZSIsImhhc01vcmUiLCJzZXRIYXNNb3JlIiwibG9hZGluZ01vcmUiLCJzZXRMb2FkaW5nTW9yZSIsImluaXRpYWxMb2FkQ29tcGxldGUiLCJzZXRJbml0aWFsTG9hZENvbXBsZXRlIiwic2Nyb2xsQ29udGFpbmVyUmVmIiwibG9hZE1vcmVUcmlnZ2VyUmVmIiwib2JzZXJ2ZXJSZWYiLCJnZXRGYWxsYmFja01vdmllcyIsImZldGNoTW92aWVzIiwicGFnZU51bWJlciIsImlzSW5pdGlhbExvYWQiLCJjb25zb2xlIiwibG9nIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwid2FybiIsInJlc3BvbnNlIiwiZ2V0IiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJzdGF0dXMiLCJkYXRhIiwibW92aWVzIiwibGVuZ3RoIiwicHJvY2Vzc2VkTW92aWVzIiwibWFwIiwibW92aWUiLCJ2aWRlb190aHVtYm5haWwiLCJ2aWRlb19pZCIsInVzZXJfaWQiLCJ1c2VyX25hbWUiLCJ0b0xvd2VyQ2FzZSIsInJlcGxhY2UiLCJsb2FkTGlrZVN0YXR1cyIsInZpZGVvIiwicHJldiIsIm5ld1ZpZGVvSWRzIiwiZXhpc3RpbmdMaWtlZE1vdmllc0RhdGEiLCJleGlzdGluZ0xpa2VkTW92aWVzIiwiSlNPTiIsInBhcnNlIiwibmV3TGlrZWRWaWRlb3MiLCJmaWx0ZXIiLCJpZCIsImluY2x1ZGVzIiwibmV4dF9wYWdlIiwiZXJyIiwidGhlbiIsImNhdGNoIiwiZmluYWxseSIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJjdXJyZW50IiwiZGlzY29ubmVjdCIsIkludGVyc2VjdGlvbk9ic2VydmVyIiwiZW50cmllcyIsImlzSW50ZXJzZWN0aW5nIiwiaW50ZXJzZWN0aW9uUmF0aW8iLCJuZXh0UGFnZSIsInJvb3QiLCJ0aHJlc2hvbGQiLCJyb290TWFyZ2luIiwib2JzZXJ2ZSIsImFkbWlyZWRVc2Vyc0pzb24iLCJhZG1pcmVkVXNlcnMiLCJuYXZpZ2F0ZVRvVXNlclByb2ZpbGUiLCJ1c2VySWQiLCJ1c2VybmFtZSIsInB1c2giLCJ0b2dnbGVMaWtlIiwidmlkZW9JZCIsImlzQ3VycmVudGx5TGlrZWQiLCJlbmRwb2ludCIsImZldGNoIiwibWV0aG9kIiwiYm9keSIsInN0cmluZ2lmeSIsImNvbnRlbnRfaWQiLCJjb250ZW50X3R5cGUiLCJvayIsImVycm9yVGV4dCIsInRleHQiLCJFcnJvciIsInJlc3BvbnNlRGF0YSIsImpzb24iLCJsaWtlZE1vdmllc0RhdGEiLCJsaWtlZE1vdmllc0FycmF5IiwidXBkYXRlZEFycmF5Iiwic2V0SXRlbSIsIndhc0xpa2VkIiwidmlkZW9JZHMiLCJjdXJyZW50TGlrZWRWaWRlb3MiLCJoYW5kbGVBZG1pcmUiLCJpc0N1cnJlbnRseUFkbWlyaW5nIiwicG9zdCIsInRhcmdldF91c2VyX2lkIiwic3RvcmFnZUVycm9yIiwiYXBpRXJyb3IiLCJoYW5kbGVNYW51YWxTY3JvbGwiLCJkaXJlY3Rpb24iLCJzY3JvbGxBbW91bnQiLCJzY3JvbGxCeSIsImxlZnQiLCJiZWhhdmlvciIsImdldEltYWdlU291cmNlIiwic2VjdGlvbiIsImRpdiIsImgyIiwiYSIsImhyZWYiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsInN0eWxlIiwibWFyZ2luTGVmdCIsInNpemUiLCJyZWYiLCJXZWJraXRPdmVyZmxvd1Njcm9sbGluZyIsInNjcm9sbGJhcldpZHRoIiwibXNPdmVyZmxvd1N0eWxlIiwic2Nyb2xsQmVoYXZpb3IiLCJpbmRleCIsImZvbnRGYW1pbHkiLCJmb250V2VpZ2h0IiwiZm9udFNpemUiLCJsaW5lSGVpZ2h0IiwibGV0dGVyU3BhY2luZyIsInZlcnRpY2FsQWxpZ24iLCJjdXJzb3IiLCJzcmMiLCJhbHQiLCJ2aWRlb19uYW1lIiwiZmlsbCIsInNpemVzIiwiY2xhc3NOYW1lIiwicHJpb3JpdHkiLCJ1bm9wdGltaXplZCIsInBsYWNlaG9sZGVyIiwiYmx1ckRhdGFVUkwiLCJvbkVycm9yIiwiZSIsImltZ0VsZW1lbnQiLCJ0YXJnZXQiLCJwb3NpdGlvbiIsInRvcCIsInJpZ2h0IiwiYm90dG9tIiwiekluZGV4IiwiYm9yZGVyUmFkaXVzIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImJhY2tncm91bmRDb2xvciIsIndpbmRvdyIsImxvY2F0aW9uIiwic3ZnIiwieG1sbnMiLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJwYXRoIiwiZCIsInBhZGRpbmciLCJmbGV4RGlyZWN0aW9uIiwiZ2FwIiwidGl0bGUiLCJjb2xvciIsInZpZGVvX2xpa2VzIiwidG9GaXhlZCIsInZpZGVvX3ZpZXdzIiwidmlkZW9fZGVzY3JpcHRpb24iLCJvdXRsaW5lIiwicHJvY2VzcyIsImFyaWEtaGlkZGVuIiwiZGF0YS10ZXN0aWQiLCJtYXJnaW5SaWdodCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/WeddingVideos.tsx\n"));

/***/ })

});