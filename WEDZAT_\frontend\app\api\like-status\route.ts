import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await request.json();
    const { content_id, content_type } = body;
    
    if (!content_id || !content_type) {
      return NextResponse.json(
        { error: 'content_id and content_type are required' },
        { status: 400 }
      );
    }
    
    // Since the backend doesn't have a specific like-status endpoint,
    // we'll return false for now and let the user interactions update the state
    // The backend like/unlike APIs will handle the actual state persistence
    
    return NextResponse.json({
      liked: false, // Default to false, will be updated by user interactions
      content_id,
      content_type
    });
    
  } catch (error: any) {
    console.error('Error in like-status API:', error);
    
    return NextResponse.json(
      { error: 'Failed to check like status' },
      { status: 500 }
    );
  }
}
