import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await request.json();
    const { content_ids, content_type, content_id } = body;

    // Handle both single content_id and multiple content_ids
    const idsToCheck = content_ids || (content_id ? [content_id] : []);

    if (!idsToCheck.length || !content_type) {
      return NextResponse.json(
        { error: 'content_ids (or content_id) and content_type are required' },
        { status: 400 }
      );
    }

    console.log('Like-status API - Checking like status for:', idsToCheck);

    try {
      // Try to get like status from backend
      // Since there might not be a bulk like-status endpoint, we'll try individual checks
      const likeStatusPromises = idsToCheck.map(async (id: string) => {
        try {
          // Try to like the content - if it's already liked, we'll get an error
          const testResponse = await axios.post(
            'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/like',
            { content_id: id, content_type },
            {
              headers: {
                'Authorization': authHeader,
                'Content-Type': 'application/json',
              },
              timeout: 10000,
            }
          );

          // If like succeeded, it wasn't liked before, so unlike it and return false
          await axios.post(
            'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unlike',
            { content_id: id, content_type },
            {
              headers: {
                'Authorization': authHeader,
                'Content-Type': 'application/json',
              },
              timeout: 10000,
            }
          );

          return { id, liked: false };
        } catch (error: any) {
          // If like failed with "already liked" error, it means it was already liked
          if (error.response && error.response.status === 400 &&
              error.response.data &&
              (error.response.data.error?.includes('already') ||
               error.response.data.message?.includes('already'))) {
            return { id, liked: true };
          }

          // For other errors, assume not liked
          console.warn(`Error checking like status for ${id}:`, error.response?.data || error.message);
          return { id, liked: false };
        }
      });

      const results = await Promise.all(likeStatusPromises);
      const likedContentIds = results.filter(r => r.liked).map(r => r.id);

      console.log('Like-status API - Individual results:', results);
      console.log('Like-status API - Liked content IDs:', likedContentIds);
      console.log('Like-status API - Total checked:', idsToCheck.length);

      // Return format that matches what the frontend expects
      if (content_id) {
        // Single content ID request
        const result = results.find(r => r.id === content_id);
        return NextResponse.json({
          liked: result?.liked || false,
          content_id,
          content_type
        });
      } else {
        // Multiple content IDs request
        return NextResponse.json({
          liked_content_ids: likedContentIds,
          content_type,
          total_checked: idsToCheck.length,
          total_liked: likedContentIds.length
        });
      }

    } catch (error: any) {
      console.error('Error checking like status from backend:', error);

      // Fallback: return all as not liked
      if (content_id) {
        return NextResponse.json({
          liked: false,
          content_id,
          content_type,
          fallback: true
        });
      } else {
        return NextResponse.json({
          liked_content_ids: [],
          content_type,
          total_checked: idsToCheck.length,
          total_liked: 0,
          fallback: true
        });
      }
    }

  } catch (error: any) {
    console.error('Error in like-status API:', error);

    return NextResponse.json(
      { error: 'Failed to check like status' },
      { status: 500 }
    );
  }
}
