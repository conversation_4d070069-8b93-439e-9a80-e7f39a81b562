"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { authService } from '../../../services/api';

export default function VendorVerificationPage() {
  const router = useRouter();
  const [emailCode, setEmailCode] = useState('');
  const [phoneCode, setPhoneCode] = useState('');
  const [emailVerified, setEmailVerified] = useState(false);
  const [phoneVerified, setPhoneVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Check if user is authenticated and is a vendor
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (!authService.isAuthenticated()) {
        router.push('/login');
        return;
      }

      // Check if user is a vendor
      const isVendor = localStorage.getItem('is_vendor') === 'true';
      if (!isVendor) {
        // If not a vendor, redirect to home
        console.log('User is not a vendor, redirecting to home');
        router.push('/home');
        return;
      }
    }
  }, [router]);

  const handleEmailVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!emailCode) {
      setError('Please enter the email verification code');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await authService.verifyVendorEmail({ code: emailCode });
      setEmailVerified(true);
      setSuccessMessage('Email verified successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err: any) {
      setError(err.error || 'Failed to verify email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendOTP = async () => {
    setIsLoading(true);
    setError('');

    try {
      await authService.sendVendorOTP();
      setSuccessMessage('OTP sent to your phone!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err: any) {
      setError(err.error || 'Failed to send OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phoneCode) {
      setError('Please enter the OTP');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await authService.verifyVendorOTP({ code: phoneCode });
      setPhoneVerified(true);
      setSuccessMessage('Phone verified successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err: any) {
      setError(err.error || 'Failed to verify phone. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinue = () => {
    router.push('/vendor/dashboard');
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <Image
            src="/pics/logo.png"
            alt="Wedzat logo"
            width={60}
            height={60}
            className="object-cover"
          />
        </div>

        <h1 className="text-2xl font-bold text-center mb-6">Verify Your Account</h1>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg">
            {successMessage}
          </div>
        )}

        {/* Email Verification */}
        <div className={`mb-6 p-4 border rounded-lg ${emailVerified ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200'}`}>
          <h2 className="text-lg font-semibold mb-2">Email Verification</h2>

          {emailVerified ? (
            <div className="flex items-center text-green-600">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span>Email verified successfully</span>
            </div>
          ) : (
            <form onSubmit={handleEmailVerification}>
              <p className="text-sm text-gray-600 mb-3">
                We've sent a verification code to your email. Please enter it below.
              </p>
              <div className="mb-3">
                <input
                  type="text"
                  placeholder="Enter verification code"
                  className="w-full p-3 border border-gray-300 rounded-lg"
                  value={emailCode}
                  onChange={(e) => setEmailCode(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <button
                type="submit"
                className="w-full bg-red-700 text-white py-2 rounded-md hover:bg-red-800 transition duration-200 disabled:bg-red-300"
                disabled={isLoading}
              >
                {isLoading ? "Verifying..." : "Verify Email"}
              </button>
            </form>
          )}
        </div>

        {/* Phone Verification */}
        <div className={`mb-6 p-4 border rounded-lg ${phoneVerified ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200'}`}>
          <h2 className="text-lg font-semibold mb-2">Phone Verification</h2>

          {phoneVerified ? (
            <div className="flex items-center text-green-600">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span>Phone verified successfully</span>
            </div>
          ) : (
            <form onSubmit={handlePhoneVerification}>
              <p className="text-sm text-gray-600 mb-3">
                We need to verify your phone number. Click below to receive an OTP.
              </p>
              <div className="mb-3">
                <button
                  type="button"
                  onClick={handleSendOTP}
                  className="w-full mb-3 bg-gray-200 text-gray-800 py-2 rounded-md hover:bg-gray-300 transition duration-200 disabled:bg-gray-100"
                  disabled={isLoading}
                >
                  {isLoading ? "Sending..." : "Send OTP"}
                </button>
                <input
                  type="text"
                  placeholder="Enter OTP"
                  className="w-full p-3 border border-gray-300 rounded-lg"
                  value={phoneCode}
                  onChange={(e) => setPhoneCode(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <button
                type="submit"
                className="w-full bg-red-700 text-white py-2 rounded-md hover:bg-red-800 transition duration-200 disabled:bg-red-300"
                disabled={isLoading}
              >
                {isLoading ? "Verifying..." : "Verify Phone"}
              </button>
            </form>
          )}
        </div>

        {/* Continue Button */}
        <button
          onClick={handleContinue}
          className={`w-full py-3 rounded-md transition duration-200 ${
            emailVerified && phoneVerified
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          }`}
          disabled={!emailVerified || !phoneVerified}
        >
          Continue to Dashboard
        </button>
      </div>
    </div>
  );
}
