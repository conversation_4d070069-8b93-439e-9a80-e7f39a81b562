"use client";
import React, { useState, useEffect, useRef, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "../../../../services/axiosConfig";
import UserAvatar from "../../../../components/HomeDashboard/UserAvatar";
import FlashVendorDetails from "../../../../components/FlashVendorDetails";
import {
  ArrowLeft,
  ChevronUp,
  ChevronDown
} from "lucide-react";
import {
  TopNavigation,
  SideNavigation,
  RightSidebar,
} from "../../../../components/HomeDashboard/Navigation";

// Define interface for flash video items
interface FlashVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface ApiResponse {
  flashes: FlashVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

// Create a Client component that uses useSearchParams
function FlashShortsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialIndex = parseInt(searchParams?.get("index") || "0");

  const [flashes, setFlashes] = useState<FlashVideo[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(initialIndex);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [likedFlashes, setLikedFlashes] = useState<Set<string>>(new Set());
  const [leftSidebarExpanded, setLeftSidebarExpanded] = useState(false);
  const [rightSidebarExpanded, setRightSidebarExpanded] = useState(false);
  const [isPaused, setIsPaused] = useState<{ [key: string]: boolean }>({});
  const [showVendorDetails, setShowVendorDetails] = useState<{ [key: string]: boolean }>({});
  const [admiringUsers, setAdmiringUsers] = useState<{ [key: string]: boolean }>({}); // Track admiring state
  const [videoLoading, setVideoLoading] = useState<{ [key: string]: boolean }>({});

  // Refs for video elements
  const videoRefs = useRef<{ [key: string]: HTMLVideoElement | null }>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const youtubeTimerRef = useRef<NodeJS.Timeout | null>(null);

  // User avatar placeholders - using placeholder.svg which exists
  const userAvatarPlaceholders = [
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
  ];

  useEffect(() => setIsClient(true), []);

  // Load admiring state from localStorage
  useEffect(() => {
    if (!isClient) return;

    try {
      const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
      const admiredUsers = JSON.parse(admiredUsersJson);
      setAdmiringUsers(admiredUsers);
    } catch (error) {
      console.error('Error loading admired users from localStorage:', error);
    }
  }, [isClient]);

  // Fetch flashes from the API
  useEffect(() => {
    const fetchFlashes = async () => {
      try {
        setLoading(true);
        // Get token from localStorage
        const token = localStorage.getItem("token");

        if (!token) {
          console.warn("No authentication token found");
          setError("Authentication required");
          return;
        }

        const response = await axios.get<ApiResponse>(
          `/flashes?page=${page}&limit=10`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.data && response.data.flashes) {
          console.log("Flashes API response:", response.data);

          if (page === 1) {
            setFlashes(response.data.flashes);
            // Initialize loading state for all videos
            const initialLoadingState: { [key: string]: boolean } = {};
            response.data.flashes.forEach((flash: FlashVideo) => {
              initialLoadingState[flash.video_id] = true;
            });
            setVideoLoading(initialLoadingState);

            // Fetch like status for initial flashes
            fetchLikeStatus(response.data.flashes.map((f: FlashVideo) => f.video_id));
          } else {
            setFlashes((prev) => [...prev, ...response.data.flashes]);
            // Initialize loading state for new videos
            setVideoLoading(prev => {
              const newState = { ...prev };
              response.data.flashes.forEach((flash: FlashVideo) => {
                newState[flash.video_id] = true;
              });
              return newState;
            });

            // Fetch like status for new flashes
            fetchLikeStatus(response.data.flashes.map((f: FlashVideo) => f.video_id));
          }

          setHasMore(response.data.next_page);
        } else {
          console.warn("Unexpected API response format:", response.data);
          setError("Failed to load flashes");
        }
      } catch (err) {
        console.error("Error fetching flashes:", err);
        setError("Failed to load flashes");
      } finally {
        setLoading(false);
      }
    };

    fetchFlashes();
  }, [page]);

  // Load more flashes when reaching the end
  useEffect(() => {
    if (currentIndex >= flashes.length - 2 && hasMore && !loading) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [currentIndex, flashes.length, hasMore, loading]);

  // Handle video playback when current index changes
  useEffect(() => {
    if (flashes.length === 0 || !isClient) return;

    // Clear any existing YouTube timer
    if (youtubeTimerRef.current) {
      clearTimeout(youtubeTimerRef.current);
      youtubeTimerRef.current = null;
    }

    // Pause all videos
    Object.values(videoRefs.current).forEach((videoEl) => {
      if (videoEl && !videoEl.paused) {
        videoEl.pause();
      }
    });

    const currentFlash = flashes[currentIndex];
    if (!currentFlash) return;

    // Handle YouTube videos with timer-based auto-advance
    if (isYoutubeVideo(currentFlash) && !isPaused[currentFlash.video_id]) {
      // Set a timer for YouTube videos (assuming average duration of 30 seconds)
      // In a real implementation, you might want to use YouTube API to get actual duration
      youtubeTimerRef.current = setTimeout(() => {
        console.log(`YouTube video timer ended: ${currentFlash.video_id}, auto-advancing to next flash`);
        if (!isPaused[currentFlash.video_id]) {
          navigateToNext();
        }
      }, 30000); // 30 seconds default duration for YouTube videos
    } else {
      // Play current video if not manually paused (for non-YouTube videos)
      const currentVideoId = currentFlash.video_id;
      const currentVideo = videoRefs.current[currentVideoId];
      if (currentVideo && !isPaused[currentVideoId]) {
        const playPromise = currentVideo.play();
        if (playPromise !== undefined) {
          playPromise.catch((error) => {
            console.error("Error playing video:", error);
          });
        }
      }
    }

    // Cleanup function
    return () => {
      if (youtubeTimerRef.current) {
        clearTimeout(youtubeTimerRef.current);
        youtubeTimerRef.current = null;
      }
    };
  }, [currentIndex, flashes, isClient, isPaused]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowUp" || e.key === "ArrowLeft") {
        navigateToPrevious();
      } else if (e.key === "ArrowDown" || e.key === "ArrowRight") {
        navigateToNext();
      } else if (e.key === "Escape") {
        router.push("/home/<USER>");
      } else if (e.key === " " || e.key === "Spacebar") {
        // Toggle play/pause on spacebar
        togglePlayPause(flashes[currentIndex]?.video_id);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [currentIndex, flashes.length]);

  // Handle touch events for swiping
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    let startY = 0;
    // let startTime = 0; // Uncomment if needed for timing-based gestures

    const handleTouchStart = (e: TouchEvent) => {
      startY = e.touches[0].clientY;
      // startTime = Date.now(); // Uncomment if needed for timing-based gestures
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const deltaY = e.changedTouches[0].clientY - startY;
      // const deltaTime = Date.now() - startTime; // Uncomment if needed for timing-based gestures

      // Make touch more responsive by reducing the threshold
      if (Math.abs(deltaY) > 30) {
        if (deltaY > 0) {
          // Swipe down - go to previous
          navigateToPrevious();
        } else {
          // Swipe up - go to next
          navigateToNext();
        }
      }
    };

    // Add touchmove handler for more responsive scrolling
    let lastY = 0;
    let touchMoveThrottle = false;

    const handleTouchMove = (e: TouchEvent) => {
      const currentY = e.touches[0].clientY;

      // Only process every few pixels of movement to avoid too many updates
      if (!touchMoveThrottle && Math.abs(currentY - lastY) > 20) {
        lastY = currentY;
        touchMoveThrottle = true;

        // Schedule reset of throttle
        setTimeout(() => {
          touchMoveThrottle = false;
        }, 100);
      }
    };

    const container = containerRef.current;
    container.addEventListener("touchstart", handleTouchStart);
    container.addEventListener("touchmove", handleTouchMove, { passive: true });
    container.addEventListener("touchend", handleTouchEnd);

    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchmove", handleTouchMove);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isClient, currentIndex, flashes.length]);

  // Handle wheel events for touchpad scrolling
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    const handleWheel = (e: WheelEvent) => {
      // Debounce the wheel event to prevent too many navigations
      if (e.deltaY > 50) {
        // Scroll down - go to next
        navigateToNext();
      } else if (e.deltaY < -50) {
        // Scroll up - go to previous
        navigateToPrevious();
      }
    };

    const container = containerRef.current;
    container.addEventListener("wheel", handleWheel, { passive: true });

    return () => {
      container.removeEventListener("wheel", handleWheel);
    };
  }, [isClient, currentIndex, flashes.length]);

  const navigateToNext = () => {
    if (currentIndex < flashes.length - 1) {
      setCurrentIndex((prevIndex) => prevIndex + 1);
    } else {
      // When reaching the last video, loop back to the first one
      setCurrentIndex(0);
    }
  };

  const navigateToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prevIndex) => prevIndex - 1);
    }
  };

  // Auto-advance to next flash when current video ends
  const handleVideoEnded = (videoId: string) => {
    console.log(`Video ended: ${videoId}, auto-advancing to next flash`);
    // Only auto-advance if the video wasn't manually paused
    if (!isPaused[videoId]) {
      navigateToNext();
    }
  };

  const toggleLike = async (flashId: string) => {
    console.log('🔥 toggleLike called for flashId:', flashId);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Optimistically update UI
      const isCurrentlyLiked = likedFlashes.has(flashId);
      console.log('🔥 isCurrentlyLiked:', isCurrentlyLiked);
      setLikedFlashes((prev) => {
        const newLiked = new Set(prev);
        if (isCurrentlyLiked) {
          newLiked.delete(flashId);
        } else {
          newLiked.add(flashId);
        }
        return newLiked;
      });

      // Make API call to Next.js API routes
      const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content_id: flashId,
          content_type: 'video'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔥 API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('🔥 API Success Response:', responseData);

      // Update localStorage to persist like status
      const likedFlashesData = localStorage.getItem('likedFlashes');
      const likedFlashesArray = likedFlashesData ? JSON.parse(likedFlashesData) : [];

      if (isCurrentlyLiked) {
        // Remove from liked flashes
        const updatedLikedFlashes = likedFlashesArray.filter((id: string) => id !== flashId);
        localStorage.setItem('likedFlashes', JSON.stringify(updatedLikedFlashes));
      } else {
        // Add to liked flashes
        if (!likedFlashesArray.includes(flashId)) {
          likedFlashesArray.push(flashId);
          localStorage.setItem('likedFlashes', JSON.stringify(likedFlashesArray));
        }
      }

      console.log(`${isCurrentlyLiked ? 'Unliked' : 'Liked'} flash: ${flashId}`);
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert optimistic update on error
      setLikedFlashes((prev) => {
        const newLiked = new Set(prev);
        if (likedFlashes.has(flashId)) {
          newLiked.delete(flashId);
        } else {
          newLiked.add(flashId);
        }
        return newLiked;
      });
    }
  };

  // Fetch like status for flashes from localStorage (temporary solution)
  const fetchLikeStatus = async (flashIds: string[]) => {
    try {
      const token = localStorage.getItem('token');
      if (!token || flashIds.length === 0) return;

      // Get liked flashes from localStorage as a temporary solution
      // In a real implementation, this would come from the backend
      const likedFlashesData = localStorage.getItem('likedFlashes');
      const likedFlashesArray = likedFlashesData ? JSON.parse(likedFlashesData) : [];

      // Filter to only include flashes that are currently loaded
      const currentLikedFlashes = flashIds.filter(id => likedFlashesArray.includes(id));

      console.log('Loaded like status for flashes:', currentLikedFlashes);
      setLikedFlashes(new Set(currentLikedFlashes));
    } catch (error) {
      console.error('Error fetching like status:', error);
      setLikedFlashes(new Set());
    }
  };

  const togglePlayPause = (videoId: string) => {
    if (!videoId) return;

    const currentFlash = flashes.find(flash => flash.video_id === videoId);
    if (!currentFlash) return;

    setIsPaused((prev) => {
      const newState = { ...prev };
      newState[videoId] = !prev[videoId];

      if (isYoutubeVideo(currentFlash)) {
        // Handle YouTube video pause/play
        if (newState[videoId]) {
          // Paused - clear the timer
          if (youtubeTimerRef.current) {
            clearTimeout(youtubeTimerRef.current);
            youtubeTimerRef.current = null;
          }
        } else {
          // Resumed - restart the timer
          youtubeTimerRef.current = setTimeout(() => {
            console.log(`YouTube video timer ended: ${videoId}, auto-advancing to next flash`);
            if (!newState[videoId]) {
              navigateToNext();
            }
          }, 30000); // 30 seconds default duration
        }
      } else {
        // Handle regular video pause/play
        const videoEl = videoRefs.current[videoId];
        if (videoEl) {
          if (newState[videoId]) {
            videoEl.pause();
          } else {
            videoEl
              .play()
              .catch((err) => console.error("Error playing video:", err));
          }
        }
      }

      return newState;
    });
  };

  // Extract YouTube video ID from URL
  const getYoutubeId = (url: string): string => {
    if (!url) return "";

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes("/")) return url;

    // Try to extract ID from YouTube URL
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : "";
  };

  // Get appropriate image source for a flash
  const getImageSource = (flash: FlashVideo): string => {
    // If we have a thumbnail, use it
    if (flash.video_thumbnail) {
      return flash.video_thumbnail;
    }

    // If it's a YouTube video, use the YouTube thumbnail
    if (flash.video_url && flash.video_url.includes("youtube")) {
      const videoId = getYoutubeId(flash.video_url);
      if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
      }
    }

    // Default fallback - use a local placeholder image
    return "/pics/placeholder.svg";
  };

  // Check if the video is from YouTube
  const isYoutubeVideo = (flash: FlashVideo): boolean => {
    return (
      typeof flash.video_url === "string" && flash.video_url.includes("youtube")
    );
  };

  // Format numbers for display (e.g., 1.2K)
  const formatNumber = (num: number | undefined): string => {
    if (!num) return "0";
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // Add custom CSS for styling and responsiveness
  useEffect(() => {
    // Add a style tag for styling and responsiveness
    const style = document.createElement("style");
    style.innerHTML = `
      .shorts-page {
        background-color: #f8f8f8;
      }

      .shorts-container {
        background-color: #000;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        border-radius: 12px;
        overflow: hidden;
        position: relative;
      }

      .shorts-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background-color: #000;
      }

      .shorts-controls {
        position: absolute;
        right: 8px;
        bottom: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        z-index: 20;
      }

      .shorts-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 16px;
        background: linear-gradient(transparent, rgba(0,0,0,0.8));
        z-index: 10;
      }

      /* Fixed layout styles for proper centering */
      .layout-container {
        display: flex;
        width: 100%;
      }

      .left-sidebar {
        width: 80px;
        min-width: 80px;
        transition: all 300ms ease-in-out;
      }

      .left-sidebar.expanded {
        width: 192px;
        min-width: 192px;
      }

      .right-sidebar {
        width: 80px;
        min-width: 80px;
        transition: all 300ms ease-in-out;
      }

      .right-sidebar.expanded {
        width: 192px;
        min-width: 192px;
      }

      .main-content {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 300ms ease-in-out;
      }

      /* Mobile responsive styles */
      @media (max-width: 768px) {
        .shorts-page {
          background-color: #000;
          padding-bottom: env(safe-area-inset-bottom, 20px) !important;
        }

        .shorts-container {
          width: 100vw !important;
          max-width: none !important;
          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;
          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;
          max-height: none !important;
          border-radius: 0 !important;
          border: none !important;
          margin: 0 !important;
          padding-bottom: 20px !important;
        }

        .mobile-video-item {
          margin-bottom: 10px !important;
          border-radius: 8px !important;
          overflow: hidden !important;
        }

        .mobile-nav-buttons {
          position: fixed !important;
          left: 16px !important;
          top: 50% !important;
          transform: translateY(-50%) !important;
          z-index: 50 !important;
          display: flex !important;
          flex-direction: column !important;
          gap: 20px !important;
        }

        .mobile-nav-button {
          width: 48px !important;
          height: 48px !important;
          border-radius: 50% !important;
          background: rgba(255, 255, 255, 0.9) !important;
          backdrop-filter: blur(10px) !important;
          border: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }

        .mobile-nav-button:disabled {
          background: rgba(255, 255, 255, 0.3) !important;
          color: rgba(0, 0, 0, 0.3) !important;
        }

        .mobile-interaction-buttons {
          position: fixed !important;
          right: 16px !important;
          bottom: calc(120px + env(safe-area-inset-bottom, 20px)) !important;
          z-index: 50 !important;
          display: flex !important;
          flex-direction: column !important;
          gap: 20px !important;
        }

        .mobile-interaction-button {
          width: 48px !important;
          height: 48px !important;
          border-radius: 50% !important;
          background: rgba(0, 0, 0, 0.6) !important;
          backdrop-filter: blur(10px) !important;
          border: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          color: white !important;
        }

        .mobile-back-button {
          position: fixed !important;
          top: 20px !important;
          left: 16px !important;
          z-index: 50 !important;
          width: 48px !important;
          height: 48px !important;
          border-radius: 50% !important;
          background: rgba(255, 255, 255, 0.9) !important;
          backdrop-filter: blur(10px) !important;
          border: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }

        .mobile-user-info {
          position: absolute !important;
          top: 20px !important;
          left: 80px !important;
          right: 16px !important;
          z-index: 50 !important;
          background: rgba(0, 0, 0, 0.6) !important;
          backdrop-filter: blur(10px) !important;
          border-radius: 12px !important;
          padding: 12px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: space-between !important;
        }

        .mobile-unlock-vendor {
          position: absolute !important;
          bottom: calc(20px + env(safe-area-inset-bottom, 10px)) !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          z-index: 50 !important;
          background: #B31B1E !important;
          color: white !important;
          border: none !important;
          border-radius: 8px !important;
          padding: 12px 24px !important;
          font-weight: 600 !important;
          box-shadow: 0 4px 12px rgba(179, 27, 30, 0.4) !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }

        .mobile-vendor-details {
          position: absolute !important;
          bottom: 80px !important;
          left: 16px !important;
          right: 16px !important;
          z-index: 60 !important;
          background: rgba(255, 255, 255, 0.95) !important;
          backdrop-filter: blur(10px) !important;
          border-radius: 12px !important;
          padding: 16px !important;
          max-height: 50vh !important;
          overflow-y: auto !important;
          color: black !important;
        }

        .mobile-progress-indicator {
          position: fixed !important;
          top: 80px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          z-index: 50 !important;
        }

        /* Hide desktop navigation buttons on mobile */
        .desktop-nav-buttons {
          display: none !important;
        }

        .desktop-interaction-buttons {
          display: none !important;
        }

        /* Ensure main content takes full space on mobile */
        .main-content-mobile {
          padding: 0 !important;
          padding-bottom: env(safe-area-inset-bottom, 20px) !important;
          margin: 0 !important;
          width: 100vw !important;
          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;
          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;
          position: relative !important;
        }
      }

      /* Desktop styles */
      @media (min-width: 769px) {
        .mobile-nav-buttons,
        .mobile-interaction-buttons,
        .mobile-back-button,
        .mobile-user-info,
        .mobile-unlock-vendor,
        .mobile-progress-indicator {
          display: none !important;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  if (!isClient) {
    return <div className="opacity-0">Loading...</div>;
  }

  return (
    <div className="flex flex-col min-h-screen w-full shorts-page">
      {/* Top Navigation Bar - Hidden on mobile */}
      <div className="hidden md:block">
        <TopNavigation />
      </div>

      <div className="flex w-full h-[calc(100vh-80px)] md:mt-20">
        {/* Left Sidebar - Hidden on mobile */}
        <div
          className={`left-sidebar ${
            leftSidebarExpanded ? "expanded" : ""
          } hidden md:block`}
        >
          <SideNavigation
            expanded={leftSidebarExpanded}
            onExpand={() => setLeftSidebarExpanded(true)}
            onCollapse={() => setLeftSidebarExpanded(false)}
          />
        </div>

        {/* Main Content - Responsive layout */}
        <main className="flex-1 flex items-center justify-center px-4 md:px-4 px-0 relative main-content-mobile">
          {/* Desktop Back button */}
          <button
            onClick={() => router.push("/home/<USER>")}
            className="absolute top-4 left-4 z-50 bg-white rounded-full p-3 text-black shadow-lg hover:bg-gray-200 transition-colors flex items-center justify-center hidden md:flex"
            style={{ width: "48px", height: "48px" }}
          >
            <ArrowLeft size={24} />
          </button>

          {/* Mobile Back button */}
          <button
            onClick={() => router.push("/home/<USER>")}
            className="mobile-back-button md:hidden"
          >
            <ArrowLeft size={24} />
          </button>

          <div className="flex items-center w-full md:w-auto">
            {/* Desktop Manual Navigation Buttons - Left Side */}
            <div className="desktop-nav-buttons flex flex-col items-center justify-center space-y-4 mr-4">
              {/* Previous button */}
              <button
                onClick={navigateToPrevious}
                disabled={currentIndex === 0}
                className={`p-3 rounded-full transition-all duration-200 ${
                  currentIndex === 0
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl"
                }`}
                title="Previous Flash"
              >
                <ChevronUp size={24} />
              </button>

              {/* Next button */}
              <button
                onClick={navigateToNext}
                disabled={currentIndex === flashes.length - 1}
                className={`p-3 rounded-full transition-all duration-200 ${
                  currentIndex === flashes.length - 1
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl"
                }`}
                title="Next Flash"
              >
                <ChevronDown size={24} />
              </button>
            </div>

            {/* Mobile Navigation Buttons */}
            <div className="mobile-nav-buttons md:hidden">
              {/* Previous button */}
              <button
                onClick={navigateToPrevious}
                disabled={currentIndex === 0}
                className="mobile-nav-button"
                title="Previous Flash"
              >
                <ChevronUp size={24} />
              </button>

              {/* Next button */}
              <button
                onClick={navigateToNext}
                disabled={currentIndex === flashes.length - 1}
                className="mobile-nav-button"
                title="Next Flash"
              >
                <ChevronDown size={24} />
              </button>
            </div>

            <div
              ref={containerRef}
              className="shorts-container relative w-full md:w-[400px] h-full md:h-[min(calc(100vh-100px),89vh)]"
              style={{
                // Desktop styles
                ...(typeof window !== 'undefined' && window.innerWidth >= 768 ? {
                  width: "400px",
                  height: "min(calc(100vh - 100px), 89vh)",
                  margin: "0 auto",
                  border: "2px solid #B31B1E",
                  borderRadius: "12px",
                  overflow: "hidden",
                } : {
                  // Mobile styles - full screen with safe area
                  width: "100vw",
                  height: "calc(100vh - env(safe-area-inset-bottom, 20px))",
                  margin: "0",
                  border: "none",
                  borderRadius: "0",
                  overflow: "hidden",
                  paddingBottom: "env(safe-area-inset-bottom, 20px)",
                })
              }}
            >
            {/* Loading state */}
            {loading && flashes.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-white">
                Loading flashes...
              </div>
            )}

            {/* Error state */}
            {error && !loading && flashes.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-red-500">
                {error}
              </div>
            )}

            {/* Flashes content */}
            {flashes.length > 0 && (
              <div
                className="h-full w-full transition-transform duration-300 ease-out"
                style={{
                  transform: `translateY(-${currentIndex * 102}%)`, // Match the 102% spacing
                }}
              >
                {flashes.map((flash, index) => (
                  <div
                    key={`${flash.video_id}-${index}`}
                    className={`h-full w-full flex items-center justify-center relative bg-black ${
                      typeof window !== 'undefined' && window.innerWidth < 768 ? 'mobile-video-item' : ''
                    }`}
                    style={{
                      position: "absolute",
                      top: `${index * 102}%`, // Add 2% gap between videos on mobile
                      left: 0,
                      right: 0,
                      bottom: 0,
                      overflow: "hidden",
                      borderRadius: "8px",
                    }}
                  >
                    {/* Video or Image Content */}
                    <div className="h-full w-full relative overflow-hidden">
                      <div className="absolute inset-0 overflow-hidden">
                        {isYoutubeVideo(flash) ? (
                          // YouTube iframe for YouTube videos
                          <div className="relative w-full h-full">
                            {/* YouTube Loading Indicator */}
                            {videoLoading[flash.video_id] !== false && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-20">
                                <div className="flex flex-col items-center text-white">
                                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2"></div>
                                  <div className="text-sm">Loading video...</div>
                                </div>
                              </div>
                            )}
                            <iframe
                              src={`https://www.youtube.com/embed/${getYoutubeId(
                                flash.video_url
                              )}?autoplay=1&controls=0&rel=0&showinfo=0&mute=0`}
                              title={flash.video_name}
                              className="shorts-video"
                              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                              allowFullScreen
                              onLoad={() => {
                                setVideoLoading(prev => ({ ...prev, [flash.video_id]: false }));
                              }}
                            />
                          </div>
                        ) : (
                          // Video player for Cloudfront videos
                          <div className="relative w-full h-full overflow-hidden">
                            {/* Video Loading Indicator */}
                            {videoLoading[flash.video_id] !== false && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-20">
                                <div className="flex flex-col items-center text-white">
                                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2"></div>
                                  <div className="text-sm">Loading video...</div>
                                </div>
                              </div>
                            )}
                            <video
                              ref={(el) => {
                                videoRefs.current[flash.video_id] = el;
                              }}
                              src={flash.video_url}
                              className="shorts-video"
                              playsInline
                              muted={false}
                              controls={false}
                              poster={getImageSource(flash)}
                              onLoadStart={() => {
                                setVideoLoading(prev => ({ ...prev, [flash.video_id]: true }));
                              }}
                              onCanPlay={() => {
                                setVideoLoading(prev => ({ ...prev, [flash.video_id]: false }));
                              }}
                              onError={() => {
                                console.error(`Failed to load video: ${flash.video_name}`);
                                setVideoLoading(prev => ({ ...prev, [flash.video_id]: false }));
                              }}
                              onEnded={() => handleVideoEnded(flash.video_id)}
                            />
                            {/* Play/Pause Button Overlay */}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                togglePlayPause(flash.video_id);
                              }}
                              className="absolute inset-0 w-full h-full flex items-center justify-center z-10 group"
                            >
                              <div
                                className={`${
                                  isPaused[flash.video_id]
                                    ? "opacity-100"
                                    : "opacity-0 group-hover:opacity-100"
                                } transition-opacity duration-200 bg-black/40 rounded-full p-4 shadow-lg`}
                              >
                                {isPaused[flash.video_id] ? (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="32"
                                    height="32"
                                    viewBox="0 0 24 24"
                                    fill="white"
                                    stroke="white"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                  </svg>
                                ) : (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="32"
                                    height="32"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="white"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <rect
                                      x="6"
                                      y="4"
                                      width="4"
                                      height="16"
                                    ></rect>
                                    <rect
                                      x="14"
                                      y="4"
                                      width="4"
                                      height="16"
                                    ></rect>
                                  </svg>
                                )}
                              </div>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Desktop User info at top */}
                    <div className="absolute top-4 left-4 right-4 z-20 flex items-center bg-black/20 backdrop-blur-sm rounded-lg p-2 hidden md:flex">
                      <div className="flex items-center">
                        <UserAvatar
                          username={flash.user_name || "user"}
                          size="sm"
                          isGradientBorder={true}
                          imageUrl={
                            userAvatarPlaceholders[
                              index % userAvatarPlaceholders.length
                            ]
                          }
                        />
                        <div className="ml-2 text-white">
                          <div className="text-sm font-medium">{flash.user_name || "user"}</div>
                          <div className="text-xs opacity-80">{Math.floor(Math.random() * 2) + 0.1}M Admiring</div>
                        </div>
                      </div>

                      {/* Admire button */}
                      <button
                        className="ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle admire functionality
                          const userId = flash.user_id;
                          if (!userId) return;

                          // Check if already admiring this user
                          const isCurrentlyAdmiring = admiringUsers[userId] || false;

                          // Optimistically update UI state
                          setAdmiringUsers(prev => ({
                            ...prev,
                            [userId]: !isCurrentlyAdmiring
                          }));

                          // Update localStorage
                          try {
                            const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
                            const admiredUsers = JSON.parse(admiredUsersJson);

                            if (!isCurrentlyAdmiring) {
                              admiredUsers[userId] = true;
                            } else {
                              delete admiredUsers[userId];
                            }

                            localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));
                          } catch (error) {
                            console.error('Error updating localStorage:', error);
                          }

                          // Make API call in the background
                          const token = localStorage.getItem('token') ||
                            localStorage.getItem('jwt_token') ||
                            localStorage.getItem('wedzat_token');

                          if (token) {
                            const endpoint = isCurrentlyAdmiring
                              ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow'
                              : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';

                            axios.post(
                              endpoint,
                              { target_user_id: userId },
                              { headers: { Authorization: `Bearer ${token}` } }
                            ).catch(error => {
                              console.error('Error with admire API call:', error);
                              // Revert UI state on error
                              setAdmiringUsers(prev => ({
                                ...prev,
                                [userId]: isCurrentlyAdmiring
                              }));
                            });
                          }
                        }}
                      >
                        {admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire'}
                        {!admiringUsers[flash.user_id || ''] && <span className="ml-1">+</span>}
                      </button>
                    </div>

                    {/* Mobile User info at top - Only show for current flash */}
                    {index === currentIndex && (
                      <div className="mobile-user-info md:hidden">
                        <div className="flex items-center">
                          <UserAvatar
                            username={flash.user_name || "user"}
                            size="sm"
                            isGradientBorder={true}
                            imageUrl={
                              userAvatarPlaceholders[
                                index % userAvatarPlaceholders.length
                              ]
                            }
                          />
                          <div className="ml-2 text-white">
                            <div className="text-sm font-medium">{flash.user_name || "user"}</div>
                            <div className="text-xs opacity-80">{Math.floor(Math.random() * 2) + 0.1}M Admiring</div>
                          </div>
                        </div>

                        {/* Admire button */}
                        <button
                          className="ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle admire functionality
                            const userId = flash.user_id;
                            if (!userId) return;

                            // Check if already admiring this user
                            const isCurrentlyAdmiring = admiringUsers[userId] || false;

                            // Optimistically update UI state
                            setAdmiringUsers(prev => ({
                              ...prev,
                              [userId]: !isCurrentlyAdmiring
                            }));

                            // Update localStorage
                            try {
                              const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
                              const admiredUsers = JSON.parse(admiredUsersJson);

                              if (!isCurrentlyAdmiring) {
                                admiredUsers[userId] = true;
                              } else {
                                delete admiredUsers[userId];
                              }

                              localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));
                            } catch (error) {
                              console.error('Error updating localStorage:', error);
                            }

                            // Make API call in the background
                            const token = localStorage.getItem('token') ||
                              localStorage.getItem('jwt_token') ||
                              localStorage.getItem('wedzat_token');

                            if (token) {
                              const endpoint = isCurrentlyAdmiring
                                ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow'
                                : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';

                              axios.post(
                                endpoint,
                                { target_user_id: userId },
                                { headers: { Authorization: `Bearer ${token}` } }
                              ).catch(error => {
                                console.error('Error with admire API call:', error);
                                // Revert UI state on error
                                setAdmiringUsers(prev => ({
                                  ...prev,
                                  [userId]: isCurrentlyAdmiring
                                }));
                              });
                            }
                          }}
                        >
                          {admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire'}
                          {!admiringUsers[flash.user_id || ''] && <span className="ml-1">+</span>}
                        </button>
                      </div>
                    )}

                    {/* Desktop Unlock vendor button at bottom */}
                    <div className="absolute bottom-8 left-0 right-0 flex justify-center z-10 hidden md:flex">
                      <button
                        className="bg-[#B31B1E] text-white text-sm font-medium px-4 py-2 rounded-md flex items-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowVendorDetails(prev => ({
                            ...prev,
                            [flash.video_id]: !prev[flash.video_id]
                          }));
                        }}
                      >
                        Unlock Vendor
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>

                    {/* Mobile Unlock vendor button - Only show for current flash */}
                    {index === currentIndex && (
                      <button
                        className="mobile-unlock-vendor md:hidden"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowVendorDetails(prev => ({
                            ...prev,
                            [flash.video_id]: !prev[flash.video_id]
                          }));
                        }}
                      >
                        Unlock Vendor
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                    )}

                    {/* Desktop Vendor Details Section */}
                    {showVendorDetails[flash.video_id] && (
                      <div className="absolute bottom-20 left-4 right-4 p-4 bg-white/90 rounded-lg text-black max-h-[40%] overflow-y-auto z-30 hidden md:block">
                        <FlashVendorDetails
                          videoId={flash.video_id}
                          isVerified={false}
                        />
                      </div>
                    )}

                    {/* Mobile Vendor Details Section - Only show for current flash */}
                    {index === currentIndex && showVendorDetails[flash.video_id] && (
                      <div className="mobile-vendor-details md:hidden">
                        <FlashVendorDetails
                          videoId={flash.video_id}
                          isVerified={false}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Loading more indicator */}
            {loading && page > 1 && (
              <div className="absolute bottom-20 left-0 right-0 text-center text-white bg-black/50 py-2 mx-auto w-48 rounded-full backdrop-blur-sm z-20">
                Loading more flashes...
              </div>
            )}

            {/* Desktop Progress indicator */}
            <div className="absolute top-14 left-0 right-0 px-4 z-20 hidden md:block">
              <div className="flex gap-1 justify-center">
                {flashes.slice(0, Math.min(flashes.length, 10)).map((_, i) => (
                  <div
                    key={i}
                    className={`h-1 rounded-full ${
                      i === currentIndex ? "bg-white w-6" : "bg-white/40 w-3"
                    } transition-all duration-200`}
                  />
                ))}
                {flashes.length > 10 && (
                  <div className="h-1 rounded-full bg-white/40 w-3" />
                )}
              </div>
            </div>

            {/* Mobile Progress indicator */}
            <div className="mobile-progress-indicator md:hidden">
              <div className="flex gap-1 justify-center">
                {flashes.slice(0, Math.min(flashes.length, 10)).map((_, i) => (
                  <div
                    key={i}
                    className={`h-1 rounded-full ${
                      i === currentIndex ? "bg-white w-6" : "bg-white/40 w-3"
                    } transition-all duration-200`}
                  />
                ))}
                {flashes.length > 10 && (
                  <div className="h-1 rounded-full bg-white/40 w-3" />
                )}
              </div>
            </div>
            </div>

            {/* Desktop Interaction buttons on the right side of the flash container */}
            <div className="desktop-interaction-buttons flex flex-col items-center justify-center space-y-6 ml-4">
              {/* Vertical 3 dots menu button */}
              <button className="text-gray-400 opacity-80 hover:opacity-100 mb-8">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="12" cy="5" r="1"></circle>
                  <circle cx="12" cy="19" r="1"></circle>
                </svg>
              </button>

              {/* Like button */}
              <button
                className={`flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 ${
                  likedFlashes.has(flashes[currentIndex]?.video_id || '')
                    ? 'bg-red-100 hover:bg-red-200'
                    : 'bg-black/20 hover:bg-black/40'
                }`}
                onClick={() => {
                  const currentFlash = flashes[currentIndex];
                  if (currentFlash) {
                    console.log('Desktop like button clicked for flash:', currentFlash.video_id);
                    toggleLike(currentFlash.video_id);
                  }
                }}
                title={likedFlashes.has(flashes[currentIndex]?.video_id || '') ? 'Unlike' : 'Like'}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill={likedFlashes.has(flashes[currentIndex]?.video_id || '') ? "#B31B1E" : "none"} stroke={likedFlashes.has(flashes[currentIndex]?.video_id || '') ? "#B31B1E" : "white"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="transition-colors duration-200">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
              </button>

              {/* Comment button */}
              <button className="text-gray-400 opacity-80 hover:opacity-100">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                </svg>
              </button>

              {/* Share button */}
              <button
                className="text-gray-400 opacity-80 hover:opacity-100"
                onClick={() => {
                  const currentFlash = flashes[currentIndex];
                  if (currentFlash) {
                    const shareUrl = `${window.location.origin}/home/<USER>/shorts?index=${currentIndex}`;
                    navigator.clipboard.writeText(shareUrl).then(() => {
                      console.log('Flash link copied to clipboard:', shareUrl);
                      // You can add a toast notification here
                      alert('Flash link copied to clipboard!');
                    }).catch(err => {
                      console.error('Failed to copy flash link:', err);
                      alert('Failed to copy link');
                    });
                  }
                }}
                title="Share Flash"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
              </button>
            </div>

            {/* Mobile Interaction buttons */}
            <div className="mobile-interaction-buttons md:hidden">
              {/* Profile button */}
              <button
                className="mobile-interaction-button"
                onClick={() => {
                  const currentFlash = flashes[currentIndex];
                  if (currentFlash?.user_id) {
                    // Navigate to user profile - you can implement this navigation
                    console.log('Navigate to profile:', currentFlash.user_id);
                    // router.push(`/profile/${currentFlash.user_id}`);
                  }
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </button>

              {/* Like button */}
              <button
                className={`flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 ${
                  likedFlashes.has(flashes[currentIndex]?.video_id || '')
                    ? 'bg-red-100 hover:bg-red-200'
                    : 'bg-black/20 hover:bg-black/40'
                }`}
                onClick={() => {
                  const currentFlash = flashes[currentIndex];
                  if (currentFlash) {
                    console.log('Mobile like button clicked for flash:', currentFlash.video_id);
                    toggleLike(currentFlash.video_id);
                  }
                }}
                title={likedFlashes.has(flashes[currentIndex]?.video_id || '') ? 'Unlike' : 'Like'}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill={likedFlashes.has(flashes[currentIndex]?.video_id || '') ? "#B31B1E" : "none"} stroke={likedFlashes.has(flashes[currentIndex]?.video_id || '') ? "#B31B1E" : "white"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="transition-colors duration-200">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
              </button>

              {/* Comment button */}
              <button className="mobile-interaction-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                </svg>
              </button>

              {/* Share button */}
              <button
                className="mobile-interaction-button"
                onClick={() => {
                  const currentFlash = flashes[currentIndex];
                  if (currentFlash) {
                    const shareUrl = `${window.location.origin}/home/<USER>/shorts?index=${currentIndex}`;
                    navigator.clipboard.writeText(shareUrl).then(() => {
                      console.log('Flash link copied to clipboard:', shareUrl);
                      alert('Flash link copied to clipboard!');
                    }).catch(err => {
                      console.error('Failed to copy flash link:', err);
                      alert('Failed to copy link');
                    });
                  }
                }}
                title="Share Flash"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
              </button>

              {/* Vertical 3 dots menu button */}
              <button className="mobile-interaction-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="12" cy="5" r="1"></circle>
                  <circle cx="12" cy="19" r="1"></circle>
                </svg>
              </button>
            </div>
          </div>
        </main>

        {/* Right Sidebar - Hidden on mobile */}
        <div
          className={`right-sidebar ${
            rightSidebarExpanded ? "expanded" : ""
          } hidden md:block`}
        >
          <RightSidebar
            expanded={rightSidebarExpanded}
            onExpand={() => setRightSidebarExpanded(true)}
            onCollapse={() => setRightSidebarExpanded(false)}
          />
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function FlashShortsLoading() {
  return (
    <div className="flex items-center justify-center h-screen w-full bg-black">
      <div className="text-white text-xl">Loading flashes...</div>
    </div>
  );
}

// Main page component with Suspense
export default function FlashShortsPage() {
  return (
    <Suspense fallback={<FlashShortsLoading />}>
      <FlashShortsContent />
    </Suspense>
  );
}
