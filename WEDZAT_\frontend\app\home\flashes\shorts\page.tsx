"use client";
import React, { useState, useEffect, useRef, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "../../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../../components/HomeDashboard/UserAvatar";
import FlashVendorDetails from "../../../../components/FlashVendorDetails";
import {
  Heart,
  MessageCircle,
  Share2,
  X,
  Info
} from "lucide-react";
import {
  TopNavigation,
  SideNavigation,
  RightSidebar,
} from "../../../../components/HomeDashboard/Navigation";

// Define interface for flash video items
interface FlashVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface ApiResponse {
  flashes: FlashVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

// Create a Client component that uses useSearchParams
function FlashShortsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialIndex = parseInt(searchParams?.get("index") || "0");

  const [flashes, setFlashes] = useState<FlashVideo[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(initialIndex);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [likedFlashes, setLikedFlashes] = useState<Set<string>>(new Set());
  const [leftSidebarExpanded, setLeftSidebarExpanded] = useState(false);
  const [rightSidebarExpanded, setRightSidebarExpanded] = useState(false);
  const [isPaused, setIsPaused] = useState<{ [key: string]: boolean }>({});
  const [showVendorDetails, setShowVendorDetails] = useState<{ [key: string]: boolean }>({});
  const [admiringUsers, setAdmiringUsers] = useState<{ [key: string]: boolean }>({}); // Track admiring state

  // Refs for video elements
  const videoRefs = useRef<{ [key: string]: HTMLVideoElement | null }>({});
  const containerRef = useRef<HTMLDivElement>(null);

  // User avatar placeholders
  const userAvatarPlaceholders = [
    "/pics/1stim.jfif",
    "/pics/2ndim.jfif",
    "/pics/jpeg3.jfif",
    "/pics/user-profile.png",
    "/pics/user-profile.png",
  ];

  useEffect(() => setIsClient(true), []);

  // Load admiring state from localStorage
  useEffect(() => {
    if (!isClient) return;

    try {
      const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
      const admiredUsers = JSON.parse(admiredUsersJson);
      setAdmiringUsers(admiredUsers);
    } catch (error) {
      console.error('Error loading admired users from localStorage:', error);
    }
  }, [isClient]);

  // Fetch flashes from the API
  useEffect(() => {
    const fetchFlashes = async () => {
      try {
        setLoading(true);
        // Get token from localStorage
        const token = localStorage.getItem("token");

        if (!token) {
          console.warn("No authentication token found");
          setError("Authentication required");
          return;
        }

        const response = await axios.get<ApiResponse>(
          `/flashes?page=${page}&limit=10`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.data && response.data.flashes) {
          console.log("Flashes API response:", response.data);

          if (page === 1) {
            setFlashes(response.data.flashes);
          } else {
            setFlashes((prev) => [...prev, ...response.data.flashes]);
          }

          setHasMore(response.data.next_page);
        } else {
          console.warn("Unexpected API response format:", response.data);
          setError("Failed to load flashes");
        }
      } catch (err) {
        console.error("Error fetching flashes:", err);
        setError("Failed to load flashes");
      } finally {
        setLoading(false);
      }
    };

    fetchFlashes();
  }, [page]);

  // Load more flashes when reaching the end
  useEffect(() => {
    if (currentIndex >= flashes.length - 2 && hasMore && !loading) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [currentIndex, flashes.length, hasMore, loading]);

  // Handle video playback when current index changes
  useEffect(() => {
    if (flashes.length === 0 || !isClient) return;

    // Pause all videos
    Object.values(videoRefs.current).forEach((videoEl) => {
      if (videoEl && !videoEl.paused) {
        videoEl.pause();
      }
    });

    // Play current video if not manually paused
    const currentVideoId = flashes[currentIndex]?.video_id;
    const currentVideo = videoRefs.current[currentVideoId];
    if (currentVideo && !isPaused[currentVideoId]) {
      const playPromise = currentVideo.play();
      if (playPromise !== undefined) {
        playPromise.catch((error) => {
          console.error("Error playing video:", error);
        });
      }
    }
  }, [currentIndex, flashes, isClient, isPaused]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowUp" || e.key === "ArrowLeft") {
        navigateToPrevious();
      } else if (e.key === "ArrowDown" || e.key === "ArrowRight") {
        navigateToNext();
      } else if (e.key === "Escape") {
        router.push("/home/<USER>");
      } else if (e.key === " " || e.key === "Spacebar") {
        // Toggle play/pause on spacebar
        togglePlayPause(flashes[currentIndex]?.video_id);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [currentIndex, flashes.length]);

  // Handle touch events for swiping
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    let startY = 0;
    // let startTime = 0; // Uncomment if needed for timing-based gestures

    const handleTouchStart = (e: TouchEvent) => {
      startY = e.touches[0].clientY;
      // startTime = Date.now(); // Uncomment if needed for timing-based gestures
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const deltaY = e.changedTouches[0].clientY - startY;
      // const deltaTime = Date.now() - startTime; // Uncomment if needed for timing-based gestures

      // Make touch more responsive by reducing the threshold
      if (Math.abs(deltaY) > 30) {
        if (deltaY > 0) {
          // Swipe down - go to previous
          navigateToPrevious();
        } else {
          // Swipe up - go to next
          navigateToNext();
        }
      }
    };

    // Add touchmove handler for more responsive scrolling
    let lastY = 0;
    let touchMoveThrottle = false;

    const handleTouchMove = (e: TouchEvent) => {
      const currentY = e.touches[0].clientY;

      // Only process every few pixels of movement to avoid too many updates
      if (!touchMoveThrottle && Math.abs(currentY - lastY) > 20) {
        lastY = currentY;
        touchMoveThrottle = true;

        // Schedule reset of throttle
        setTimeout(() => {
          touchMoveThrottle = false;
        }, 100);
      }
    };

    const container = containerRef.current;
    container.addEventListener("touchstart", handleTouchStart);
    container.addEventListener("touchmove", handleTouchMove, { passive: true });
    container.addEventListener("touchend", handleTouchEnd);

    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchmove", handleTouchMove);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isClient, currentIndex, flashes.length]);

  // Handle wheel events for touchpad scrolling
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    const handleWheel = (e: WheelEvent) => {
      // Debounce the wheel event to prevent too many navigations
      if (e.deltaY > 50) {
        // Scroll down - go to next
        navigateToNext();
      } else if (e.deltaY < -50) {
        // Scroll up - go to previous
        navigateToPrevious();
      }
    };

    const container = containerRef.current;
    container.addEventListener("wheel", handleWheel, { passive: true });

    return () => {
      container.removeEventListener("wheel", handleWheel);
    };
  }, [isClient, currentIndex, flashes.length]);

  const navigateToNext = () => {
    if (currentIndex < flashes.length - 1) {
      setCurrentIndex((prevIndex) => prevIndex + 1);
    }
  };

  const navigateToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prevIndex) => prevIndex - 1);
    }
  };

  const toggleLike = (flashId: string) => {
    setLikedFlashes((prev) => {
      const newLiked = new Set(prev);
      if (newLiked.has(flashId)) {
        newLiked.delete(flashId);
      } else {
        newLiked.add(flashId);
      }
      return newLiked;
    });
  };

  const togglePlayPause = (videoId: string) => {
    if (!videoId) return;

    const videoEl = videoRefs.current[videoId];
    if (!videoEl) return;

    setIsPaused((prev) => {
      const newState = { ...prev };
      newState[videoId] = !prev[videoId];

      if (newState[videoId]) {
        videoEl.pause();
      } else {
        videoEl
          .play()
          .catch((err) => console.error("Error playing video:", err));
      }

      return newState;
    });
  };

  // Extract YouTube video ID from URL
  const getYoutubeId = (url: string): string => {
    if (!url) return "";

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes("/")) return url;

    // Try to extract ID from YouTube URL
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : "";
  };

  // Get appropriate image source for a flash
  const getImageSource = (flash: FlashVideo): string => {
    // If we have a thumbnail, use it
    if (flash.video_thumbnail) {
      return flash.video_thumbnail;
    }

    // If it's a YouTube video, use the YouTube thumbnail
    if (flash.video_url && flash.video_url.includes("youtube")) {
      const videoId = getYoutubeId(flash.video_url);
      if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
      }
    }

    // Default fallback - use a local placeholder image
    return "/pics/placeholder.svg";
  };

  // Check if the video is from YouTube
  const isYoutubeVideo = (flash: FlashVideo): boolean => {
    return (
      typeof flash.video_url === "string" && flash.video_url.includes("youtube")
    );
  };

  // Format numbers for display (e.g., 1.2K)
  const formatNumber = (num: number | undefined): string => {
    if (!num) return "0";
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // Add custom CSS for styling and responsiveness
  useEffect(() => {
    // Add a style tag for styling and responsiveness
    const style = document.createElement("style");
    style.innerHTML = `
      .shorts-page {
        background-color: #f8f8f8;
      }

      .shorts-container {
        background-color: #000;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        border-radius: 12px;
        overflow: hidden;
        position: relative;
      }

      .shorts-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background-color: #000;
      }

      .shorts-controls {
        position: absolute;
        right: 8px;
        bottom: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        z-index: 20;
      }

      .shorts-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 16px;
        background: linear-gradient(transparent, rgba(0,0,0,0.8));
        z-index: 10;
      }

      /* Fixed layout styles for proper centering */
      .layout-container {
        display: flex;
        width: 100%;
      }

      .left-sidebar {
        width: 80px;
        min-width: 80px;
        transition: all 300ms ease-in-out;
      }

      .left-sidebar.expanded {
        width: 192px;
        min-width: 192px;
      }

      .right-sidebar {
        width: 80px;
        min-width: 80px;
        transition: all 300ms ease-in-out;
      }

      .right-sidebar.expanded {
        width: 192px;
        min-width: 192px;
      }

      .main-content {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 300ms ease-in-out;
      }

      @media (max-width: 768px) {
        .shorts-container {
          width: 100% !important;
          max-width: none !important;
          height: calc(100vh - 80px) !important;
          max-height: none !important;
          border-radius: 0 !important;
        }

        .shorts-page {
          background-color: #000;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  if (!isClient) {
    return <div className="opacity-0">Loading...</div>;
  }

  return (
    <div className="flex flex-col min-h-screen w-full shorts-page">
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex w-full h-[calc(100vh-80px)] mt-20">
        {/* Left Sidebar */}
        <div
          className={`left-sidebar ${
            leftSidebarExpanded ? "expanded" : ""
          } hidden md:block`}
        >
          <SideNavigation
            expanded={leftSidebarExpanded}
            onExpand={() => setLeftSidebarExpanded(true)}
            onCollapse={() => setLeftSidebarExpanded(false)}
          />
        </div>

        {/* Main Content - Centered */}
        <main className="flex-1 flex items-center justify-center px-4">
          <div
            ref={containerRef}
            className="shorts-container relative"
            style={{
              width: "min(500px, 100%)",
              height: "min(calc(100vh - 100px), 89vh)",
              aspectRatio: "9/16",
              margin: "0 auto",
              border: "2px solid #B31B1E",
              borderRadius: "12px",
              overflow: "hidden",
            }}
          >
            {/* Close button */}
            <button
              onClick={() => router.push("/home/<USER>")}
              className="absolute top-4 left-4 z-50 bg-white rounded-full p-2 text-black shadow-lg hover:bg-gray-200 transition-colors"
            >
              <X size={20} />
            </button>

            {/* Loading state */}
            {loading && flashes.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-white">
                Loading flashes...
              </div>
            )}

            {/* Error state */}
            {error && !loading && flashes.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-red-500">
                {error}
              </div>
            )}

            {/* Flashes content */}
            {flashes.length > 0 && (
              <div
                className="h-full w-full transition-transform duration-300 ease-out"
                style={{
                  transform: `translateY(-${currentIndex * 100}%)`,
                }}
              >
                {flashes.map((flash, index) => (
                  <div
                    key={`${flash.video_id}-${index}`}
                    className="h-full w-full flex items-center justify-center relative bg-black"
                    style={{
                      position: "absolute",
                      top: `${index * 100}%`,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      overflow: "hidden",
                      borderRadius: "8px",
                    }}
                  >
                    {/* Video or Image Content */}
                    <div className="h-full w-full relative overflow-hidden">
                      <div className="absolute inset-0 overflow-hidden">
                        {isYoutubeVideo(flash) ? (
                          // YouTube iframe for YouTube videos
                          <iframe
                            src={`https://www.youtube.com/embed/${getYoutubeId(
                              flash.video_url
                            )}?autoplay=1&controls=0&rel=0&showinfo=0&mute=0`}
                            title={flash.video_name}
                            className="shorts-video"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                          />
                        ) : (
                          // Video player for Cloudfront videos
                          <div className="relative w-full h-full overflow-hidden">
                            <video
                              ref={(el) => {
                                videoRefs.current[flash.video_id] = el;
                              }}
                              src={flash.video_url}
                              className="shorts-video"
                              playsInline
                              loop
                              muted={false}
                              controls={false}
                              poster={getImageSource(flash)}
                              onError={() =>
                                console.error(
                                  `Failed to load video: ${flash.video_name}`
                                )
                              }
                            />
                            {/* Play/Pause Button Overlay */}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                togglePlayPause(flash.video_id);
                              }}
                              className="absolute inset-0 w-full h-full flex items-center justify-center z-10 group"
                            >
                              <div
                                className={`${
                                  isPaused[flash.video_id]
                                    ? "opacity-100"
                                    : "opacity-0 group-hover:opacity-100"
                                } transition-opacity duration-200 bg-black/40 rounded-full p-4 shadow-lg`}
                              >
                                {isPaused[flash.video_id] ? (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="32"
                                    height="32"
                                    viewBox="0 0 24 24"
                                    fill="white"
                                    stroke="white"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                  </svg>
                                ) : (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="32"
                                    height="32"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="white"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <rect
                                      x="6"
                                      y="4"
                                      width="4"
                                      height="16"
                                    ></rect>
                                    <rect
                                      x="14"
                                      y="4"
                                      width="4"
                                      height="16"
                                    ></rect>
                                  </svg>
                                )}
                              </div>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* User info at top with red border - exactly matching the image */}
                    <div className="absolute top-4 left-4 right-4 z-20 flex items-center bg-black/20 backdrop-blur-sm rounded-lg p-2">
                      <div className="flex items-center">
                        <UserAvatar
                          username={flash.user_name || "user"}
                          size="sm"
                          isGradientBorder={true}
                          imageUrl={
                            userAvatarPlaceholders[
                              index % userAvatarPlaceholders.length
                            ]
                          }
                        />
                        <div className="ml-2 text-white">
                          <div className="text-sm font-medium">{flash.user_name || "user"}</div>
                          <div className="text-xs opacity-80">{Math.floor(Math.random() * 2) + 0.1}M Admiring</div>
                        </div>
                      </div>

                      {/* Admire button */}
                      <button
                        className="ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle admire functionality
                          const userId = flash.user_id;
                          if (!userId) return;

                          // Check if already admiring this user
                          const isCurrentlyAdmiring = admiringUsers[userId] || false;

                          // Optimistically update UI state
                          setAdmiringUsers(prev => ({
                            ...prev,
                            [userId]: !isCurrentlyAdmiring
                          }));

                          // Update localStorage
                          try {
                            const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
                            const admiredUsers = JSON.parse(admiredUsersJson);

                            if (!isCurrentlyAdmiring) {
                              admiredUsers[userId] = true;
                            } else {
                              delete admiredUsers[userId];
                            }

                            localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));
                          } catch (error) {
                            console.error('Error updating localStorage:', error);
                          }

                          // Make API call in the background
                          const token = localStorage.getItem('token') ||
                            localStorage.getItem('jwt_token') ||
                            localStorage.getItem('wedzat_token');

                          if (token) {
                            const endpoint = isCurrentlyAdmiring
                              ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow'
                              : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';

                            axios.post(
                              endpoint,
                              { target_user_id: userId },
                              { headers: { Authorization: `Bearer ${token}` } }
                            ).catch(error => {
                              console.error('Error with admire API call:', error);
                              // Revert UI state on error
                              setAdmiringUsers(prev => ({
                                ...prev,
                                [userId]: isCurrentlyAdmiring
                              }));
                            });
                          }
                        }}
                      >
                        {admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire'}
                        {!admiringUsers[flash.user_id || ''] && <span className="ml-1">+</span>}
                      </button>
                    </div>

                    {/* Vertical 3 dots menu button at top right */}
                    <button className="absolute top-20 right-4 z-20 text-white opacity-80 hover:opacity-100">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="1"></circle>
                        <circle cx="12" cy="5" r="1"></circle>
                        <circle cx="12" cy="19" r="1"></circle>
                      </svg>
                    </button>

                    {/* Interaction buttons on the right side - exactly matching the image */}
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col items-center space-y-6 z-10">
                      {/* Like button */}
                      <button
                        className="text-white opacity-80 hover:opacity-100"
                        onClick={() => toggleLike(flash.video_id)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill={likedFlashes.has(flash.video_id) ? "#B31B1E" : "none"} stroke={likedFlashes.has(flash.video_id) ? "#B31B1E" : "white"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                      </button>

                      {/* Comment button */}
                      <button className="text-white opacity-80 hover:opacity-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                        </svg>
                      </button>

                      {/* Share button */}
                      <button className="text-white opacity-80 hover:opacity-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="22" y1="2" x2="11" y2="13"></line>
                          <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                        </svg>
                      </button>
                    </div>

                    {/* Unlock vendor button at bottom */}
                    <div className="absolute bottom-8 left-0 right-0 flex justify-center z-10">
                      <button
                        className="bg-[#B31B1E] text-white text-sm font-medium px-4 py-2 rounded-md flex items-center"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowVendorDetails(prev => ({
                            ...prev,
                            [flash.video_id]: !prev[flash.video_id]
                          }));
                        }}
                      >
                        Unlock Vendor
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>

                    {/* Vendor Details Section */}
                    {showVendorDetails[flash.video_id] && (
                      <div className="absolute bottom-20 left-4 right-4 p-4 bg-white/90 rounded-lg text-black max-h-[40%] overflow-y-auto z-30">
                        <FlashVendorDetails
                          videoId={flash.video_id}
                          isVerified={false}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Loading more indicator */}
            {loading && page > 1 && (
              <div className="absolute bottom-20 left-0 right-0 text-center text-white bg-black/50 py-2 mx-auto w-48 rounded-full backdrop-blur-sm z-20">
                Loading more flashes...
              </div>
            )}

            {/* Progress indicator */}
            <div className="absolute top-14 left-0 right-0 px-4 z-20">
              <div className="flex gap-1 justify-center">
                {flashes.slice(0, Math.min(flashes.length, 10)).map((_, i) => (
                  <div
                    key={i}
                    className={`h-1 rounded-full ${
                      i === currentIndex ? "bg-white w-6" : "bg-white/40 w-3"
                    } transition-all duration-200`}
                  />
                ))}
                {flashes.length > 10 && (
                  <div className="h-1 rounded-full bg-white/40 w-3" />
                )}
              </div>
            </div>
          </div>
        </main>

        {/* Right Sidebar */}
        <div
          className={`right-sidebar ${
            rightSidebarExpanded ? "expanded" : ""
          } hidden md:block`}
        >
          <RightSidebar
            expanded={rightSidebarExpanded}
            onExpand={() => setRightSidebarExpanded(true)}
            onCollapse={() => setRightSidebarExpanded(false)}
          />
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function FlashShortsLoading() {
  return (
    <div className="flex items-center justify-center h-screen w-full bg-black">
      <div className="text-white text-xl">Loading flashes...</div>
    </div>
  );
}

// Main page component with Suspense
export default function FlashShortsPage() {
  return (
    <Suspense fallback={<FlashShortsLoading />}>
      <FlashShortsContent />
    </Suspense>
  );
}
