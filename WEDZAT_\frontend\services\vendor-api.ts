import axios from 'axios';

// Use environment variable or fallback to localhost for development
const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:5000/hub';
const TOKEN_KEY = 'token';

// Vendor management services
export const vendorService = {
  // Register a new vendor
  registerVendor: async (vendorData: any) => {
    try {
      console.log('Attempting vendor registration with data:', {
        ...vendorData,
        password: vendorData.password ? '********' : undefined
      });

      const response = await axios.post(`${BASE_URL}/register-vendor`, vendorData, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      console.log('Vendor registration response received:', {
        success: true,
        hasToken: !!response.data.token
      });

      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('is_vendor', 'true'); // Mark this as a vendor token

        // Also set as cookie for middleware
        document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';
        document.cookie = 'token=' + response.data.token + '; path=/; max-age=86400; SameSite=Lax';
      } else {
        console.warn('No token received in vendor registration response');
      }

      return response.data;
    } catch (error: any) {
      console.error("Vendor registration error:", error);
      throw error.response?.data || { error: "An error occurred during vendor registration" };
    }
  },

  // Get business types
  getBusinessTypes: async () => {
    try {
      const response = await axios.get(`${BASE_URL}/business-types`);
      return response.data.business_types;
    } catch (error: any) {
      console.error("Get business types error:", error);
      throw error.response?.data || { error: "An error occurred while fetching business types" };
    }
  },

  // NOTE: Business subtypes are now handled directly in the frontend
  // to avoid API calls that might fail

  // Get vendor profile with retry mechanism
  getVendorProfile: async (retryCount = 0, maxRetries = 2) => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      if (!token) {
        throw { error: 'No authentication token found' };
      }

      console.log(`Fetching vendor profile with token: ${token.substring(0, 10)}... (Attempt ${retryCount + 1}/${maxRetries + 1})`);

      const response = await axios.get(`${BASE_URL}/vendor-profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Vendor profile response:', response.status);
      return response.data.profile;
    } catch (error: any) {
      console.error(`Get vendor profile error (Attempt ${retryCount + 1}/${maxRetries + 1}):`, error);

      // Create a more detailed error object
      const errorObj = {
        error: error.response?.data?.error || "An error occurred while fetching vendor profile",
        message: error.response?.data?.message || "",
        details: error.response?.data?.details || error.message,
        status: error.response?.status || 500
      };

      console.error("Detailed error:", errorObj);
      console.error("Full error response:", error.response?.data);

      // If we have retries left and it's a 500 error (server error), retry
      if (retryCount < maxRetries && (errorObj.status === 500 || errorObj.status === 0)) {
        console.log(`Retrying... (${retryCount + 1}/${maxRetries})`);
        // Wait for 1 second before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
        return vendorService.getVendorProfile(retryCount + 1, maxRetries);
      }

      throw errorObj;
    }
  },

  // Add a new vendor service
  addVendorService: async (serviceData: any) => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      if (!token) {
        throw { error: 'No authentication token found' };
      }

      const response = await axios.post(`${BASE_URL}/add-vendor-service`, serviceData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error: any) {
      console.error("Add vendor service error:", error);
      throw error.response?.data || { error: "An error occurred while adding the service" };
    }
  },

  // Get all vendor services
  getVendorServices: async () => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      if (!token) {
        throw { error: 'No authentication token found' };
      }

      const response = await axios.get(`${BASE_URL}/vendor-services`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.services;
    } catch (error: any) {
      console.error("Get vendor services error:", error);
      throw error.response?.data || { error: "An error occurred while fetching services" };
    }
  },

  // Update a vendor service
  updateVendorService: async (serviceData: any) => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      if (!token) {
        throw { error: 'No authentication token found' };
      }

      const response = await axios.put(`${BASE_URL}/update-vendor-service`, serviceData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error: any) {
      console.error("Update vendor service error:", error);
      throw error.response?.data || { error: "An error occurred while updating the service" };
    }
  },

  // Delete a vendor service
  deleteVendorService: async (serviceId: string) => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      if (!token) {
        throw { error: 'No authentication token found' };
      }

      const response = await axios.delete(`${BASE_URL}/delete-vendor-service?service_id=${serviceId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error: any) {
      console.error("Delete vendor service error:", error);
      throw error.response?.data || { error: "An error occurred while deleting the service" };
    }
  },

  // Check if the vendor is authenticated
  isAuthenticated: () => {
    return !!localStorage.getItem(TOKEN_KEY);
  },

  // Logout - clear token from localStorage and cookies
  logout: () => {
    console.log('Logging out and removing tokens');
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem('is_vendor'); // Also remove the vendor flag

    // Clear cookies
    document.cookie = 'token=; path=/; max-age=0; SameSite=Lax';
    document.cookie = 'is_vendor=; path=/; max-age=0; SameSite=Lax';
  }
};

export default vendorService;
