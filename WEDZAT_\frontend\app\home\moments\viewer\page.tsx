"use client";
import React, { useState, useEffect, useRef, Suspense } from "react";
import { useRouter } from "next/navigation";
import axios from "../../../../services/axiosConfig";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import {
  TopNavigation,
  SideNavigation,
} from "../../../../components/HomeDashboard/Navigation";

// Define interface for story items
interface Story {
  content_id: string;
  content_name: string;
  content_url: string;
  content_description?: string;
  thumbnail_url?: string;
  duration?: number;
  created_at: string;
  user_name: string;
  content_type: "video" | "photo";
  is_own_content: boolean;
  viewed?: boolean;
}

interface ApiResponse {
  stories: Story[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

// Create a Client component
function MomentsContent() {
  const router = useRouter();

  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [videoPlayStates, setVideoPlayStates] = useState<Record<string, boolean>>({});

  // Initialize from localStorage when component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Get stories from localStorage
      const storedStories = localStorage.getItem('moments_stories');
      const storedIndex = localStorage.getItem('moments_current_index');

      if (storedStories) {
        try {
          const parsedStories = JSON.parse(storedStories) as Story[];
          setStories(parsedStories);
          setLoading(false);

          // Set current index if available
          if (storedIndex) {
            const index = parseInt(storedIndex, 10);
            if (!isNaN(index) && index >= 0 && index < parsedStories.length) {
              setCurrentIndex(index);
            }
          }
        } catch (e) {
          console.error('Error parsing stored stories:', e);
          // Will fall back to API fetch
        }
      }
    }
  }, []);

  const containerRef = useRef<HTMLDivElement>(null);
  const videoRefs = useRef<Record<string, HTMLVideoElement>>({});

  // Set isClient to true on mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fetch stories from the API if not available in localStorage
  useEffect(() => {
    // Skip API fetch if we already have stories from localStorage
    if (stories.length > 0) {
      return;
    }

    const fetchStories = async () => {
      try {
        setLoading(true);
        // Get token from localStorage
        const token = localStorage.getItem("token");

        if (!token) {
          console.warn("No authentication token found");
          setError("Authentication required");
          return;
        }

        const response = await axios.get<ApiResponse>(
          `/stories?page=${page}&limit=10`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.data && response.data.stories) {
          console.log("Stories API response:", response.data);

          // Process the stories to ensure thumbnails are properly formatted
          const processedStories = response.data.stories.map((story) => {
            // For photos, ensure we're using content_url directly
            // For videos, use thumbnail_url if available, otherwise generate one
            const thumbnailUrl =
              story.content_type === "photo"
                ? story.content_url
                : story.thumbnail_url || getDefaultThumbnail(story);

            return {
              ...story,
              viewed: true, // Mark as viewed since we're viewing it
              thumbnail_url: thumbnailUrl,
              // Ensure content_url is also set correctly
              content_url: story.content_url || thumbnailUrl,
            };
          });

          if (page === 1) {
            setStories(processedStories);

            // Also save to localStorage for future use
            localStorage.setItem('moments_stories', JSON.stringify(processedStories));
          } else {
            setStories((prev) => {
              const newStories = [...prev, ...processedStories];
              // Update localStorage
              localStorage.setItem('moments_stories', JSON.stringify(newStories));
              return newStories;
            });
          }

          setHasMore(response.data.next_page);
        } else {
          console.warn("Unexpected API response format:", response.data);
          setError("Failed to load moments");
        }
      } catch (err) {
        console.error("Error fetching stories:", err);
        setError("Failed to load moments");
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, [page, stories.length]);

  // Load more stories when reaching the end
  useEffect(() => {
    if (currentIndex >= stories.length - 2 && hasMore && !loading) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [currentIndex, stories.length, hasMore, loading]);

  // Auto-play videos when they become the current item
  useEffect(() => {
    if (!isClient || stories.length === 0) return;

    const currentStory = stories[currentIndex];
    if (currentStory?.content_type === "video") {
      // Get the current video element
      const videoElement = videoRefs.current[currentStory.content_id];
      if (videoElement) {
        // Play the video
        videoElement.currentTime = 0; // Reset to beginning
        const playPromise = videoElement.play();

        // Handle play promise (might be rejected if user hasn't interacted with the document)
        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              // Video started playing successfully
              setVideoPlayStates((prev) => ({
                ...prev,
                [currentStory.content_id]: true,
              }));
            })
            .catch((error) => {
              console.warn("Auto-play was prevented:", error);
              // Mark this video as not playing
              setVideoPlayStates((prev) => ({
                ...prev,
                [currentStory.content_id]: false,
              }));
            });
        }
      }
    }

    // Pause all other videos
    Object.entries(videoRefs.current).forEach(([id, video]) => {
      if (id !== currentStory?.content_id && !video.paused) {
        video.pause();
      }
    });
  }, [currentIndex, stories, isClient]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!isClient) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft" || e.key === "ArrowUp") {
        navigateToPrevious();
      } else if (e.key === "ArrowRight" || e.key === "ArrowDown") {
        navigateToNext();
      } else if (e.key === "Escape") {
        router.push("/home/<USER>");
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isClient, currentIndex, stories.length, router]);

  // Handle touch events for mobile swipe
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartY = 0;
    let touchEndY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      touchStartX = e.changedTouches[0].screenX;
      touchStartY = e.changedTouches[0].screenY;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      touchEndX = e.changedTouches[0].screenX;
      touchEndY = e.changedTouches[0].screenY;

      // Calculate horizontal and vertical distance
      const horizontalDistance = touchEndX - touchStartX;
      const verticalDistance = touchEndY - touchStartY;

      // Only handle horizontal swipes if they're more significant than vertical movement
      if (Math.abs(horizontalDistance) > Math.abs(verticalDistance)) {
        if (horizontalDistance > 50) {
          // Swipe right - go to previous
          navigateToPrevious();
        } else if (horizontalDistance < -50) {
          // Swipe left - go to next
          navigateToNext();
        }
      }
    };

    const container = containerRef.current;
    container.addEventListener("touchstart", handleTouchStart, {
      passive: true,
    });
    container.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isClient, currentIndex, stories.length]);

  // Handle wheel events for touchpad scrolling
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    const handleWheel = (e: WheelEvent) => {
      // Debounce the wheel event to prevent too many navigations
      if (e.deltaX > 50) {
        // Scroll right - go to next
        navigateToNext();
      } else if (e.deltaX < -50) {
        // Scroll left - go to previous
        navigateToPrevious();
      }
    };

    const container = containerRef.current;
    container.addEventListener("wheel", handleWheel, { passive: true });

    return () => {
      container.removeEventListener("wheel", handleWheel);
    };
  }, [isClient, currentIndex, stories.length]);

  const navigateToNext = () => {
    if (currentIndex < stories.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      // Save current index to localStorage
      localStorage.setItem('moments_current_index', newIndex.toString());
    }
  };

  const navigateToPrevious = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      // Save current index to localStorage
      localStorage.setItem('moments_current_index', newIndex.toString());
    }
  };

  // Process image URL to handle cloudfront URLs properly
  const processImageUrl = (url: string): string => {
    if (!url) return "/pics/placeholder.svg";

    // If it's already a local URL, return as is
    if (url.startsWith("/")) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes("cloudfront.net") || url.includes("amazonaws.com")) {
      return url;
    }

    // Return the URL as is for other external URLs
    return url;
  };

  // Get thumbnail URL for sidebar
  const getThumbnailUrl = (story: Story): string => {
    if (story.thumbnail_url) return story.thumbnail_url;
    if (story.content_type === "photo") return story.content_url;
    return getDefaultThumbnail(story);
  };

  // Get default thumbnail based on content type
  const getDefaultThumbnail = (story: Story): string => {
    if (story.content_type === "video") {
      // Try to extract YouTube thumbnail if it's a YouTube video
      if (story.content_url && story.content_url.includes("youtube")) {
        const videoId = getYoutubeId(story.content_url);
        if (videoId) {
          return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        }
      }
      return `/pics/video-placeholder.jpg`;
    } else {
      // For photos, use the content_url directly if available
      return story.content_url || `/pics/placeholder.svg`;
    }
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return "";

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes("/")) return url;

    // Try to extract ID from YouTube URL
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : "";
  };

  // Format date to a readable string
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get current story
  const currentStory = stories[currentIndex];

  // Determine if we should show navigation buttons
  const showPrevButton = currentIndex > 0;
  const showNextButton = currentIndex < stories.length - 1;

  if (!isClient) {
    return null; // Don't render anything on the server
  }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Top Navigation */}
      <TopNavigation />

      <div className="flex flex-1">
        {/* Left Sidebar */}
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Story Info Bar */}
        <div className="fixed top-20 left-0 right-0 z-30 bg-white shadow-sm p-2 border-b border-gray-200">
          <div className="container mx-auto w-full md:w-[65%] flex justify-between items-center px-4">
            <button
              onClick={() => router.push("/home")}
              className="text-gray-800 hover:text-red-600 transition-colors"
              title="Back to Home"
            >
              <X size={20} />
            </button>

            {currentStory && (
              <div className="text-gray-800 text-sm font-medium">
                {currentIndex + 1} / {stories.length}
              </div>
            )}

            <div className="text-gray-800 text-sm font-medium truncate max-w-[200px]">
              {currentStory?.content_name}
            </div>
          </div>
        </div>

        {/* Main Content - Centered */}
        <main
          className={`flex-1 flex items-center justify-center pt-32 pb-4 px-4 ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            transition: "all 300ms ease-in-out",
            height: "calc(100vh - 80px)", // Adjust height to account for top navigation
            overflowY: "auto",
          }}
        >
          <div
            ref={containerRef}
            className="moments-container relative container mx-auto md:w-[65%] w-full"
            style={{
              maxWidth: "1000px",
              height: "calc(100vh - 160px)", // Adjusted to account for top nav and story info bar
              maxHeight: "80vh", // Ensure it doesn't get too tall on large screens
              overflow: "hidden",
              marginTop: "10px", // Add some space at the top
              marginBottom: "20px", // Add some space at the bottom
            }}
          >
            {/* Loading state */}
            {loading && stories.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-gray-800 bg-white">
                <div className="animate-spin rounded-full h-10 w-10 border-2 border-red-600 border-t-transparent mr-3"></div>
                <span>Loading moments...</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && stories.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-red-600 bg-white p-4 text-center">
                <div>
                  <div className="mb-2 font-medium">Error</div>
                  <div>{error}</div>
                </div>
              </div>
            )}

            {/* Stories content */}
            {stories.length > 0 && (
              <div
                className="h-full w-full transition-transform duration-300 ease-out"
                style={{
                  transform: `translateX(-${currentIndex * 100}%)`,
                }}
              >
                {stories.map((story, index) => (
                  <div
                    key={`${story.content_id}-${index}`}
                    className="h-full w-full flex items-center justify-center relative bg-white"
                    style={{
                      position: "absolute",
                      top: 0,
                      left: `${index * 100}%`,
                      right: 0,
                      bottom: 0,
                      overflow: "hidden",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    {/* Content */}
                    {story.content_type === "video" ? (
                      // Video content
                      <div className="relative w-full h-full flex items-center justify-center">
                        <video
                          ref={(el) => {
                            if (el) videoRefs.current[story.content_id] = el;
                          }}
                          src={story.content_url}
                          className="max-h-[calc(100%-80px)] max-w-full object-contain"
                          autoPlay={index === currentIndex}
                          muted={false}
                          loop={false}
                          playsInline
                          onClick={(e) => {
                            // Toggle play/pause on click
                            const video = e.target as HTMLVideoElement;
                            if (video.paused) {
                              const playPromise = video.play();
                              if (playPromise !== undefined) {
                                playPromise
                                  .then(() => {
                                    setVideoPlayStates((prev) => ({
                                      ...prev,
                                      [story.content_id]: true,
                                    }));
                                  })
                                  .catch((error) => {
                                    console.warn("Play was prevented:", error);
                                  });
                              }
                            } else {
                              video.pause();
                              setVideoPlayStates((prev) => ({
                                ...prev,
                                [story.content_id]: false,
                              }));
                            }
                          }}
                          onError={() => {
                            console.error(
                              `Failed to load video: ${story.content_url}`
                            );
                          }}
                        />

                        {/* Play button overlay - only show if video is current and not playing */}
                        {index === currentIndex &&
                          story.content_type === "video" &&
                          videoPlayStates[story.content_id] === false && (
                            <div
                              className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 cursor-pointer z-20"
                              onClick={() => {
                                const video =
                                  videoRefs.current[story.content_id];
                                if (video) {
                                  const playPromise = video.play();
                                  if (playPromise !== undefined) {
                                    playPromise
                                      .then(() => {
                                        setVideoPlayStates((prev) => ({
                                          ...prev,
                                          [story.content_id]: true,
                                        }));
                                      })
                                      .catch(() => {});
                                  }
                                }
                              }}
                            >
                              <div className="w-16 h-16 rounded-full bg-white bg-opacity-80 flex items-center justify-center">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="24"
                                  height="24"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                </svg>
                              </div>
                            </div>
                          )}
                      </div>
                    ) : (
                      // Photo content
                      <div className="relative h-[calc(100%-80px)] w-full flex items-center justify-center">
                        <Image
                          src={story.content_url}
                          alt={story.content_name || "Moment"}
                          fill
                          className="object-contain"
                          unoptimized={true}
                          onError={(e) => {
                            const imgElement = e.target as HTMLImageElement;
                            imgElement.src = "/pics/placeholder.svg";
                          }}
                        />
                      </div>
                    )}

                    {/* Story info overlay */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-gray-800 to-transparent p-4 z-10">
                      <div className="text-white">
                        <h3 className="text-lg font-bold truncate">
                          {story.content_name}
                        </h3>
                        <p className="text-sm font-medium truncate">
                          {story.user_name}
                        </p>
                        {story.content_description && (
                          <p className="text-sm mt-1 line-clamp-2">
                            {story.content_description}
                          </p>
                        )}
                        <p className="text-xs mt-1 text-gray-200">
                          {formatDate(story.created_at)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Navigation buttons - more subtle now that we have sidebar */}
            {showPrevButton && (
              <button
                onClick={navigateToPrevious}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-80 rounded-full p-2 text-gray-600 transition-colors z-20"
                aria-label="Previous story"
                style={{ marginTop: "-40px" }} /* Adjust for the info overlay */
              >
                <ChevronLeft size={20} />
              </button>
            )}

            {showNextButton && (
              <button
                onClick={navigateToNext}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-80 rounded-full p-2 text-gray-600 transition-colors z-20"
                aria-label="Next story"
                style={{ marginTop: "-40px" }} /* Adjust for the info overlay */
              >
                <ChevronRight size={20} />
              </button>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

// Loading component for suspense fallback
function MomentsViewerLoading() {
  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center">
      <div className="animate-spin rounded-full h-10 w-10 border-2 border-red-600 border-t-transparent mr-3"></div>
      <span className="text-gray-800 mt-4">Loading moments...</span>
    </div>
  );
}

// Wrap with a Page component
export default function MomentsViewerPage() {
  return (
    <Suspense fallback={<MomentsViewerLoading />}>
      <MomentsContent />
    </Suspense>
  );
}
