(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_db6d0954._.js", {

"[project]/app/wedding/[id]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
const PublicWeddingWebsite = ({ params })=>{
    _s();
    const [website, setWebsite] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Use useEffect to fetch the website when the component mounts
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PublicWeddingWebsite.useEffect": ()=>{
            // Get the ID from params inside the effect to avoid the Next.js warning
            const id = params.id;
            if (id) {
                fetchWebsite(id);
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["PublicWeddingWebsite.useEffect"], []);
    // Load Google Fonts for the website
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PublicWeddingWebsite.useEffect": ()=>{
            if (!website || !website.design_settings?.fonts) return;
            // Get the fonts used in the website
            const fonts = website.design_settings.fonts;
            const fontNames = Object.values(fonts).filter({
                "PublicWeddingWebsite.useEffect.fontNames": (font)=>typeof font === 'string' && font !== ''
            }["PublicWeddingWebsite.useEffect.fontNames"]) // Remove any undefined values
            .map({
                "PublicWeddingWebsite.useEffect.fontNames": (font)=>font.replace(/ /g, '+')
            }["PublicWeddingWebsite.useEffect.fontNames"]); // Format for Google Fonts URL
            // If no fonts, return early
            if (fontNames.length === 0) return;
            // Create a link element for Google Fonts
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = `https://fonts.googleapis.com/css2?family=${[
                ...new Set(fontNames)
            ].join('&family=')}&display=swap`;
            // Add the link to the document head
            document.head.appendChild(link);
            // Clean up when component unmounts or fonts change
            return ({
                "PublicWeddingWebsite.useEffect": ()=>{
                    document.head.removeChild(link);
                }
            })["PublicWeddingWebsite.useEffect"];
        }
    }["PublicWeddingWebsite.useEffect"], [
        website
    ]);
    // Debug log for template image
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PublicWeddingWebsite.useEffect": ()=>{
            if (website) {
                console.log('Website data for debugging:', {
                    customImage: website.design_settings?.customImage,
                    templateThumbnail: website.template_thumbnail,
                    designSettings: website.design_settings
                });
            }
        }
    }["PublicWeddingWebsite.useEffect"], [
        website
    ]);
    const fetchWebsite = async (id)=>{
        try {
            setLoading(true);
            setError(null); // Clear any previous errors
            // For demonstration purposes, create a mock website if we can't fetch it
            // This ensures the website is always available for viewing
            try {
                // Try to get the website directly without checking if it's published
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website?website_id=${id}`, {
                    // Try without authentication first
                    validateStatus: (status)=>status < 500 // Don't throw for 4xx errors
                });
                if (response.data && response.data.website) {
                    console.log('Website data from API:', response.data.website);
                    // Make sure we have all the necessary fields
                    const websiteData = {
                        ...response.data.website,
                        // Ensure these fields exist with fallbacks
                        title: response.data.website.title || 'Our Wedding',
                        wedding_date: response.data.website.wedding_date || new Date().toISOString().split('T')[0],
                        wedding_location: response.data.website.wedding_location || 'Beautiful Venue, City',
                        about_couple: response.data.website.about_couple || 'We are excited to celebrate our special day with you!',
                        couple_names: response.data.website.couple_names || response.data.website.design_settings && response.data.website.design_settings.couple_names || 'Jane & John',
                        design_settings: response.data.website.design_settings || {
                            colors: {
                                primary: '#B31B1E',
                                secondary: '#333333',
                                background: '#FFFFFF',
                                text: '#000000'
                            },
                            fonts: {
                                heading: 'Playfair Display',
                                body: 'Roboto'
                            },
                            customImage: `https://placehold.co/1200x800/B31B1E/FFFFFF?text=${encodeURIComponent(response.data.website.title || 'Our Wedding')}`
                        }
                    };
                    setWebsite(websiteData);
                    setLoading(false);
                    return;
                }
                // If that fails, try the public endpoint
                const publicResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/public-website?website_id=${id}`);
                if (publicResponse.data && publicResponse.data.website) {
                    console.log('Public website data from API:', publicResponse.data.website);
                    // Make sure we have all the necessary fields
                    const websiteData = {
                        ...publicResponse.data.website,
                        // Ensure these fields exist with fallbacks
                        title: publicResponse.data.website.title || 'Our Wedding',
                        wedding_date: publicResponse.data.website.wedding_date || new Date().toISOString().split('T')[0],
                        wedding_location: publicResponse.data.website.wedding_location || 'Beautiful Venue, City',
                        about_couple: publicResponse.data.website.about_couple || 'We are excited to celebrate our special day with you!',
                        couple_names: publicResponse.data.website.couple_names || publicResponse.data.website.design_settings && publicResponse.data.website.design_settings.couple_names || 'Jane & John',
                        design_settings: publicResponse.data.website.design_settings || {
                            colors: {
                                primary: '#B31B1E',
                                secondary: '#333333',
                                background: '#FFFFFF',
                                text: '#000000'
                            },
                            fonts: {
                                heading: 'Playfair Display',
                                body: 'Roboto'
                            },
                            customImage: `https://placehold.co/1200x800/B31B1E/FFFFFF?text=${encodeURIComponent(publicResponse.data.website.title || 'Our Wedding')}`
                        }
                    };
                    setWebsite(websiteData);
                    setLoading(false);
                    return;
                }
            } catch (apiError) {
                console.error('Error fetching from API:', apiError);
            // Continue to fallback
            }
            // Fallback: Create a mock website for demonstration
            console.log('Creating mock website for demonstration');
            const mockWebsite = {
                website_id: id,
                title: 'Our Wedding',
                wedding_date: new Date().toISOString().split('T')[0],
                wedding_location: 'Beautiful Venue, City',
                about_couple: 'We are excited to celebrate our special day with you! This is a demonstration of the wedding website feature.',
                couple_names: 'The Couple',
                design_settings: {
                    colors: {
                        primary: '#B31B1E',
                        secondary: '#333333',
                        background: '#FFFFFF',
                        text: '#000000'
                    },
                    fonts: {
                        heading: 'Playfair Display',
                        body: 'Roboto',
                        coupleNames: 'Playfair Display',
                        date: 'Roboto',
                        location: 'Roboto',
                        aboutCouple: 'Roboto'
                    },
                    customImage: '/placeholder-template.jpg'
                },
                template_thumbnail: '/placeholder-template.jpg'
            };
            setWebsite(mockWebsite);
            setLoading(false);
        } catch (error) {
            console.error('Error in fetchWebsite:', error);
            setError('Failed to load website. Please try again later.');
            setLoading(false);
        }
    };
    const formatDate = (dateString)=>{
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex flex-col items-center justify-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                    size: 48,
                    className: "animate-spin text-[#B31B1E] mb-4"
                }, void 0, false, {
                    fileName: "[project]/app/wedding/[id]/page.tsx",
                    lineNumber: 202,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600",
                    children: "Loading wedding website..."
                }, void 0, false, {
                    fileName: "[project]/app/wedding/[id]/page.tsx",
                    lineNumber: 203,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/wedding/[id]/page.tsx",
            lineNumber: 201,
            columnNumber: 7
        }, this);
    }
    if (error || !website) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex flex-col items-center justify-center p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-red-50 border border-red-200 rounded-lg p-6 max-w-md w-full text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold text-red-700 mb-2",
                        children: "Website Not Available"
                    }, void 0, false, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 212,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-700 mb-4",
                        children: error || 'This wedding website is not available or has been removed.'
                    }, void 0, false, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 213,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                        href: "/",
                        className: "inline-block px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors",
                        children: "Return to Home"
                    }, void 0, false, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 214,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/wedding/[id]/page.tsx",
                lineNumber: 211,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/wedding/[id]/page.tsx",
            lineNumber: 210,
            columnNumber: 7
        }, this);
    }
    // Extract design settings
    const colors = website.design_settings?.colors || {
        primary: "#B31B1E",
        secondary: "#333333",
        background: "#FFFFFF",
        text: "#000000"
    };
    const fonts = website.design_settings?.fonts || {
        heading: "Playfair Display",
        body: "Roboto",
        coupleNames: "Playfair Display",
        date: "Roboto",
        location: "Roboto",
        aboutCouple: "Roboto"
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen",
        style: {
            backgroundColor: colors.background,
            color: colors.text,
            fontFamily: fonts.body
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "relative h-screen flex items-center justify-center overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 bg-cover bg-center",
                        style: {
                            backgroundImage: `url(${website.design_settings?.customImage || ''})`,
                            opacity: 0.3
                        }
                    }, void 0, false, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 251,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                        src: website.design_settings?.customImage || '',
                        alt: "Wedding background",
                        className: "absolute inset-0 w-full h-full object-cover opacity-0",
                        onError: (e)=>{
                            // If image fails to load, replace with fallback
                            const target = e.target;
                            target.onerror = null; // Prevent infinite loop
                            // No fallback image needed
                            // Make the image visible since the background image failed
                            target.style.opacity = '0.3';
                        }
                    }, void 0, false, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 259,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 bg-black opacity-30"
                    }, void 0, false, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 274,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative z-10 text-center p-8 max-w-4xl",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-3xl md:text-4xl mb-2",
                                style: {
                                    color: colors.primary,
                                    fontFamily: fonts.coupleNames || fonts.heading
                                },
                                children: website.couple_names
                            }, void 0, false, {
                                fileName: "[project]/app/wedding/[id]/page.tsx",
                                lineNumber: 279,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-5xl md:text-7xl mb-4",
                                style: {
                                    color: colors.primary,
                                    fontFamily: fonts.heading
                                },
                                children: website.title
                            }, void 0, false, {
                                fileName: "[project]/app/wedding/[id]/page.tsx",
                                lineNumber: 289,
                                columnNumber: 11
                            }, this),
                            website.wedding_date && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-2xl md:text-3xl mb-4 text-white",
                                style: {
                                    fontFamily: fonts.date || fonts.body
                                },
                                children: formatDate(website.wedding_date)
                            }, void 0, false, {
                                fileName: "[project]/app/wedding/[id]/page.tsx",
                                lineNumber: 300,
                                columnNumber: 13
                            }, this),
                            website.wedding_location && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xl md:text-2xl mb-8 text-white",
                                style: {
                                    fontFamily: fonts.location || fonts.body
                                },
                                children: website.wedding_location
                            }, void 0, false, {
                                fileName: "[project]/app/wedding/[id]/page.tsx",
                                lineNumber: 311,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "inline-block px-6 py-3 rounded-md text-white text-lg",
                                style: {
                                    backgroundColor: colors.secondary
                                },
                                children: "We're getting married!"
                            }, void 0, false, {
                                fileName: "[project]/app/wedding/[id]/page.tsx",
                                lineNumber: 321,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 277,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/wedding/[id]/page.tsx",
                lineNumber: 249,
                columnNumber: 7
            }, this),
            website.about_couple && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "py-16 px-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-4xl mx-auto",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-3xl md:text-4xl mb-8 text-center",
                            style: {
                                color: colors.primary,
                                fontFamily: fonts.heading
                            },
                            children: "Our Story"
                        }, void 0, false, {
                            fileName: "[project]/app/wedding/[id]/page.tsx",
                            lineNumber: 334,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "prose prose-lg mx-auto",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                style: {
                                    color: colors.text,
                                    fontFamily: fonts.aboutCouple || fonts.body
                                },
                                children: website.about_couple
                            }, void 0, false, {
                                fileName: "[project]/app/wedding/[id]/page.tsx",
                                lineNumber: 345,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/wedding/[id]/page.tsx",
                            lineNumber: 344,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/wedding/[id]/page.tsx",
                    lineNumber: 333,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/wedding/[id]/page.tsx",
                lineNumber: 332,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                className: "py-8 text-center",
                style: {
                    backgroundColor: colors.secondary,
                    color: '#FFFFFF'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "© ",
                            new Date().getFullYear(),
                            " - ",
                            website.title
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 364,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm mt-2",
                        children: "Created with Wedzat"
                    }, void 0, false, {
                        fileName: "[project]/app/wedding/[id]/page.tsx",
                        lineNumber: 365,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/wedding/[id]/page.tsx",
                lineNumber: 357,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/wedding/[id]/page.tsx",
        lineNumber: 240,
        columnNumber: 5
    }, this);
};
_s(PublicWeddingWebsite, "JZcENU9Elcedr4k8vd/z0UoGmqI=");
_c = PublicWeddingWebsite;
const __TURBOPACK__default__export__ = PublicWeddingWebsite;
var _c;
__turbopack_context__.k.register(_c, "PublicWeddingWebsite");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.479.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>LoaderCircle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M21 12a9 9 0 1 1-6.219-8.56",
            key: "13zald"
        }
    ]
];
const LoaderCircle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("LoaderCircle", __iconNode);
;
 //# sourceMappingURL=loader-circle.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Loader2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=_db6d0954._.js.map