"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./components/VideoInteractionBar.tsx":
/*!********************************************!*\
  !*** ./components/VideoInteractionBar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MdFavorite,MdFavoriteBorder!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst VideoInteractionBar = (param)=>{\n    let { username, uploadDate, viewCount, description, userId, videoId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isCreatingChat, setIsCreatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiring, setIsAdmiring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiringLoading, setIsAdmiringLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiking, setIsLiking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Format view count\n    const formatViewCount = (count)=>{\n        if (count >= 1000000) {\n            return \"\".concat((count / 1000000).toFixed(1), \"M\");\n        } else if (count >= 1000) {\n            return \"\".concat((count / 1000).toFixed(1), \"K\");\n        }\n        return count.toString();\n    };\n    // Handle message button click\n    const handleMessageClick = async ()=>{\n        if (isCreatingChat) return;\n        try {\n            setIsCreatingChat(true);\n            // Get token from localStorage - use userToken as in the chat page\n            const token = localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n            if (!token) {\n                console.error(\"No authentication token found\");\n                alert(\"Please log in to send messages\");\n                setIsCreatingChat(false);\n                return;\n            }\n            // Use the hardcoded URL from the messages page\n            const apiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\";\n            // If userId is 'default' or missing, use a fallback ID for testing\n            // In a real app, you would handle this differently\n            const participantId = !userId || userId === \"default\" ? username.toLowerCase().replace(/\\s+/g, \"_\") + \"_id\" : userId;\n            console.log(\"Creating conversation with user ID: \".concat(participantId));\n            console.log(\"Using API URL: \".concat(apiUrl, \"/conversations\"));\n            console.log(\"Authorization token: \".concat(token.substring(0, 10), \"...\"));\n            try {\n                // Create or get existing conversation\n                console.log(\"Request payload:\", JSON.stringify({\n                    participants: [\n                        participantId\n                    ]\n                }));\n                const response = await fetch(\"\".concat(apiUrl, \"/conversations\"), {\n                    method: \"POST\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        participants: [\n                            participantId\n                        ]\n                    }),\n                    // Add these options to help with CORS issues\n                    mode: \"cors\",\n                    credentials: \"same-origin\"\n                });\n                console.log(\"Response status:\", response.status);\n                console.log(\"Response headers:\", [\n                    ...response.headers.entries()\n                ]);\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"Conversation created/retrieved:\", data);\n                // Handle different response formats\n                let conversationId = null;\n                // Check if the response has a direct conversation_id property\n                if (data.conversation_id) {\n                    conversationId = data.conversation_id;\n                } else if (data.body && typeof data.body === \"string\") {\n                    try {\n                        const bodyData = JSON.parse(data.body);\n                        if (bodyData.conversation_id) {\n                            conversationId = bodyData.conversation_id;\n                            console.log(\"Found conversation ID in body:\", conversationId);\n                        }\n                    } catch (e) {\n                        console.error(\"Error parsing body data:\", e);\n                    }\n                }\n                if (!conversationId) {\n                    console.error(\"No conversation ID found in any format\", data);\n                    throw new Error(\"Invalid response from server\");\n                }\n                // Navigate to the chat page\n                router.push(\"/messages/\".concat(conversationId));\n            } catch (innerError) {\n                console.error(\"Inner fetch error:\", innerError);\n                throw innerError;\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            // Provide more specific error messages based on the error\n            if (error instanceof TypeError && error.message.includes(\"Failed to fetch\")) {\n                alert(\"Network error: Please check your internet connection and try again.\");\n            } else if (error instanceof Error && error.message.includes(\"401\")) {\n                alert(\"Authentication error: Please log in again.\");\n            } else if (error instanceof Error && error.message.includes(\"403\")) {\n                alert(\"Permission denied: You don't have permission to message this user.\");\n            } else {\n                alert(\"Failed to start conversation. Please try again.\");\n            }\n        } finally{\n            setIsCreatingChat(false);\n        }\n    };\n    // Function to navigate to user profile\n    const navigateToUserProfile = ()=>{\n        if (userId) {\n            // Get the token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Navigate to the profile page with the userId\n            router.push(\"/profile/\".concat(userId));\n        }\n    };\n    // Function to handle like/unlike\n    const handleLikeToggle = async ()=>{\n        if (isLiking || !videoId) return;\n        try {\n            setIsLiking(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to like this video');\n                setIsLiking(false);\n                return;\n            }\n            // Optimistically update UI\n            const newLikedState = !isLiked;\n            setIsLiked(newLikedState);\n            // Make API call to Next.js API routes\n            const endpoint = isLiked ? '/api/unlike' : '/api/like';\n            console.log(\"Making API call to: \".concat(endpoint, \" for video: \").concat(videoId));\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: videoId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const responseData = await response.json();\n            console.log('API Success Response:', responseData);\n            // Update localStorage to persist like status\n            try {\n                // Determine if this is a glimpse or movie based on current page or content type\n                const currentPath = window.location.pathname;\n                const isGlimpse = currentPath.includes('/glimpses');\n                const storageKey = isGlimpse ? 'likedGlimpses' : 'likedMovies';\n                const likedData = localStorage.getItem(storageKey);\n                const likedArray = likedData ? JSON.parse(likedData) : [];\n                if (isLiked) {\n                    // Remove from liked items\n                    const updatedLiked = likedArray.filter((id)=>id !== videoId);\n                    localStorage.setItem(storageKey, JSON.stringify(updatedLiked));\n                } else {\n                    // Add to liked items\n                    if (!likedArray.includes(videoId)) {\n                        likedArray.push(videoId);\n                        localStorage.setItem(storageKey, JSON.stringify(likedArray));\n                    }\n                }\n                console.log(\"Updated \".concat(storageKey, \" in localStorage\"));\n            } catch (storageError) {\n                console.error('Error updating localStorage:', storageError);\n            }\n            console.log(\"\".concat(isLiked ? 'Unliked' : 'Liked', \" video: \").concat(videoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setIsLiked(!isLiked);\n        } finally{\n            setIsLiking(false);\n        }\n    };\n    // Function to handle admire/unadmire\n    const handleAdmireToggle = async ()=>{\n        if (isAdmiringLoading || !userId) return;\n        try {\n            setIsAdmiringLoading(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to admire this user');\n                setIsAdmiringLoading(false);\n                return;\n            }\n            // Optimistically update UI state immediately for better user experience\n            const newAdmiringState = !isAdmiring;\n            setIsAdmiring(newAdmiringState);\n            // Make API call to follow/unfollow user\n            const endpoint = isAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n            console.log(\"Making request to \".concat(endpoint, \" with user ID: \").concat(userId));\n            try {\n                // Make the API call\n                const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(endpoint, {\n                    target_user_id: userId\n                }, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('API response:', response.data);\n                console.log(\"Successfully \".concat(isAdmiring ? 'unadmired' : 'admired', \" user\"));\n                // Update localStorage with the new state\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    let admiredUsers = JSON.parse(admiredUsersJson);\n                    if (newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                    console.log('Updated admired users in localStorage:', admiredUsers);\n                    // Force a refresh of the following list to ensure it's up to date\n                    const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        }\n                    });\n                    console.log('Updated following list:', followingResponse.data);\n                    // Double-check our state is correct\n                    if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                        const isActuallyFollowing = followingResponse.data.following.some((user)=>user.user_id === userId || user === userId);\n                        if (isActuallyFollowing !== newAdmiringState) {\n                            console.log('State mismatch detected, correcting...');\n                            setIsAdmiring(isActuallyFollowing);\n                            // Update localStorage again with the correct state\n                            admiredUsers = JSON.parse(localStorage.getItem('admiredUsers') || '{}');\n                            if (isActuallyFollowing) {\n                                admiredUsers[userId] = true;\n                            } else {\n                                delete admiredUsers[userId];\n                            }\n                            localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                        }\n                    }\n                } catch (storageError) {\n                    console.error('Error updating localStorage:', storageError);\n                }\n            } catch (apiError) {\n                console.error(\"Error \".concat(isAdmiring ? 'unadmiring' : 'admiring', \" user:\"), apiError);\n                if (apiError.response) {\n                    console.log('Error response data:', apiError.response.data);\n                    console.log('Error response status:', apiError.response.status);\n                    // If the error is that the user is already following/not following, the UI state is already correct\n                    if (apiError.response.status === 400 && apiError.response.data && (apiError.response.data.error === \"Already following this user\" || apiError.response.data.error === \"Not following this user\")) {\n                        console.log('Already in desired state, keeping UI updated');\n                        return;\n                    }\n                }\n                // If there was an error that wasn't just \"already in desired state\", revert the UI\n                setIsAdmiring(!newAdmiringState);\n                // Also update localStorage to match\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (!newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                } catch (storageError) {\n                    console.error('Error updating localStorage after API error:', storageError);\n                }\n            }\n        } catch (error) {\n            console.error('Unexpected error in handleAdmireToggle:', error);\n            // Revert UI state on unexpected errors\n            setIsAdmiring(!isAdmiring);\n        } finally{\n            setIsAdmiringLoading(false);\n        }\n    };\n    // Initialize like status from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!videoId) return;\n            try {\n                // Check if this video is liked in localStorage\n                const likedGlimpsesData = localStorage.getItem('likedGlimpses');\n                const likedMoviesData = localStorage.getItem('likedMovies');\n                let isVideoLiked = false;\n                // Check glimpses\n                if (likedGlimpsesData) {\n                    const likedGlimpses = JSON.parse(likedGlimpsesData);\n                    if (Array.isArray(likedGlimpses) && likedGlimpses.includes(videoId)) {\n                        isVideoLiked = true;\n                    }\n                }\n                // Check movies\n                if (!isVideoLiked && likedMoviesData) {\n                    const likedMovies = JSON.parse(likedMoviesData);\n                    if (Array.isArray(likedMovies) && likedMovies.includes(videoId)) {\n                        isVideoLiked = true;\n                    }\n                }\n                setIsLiked(isVideoLiked);\n                console.log(\"\\uD83C\\uDFAC VideoInteractionBar: Video \".concat(videoId, \" like status:\"), isVideoLiked);\n            } catch (error) {\n                console.error('🎬 Error loading like status for VideoInteractionBar:', error);\n                setIsLiked(false);\n            }\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        videoId\n    ]);\n    // Check if user is already admiring on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!userId) return;\n            // First check localStorage for cached admiring state\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers');\n                if (admiredUsersJson) {\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (admiredUsers[userId]) {\n                        console.log('Found admiring status in localStorage:', true);\n                        setIsAdmiring(true);\n                    // Continue with API check to verify\n                    }\n                }\n            } catch (storageError) {\n                console.error('Error reading from localStorage:', storageError);\n            }\n            const checkAdmiringStatus = {\n                \"VideoInteractionBar.useEffect.checkAdmiringStatus\": async ()=>{\n                    console.log('Checking admiring status for user ID:', userId);\n                    // Get token from localStorage\n                    const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                    if (!token) {\n                        console.warn('No authentication token found');\n                        return;\n                    }\n                    // Check if this is the current user's content - no need to check admiring status\n                    try {\n                        const tokenParts = token.split('.');\n                        if (tokenParts.length === 3) {\n                            const payload = JSON.parse(atob(tokenParts[1]));\n                            if (payload.user_id === userId) {\n                                console.log('This is the current user\\'s content, skipping admiring status check');\n                                setIsAdmiring(false); // User can't admire themselves\n                                return;\n                            }\n                        }\n                    } catch (tokenError) {\n                        console.log('Could not decode token to check current user, proceeding with API calls');\n                    }\n                    try {\n                        // Direct API call to check following status - most reliable method\n                        const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('Following response:', followingResponse.data);\n                        if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                            // Check if userId is in the following array\n                            const isFollowing = followingResponse.data.following.some({\n                                \"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\": (user)=>user.user_id === userId || user === userId\n                            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\"]);\n                            console.log(\"User \".concat(isFollowing ? 'is' : 'is not', \" in following list:\"), userId);\n                            setIsAdmiring(isFollowing);\n                            // Update localStorage for future reference\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (isFollowing) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                            return;\n                        }\n                    } catch (followingError) {\n                        console.log('Error fetching following list, trying alternative method');\n                    }\n                    // Fallback: Try to get user profile\n                    try {\n                        const profileResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-profile?user_id=\".concat(userId), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('User profile response:', profileResponse.data);\n                        if (profileResponse.data && profileResponse.data.is_following !== undefined) {\n                            console.log('Setting admiring status from profile:', profileResponse.data.is_following);\n                            setIsAdmiring(profileResponse.data.is_following);\n                            // Update localStorage\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (profileResponse.data.is_following) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                        }\n                    } catch (profileError) {\n                        console.log('Error fetching user profile, trying another method');\n                        // Last resort: Try the user-follow-stats endpoint\n                        try {\n                            const statsResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-follow-stats?target_user_id=\".concat(userId), {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token),\n                                    'Content-Type': 'application/json'\n                                }\n                            });\n                            console.log('User follow stats response:', statsResponse.data);\n                            if (statsResponse.data && statsResponse.data.is_following !== undefined) {\n                                setIsAdmiring(statsResponse.data.is_following);\n                                // Update localStorage\n                                try {\n                                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                    const admiredUsers = JSON.parse(admiredUsersJson);\n                                    if (statsResponse.data.is_following) {\n                                        admiredUsers[userId] = true;\n                                    } else {\n                                        delete admiredUsers[userId];\n                                    }\n                                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                } catch (storageError) {\n                                    console.error('Error updating localStorage:', storageError);\n                                }\n                            }\n                        } catch (statsError) {\n                            console.log('All API methods failed to check admiring status, using localStorage fallback');\n                            // Fallback to localStorage if all API calls fail\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                const isAdmiringFromStorage = admiredUsers[userId] === true;\n                                setIsAdmiring(isAdmiringFromStorage);\n                                console.log(\"Using localStorage fallback: \".concat(isAdmiringFromStorage ? 'admiring' : 'not admiring'));\n                            } catch (storageError) {\n                                console.log('localStorage fallback also failed, defaulting to not admiring');\n                                setIsAdmiring(false);\n                            }\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus\"];\n            // Call the API check function\n            checkAdmiringStatus();\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        userId\n    ]);\n    // Add click outside handler to close menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"VideoInteractionBar.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.menu-container')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"VideoInteractionBar.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"VideoInteractionBar.useEffect\"];\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        showMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-white p-2 sm:p-3 md:p-4 rounded-b-xl border border-gray-200 border-t-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 sm:space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLikeToggle,\n                                    disabled: isLiking,\n                                    className: \"flex items-center justify-center rounded-full p-1.5 sm:p-2 transition-colors \".concat(isLiked ? 'bg-red-100 hover:bg-red-200' : 'bg-gray-100 hover:bg-gray-200'),\n                                    title: isLiked ? 'Unlike' : 'Like',\n                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdFavorite, {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        color: \"#B31B1E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdFavoriteBorder, {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        color: \"#666\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    onClick: handleMessageClick,\n                                    disabled: isCreatingChat,\n                                    title: \"Send a message\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: formatViewCount(viewCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"views\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative menu-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMenu(!showMenu),\n                                        className: \"p-1.5 rounded-full hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 18,\n                                            className: \"text-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: \"Report content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 685,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 649,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"cursor-pointer\",\n                                        onClick: navigateToUserProfile,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            username: username || \"Anonymous\",\n                                            size: \"md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-black cursor-pointer hover:underline\",\n                                                onClick: navigateToUserProfile,\n                                                children: username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Uploaded \",\n                                                    uploadDate || 'recently'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMessageClick,\n                                        disabled: isCreatingChat,\n                                        className: \"flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-full text-sm transition-colors\",\n                                        title: \"Send a message\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isCreatingChat ? \"Opening...\" : \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAdmireToggle,\n                                        disabled: isAdmiringLoading,\n                                        className: \"flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors bg-[#B31B1E] text-white hover:bg-red-700\",\n                                        title: isAdmiring ? \"Admiring\" : \"Admire this user\",\n                                        children: isAdmiringLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                isAdmiring ? 'Admiring' : 'Admire',\n                                                !isAdmiring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 35\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 9\n                    }, undefined),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black mt-2 text-xs sm:text-sm line-clamp-3 sm:line-clamp-none\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 753,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 706,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n        lineNumber: 648,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoInteractionBar, \"Ub7IOFbhXSoy7hmqI9MF3vyUtUI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = VideoInteractionBar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VideoInteractionBar);\nvar _c;\n$RefreshReg$(_c, \"VideoInteractionBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/VideoInteractionBar.tsx\n"));

/***/ })

});