import os
import json
import uuid
import jwt
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
from datetime import datetime, date
from decimal import Decimal

# Load environment variables
load_dotenv()

# Database and JWT configuration
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
JWT_SECRET = os.getenv('JWT_SECRET')

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST, PUT, DELETE",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        token = token.split()[1]

        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST, PUT, DELETE",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST, PUT, DELETE",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

# Website tables are created in wedding_tables.py

# Website API endpoints
def get_website_templates(event):
    """Get all available website templates"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        cursor.execute('SELECT * FROM website_templates ORDER BY name')
        templates = cursor.fetchall()

        # Convert to serializable format
        serializable_templates = []
        for template in templates:
            template_dict = dict(template)
            for key, value in template_dict.items():
                if isinstance(value, (datetime, date)):
                    template_dict[key] = value.isoformat()
            serializable_templates.append(template_dict)

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'templates': serializable_templates})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def get_user_websites(event):
    """Get all websites created by a user"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Join with templates to get template information
        cursor.execute('''
            SELECT w.*, t.name as template_name, t.thumbnail_url as template_thumbnail
            FROM user_websites w
            LEFT JOIN website_templates t ON w.template_id = t.template_id
            WHERE w.user_id = %s
            ORDER BY w.updated_at DESC
        ''', (user_id,))

        websites = cursor.fetchall()

        # Convert to serializable format
        serializable_websites = []
        for website in websites:
            website_dict = dict(website)
            for key, value in website_dict.items():
                if isinstance(value, (datetime, date)):
                    website_dict[key] = value.isoformat()
            serializable_websites.append(website_dict)

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'websites': serializable_websites})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def get_website_details(event):
    """Get details of a specific website"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    # Get website_id from query parameters
    query_params = event.get('queryStringParameters', {}) or {}
    website_id = query_params.get('website_id')

    if not website_id:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'website_id is required'})
        }

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Join with templates to get template information
        cursor.execute('''
            SELECT w.*, t.name as template_name, t.thumbnail_url as template_thumbnail,
                   t.default_colors as template_default_colors, t.default_fonts as template_default_fonts
            FROM user_websites w
            LEFT JOIN website_templates t ON w.template_id = t.template_id
            WHERE w.website_id = %s AND w.user_id = %s
        ''', (website_id, user_id))

        website = cursor.fetchone()

        if not website:
            return {
                'statusCode': 404,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, GET',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Website not found'})
            }

        # Convert to serializable format
        website_dict = dict(website)
        for key, value in website_dict.items():
            if isinstance(value, (datetime, date)):
                website_dict[key] = value.isoformat()

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'website': website_dict})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def create_website(event):
    """Create a new website"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        # Parse request body
        body = json.loads(event.get('body', '{}'))

        title = body.get('title')
        couple_names = body.get('couple_names', '')
        template_id = body.get('template_id')

        # Handle empty date values - PostgreSQL doesn't accept empty strings for dates
        wedding_date = body.get('wedding_date')
        if wedding_date == '':
            wedding_date = None

        wedding_location = body.get('wedding_location')
        about_couple = body.get('about_couple')
        design_settings = body.get('design_settings', {})

        if not title or not template_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'title and template_id are required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Generate a unique website ID
            website_id = str(uuid.uuid4())

            # Generate a deployed URL that will work with our frontend
            # This URL will point to the wedding/[id] route in the Next.js app
            deployed_url = f"/wedding/{website_id}"

            # First, check if the couple_names column exists
            cursor.execute(
                "SELECT column_name FROM information_schema.columns WHERE table_name='user_websites' AND column_name='couple_names'"
            )
            column_exists = cursor.fetchone()

            # Always store couple_names in design_settings for compatibility
            if couple_names:
                design_settings['couple_names'] = couple_names

            # If the column exists, include it in the INSERT
            if column_exists:
                cursor.execute(
                    '''INSERT INTO user_websites
                    (website_id, user_id, title, couple_names, template_id, wedding_date, wedding_location,
                     about_couple, design_settings, deployed_url, is_published)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING *''',
                    (website_id, user_id, title, couple_names, template_id, wedding_date, wedding_location,
                     about_couple, json.dumps(design_settings), deployed_url, False)
                )
            else:
                # If the column doesn't exist, only use design_settings

                cursor.execute(
                    '''INSERT INTO user_websites
                    (website_id, user_id, title, template_id, wedding_date, wedding_location,
                     about_couple, design_settings, deployed_url, is_published)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING *''',
                    (website_id, user_id, title, template_id, wedding_date, wedding_location,
                     about_couple, json.dumps(design_settings), deployed_url, False)
                )

            new_website = cursor.fetchone()
            conn.commit()

            # Convert to serializable format
            website_dict = dict(new_website)
            for key, value in website_dict.items():
                if isinstance(value, (datetime, date)):
                    website_dict[key] = value.isoformat()

            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'success': True, 'website': website_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            # Print detailed error for debugging
            import traceback
            error_details = traceback.format_exc()
            print(f"Error in create_website: {str(e)}\n{error_details}")

            # Check if the column exists
            try:
                check_cursor = conn.cursor()
                check_cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name='user_websites' AND column_name='couple_names'")
                column_exists = check_cursor.fetchone()
                column_status = "Column exists" if column_exists else "Column does not exist"
            except:
                column_status = "Could not check column existence"

            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({
                    'error': str(e),
                    'details': error_details,
                    'column_status': column_status
                })
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def update_website(event):
    """Update an existing website"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        # Parse request body
        body = json.loads(event.get('body', '{}'))

        website_id = body.get('website_id')
        title = body.get('title')
        couple_names = body.get('couple_names')

        # Handle empty date values - PostgreSQL doesn't accept empty strings for dates
        wedding_date = body.get('wedding_date')
        if wedding_date == '':
            wedding_date = None

        wedding_location = body.get('wedding_location')
        about_couple = body.get('about_couple')
        design_settings = body.get('design_settings')
        is_published = body.get('is_published')

        if not website_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'website_id is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Check if the website exists and belongs to the user
            cursor.execute(
                'SELECT * FROM user_websites WHERE website_id = %s AND user_id = %s',
                (website_id, user_id)
            )

            existing_website = cursor.fetchone()

            if not existing_website:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Website not found or not owned by user'})
                }

            # Generate a new deployed URL that will work with our frontend
            # This URL will point to the wedding/[id] route in the Next.js app
            deployed_url = f"/wedding/{website_id}?v={int(datetime.now().timestamp())}"

            # Build the update query dynamically based on provided fields
            update_fields = []
            update_values = []

            if title is not None:
                update_fields.append("title = %s")
                update_values.append(title)

            # Check if the couple_names column exists
            cursor.execute(
                "SELECT column_name FROM information_schema.columns WHERE table_name='user_websites' AND column_name='couple_names'"
            )
            column_exists = cursor.fetchone()

            # Handle couple_names - store in both column and design_settings for compatibility
            if couple_names is not None:
                # If the column exists, update it directly
                if column_exists:
                    update_fields.append("couple_names = %s")
                    update_values.append(couple_names)

                # Always store in design_settings too for backward compatibility
                if design_settings is None:
                    # Get the current design_settings
                    cursor.execute(
                        'SELECT design_settings FROM user_websites WHERE website_id = %s',
                        (website_id,)
                    )
                    current_settings = cursor.fetchone()[0] or {}
                    if isinstance(current_settings, str):
                        current_settings = json.loads(current_settings)

                    # Update with couple_names
                    current_settings['couple_names'] = couple_names
                    design_settings = current_settings

                    # Add to update fields
                    update_fields.append("design_settings = %s")
                    update_values.append(json.dumps(design_settings))
                else:
                    # Update the provided design_settings
                    design_settings['couple_names'] = couple_names

            if wedding_date is not None:
                update_fields.append("wedding_date = %s")
                update_values.append(wedding_date)

            if wedding_location is not None:
                update_fields.append("wedding_location = %s")
                update_values.append(wedding_location)

            if about_couple is not None:
                update_fields.append("about_couple = %s")
                update_values.append(about_couple)

            if design_settings is not None:
                update_fields.append("design_settings = %s")
                update_values.append(json.dumps(design_settings))

            if is_published is not None:
                update_fields.append("is_published = %s")
                update_values.append(is_published)

            # Always update the deployed URL and updated_at timestamp
            update_fields.append("deployed_url = %s")
            update_values.append(deployed_url)

            update_fields.append("updated_at = NOW()")

            # Add the website_id and user_id to the values list
            update_values.append(website_id)
            update_values.append(user_id)

            # Execute the update query
            cursor.execute(
                f'''UPDATE user_websites
                SET {', '.join(update_fields)}
                WHERE website_id = %s AND user_id = %s
                RETURNING *''',
                update_values
            )

            updated_website = cursor.fetchone()
            conn.commit()

            # Convert to serializable format
            website_dict = dict(updated_website)
            for key, value in website_dict.items():
                if isinstance(value, (datetime, date)):
                    website_dict[key] = value.isoformat()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'success': True, 'website': website_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def delete_website(event):
    """Delete a website"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    # Get website_id from query parameters
    query_params = event.get('queryStringParameters', {}) or {}
    website_id = query_params.get('website_id')

    if not website_id:
        # Try to get website_id from the body (for POST requests)
        try:
            body = json.loads(event.get('body', '{}'))
            website_id = body.get('website_id')
        except:
            pass

    if not website_id:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'website_id is required'})
        }

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if the website exists and belongs to the user
        cursor.execute(
            'SELECT * FROM user_websites WHERE website_id = %s AND user_id = %s',
            (website_id, user_id)
        )

        existing_website = cursor.fetchone()

        if not existing_website:
            return {
                'statusCode': 404,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Website not found or not owned by user'})
            }

        # Delete the website
        cursor.execute(
            'DELETE FROM user_websites WHERE website_id = %s AND user_id = %s',
            (website_id, user_id)
        )

        conn.commit()

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'success': True})
        }
    except Exception as e:
        if conn:
            conn.rollback()
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def get_public_website(event):
    """Get a public view of a website"""
    # Get website_id from query parameters
    query_params = event.get('queryStringParameters', {}) or {}
    website_id = query_params.get('website_id')

    if not website_id:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'website_id is required'})
        }

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Join with templates to get template information
        # Note: We're not checking is_published anymore to make it easier for testing
        cursor.execute('''
            SELECT w.*, t.name as template_name, t.thumbnail_url as template_thumbnail,
                   t.default_colors as template_default_colors, t.default_fonts as template_default_fonts
            FROM user_websites w
            LEFT JOIN website_templates t ON w.template_id = t.template_id
            WHERE w.website_id = %s
        ''', (website_id,))

        website = cursor.fetchone()

        if not website:
            return {
                'statusCode': 404,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, GET',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Website not found'})
            }

        # Convert to serializable format
        website_dict = dict(website)
        for key, value in website_dict.items():
            if isinstance(value, (datetime, date)):
                website_dict[key] = value.isoformat()

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'website': website_dict})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()
