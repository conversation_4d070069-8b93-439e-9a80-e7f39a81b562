"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/[id]/page.tsx":
/*!***************************************!*\
  !*** ./app/home/<USER>/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PhotoViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Navigation */ \"(app-pages-browser)/./components/HomeDashboard/Navigation.tsx\");\n/* harmony import */ var _components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _components_HomeDashboard_Photos__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Photos */ \"(app-pages-browser)/./components/HomeDashboard/Photos.tsx\");\n/* harmony import */ var _components_DetailPageLazyLoad__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../../components/DetailPageLazyLoad */ \"(app-pages-browser)/./components/DetailPageLazyLoad.tsx\");\n/* harmony import */ var _services_myWeddingService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../../services/myWeddingService */ \"(app-pages-browser)/./services/myWeddingService.ts\");\n/* harmony import */ var _barrel_optimize_names_Bookmark_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bookmark!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Main component for the photo viewing page\nfunction PhotoViewPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const photoId = params === null || params === void 0 ? void 0 : params.id;\n    const [photo, setPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarExpanded, setSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightSidebarExpanded, setRightSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likeCount, setLikeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [relatedPhotos, setRelatedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiking, setIsLiking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const imageRef = useRef(null);\n    const [isSaved, setIsSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toast, setToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Set client-side rendering flag\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PhotoViewPage.useEffect\": ()=>setIsClient(true)\n    }[\"PhotoViewPage.useEffect\"], []);\n    // Fetch photo details when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PhotoViewPage.useEffect\": ()=>{\n            const fetchPhotoDetails = {\n                \"PhotoViewPage.useEffect.fetchPhotoDetails\": async ()=>{\n                    console.log('=== Starting photo fetch ===');\n                    console.log('Photo ID:', photoId);\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // Get token from localStorage\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                        console.log('Token found:', token ? 'Yes' : 'No');\n                        if (token) {\n                            console.log('Token preview:', token.substring(0, 50) + '...');\n                            console.log('Token length:', token.length);\n                            console.log('Token starts with eyJ (JWT):', token.startsWith('eyJ'));\n                            console.log('Full token:', token);\n                        }\n                        if (!token) {\n                            console.warn('No authentication token found');\n                            setError('Authentication required. Please log in.');\n                            setLoading(false);\n                            return;\n                        }\n                        // Store token in a cookie for server components to access\n                        document.cookie = \"token=\".concat(token, \"; path=/; max-age=3600; SameSite=Strict\");\n                        // First test if the photos list API is working\n                        console.log('Testing photos list API first...');\n                        try {\n                            const photosTestResponse = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get('/api/photos?page=1&limit=1', {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token)\n                                }\n                            });\n                            console.log('Photos list API test successful:', photosTestResponse.status);\n                        } catch (photosError) {\n                            var _photosError_response, _photosError_response_data, _photosError_response1;\n                            console.error('Photos list API test failed:', ((_photosError_response = photosError.response) === null || _photosError_response === void 0 ? void 0 : _photosError_response.data) || photosError.message);\n                            throw new Error('Photos list API is also failing: ' + (((_photosError_response1 = photosError.response) === null || _photosError_response1 === void 0 ? void 0 : (_photosError_response_data = _photosError_response1.data) === null || _photosError_response_data === void 0 ? void 0 : _photosError_response_data.error) || photosError.message));\n                        }\n                        // Try to get the photo from the photos list first as a workaround\n                        console.log('Trying to get photo from photos list...');\n                        let photoFromList = null;\n                        try {\n                            const photosResponse = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get('/api/photos?page=1&limit=50', {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token)\n                                }\n                            });\n                            if (photosResponse.data && photosResponse.data.photos) {\n                                photoFromList = photosResponse.data.photos.find({\n                                    \"PhotoViewPage.useEffect.fetchPhotoDetails\": (p)=>p.photo_id === photoId\n                                }[\"PhotoViewPage.useEffect.fetchPhotoDetails\"]);\n                                if (photoFromList) {\n                                    console.log('Found photo in photos list:', photoFromList);\n                                    // Add some default values that might be missing\n                                    photoFromList.liked = false;\n                                    photoFromList.comments = [];\n                                }\n                            }\n                        } catch (listError) {\n                            console.error('Failed to get photo from list:', listError);\n                        }\n                        // Try the individual photo API first, but fall back to photos list if it fails\n                        let response;\n                        try {\n                            var _response_data;\n                            response = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/api/photo/\".concat(photoId), {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token)\n                                }\n                            });\n                            // Check if the response contains an error\n                            if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.error) {\n                                throw new Error(response.data.error);\n                            }\n                        } catch (individualError) {\n                            var _individualError_response, _individualError_response1;\n                            console.error('Individual photo API failed:', {\n                                error: individualError,\n                                response: (_individualError_response = individualError.response) === null || _individualError_response === void 0 ? void 0 : _individualError_response.data,\n                                message: individualError.message,\n                                status: (_individualError_response1 = individualError.response) === null || _individualError_response1 === void 0 ? void 0 : _individualError_response1.status\n                            });\n                            // If individual photo API fails, try to get the photo from the photos list\n                            if (photoFromList) {\n                                console.log('Using photo data from photos list as fallback');\n                                response = {\n                                    data: photoFromList\n                                };\n                            } else {\n                                // Try to fetch from photos list API as a last resort\n                                try {\n                                    var _photosResponse_data_photos, _photosResponse_data;\n                                    console.log('Attempting to fetch photo from photos list API...');\n                                    const photosResponse = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get('/api/photos?page=1&limit=50', {\n                                        headers: {\n                                            Authorization: \"Bearer \".concat(token)\n                                        }\n                                    });\n                                    const foundPhoto = (_photosResponse_data = photosResponse.data) === null || _photosResponse_data === void 0 ? void 0 : (_photosResponse_data_photos = _photosResponse_data.photos) === null || _photosResponse_data_photos === void 0 ? void 0 : _photosResponse_data_photos.find({\n                                        \"PhotoViewPage.useEffect.fetchPhotoDetails\": (p)=>p.photo_id === photoId\n                                    }[\"PhotoViewPage.useEffect.fetchPhotoDetails\"]);\n                                    if (foundPhoto) {\n                                        console.log('Found photo in photos list, using as fallback');\n                                        response = {\n                                            data: foundPhoto\n                                        };\n                                    } else {\n                                        throw new Error('Photo not found in photos list');\n                                    }\n                                } catch (listError) {\n                                    var _listError_response, _listError_response1;\n                                    console.error('Photos list API also failed:', {\n                                        error: listError,\n                                        response: (_listError_response = listError.response) === null || _listError_response === void 0 ? void 0 : _listError_response.data,\n                                        message: listError.message,\n                                        status: (_listError_response1 = listError.response) === null || _listError_response1 === void 0 ? void 0 : _listError_response1.status\n                                    });\n                                    throw new Error('Failed to load photo: Both individual photo API and photos list API failed');\n                                }\n                            }\n                        }\n                        if (response.data) {\n                            if (response.data.error) {\n                                throw new Error(response.data.error);\n                            }\n                            // The backend returns the photo data directly\n                            const photoData = response.data;\n                            if (!photoData.photo_id) {\n                                throw new Error('Invalid photo data received - missing photo_id');\n                            }\n                            setPhoto(photoData);\n                            setIsLiked(photoData.liked || false);\n                            setLikeCount(photoData.photo_likes || 0);\n                            // Fetch related photos\n                            fetchRelatedPhotos(photoData.photo_category, photoData.user_id);\n                        } else {\n                            throw new Error('No data received from server');\n                        }\n                    } catch (err) {\n                        var _err_response, _err_response1, _err_response2, _err_response3, _err_response_data, _err_response4;\n                        console.error('Error fetching photo details:', {\n                            error: err,\n                            message: err.message,\n                            response: (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data,\n                            status: (_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status,\n                            stack: err.stack\n                        });\n                        let errorMessage = 'Failed to load photo. Please try again later.';\n                        if (((_err_response2 = err.response) === null || _err_response2 === void 0 ? void 0 : _err_response2.status) === 401) {\n                            errorMessage = 'Authentication failed. Please log in again.';\n                        } else if (((_err_response3 = err.response) === null || _err_response3 === void 0 ? void 0 : _err_response3.status) === 404) {\n                            errorMessage = 'Photo not found.';\n                        } else if ((_err_response4 = err.response) === null || _err_response4 === void 0 ? void 0 : (_err_response_data = _err_response4.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) {\n                            errorMessage = err.response.data.error;\n                        } else if (err.message) {\n                            errorMessage = err.message;\n                        }\n                        setError(errorMessage);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"PhotoViewPage.useEffect.fetchPhotoDetails\"];\n            if (photoId && isClient) {\n                fetchPhotoDetails();\n            }\n        }\n    }[\"PhotoViewPage.useEffect\"], [\n        photoId,\n        isClient\n    ]);\n    // Utility to decode JWT and extract user_id\n    function getUserIdFromToken() {\n        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n        if (!token) return null;\n        try {\n            const payload = JSON.parse(atob(token.split('.')[1]));\n            return payload.user_id || payload.sub || null;\n        } catch (e) {\n            return null;\n        }\n    }\n    // Check saved status on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PhotoViewPage.useEffect\": ()=>{\n            if (!photo || !photo.photo_id) return;\n            const userId = getUserIdFromToken();\n            if (!userId) return;\n            (0,_services_myWeddingService__WEBPACK_IMPORTED_MODULE_8__.checkContentSavedStatus)({\n                user_id: userId,\n                content_id: photo.photo_id\n            }).then({\n                \"PhotoViewPage.useEffect\": (res)=>setIsSaved(res.data.is_saved)\n            }[\"PhotoViewPage.useEffect\"]).catch({\n                \"PhotoViewPage.useEffect\": ()=>setIsSaved(false)\n            }[\"PhotoViewPage.useEffect\"]);\n        }\n    }[\"PhotoViewPage.useEffect\"], [\n        photo\n    ]);\n    // Function to fetch related photos\n    const fetchRelatedPhotos = async (category, userId)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) return;\n            // Prioritize category if available, otherwise fetch user's photos\n            const queryParam = category ? \"?category=\".concat(encodeURIComponent(category), \"&limit=6\") : userId ? \"?user_id=\".concat(encodeURIComponent(userId), \"&limit=6\") : '?limit=6';\n            const response = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/api/photos\".concat(queryParam), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.data && response.data.photos) {\n                // Filter out the current photo\n                const filtered = response.data.photos.filter((p)=>p.photo_id !== photoId);\n                setRelatedPhotos(filtered.slice(0, 5)); // Limit to 5 related photos\n            }\n        } catch (err) {\n            console.error('Error fetching related photos:', err);\n        }\n    };\n    // Function to handle like/unlike\n    const handleLikeToggle = async ()=>{\n        if (isLiking) return; // Prevent multiple clicks\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            setIsLiking(true);\n            // Optimistically update UI\n            const newLikedState = !isLiked;\n            setIsLiked(newLikedState);\n            setLikeCount((prev)=>newLikedState ? prev + 1 : prev - 1);\n            // Make API request to like/unlike\n            const endpoint = newLikedState ? '/api/like' : '/api/unlike';\n            await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(endpoint, {\n                content_id: photoId,\n                content_type: 'photo'\n            }, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n        } catch (err) {\n            console.error('Error toggling like:', err);\n            // Revert optimistic update on error\n            setIsLiked(!isLiked);\n            setLikeCount((prev)=>isLiked ? prev + 1 : prev - 1);\n        } finally{\n            setIsLiking(false);\n        }\n    };\n    // Function to toggle fullscreen mode\n    const toggleFullscreen = ()=>{\n        setIsFullscreen(!isFullscreen);\n    };\n    // Function to navigate back\n    const goBack = ()=>{\n        router.back();\n    };\n    // Add this function near your other utility functions\n    const getImageSource = (photoUrl)=>{\n        if (!photoUrl || photoUrl.trim() === '') {\n            console.warn('Empty photo URL detected');\n            return '/pics/placeholder.svg';\n        }\n        return photoUrl;\n    };\n    const handleSaveToggle = async ()=>{\n        if (!photo || !photo.photo_id) return;\n        const userId = getUserIdFromToken();\n        if (!userId) return;\n        setIsSaving(true);\n        try {\n            if (isSaved) {\n                await (0,_services_myWeddingService__WEBPACK_IMPORTED_MODULE_8__.removeContentFromMyWedding)({\n                    user_id: userId,\n                    content_id: photo.photo_id\n                });\n                setIsSaved(false);\n                setToast('Removed from My Weddings');\n            } else {\n                await (0,_services_myWeddingService__WEBPACK_IMPORTED_MODULE_8__.saveContentToMyWedding)({\n                    user_id: userId,\n                    content_id: photo.photo_id,\n                    content_type: 'photo'\n                });\n                setIsSaved(true);\n                setToast('Photo saved to My Weddings');\n            }\n        } catch (e) {\n            setToast('Something went wrong');\n            console.error('Save/remove API error', e);\n        } finally{\n            setIsSaving(false);\n            setTimeout(()=>setToast(null), 2000);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: isClient ? \"flex flex-col min-h-screen bg-white w-full\" : \"opacity-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__.TopNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__.SideNavigation, {\n                        expanded: sidebarExpanded,\n                        onExpand: ()=>setSidebarExpanded(true),\n                        onCollapse: ()=>setSidebarExpanded(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 py-4 pr-4 pl-0 bg-white \".concat(sidebarExpanded ? \"md:ml-48\" : \"md:ml-20\"),\n                        style: {\n                            marginTop: \"80px\",\n                            transition: \"all 300ms ease-in-out\",\n                            minHeight: \"calc(100vh - 80px)\",\n                            paddingBottom: \"40px\",\n                            overflowY: \"auto\",\n                            overflowX: \"hidden\",\n                            marginRight: rightSidebarExpanded ? \"320px\" : \"0\",\n                            paddingRight: \"20px\",\n                            paddingLeft: \"0\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-8 max-w-[1100px] w-full pl-2\",\n                            children: [\n                                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-10 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Loading photo...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this),\n                                error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-10 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-500 mb-4\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goBack,\n                                            className: \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                                            children: \"Go Back\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, this),\n                                !loading && !error && photo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: goBack,\n                                                className: \"flex items-center text-gray-600 hover:text-red-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        className: \"mr-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"15 18 9 12 15 6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Back\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-semibold\",\n                                                            children: photo.photo_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        photo.created_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mt-1\",\n                                                            children: new Date(photo.created_at).toLocaleDateString('en-US', {\n                                                                year: 'numeric',\n                                                                month: 'long',\n                                                                day: 'numeric'\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            username: photo.username || \"user\",\n                                                            size: \"md\",\n                                                            isGradientBorder: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm font-medium\",\n                                                            children: photo.username\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: imageRef,\n                                            className: \"relative \".concat(isFullscreen ? \"fixed inset-0 z-50 bg-black flex items-center justify-center\" : \"w-full rounded-lg overflow-hidden shadow-lg\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative \".concat(isFullscreen ? \"w-full h-full\" : \"w-full\"),\n                                                    style: {\n                                                        height: isFullscreen ? \"100vh\" : \"calc(100vh - 300px)\",\n                                                        maxHeight: isFullscreen ? \"100vh\" : \"70vh\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        src: getImageSource(photo.photo_url),\n                                                        alt: photo.photo_name || \"Photo\",\n                                                        fill: true,\n                                                        sizes: \"(max-width: 640px) 100vw, (max-width: 1024px) 80vw, 70vw\",\n                                                        className: \"object-contain\",\n                                                        priority: true,\n                                                        unoptimized: true,\n                                                        onError: (e)=>{\n                                                            console.error(\"Failed to load image: \".concat(photo.photo_url));\n                                                            const imgElement = e.target;\n                                                            imgElement.src = \"/pics/placeholder.svg\";\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: toggleFullscreen,\n                                                    className: \"absolute bottom-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all\",\n                                                    \"aria-label\": isFullscreen ? \"Exit fullscreen\" : \"View fullscreen\",\n                                                    children: isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"4 14 10 14 10 20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"20 10 14 10 14 4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"14\",\n                                                                y1: \"10\",\n                                                                x2: \"21\",\n                                                                y2: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"3\",\n                                                                y1: \"21\",\n                                                                x2: \"10\",\n                                                                y2: \"14\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"15 3 21 3 21 9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"9 21 3 21 3 15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"21\",\n                                                                y1: \"3\",\n                                                                x2: \"14\",\n                                                                y2: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"3\",\n                                                                y1: \"21\",\n                                                                x2: \"10\",\n                                                                y2: \"14\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: toggleFullscreen,\n                                                    className: \"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all\",\n                                                    \"aria-label\": \"Close fullscreen\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"24\",\n                                                        height: \"24\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"18\",\n                                                                y1: \"6\",\n                                                                x2: \"6\",\n                                                                y2: \"18\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"6\",\n                                                                y1: \"6\",\n                                                                x2: \"18\",\n                                                                y2: \"18\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, this),\n                                !loading && !error && photo && !isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex flex-col gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-6 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"18\",\n                                                            height: \"18\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                photo.photo_views || 0,\n                                                                \" views\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center gap-1 hover:text-red-500 \".concat(isLiking ? 'opacity-50 cursor-not-allowed' : ''),\n                                                    onClick: handleLikeToggle,\n                                                    disabled: isLiking,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"18\",\n                                                            height: \"18\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: isLiked ? \"currentColor\" : \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            className: isLiked ? \"text-red-500\" : \"\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                likeCount,\n                                                                \" likes\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center gap-1 hover:text-yellow-600 \".concat(isSaved ? 'text-yellow-600' : ''),\n                                                    title: isSaved ? 'Remove from My Weddings' : 'Save to My Weddings',\n                                                    onClick: handleSaveToggle,\n                                                    disabled: isSaving,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bookmark_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 \".concat(isSaved ? 'text-yellow-600' : 'text-gray-700'),\n                                                            fill: isSaved ? '#FFD700' : 'none'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isSaved ? 'Saved' : 'Save'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this),\n                                        photo.photo_description && photo.photo_description.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-1\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: photo.photo_description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 19\n                                        }, this),\n                                        photo.photo_tags && photo.photo_tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-1\",\n                                                    children: \"Tags\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: photo.photo_tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-gray-100 rounded-full text-sm text-gray-700\",\n                                                            children: tag\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 19\n                                        }, this),\n                                        photo.comments && photo.comments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-2\",\n                                                    children: \"Comments\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: photo.comments.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-3 p-3 bg-gray-50 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    username: comment.username,\n                                                                    size: \"sm\",\n                                                                    isGradientBorder: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: comment.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 696,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-gray-700\",\n                                                                            children: comment.comment_text\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: new Date(comment.created_at).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, comment.comment_id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 19\n                                        }, this),\n                                        relatedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-4\",\n                                                    children: \"Related Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                                                    children: relatedPhotos.map((relatedPhoto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer\",\n                                                            style: {\n                                                                height: \"160px\"\n                                                            },\n                                                            onClick: ()=>{\n                                                                window.location.href = \"/home/<USER>/\".concat(relatedPhoto.photo_id);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative w-full h-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        src: relatedPhoto.photo_url || \"/pics/placeholder.svg\",\n                                                                        alt: relatedPhoto.photo_name || \"Related photo\",\n                                                                        fill: true,\n                                                                        sizes: \"(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw\",\n                                                                        className: \"object-cover\",\n                                                                        unoptimized: true,\n                                                                        onError: (e)=>{\n                                                                            const imgElement = e.target;\n                                                                            if (imgElement) {\n                                                                                imgElement.src = '/pics/placeholder.svg';\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium truncate\",\n                                                                        children: relatedPhoto.photo_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 739,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, \"\".concat(relatedPhoto.photo_id, \"-\").concat(index), true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-4\",\n                                                    children: \"More Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 749,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DetailPageLazyLoad__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    id: \"more-photos-section\",\n                                                    index: 1,\n                                                    rootMargin: \"0px 0px 200px 0px\",\n                                                    children: (param)=>{\n                                                        let { shouldLoad } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto w-full mx-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Photos__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                shouldLoad: shouldLoad\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 752,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__.RightSidebar, {\n                        expanded: rightSidebarExpanded,\n                        onExpand: ()=>setRightSidebarExpanded(true),\n                        onCollapse: ()=>setRightSidebarExpanded(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                        lineNumber: 763,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_4__.MobileNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                lineNumber: 770,\n                columnNumber: 7\n            }, this),\n            toast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    bottom: 24,\n                    right: 24,\n                    zIndex: 9999\n                },\n                className: \"bg-black text-white px-4 py-2 rounded shadow-lg animate-fade-in\",\n                children: toast\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n                lineNumber: 774,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\photos\\\\[id]\\\\page.tsx\",\n        lineNumber: 387,\n        columnNumber: 5\n    }, this);\n}\n_s(PhotoViewPage, \"CZ7+3NwJTkJmJV95Zwkn1arZmSg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = PhotoViewPage;\nfunction useRef(initialValue) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(initialValue);\n}\nvar _c;\n$RefreshReg$(_c, \"PhotoViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9ob21lL3Bob3Rvcy9baWRdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNBO0FBQ2xCO0FBQ0s7QUFDYTtBQU1hO0FBQ2dCO0FBQ1I7QUFDVTtBQUM3QjtBQUNzRjtBQUM1RjtBQTZCeEMsNENBQTRDO0FBQzdCLFNBQVNtQjs7SUFDdEIsTUFBTUMsU0FBU2xCLDBEQUFTQTtJQUN4QixNQUFNbUIsU0FBU2hCLDBEQUFTQTtJQUN4QixNQUFNaUIsVUFBVUYsbUJBQUFBLDZCQUFBQSxPQUFRRyxFQUFFO0lBRTFCLE1BQU0sQ0FBQ0MsT0FBT0MsU0FBUyxHQUFHeEIsK0NBQVFBLENBQXNCO0lBQ3hELE1BQU0sQ0FBQ3lCLFNBQVNDLFdBQVcsR0FBRzFCLCtDQUFRQSxDQUFVO0lBQ2hELE1BQU0sQ0FBQzJCLE9BQU9DLFNBQVMsR0FBRzVCLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUM2QixpQkFBaUJDLG1CQUFtQixHQUFHOUIsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDK0Isc0JBQXNCQyx3QkFBd0IsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQ2lDLFVBQVVDLFlBQVksR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ21DLFNBQVNDLFdBQVcsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3FDLFdBQVdDLGFBQWEsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3VDLGVBQWVDLGlCQUFpQixHQUFHeEMsK0NBQVFBLENBQVEsRUFBRTtJQUM1RCxNQUFNLENBQUN5QyxjQUFjQyxnQkFBZ0IsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzJDLFVBQVVDLFlBQVksR0FBRzVDLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU02QyxXQUFXakMsT0FBdUI7SUFDeEMsTUFBTSxDQUFDa0MsU0FBU0MsV0FBVyxHQUFHL0MsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDZ0QsVUFBVUMsWUFBWSxHQUFHakQsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDa0QsT0FBT0MsU0FBUyxHQUFHbkQsK0NBQVFBLENBQWdCO0lBRWxELGlDQUFpQztJQUNqQ0QsZ0RBQVNBO21DQUFDLElBQU1tQyxZQUFZO2tDQUFPLEVBQUU7SUFFckMsNENBQTRDO0lBQzVDbkMsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTXFEOzZEQUFvQjtvQkFDeEJDLFFBQVFDLEdBQUcsQ0FBQztvQkFDWkQsUUFBUUMsR0FBRyxDQUFDLGFBQWFqQztvQkFFekJLLFdBQVc7b0JBQ1hFLFNBQVM7b0JBRVQsSUFBSTt3QkFDRiw4QkFBOEI7d0JBQzlCLE1BQU0yQixRQUFRQyxhQUFhQyxPQUFPLENBQUMsWUFDdEJELGFBQWFDLE9BQU8sQ0FBQyxnQkFDckJELGFBQWFDLE9BQU8sQ0FBQzt3QkFFbENKLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0JDLFFBQVEsUUFBUTt3QkFDNUMsSUFBSUEsT0FBTzs0QkFDVEYsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQkMsTUFBTUcsU0FBUyxDQUFDLEdBQUcsTUFBTTs0QkFDdkRMLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJDLE1BQU1JLE1BQU07NEJBQ3pDTixRQUFRQyxHQUFHLENBQUMsZ0NBQWdDQyxNQUFNSyxVQUFVLENBQUM7NEJBQzdEUCxRQUFRQyxHQUFHLENBQUMsZUFBZUM7d0JBQzdCO3dCQUVBLElBQUksQ0FBQ0EsT0FBTzs0QkFDVkYsUUFBUVEsSUFBSSxDQUFDOzRCQUNiakMsU0FBUzs0QkFDVEYsV0FBVzs0QkFDWDt3QkFDRjt3QkFFQSwwREFBMEQ7d0JBQzFEb0MsU0FBU0MsTUFBTSxHQUFHLFNBQWUsT0FBTlIsT0FBTTt3QkFFakMsK0NBQStDO3dCQUMvQ0YsUUFBUUMsR0FBRyxDQUFDO3dCQUNaLElBQUk7NEJBQ0YsTUFBTVUscUJBQXFCLE1BQU05RCw2Q0FBS0EsQ0FBQytELEdBQUcsQ0FBQyw4QkFBOEI7Z0NBQ3ZFQyxTQUFTO29DQUNQQyxlQUFlLFVBQWdCLE9BQU5aO2dDQUMzQjs0QkFDRjs0QkFDQUYsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ1UsbUJBQW1CSSxNQUFNO3dCQUMzRSxFQUFFLE9BQU9DLGFBQWtCO2dDQUNxQkEsdUJBQ1NBLDRCQUFBQTs0QkFEdkRoQixRQUFRMUIsS0FBSyxDQUFDLGdDQUFnQzBDLEVBQUFBLHdCQUFBQSxZQUFZQyxRQUFRLGNBQXBCRCw0Q0FBQUEsc0JBQXNCRSxJQUFJLEtBQUlGLFlBQVlHLE9BQU87NEJBQy9GLE1BQU0sSUFBSUMsTUFBTSxzQ0FBdUNKLENBQUFBLEVBQUFBLHlCQUFBQSxZQUFZQyxRQUFRLGNBQXBCRCw4Q0FBQUEsNkJBQUFBLHVCQUFzQkUsSUFBSSxjQUExQkYsaURBQUFBLDJCQUE0QjFDLEtBQUssS0FBSTBDLFlBQVlHLE9BQU87d0JBQ2pIO3dCQUVBLGtFQUFrRTt3QkFDbEVuQixRQUFRQyxHQUFHLENBQUM7d0JBQ1osSUFBSW9CLGdCQUFnQjt3QkFDcEIsSUFBSTs0QkFDRixNQUFNQyxpQkFBaUIsTUFBTXpFLDZDQUFLQSxDQUFDK0QsR0FBRyxDQUFDLCtCQUErQjtnQ0FDcEVDLFNBQVM7b0NBQ1BDLGVBQWUsVUFBZ0IsT0FBTlo7Z0NBQzNCOzRCQUNGOzRCQUVBLElBQUlvQixlQUFlSixJQUFJLElBQUlJLGVBQWVKLElBQUksQ0FBQ0ssTUFBTSxFQUFFO2dDQUNyREYsZ0JBQWdCQyxlQUFlSixJQUFJLENBQUNLLE1BQU0sQ0FBQ0MsSUFBSTtpRkFBQyxDQUFDQyxJQUFXQSxFQUFFQyxRQUFRLEtBQUsxRDs7Z0NBQzNFLElBQUlxRCxlQUFlO29DQUNqQnJCLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JvQjtvQ0FDM0MsZ0RBQWdEO29DQUNoREEsY0FBY00sS0FBSyxHQUFHO29DQUN0Qk4sY0FBY08sUUFBUSxHQUFHLEVBQUU7Z0NBQzdCOzRCQUNGO3dCQUNGLEVBQUUsT0FBT0MsV0FBVzs0QkFDbEI3QixRQUFRMUIsS0FBSyxDQUFDLGtDQUFrQ3VEO3dCQUNsRDt3QkFFQSwrRUFBK0U7d0JBQy9FLElBQUlaO3dCQUNKLElBQUk7Z0NBUUVBOzRCQVBKQSxXQUFXLE1BQU1wRSw2Q0FBS0EsQ0FBQytELEdBQUcsQ0FBQyxjQUFzQixPQUFSNUMsVUFBVztnQ0FDbEQ2QyxTQUFTO29DQUNQQyxlQUFlLFVBQWdCLE9BQU5aO2dDQUMzQjs0QkFDRjs0QkFFQSwwQ0FBMEM7NEJBQzFDLEtBQUllLGlCQUFBQSxTQUFTQyxJQUFJLGNBQWJELHFDQUFBQSxlQUFlM0MsS0FBSyxFQUFFO2dDQUN4QixNQUFNLElBQUk4QyxNQUFNSCxTQUFTQyxJQUFJLENBQUM1QyxLQUFLOzRCQUNyQzt3QkFDRixFQUFFLE9BQU93RCxpQkFBc0I7Z0NBR2pCQSwyQkFFRkE7NEJBSlY5QixRQUFRMUIsS0FBSyxDQUFDLGdDQUFnQztnQ0FDNUNBLE9BQU93RDtnQ0FDUGIsUUFBUSxHQUFFYSw0QkFBQUEsZ0JBQWdCYixRQUFRLGNBQXhCYSxnREFBQUEsMEJBQTBCWixJQUFJO2dDQUN4Q0MsU0FBU1csZ0JBQWdCWCxPQUFPO2dDQUNoQ0osTUFBTSxHQUFFZSw2QkFBQUEsZ0JBQWdCYixRQUFRLGNBQXhCYSxpREFBQUEsMkJBQTBCZixNQUFNOzRCQUMxQzs0QkFFQSwyRUFBMkU7NEJBQzNFLElBQUlNLGVBQWU7Z0NBQ2pCckIsUUFBUUMsR0FBRyxDQUFDO2dDQUNaZ0IsV0FBVztvQ0FBRUMsTUFBTUc7Z0NBQWM7NEJBQ25DLE9BQU87Z0NBQ0wscURBQXFEO2dDQUNyRCxJQUFJO3dDQVFpQkMsNkJBQUFBO29DQVBuQnRCLFFBQVFDLEdBQUcsQ0FBQztvQ0FDWixNQUFNcUIsaUJBQWlCLE1BQU16RSw2Q0FBS0EsQ0FBQytELEdBQUcsQ0FBQywrQkFBK0I7d0NBQ3BFQyxTQUFTOzRDQUNQQyxlQUFlLFVBQWdCLE9BQU5aO3dDQUMzQjtvQ0FDRjtvQ0FFQSxNQUFNNkIsY0FBYVQsdUJBQUFBLGVBQWVKLElBQUksY0FBbkJJLDRDQUFBQSw4QkFBQUEscUJBQXFCQyxNQUFNLGNBQTNCRCxrREFBQUEsNEJBQTZCRSxJQUFJO3FGQUFDLENBQUNDLElBQVdBLEVBQUVDLFFBQVEsS0FBSzFEOztvQ0FDaEYsSUFBSStELFlBQVk7d0NBQ2QvQixRQUFRQyxHQUFHLENBQUM7d0NBQ1pnQixXQUFXOzRDQUFFQyxNQUFNYTt3Q0FBVztvQ0FDaEMsT0FBTzt3Q0FDTCxNQUFNLElBQUlYLE1BQU07b0NBQ2xCO2dDQUNGLEVBQUUsT0FBT1MsV0FBZ0I7d0NBR1hBLHFCQUVGQTtvQ0FKVjdCLFFBQVExQixLQUFLLENBQUMsZ0NBQWdDO3dDQUM1Q0EsT0FBT3VEO3dDQUNQWixRQUFRLEdBQUVZLHNCQUFBQSxVQUFVWixRQUFRLGNBQWxCWSwwQ0FBQUEsb0JBQW9CWCxJQUFJO3dDQUNsQ0MsU0FBU1UsVUFBVVYsT0FBTzt3Q0FDMUJKLE1BQU0sR0FBRWMsdUJBQUFBLFVBQVVaLFFBQVEsY0FBbEJZLDJDQUFBQSxxQkFBb0JkLE1BQU07b0NBQ3BDO29DQUNBLE1BQU0sSUFBSUssTUFBTTtnQ0FDbEI7NEJBQ0Y7d0JBQ0Y7d0JBRUEsSUFBSUgsU0FBU0MsSUFBSSxFQUFFOzRCQUNqQixJQUFJRCxTQUFTQyxJQUFJLENBQUM1QyxLQUFLLEVBQUU7Z0NBQ3ZCLE1BQU0sSUFBSThDLE1BQU1ILFNBQVNDLElBQUksQ0FBQzVDLEtBQUs7NEJBQ3JDOzRCQUVBLDhDQUE4Qzs0QkFDOUMsTUFBTTBELFlBQVlmLFNBQVNDLElBQUk7NEJBRS9CLElBQUksQ0FBQ2MsVUFBVU4sUUFBUSxFQUFFO2dDQUN2QixNQUFNLElBQUlOLE1BQU07NEJBQ2xCOzRCQUVBakQsU0FBUzZEOzRCQUNUakQsV0FBV2lELFVBQVVMLEtBQUssSUFBSTs0QkFDOUIxQyxhQUFhK0MsVUFBVUMsV0FBVyxJQUFJOzRCQUV0Qyx1QkFBdUI7NEJBQ3ZCQyxtQkFBbUJGLFVBQVVHLGNBQWMsRUFBRUgsVUFBVUksT0FBTzt3QkFDaEUsT0FBTzs0QkFDTCxNQUFNLElBQUloQixNQUFNO3dCQUNsQjtvQkFDRixFQUFFLE9BQU9pQixLQUFVOzRCQUlMQSxlQUNGQSxnQkFNTkEsZ0JBRU9BLGdCQUVBQSxvQkFBQUE7d0JBZFhyQyxRQUFRMUIsS0FBSyxDQUFDLGlDQUFpQzs0QkFDN0NBLE9BQU8rRDs0QkFDUGxCLFNBQVNrQixJQUFJbEIsT0FBTzs0QkFDcEJGLFFBQVEsR0FBRW9CLGdCQUFBQSxJQUFJcEIsUUFBUSxjQUFab0Isb0NBQUFBLGNBQWNuQixJQUFJOzRCQUM1QkgsTUFBTSxHQUFFc0IsaUJBQUFBLElBQUlwQixRQUFRLGNBQVpvQixxQ0FBQUEsZUFBY3RCLE1BQU07NEJBQzVCdUIsT0FBT0QsSUFBSUMsS0FBSzt3QkFDbEI7d0JBRUEsSUFBSUMsZUFBZTt3QkFFbkIsSUFBSUYsRUFBQUEsaUJBQUFBLElBQUlwQixRQUFRLGNBQVpvQixxQ0FBQUEsZUFBY3RCLE1BQU0sTUFBSyxLQUFLOzRCQUNoQ3dCLGVBQWU7d0JBQ2pCLE9BQU8sSUFBSUYsRUFBQUEsaUJBQUFBLElBQUlwQixRQUFRLGNBQVpvQixxQ0FBQUEsZUFBY3RCLE1BQU0sTUFBSyxLQUFLOzRCQUN2Q3dCLGVBQWU7d0JBQ2pCLE9BQU8sS0FBSUYsaUJBQUFBLElBQUlwQixRQUFRLGNBQVpvQixzQ0FBQUEscUJBQUFBLGVBQWNuQixJQUFJLGNBQWxCbUIseUNBQUFBLG1CQUFvQi9ELEtBQUssRUFBRTs0QkFDcENpRSxlQUFlRixJQUFJcEIsUUFBUSxDQUFDQyxJQUFJLENBQUM1QyxLQUFLO3dCQUN4QyxPQUFPLElBQUkrRCxJQUFJbEIsT0FBTyxFQUFFOzRCQUN0Qm9CLGVBQWVGLElBQUlsQixPQUFPO3dCQUM1Qjt3QkFFQTVDLFNBQVNnRTtvQkFDWCxTQUFVO3dCQUNSbEUsV0FBVztvQkFDYjtnQkFDRjs7WUFFQSxJQUFJTCxXQUFXWSxVQUFVO2dCQUN2Qm1CO1lBQ0Y7UUFDRjtrQ0FBRztRQUFDL0I7UUFBU1k7S0FBUztJQUV0Qiw0Q0FBNEM7SUFDNUMsU0FBUzREO1FBQ1AsTUFBTXRDLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQyxZQUFZRCxhQUFhQyxPQUFPLENBQUMsZ0JBQWdCRCxhQUFhQyxPQUFPLENBQUM7UUFDekcsSUFBSSxDQUFDRixPQUFPLE9BQU87UUFDbkIsSUFBSTtZQUNGLE1BQU11QyxVQUFVQyxLQUFLQyxLQUFLLENBQUNDLEtBQUsxQyxNQUFNMkMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ25ELE9BQU9KLFFBQVFMLE9BQU8sSUFBSUssUUFBUUssR0FBRyxJQUFJO1FBQzNDLEVBQUUsT0FBT0MsR0FBRztZQUNWLE9BQU87UUFDVDtJQUNGO0lBRUEsOEJBQThCO0lBQzlCckcsZ0RBQVNBO21DQUFDO1lBQ1IsSUFBSSxDQUFDd0IsU0FBUyxDQUFDQSxNQUFNd0QsUUFBUSxFQUFFO1lBQy9CLE1BQU1zQixTQUFTUjtZQUNmLElBQUksQ0FBQ1EsUUFBUTtZQUNickYsbUZBQXVCQSxDQUFDO2dCQUFFeUUsU0FBU1k7Z0JBQVFDLFlBQVkvRSxNQUFNd0QsUUFBUTtZQUFDLEdBQ25Fd0IsSUFBSTsyQ0FBQ0MsQ0FBQUEsTUFBT3pELFdBQVd5RCxJQUFJakMsSUFBSSxDQUFDa0MsUUFBUTswQ0FDeENDLEtBQUs7MkNBQUMsSUFBTTNELFdBQVc7O1FBQzVCO2tDQUFHO1FBQUN4QjtLQUFNO0lBRVYsbUNBQW1DO0lBQ25DLE1BQU1nRSxxQkFBcUIsT0FBT29CLFVBQW1CTjtRQUNuRCxJQUFJO1lBQ0YsTUFBTTlDLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUVuQyxJQUFJLENBQUNGLE9BQU87WUFFWixrRUFBa0U7WUFDbEUsTUFBTXFELGFBQWFELFdBQ2YsYUFBMEMsT0FBN0JFLG1CQUFtQkYsV0FBVSxjQUMxQ04sU0FDRSxZQUF1QyxPQUEzQlEsbUJBQW1CUixTQUFRLGNBQ3ZDO1lBRU4sTUFBTS9CLFdBQVcsTUFBTXBFLDZDQUFLQSxDQUFDK0QsR0FBRyxDQUFDLGNBQXlCLE9BQVgyQyxhQUFjO2dCQUMzRDFDLFNBQVM7b0JBQ1BDLGVBQWUsVUFBZ0IsT0FBTlo7Z0JBQzNCO1lBQ0Y7WUFFQSxJQUFJZSxTQUFTQyxJQUFJLElBQUlELFNBQVNDLElBQUksQ0FBQ0ssTUFBTSxFQUFFO2dCQUN6QywrQkFBK0I7Z0JBQy9CLE1BQU1rQyxXQUFXeEMsU0FBU0MsSUFBSSxDQUFDSyxNQUFNLENBQUNtQyxNQUFNLENBQzFDLENBQUNqQyxJQUFXQSxFQUFFQyxRQUFRLEtBQUsxRDtnQkFFN0JtQixpQkFBaUJzRSxTQUFTRSxLQUFLLENBQUMsR0FBRyxLQUFLLDRCQUE0QjtZQUN0RTtRQUNGLEVBQUUsT0FBT3RCLEtBQUs7WUFDWnJDLFFBQVExQixLQUFLLENBQUMsa0NBQWtDK0Q7UUFDbEQ7SUFDRjtJQUVBLGlDQUFpQztJQUNqQyxNQUFNdUIsbUJBQW1CO1FBQ3ZCLElBQUl0RSxVQUFVLFFBQVEsMEJBQTBCO1FBRWhELElBQUk7WUFDRixNQUFNWSxRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFFbkMsSUFBSSxDQUFDRixPQUFPO2dCQUNWRixRQUFRUSxJQUFJLENBQUM7Z0JBQ2I7WUFDRjtZQUVBakIsWUFBWTtZQUVaLDJCQUEyQjtZQUMzQixNQUFNc0UsZ0JBQWdCLENBQUMvRTtZQUN2QkMsV0FBVzhFO1lBQ1g1RSxhQUFhNkUsQ0FBQUEsT0FBUUQsZ0JBQWdCQyxPQUFPLElBQUlBLE9BQU87WUFFdkQsa0NBQWtDO1lBQ2xDLE1BQU1DLFdBQVdGLGdCQUFnQixjQUFjO1lBQy9DLE1BQU1oSCw2Q0FBS0EsQ0FBQ21ILElBQUksQ0FBQ0QsVUFBVTtnQkFDekJkLFlBQVlqRjtnQkFDWmlHLGNBQWM7WUFDaEIsR0FBRztnQkFDRHBELFNBQVM7b0JBQ1BDLGVBQWUsVUFBZ0IsT0FBTlo7Z0JBQzNCO1lBQ0Y7UUFDRixFQUFFLE9BQU9tQyxLQUFLO1lBQ1pyQyxRQUFRMUIsS0FBSyxDQUFDLHdCQUF3QitEO1lBQ3RDLG9DQUFvQztZQUNwQ3RELFdBQVcsQ0FBQ0Q7WUFDWkcsYUFBYTZFLENBQUFBLE9BQVFoRixVQUFVZ0YsT0FBTyxJQUFJQSxPQUFPO1FBQ25ELFNBQVU7WUFDUnZFLFlBQVk7UUFDZDtJQUNGO0lBRUEscUNBQXFDO0lBQ3JDLE1BQU0yRSxtQkFBbUI7UUFDdkI3RSxnQkFBZ0IsQ0FBQ0Q7SUFDbkI7SUFFQSw0QkFBNEI7SUFDNUIsTUFBTStFLFNBQVM7UUFDYnBHLE9BQU9xRyxJQUFJO0lBQ2I7SUFFQSxzREFBc0Q7SUFDdEQsTUFBTUMsaUJBQWlCLENBQUNDO1FBQ3RCLElBQUksQ0FBQ0EsWUFBWUEsU0FBU0MsSUFBSSxPQUFPLElBQUk7WUFDdkN2RSxRQUFRUSxJQUFJLENBQUM7WUFDYixPQUFPO1FBQ1Q7UUFDQSxPQUFPOEQ7SUFDVDtJQUVBLE1BQU1FLG1CQUFtQjtRQUN2QixJQUFJLENBQUN0RyxTQUFTLENBQUNBLE1BQU13RCxRQUFRLEVBQUU7UUFDL0IsTUFBTXNCLFNBQVNSO1FBQ2YsSUFBSSxDQUFDUSxRQUFRO1FBQ2JwRCxZQUFZO1FBQ1osSUFBSTtZQUNGLElBQUlILFNBQVM7Z0JBQ1gsTUFBTS9CLHNGQUEwQkEsQ0FBQztvQkFBRTBFLFNBQVNZO29CQUFRQyxZQUFZL0UsTUFBTXdELFFBQVE7Z0JBQUM7Z0JBQy9FaEMsV0FBVztnQkFDWEksU0FBUztZQUNYLE9BQU87Z0JBQ0wsTUFBTXJDLGtGQUFzQkEsQ0FBQztvQkFBRTJFLFNBQVNZO29CQUFRQyxZQUFZL0UsTUFBTXdELFFBQVE7b0JBQUV1QyxjQUFjO2dCQUFRO2dCQUNsR3ZFLFdBQVc7Z0JBQ1hJLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBT2lELEdBQUc7WUFDVmpELFNBQVM7WUFDVEUsUUFBUTFCLEtBQUssQ0FBQyx5QkFBeUJ5RTtRQUN6QyxTQUFVO1lBQ1JuRCxZQUFZO1lBQ1o2RSxXQUFXLElBQU0zRSxTQUFTLE9BQU87UUFDbkM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDNEU7UUFDQ0MsV0FDRS9GLFdBQVcsK0NBQStDOzswQkFJNUQsOERBQUM1QiwrRUFBYUE7Ozs7OzBCQUVkLDhEQUFDMEg7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDMUgsZ0ZBQWNBO3dCQUNiMkgsVUFBVXBHO3dCQUNWcUcsVUFBVSxJQUFNcEcsbUJBQW1CO3dCQUNuQ3FHLFlBQVksSUFBTXJHLG1CQUFtQjs7Ozs7O2tDQUl2Qyw4REFBQ3NHO3dCQUNDSixXQUFXLGtDQUVWLE9BRENuRyxrQkFBa0IsYUFBYTt3QkFFakN3RyxPQUFPOzRCQUNMQyxXQUFXOzRCQUNYQyxZQUFZOzRCQUNaQyxXQUFXOzRCQUNYQyxlQUFlOzRCQUNmQyxXQUFXOzRCQUNYQyxXQUFXOzRCQUNYQyxhQUFhN0csdUJBQXVCLFVBQVU7NEJBQzlDOEcsY0FBYzs0QkFDZEMsYUFBYTt3QkFDZjtrQ0FHQSw0RUFBQ2Y7NEJBQUlDLFdBQVU7O2dDQUVadkcseUJBQ0MsOERBQUNzRztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs7Ozs7c0RBQ2YsOERBQUNlOzRDQUFLZixXQUFVO3NEQUFnQjs7Ozs7Ozs7Ozs7O2dDQUtuQ3JHLFNBQVMsQ0FBQ0YseUJBQ1QsOERBQUNzRztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUFxQnJHOzs7Ozs7c0RBQ3BDLDhEQUFDcUg7NENBQ0NDLFNBQVN6Qjs0Q0FDVFEsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7O2dDQU9KLENBQUN2RyxXQUFXLENBQUNFLFNBQVNKLHVCQUNyQiw4REFBQ3dHO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNnQjtnREFDQ0MsU0FBU3pCO2dEQUNUUSxXQUFVOztrRUFFViw4REFBQ2tCO3dEQUNDQyxPQUFNO3dEQUNOQyxPQUFNO3dEQUNOQyxRQUFPO3dEQUNQQyxTQUFRO3dEQUNSQyxNQUFLO3dEQUNMQyxRQUFPO3dEQUNQQyxhQUFZO3dEQUNaQyxlQUFjO3dEQUNkQyxnQkFBZTt3REFDZjNCLFdBQVU7a0VBRVYsNEVBQUM0Qjs0REFBU0MsUUFBTzs7Ozs7Ozs7Ozs7b0RBQ2I7Ozs7Ozs7Ozs7OztzREFNViw4REFBQzlCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDK0I7NERBQUc5QixXQUFVO3NFQUEwQnpHLE1BQU13SSxVQUFVOzs7Ozs7d0RBQ3ZEeEksTUFBTXlJLFVBQVUsa0JBQ2YsOERBQUNsRjs0REFBRWtELFdBQVU7c0VBQ1YsSUFBSWlDLEtBQUsxSSxNQUFNeUksVUFBVSxFQUFFRSxrQkFBa0IsQ0FBQyxTQUFTO2dFQUN0REMsTUFBTTtnRUFDTkMsT0FBTztnRUFDUEMsS0FBSzs0REFDUDs7Ozs7Ozs7Ozs7OzhEQUlOLDhEQUFDdEM7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDdkgsNEVBQVVBOzREQUNUNkosVUFBVS9JLE1BQU0rSSxRQUFRLElBQUk7NERBQzVCQyxNQUFLOzREQUNMQyxrQkFBa0I7Ozs7OztzRUFFcEIsOERBQUN6Qjs0REFBS2YsV0FBVTtzRUFDYnpHLE1BQU0rSSxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTXJCLDhEQUFDdkM7NENBQ0MwQyxLQUFLNUg7NENBQ0xtRixXQUFXLFlBSVYsT0FIQ3ZGLGVBQ0ksaUVBQ0E7OzhEQUdOLDhEQUFDc0Y7b0RBQ0NDLFdBQVcsWUFJVixPQUhDdkYsZUFDSSxrQkFDQTtvREFFTjRGLE9BQU87d0RBQ0xnQixRQUFRNUcsZUFBZSxVQUFVO3dEQUNqQ2lJLFdBQVdqSSxlQUFlLFVBQVU7b0RBQ3RDOzhEQUVBLDRFQUFDdEMsa0RBQUtBO3dEQUNKd0ssS0FBS2pELGVBQWVuRyxNQUFNcUosU0FBUzt3REFDbkNDLEtBQUt0SixNQUFNd0ksVUFBVSxJQUFJO3dEQUN6QlIsSUFBSTt3REFDSnVCLE9BQU07d0RBQ045QyxXQUFVO3dEQUNWK0MsUUFBUTt3REFDUkMsYUFBYTt3REFDYkMsU0FBUyxDQUFDN0U7NERBQ1IvQyxRQUFRMUIsS0FBSyxDQUFDLHlCQUF5QyxPQUFoQkosTUFBTXFKLFNBQVM7NERBQ3RELE1BQU1NLGFBQWE5RSxFQUFFK0UsTUFBTTs0REFDM0JELFdBQVdQLEdBQUcsR0FBRzt3REFDbkI7Ozs7Ozs7Ozs7OzhEQUtKLDhEQUFDM0I7b0RBQ0NDLFNBQVMxQjtvREFDVFMsV0FBVTtvREFDVm9ELGNBQVkzSSxlQUFlLG9CQUFvQjs4REFFOUNBLDZCQUNDLDhEQUFDeUc7d0RBQ0NDLE9BQU07d0RBQ05DLE9BQU07d0RBQ05DLFFBQU87d0RBQ1BDLFNBQVE7d0RBQ1JDLE1BQUs7d0RBQ0xDLFFBQU87d0RBQ1BDLGFBQVk7d0RBQ1pDLGVBQWM7d0RBQ2RDLGdCQUFlOzswRUFFZiw4REFBQ0M7Z0VBQVNDLFFBQU87Ozs7OzswRUFDakIsOERBQUNEO2dFQUFTQyxRQUFPOzs7Ozs7MEVBQ2pCLDhEQUFDd0I7Z0VBQUtDLElBQUc7Z0VBQUtDLElBQUc7Z0VBQUtDLElBQUc7Z0VBQUtDLElBQUc7Ozs7OzswRUFDakMsOERBQUNKO2dFQUFLQyxJQUFHO2dFQUFJQyxJQUFHO2dFQUFLQyxJQUFHO2dFQUFLQyxJQUFHOzs7Ozs7Ozs7Ozs2RUFHbEMsOERBQUN2Qzt3REFDQ0MsT0FBTTt3REFDTkMsT0FBTTt3REFDTkMsUUFBTzt3REFDUEMsU0FBUTt3REFDUkMsTUFBSzt3REFDTEMsUUFBTzt3REFDUEMsYUFBWTt3REFDWkMsZUFBYzt3REFDZEMsZ0JBQWU7OzBFQUVmLDhEQUFDQztnRUFBU0MsUUFBTzs7Ozs7OzBFQUNqQiw4REFBQ0Q7Z0VBQVNDLFFBQU87Ozs7OzswRUFDakIsOERBQUN3QjtnRUFBS0MsSUFBRztnRUFBS0MsSUFBRztnRUFBSUMsSUFBRztnRUFBS0MsSUFBRzs7Ozs7OzBFQUNoQyw4REFBQ0o7Z0VBQUtDLElBQUc7Z0VBQUlDLElBQUc7Z0VBQUtDLElBQUc7Z0VBQUtDLElBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O2dEQU1yQ2hKLDhCQUNDLDhEQUFDdUc7b0RBQ0NDLFNBQVMxQjtvREFDVFMsV0FBVTtvREFDVm9ELGNBQVc7OERBRVgsNEVBQUNsQzt3REFDQ0MsT0FBTTt3REFDTkMsT0FBTTt3REFDTkMsUUFBTzt3REFDUEMsU0FBUTt3REFDUkMsTUFBSzt3REFDTEMsUUFBTzt3REFDUEMsYUFBWTt3REFDWkMsZUFBYzt3REFDZEMsZ0JBQWU7OzBFQUVmLDhEQUFDMEI7Z0VBQUtDLElBQUc7Z0VBQUtDLElBQUc7Z0VBQUlDLElBQUc7Z0VBQUlDLElBQUc7Ozs7OzswRUFDL0IsOERBQUNKO2dFQUFLQyxJQUFHO2dFQUFJQyxJQUFHO2dFQUFJQyxJQUFHO2dFQUFLQyxJQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FTMUMsQ0FBQ2hLLFdBQVcsQ0FBQ0UsU0FBU0osU0FBUyxDQUFDa0IsOEJBQy9CLDhEQUFDc0Y7b0NBQUlDLFdBQVU7O3NEQUViLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2tCOzREQUNDQyxPQUFNOzREQUNOQyxPQUFNOzREQUNOQyxRQUFPOzREQUNQQyxTQUFROzREQUNSQyxNQUFLOzREQUNMQyxRQUFPOzREQUNQQyxhQUFZOzREQUNaQyxlQUFjOzREQUNkQyxnQkFBZTs7OEVBRWYsOERBQUMrQjtvRUFBS0MsR0FBRTs7Ozs7OzhFQUNSLDhEQUFDQztvRUFBT0MsSUFBRztvRUFBS0MsSUFBRztvRUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7O3NFQUU1Qiw4REFBQ2hEOztnRUFBTXhILE1BQU15SyxXQUFXLElBQUk7Z0VBQUU7Ozs7Ozs7Ozs7Ozs7OERBR2hDLDhEQUFDaEQ7b0RBQ0NoQixXQUFXLDhDQUE4RixPQUFoRHJGLFdBQVcsa0NBQWtDO29EQUN0R3NHLFNBQVNoQztvREFDVGdGLFVBQVV0Sjs7c0VBRVYsOERBQUN1Rzs0REFDQ0MsT0FBTTs0REFDTkMsT0FBTTs0REFDTkMsUUFBTzs0REFDUEMsU0FBUTs0REFDUkMsTUFBTXBILFVBQVUsaUJBQWlCOzREQUNqQ3FILFFBQU87NERBQ1BDLGFBQVk7NERBQ1pDLGVBQWM7NERBQ2RDLGdCQUFlOzREQUNmM0IsV0FBVzdGLFVBQVUsaUJBQWlCO3NFQUV0Qyw0RUFBQ3VKO2dFQUFLQyxHQUFFOzs7Ozs7Ozs7OztzRUFFViw4REFBQzVDOztnRUFBTTFHO2dFQUFVOzs7Ozs7Ozs7Ozs7OzhEQUluQiw4REFBQzJHO29EQUNDaEIsV0FBVyxpREFBa0YsT0FBakNsRixVQUFVLG9CQUFvQjtvREFDMUZvSixPQUFPcEosVUFBVSw0QkFBNEI7b0RBQzdDbUcsU0FBU3BCO29EQUNUb0UsVUFBVWpKOztzRUFFViw4REFBQy9CLHFGQUFRQTs0REFBQytHLFdBQVcsV0FBeUQsT0FBOUNsRixVQUFVLG9CQUFvQjs0REFBbUJ5RyxNQUFNekcsVUFBVSxZQUFZOzs7Ozs7c0VBQzdHLDhEQUFDaUc7c0VBQU1qRyxVQUFVLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FLOUJ2QixNQUFNNEssaUJBQWlCLElBQUk1SyxNQUFNNEssaUJBQWlCLENBQUN2RSxJQUFJLG9CQUN0RCw4REFBQ0c7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDb0U7b0RBQUdwRSxXQUFVOzhEQUEyQjs7Ozs7OzhEQUN6Qyw4REFBQ2xEO29EQUFFa0QsV0FBVTs4REFBaUJ6RyxNQUFNNEssaUJBQWlCOzs7Ozs7Ozs7Ozs7d0NBS3hENUssTUFBTThLLFVBQVUsSUFBSTlLLE1BQU04SyxVQUFVLENBQUMxSSxNQUFNLEdBQUcsbUJBQzdDLDhEQUFDb0U7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDb0U7b0RBQUdwRSxXQUFVOzhEQUEyQjs7Ozs7OzhEQUN6Qyw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ1p6RyxNQUFNOEssVUFBVSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsS0FBS0Msc0JBQzFCLDhEQUFDekQ7NERBRUNmLFdBQVU7c0VBRVR1RTsyREFISUM7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBV2RqTCxNQUFNMEQsUUFBUSxJQUFJMUQsTUFBTTBELFFBQVEsQ0FBQ3RCLE1BQU0sR0FBRyxtQkFDekMsOERBQUNvRTs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNvRTtvREFBR3BFLFdBQVU7OERBQTJCOzs7Ozs7OERBQ3pDLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWnpHLE1BQU0wRCxRQUFRLENBQUNxSCxHQUFHLENBQUMsQ0FBQ0csd0JBQ25CLDhEQUFDMUU7NERBQTZCQyxXQUFVOzs4RUFDdEMsOERBQUN2SCw0RUFBVUE7b0VBQ1Q2SixVQUFVbUMsUUFBUW5DLFFBQVE7b0VBQzFCQyxNQUFLO29FQUNMQyxrQkFBa0I7Ozs7Ozs4RUFFcEIsOERBQUN6Qzs7c0ZBQ0MsOERBQUNBOzRFQUFJQyxXQUFVO3NGQUF1QnlFLFFBQVFuQyxRQUFROzs7Ozs7c0ZBQ3RELDhEQUFDdkM7NEVBQUlDLFdBQVU7c0ZBQWlCeUUsUUFBUUMsWUFBWTs7Ozs7O3NGQUNwRCw4REFBQzNFOzRFQUFJQyxXQUFVO3NGQUNaLElBQUlpQyxLQUFLd0MsUUFBUXpDLFVBQVUsRUFBRUUsa0JBQWtCOzs7Ozs7Ozs7Ozs7OzJEQVY1Q3VDLFFBQVFFLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBb0JuQ3BLLGNBQWNvQixNQUFNLEdBQUcsbUJBQ3RCLDhEQUFDb0U7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDb0U7b0RBQUdwRSxXQUFVOzhEQUEyQjs7Ozs7OzhEQUN6Qyw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ1p6RixjQUFjK0osR0FBRyxDQUFDLENBQUNNLGNBQWNKLHNCQUNoQyw4REFBQ3pFOzREQUVDQyxXQUFVOzREQUNWSyxPQUFPO2dFQUFFZ0IsUUFBUTs0REFBUTs0REFDekJKLFNBQVM7Z0VBQ1A0RCxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRyxnQkFBc0MsT0FBdEJILGFBQWE3SCxRQUFROzREQUM5RDs7OEVBRUEsOERBQUNnRDtvRUFBSUMsV0FBVTs4RUFDYiw0RUFBQzdILGtEQUFLQTt3RUFDSndLLEtBQUtpQyxhQUFhaEMsU0FBUyxJQUFJO3dFQUMvQkMsS0FBSytCLGFBQWE3QyxVQUFVLElBQUk7d0VBQ2hDUixJQUFJO3dFQUNKdUIsT0FBTTt3RUFDTjlDLFdBQVU7d0VBQ1ZnRCxhQUFhO3dFQUNiQyxTQUFTLENBQUM3RTs0RUFDUixNQUFNOEUsYUFBYTlFLEVBQUUrRSxNQUFNOzRFQUMzQixJQUFJRCxZQUFZO2dGQUNkQSxXQUFXUCxHQUFHLEdBQUc7NEVBQ25CO3dFQUNGOzs7Ozs7Ozs7Ozs4RUFHSiw4REFBQzVDO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDRDt3RUFBSUMsV0FBVTtrRkFBZ0M0RSxhQUFhN0MsVUFBVTs7Ozs7Ozs7Ozs7OzJEQXhCbkUsR0FBNEJ5QyxPQUF6QkksYUFBYTdILFFBQVEsRUFBQyxLQUFTLE9BQU55SDs7Ozs7Ozs7Ozs7Ozs7OztzREFpQzNDLDhEQUFDekU7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDb0U7b0RBQUdwRSxXQUFVOzhEQUEyQjs7Ozs7OzhEQUN6Qyw4REFBQ3JILHNFQUFrQkE7b0RBQUNXLElBQUc7b0RBQXNCa0wsT0FBTztvREFBR1EsWUFBVzs4REFDL0Q7NERBQUMsRUFBRUMsVUFBVSxFQUFFOzZFQUNkLDhEQUFDbEY7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUN0SCx3RUFBTUE7Z0VBQUN1TSxZQUFZQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FVcEMsOERBQUN6TSw4RUFBWUE7d0JBQ1h5SCxVQUFVbEc7d0JBQ1ZtRyxVQUFVLElBQU1sRyx3QkFBd0I7d0JBQ3hDbUcsWUFBWSxJQUFNbkcsd0JBQXdCOzs7Ozs7Ozs7Ozs7MEJBSTlDLDhEQUFDekIsa0ZBQWdCQTs7Ozs7WUFHaEIyQyx1QkFDQyw4REFBQzZFO2dCQUFJTSxPQUFPO29CQUFFNkUsVUFBVTtvQkFBU0MsUUFBUTtvQkFBSUMsT0FBTztvQkFBSUMsUUFBUTtnQkFBSztnQkFBR3JGLFdBQVU7MEJBQy9FOUU7Ozs7Ozs7Ozs7OztBQUtYO0dBM3RCd0JoQzs7UUFDUGpCLHNEQUFTQTtRQUNURyxzREFBU0E7OztLQUZGYztBQTZ0QnhCLFNBQVNOLE9BQVUwTSxZQUFzQjtJQUN2QyxPQUFPek0sNkNBQVdBLENBQUl5TTtBQUN4QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaXZhc1xcT25lRHJpdmVcXERlc2t0b3BcXGZpbmFsXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxob21lXFxwaG90b3NcXFtpZF1cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZVBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbmltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XHJcbmltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcbmltcG9ydCB7XHJcbiAgVG9wTmF2aWdhdGlvbixcclxuICBTaWRlTmF2aWdhdGlvbixcclxuICBNb2JpbGVOYXZpZ2F0aW9uLFxyXG4gIFJpZ2h0U2lkZWJhcixcclxufSBmcm9tIFwiLi4vLi4vLi4vLi4vY29tcG9uZW50cy9Ib21lRGFzaGJvYXJkL05hdmlnYXRpb25cIjtcclxuaW1wb3J0IFVzZXJBdmF0YXIgZnJvbSBcIi4uLy4uLy4uLy4uL2NvbXBvbmVudHMvSG9tZURhc2hib2FyZC9Vc2VyQXZhdGFyXCI7XHJcbmltcG9ydCBQaG90b3MgZnJvbSBcIi4uLy4uLy4uLy4uL2NvbXBvbmVudHMvSG9tZURhc2hib2FyZC9QaG90b3NcIjtcclxuaW1wb3J0IERldGFpbFBhZ2VMYXp5TG9hZCBmcm9tIFwiLi4vLi4vLi4vLi4vY29tcG9uZW50cy9EZXRhaWxQYWdlTGF6eUxvYWRcIjtcclxuaW1wb3J0IHsgdXNlUmVmIGFzIHJlYWN0VXNlUmVmIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBzYXZlQ29udGVudFRvTXlXZWRkaW5nLCByZW1vdmVDb250ZW50RnJvbU15V2VkZGluZywgY2hlY2tDb250ZW50U2F2ZWRTdGF0dXMgfSBmcm9tICcuLi8uLi8uLi8uLi9zZXJ2aWNlcy9teVdlZGRpbmdTZXJ2aWNlJztcclxuaW1wb3J0IHsgQm9va21hcmsgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xyXG5cclxuLy8gRGVmaW5lIGludGVyZmFjZSBmb3IgcGhvdG8gZGV0YWlscyAtIFVwZGF0ZWQgdG8gbWF0Y2ggYmFja2VuZCByZXNwb25zZVxyXG5pbnRlcmZhY2UgUGhvdG9EZXRhaWxzIHtcclxuICBwaG90b19pZDogc3RyaW5nO1xyXG4gIHBob3RvX25hbWU6IHN0cmluZztcclxuICBwaG90b191cmw6IHN0cmluZztcclxuICBwaG90b19kZXNjcmlwdGlvbj86IHN0cmluZztcclxuICBwaG90b190YWdzPzogc3RyaW5nW107XHJcbiAgcGhvdG9fY2F0ZWdvcnk/OiBzdHJpbmc7XHJcbiAgY3JlYXRlZF9hdD86IHN0cmluZztcclxuICB1c2VyX2lkPzogc3RyaW5nO1xyXG4gIHVzZXJuYW1lPzogc3RyaW5nO1xyXG4gIHBob3RvX3ZpZXdzPzogbnVtYmVyO1xyXG4gIHBob3RvX2xpa2VzPzogbnVtYmVyO1xyXG4gIHBob3RvX2NvbW1lbnRzPzogbnVtYmVyO1xyXG4gIGxpa2VkPzogYm9vbGVhbjtcclxuICBpc19vd25fY29udGVudD86IGJvb2xlYW47XHJcbiAgY29tbWVudHM/OiBQaG90b0NvbW1lbnRbXTtcclxufVxyXG5cclxuaW50ZXJmYWNlIFBob3RvQ29tbWVudCB7XHJcbiAgY29tbWVudF9pZDogc3RyaW5nO1xyXG4gIHVzZXJfaWQ6IHN0cmluZztcclxuICB1c2VybmFtZTogc3RyaW5nO1xyXG4gIGNvbW1lbnRfdGV4dDogc3RyaW5nO1xyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcclxufVxyXG5cclxuLy8gTWFpbiBjb21wb25lbnQgZm9yIHRoZSBwaG90byB2aWV3aW5nIHBhZ2VcclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGhvdG9WaWV3UGFnZSgpIHtcclxuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCBwaG90b0lkID0gcGFyYW1zPy5pZCBhcyBzdHJpbmc7XHJcblxyXG4gIGNvbnN0IFtwaG90bywgc2V0UGhvdG9dID0gdXNlU3RhdGU8UGhvdG9EZXRhaWxzIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGU8Ym9vbGVhbj4odHJ1ZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbc2lkZWJhckV4cGFuZGVkLCBzZXRTaWRlYmFyRXhwYW5kZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtyaWdodFNpZGViYXJFeHBhbmRlZCwgc2V0UmlnaHRTaWRlYmFyRXhwYW5kZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc0NsaWVudCwgc2V0SXNDbGllbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc0xpa2VkLCBzZXRJc0xpa2VkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbbGlrZUNvdW50LCBzZXRMaWtlQ291bnRdID0gdXNlU3RhdGUoMCk7XHJcbiAgY29uc3QgW3JlbGF0ZWRQaG90b3MsIHNldFJlbGF0ZWRQaG90b3NdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuICBjb25zdCBbaXNGdWxsc2NyZWVuLCBzZXRJc0Z1bGxzY3JlZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc0xpa2luZywgc2V0SXNMaWtpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IGltYWdlUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBbaXNTYXZlZCwgc2V0SXNTYXZlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzU2F2aW5nLCBzZXRJc1NhdmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3RvYXN0LCBzZXRUb2FzdF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gU2V0IGNsaWVudC1zaWRlIHJlbmRlcmluZyBmbGFnXHJcbiAgdXNlRWZmZWN0KCgpID0+IHNldElzQ2xpZW50KHRydWUpLCBbXSk7XHJcblxyXG4gIC8vIEZldGNoIHBob3RvIGRldGFpbHMgd2hlbiBjb21wb25lbnQgbW91bnRzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGZldGNoUGhvdG9EZXRhaWxzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZygnPT09IFN0YXJ0aW5nIHBob3RvIGZldGNoID09PScpO1xyXG4gICAgICBjb25zb2xlLmxvZygnUGhvdG8gSUQ6JywgcGhvdG9JZCk7XHJcblxyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgICBzZXRFcnJvcihudWxsKTtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8gR2V0IHRva2VuIGZyb20gbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnand0X3Rva2VuJykgfHxcclxuICAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dlZHphdF90b2tlbicpO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygnVG9rZW4gZm91bmQ6JywgdG9rZW4gPyAnWWVzJyA6ICdObycpO1xyXG4gICAgICAgIGlmICh0b2tlbikge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1Rva2VuIHByZXZpZXc6JywgdG9rZW4uc3Vic3RyaW5nKDAsIDUwKSArICcuLi4nKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiBsZW5ndGg6JywgdG9rZW4ubGVuZ3RoKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiBzdGFydHMgd2l0aCBleUogKEpXVCk6JywgdG9rZW4uc3RhcnRzV2l0aCgnZXlKJykpO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0Z1bGwgdG9rZW46JywgdG9rZW4pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgICAgY29uc29sZS53YXJuKCdObyBhdXRoZW50aWNhdGlvbiB0b2tlbiBmb3VuZCcpO1xyXG4gICAgICAgICAgc2V0RXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkLiBQbGVhc2UgbG9nIGluLicpO1xyXG4gICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBTdG9yZSB0b2tlbiBpbiBhIGNvb2tpZSBmb3Igc2VydmVyIGNvbXBvbmVudHMgdG8gYWNjZXNzXHJcbiAgICAgICAgZG9jdW1lbnQuY29va2llID0gYHRva2VuPSR7dG9rZW59OyBwYXRoPS87IG1heC1hZ2U9MzYwMDsgU2FtZVNpdGU9U3RyaWN0YDtcclxuXHJcbiAgICAgICAgLy8gRmlyc3QgdGVzdCBpZiB0aGUgcGhvdG9zIGxpc3QgQVBJIGlzIHdvcmtpbmdcclxuICAgICAgICBjb25zb2xlLmxvZygnVGVzdGluZyBwaG90b3MgbGlzdCBBUEkgZmlyc3QuLi4nKTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgcGhvdG9zVGVzdFJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KCcvYXBpL3Bob3Rvcz9wYWdlPTEmbGltaXQ9MScsIHtcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1Bob3RvcyBsaXN0IEFQSSB0ZXN0IHN1Y2Nlc3NmdWw6JywgcGhvdG9zVGVzdFJlc3BvbnNlLnN0YXR1cyk7XHJcbiAgICAgICAgfSBjYXRjaCAocGhvdG9zRXJyb3I6IGFueSkge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignUGhvdG9zIGxpc3QgQVBJIHRlc3QgZmFpbGVkOicsIHBob3Rvc0Vycm9yLnJlc3BvbnNlPy5kYXRhIHx8IHBob3Rvc0Vycm9yLm1lc3NhZ2UpO1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdQaG90b3MgbGlzdCBBUEkgaXMgYWxzbyBmYWlsaW5nOiAnICsgKHBob3Rvc0Vycm9yLnJlc3BvbnNlPy5kYXRhPy5lcnJvciB8fCBwaG90b3NFcnJvci5tZXNzYWdlKSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBUcnkgdG8gZ2V0IHRoZSBwaG90byBmcm9tIHRoZSBwaG90b3MgbGlzdCBmaXJzdCBhcyBhIHdvcmthcm91bmRcclxuICAgICAgICBjb25zb2xlLmxvZygnVHJ5aW5nIHRvIGdldCBwaG90byBmcm9tIHBob3RvcyBsaXN0Li4uJyk7XHJcbiAgICAgICAgbGV0IHBob3RvRnJvbUxpc3QgPSBudWxsO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCBwaG90b3NSZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldCgnL2FwaS9waG90b3M/cGFnZT0xJmxpbWl0PTUwJywge1xyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgaWYgKHBob3Rvc1Jlc3BvbnNlLmRhdGEgJiYgcGhvdG9zUmVzcG9uc2UuZGF0YS5waG90b3MpIHtcclxuICAgICAgICAgICAgcGhvdG9Gcm9tTGlzdCA9IHBob3Rvc1Jlc3BvbnNlLmRhdGEucGhvdG9zLmZpbmQoKHA6IGFueSkgPT4gcC5waG90b19pZCA9PT0gcGhvdG9JZCk7XHJcbiAgICAgICAgICAgIGlmIChwaG90b0Zyb21MaXN0KSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0ZvdW5kIHBob3RvIGluIHBob3RvcyBsaXN0OicsIHBob3RvRnJvbUxpc3QpO1xyXG4gICAgICAgICAgICAgIC8vIEFkZCBzb21lIGRlZmF1bHQgdmFsdWVzIHRoYXQgbWlnaHQgYmUgbWlzc2luZ1xyXG4gICAgICAgICAgICAgIHBob3RvRnJvbUxpc3QubGlrZWQgPSBmYWxzZTtcclxuICAgICAgICAgICAgICBwaG90b0Zyb21MaXN0LmNvbW1lbnRzID0gW107XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChsaXN0RXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgcGhvdG8gZnJvbSBsaXN0OicsIGxpc3RFcnJvcik7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBUcnkgdGhlIGluZGl2aWR1YWwgcGhvdG8gQVBJIGZpcnN0LCBidXQgZmFsbCBiYWNrIHRvIHBob3RvcyBsaXN0IGlmIGl0IGZhaWxzXHJcbiAgICAgICAgbGV0IHJlc3BvbnNlO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgL2FwaS9waG90by8ke3Bob3RvSWR9YCwge1xyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgLy8gQ2hlY2sgaWYgdGhlIHJlc3BvbnNlIGNvbnRhaW5zIGFuIGVycm9yXHJcbiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YT8uZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGEuZXJyb3IpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGluZGl2aWR1YWxFcnJvcjogYW55KSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdJbmRpdmlkdWFsIHBob3RvIEFQSSBmYWlsZWQ6Jywge1xyXG4gICAgICAgICAgICBlcnJvcjogaW5kaXZpZHVhbEVycm9yLFxyXG4gICAgICAgICAgICByZXNwb25zZTogaW5kaXZpZHVhbEVycm9yLnJlc3BvbnNlPy5kYXRhLFxyXG4gICAgICAgICAgICBtZXNzYWdlOiBpbmRpdmlkdWFsRXJyb3IubWVzc2FnZSxcclxuICAgICAgICAgICAgc3RhdHVzOiBpbmRpdmlkdWFsRXJyb3IucmVzcG9uc2U/LnN0YXR1c1xyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgLy8gSWYgaW5kaXZpZHVhbCBwaG90byBBUEkgZmFpbHMsIHRyeSB0byBnZXQgdGhlIHBob3RvIGZyb20gdGhlIHBob3RvcyBsaXN0XHJcbiAgICAgICAgICBpZiAocGhvdG9Gcm9tTGlzdCkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnVXNpbmcgcGhvdG8gZGF0YSBmcm9tIHBob3RvcyBsaXN0IGFzIGZhbGxiYWNrJyk7XHJcbiAgICAgICAgICAgIHJlc3BvbnNlID0geyBkYXRhOiBwaG90b0Zyb21MaXN0IH07XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAvLyBUcnkgdG8gZmV0Y2ggZnJvbSBwaG90b3MgbGlzdCBBUEkgYXMgYSBsYXN0IHJlc29ydFxyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIHRvIGZldGNoIHBob3RvIGZyb20gcGhvdG9zIGxpc3QgQVBJLi4uJyk7XHJcbiAgICAgICAgICAgICAgY29uc3QgcGhvdG9zUmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoJy9hcGkvcGhvdG9zP3BhZ2U9MSZsaW1pdD01MCcsIHtcclxuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgICAgY29uc3QgZm91bmRQaG90byA9IHBob3Rvc1Jlc3BvbnNlLmRhdGE/LnBob3Rvcz8uZmluZCgocDogYW55KSA9PiBwLnBob3RvX2lkID09PSBwaG90b0lkKTtcclxuICAgICAgICAgICAgICBpZiAoZm91bmRQaG90bykge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0ZvdW5kIHBob3RvIGluIHBob3RvcyBsaXN0LCB1c2luZyBhcyBmYWxsYmFjaycpO1xyXG4gICAgICAgICAgICAgICAgcmVzcG9uc2UgPSB7IGRhdGE6IGZvdW5kUGhvdG8gfTtcclxuICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdQaG90byBub3QgZm91bmQgaW4gcGhvdG9zIGxpc3QnKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gY2F0Y2ggKGxpc3RFcnJvcjogYW55KSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignUGhvdG9zIGxpc3QgQVBJIGFsc28gZmFpbGVkOicsIHtcclxuICAgICAgICAgICAgICAgIGVycm9yOiBsaXN0RXJyb3IsXHJcbiAgICAgICAgICAgICAgICByZXNwb25zZTogbGlzdEVycm9yLnJlc3BvbnNlPy5kYXRhLFxyXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogbGlzdEVycm9yLm1lc3NhZ2UsXHJcbiAgICAgICAgICAgICAgICBzdGF0dXM6IGxpc3RFcnJvci5yZXNwb25zZT8uc3RhdHVzXHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gbG9hZCBwaG90bzogQm90aCBpbmRpdmlkdWFsIHBob3RvIEFQSSBhbmQgcGhvdG9zIGxpc3QgQVBJIGZhaWxlZCcpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJyb3IpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGEuZXJyb3IpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIFRoZSBiYWNrZW5kIHJldHVybnMgdGhlIHBob3RvIGRhdGEgZGlyZWN0bHlcclxuICAgICAgICAgIGNvbnN0IHBob3RvRGF0YSA9IHJlc3BvbnNlLmRhdGE7XHJcblxyXG4gICAgICAgICAgaWYgKCFwaG90b0RhdGEucGhvdG9faWQpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIHBob3RvIGRhdGEgcmVjZWl2ZWQgLSBtaXNzaW5nIHBob3RvX2lkJyk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgc2V0UGhvdG8ocGhvdG9EYXRhKTtcclxuICAgICAgICAgIHNldElzTGlrZWQocGhvdG9EYXRhLmxpa2VkIHx8IGZhbHNlKTtcclxuICAgICAgICAgIHNldExpa2VDb3VudChwaG90b0RhdGEucGhvdG9fbGlrZXMgfHwgMCk7XHJcblxyXG4gICAgICAgICAgLy8gRmV0Y2ggcmVsYXRlZCBwaG90b3NcclxuICAgICAgICAgIGZldGNoUmVsYXRlZFBob3RvcyhwaG90b0RhdGEucGhvdG9fY2F0ZWdvcnksIHBob3RvRGF0YS51c2VyX2lkKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBkYXRhIHJlY2VpdmVkIGZyb20gc2VydmVyJyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHBob3RvIGRldGFpbHM6Jywge1xyXG4gICAgICAgICAgZXJyb3I6IGVycixcclxuICAgICAgICAgIG1lc3NhZ2U6IGVyci5tZXNzYWdlLFxyXG4gICAgICAgICAgcmVzcG9uc2U6IGVyci5yZXNwb25zZT8uZGF0YSxcclxuICAgICAgICAgIHN0YXR1czogZXJyLnJlc3BvbnNlPy5zdGF0dXMsXHJcbiAgICAgICAgICBzdGFjazogZXJyLnN0YWNrXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSAnRmFpbGVkIHRvIGxvYWQgcGhvdG8uIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuJztcclxuXHJcbiAgICAgICAgaWYgKGVyci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgICAgIGVycm9yTWVzc2FnZSA9ICdBdXRoZW50aWNhdGlvbiBmYWlsZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJztcclxuICAgICAgICB9IGVsc2UgaWYgKGVyci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDQpIHtcclxuICAgICAgICAgIGVycm9yTWVzc2FnZSA9ICdQaG90byBub3QgZm91bmQuJztcclxuICAgICAgICB9IGVsc2UgaWYgKGVyci5yZXNwb25zZT8uZGF0YT8uZXJyb3IpIHtcclxuICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVyci5yZXNwb25zZS5kYXRhLmVycm9yO1xyXG4gICAgICAgIH0gZWxzZSBpZiAoZXJyLm1lc3NhZ2UpIHtcclxuICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVyci5tZXNzYWdlO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBpZiAocGhvdG9JZCAmJiBpc0NsaWVudCkge1xyXG4gICAgICBmZXRjaFBob3RvRGV0YWlscygpO1xyXG4gICAgfVxyXG4gIH0sIFtwaG90b0lkLCBpc0NsaWVudF0pO1xyXG5cclxuICAvLyBVdGlsaXR5IHRvIGRlY29kZSBKV1QgYW5kIGV4dHJhY3QgdXNlcl9pZFxyXG4gIGZ1bmN0aW9uIGdldFVzZXJJZEZyb21Ub2tlbigpIHtcclxuICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJykgfHwgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2p3dF90b2tlbicpIHx8IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd3ZWR6YXRfdG9rZW4nKTtcclxuICAgIGlmICghdG9rZW4pIHJldHVybiBudWxsO1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcGF5bG9hZCA9IEpTT04ucGFyc2UoYXRvYih0b2tlbi5zcGxpdCgnLicpWzFdKSk7XHJcbiAgICAgIHJldHVybiBwYXlsb2FkLnVzZXJfaWQgfHwgcGF5bG9hZC5zdWIgfHwgbnVsbDtcclxuICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBDaGVjayBzYXZlZCBzdGF0dXMgb24gbW91bnRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFwaG90byB8fCAhcGhvdG8ucGhvdG9faWQpIHJldHVybjtcclxuICAgIGNvbnN0IHVzZXJJZCA9IGdldFVzZXJJZEZyb21Ub2tlbigpO1xyXG4gICAgaWYgKCF1c2VySWQpIHJldHVybjtcclxuICAgIGNoZWNrQ29udGVudFNhdmVkU3RhdHVzKHsgdXNlcl9pZDogdXNlcklkLCBjb250ZW50X2lkOiBwaG90by5waG90b19pZCB9KVxyXG4gICAgICAudGhlbihyZXMgPT4gc2V0SXNTYXZlZChyZXMuZGF0YS5pc19zYXZlZCkpXHJcbiAgICAgIC5jYXRjaCgoKSA9PiBzZXRJc1NhdmVkKGZhbHNlKSk7XHJcbiAgfSwgW3Bob3RvXSk7XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIGZldGNoIHJlbGF0ZWQgcGhvdG9zXHJcbiAgY29uc3QgZmV0Y2hSZWxhdGVkUGhvdG9zID0gYXN5bmMgKGNhdGVnb3J5Pzogc3RyaW5nLCB1c2VySWQ/OiBzdHJpbmcpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XHJcblxyXG4gICAgICBpZiAoIXRva2VuKSByZXR1cm47XHJcblxyXG4gICAgICAvLyBQcmlvcml0aXplIGNhdGVnb3J5IGlmIGF2YWlsYWJsZSwgb3RoZXJ3aXNlIGZldGNoIHVzZXIncyBwaG90b3NcclxuICAgICAgY29uc3QgcXVlcnlQYXJhbSA9IGNhdGVnb3J5XHJcbiAgICAgICAgPyBgP2NhdGVnb3J5PSR7ZW5jb2RlVVJJQ29tcG9uZW50KGNhdGVnb3J5KX0mbGltaXQ9NmBcclxuICAgICAgICA6IHVzZXJJZFxyXG4gICAgICAgICAgPyBgP3VzZXJfaWQ9JHtlbmNvZGVVUklDb21wb25lbnQodXNlcklkKX0mbGltaXQ9NmBcclxuICAgICAgICAgIDogJz9saW1pdD02JztcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KGAvYXBpL3Bob3RvcyR7cXVlcnlQYXJhbX1gLCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5waG90b3MpIHtcclxuICAgICAgICAvLyBGaWx0ZXIgb3V0IHRoZSBjdXJyZW50IHBob3RvXHJcbiAgICAgICAgY29uc3QgZmlsdGVyZWQgPSByZXNwb25zZS5kYXRhLnBob3Rvcy5maWx0ZXIoXHJcbiAgICAgICAgICAocDogYW55KSA9PiBwLnBob3RvX2lkICE9PSBwaG90b0lkXHJcbiAgICAgICAgKTtcclxuICAgICAgICBzZXRSZWxhdGVkUGhvdG9zKGZpbHRlcmVkLnNsaWNlKDAsIDUpKTsgLy8gTGltaXQgdG8gNSByZWxhdGVkIHBob3Rvc1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcmVsYXRlZCBwaG90b3M6JywgZXJyKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byBoYW5kbGUgbGlrZS91bmxpa2VcclxuICBjb25zdCBoYW5kbGVMaWtlVG9nZ2xlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKGlzTGlraW5nKSByZXR1cm47IC8vIFByZXZlbnQgbXVsdGlwbGUgY2xpY2tzXHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuXHJcbiAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oJ05vIGF1dGhlbnRpY2F0aW9uIHRva2VuIGZvdW5kJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBzZXRJc0xpa2luZyh0cnVlKTtcclxuXHJcbiAgICAgIC8vIE9wdGltaXN0aWNhbGx5IHVwZGF0ZSBVSVxyXG4gICAgICBjb25zdCBuZXdMaWtlZFN0YXRlID0gIWlzTGlrZWQ7XHJcbiAgICAgIHNldElzTGlrZWQobmV3TGlrZWRTdGF0ZSk7XHJcbiAgICAgIHNldExpa2VDb3VudChwcmV2ID0+IG5ld0xpa2VkU3RhdGUgPyBwcmV2ICsgMSA6IHByZXYgLSAxKTtcclxuXHJcbiAgICAgIC8vIE1ha2UgQVBJIHJlcXVlc3QgdG8gbGlrZS91bmxpa2VcclxuICAgICAgY29uc3QgZW5kcG9pbnQgPSBuZXdMaWtlZFN0YXRlID8gJy9hcGkvbGlrZScgOiAnL2FwaS91bmxpa2UnO1xyXG4gICAgICBhd2FpdCBheGlvcy5wb3N0KGVuZHBvaW50LCB7XHJcbiAgICAgICAgY29udGVudF9pZDogcGhvdG9JZCxcclxuICAgICAgICBjb250ZW50X3R5cGU6ICdwaG90bydcclxuICAgICAgfSwge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gXHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB0b2dnbGluZyBsaWtlOicsIGVycik7XHJcbiAgICAgIC8vIFJldmVydCBvcHRpbWlzdGljIHVwZGF0ZSBvbiBlcnJvclxyXG4gICAgICBzZXRJc0xpa2VkKCFpc0xpa2VkKTtcclxuICAgICAgc2V0TGlrZUNvdW50KHByZXYgPT4gaXNMaWtlZCA/IHByZXYgKyAxIDogcHJldiAtIDEpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMaWtpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIHRvZ2dsZSBmdWxsc2NyZWVuIG1vZGVcclxuICBjb25zdCB0b2dnbGVGdWxsc2NyZWVuID0gKCkgPT4ge1xyXG4gICAgc2V0SXNGdWxsc2NyZWVuKCFpc0Z1bGxzY3JlZW4pO1xyXG4gIH07XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIG5hdmlnYXRlIGJhY2tcclxuICBjb25zdCBnb0JhY2sgPSAoKSA9PiB7XHJcbiAgICByb3V0ZXIuYmFjaygpO1xyXG4gIH07XHJcblxyXG4gIC8vIEFkZCB0aGlzIGZ1bmN0aW9uIG5lYXIgeW91ciBvdGhlciB1dGlsaXR5IGZ1bmN0aW9uc1xyXG4gIGNvbnN0IGdldEltYWdlU291cmNlID0gKHBob3RvVXJsOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xyXG4gICAgaWYgKCFwaG90b1VybCB8fCBwaG90b1VybC50cmltKCkgPT09ICcnKSB7XHJcbiAgICAgIGNvbnNvbGUud2FybignRW1wdHkgcGhvdG8gVVJMIGRldGVjdGVkJyk7XHJcbiAgICAgIHJldHVybiAnL3BpY3MvcGxhY2Vob2xkZXIuc3ZnJztcclxuICAgIH1cclxuICAgIHJldHVybiBwaG90b1VybDtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTYXZlVG9nZ2xlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFwaG90byB8fCAhcGhvdG8ucGhvdG9faWQpIHJldHVybjtcclxuICAgIGNvbnN0IHVzZXJJZCA9IGdldFVzZXJJZEZyb21Ub2tlbigpO1xyXG4gICAgaWYgKCF1c2VySWQpIHJldHVybjtcclxuICAgIHNldElzU2F2aW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKGlzU2F2ZWQpIHtcclxuICAgICAgICBhd2FpdCByZW1vdmVDb250ZW50RnJvbU15V2VkZGluZyh7IHVzZXJfaWQ6IHVzZXJJZCwgY29udGVudF9pZDogcGhvdG8ucGhvdG9faWQgfSk7XHJcbiAgICAgICAgc2V0SXNTYXZlZChmYWxzZSk7XHJcbiAgICAgICAgc2V0VG9hc3QoJ1JlbW92ZWQgZnJvbSBNeSBXZWRkaW5ncycpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGF3YWl0IHNhdmVDb250ZW50VG9NeVdlZGRpbmcoeyB1c2VyX2lkOiB1c2VySWQsIGNvbnRlbnRfaWQ6IHBob3RvLnBob3RvX2lkLCBjb250ZW50X3R5cGU6ICdwaG90bycgfSk7XHJcbiAgICAgICAgc2V0SXNTYXZlZCh0cnVlKTtcclxuICAgICAgICBzZXRUb2FzdCgnUGhvdG8gc2F2ZWQgdG8gTXkgV2VkZGluZ3MnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZSkge1xyXG4gICAgICBzZXRUb2FzdCgnU29tZXRoaW5nIHdlbnQgd3JvbmcnKTtcclxuICAgICAgY29uc29sZS5lcnJvcignU2F2ZS9yZW1vdmUgQVBJIGVycm9yJywgZSk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc1NhdmluZyhmYWxzZSk7XHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gc2V0VG9hc3QobnVsbCksIDIwMDApO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2XHJcbiAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgaXNDbGllbnQgPyBcImZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuIGJnLXdoaXRlIHctZnVsbFwiIDogXCJvcGFjaXR5LTBcIlxyXG4gICAgICB9XHJcbiAgICA+XHJcbiAgICAgIHsvKiBUb3AgTmF2aWdhdGlvbiBCYXIgKi99XHJcbiAgICAgIDxUb3BOYXZpZ2F0aW9uIC8+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC0xIGJnLXdoaXRlXCI+XHJcbiAgICAgICAgPFNpZGVOYXZpZ2F0aW9uXHJcbiAgICAgICAgICBleHBhbmRlZD17c2lkZWJhckV4cGFuZGVkfVxyXG4gICAgICAgICAgb25FeHBhbmQ9eygpID0+IHNldFNpZGViYXJFeHBhbmRlZCh0cnVlKX1cclxuICAgICAgICAgIG9uQ29sbGFwc2U9eygpID0+IHNldFNpZGViYXJFeHBhbmRlZChmYWxzZSl9XHJcbiAgICAgICAgLz5cclxuXHJcbiAgICAgICAgey8qIE1haW4gQ29udGVudCAqL31cclxuICAgICAgICA8bWFpblxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtgZmxleC0xIHB5LTQgcHItNCBwbC0wIGJnLXdoaXRlICR7XHJcbiAgICAgICAgICAgIHNpZGViYXJFeHBhbmRlZCA/IFwibWQ6bWwtNDhcIiA6IFwibWQ6bWwtMjBcIlxyXG4gICAgICAgICAgfWB9XHJcbiAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICBtYXJnaW5Ub3A6IFwiODBweFwiLFxyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBcImFsbCAzMDBtcyBlYXNlLWluLW91dFwiLFxyXG4gICAgICAgICAgICBtaW5IZWlnaHQ6IFwiY2FsYygxMDB2aCAtIDgwcHgpXCIsXHJcbiAgICAgICAgICAgIHBhZGRpbmdCb3R0b206IFwiNDBweFwiLFxyXG4gICAgICAgICAgICBvdmVyZmxvd1k6IFwiYXV0b1wiLFxyXG4gICAgICAgICAgICBvdmVyZmxvd1g6IFwiaGlkZGVuXCIsXHJcbiAgICAgICAgICAgIG1hcmdpblJpZ2h0OiByaWdodFNpZGViYXJFeHBhbmRlZCA/IFwiMzIwcHhcIiA6IFwiMFwiLFxyXG4gICAgICAgICAgICBwYWRkaW5nUmlnaHQ6IFwiMjBweFwiLFxyXG4gICAgICAgICAgICBwYWRkaW5nTGVmdDogXCIwXCIsXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIHsvKiBDb250ZW50IENvbnRhaW5lciAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtOCBtYXgtdy1bMTEwMHB4XSB3LWZ1bGwgcGwtMlwiPlxyXG4gICAgICAgICAgICB7LyogTG9hZGluZyBzdGF0ZSAqL31cclxuICAgICAgICAgICAge2xvYWRpbmcgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMTAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIHctNiBoLTYgYm9yZGVyLTIgYm9yZGVyLXJlZC01MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBtci0yXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TG9hZGluZyBwaG90by4uLjwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgIHsvKiBFcnJvciBzdGF0ZSAqL31cclxuICAgICAgICAgICAge2Vycm9yICYmICFsb2FkaW5nICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTEwIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCBtYi00XCI+e2Vycm9yfTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtnb0JhY2t9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1yZWQtNTAwIHRleHQtd2hpdGUgcm91bmRlZCBob3ZlcjpiZy1yZWQtNjAwIHRleHQtc21cIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICBHbyBCYWNrXHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgIHsvKiBQaG90byBjb250ZW50ICovfVxyXG4gICAgICAgICAgICB7IWxvYWRpbmcgJiYgIWVycm9yICYmIHBob3RvICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICB7LyogQmFjayBidXR0b24gKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvQmFja31cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtcmVkLTUwMFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPVwiMjBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PVwiMjBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1yLTFcIlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwb2x5bGluZSBwb2ludHM9XCIxNSAxOCA5IDEyIDE1IDZcIj48L3BvbHlsaW5lPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgIEJhY2tcclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogUGhvdG8gdGl0bGUgYW5kIHVzZXIgaW5mbyAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZFwiPntwaG90by5waG90b19uYW1lfTwvaDE+XHJcbiAgICAgICAgICAgICAgICAgICAge3Bob3RvLmNyZWF0ZWRfYXQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge25ldyBEYXRlKHBob3RvLmNyZWF0ZWRfYXQpLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgeWVhcjogJ251bWVyaWMnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1vbnRoOiAnbG9uZycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGF5OiAnbnVtZXJpYydcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8VXNlckF2YXRhclxyXG4gICAgICAgICAgICAgICAgICAgICAgdXNlcm5hbWU9e3Bob3RvLnVzZXJuYW1lIHx8IFwidXNlclwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cIm1kXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGlzR3JhZGllbnRCb3JkZXI9e3RydWV9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc20gZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtwaG90by51c2VybmFtZX1cclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFBob3RvIGNvbnRhaW5lciAqL31cclxuICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgcmVmPXtpbWFnZVJlZn1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgJHtcclxuICAgICAgICAgICAgICAgICAgICBpc0Z1bGxzY3JlZW5cclxuICAgICAgICAgICAgICAgICAgICAgID8gXCJmaXhlZCBpbnNldC0wIHotNTAgYmctYmxhY2sgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgOiBcInctZnVsbCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBzaGFkb3ctbGdcIlxyXG4gICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHJlbGF0aXZlICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpc0Z1bGxzY3JlZW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcInctZnVsbCBoLWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBpc0Z1bGxzY3JlZW4gPyBcIjEwMHZoXCIgOiBcImNhbGMoMTAwdmggLSAzMDBweClcIixcclxuICAgICAgICAgICAgICAgICAgICAgIG1heEhlaWdodDogaXNGdWxsc2NyZWVuID8gXCIxMDB2aFwiIDogXCI3MHZoXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgc3JjPXtnZXRJbWFnZVNvdXJjZShwaG90by5waG90b191cmwpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgYWx0PXtwaG90by5waG90b19uYW1lIHx8IFwiUGhvdG9cIn1cclxuICAgICAgICAgICAgICAgICAgICAgIGZpbGxcclxuICAgICAgICAgICAgICAgICAgICAgIHNpemVzPVwiKG1heC13aWR0aDogNjQwcHgpIDEwMHZ3LCAobWF4LXdpZHRoOiAxMDI0cHgpIDgwdncsIDcwdndcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvbnRhaW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgcHJpb3JpdHlcclxuICAgICAgICAgICAgICAgICAgICAgIHVub3B0aW1pemVkPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIGxvYWQgaW1hZ2U6ICR7cGhvdG8ucGhvdG9fdXJsfWApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpbWdFbGVtZW50ID0gZS50YXJnZXQgYXMgSFRNTEltYWdlRWxlbWVudDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5zcmMgPSBcIi9waWNzL3BsYWNlaG9sZGVyLnN2Z1wiO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIHsvKiBGdWxsc2NyZWVuIHRvZ2dsZSBidXR0b24gKi99XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVGdWxsc2NyZWVufVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS00IHJpZ2h0LTQgYmctYmxhY2sgYmctb3BhY2l0eS01MCB0ZXh0LXdoaXRlIHAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6Ymctb3BhY2l0eS03MCB0cmFuc2l0aW9uLWFsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD17aXNGdWxsc2NyZWVuID8gXCJFeGl0IGZ1bGxzY3JlZW5cIiA6IFwiVmlldyBmdWxsc2NyZWVuXCJ9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7aXNGdWxsc2NyZWVuID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9XCIyMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodD1cIjIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwb2x5bGluZSBwb2ludHM9XCI0IDE0IDEwIDE0IDEwIDIwXCI+PC9wb2x5bGluZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHBvbHlsaW5lIHBvaW50cz1cIjIwIDEwIDE0IDEwIDE0IDRcIj48L3BvbHlsaW5lPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGluZSB4MT1cIjE0XCIgeTE9XCIxMFwiIHgyPVwiMjFcIiB5Mj1cIjNcIj48L2xpbmU+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaW5lIHgxPVwiM1wiIHkxPVwiMjFcIiB4Mj1cIjEwXCIgeTI9XCIxNFwiPjwvbGluZT5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aD1cIjIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PVwiMjBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHBvbHlsaW5lIHBvaW50cz1cIjE1IDMgMjEgMyAyMSA5XCI+PC9wb2x5bGluZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHBvbHlsaW5lIHBvaW50cz1cIjkgMjEgMyAyMSAzIDE1XCI+PC9wb2x5bGluZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpbmUgeDE9XCIyMVwiIHkxPVwiM1wiIHgyPVwiMTRcIiB5Mj1cIjEwXCI+PC9saW5lPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGluZSB4MT1cIjNcIiB5MT1cIjIxXCIgeDI9XCIxMFwiIHkyPVwiMTRcIj48L2xpbmU+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIHsvKiBDbG9zZSBidXR0b24gZm9yIGZ1bGxzY3JlZW4gbW9kZSAqL31cclxuICAgICAgICAgICAgICAgICAge2lzRnVsbHNjcmVlbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlRnVsbHNjcmVlbn1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IHJpZ2h0LTQgYmctYmxhY2sgYmctb3BhY2l0eS01MCB0ZXh0LXdoaXRlIHAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6Ymctb3BhY2l0eS03MCB0cmFuc2l0aW9uLWFsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2xvc2UgZnVsbHNjcmVlblwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9XCIyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodD1cIjI0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaW5lIHgxPVwiMThcIiB5MT1cIjZcIiB4Mj1cIjZcIiB5Mj1cIjE4XCI+PC9saW5lPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGluZSB4MT1cIjZcIiB5MT1cIjZcIiB4Mj1cIjE4XCIgeTI9XCIxOFwiPjwvbGluZT5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICB7LyogUGhvdG8gZGV0YWlscyBhbmQgaW50ZXJhY3Rpb25zICovfVxyXG4gICAgICAgICAgICB7IWxvYWRpbmcgJiYgIWVycm9yICYmIHBob3RvICYmICFpc0Z1bGxzY3JlZW4gJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBmbGV4IGZsZXgtY29sIGdhcC02XCI+XHJcbiAgICAgICAgICAgICAgICB7LyogRW5nYWdlbWVudCBzdGF0cyBhbmQgYWN0aW9ucyAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTYgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPVwiMThcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PVwiMThcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTEgMTJzNC04IDExLTggMTEgOCAxMSA4LTQgOC0xMSA4LTExLTgtMTEtOHpcIj48L3BhdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjNcIj48L2NpcmNsZT5cclxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57cGhvdG8ucGhvdG9fdmlld3MgfHwgMH0gdmlld3M8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIGhvdmVyOnRleHQtcmVkLTUwMCAke2lzTGlraW5nID8gJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJyA6ICcnfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTGlrZVRvZ2dsZX1cclxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMaWtpbmd9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPVwiMThcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PVwiMThcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBmaWxsPXtpc0xpa2VkID8gXCJjdXJyZW50Q29sb3JcIiA6IFwibm9uZVwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2lzTGlrZWQgPyBcInRleHQtcmVkLTUwMFwiIDogXCJcIn1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTIwLjg0IDQuNjFhNS41IDUuNSAwIDAgMC03Ljc4IDBMMTIgNS42N2wtMS4wNi0xLjA2YTUuNSA1LjUgMCAwIDAtNy43OCA3Ljc4bDEuMDYgMS4wNkwxMiAyMS4yM2w3Ljc4LTcuNzggMS4wNi0xLjA2YTUuNSA1LjUgMCAwIDAgMC03Ljc4elwiPjwvcGF0aD5cclxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57bGlrZUNvdW50fSBsaWtlczwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgICAgICAgICB7LyogU2F2ZSBidXR0b24gcmVwbGFjZXMgY29tbWVudCBpY29uICovfVxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgaG92ZXI6dGV4dC15ZWxsb3ctNjAwICR7aXNTYXZlZCA/ICd0ZXh0LXllbGxvdy02MDAnIDogJyd9YH1cclxuICAgICAgICAgICAgICAgICAgICB0aXRsZT17aXNTYXZlZCA/ICdSZW1vdmUgZnJvbSBNeSBXZWRkaW5ncycgOiAnU2F2ZSB0byBNeSBXZWRkaW5ncyd9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2F2ZVRvZ2dsZX1cclxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTYXZpbmd9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8Qm9va21hcmsgY2xhc3NOYW1lPXtgdy00IGgtNCAke2lzU2F2ZWQgPyAndGV4dC15ZWxsb3ctNjAwJyA6ICd0ZXh0LWdyYXktNzAwJ31gfSBmaWxsPXtpc1NhdmVkID8gJyNGRkQ3MDAnIDogJ25vbmUnfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPntpc1NhdmVkID8gJ1NhdmVkJyA6ICdTYXZlJ308L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFBob3RvIGRlc2NyaXB0aW9uICovfVxyXG4gICAgICAgICAgICAgICAge3Bob3RvLnBob3RvX2Rlc2NyaXB0aW9uICYmIHBob3RvLnBob3RvX2Rlc2NyaXB0aW9uLnRyaW0oKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTFcIj5EZXNjcmlwdGlvbjwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMFwiPntwaG90by5waG90b19kZXNjcmlwdGlvbn08L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogUGhvdG8gdGFncyAqL31cclxuICAgICAgICAgICAgICAgIHtwaG90by5waG90b190YWdzICYmIHBob3RvLnBob3RvX3RhZ3MubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTFcIj5UYWdzPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7cGhvdG8ucGhvdG9fdGFncy5tYXAoKHRhZywgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTEwMCByb3VuZGVkLWZ1bGwgdGV4dC1zbSB0ZXh0LWdyYXktNzAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0YWd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIENvbW1lbnRzIHNlY3Rpb24gKi99XHJcbiAgICAgICAgICAgICAgICB7cGhvdG8uY29tbWVudHMgJiYgcGhvdG8uY29tbWVudHMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj5Db21tZW50czwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtwaG90by5jb21tZW50cy5tYXAoKGNvbW1lbnQpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2NvbW1lbnQuY29tbWVudF9pZH0gY2xhc3NOYW1lPVwiZmxleCBnYXAtMyBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXJBdmF0YXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJuYW1lPXtjb21tZW50LnVzZXJuYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzR3JhZGllbnRCb3JkZXI9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbVwiPntjb21tZW50LnVzZXJuYW1lfTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwXCI+e2NvbW1lbnQuY29tbWVudF90ZXh0fTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoY29tbWVudC5jcmVhdGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFJlbGF0ZWQgcGhvdG9zIHNlY3Rpb24gKi99XHJcbiAgICAgICAgICAgICAgICB7cmVsYXRlZFBob3Rvcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItNFwiPlJlbGF0ZWQgUGhvdG9zPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTMgbGc6Z3JpZC1jb2xzLTUgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtyZWxhdGVkUGhvdG9zLm1hcCgocmVsYXRlZFBob3RvLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtgJHtyZWxhdGVkUGhvdG8ucGhvdG9faWR9LSR7aW5kZXh9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBzaGFkb3ctbWQgcmVsYXRpdmUgaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBoZWlnaHQ6IFwiMTYwcHhcIiB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gYC9ob21lL3Bob3Rvcy8ke3JlbGF0ZWRQaG90by5waG90b19pZH1gO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbCBoLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3JlbGF0ZWRQaG90by5waG90b191cmwgfHwgXCIvcGljcy9wbGFjZWhvbGRlci5zdmdcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXtyZWxhdGVkUGhvdG8ucGhvdG9fbmFtZSB8fCBcIlJlbGF0ZWQgcGhvdG9cIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplcz1cIihtYXgtd2lkdGg6IDY0MHB4KSA1MHZ3LCAobWF4LXdpZHRoOiAxMDI0cHgpIDMzdncsIDIwdndcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvYmplY3QtY292ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1bm9wdGltaXplZD17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpbWdFbGVtZW50ID0gZS50YXJnZXQgYXMgSFRNTEltYWdlRWxlbWVudDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoaW1nRWxlbWVudCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5zcmMgPSAnL3BpY3MvcGxhY2Vob2xkZXIuc3ZnJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgcC0yIGJnLWdyYWRpZW50LXRvLXQgZnJvbS1ibGFjay84MCB0by10cmFuc3BhcmVudCB0ZXh0LXdoaXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdHJ1bmNhdGVcIj57cmVsYXRlZFBob3RvLnBob3RvX25hbWV9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogTW9yZSBwaG90b3Mgc2VjdGlvbiAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOFwiPlxyXG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSBtYi00XCI+TW9yZSBQaG90b3M8L2gzPlxyXG4gICAgICAgICAgICAgICAgICA8RGV0YWlsUGFnZUxhenlMb2FkIGlkPVwibW9yZS1waG90b3Mtc2VjdGlvblwiIGluZGV4PXsxfSByb290TWFyZ2luPVwiMHB4IDBweCAyMDBweCAwcHhcIj5cclxuICAgICAgICAgICAgICAgICAgICB7KHsgc2hvdWxkTG9hZCB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0byB3LWZ1bGwgbXgtYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8UGhvdG9zIHNob3VsZExvYWQ9e3Nob3VsZExvYWR9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8L0RldGFpbFBhZ2VMYXp5TG9hZD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9tYWluPlxyXG5cclxuICAgICAgICA8UmlnaHRTaWRlYmFyXHJcbiAgICAgICAgICBleHBhbmRlZD17cmlnaHRTaWRlYmFyRXhwYW5kZWR9XHJcbiAgICAgICAgICBvbkV4cGFuZD17KCkgPT4gc2V0UmlnaHRTaWRlYmFyRXhwYW5kZWQodHJ1ZSl9XHJcbiAgICAgICAgICBvbkNvbGxhcHNlPXsoKSA9PiBzZXRSaWdodFNpZGViYXJFeHBhbmRlZChmYWxzZSl9XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8TW9iaWxlTmF2aWdhdGlvbiAvPlxyXG5cclxuICAgICAgey8qIFRvYXN0IG1lc3NhZ2UgKi99XHJcbiAgICAgIHt0b2FzdCAmJiAoXHJcbiAgICAgICAgPGRpdiBzdHlsZT17eyBwb3NpdGlvbjogJ2ZpeGVkJywgYm90dG9tOiAyNCwgcmlnaHQ6IDI0LCB6SW5kZXg6IDk5OTkgfX0gY2xhc3NOYW1lPVwiYmctYmxhY2sgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZCBzaGFkb3ctbGcgYW5pbWF0ZS1mYWRlLWluXCI+XHJcbiAgICAgICAgICB7dG9hc3R9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcblxyXG5mdW5jdGlvbiB1c2VSZWY8VD4oaW5pdGlhbFZhbHVlOiBUIHwgbnVsbCkge1xyXG4gIHJldHVybiByZWFjdFVzZVJlZjxUPihpbml0aWFsVmFsdWUpO1xyXG59XHJcblxyXG5cclxuXHJcblxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VQYXJhbXMiLCJheGlvcyIsIkltYWdlIiwidXNlUm91dGVyIiwiVG9wTmF2aWdhdGlvbiIsIlNpZGVOYXZpZ2F0aW9uIiwiTW9iaWxlTmF2aWdhdGlvbiIsIlJpZ2h0U2lkZWJhciIsIlVzZXJBdmF0YXIiLCJQaG90b3MiLCJEZXRhaWxQYWdlTGF6eUxvYWQiLCJ1c2VSZWYiLCJyZWFjdFVzZVJlZiIsInNhdmVDb250ZW50VG9NeVdlZGRpbmciLCJyZW1vdmVDb250ZW50RnJvbU15V2VkZGluZyIsImNoZWNrQ29udGVudFNhdmVkU3RhdHVzIiwiQm9va21hcmsiLCJQaG90b1ZpZXdQYWdlIiwicGFyYW1zIiwicm91dGVyIiwicGhvdG9JZCIsImlkIiwicGhvdG8iLCJzZXRQaG90byIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInNpZGViYXJFeHBhbmRlZCIsInNldFNpZGViYXJFeHBhbmRlZCIsInJpZ2h0U2lkZWJhckV4cGFuZGVkIiwic2V0UmlnaHRTaWRlYmFyRXhwYW5kZWQiLCJpc0NsaWVudCIsInNldElzQ2xpZW50IiwiaXNMaWtlZCIsInNldElzTGlrZWQiLCJsaWtlQ291bnQiLCJzZXRMaWtlQ291bnQiLCJyZWxhdGVkUGhvdG9zIiwic2V0UmVsYXRlZFBob3RvcyIsImlzRnVsbHNjcmVlbiIsInNldElzRnVsbHNjcmVlbiIsImlzTGlraW5nIiwic2V0SXNMaWtpbmciLCJpbWFnZVJlZiIsImlzU2F2ZWQiLCJzZXRJc1NhdmVkIiwiaXNTYXZpbmciLCJzZXRJc1NhdmluZyIsInRvYXN0Iiwic2V0VG9hc3QiLCJmZXRjaFBob3RvRGV0YWlscyIsImNvbnNvbGUiLCJsb2ciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzdWJzdHJpbmciLCJsZW5ndGgiLCJzdGFydHNXaXRoIiwid2FybiIsImRvY3VtZW50IiwiY29va2llIiwicGhvdG9zVGVzdFJlc3BvbnNlIiwiZ2V0IiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJzdGF0dXMiLCJwaG90b3NFcnJvciIsInJlc3BvbnNlIiwiZGF0YSIsIm1lc3NhZ2UiLCJFcnJvciIsInBob3RvRnJvbUxpc3QiLCJwaG90b3NSZXNwb25zZSIsInBob3RvcyIsImZpbmQiLCJwIiwicGhvdG9faWQiLCJsaWtlZCIsImNvbW1lbnRzIiwibGlzdEVycm9yIiwiaW5kaXZpZHVhbEVycm9yIiwiZm91bmRQaG90byIsInBob3RvRGF0YSIsInBob3RvX2xpa2VzIiwiZmV0Y2hSZWxhdGVkUGhvdG9zIiwicGhvdG9fY2F0ZWdvcnkiLCJ1c2VyX2lkIiwiZXJyIiwic3RhY2siLCJlcnJvck1lc3NhZ2UiLCJnZXRVc2VySWRGcm9tVG9rZW4iLCJwYXlsb2FkIiwiSlNPTiIsInBhcnNlIiwiYXRvYiIsInNwbGl0Iiwic3ViIiwiZSIsInVzZXJJZCIsImNvbnRlbnRfaWQiLCJ0aGVuIiwicmVzIiwiaXNfc2F2ZWQiLCJjYXRjaCIsImNhdGVnb3J5IiwicXVlcnlQYXJhbSIsImVuY29kZVVSSUNvbXBvbmVudCIsImZpbHRlcmVkIiwiZmlsdGVyIiwic2xpY2UiLCJoYW5kbGVMaWtlVG9nZ2xlIiwibmV3TGlrZWRTdGF0ZSIsInByZXYiLCJlbmRwb2ludCIsInBvc3QiLCJjb250ZW50X3R5cGUiLCJ0b2dnbGVGdWxsc2NyZWVuIiwiZ29CYWNrIiwiYmFjayIsImdldEltYWdlU291cmNlIiwicGhvdG9VcmwiLCJ0cmltIiwiaGFuZGxlU2F2ZVRvZ2dsZSIsInNldFRpbWVvdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJleHBhbmRlZCIsIm9uRXhwYW5kIiwib25Db2xsYXBzZSIsIm1haW4iLCJzdHlsZSIsIm1hcmdpblRvcCIsInRyYW5zaXRpb24iLCJtaW5IZWlnaHQiLCJwYWRkaW5nQm90dG9tIiwib3ZlcmZsb3dZIiwib3ZlcmZsb3dYIiwibWFyZ2luUmlnaHQiLCJwYWRkaW5nUmlnaHQiLCJwYWRkaW5nTGVmdCIsInNwYW4iLCJidXR0b24iLCJvbkNsaWNrIiwic3ZnIiwieG1sbnMiLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJmaWxsIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJwb2x5bGluZSIsInBvaW50cyIsImgxIiwicGhvdG9fbmFtZSIsImNyZWF0ZWRfYXQiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwidXNlcm5hbWUiLCJzaXplIiwiaXNHcmFkaWVudEJvcmRlciIsInJlZiIsIm1heEhlaWdodCIsInNyYyIsInBob3RvX3VybCIsImFsdCIsInNpemVzIiwicHJpb3JpdHkiLCJ1bm9wdGltaXplZCIsIm9uRXJyb3IiLCJpbWdFbGVtZW50IiwidGFyZ2V0IiwiYXJpYS1sYWJlbCIsImxpbmUiLCJ4MSIsInkxIiwieDIiLCJ5MiIsInBhdGgiLCJkIiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJwaG90b192aWV3cyIsImRpc2FibGVkIiwidGl0bGUiLCJwaG90b19kZXNjcmlwdGlvbiIsImgzIiwicGhvdG9fdGFncyIsIm1hcCIsInRhZyIsImluZGV4IiwiY29tbWVudCIsImNvbW1lbnRfdGV4dCIsImNvbW1lbnRfaWQiLCJyZWxhdGVkUGhvdG8iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJyb290TWFyZ2luIiwic2hvdWxkTG9hZCIsInBvc2l0aW9uIiwiYm90dG9tIiwicmlnaHQiLCJ6SW5kZXgiLCJpbml0aWFsVmFsdWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/HomeDashboard/Photos.tsx":
/*!*********************************************!*\
  !*** ./components/HomeDashboard/Photos.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Photos = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [photos, setPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // Fallback data if API fails - using empty array\n    const getFallbackPhotos = ()=>[];\n    // Function to fetch photos from the API\n    const fetchPhotos = async function(pageNumber) {\n        let isInitialLoad = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Prevent multiple simultaneous requests\n        if (isInitialLoad && loading || !isInitialLoad && loadingMore) {\n            console.log('Request already in progress, skipping');\n            return;\n        }\n        try {\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching photos for page \".concat(pageNumber, \"...\"));\n            // Simple token retrieval\n            const token = localStorage.getItem('token');\n            console.log(\"Auth token found: \".concat(token ? 'Yes' : 'No'));\n            if (!token) {\n                console.warn('No authentication token found');\n                setError('Authentication required');\n                setPhotos([]);\n                return;\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=\".concat(pageNumber, \"&limit=10\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('API response status:', response.status);\n            // Process the data\n            if (response.data && response.data.photos) {\n                console.log(\"Loaded \".concat(response.data.photos.length, \" photos for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.photos.length > 0) {\n                    console.log('Sample photo data:', response.data.photos[0]);\n                }\n                // Process the response\n                const processedPhotos = response.data.photos.map((photo)=>{\n                    if (!photo.photo_url) {\n                        console.log(\"Photo missing URL: \".concat(photo.photo_id));\n                    }\n                    return photo;\n                });\n                if (pageNumber === 1) {\n                    setPhotos(processedPhotos);\n                } else {\n                    setPhotos((prev)=>[\n                            ...prev,\n                            ...processedPhotos\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn('Unexpected response format:', response.data);\n                setError('Failed to load photos - unexpected response format');\n                setPhotos([]);\n            }\n        } catch (err) {\n            console.error('API request failed:', err);\n            setError('Failed to load photos');\n            setPhotos([]);\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log('Initial loading complete');\n            } else {\n                setLoadingMore(false);\n                console.log('Loading more complete');\n            }\n        }\n    };\n    // Fetch first page of photos as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log('Photos component is now visible and ready to load data');\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log('Triggering initial photos load IMMEDIATELY...');\n                setLoading(true);\n                // Skip the fetchPhotos function and make the API call directly here\n                const token = localStorage.getItem('token');\n                if (token) {\n                    // Make direct API request\n                    console.log('Making direct API request for photos page 1...');\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/photos?page=1&limit=10\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"Photos.useEffect\": (response)=>{\n                            console.log('API response received for photos page 1');\n                            if (response.data && response.data.photos) {\n                                setPhotos(response.data.photos);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setPhotos([]);\n                                setError('No photos found');\n                            }\n                        }\n                    }[\"Photos.useEffect\"]).catch({\n                        \"Photos.useEffect\": (err)=>{\n                            console.error('Direct API request failed:', err);\n                            setError('Failed to load photos');\n                            setPhotos([]);\n                        }\n                    }[\"Photos.useEffect\"]).finally({\n                        \"Photos.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log('Initial loading complete');\n                        }\n                    }[\"Photos.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError('Authentication required');\n                }\n            }\n        }\n    }[\"Photos.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Photos.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log('Disconnected previous intersection observer');\n            }\n            // Create new observer\n            observerRef.current = new IntersectionObserver({\n                \"Photos.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log('Load more trigger is visible, loading next page...');\n                        console.log('Intersection ratio:', entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchPhotos(nextPage, false);\n                    }\n                }\n            }[\"Photos.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log('Now observing load more trigger element');\n            } else {\n                console.warn('Load more trigger element not found');\n            }\n            return ({\n                \"Photos.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log('Disconnected intersection observer');\n                    }\n                }\n            })[\"Photos.useEffect\"];\n        }\n    }[\"Photos.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a photo\n    const getImageSource = (photo)=>{\n        if (photo.photo_url) {\n            return photo.photo_url;\n        }\n        return '/pics/placeholder.svg';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-between mb-4 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] font-semibold text-black\",\n                        children: \"Photos\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative mr-1 ml-auto\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading photos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log('Retrying photos load...');\n                            setError(null);\n                            fetchPhotos(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: 'smooth',\n                    WebkitOverflowScrolling: 'touch',\n                    minHeight: photos.length === 0 && !error ? '220px' : 'auto'\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative\",\n                children: [\n                    photos.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No photos available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-4 pb-4 flex-nowrap\",\n                        children: [\n                            photos.map((photo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: '250px',\n                                        height: '200px'\n                                    },\n                                    onClick: ()=>{\n                                        window.location.href = \"/home/<USER>/\".concat(photo.photo_id);\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute top-2 left-2 z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                username: photo.user_name || \"user\",\n                                                size: \"sm\",\n                                                isGradientBorder: true,\n                                                onClick: ()=>navigateToUserProfile(photo.user_id, photo.user_name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: getImageSource(photo),\n                                                alt: photo.photo_name || \"Photo\",\n                                                fill: true,\n                                                sizes: \"(max-width: 640px) 250px, 250px\",\n                                                className: \"object-cover\",\n                                                ...index < 4 ? {\n                                                    priority: true\n                                                } : {\n                                                    loading: 'lazy'\n                                                },\n                                                placeholder: \"blur\" // Show blur placeholder while loading\n                                                ,\n                                                blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\" // Base64 encoded SVG loading animation\n                                                ,\n                                                onError: (e)=>{\n                                                    const imgElement = e.target;\n                                                    if (imgElement) {\n                                                        imgElement.src = '/pics/placeholder.svg';\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-sm font-medium truncate\",\n                                                    children: photo.photo_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-xs flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            onClick: (e)=>navigateToUserProfile(photo.user_id, photo.user_name, e),\n                                                            className: \"jsx-83037452c623c470\" + \" \" + \"cursor-pointer hover:underline\",\n                                                            children: photo.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-83037452c623c470\",\n                                                            children: photo.photo_likes ? \"\".concat(photo.photo_likes, \" likes\") : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(photo.photo_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: 'relative',\n                                    // Add debug outline in development\n                                    outline:  true ? '1px dashed rgba(255, 0, 0, 0.3)' : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"photos-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[200px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Photos.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Photos, \"9Jg+DVptG/SwtKabWduqH6CnTuE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = Photos;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Photos);\nvar _c;\n$RefreshReg$(_c, \"Photos\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvSG9tZURhc2hib2FyZC9QaG90b3MudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDMkQ7QUFDNUI7QUFDTztBQUNTO0FBQ0g7QUE4QjVDLE1BQU1RLFNBQWdDO1FBQUMsRUFBRUMsYUFBYSxLQUFLLEVBQUU7O0lBQzNELE1BQU1DLFNBQVNILDBEQUFTQTtJQUN4QixNQUFNLENBQUNJLFFBQVFDLFVBQVUsR0FBR1gsK0NBQVFBLENBQVUsRUFBRTtJQUNoRCxNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBR2IsK0NBQVFBLENBQVU7SUFDaEQsTUFBTSxDQUFDYyxPQUFPQyxTQUFTLEdBQUdmLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNnQixNQUFNQyxRQUFRLEdBQUdqQiwrQ0FBUUEsQ0FBUztJQUN6QyxNQUFNLENBQUNrQixTQUFTQyxXQUFXLEdBQUduQiwrQ0FBUUEsQ0FBVTtJQUNoRCxNQUFNLENBQUNvQixhQUFhQyxlQUFlLEdBQUdyQiwrQ0FBUUEsQ0FBVTtJQUN4RCxNQUFNLENBQUNzQixxQkFBcUJDLHVCQUF1QixHQUFHdkIsK0NBQVFBLENBQVU7SUFDeEUsTUFBTXdCLHFCQUFxQnRCLDZDQUFNQSxDQUFpQjtJQUNsRCxNQUFNdUIscUJBQXFCdkIsNkNBQU1BLENBQWlCO0lBQ2xELE1BQU13QixjQUFjeEIsNkNBQU1BLENBQThCO0lBRXhELDJDQUEyQztJQUMzQyxNQUFNeUIsd0JBQXdCLENBQUNDLFFBQWlCQyxVQUFtQkM7UUFDakUsbUVBQW1FO1FBQ25FLElBQUlBLE9BQU87WUFDVEEsTUFBTUMsZUFBZTtRQUN2QjtRQUVBLGdFQUFnRTtRQUNoRSxJQUFJSCxRQUFRO1lBQ1ZuQixPQUFPdUIsSUFBSSxDQUFDLFlBQW1CLE9BQVBKO1FBQzFCLE9BQU8sSUFBSUMsVUFBVTtZQUNuQixnREFBZ0Q7WUFDaEQsMkRBQTJEO1lBQzNEcEIsT0FBT3VCLElBQUksQ0FBQyxZQUFxQixPQUFUSDtRQUMxQixPQUFPO1lBQ0xJLFFBQVFDLElBQUksQ0FBQztRQUNmO0lBQ0Y7SUFFQSxpREFBaUQ7SUFDakQsTUFBTUMsb0JBQW9CLElBQWUsRUFBRTtJQUUzQyx3Q0FBd0M7SUFDeEMsTUFBTUMsY0FBYyxlQUFPQztZQUFvQkMsaUZBQXlCO1FBQ3RFLHlDQUF5QztRQUN6QyxJQUFJLGlCQUFrQjFCLFdBQWEsQ0FBQzBCLGlCQUFpQmxCLGFBQWM7WUFDakVhLFFBQVFNLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsSUFBSUQsZUFBZTtnQkFDakJ6QixXQUFXO1lBQ2IsT0FBTztnQkFDTFEsZUFBZTtZQUNqQjtZQUVBWSxRQUFRTSxHQUFHLENBQUMsNEJBQXVDLE9BQVhGLFlBQVc7WUFFbkQseUJBQXlCO1lBQ3pCLE1BQU1HLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUVuQ1QsUUFBUU0sR0FBRyxDQUFDLHFCQUEwQyxPQUFyQkMsUUFBUSxRQUFRO1lBRWpELElBQUksQ0FBQ0EsT0FBTztnQkFDVlAsUUFBUUMsSUFBSSxDQUFDO2dCQUNibkIsU0FBUztnQkFDVEosVUFBVSxFQUFFO2dCQUNaO1lBQ0Y7WUFFQSxtQkFBbUI7WUFDbkIsTUFBTWdDLFdBQVcsTUFBTXRDLDZEQUFLQSxDQUFDdUMsR0FBRyxDQUFjLGdCQUEyQixPQUFYUCxZQUFXLGNBQVk7Z0JBQ25GUSxTQUFTO29CQUNQQyxlQUFlLFVBQWdCLE9BQU5OO2dCQUMzQjtZQUNGO1lBRUFQLFFBQVFNLEdBQUcsQ0FBQyx3QkFBd0JJLFNBQVNJLE1BQU07WUFFbkQsbUJBQW1CO1lBQ25CLElBQUlKLFNBQVNLLElBQUksSUFBSUwsU0FBU0ssSUFBSSxDQUFDdEMsTUFBTSxFQUFFO2dCQUN6Q3VCLFFBQVFNLEdBQUcsQ0FBQyxVQUF5REYsT0FBL0NNLFNBQVNLLElBQUksQ0FBQ3RDLE1BQU0sQ0FBQ3VDLE1BQU0sRUFBQyxxQkFBOEIsT0FBWFo7Z0JBRXJFLG1DQUFtQztnQkFDbkMsSUFBSU0sU0FBU0ssSUFBSSxDQUFDdEMsTUFBTSxDQUFDdUMsTUFBTSxHQUFHLEdBQUc7b0JBQ25DaEIsUUFBUU0sR0FBRyxDQUFDLHNCQUFzQkksU0FBU0ssSUFBSSxDQUFDdEMsTUFBTSxDQUFDLEVBQUU7Z0JBQzNEO2dCQUVBLHVCQUF1QjtnQkFDdkIsTUFBTXdDLGtCQUFrQlAsU0FBU0ssSUFBSSxDQUFDdEMsTUFBTSxDQUFDeUMsR0FBRyxDQUFDQyxDQUFBQTtvQkFDL0MsSUFBSSxDQUFDQSxNQUFNQyxTQUFTLEVBQUU7d0JBQ3BCcEIsUUFBUU0sR0FBRyxDQUFDLHNCQUFxQyxPQUFmYSxNQUFNRSxRQUFRO29CQUNsRDtvQkFDQSxPQUFPRjtnQkFDVDtnQkFFQSxJQUFJZixlQUFlLEdBQUc7b0JBQ3BCMUIsVUFBVXVDO2dCQUNaLE9BQU87b0JBQ0x2QyxVQUFVNEMsQ0FBQUEsT0FBUTsrQkFBSUE7K0JBQVNMO3lCQUFnQjtnQkFDakQ7Z0JBRUEvQixXQUFXd0IsU0FBU0ssSUFBSSxDQUFDUSxTQUFTO2dCQUNsQ3ZDLFFBQVFvQixhQUFhLDBCQUEwQjtnQkFDL0N0QixTQUFTLE9BQU8sNEJBQTRCO1lBQzlDLE9BQU87Z0JBQ0xrQixRQUFRQyxJQUFJLENBQUMsK0JBQStCUyxTQUFTSyxJQUFJO2dCQUN6RGpDLFNBQVM7Z0JBQ1RKLFVBQVUsRUFBRTtZQUNkO1FBQ0YsRUFBRSxPQUFPOEMsS0FBSztZQUNaeEIsUUFBUW5CLEtBQUssQ0FBQyx1QkFBdUIyQztZQUNyQzFDLFNBQVM7WUFDVEosVUFBVSxFQUFFO1FBQ2QsU0FBVTtZQUNSLDhCQUE4QjtZQUM5QixJQUFJMkIsZUFBZTtnQkFDakJ6QixXQUFXO2dCQUNYb0IsUUFBUU0sR0FBRyxDQUFDO1lBQ2QsT0FBTztnQkFDTGxCLGVBQWU7Z0JBQ2ZZLFFBQVFNLEdBQUcsQ0FBQztZQUNkO1FBQ0Y7SUFDRjtJQUVBLDZEQUE2RDtJQUM3RHRDLGdEQUFTQTs0QkFBQztZQUNSLCtDQUErQztZQUMvQyxJQUFJTyxZQUFZO2dCQUNkeUIsUUFBUU0sR0FBRyxDQUFDO2dCQUVaLG1DQUFtQztnQkFDbkNoQix1QkFBdUI7Z0JBRXZCLHFEQUFxRDtnQkFDckRVLFFBQVFNLEdBQUcsQ0FBQztnQkFDWjFCLFdBQVc7Z0JBRVgsb0VBQW9FO2dCQUNwRSxNQUFNMkIsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO2dCQUVuQyxJQUFJRixPQUFPO29CQUNULDBCQUEwQjtvQkFDMUJQLFFBQVFNLEdBQUcsQ0FBQztvQkFDWmxDLDZEQUFLQSxDQUFDdUMsR0FBRyxDQUFlLDJCQUEwQjt3QkFDaERDLFNBQVM7NEJBQ1BDLGVBQWUsVUFBZ0IsT0FBTk47d0JBQzNCO29CQUNGLEdBQ0drQixJQUFJOzRDQUFDZixDQUFBQTs0QkFDSlYsUUFBUU0sR0FBRyxDQUFDOzRCQUNaLElBQUlJLFNBQVNLLElBQUksSUFBSUwsU0FBU0ssSUFBSSxDQUFDdEMsTUFBTSxFQUFFO2dDQUN6Q0MsVUFBVWdDLFNBQVNLLElBQUksQ0FBQ3RDLE1BQU07Z0NBQzlCUyxXQUFXd0IsU0FBU0ssSUFBSSxDQUFDUSxTQUFTO2dDQUNsQ3ZDLFFBQVE7Z0NBQ1JGLFNBQVM7NEJBQ1gsT0FBTztnQ0FDTEosVUFBVSxFQUFFO2dDQUNaSSxTQUFTOzRCQUNYO3dCQUNGOzJDQUNDNEMsS0FBSzs0Q0FBQ0YsQ0FBQUE7NEJBQ0x4QixRQUFRbkIsS0FBSyxDQUFDLDhCQUE4QjJDOzRCQUM1QzFDLFNBQVM7NEJBQ1RKLFVBQVUsRUFBRTt3QkFDZDsyQ0FDQ2lELE9BQU87NENBQUM7NEJBQ1AvQyxXQUFXOzRCQUNYb0IsUUFBUU0sR0FBRyxDQUFDO3dCQUNkOztnQkFDSixPQUFPO29CQUNMMUIsV0FBVztvQkFDWEUsU0FBUztnQkFDWDtZQUNGO1FBQ0Y7MkJBQUc7UUFBQ1A7S0FBVyxHQUFHLDRCQUE0QjtJQUU5QywwREFBMEQ7SUFDMURQLGdEQUFTQTs0QkFBQztZQUNSLCtFQUErRTtZQUMvRSxJQUFJLENBQUNxQix1QkFBdUIsQ0FBQ0osU0FBUztZQUV0Qyw0Q0FBNEM7WUFDNUMsSUFBSVEsWUFBWW1DLE9BQU8sRUFBRTtnQkFDdkJuQyxZQUFZbUMsT0FBTyxDQUFDQyxVQUFVO2dCQUM5QjdCLFFBQVFNLEdBQUcsQ0FBQztZQUNkO1lBRUEsc0JBQXNCO1lBQ3RCYixZQUFZbUMsT0FBTyxHQUFHLElBQUlFO29DQUN4QixDQUFDQzt3QkFFS0E7b0JBREosa0VBQWtFO29CQUNsRSxJQUFJQSxFQUFBQSxZQUFBQSxPQUFPLENBQUMsRUFBRSxjQUFWQSxnQ0FBQUEsVUFBWUMsY0FBYyxLQUFJLENBQUNyRCxXQUFXLENBQUNRLGVBQWVGLFNBQVM7d0JBQ3JFZSxRQUFRTSxHQUFHLENBQUM7d0JBQ1pOLFFBQVFNLEdBQUcsQ0FBQyx1QkFBdUJ5QixPQUFPLENBQUMsRUFBRSxDQUFDRSxpQkFBaUI7d0JBQy9ELE1BQU1DLFdBQVduRCxPQUFPO3dCQUN4Qm9CLFlBQVkrQixVQUFVO29CQUN4QjtnQkFDRjttQ0FDQSxxRUFBcUU7WUFDckU7Z0JBQ0VDLE1BQU01QyxtQkFBbUJxQyxPQUFPO2dCQUNoQ1EsV0FBVztnQkFDWEMsWUFBWSxvQkFBb0IsMENBQTBDO1lBQzVFO1lBR0Ysc0NBQXNDO1lBQ3RDLElBQUk3QyxtQkFBbUJvQyxPQUFPLEVBQUU7Z0JBQzlCbkMsWUFBWW1DLE9BQU8sQ0FBQ1UsT0FBTyxDQUFDOUMsbUJBQW1Cb0MsT0FBTztnQkFDdEQ1QixRQUFRTSxHQUFHLENBQUM7WUFDZCxPQUFPO2dCQUNMTixRQUFRQyxJQUFJLENBQUM7WUFDZjtZQUVBO29DQUFPO29CQUNMLElBQUlSLFlBQVltQyxPQUFPLEVBQUU7d0JBQ3ZCbkMsWUFBWW1DLE9BQU8sQ0FBQ0MsVUFBVTt3QkFDOUI3QixRQUFRTSxHQUFHLENBQUM7b0JBQ2Q7Z0JBQ0Y7O1FBQ0Y7MkJBQUc7UUFBQ3JCO1FBQVNOO1FBQVNRO1FBQWFKO1FBQU1NO0tBQW9CO0lBRTdELDJDQUEyQztJQUMzQyxNQUFNa0QsaUJBQWlCLENBQUNwQjtRQUN0QixJQUFJQSxNQUFNQyxTQUFTLEVBQUU7WUFDbkIsT0FBT0QsTUFBTUMsU0FBUztRQUN4QjtRQUNBLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDb0I7a0RBQWtCOzswQkFDakIsOERBQUNDOzBEQUFjOztrQ0FDYiw4REFBQ0M7a0VBQWE7a0NBQWlFOzs7Ozs7a0NBRy9FLDhEQUFDQzt3QkFDQ0MsTUFBSztrRUFDSztrQ0FDWDs7Ozs7Ozs7Ozs7O1lBTUZqRSx5QkFDQyw4REFBQzhEOzBEQUFjOztrQ0FDYiw4REFBQ0E7a0VBQWM7Ozs7OztrQ0FDZiw4REFBQ0k7a0VBQWU7a0NBQWdCOzs7Ozs7Ozs7Ozs7WUFLbkNoRSxTQUFTLENBQUNGLHlCQUNULDhEQUFDOEQ7MERBQWM7O2tDQUNiLDhEQUFDQTtrRUFBYztrQ0FBcUI1RDs7Ozs7O2tDQUNwQyw4REFBQ2lFO3dCQUNDQyxTQUFTOzRCQUNQL0MsUUFBUU0sR0FBRyxDQUFDOzRCQUNaeEIsU0FBUzs0QkFDVHFCLFlBQVksR0FBRzt3QkFDakI7a0VBQ1U7a0NBQ1g7Ozs7Ozs7Ozs7OztZQU9KLENBQUN4Qix5QkFDQSw4REFBQzhEO2dCQUVDTyxLQUFLekQ7Z0JBQ0wwRCxPQUFPO29CQUNMQyxnQkFBZ0I7b0JBQ2hCQyx5QkFBeUI7b0JBQ3pCQyxXQUFXM0UsT0FBT3VDLE1BQU0sS0FBSyxLQUFLLENBQUNuQyxRQUFRLFVBQVU7Z0JBQ3ZEOzBEQU5VOztvQkFTVEosT0FBT3VDLE1BQU0sS0FBSyxLQUFLLENBQUNuQyxTQUFTLENBQUNGLHlCQUNqQyw4REFBQzhEO2tFQUFjO2tDQUNiLDRFQUFDQTtzRUFBYztzQ0FBZ0I7Ozs7Ozs7Ozs7O2tDQUluQyw4REFBQ0E7a0VBQWM7OzRCQUNaaEUsT0FBT3lDLEdBQUcsQ0FBQyxDQUFDQyxPQUFPa0Msc0JBQ2xCLDhEQUFDWjtvQ0FHQ1EsT0FBTzt3Q0FBRUssT0FBTzt3Q0FBU0MsUUFBUTtvQ0FBUTtvQ0FDekNSLFNBQVM7d0NBQ1BTLE9BQU9DLFFBQVEsQ0FBQ2IsSUFBSSxHQUFHLGdCQUErQixPQUFmekIsTUFBTUUsUUFBUTtvQ0FDdkQ7OEVBSlU7O3NEQU9WLDhEQUFDb0I7NENBRUNNLFNBQVMsQ0FBQ1csSUFBTWhFLHNCQUFzQnlCLE1BQU13QyxPQUFPLEVBQUV4QyxNQUFNeUMsU0FBUyxFQUFFRjtzRkFENUQ7c0RBR1YsNEVBQUN2RixtREFBVUE7Z0RBQ1R5QixVQUFVdUIsTUFBTXlDLFNBQVMsSUFBSTtnREFDN0JDLE1BQUs7Z0RBQ0xDLGtCQUFrQjtnREFDbEJmLFNBQVMsSUFBTXJELHNCQUFzQnlCLE1BQU13QyxPQUFPLEVBQUV4QyxNQUFNeUMsU0FBUzs7Ozs7Ozs7Ozs7c0RBS3ZFLDhEQUFDbkI7c0ZBQWM7c0RBQ2IsNEVBQUN2RSxrREFBS0E7Z0RBQ0o2RixLQUFLeEIsZUFBZXBCO2dEQUNwQjZDLEtBQUs3QyxNQUFNOEMsVUFBVSxJQUFJO2dEQUN6QkMsSUFBSTtnREFDSkMsT0FBTTtnREFDTkMsV0FBVTtnREFDVCxHQUFJZixRQUFRLElBQUk7b0RBQUVnQixVQUFVO2dEQUFLLElBQUk7b0RBQUUxRixTQUFTO2dEQUFPLENBQUM7Z0RBQ3pEMkYsYUFBWSxPQUFPLHNDQUFzQzs7Z0RBQ3pEQyxhQUFZLHlyQkFBeXJCLHVDQUF1Qzs7Z0RBQzV1QkMsU0FBUyxDQUFDZDtvREFDUixNQUFNZSxhQUFhZixFQUFFZ0IsTUFBTTtvREFDM0IsSUFBSUQsWUFBWTt3REFDZEEsV0FBV1YsR0FBRyxHQUFHO29EQUNuQjtnREFDRjs7Ozs7Ozs7Ozs7c0RBS0osOERBQUN0QjtzRkFBYzs7OERBQ2IsOERBQUNBOzhGQUFjOzhEQUFnQ3RCLE1BQU04QyxVQUFVOzs7Ozs7OERBQy9ELDhEQUFDeEI7OEZBQWM7O3NFQUNiLDhEQUFDSTs0REFFQ0UsU0FBUyxDQUFDVyxJQUFNaEUsc0JBQXNCeUIsTUFBTXdDLE9BQU8sRUFBRXhDLE1BQU15QyxTQUFTLEVBQUVGO3NHQUQ1RDtzRUFHVHZDLE1BQU15QyxTQUFTOzs7Ozs7c0VBRWxCLDhEQUFDZjs7c0VBQ0UxQixNQUFNd0QsV0FBVyxHQUFHLEdBQXFCLE9BQWxCeEQsTUFBTXdELFdBQVcsRUFBQyxZQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O21DQW5EckQsR0FBcUJ0QixPQUFsQmxDLE1BQU1FLFFBQVEsRUFBQyxLQUFTLE9BQU5nQzs7Ozs7NEJBMkQ3QnBFLHlCQUNDLDhEQUFDd0Q7Z0NBQ0NPLEtBQUt4RDtnQ0FFTHlELE9BQU87b0NBQ0wyQixVQUFVO29DQUNWLG1DQUFtQztvQ0FDbkNDLFNBQVNDLEtBQXNDLEdBQUcsb0NBQW9DLENBQU07Z0NBQzlGO2dDQUNBQyxlQUFZO2dDQUNaQyxlQUFZOzBFQVBGOzs7Ozs7NEJBWWI3Riw2QkFDQyw4REFBQ3NEOzBFQUFjOztrREFDYiw4REFBQ0E7a0ZBQWM7Ozs7OztrREFDZiw4REFBQ0k7a0ZBQWU7a0RBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBa0I3RDtHQTdYTXZFOztRQUNXRCxzREFBU0E7OztLQURwQkM7QUErWE4saUVBQWVBLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2l2YXNcXE9uZURyaXZlXFxEZXNrdG9wXFxmaW5hbFxcV0VEWkFUX1xcZnJvbnRlbmRcXGNvbXBvbmVudHNcXEhvbWVEYXNoYm9hcmRcXFBob3Rvcy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XHJcbmltcG9ydCBVc2VyQXZhdGFyIGZyb20gXCIuL1VzZXJBdmF0YXJcIjtcclxuaW1wb3J0IGF4aW9zIGZyb20gXCIuLi8uLi9zZXJ2aWNlcy9heGlvc0NvbmZpZ1wiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcblxyXG4vLyBEZWZpbmUgaW50ZXJmYWNlIGZvciBwaG90byBpdGVtc1xyXG5pbnRlcmZhY2UgUGhvdG8ge1xyXG4gIHBob3RvX2lkOiBzdHJpbmc7XHJcbiAgcGhvdG9fbmFtZTogc3RyaW5nO1xyXG4gIHBob3RvX3VybDogc3RyaW5nO1xyXG4gIHBob3RvX2Rlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIHBob3RvX3RhZ3M/OiBzdHJpbmdbXTtcclxuICBwaG90b19zdWJ0eXBlPzogc3RyaW5nO1xyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcclxuICB1c2VyX25hbWU/OiBzdHJpbmc7XHJcbiAgdXNlcl9pZD86IHN0cmluZztcclxuICBpc19vd25fY29udGVudD86IGJvb2xlYW47XHJcbiAgcGhvdG9fdmlld3M/OiBudW1iZXI7XHJcbiAgcGhvdG9fbGlrZXM/OiBudW1iZXI7XHJcbiAgcGhvdG9fY29tbWVudHM/OiBudW1iZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBBcGlSZXNwb25zZSB7XHJcbiAgcGhvdG9zOiBQaG90b1tdO1xyXG4gIG5leHRfcGFnZTogYm9vbGVhbjtcclxuICB0b3RhbF9jb3VudDogbnVtYmVyO1xyXG4gIGN1cnJlbnRfcGFnZTogbnVtYmVyO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgUGhvdG9zUHJvcHMge1xyXG4gIHNob3VsZExvYWQ/OiBib29sZWFuO1xyXG59XHJcblxyXG5jb25zdCBQaG90b3M6IFJlYWN0LkZDPFBob3Rvc1Byb3BzPiA9ICh7IHNob3VsZExvYWQgPSBmYWxzZSB9KSA9PiB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgW3Bob3Rvcywgc2V0UGhvdG9zXSA9IHVzZVN0YXRlPFBob3RvW10+KFtdKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZTxib29sZWFuPih0cnVlKTtcclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtwYWdlLCBzZXRQYWdlXSA9IHVzZVN0YXRlPG51bWJlcj4oMSk7XHJcbiAgY29uc3QgW2hhc01vcmUsIHNldEhhc01vcmVdID0gdXNlU3RhdGU8Ym9vbGVhbj4odHJ1ZSk7XHJcbiAgY29uc3QgW2xvYWRpbmdNb3JlLCBzZXRMb2FkaW5nTW9yZV0gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSk7XHJcbiAgY29uc3QgW2luaXRpYWxMb2FkQ29tcGxldGUsIHNldEluaXRpYWxMb2FkQ29tcGxldGVdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xyXG4gIGNvbnN0IHNjcm9sbENvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcbiAgY29uc3QgbG9hZE1vcmVUcmlnZ2VyUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBvYnNlcnZlclJlZiA9IHVzZVJlZjxJbnRlcnNlY3Rpb25PYnNlcnZlciB8IG51bGw+KG51bGwpO1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byBuYXZpZ2F0ZSB0byBhIHVzZXIncyBwcm9maWxlXHJcbiAgY29uc3QgbmF2aWdhdGVUb1VzZXJQcm9maWxlID0gKHVzZXJJZD86IHN0cmluZywgdXNlcm5hbWU/OiBzdHJpbmcsIGV2ZW50PzogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xyXG4gICAgLy8gU3RvcCBldmVudCBwcm9wYWdhdGlvbiB0byBwcmV2ZW50IHRyaWdnZXJpbmcgcGFyZW50IGNsaWNrIGV2ZW50c1xyXG4gICAgaWYgKGV2ZW50KSB7XHJcbiAgICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIElmIHdlIGhhdmUgYSB1c2VySWQsIG5hdmlnYXRlIHRvIHRoYXQgc3BlY2lmaWMgdXNlcidzIHByb2ZpbGVcclxuICAgIGlmICh1c2VySWQpIHtcclxuICAgICAgcm91dGVyLnB1c2goYC9wcm9maWxlLyR7dXNlcklkfWApO1xyXG4gICAgfSBlbHNlIGlmICh1c2VybmFtZSkge1xyXG4gICAgICAvLyBGb3Igbm93LCBqdXN0IHVzZSB0aGUgdXNlcm5hbWUgYXMgYSBwYXJhbWV0ZXJcclxuICAgICAgLy8gSW4gYSByZWFsIGFwcCwgeW91IG1pZ2h0IHdhbnQgdG8gZmV0Y2ggdGhlIHVzZXIgSUQgZmlyc3RcclxuICAgICAgcm91dGVyLnB1c2goYC9wcm9maWxlLyR7dXNlcm5hbWV9YCk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ0Nhbm5vdCBuYXZpZ2F0ZSB0byBwcm9maWxlOiBtaXNzaW5nIGJvdGggdXNlcklkIGFuZCB1c2VybmFtZScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEZhbGxiYWNrIGRhdGEgaWYgQVBJIGZhaWxzIC0gdXNpbmcgZW1wdHkgYXJyYXlcclxuICBjb25zdCBnZXRGYWxsYmFja1Bob3RvcyA9ICgpOiBQaG90b1tdID0+IFtdO1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byBmZXRjaCBwaG90b3MgZnJvbSB0aGUgQVBJXHJcbiAgY29uc3QgZmV0Y2hQaG90b3MgPSBhc3luYyAocGFnZU51bWJlcjogbnVtYmVyLCBpc0luaXRpYWxMb2FkOiBib29sZWFuID0gZmFsc2UpID0+IHtcclxuICAgIC8vIFByZXZlbnQgbXVsdGlwbGUgc2ltdWx0YW5lb3VzIHJlcXVlc3RzXHJcbiAgICBpZiAoKGlzSW5pdGlhbExvYWQgJiYgbG9hZGluZykgfHwgKCFpc0luaXRpYWxMb2FkICYmIGxvYWRpbmdNb3JlKSkge1xyXG4gICAgICBjb25zb2xlLmxvZygnUmVxdWVzdCBhbHJlYWR5IGluIHByb2dyZXNzLCBza2lwcGluZycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKGlzSW5pdGlhbExvYWQpIHtcclxuICAgICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldExvYWRpbmdNb3JlKHRydWUpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhgRmV0Y2hpbmcgcGhvdG9zIGZvciBwYWdlICR7cGFnZU51bWJlcn0uLi5gKTtcclxuXHJcbiAgICAgIC8vIFNpbXBsZSB0b2tlbiByZXRyaWV2YWxcclxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKGBBdXRoIHRva2VuIGZvdW5kOiAke3Rva2VuID8gJ1llcycgOiAnTm8nfWApO1xyXG5cclxuICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnKTtcclxuICAgICAgICBzZXRFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQnKTtcclxuICAgICAgICBzZXRQaG90b3MoW10pO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gTWFrZSBBUEkgcmVxdWVzdFxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldDxBcGlSZXNwb25zZT4oYC9waG90b3M/cGFnZT0ke3BhZ2VOdW1iZXJ9JmxpbWl0PTEwYCwge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gXHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdBUEkgcmVzcG9uc2Ugc3RhdHVzOicsIHJlc3BvbnNlLnN0YXR1cyk7XHJcblxyXG4gICAgICAvLyBQcm9jZXNzIHRoZSBkYXRhXHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEucGhvdG9zKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCAke3Jlc3BvbnNlLmRhdGEucGhvdG9zLmxlbmd0aH0gcGhvdG9zIGZvciBwYWdlICR7cGFnZU51bWJlcn1gKTtcclxuXHJcbiAgICAgICAgLy8gTG9nIHRoZSBmaXJzdCBpdGVtIGZvciBkZWJ1Z2dpbmdcclxuICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5waG90b3MubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1NhbXBsZSBwaG90byBkYXRhOicsIHJlc3BvbnNlLmRhdGEucGhvdG9zWzBdKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFByb2Nlc3MgdGhlIHJlc3BvbnNlXHJcbiAgICAgICAgY29uc3QgcHJvY2Vzc2VkUGhvdG9zID0gcmVzcG9uc2UuZGF0YS5waG90b3MubWFwKHBob3RvID0+IHtcclxuICAgICAgICAgIGlmICghcGhvdG8ucGhvdG9fdXJsKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBQaG90byBtaXNzaW5nIFVSTDogJHtwaG90by5waG90b19pZH1gKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHJldHVybiBwaG90bztcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKHBhZ2VOdW1iZXIgPT09IDEpIHtcclxuICAgICAgICAgIHNldFBob3Rvcyhwcm9jZXNzZWRQaG90b3MpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBzZXRQaG90b3MocHJldiA9PiBbLi4ucHJldiwgLi4ucHJvY2Vzc2VkUGhvdG9zXSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzZXRIYXNNb3JlKHJlc3BvbnNlLmRhdGEubmV4dF9wYWdlKTtcclxuICAgICAgICBzZXRQYWdlKHBhZ2VOdW1iZXIpOyAvLyBVcGRhdGUgdGhlIGN1cnJlbnQgcGFnZVxyXG4gICAgICAgIHNldEVycm9yKG51bGwpOyAvLyBDbGVhciBhbnkgcHJldmlvdXMgZXJyb3JzXHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKCdVbmV4cGVjdGVkIHJlc3BvbnNlIGZvcm1hdDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGxvYWQgcGhvdG9zIC0gdW5leHBlY3RlZCByZXNwb25zZSBmb3JtYXQnKTtcclxuICAgICAgICBzZXRQaG90b3MoW10pO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQVBJIHJlcXVlc3QgZmFpbGVkOicsIGVycik7XHJcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBwaG90b3MnKTtcclxuICAgICAgc2V0UGhvdG9zKFtdKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIC8vIEFsd2F5cyBjbGVhciBsb2FkaW5nIHN0YXRlc1xyXG4gICAgICBpZiAoaXNJbml0aWFsTG9hZCkge1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdJbml0aWFsIGxvYWRpbmcgY29tcGxldGUnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRMb2FkaW5nTW9yZShmYWxzZSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgbW9yZSBjb21wbGV0ZScpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gRmV0Y2ggZmlyc3QgcGFnZSBvZiBwaG90b3MgYXMgc29vbiBhcyB0aGUgY29tcG9uZW50IG1vdW50c1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBPbmx5IHRyaWdnZXIgd2hlbiBzaG91bGRMb2FkIGNoYW5nZXMgdG8gdHJ1ZVxyXG4gICAgaWYgKHNob3VsZExvYWQpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1Bob3RvcyBjb21wb25lbnQgaXMgbm93IHZpc2libGUgYW5kIHJlYWR5IHRvIGxvYWQgZGF0YScpO1xyXG5cclxuICAgICAgLy8gU2V0IGEgZmxhZyB0byB0cmFjayBpbml0aWFsIGxvYWRcclxuICAgICAgc2V0SW5pdGlhbExvYWRDb21wbGV0ZSh0cnVlKTtcclxuXHJcbiAgICAgIC8vIElNTUVESUFURSBBUEkgcmVxdWVzdCB3aXRoIG5vIGNvbmRpdGlvbnMgb3IgZGVsYXlzXHJcbiAgICAgIGNvbnNvbGUubG9nKCdUcmlnZ2VyaW5nIGluaXRpYWwgcGhvdG9zIGxvYWQgSU1NRURJQVRFTFkuLi4nKTtcclxuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcclxuXHJcbiAgICAgIC8vIFNraXAgdGhlIGZldGNoUGhvdG9zIGZ1bmN0aW9uIGFuZCBtYWtlIHRoZSBBUEkgY2FsbCBkaXJlY3RseSBoZXJlXHJcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XHJcblxyXG4gICAgICBpZiAodG9rZW4pIHtcclxuICAgICAgICAvLyBNYWtlIGRpcmVjdCBBUEkgcmVxdWVzdFxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdNYWtpbmcgZGlyZWN0IEFQSSByZXF1ZXN0IGZvciBwaG90b3MgcGFnZSAxLi4uJyk7XHJcbiAgICAgICAgYXhpb3MuZ2V0PEFwaVJlc3BvbnNlPihgL3Bob3Rvcz9wYWdlPTEmbGltaXQ9MTBgLCB7XHJcbiAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgICAgIC50aGVuKHJlc3BvbnNlID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0FQSSByZXNwb25zZSByZWNlaXZlZCBmb3IgcGhvdG9zIHBhZ2UgMScpO1xyXG4gICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLnBob3Rvcykge1xyXG4gICAgICAgICAgICAgIHNldFBob3RvcyhyZXNwb25zZS5kYXRhLnBob3Rvcyk7XHJcbiAgICAgICAgICAgICAgc2V0SGFzTW9yZShyZXNwb25zZS5kYXRhLm5leHRfcGFnZSk7XHJcbiAgICAgICAgICAgICAgc2V0UGFnZSgxKTtcclxuICAgICAgICAgICAgICBzZXRFcnJvcihudWxsKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBzZXRQaG90b3MoW10pO1xyXG4gICAgICAgICAgICAgIHNldEVycm9yKCdObyBwaG90b3MgZm91bmQnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSlcclxuICAgICAgICAgIC5jYXRjaChlcnIgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdEaXJlY3QgQVBJIHJlcXVlc3QgZmFpbGVkOicsIGVycik7XHJcbiAgICAgICAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBwaG90b3MnKTtcclxuICAgICAgICAgICAgc2V0UGhvdG9zKFtdKTtcclxuICAgICAgICAgIH0pXHJcbiAgICAgICAgICAuZmluYWxseSgoKSA9PiB7XHJcbiAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnSW5pdGlhbCBsb2FkaW5nIGNvbXBsZXRlJyk7XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgICBzZXRFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQnKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFtzaG91bGRMb2FkXSk7IC8vIE9ubHkgZGVwZW5kIG9uIHNob3VsZExvYWRcclxuXHJcbiAgLy8gU2V0dXAgSW50ZXJzZWN0aW9uIE9ic2VydmVyIGZvciBob3Jpem9udGFsIGxhenkgbG9hZGluZ1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBPbmx5IHNldCB1cCBvYnNlcnZlciBhZnRlciBpbml0aWFsIGxvYWQgYW5kIGlmIHRoZXJlJ3MgbW9yZSBjb250ZW50IHRvIGZldGNoXHJcbiAgICBpZiAoIWluaXRpYWxMb2FkQ29tcGxldGUgfHwgIWhhc01vcmUpIHJldHVybjtcclxuXHJcbiAgICAvLyBEaXNjb25uZWN0IHByZXZpb3VzIG9ic2VydmVyIGlmIGl0IGV4aXN0c1xyXG4gICAgaWYgKG9ic2VydmVyUmVmLmN1cnJlbnQpIHtcclxuICAgICAgb2JzZXJ2ZXJSZWYuY3VycmVudC5kaXNjb25uZWN0KCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdEaXNjb25uZWN0ZWQgcHJldmlvdXMgaW50ZXJzZWN0aW9uIG9ic2VydmVyJyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ3JlYXRlIG5ldyBvYnNlcnZlclxyXG4gICAgb2JzZXJ2ZXJSZWYuY3VycmVudCA9IG5ldyBJbnRlcnNlY3Rpb25PYnNlcnZlcihcclxuICAgICAgKGVudHJpZXMpID0+IHtcclxuICAgICAgICAvLyBJZiB0aGUgdHJpZ2dlciBlbGVtZW50IGlzIHZpc2libGUgYW5kIHdlJ3JlIG5vdCBhbHJlYWR5IGxvYWRpbmdcclxuICAgICAgICBpZiAoZW50cmllc1swXT8uaXNJbnRlcnNlY3RpbmcgJiYgIWxvYWRpbmcgJiYgIWxvYWRpbmdNb3JlICYmIGhhc01vcmUpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdMb2FkIG1vcmUgdHJpZ2dlciBpcyB2aXNpYmxlLCBsb2FkaW5nIG5leHQgcGFnZS4uLicpO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0ludGVyc2VjdGlvbiByYXRpbzonLCBlbnRyaWVzWzBdLmludGVyc2VjdGlvblJhdGlvKTtcclxuICAgICAgICAgIGNvbnN0IG5leHRQYWdlID0gcGFnZSArIDE7XHJcbiAgICAgICAgICBmZXRjaFBob3RvcyhuZXh0UGFnZSwgZmFsc2UpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuICAgICAgLy8gVXNlIHRoZSBzY3JvbGwgY29udGFpbmVyIGFzIHRoZSByb290IGZvciB0aGUgaW50ZXJzZWN0aW9uIG9ic2VydmVyXHJcbiAgICAgIHtcclxuICAgICAgICByb290OiBzY3JvbGxDb250YWluZXJSZWYuY3VycmVudCxcclxuICAgICAgICB0aHJlc2hvbGQ6IDAuMSwgLy8gVHJpZ2dlciB3aGVuIDEwJSBvZiB0aGUgZWxlbWVudCBpcyB2aXNpYmxlXHJcbiAgICAgICAgcm9vdE1hcmdpbjogJzBweCAxNTBweCAwcHggMHB4JyAvLyBBZGQgbWFyZ2luIHRvIHRoZSByaWdodCB0byBsb2FkIGVhcmxpZXJcclxuICAgICAgfVxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBTdGFydCBvYnNlcnZpbmcgdGhlIHRyaWdnZXIgZWxlbWVudFxyXG4gICAgaWYgKGxvYWRNb3JlVHJpZ2dlclJlZi5jdXJyZW50KSB7XHJcbiAgICAgIG9ic2VydmVyUmVmLmN1cnJlbnQub2JzZXJ2ZShsb2FkTW9yZVRyaWdnZXJSZWYuY3VycmVudCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdOb3cgb2JzZXJ2aW5nIGxvYWQgbW9yZSB0cmlnZ2VyIGVsZW1lbnQnKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnNvbGUud2FybignTG9hZCBtb3JlIHRyaWdnZXIgZWxlbWVudCBub3QgZm91bmQnKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpZiAob2JzZXJ2ZXJSZWYuY3VycmVudCkge1xyXG4gICAgICAgIG9ic2VydmVyUmVmLmN1cnJlbnQuZGlzY29ubmVjdCgpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdEaXNjb25uZWN0ZWQgaW50ZXJzZWN0aW9uIG9ic2VydmVyJyk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSwgW2hhc01vcmUsIGxvYWRpbmcsIGxvYWRpbmdNb3JlLCBwYWdlLCBpbml0aWFsTG9hZENvbXBsZXRlXSk7XHJcblxyXG4gIC8vIEdldCBhcHByb3ByaWF0ZSBpbWFnZSBzb3VyY2UgZm9yIGEgcGhvdG9cclxuICBjb25zdCBnZXRJbWFnZVNvdXJjZSA9IChwaG90bzogUGhvdG8pOiBzdHJpbmcgPT4ge1xyXG4gICAgaWYgKHBob3RvLnBob3RvX3VybCkge1xyXG4gICAgICByZXR1cm4gcGhvdG8ucGhvdG9fdXJsO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuICcvcGljcy9wbGFjZWhvbGRlci5zdmcnO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJtYi04XCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTQgcHgtMlwiPlxyXG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LWludGVyIHRleHQtWzIwcHhdIGxlYWRpbmctWzE4cHhdIGZvbnQtc2VtaWJvbGQgdGV4dC1ibGFja1wiPlxyXG4gICAgICAgICAgUGhvdG9zXHJcbiAgICAgICAgPC9oMj5cclxuICAgICAgICA8YVxyXG4gICAgICAgICAgaHJlZj1cIi9ob21lL3Bob3Rvc1wiXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC1zbSBmb250LW1lZGl1bSBob3Zlcjp1bmRlcmxpbmUgei0xMCByZWxhdGl2ZSBtci0xIG1sLWF1dG9cIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIFNlZSBhbGxcclxuICAgICAgICA8L2E+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIExvYWRpbmcgc3RhdGUgKi99XHJcbiAgICAgIHtsb2FkaW5nICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTEwIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1ibG9jayB3LTYgaC02IGJvcmRlci0yIGJvcmRlci1yZWQtNTAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbXItMlwiPjwvZGl2PlxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgcGhvdG9zLi4uPC9zcGFuPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIEVycm9yIHN0YXRlICovfVxyXG4gICAgICB7ZXJyb3IgJiYgIWxvYWRpbmcgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMTAgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIG1iLTRcIj57ZXJyb3J9PC9kaXY+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnUmV0cnlpbmcgcGhvdG9zIGxvYWQuLi4nKTtcclxuICAgICAgICAgICAgICBzZXRFcnJvcihudWxsKTtcclxuICAgICAgICAgICAgICBmZXRjaFBob3RvcygxLCB0cnVlKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLXJlZC01MDAgdGV4dC13aGl0ZSByb3VuZGVkIGhvdmVyOmJnLXJlZC02MDAgdGV4dC1zbVwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIFJldHJ5XHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHsvKiBIb3Jpem9udGFsIHNjcm9sbCBjb250YWluZXIgKi99XHJcbiAgICAgIHshbG9hZGluZyAmJiAoXHJcbiAgICAgICAgPGRpdlxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvIHNjcm9sbGJhci1oaWRlIHJlbGF0aXZlXCJcclxuICAgICAgICAgIHJlZj17c2Nyb2xsQ29udGFpbmVyUmVmfVxyXG4gICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgc2Nyb2xsQmVoYXZpb3I6ICdzbW9vdGgnLFxyXG4gICAgICAgICAgICBXZWJraXRPdmVyZmxvd1Njcm9sbGluZzogJ3RvdWNoJywgLy8gRm9yIHNtb290aGVyIHNjcm9sbGluZyBvbiBpT1NcclxuICAgICAgICAgICAgbWluSGVpZ2h0OiBwaG90b3MubGVuZ3RoID09PSAwICYmICFlcnJvciA/ICcyMjBweCcgOiAnYXV0bydcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgey8qIEVtcHR5IHN0YXRlIG1lc3NhZ2Ugd2hlbiBubyBwaG90b3MgKi99XHJcbiAgICAgICAgICB7cGhvdG9zLmxlbmd0aCA9PT0gMCAmJiAhZXJyb3IgJiYgIWxvYWRpbmcgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtWzIyMHB4XSB3LWZ1bGxcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5ObyBwaG90b3MgYXZhaWxhYmxlPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTQgcGItNCBmbGV4LW5vd3JhcFwiPlxyXG4gICAgICAgICAgICB7cGhvdG9zLm1hcCgocGhvdG8sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAga2V5PXtgJHtwaG90by5waG90b19pZH0tJHtpbmRleH1gfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBzaGFkb3ctbWQgcmVsYXRpdmUgaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMjUwcHgnLCBoZWlnaHQ6ICcyMDBweCcgfX1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBgL2hvbWUvcGhvdG9zLyR7cGhvdG8ucGhvdG9faWR9YDtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgey8qIFVzZXIgYXZhdGFyICovfVxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiBsZWZ0LTIgei0xMFwiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiBuYXZpZ2F0ZVRvVXNlclByb2ZpbGUocGhvdG8udXNlcl9pZCwgcGhvdG8udXNlcl9uYW1lLCBlKX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFVzZXJBdmF0YXJcclxuICAgICAgICAgICAgICAgICAgICB1c2VybmFtZT17cGhvdG8udXNlcl9uYW1lIHx8IFwidXNlclwifVxyXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgaXNHcmFkaWVudEJvcmRlcj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZVRvVXNlclByb2ZpbGUocGhvdG8udXNlcl9pZCwgcGhvdG8udXNlcl9uYW1lKX1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiBQaG90byAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIGgtZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgICAgICBzcmM9e2dldEltYWdlU291cmNlKHBob3RvKX1cclxuICAgICAgICAgICAgICAgICAgICBhbHQ9e3Bob3RvLnBob3RvX25hbWUgfHwgXCJQaG90b1wifVxyXG4gICAgICAgICAgICAgICAgICAgIGZpbGxcclxuICAgICAgICAgICAgICAgICAgICBzaXplcz1cIihtYXgtd2lkdGg6IDY0MHB4KSAyNTBweCwgMjUwcHhcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9iamVjdC1jb3ZlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgey4uLihpbmRleCA8IDQgPyB7IHByaW9yaXR5OiB0cnVlIH0gOiB7IGxvYWRpbmc6ICdsYXp5JyB9KX0gLy8gUHJpb3JpdHkgZm9yIGZpcnN0IGZvdXIsIGxhenkgbG9hZGluZyBmb3IgcmVzdFxyXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiYmx1clwiIC8vIFNob3cgYmx1ciBwbGFjZWhvbGRlciB3aGlsZSBsb2FkaW5nXHJcbiAgICAgICAgICAgICAgICAgICAgYmx1ckRhdGFVUkw9XCJkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlOekF3SWlCb1pXbG5hSFE5SWpRM05TSWdkbVZ5YzJsdmJqMGlNUzR4SWlCNGJXeHVjejBpYUhSMGNEb3ZMM2QzZHk1M015NXZjbWN2TWpBd01DOXpkbWNpSUhodGJHNXpPbmhzYVc1clBTSm9kSFJ3T2k4dmQzZDNMbmN6TG05eVp5OHhPVGs1TDNoc2FXNXJJajQ4WkdWbWN6NDhiR2x1WldGeVIzSmhaR2xsYm5RZ2FXUTlJbWNpUGp4emRHOXdJSE4wYjNBdFkyOXNiM0k5SWlObFpXVWlJRzltWm5ObGREMGlNakFsSWlBdlBqeHpkRzl3SUhOMGIzQXRZMjlzYjNJOUlpTm1abVlpSUc5bVpuTmxkRDBpTlRBbElpQXZQanh6ZEc5d0lITjBiM0F0WTI5c2IzSTlJaU5sWldVaUlHOW1abk5sZEQwaU56QWxJaUF2UGp3dmJHbHVaV0Z5UjNKaFpHbGxiblErUEM5a1pXWnpQanh5WldOMElIZHBaSFJvUFNJM01EQWlJR2hsYVdkb2REMGlORGMxSWlCbWFXeHNQU0lqWldWbElpQXZQanh5WldOMElHbGtQU0p5SWlCM2FXUjBhRDBpTnpBd0lpQm9aV2xuYUhROUlqUTNOU0lnWm1sc2JEMGlkWEpzS0NObktTSWdMejQ4WVc1cGJXRjBaU0I0YkdsdWF6cG9jbVZtUFNJamNpSWdZWFIwY21saWRYUmxUbUZ0WlQwaWVDSWdabkp2YlQwaUxUY3dNQ0lnZEc4OUlqY3dNQ0lnWkhWeVBTSXhjeUlnY21Wd1pXRjBRMjkxYm5ROUltbHVaR1ZtYVc1cGRHVWlJQ0F2UGp3dmMzWm5QZz09XCIgLy8gQmFzZTY0IGVuY29kZWQgU1ZHIGxvYWRpbmcgYW5pbWF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGltZ0VsZW1lbnQgPSBlLnRhcmdldCBhcyBIVE1MSW1hZ2VFbGVtZW50O1xyXG4gICAgICAgICAgICAgICAgICAgICAgaWYgKGltZ0VsZW1lbnQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5zcmMgPSAnL3BpY3MvcGxhY2Vob2xkZXIuc3ZnJztcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFBob3RvIGluZm8gKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0wIGxlZnQtMCByaWdodC0wIHAtMiBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svODAgdG8tdHJhbnNwYXJlbnQgdGV4dC13aGl0ZVwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdHJ1bmNhdGVcIj57cGhvdG8ucGhvdG9fbmFtZX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW5cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGhvdmVyOnVuZGVybGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gbmF2aWdhdGVUb1VzZXJQcm9maWxlKHBob3RvLnVzZXJfaWQsIHBob3RvLnVzZXJfbmFtZSwgZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAge3Bob3RvLnVzZXJfbmFtZX1cclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7cGhvdG8ucGhvdG9fbGlrZXMgPyBgJHtwaG90by5waG90b19saWtlc30gbGlrZXNgIDogJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApKX1cclxuXHJcbiAgICAgICAgICAgIHsvKiBMb2FkIG1vcmUgdHJpZ2dlciBlbGVtZW50IC0gdGhpcyBpcyB3aGF0IEludGVyc2VjdGlvbk9ic2VydmVyIHdhdGNoZXMgKi99XHJcbiAgICAgICAgICAgIHtoYXNNb3JlICYmIChcclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICByZWY9e2xvYWRNb3JlVHJpZ2dlclJlZn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgdy0xMCBoLWZ1bGwgb3BhY2l0eS0wXCJcclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxyXG4gICAgICAgICAgICAgICAgICAvLyBBZGQgZGVidWcgb3V0bGluZSBpbiBkZXZlbG9wbWVudFxyXG4gICAgICAgICAgICAgICAgICBvdXRsaW5lOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyA/ICcxcHggZGFzaGVkIHJnYmEoMjU1LCAwLCAwLCAwLjMpJyA6ICdub25lJ1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICBkYXRhLXRlc3RpZD1cInBob3Rvcy1sb2FkLW1vcmUtdHJpZ2dlclwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgIHsvKiBMb2FkaW5nIGluZGljYXRvciAtIG9ubHkgc2hvdyB3aGVuIGxvYWRpbmcgbW9yZSAqL31cclxuICAgICAgICAgICAge2xvYWRpbmdNb3JlICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLXctWzEwMHB4XSBoLVsyMDBweF1cIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIHctNiBoLTYgYm9yZGVyLTIgYm9yZGVyLXJlZC01MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIG1vcmUuLi48L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIDxzdHlsZSBqc3g+e2BcclxuICAgICAgICAuc2Nyb2xsYmFyLWhpZGU6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICAgICAgICAgIGRpc3BsYXk6IG5vbmU7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC5zY3JvbGxiYXItaGlkZSB7XHJcbiAgICAgICAgICAtbXMtb3ZlcmZsb3ctc3R5bGU6IG5vbmU7XHJcbiAgICAgICAgICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7XHJcbiAgICAgICAgfVxyXG4gICAgICBgfTwvc3R5bGU+XHJcbiAgICA8L3NlY3Rpb24+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFBob3RvczsiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIkltYWdlIiwiVXNlckF2YXRhciIsImF4aW9zIiwidXNlUm91dGVyIiwiUGhvdG9zIiwic2hvdWxkTG9hZCIsInJvdXRlciIsInBob3RvcyIsInNldFBob3RvcyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInBhZ2UiLCJzZXRQYWdlIiwiaGFzTW9yZSIsInNldEhhc01vcmUiLCJsb2FkaW5nTW9yZSIsInNldExvYWRpbmdNb3JlIiwiaW5pdGlhbExvYWRDb21wbGV0ZSIsInNldEluaXRpYWxMb2FkQ29tcGxldGUiLCJzY3JvbGxDb250YWluZXJSZWYiLCJsb2FkTW9yZVRyaWdnZXJSZWYiLCJvYnNlcnZlclJlZiIsIm5hdmlnYXRlVG9Vc2VyUHJvZmlsZSIsInVzZXJJZCIsInVzZXJuYW1lIiwiZXZlbnQiLCJzdG9wUHJvcGFnYXRpb24iLCJwdXNoIiwiY29uc29sZSIsIndhcm4iLCJnZXRGYWxsYmFja1Bob3RvcyIsImZldGNoUGhvdG9zIiwicGFnZU51bWJlciIsImlzSW5pdGlhbExvYWQiLCJsb2ciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJyZXNwb25zZSIsImdldCIsImhlYWRlcnMiLCJBdXRob3JpemF0aW9uIiwic3RhdHVzIiwiZGF0YSIsImxlbmd0aCIsInByb2Nlc3NlZFBob3RvcyIsIm1hcCIsInBob3RvIiwicGhvdG9fdXJsIiwicGhvdG9faWQiLCJwcmV2IiwibmV4dF9wYWdlIiwiZXJyIiwidGhlbiIsImNhdGNoIiwiZmluYWxseSIsImN1cnJlbnQiLCJkaXNjb25uZWN0IiwiSW50ZXJzZWN0aW9uT2JzZXJ2ZXIiLCJlbnRyaWVzIiwiaXNJbnRlcnNlY3RpbmciLCJpbnRlcnNlY3Rpb25SYXRpbyIsIm5leHRQYWdlIiwicm9vdCIsInRocmVzaG9sZCIsInJvb3RNYXJnaW4iLCJvYnNlcnZlIiwiZ2V0SW1hZ2VTb3VyY2UiLCJzZWN0aW9uIiwiZGl2IiwiaDIiLCJhIiwiaHJlZiIsInNwYW4iLCJidXR0b24iLCJvbkNsaWNrIiwicmVmIiwic3R5bGUiLCJzY3JvbGxCZWhhdmlvciIsIldlYmtpdE92ZXJmbG93U2Nyb2xsaW5nIiwibWluSGVpZ2h0IiwiaW5kZXgiLCJ3aWR0aCIsImhlaWdodCIsIndpbmRvdyIsImxvY2F0aW9uIiwiZSIsInVzZXJfaWQiLCJ1c2VyX25hbWUiLCJzaXplIiwiaXNHcmFkaWVudEJvcmRlciIsInNyYyIsImFsdCIsInBob3RvX25hbWUiLCJmaWxsIiwic2l6ZXMiLCJjbGFzc05hbWUiLCJwcmlvcml0eSIsInBsYWNlaG9sZGVyIiwiYmx1ckRhdGFVUkwiLCJvbkVycm9yIiwiaW1nRWxlbWVudCIsInRhcmdldCIsInBob3RvX2xpa2VzIiwicG9zaXRpb24iLCJvdXRsaW5lIiwicHJvY2VzcyIsImFyaWEtaGlkZGVuIiwiZGF0YS10ZXN0aWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Photos.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/bookmark.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Bookmark)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.479.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z\",\n            key: \"1fy3hk\"\n        }\n    ]\n];\nconst Bookmark = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Bookmark\", __iconNode);\n //# sourceMappingURL=bookmark.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./services/myWeddingService.ts":
/*!**************************************!*\
  !*** ./services/myWeddingService.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkContentSavedStatus: () => (/* binding */ checkContentSavedStatus),\n/* harmony export */   getMyWeddingContent: () => (/* binding */ getMyWeddingContent),\n/* harmony export */   removeContentFromMyWedding: () => (/* binding */ removeContentFromMyWedding),\n/* harmony export */   saveContentToMyWedding: () => (/* binding */ saveContentToMyWedding)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\nfunction getAuthHeaders() {\n    const token =  true && (localStorage.getItem('token') || sessionStorage.getItem('token'));\n    return token ? {\n        Authorization: \"Bearer \".concat(token)\n    } : {};\n}\nasync function saveContentToMyWedding(param) {\n    let { user_id, content_id, content_type, title = '' } = param;\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE, \"/save-to-my-wedding\"), {\n        user_id,\n        content_id,\n        content_type,\n        title\n    }, {\n        headers: {\n            ...getAuthHeaders(),\n            'Content-Type': 'application/json'\n        }\n    });\n}\nasync function removeContentFromMyWedding(param) {\n    let { user_id, content_id } = param;\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"\".concat(API_BASE, \"/remove-from-my-wedding\"), {\n        data: {\n            user_id,\n            content_id\n        },\n        headers: {\n            ...getAuthHeaders(),\n            'Content-Type': 'application/json'\n        }\n    });\n}\nasync function checkContentSavedStatus(param) {\n    let { user_id, content_id } = param;\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(API_BASE, \"/check-video-saved\"), {\n        params: {\n            user_id,\n            content_id\n        },\n        headers: getAuthHeaders()\n    });\n}\nasync function getMyWeddingContent(param) {\n    let { user_id, content_type, page = 1, limit = 20 } = param;\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(API_BASE, \"/my-wedding-videos\"), {\n        params: {\n            user_id,\n            content_type,\n            page,\n            limit\n        },\n        headers: getAuthHeaders()\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/myWeddingService.ts\n"));

/***/ })

});