"use client";
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
    TopNavigation,
    SideNavigation,
    MobileNavigation,
} from "../HomeDashboard/Navigation";
import axios from "../../services/axiosConfig";
import UserAvatar from "../HomeDashboard/UserAvatar";
import { useAuth } from "../../contexts/AuthContext";
import EditProfileModal from "./EditProfileModal";

// Define interfaces for our data types
interface Story {
    content_id: string;
    content_name: string;
    content_url: string;
    content_description?: string;
    thumbnail_url?: string;
    duration?: number;
    created_at: string;
    user_name: string;
    content_type: 'video' | 'photo';
    is_own_content: boolean;
    viewed?: boolean; // Track if the user has viewed this story
}

interface StoriesResponse {
    stories: Story[];
    next_page: boolean;
    total_count: number;
    current_page: number;
}

interface FlashVideo {
    video_id: string;
    video_name: string;
    video_url: string;
    video_description?: string;
    video_thumbnail?: string;
    video_duration?: number;
    video_category?: string;
    created_at: string;
    user_name?: string;
    is_own_content?: boolean;
    video_views?: number;
    video_likes?: number;
    video_comments?: number;
    content_type?: string;
    unique_id?: string;
}

interface GlimpseVideo {
    video_id: string;
    video_name: string;
    video_url: string;
    video_description?: string;
    video_thumbnail?: string;
    video_duration?: number;
    video_category?: string;
    created_at: string;
    user_name?: string;
    user_id?: string;
    is_own_content?: boolean;
    video_views?: number;
    video_likes?: number;
    video_comments?: number;
    content_type?: string;
    unique_id?: string;
}

interface PhotoItem {
    photo_id: string;
    photo_name: string;
    photo_url: string;
    photo_description?: string;
    photo_thumbnail?: string;
    created_at: string;
    user_name?: string;
    user_id?: string;
    is_own_content?: boolean;
    photo_views?: number;
    photo_likes?: number;
    photo_comments?: number;
    content_type?: string;
    unique_id?: string;
}

interface ApiResponse<T> {
    data: T[];
    next_page: boolean;
    total_count: number;
    current_page: number;
}

interface FlashesResponse {
    flashes: FlashVideo[];
    next_page: boolean;
    total_count: number;
    current_page: number;
}

interface GlimpsesResponse {
    glimpses: GlimpseVideo[];
    next_page: boolean;
    total_count: number;
    current_page: number;
}

interface PhotosResponse {
    photos: PhotoItem[];
    next_page: boolean;
    total_count: number;
    current_page: number;
}

interface AllPostsResponse {
    posts: (FlashVideo | GlimpseVideo | PhotoItem)[];
    next_page: boolean;
    total_count: number;
    current_page: number;
}

interface UserProfileProps {
    userId?: string; // Optional user ID for viewing other profiles
}

const UserProfile: React.FC<UserProfileProps> = ({ userId: externalUserId }) => {
    const router = useRouter();
    const { logout, userProfile, updateProfile } = useAuth();
    const [sidebarExpanded, setSidebarExpanded] = useState(false);
    const [activeTab, setActiveTab] = useState('posts');
    const [contentType, setContentType] = useState('all-posts'); // 'all-posts', 'flashes', 'glimpses', 'movies', 'photos'
    // Removed unused state
    const [showProfileMenu, setShowProfileMenu] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const contentTypeDropdownRef = useRef<HTMLDivElement>(null);

    // State to track if this is the current user's profile or another user's
    const [isOwnProfile, setIsOwnProfile] = useState<boolean>(true);
    // State to track if the current user is following the profile user
    const [isFollowing, setIsFollowing] = useState<boolean>(false);

    // State for user profile data
    const [userProfileData, setUserProfileData] = useState<any>(null);

    // State for edit profile modal
    const [isEditProfileModalOpen, setIsEditProfileModalOpen] = useState<boolean>(false);

    // State for calendar modal
    const [calendarModalOpen, setCalendarModalOpen] = useState<boolean>(false);
    const [selectedDate, setSelectedDate] = useState<Date>(new Date());
    const [eventTitle, setEventTitle] = useState<string>('');

    // State for user content
    const [userFlashes, setUserFlashes] = useState<FlashVideo[]>([]);
    const [userGlimpses, setUserGlimpses] = useState<GlimpseVideo[]>([]);
    const [userPhotos, setUserPhotos] = useState<PhotoItem[]>([]);
    const [userStories, setUserStories] = useState<Story[]>([]);
    const [userMovies, setUserMovies] = useState<any[]>([]);
    const [allUserContent, setAllUserContent] = useState<(FlashVideo | GlimpseVideo | PhotoItem)[]>([]);
    const [totalPostsCount, setTotalPostsCount] = useState<number>(0);

    // State for lazy loading
    const [visiblePosts, setVisiblePosts] = useState<(FlashVideo | GlimpseVideo | PhotoItem)[]>([]);
    const [visibleFlashes, setVisibleFlashes] = useState<FlashVideo[]>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [loadingMore, setLoadingMore] = useState<boolean>(false);
    const [hasMore, setHasMore] = useState<boolean>(true);
    const postsPerPage = 10; // Number of posts to load at once
    const postsContainerRef = useRef<HTMLDivElement>(null);

    // Refs to track previous values
    const prevContentType = useRef<string>(contentType);
    const prevActiveTab = useRef<string>(activeTab);

    // State for tracking pagination of different content types
    const [hasMoreFlashes, setHasMoreFlashes] = useState<boolean>(false);
    const [hasMoreGlimpses, setHasMoreGlimpses] = useState<boolean>(false);
    const [hasMorePhotos, setHasMorePhotos] = useState<boolean>(false);
    const [hasMoreMovies, setHasMoreMovies] = useState<boolean>(false);
    const [hasMoreStories, setHasMoreStories] = useState<boolean>(false);
    const [flashesPage, setFlashesPage] = useState<number>(1);
    const [glimpsesPage, setGlimpsesPage] = useState<number>(1);
    const [photosPage, setPhotosPage] = useState<number>(1);
    const [moviesPage, setMoviesPage] = useState<number>(1);
    const [storiesPage, setStoriesPage] = useState<number>(1);

    // Loading and error states
    const [loading, setLoading] = useState(true);
    const [profileLoading, setProfileLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    // We still need userId for API calls
    const [userId, setUserId] = useState<string | null>(null);
    const [token, setToken] = useState<string | null>(null);
    const [userName, setUserName] = useState<string>('User');

    // Get current user ID and token from localStorage and AuthContext
    useEffect(() => {
        // Check for authentication token
        const checkAuth = () => {
            // Try multiple possible token keys
            const storedToken = localStorage.getItem('token') ||
                localStorage.getItem('jwt_token') ||
                localStorage.getItem('wedzat_token');

            if (!storedToken) {
                console.warn('No authentication token found in localStorage');
                setError('Authentication required');
                router.push('/');
                return;
            }

            // First check if we have user profile from AuthContext
            if (userProfile) {
                console.log('Using user profile from AuthContext:', userProfile);
                setUserName(userProfile.name || 'User');
            } else {
                // Fallback: Try to get user info from JWT token if it's a JWT
                try {
                    const tokenParts = storedToken.split('.');
                    if (tokenParts.length === 3) {
                        // It's a JWT, decode the payload
                        const payload = JSON.parse(atob(tokenParts[1]));

                        // Extract user ID and name if available
                        if (payload.user_id) {
                            setUserId(payload.user_id);
                        }

                        // Set user name if available in token
                        if (payload.name) {
                            setUserName(payload.name);
                        } else if (payload.username) {
                            setUserName(payload.username);
                        } else if (payload.email) {
                            // Use email as fallback, but remove domain part
                            const emailName = payload.email.split('@')[0];
                            setUserName(emailName);
                        }
                    } else {
                        // Not a JWT, try to get user info from localStorage
                        const storedUserName = localStorage.getItem('user_name');
                        if (storedUserName) {
                            setUserName(storedUserName);
                        }
                    }
                } catch (err) {
                    console.error('Error extracting user info from token:', err);
                }
            }

            setToken(storedToken);

            // Once we have the user ID and token, fetch user content
            fetchUserContent();
        };

        checkAuth();

        // Add event listener for storage changes (e.g., token deletion)
        const handleStorageChange = (e: StorageEvent) => {
            if (e.key === 'token' || e.key === 'jwt_token' || e.key === 'wedzat_token') {
                if (!e.newValue) {
                    console.warn('Token removed, redirecting to login');
                    router.push('/');
                }
            }
        };

        window.addEventListener('storage', handleStorageChange);

        return () => {
            window.removeEventListener('storage', handleStorageChange);
        };
    }, [router, userProfile, externalUserId]);

    // Function to fetch user profile data
    const fetchUserProfile = useCallback(async (storedToken: string, targetUserId?: string) => {
        try {
            setProfileLoading(true);
            console.log('Fetching user profile data...', targetUserId ? `for user ID: ${targetUserId}` : 'for current user');

            // Determine if this is the current user's profile or another user's
            const isCurrentUserProfile = !targetUserId;
            setIsOwnProfile(isCurrentUserProfile);

            // Make API call to get user profile using the profile_api.py endpoint
            // Use the correct endpoint format with user_id parameter
            const url = `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user${targetUserId ? `?user_id=${targetUserId}` : ''}`;
            console.log(`Fetching profile from URL: ${url}`);

            const response = await axios.get(url, {
                headers: { Authorization: `Bearer ${storedToken}` }
            });

            const profileData = response.data;
            console.log('User profile data received:', profileData);

            // Update state with profile data
            setUserProfileData(profileData);

            // Update user name - make sure to use the correct field from the response
            if (profileData.name || profileData.user_name) {
                const displayName = profileData.name || profileData.user_name;
                console.log(`Setting user name to: ${displayName}`);
                setUserName(displayName);
            }

            // Update following status if viewing another user's profile
            if (!isCurrentUserProfile && profileData.is_following !== undefined) {
                console.log(`Setting following status to: ${profileData.is_following}`);
                setIsFollowing(profileData.is_following);
            }

            // Return the profile data for further processing
            return profileData;
        } catch (err) {
            console.error('Error fetching user profile:', err);
            setError('Failed to load user profile');
            return null;
        } finally {
            setProfileLoading(false);
        }
    }, []);

    // Function to fetch all user content - wrapped in useCallback to prevent infinite loops
    const fetchUserContent = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            console.log('Fetching user content...', externalUserId ? `for user ID: ${externalUserId}` : 'for current user');

            // Get token from localStorage - try multiple possible keys
            const storedToken = localStorage.getItem('token') ||
                localStorage.getItem('jwt_token') ||
                localStorage.getItem('wedzat_token');

            if (!storedToken) {
                console.warn('No authentication token found');
                setError('Authentication required');
                setLoading(false);
                router.push('/');
                return;
            }

            // Store token in state for later use
            setToken(storedToken);

            // If we have userProfile from AuthContext and this is our own profile, update the userName
            if (!externalUserId && userProfile && userProfile.name) {
                setUserName(userProfile.name);
                console.log('Updated username from AuthContext:', userProfile.name);
            }

            // Check if token is expired (if it's a JWT)
            try {
                const tokenParts = storedToken.split('.');
                if (tokenParts.length === 3) {
                    // It's a JWT, decode the payload
                    const payload = JSON.parse(atob(tokenParts[1]));
                    const expTime = payload.exp * 1000; // Convert to milliseconds

                    if (expTime < Date.now()) {
                        console.warn('Token is expired');
                        setError('Authentication expired');
                        setLoading(false);
                        localStorage.removeItem('token');
                        localStorage.removeItem('jwt_token');
                        localStorage.removeItem('wedzat_token');
                        router.push('/');
                        return;
                    }
                }
            } catch (err) {
                console.error('Error checking token expiration:', err);
                // Continue anyway, the API call will fail if the token is invalid
            }

            // First fetch user profile data
            console.log(`Fetching profile for user ID: ${externalUserId || 'current user'}`);
            await fetchUserProfile(storedToken, externalUserId);

            // Then fetch all user content by making multiple requests if necessary
            // We'll implement pagination to get ALL posts without limits
            console.log(`Fetching content for user ID: ${externalUserId || 'current user'}`);
            await fetchAllUserContent(storedToken, externalUserId);
        } catch (err) {
            console.error('Error fetching user content:', err);
            setError('Failed to load user content');
        } finally {
            setLoading(false);
            console.log('Finished loading user content');
        }
    }, [router, userProfile, fetchUserProfile, externalUserId]);

    // Only fetch content when token changes or on initial load
    // We don't need this effect anymore since fetchUserContent is already called in the auth check
    // and it's properly memoized with useCallback

    // Function to fetch initial user content (first page only)
    const fetchAllUserContent = async (token: string, targetUserId?: string) => {
        try {
            // Arrays to store content
            let allFlashes: FlashVideo[] = [];
            let allGlimpses: GlimpseVideo[] = [];
            let allPhotos: PhotoItem[] = [];
            let allStories: Story[] = [];
            let allMovies: any[] = [];
            let allPosts: (FlashVideo | GlimpseVideo | PhotoItem)[] = [];

            // Create user ID parameter for API calls - only include for other users
            const userIdParam = targetUserId ? `user_id=${targetUserId}` : '';

            // Reset pagination state
            setMoviesPage(1);
            setFlashesPage(1);
            setGlimpsesPage(1);
            setPhotosPage(1);
            setStoriesPage(1);

            // Initially, we only fetch the all-posts content since that's the default view
            console.log('Fetching first page of user content - only all-posts initially');

            // Fetch all posts - first page only
            console.log('Fetching all posts with all-posts API');
            const allPostsResponse = await axios.get<AllPostsResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/all-posts?page=1&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                headers: { Authorization: `Bearer ${token}` }
            }).catch(err => {
                console.error('Error fetching all posts:', err);
                return { data: { posts: [], total_count: 0, next_page: false, current_page: 1 } };
            });

            allPosts = allPostsResponse.data.posts || [];

            // Set hasMore flags based on the all-posts response
            // We'll fetch the specific content types only when the user selects them from the dropdown
            setHasMoreFlashes(allPostsResponse.data.next_page || false);
            setHasMoreGlimpses(allPostsResponse.data.next_page || false);
            setHasMorePhotos(allPostsResponse.data.next_page || false);
            setHasMoreMovies(allPostsResponse.data.next_page || false);
            setHasMoreStories(allPostsResponse.data.next_page || false);

            // Extract flashes from all posts if available
            allFlashes = allPosts.filter(
                (post): post is FlashVideo =>
                    (post.content_type === 'flash' || ('video_subtype' in post && post.video_subtype === 'flash')) &&
                    'video_id' in post && 'video_name' in post && 'video_url' in post
            );
            console.log(`Extracted ${allFlashes.length} flashes from all posts`);

            // Initialize other content types as empty arrays
            // They will be fetched only when the user selects them
            allGlimpses = [];
            allPhotos = [];
            allMovies = [];
            allStories = [];

            // Log the total counts
            console.log(`Initial content counts - Flashes: ${allFlashes.length}, Glimpses: ${allGlimpses.length}, Photos: ${allPhotos.length}, Movies: ${allMovies.length}, Stories: ${allStories.length}`);

            // Update state with fetched data
            setUserFlashes(allFlashes);
            setUserGlimpses(allGlimpses);
            setUserPhotos(allPhotos);
            setUserStories(allStories);
            setUserMovies(allMovies);

            // Process all posts from the all-posts API
            const timestamp = Date.now();

            // Process all posts from the all-posts API
            const allPostsWithIds = allPosts.map((post, index) => {
                // Determine content type
                let contentType = 'photo';
                let uniqueId = '';

                // Log the post for debugging
                console.log(`Processing post ${index}:`, post);

                if ('content_type' in post) {
                    // If content_type is already provided by the API, use it
                    contentType = post.content_type as string;
                } else if ('video_id' in post) {
                    if ('video_subtype' in post) {
                        contentType = post.video_subtype as string;
                    }
                    uniqueId = `${contentType}-${post.video_id || index}-${timestamp}-${index}`;
                } else if ('photo_id' in post) {
                    contentType = 'photo';
                    uniqueId = `photo-${post.photo_id || index}-${timestamp}-${index}`;
                }

                // Generate unique ID if not already set
                if (!uniqueId) {
                    // For the new API response format, use content_id
                    if ('content_id' in post && post.content_id) {
                        uniqueId = `${contentType}-${post.content_id}-${timestamp}`;
                        console.log(`Using content_id for unique ID: ${uniqueId}`);
                    } else if ('video_id' in post) {
                        uniqueId = `${contentType}-${post.video_id || index}-${timestamp}`;
                    } else if ('photo_id' in post) {
                        uniqueId = `photo-${post.photo_id || index}-${timestamp}`;
                    } else {
                        uniqueId = `content-${index}-${timestamp}-${Math.random().toString(36).substring(2, 15)}`;
                    }
                    console.log(`Generated unique ID: ${uniqueId}`);
                }

                return {
                    ...post,
                    content_type: contentType,
                    unique_id: uniqueId
                };
            });

            // Process flashes
            const flashesWithIds = allFlashes.map((flash, index) => {
                const uniqueId = `flash-${flash.video_id || index}-${timestamp}-${index}`;
                return {
                    ...flash,
                    content_type: 'flash',
                    unique_id: uniqueId
                };
            });

            // Process glimpses
            const glimpsesWithIds = allGlimpses.map((glimpse, index) => {
                const uniqueId = `glimpse-${glimpse.video_id || index}-${timestamp}-${index}`;
                return {
                    ...glimpse,
                    content_type: 'glimpse',
                    unique_id: uniqueId
                };
            });

            // Process movies
            const moviesWithIds = allMovies.map((movie: any, index) => {
                const uniqueId = `movie-${movie.video_id || index}-${timestamp}-${index}`;
                return {
                    ...movie,
                    content_type: 'movie',
                    unique_id: uniqueId
                };
            });

            // Process photos
            const photosWithIds = allPhotos.map((photo, index) => {
                const uniqueId = `photo-${photo.photo_id || index}-${timestamp}-${index}`;
                return {
                    ...photo,
                    content_type: 'photo',
                    unique_id: uniqueId
                };
            });

            // If we have all posts from the API, use those directly
            if (allPostsWithIds.length > 0) {
                // Update state with all posts from API
                setAllUserContent(allPostsWithIds);
                setVisiblePosts(allPostsWithIds);
            } else {
                // Fallback: Combine all content ensuring no duplicates
                const allContent: (FlashVideo | GlimpseVideo | PhotoItem)[] = [];
                const contentMap = new Map();

                // Add each content type to the map and final array
                [...flashesWithIds, ...glimpsesWithIds, ...moviesWithIds, ...photosWithIds].forEach(item => {
                    // Create a unique key based on content type and ID
                    let idValue = '';
                    if ('video_id' in item && item.video_id) {
                        idValue = item.video_id;
                    } else if ('photo_id' in item && item.photo_id) {
                        idValue = item.photo_id;
                    } else {
                        idValue = Math.random().toString();
                    }

                    const mapKey = `${item.content_type}-${idValue}`;

                    if (!contentMap.has(mapKey)) {
                        contentMap.set(mapKey, true);
                        allContent.push(item);
                    }
                });

                // Sort by creation date (newest first)
                allContent.sort((a, b) => {
                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
                });

                setAllUserContent(allContent);
                setVisiblePosts(allContent);
            }

            // Initialize visible flashes with first batch
            setVisibleFlashes(allFlashes);

            // Set hasMore flag based on API response for all-posts
            const hasMoreValue = allPostsResponse.data.next_page || false;
            console.log(`Initial fetch - Setting hasMore to ${hasMoreValue}, API says next_page: ${allPostsResponse.data.next_page}, total_count: ${allPostsResponse.data.total_count}`);
            setHasMore(hasMoreValue);

            // Reset current page
            setCurrentPage(1);

            // Update the total posts count based on the API response
            const totalCount = allPostsResponse.data.total_count || userProfileData?.posts_count || allUserContent.length;
            console.log(`Setting total posts count to: ${totalCount} (API total_count: ${allPostsResponse.data.total_count})`);
            setTotalPostsCount(totalCount);

            // Store the total count in localStorage to persist between sessions
            localStorage.setItem('user_total_posts', totalCount.toString());
        } catch (error) {
            console.error('Error in fetchAllUserContent:', error);
            setError('Failed to load user content');
        }
    };

    // Load the total posts count from localStorage on initial render
    useEffect(() => {
        const savedTotalPosts = localStorage.getItem('user_total_posts');
        if (savedTotalPosts) {
            const parsedCount = parseInt(savedTotalPosts, 10);
            if (!isNaN(parsedCount) && parsedCount > 0) {
                console.log(`Loaded saved total posts count from localStorage: ${parsedCount}`);
                setTotalPostsCount(parsedCount);
            }
        }
    }, []);

    // Update total posts count whenever allUserContent changes
    useEffect(() => {
        // Only update if we don't have a total count from the API yet
        // or if the current content length is greater than the stored total count
        if (allUserContent.length > 0 && allUserContent.length > totalPostsCount) {
            console.log(`Updating total posts count from ${totalPostsCount} to ${allUserContent.length} based on content length`);
            setTotalPostsCount(allUserContent.length);
            localStorage.setItem('user_total_posts', allUserContent.length.toString());
        } else if (allUserContent.length > 0) {
            console.log(`Content length (${allUserContent.length}) is less than total count (${totalPostsCount}), not updating`);
        }
    }, [allUserContent, totalPostsCount]);

    // Function to load more posts when user scrolls
    const loadMorePosts = useCallback(async () => {
        if (loadingMore || !hasMore) {
            console.log(`Skipping loadMorePosts - loadingMore: ${loadingMore}, hasMore: ${hasMore}`);
            return;
        }

        setLoadingMore(true);
        console.log('Loading more posts...', { currentPage, contentType, activeTab, hasMore });

        try {
            // We'll use currentPage + 1 directly in each section instead of a separate variable
            const token = localStorage.getItem('token') ||
                localStorage.getItem('jwt_token') ||
                localStorage.getItem('wedzat_token');

            if (!token) {
                console.warn('No authentication token found');
                setLoadingMore(false);
                return;
            }

            // Create user ID parameter for API calls - only include for other users
            const userIdParam = externalUserId ? `user_id=${externalUserId}` : '';


            // Load more posts based on active tab and content type
            if (activeTab === 'posts') {
                // For posts tab, we need to fetch based on the selected content type
                if (contentType === 'all-posts') {
                    // Fetch the next page of all posts using the new API
                    const nextPage = currentPage + 1;
                    console.log(`Loading more posts: page ${nextPage}, content type: all-posts`);
                    const apiUrl = `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/all-posts?page=${nextPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`;
                    console.log(`Making API request to: ${apiUrl} (current page: ${currentPage}, next page: ${nextPage})`);
                    const allPostsResponse = await axios.get<AllPostsResponse>(apiUrl, {
                        headers: { Authorization: `Bearer ${token}` }
                    }).catch(err => {
                        console.error(`Error fetching all posts page ${nextPage}:`, err);
                        return { data: { posts: [], total_count: 0, next_page: false, current_page: nextPage } };
                    });

                    console.log(`Received response for page ${nextPage}:`, {
                        postsCount: allPostsResponse.data.posts?.length || 0,
                        hasNextPage: allPostsResponse.data.next_page,
                        totalCount: allPostsResponse.data.total_count,
                        currentPage: allPostsResponse.data.current_page
                    });

                    // Debug the hasMore flag
                    console.log(`Before update - hasMore: ${hasMore}, will set to: ${allPostsResponse.data.next_page || false}`);
                    console.log(`Current content length: ${allUserContent.length}, total count: ${allPostsResponse.data.total_count}`);

                    const postsContent = allPostsResponse.data.posts || [];
                    const hasMorePosts = allPostsResponse.data.next_page || false;

                    // Process and add new posts
                    if (postsContent.length > 0) {
                        const timestamp = Date.now();
                        console.log(`Processing ${postsContent.length} posts`);
                        const newPostsWithIds = postsContent.map((post, index) => {
                            // Determine content type
                            let contentType = 'photo';
                            let uniqueId = '';

                            // Log the post for debugging
                            console.log(`Post ${index} keys:`, Object.keys(post));

                            if ('content_type' in post) {
                                // If content_type is already provided by the API, use it
                                contentType = post.content_type as string;
                                console.log(`Using content_type from API: ${contentType}`);
                            } else if ('video_id' in post) {
                                if ('video_subtype' in post) {
                                    contentType = post.video_subtype as string;
                                    console.log(`Using video_subtype: ${contentType}`);
                                }
                                uniqueId = `${contentType}-${post.video_id || index}-${timestamp}-${index}`;
                            } else if ('photo_id' in post) {
                                contentType = 'photo';
                                uniqueId = `photo-${post.photo_id || index}-${timestamp}-${index}`;
                                console.log(`Detected photo content`);
                            }

                            // Generate unique ID if not already set
                            if (!uniqueId) {
                                // For the new API response format, use content_id
                                if ('content_id' in post && post.content_id) {
                                    uniqueId = `${contentType}-${post.content_id}-${timestamp}`;
                                    console.log(`Using content_id for unique ID: ${uniqueId}`);
                                } else if ('video_id' in post) {
                                    uniqueId = `${contentType}-${post.video_id || index}-${timestamp}`;
                                } else if ('photo_id' in post) {
                                    uniqueId = `photo-${post.photo_id || index}-${timestamp}`;
                                } else {
                                    uniqueId = `content-${index}-${timestamp}-${Math.random().toString(36).substring(2, 15)}`;
                                }
                                console.log(`Generated unique ID: ${uniqueId}`);
                            }

                            return {
                                ...post,
                                content_type: contentType,
                                unique_id: uniqueId
                            };
                        });

                        // Add to all content - make sure we're appending, not replacing
                        const newContent = [...allUserContent, ...newPostsWithIds];
                        console.log(`Updated content: now have ${newContent.length} posts, hasMore: ${hasMorePosts}`);
                        console.log(`Previous content length: ${allUserContent.length}, new items: ${newPostsWithIds.length}`);

                        // Important: Update both state variables to ensure content is properly displayed
                        setAllUserContent(newContent);
                        setVisiblePosts(newContent);

                        // Always update hasMore based on API response
                        console.log(`Setting hasMore to ${hasMorePosts} based on API response`);
                        setHasMore(hasMorePosts);
                    } else {
                        console.log('No posts received, setting hasMore to false');
                        setHasMore(false);
                    }

                    // Make sure we update the current page
                    const newPage = currentPage + 1;
                    console.log(`Updating current page from ${currentPage} to ${newPage}`);
                    setCurrentPage(newPage);
                } else if (contentType === 'flashes') {
                    // Fetch the next page of flashes
                    const nextFlashesPage = flashesPage + 1;
                    const flashesResponse = await axios.get<FlashesResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-flashes?page=${nextFlashesPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                        headers: { Authorization: `Bearer ${token}` }
                    }).catch(err => {
                        console.error(`Error fetching flashes page ${nextFlashesPage}:`, err);
                        return { data: { flashes: [], total_count: 0, next_page: false, current_page: nextFlashesPage } };
                    });

                    const flashesContent = flashesResponse.data.flashes || [];
                    setHasMoreFlashes(flashesResponse.data.next_page || false);
                    setFlashesPage(nextFlashesPage);

                    // Process and add new flashes
                    if (flashesContent.length > 0) {
                        const timestamp = Date.now();

                        // Add to user flashes
                        const newFlashes = [...userFlashes, ...flashesContent];
                        setUserFlashes(newFlashes);
                        setVisibleFlashes(newFlashes);

                        // Create combined array with all flashes including the new ones
                        const allFlashesWithIds = newFlashes.map((flash, index) => ({
                            ...flash,
                            content_type: 'flash',
                            unique_id: `flash-${flash.video_id || index}-${timestamp}-${index}`
                        }));

                        // Also set visible posts to show all flashes, not just the new ones
                        setVisiblePosts(allFlashesWithIds);
                        console.log(`Updated flashes: now have ${newFlashes.length} flashes, hasMore: ${flashesResponse.data.next_page}`);

                        // Update hasMore flag
                        setHasMore(flashesResponse.data.next_page || false);
                    } else {
                        setHasMore(false);
                    }

                    setCurrentPage(nextFlashesPage);
                } else if (contentType === 'glimpses') {
                    // Fetch the next page of glimpses
                    const nextGlimpsesPage = glimpsesPage + 1;
                    const glimpsesResponse = await axios.get<GlimpsesResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-glimpses?page=${nextGlimpsesPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                        headers: { Authorization: `Bearer ${token}` }
                    }).catch(err => {
                        console.error(`Error fetching glimpses page ${nextGlimpsesPage}:`, err);
                        return { data: { glimpses: [], total_count: 0, next_page: false, current_page: nextGlimpsesPage } };
                    });

                    const glimpsesContent = glimpsesResponse.data.glimpses || [];
                    setHasMoreGlimpses(glimpsesResponse.data.next_page || false);
                    setGlimpsesPage(nextGlimpsesPage);

                    // Process and add new glimpses
                    if (glimpsesContent.length > 0) {
                        const timestamp = Date.now();

                        // Add to user glimpses
                        const newGlimpses = [...userGlimpses, ...glimpsesContent];
                        setUserGlimpses(newGlimpses);

                        // Create combined array with all glimpses including the new ones
                        const allGlimpsesWithIds = newGlimpses.map((glimpse, index) => ({
                            ...glimpse,
                            content_type: 'glimpse',
                            unique_id: `glimpse-${glimpse.video_id || index}-${timestamp}-${index}`
                        }));

                        // Add to visible posts - show all glimpses, not just the new ones
                        setVisiblePosts(allGlimpsesWithIds);
                        console.log(`Updated glimpses: now have ${newGlimpses.length} glimpses, hasMore: ${glimpsesResponse.data.next_page}`);

                        // Update hasMore flag
                        setHasMore(glimpsesResponse.data.next_page || false);
                    } else {
                        setHasMore(false);
                    }

                    setCurrentPage(nextGlimpsesPage);
                } else if (contentType === 'movies') {
                    // Fetch the next page of movies
                    const nextMoviesPage = moviesPage + 1;
                    const moviesResponse = await axios.get(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-movies?page=${nextMoviesPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                        headers: { Authorization: `Bearer ${token}` }
                    }).catch(err => {
                        console.error(`Error fetching movies page ${nextMoviesPage}:`, err);
                        return { data: { movies: [], total_count: 0, next_page: false, current_page: nextMoviesPage } };
                    });

                    const moviesContent = moviesResponse.data.movies || [];
                    setHasMoreMovies(moviesResponse.data.next_page || false);
                    setMoviesPage(nextMoviesPage);

                    // Process and add new movies
                    if (moviesContent.length > 0) {
                        const timestamp = Date.now();

                        // Add to user movies
                        const newMovies = [...userMovies, ...moviesContent];
                        setUserMovies(newMovies);

                        // Create combined array with all movies including the new ones
                        const allMoviesWithIds = newMovies.map((movie: any, index: number) => ({
                            ...movie,
                            content_type: 'movie',
                            unique_id: `movie-${movie.video_id || index}-${timestamp}-${index}`
                        }));

                        // Add to visible posts - show all movies, not just the new ones
                        setVisiblePosts(allMoviesWithIds);
                        console.log(`Updated movies: now have ${newMovies.length} movies, hasMore: ${moviesResponse.data.next_page}`);

                        // Update hasMore flag
                        setHasMore(moviesResponse.data.next_page || false);
                    } else {
                        setHasMore(false);
                    }

                    setCurrentPage(nextMoviesPage);
                } else if (contentType === 'photos') {
                    // Fetch the next page of photos
                    const nextPhotosPage = photosPage + 1;
                    const photosResponse = await axios.get<PhotosResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-photos?page=${nextPhotosPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                        headers: { Authorization: `Bearer ${token}` }
                    }).catch(err => {
                        console.error(`Error fetching photos page ${nextPhotosPage}:`, err);
                        return { data: { photos: [], total_count: 0, next_page: false, current_page: nextPhotosPage } };
                    });

                    const photosContent = photosResponse.data.photos || [];
                    setHasMorePhotos(photosResponse.data.next_page || false);
                    setPhotosPage(nextPhotosPage);

                    // Process and add new photos
                    if (photosContent.length > 0) {
                        const timestamp = Date.now();

                        // Add to user photos
                        const newPhotos = [...userPhotos, ...photosContent];
                        setUserPhotos(newPhotos);

                        // Create combined array with all photos including the new ones
                        const allPhotosWithIds = newPhotos.map((photo, index) => ({
                            ...photo,
                            content_type: 'photo',
                            unique_id: `photo-${photo.photo_id || index}-${timestamp}-${index}`
                        }));

                        // Add to visible posts - show all photos, not just the new ones
                        setVisiblePosts(allPhotosWithIds);
                        console.log(`Updated photos: now have ${newPhotos.length} photos, hasMore: ${photosResponse.data.next_page}`);

                        // Update hasMore flag
                        setHasMore(photosResponse.data.next_page || false);
                    } else {
                        setHasMore(false);
                    }

                    setCurrentPage(nextPhotosPage);
                } else {
                    // For backward compatibility, use the old approach
                    let newContent: (FlashVideo | GlimpseVideo | PhotoItem)[] = [...allUserContent];
                    let hasMoreContent = false;

                    // Fetch next page of flashes if there are more
                    if (hasMoreFlashes) {
                        const nextFlashesPage = flashesPage + 1;
                        const flashesResponse = await axios.get<FlashesResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-flashes?page=${nextFlashesPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                            headers: { Authorization: `Bearer ${token}` }
                        }).catch(err => {
                            console.error(`Error fetching flashes page ${nextFlashesPage}:`, err);
                            return { data: { flashes: [], total_count: 0, next_page: false, current_page: nextFlashesPage } };
                        });

                        const flashesContent = flashesResponse.data.flashes || [];
                        setHasMoreFlashes(flashesResponse.data.next_page || false);
                        setFlashesPage(nextFlashesPage);

                        // Process and add new flashes
                        if (flashesContent.length > 0) {
                            const timestamp = Date.now();
                            // Create flashes with unique IDs
                            const newFlashesWithIds = flashesContent.map((flash, index) => ({
                                ...flash,
                                content_type: 'flash',
                                unique_id: `flash-${flash.video_id || index}-${timestamp}-${index}`
                            }));

                            // Add to user flashes
                            setUserFlashes([...userFlashes, ...flashesContent]);

                            // Set visible posts to the new flashes
                            setVisiblePosts(newFlashesWithIds);

                            // Add to all content if needed
                            if (typeof newContent !== 'undefined') {
                                newContent = [...newContent, ...newFlashesWithIds];
                            }
                            hasMoreContent = hasMoreContent || flashesResponse.data.next_page;
                        }
                    }

                    // Fetch next page of glimpses if there are more
                    if (hasMoreGlimpses) {
                        const nextGlimpsesPage = glimpsesPage + 1;
                        const glimpsesResponse = await axios.get<GlimpsesResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-glimpses?page=${nextGlimpsesPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                            headers: { Authorization: `Bearer ${token}` }
                        }).catch(err => {
                            console.error(`Error fetching glimpses page ${nextGlimpsesPage}:`, err);
                            return { data: { glimpses: [], total_count: 0, next_page: false, current_page: nextGlimpsesPage } };
                        });

                        const glimpsesContent = glimpsesResponse.data.glimpses || [];
                        setHasMoreGlimpses(glimpsesResponse.data.next_page || false);
                        setGlimpsesPage(nextGlimpsesPage);

                        // Process and add new glimpses
                        if (glimpsesContent.length > 0) {
                            const timestamp = Date.now();
                            const newGlimpsesWithIds = glimpsesContent.map((glimpse, index) => ({
                                ...glimpse,
                                content_type: 'glimpse',
                                unique_id: `glimpse-${glimpse.video_id || index}-${timestamp}-${index}`
                            }));

                            // Add to user glimpses
                            setUserGlimpses([...userGlimpses, ...glimpsesContent]);

                            // Add to all content
                            newContent = [...newContent, ...newGlimpsesWithIds];
                            hasMoreContent = hasMoreContent || glimpsesResponse.data.next_page;
                        }
                    }

                    // Fetch next page of photos if there are more
                    if (hasMorePhotos) {
                        const nextPhotosPage = photosPage + 1;
                        const photosResponse = await axios.get<PhotosResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-photos?page=${nextPhotosPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                            headers: { Authorization: `Bearer ${token}` }
                        }).catch(err => {
                            console.error(`Error fetching photos page ${nextPhotosPage}:`, err);
                            return { data: { photos: [], total_count: 0, next_page: false, current_page: nextPhotosPage } };
                        });

                        const photosContent = photosResponse.data.photos || [];
                        setHasMorePhotos(photosResponse.data.next_page || false);
                        setPhotosPage(nextPhotosPage);

                        // Process and add new photos
                        if (photosContent.length > 0) {
                            const timestamp = Date.now();
                            const newPhotosWithIds = photosContent.map((photo, index) => ({
                                ...photo,
                                content_type: 'photo',
                                unique_id: `photo-${photo.photo_id || index}-${timestamp}-${index}`
                            }));

                            // Add to user photos
                            setUserPhotos([...userPhotos, ...photosContent]);

                            // Add to all content
                            newContent = [...newContent, ...newPhotosWithIds];
                            hasMoreContent = hasMoreContent || photosResponse.data.next_page;
                        }
                    }

                    // Fetch next page of movies if there are more
                    if (hasMoreMovies) {
                        const nextMoviesPage = moviesPage + 1;
                        const moviesResponse = await axios.get(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-movies?page=${nextMoviesPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                            headers: { Authorization: `Bearer ${token}` }
                        }).catch(err => {
                            console.error(`Error fetching movies page ${nextMoviesPage}:`, err);
                            return { data: { movies: [], total_count: 0, next_page: false, current_page: nextMoviesPage } };
                        });

                        const moviesContent = moviesResponse.data.movies || [];
                        setHasMoreMovies(moviesResponse.data.next_page || false);
                        setMoviesPage(nextMoviesPage);

                        // Process and add new movies
                        if (moviesContent.length > 0) {
                            const timestamp = Date.now();
                            const newMoviesWithIds = moviesContent.map((movie: any, index: number) => ({
                                ...movie,
                                content_type: 'movie',
                                unique_id: `movie-${movie.video_id || index}-${timestamp}-${index}`
                            }));

                            // Add to user movies
                            setUserMovies([...userMovies, ...moviesContent]);

                            // Add to all content
                            newContent = [...newContent, ...newMoviesWithIds];
                            hasMoreContent = hasMoreContent || moviesResponse.data.next_page;
                        }
                    }

                    // Sort by creation date (newest first)
                    newContent.sort((a, b) => {
                        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
                    });

                    // Update all user content
                    setAllUserContent(newContent);
                    setVisiblePosts(newContent);

                    // Update hasMore flag
                    setHasMore(hasMoreFlashes || hasMoreGlimpses || hasMorePhotos || hasMoreMovies || hasMoreStories);
                }
            } else if (activeTab === 'flashes') {
                // For flashes tab, we only need to fetch the next page of flashes
                const nextFlashesPage = flashesPage + 1;
                const flashesResponse = await axios.get<FlashesResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-flashes?page=${nextFlashesPage}&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                    headers: { Authorization: `Bearer ${token}` }
                }).catch(err => {
                    console.error(`Error fetching flashes page ${nextFlashesPage}:`, err);
                    return { data: { flashes: [], total_count: 0, next_page: false, current_page: nextFlashesPage } };
                });

                const flashesContent = flashesResponse.data.flashes || [];
                setHasMoreFlashes(flashesResponse.data.next_page || false);
                setFlashesPage(nextFlashesPage);

                // Add new flashes to existing ones
                const newFlashes = [...userFlashes, ...flashesContent];
                setUserFlashes(newFlashes);
                setVisibleFlashes(newFlashes);

                // Update hasMore flag
                setHasMore(flashesResponse.data.next_page || false);
            }

            // Note: currentPage is already updated in the specific content type sections
            console.log(`Loaded more posts. Now on page ${currentPage + 1}.`);
        } catch (error) {
            console.error('Error loading more posts:', error);
        } finally {
            setLoadingMore(false);
        }
    }, [currentPage, postsPerPage, allUserContent, userFlashes, userGlimpses, userPhotos, userMovies,
        loadingMore, hasMore, activeTab, externalUserId, flashesPage, glimpsesPage, photosPage, moviesPage,
        hasMoreFlashes, hasMoreGlimpses, hasMorePhotos, hasMoreMovies, hasMoreStories]);

    // Set up intersection observer for infinite scroll
    useEffect(() => {
        // Only run this effect when tab or content type changes
        if (prevContentType.current !== contentType || prevActiveTab.current !== activeTab) {
            console.log(`Content type changed from ${prevContentType.current} to ${contentType}, resetting page to 1`);

            // Reset visible content when tab or content type changes
            if (activeTab === 'posts') {
                if (contentType === 'all-posts') {
                    // For all-posts, show all loaded content
                    console.log(`Setting visible posts for all-posts: ${allUserContent.length} items`);
                    setVisiblePosts(allUserContent);
                    // Make sure hasMore is set correctly based on API response
                    console.log(`Setting hasMore for all-posts: ${allUserContent.length < totalPostsCount}`);
                    setHasMore(allUserContent.length < totalPostsCount);
                } else if (contentType === 'flashes') {
                    setVisiblePosts(userFlashes.map(flash => ({
                        ...flash,
                        content_type: 'flash',
                        unique_id: `flash-${flash.video_id}-${Date.now()}`
                    })));
                    setHasMore(hasMoreFlashes);
                } else if (contentType === 'glimpses') {
                    setVisiblePosts(userGlimpses.map(glimpse => ({
                        ...glimpse,
                        content_type: 'glimpse',
                        unique_id: `glimpse-${glimpse.video_id}-${Date.now()}`
                    })));
                    setHasMore(hasMoreGlimpses);
                } else if (contentType === 'movies') {
                    setVisiblePosts(userMovies.map((movie: any) => ({
                        ...movie,
                        content_type: 'movie',
                        unique_id: `movie-${movie.video_id}-${Date.now()}`
                    })));
                    setHasMore(hasMoreMovies);
                } else if (contentType === 'photos') {
                    setVisiblePosts(userPhotos.map(photo => ({
                        ...photo,
                        content_type: 'photo',
                        unique_id: `photo-${photo.photo_id}-${Date.now()}`
                    })));
                    setHasMore(hasMorePhotos);
                }
            } else if (activeTab === 'flashes') {
                setVisibleFlashes(userFlashes);
                setHasMore(hasMoreFlashes);
            }

            // Reset current page when changing content type
            setCurrentPage(1);
            prevContentType.current = contentType;
            prevActiveTab.current = activeTab;
        }
    }, [activeTab, contentType, allUserContent, userFlashes, userGlimpses, userMovies, userPhotos, totalPostsCount, hasMoreFlashes, hasMoreGlimpses, hasMoreMovies, hasMorePhotos]);

    // Effect to extract flashes from allUserContent when activeTab changes to 'flashes'
    useEffect(() => {
        if (activeTab === 'flashes' && allUserContent.length > 0 && userFlashes.length === 0) {
            console.log('Extracting flashes from allUserContent');
            // Filter out flashes from allUserContent
            const flashesFromAllContent = allUserContent.filter(item =>
                (item.content_type === 'flash') ||
                ('video_subtype' in item && item.video_subtype === 'flash')
            );

            if (flashesFromAllContent.length > 0) {
                console.log(`Found ${flashesFromAllContent.length} flashes in allUserContent`);
                // Filter to only FlashVideo items
                const onlyFlashes = flashesFromAllContent.filter(
                    (item): item is FlashVideo =>
                        item.content_type === 'flash' &&
                        'video_id' in item &&
                        'video_name' in item &&
                        'video_url' in item
                );
                setUserFlashes(onlyFlashes);
                setVisibleFlashes(onlyFlashes);
            } else {
                // If no flashes found in allUserContent, fetch them
                fetchContentForType('flashes');
            }
        }
    }, [activeTab, allUserContent]);

    // Set up scroll event listener for loading more content
    useEffect(() => {
        // Add debounce to prevent multiple rapid calls
        let scrollTimeout: NodeJS.Timeout | null = null;

        const handleScroll = () => {
            if (!postsContainerRef.current || loadingMore || !hasMore) return;

            // Clear any existing timeout
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }

            // Set a new timeout to delay the check
            scrollTimeout = setTimeout(() => {
                if (!postsContainerRef.current || loadingMore || !hasMore) return;

                const container = postsContainerRef.current;
                const { bottom } = container.getBoundingClientRect();
                const { innerHeight } = window;

                // Load more when user scrolls near the bottom of the container
                // Increased threshold to load earlier - now 1500px from bottom
                if (bottom - 1500 <= innerHeight) {
                    console.log('Near bottom of container, loading more posts...');
                    console.log(`Scroll position: bottom=${bottom}, innerHeight=${innerHeight}, difference=${bottom - innerHeight}`);
                    console.log(`Current content length: ${allUserContent.length}, hasMore: ${hasMore}`);
                    loadMorePosts();
                }
            }, 200); // 200ms delay to debounce scroll events
        };

        // Add the scroll event listener
        window.addEventListener('scroll', handleScroll);

        // Also check immediately in case the initial content doesn't fill the screen
        setTimeout(() => {
            if (postsContainerRef.current && hasMore && !loadingMore) {
                const container = postsContainerRef.current;
                const { bottom } = container.getBoundingClientRect();
                const { innerHeight } = window;

                console.log(`Initial check - container bottom: ${bottom}, window height: ${innerHeight}`);
                console.log(`hasMore: ${hasMore}, loadingMore: ${loadingMore}`);
                console.log(`Content fills screen: ${bottom > innerHeight}`);

                // Load more if we have less than 20 items or if content doesn't fill the screen
                if ((allUserContent.length < 20 || bottom <= innerHeight + 500) && hasMore && !loadingMore) {
                    console.log('Initial content doesn\'t fill screen or has few items, loading more...');
                    loadMorePosts();
                }
            }
        }, 500);

        // Add a second check after a delay to ensure we load enough content
        setTimeout(() => {
            if (postsContainerRef.current && hasMore && !loadingMore && allUserContent.length < 20) {
                console.log('Second check - loading more content to ensure we have enough items');
                loadMorePosts();
            }
        }, 1500);

        return () => {
            window.removeEventListener('scroll', handleScroll);
            // Clear any pending timeout on cleanup
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
        };
    }, [loadMorePosts, loadingMore, hasMore]);

    // Profile data - updated to use dynamic content counts and user profile data
    const profileData = {
        // Basic user info
        username: userProfileData?.user_name || userName.toLowerCase().replace(/\s+/g, '_'),
        displayName: userProfileData?.name || userProfileData?.user_name || userName,
        user_id: userProfileData?.user_id || externalUserId,

        // Counts
        posts: userProfileData?.posts_count || totalPostsCount || 0,
        followers: userProfileData?.followers_count || 0,
        following: userProfileData?.following_count || 0,
        posts_count: userProfileData?.posts_count || totalPostsCount || 0,
        followers_count: userProfileData?.followers_count || 0,
        following_count: userProfileData?.following_count || 0,
        views_count: userProfileData?.views_count || 0,
        flash_count: userProfileData?.flash_count || 0,
        glimpse_count: userProfileData?.glimpse_count || 0,
        movie_count: userProfileData?.movie_count || 0,
        photo_count: userProfileData?.photo_count || 0,

        // Profile details
        category: userProfileData?.user_type || (userProfile?.user_type === 'vendor' ? 'Vendor/Service' : 'Product/service'),
        description: userProfileData?.bio || 'Your favourite fun clips 📱 in your language 🌎',
        websiteUrl: userProfileData?.website || `${userName.toLowerCase().replace(/\s+/g, '')}.com/more..`,
        profileImage: userProfileData?.user_avatar || 'https://raw.githubusercontent.com/juanpablomarin/wedzat/main/src/app/img/profile-logo.png',

        // Additional features
        chaptersOfLove: userProfileData?.chapters_of_love || [],
        userShortAudio: userProfileData?.user_short_audio || null,
        place: userProfileData?.place || null,
        faceVerified: userProfileData?.face_verified || false,

        // Following status
        is_following: userProfileData?.is_following
    };

    // Function to process image URL for stories
    const processImageUrl = (story: Story): string => {
        if (!story.thumbnail_url && !story.content_url) return '/pics/placeholder.svg';

        // For photos, use content_url directly
        // For videos, use thumbnail_url if available, otherwise use content_url
        const url = story.content_type === 'photo' ? story.content_url : (story.thumbnail_url || story.content_url);

        // If it's already a local URL, return as is
        if (url.startsWith('/')) return url;

        // For cloudfront URLs, ensure they're returned as is
        if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
            return url;
        }

        // Return the URL as is for other external URLs
        return url || '/pics/placeholder.svg';
    };

    // Function to handle follow/unfollow
    const handleFollowToggle = async () => {
        if (!externalUserId) return; // Only for other user profiles

        try {
            setLoading(true);

            // Get token from localStorage
            const storedToken = localStorage.getItem('token') ||
                localStorage.getItem('jwt_token') ||
                localStorage.getItem('wedzat_token');

            if (!storedToken) {
                console.warn('No authentication token found');
                return;
            }

            // Make API call to follow/unfollow user using the correct API endpoints
            const endpoint = isFollowing ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';
            await axios.post(endpoint,
                { target_user_id: externalUserId },
                { headers: { Authorization: `Bearer ${storedToken}` } }
            );

            // Update following status
            setIsFollowing(!isFollowing);

            // Update follower count in profile data
            if (userProfileData) {
                const updatedProfileData = { ...userProfileData };
                if (isFollowing) {
                    // Unfollowing, decrease follower count
                    updatedProfileData.followers_count = Math.max(0, (updatedProfileData.followers_count || 0) - 1);
                } else {
                    // Following, increase follower count
                    updatedProfileData.followers_count = (updatedProfileData.followers_count || 0) + 1;
                }
                setUserProfileData(updatedProfileData);
            }

            console.log(`Successfully ${isFollowing ? 'unfollowed' : 'followed'} user`);
        } catch (err) {
            console.error(`Error ${isFollowing ? 'unfollowing' : 'following'} user:`, err);
        } finally {
            setLoading(false);
        }
    };

    // Function to toggle profile menu
    const toggleProfileMenu = () => {
        setShowProfileMenu(!showProfileMenu);
    };

    // Function to handle logout
    const handleLogout = () => {
        // Clear all tokens from localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('jwt_token');
        localStorage.removeItem('wedzat_token');

        // Use the logout function from AuthContext
        logout();

        // Redirect to the login page
        router.push('/');
    };

    // Function to handle settings navigation
    const handleSettings = () => {
        router.push('/settings');
    };

    // Function to fetch content for a specific type when user changes the dropdown
    const fetchContentForType = async (contentType: string) => {
        // Skip if we're already loading or if the content type is all-posts
        if (loadingMore || contentType === 'all-posts') return;

        // Force loading flashes if we're on the flashes tab
        const forceLoad = activeTab === 'flashes' && contentType === 'flashes';

        console.log(`Fetching content for type: ${contentType}`);
        setLoadingMore(true);

        try {
            const token = localStorage.getItem('token') ||
                localStorage.getItem('jwt_token') ||
                localStorage.getItem('wedzat_token');

            if (!token) {
                console.warn('No authentication token found');
                setLoadingMore(false);
                return;
            }

            // Create user ID parameter for API calls - only include for other users
            const userIdParam = externalUserId ? `user_id=${externalUserId}` : '';

            // Fetch content based on the selected type
            if (contentType === 'flashes' && (userFlashes.length === 0 || forceLoad)) {
                const flashesResponse = await axios.get<FlashesResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-flashes?page=1&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                    headers: { Authorization: `Bearer ${token}` }
                }).catch(err => {
                    console.error('Error fetching flashes:', err);
                    return { data: { flashes: [], total_count: 0, next_page: false, current_page: 1 } };
                });

                const flashesContent = flashesResponse.data.flashes || [];
                setUserFlashes(flashesContent);
                setVisibleFlashes(flashesContent);
                setHasMoreFlashes(flashesResponse.data.next_page || false);
                setFlashesPage(1);

                // Create flashes with unique IDs for display
                const timestamp = Date.now();
                const flashesWithIds = flashesContent.map((flash, index) => ({
                    ...flash,
                    content_type: 'flash',
                    unique_id: `flash-${flash.video_id || index}-${timestamp}`
                }));

                setVisiblePosts(flashesWithIds);
                setHasMore(flashesResponse.data.next_page || false);

                // Update total count for flashes
                console.log(`Setting total count for flashes: ${flashesResponse.data.total_count}`);
                setTotalPostsCount(flashesResponse.data.total_count || flashesContent.length);
            }
            else if (contentType === 'glimpses' && userGlimpses.length === 0) {
                const glimpsesResponse = await axios.get<GlimpsesResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-glimpses?page=1&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                    headers: { Authorization: `Bearer ${token}` }
                }).catch(err => {
                    console.error('Error fetching glimpses:', err);
                    return { data: { glimpses: [], total_count: 0, next_page: false, current_page: 1 } };
                });

                const glimpsesContent = glimpsesResponse.data.glimpses || [];
                setUserGlimpses(glimpsesContent);
                setHasMoreGlimpses(glimpsesResponse.data.next_page || false);
                setGlimpsesPage(1);

                // Create glimpses with unique IDs for display
                const timestamp = Date.now();
                const glimpsesWithIds = glimpsesContent.map((glimpse, index) => ({
                    ...glimpse,
                    content_type: 'glimpse',
                    unique_id: `glimpse-${glimpse.video_id || index}-${timestamp}`
                }));

                setVisiblePosts(glimpsesWithIds);
                setHasMore(glimpsesResponse.data.next_page || false);

                // Update total count for glimpses
                console.log(`Setting total count for glimpses: ${glimpsesResponse.data.total_count}`);
                setTotalPostsCount(glimpsesResponse.data.total_count || glimpsesContent.length);
            }
            else if (contentType === 'movies' && userMovies.length === 0) {
                const moviesResponse = await axios.get(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-movies?page=1&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                    headers: { Authorization: `Bearer ${token}` }
                }).catch(err => {
                    console.error('Error fetching movies:', err);
                    return { data: { movies: [], total_count: 0, next_page: false, current_page: 1 } };
                });

                const moviesContent = moviesResponse.data.movies || [];
                setUserMovies(moviesContent);
                setHasMoreMovies(moviesResponse.data.next_page || false);
                setMoviesPage(1);

                // Create movies with unique IDs for display
                const timestamp = Date.now();
                const moviesWithIds = moviesContent.map((movie: any, index: number) => ({
                    ...movie,
                    content_type: 'movie',
                    unique_id: `movie-${movie.video_id || index}-${timestamp}`
                }));

                setVisiblePosts(moviesWithIds);
                setHasMore(moviesResponse.data.next_page || false);

                // Update total count for movies
                console.log(`Setting total count for movies: ${moviesResponse.data.total_count}`);
                setTotalPostsCount(moviesResponse.data.total_count || moviesContent.length);
            }
            else if (contentType === 'photos' && userPhotos.length === 0) {
                const photosResponse = await axios.get<PhotosResponse>(`https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-photos?page=1&limit=${postsPerPage}${userIdParam ? `&${userIdParam}` : ''}`, {
                    headers: { Authorization: `Bearer ${token}` }
                }).catch(err => {
                    console.error('Error fetching photos:', err);
                    return { data: { photos: [], total_count: 0, next_page: false, current_page: 1 } };
                });

                const photosContent = photosResponse.data.photos || [];
                setUserPhotos(photosContent);
                setHasMorePhotos(photosResponse.data.next_page || false);
                setPhotosPage(1);

                // Create photos with unique IDs for display
                const timestamp = Date.now();
                const photosWithIds = photosContent.map((photo, index) => ({
                    ...photo,
                    content_type: 'photo',
                    unique_id: `photo-${photo.photo_id || index}-${timestamp}`
                }));

                setVisiblePosts(photosWithIds);
                setHasMore(photosResponse.data.next_page || false);

                // Update total count for photos
                console.log(`Setting total count for photos: ${photosResponse.data.total_count}`);
                setTotalPostsCount(photosResponse.data.total_count || photosContent.length);
            }
        } catch (error) {
            console.error(`Error fetching ${contentType}:`, error);
        } finally {
            setLoadingMore(false);
        }
    };

    // Function to handle switch accounts
    const handleSwitchAccounts = () => {
        // This could open a modal or navigate to an account selection page
        alert('Switch accounts functionality will be implemented soon.');
    };

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            // Close profile menu if clicked outside
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowProfileMenu(false);
            }

            // No need to close dropdown since we're using a select element now
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);



    // Function to navigate with token persistence
    const navigateWithAuth = (path: string) => {
        const currentToken = token || localStorage.getItem('token');
        if (!currentToken) {
            alert('Authentication required. Please log in.');
            return;
        }

        // Ensure token is in localStorage before navigation
        if (token && !localStorage.getItem('token')) {
            localStorage.setItem('token', token);
        }

        router.push(path);
    };


    return (
        <div className="flex flex-col min-h-screen overflow-x-hidden bg-white">
            {/* Top Navigation */}
            <div className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200">
                <TopNavigation />
            </div>

            <div className="flex pt-16"> {/* Add padding to accommodate fixed header */}
                {/* Side Navigation - fixed on the left, hidden on mobile */}
                <div className={`fixed left-0 top-16 bottom-0 z-40 ${sidebarExpanded ? "w-48" : "w-20"} bg-white border-r border-gray-200 transition-all duration-150 ease-in-out hidden md:block`}>
                    <SideNavigation
                        expanded={sidebarExpanded}
                        onExpand={() => setSidebarExpanded(true)}
                        onCollapse={() => setSidebarExpanded(false)}
                    />
                </div>

                {/* Main Content Area with dynamic margin to accommodate side nav */}
                <div
                    className={`flex-1 w-full pt-4 sm:pt-10 pb-16 md:pb-4 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"} flex justify-center`}
                    style={{
                        transition: "all 150ms ease-in-out",
                        overflowX: "hidden",
                        paddingLeft: 0
                    }}>
                    {/* Profile Header Section */}
                    <div className={`w-full max-w-6xl px-2 sm:px-6 mt-2 sm:mt-4`}
                        style={{
                            transition: "margin 150ms ease-in-out"
                        }}>
                        <div className="flex flex-col">
                            {/* Header Row - Simplified */}
                            <div className="w-full flex items-center justify-between mb-6">
                                {/* Left Side - Name and Action Icons */}
                                <div className="flex items-center">
                                    <div className="flex items-center relative" ref={dropdownRef}>
                                        <h2 className="text-lg font-medium text-black">{userName}</h2>
                                        <button
                                            onClick={toggleProfileMenu}
                                            className="ml-1 focus:outline-none p-1 hover:bg-gray-100 rounded-full"
                                            aria-label="Toggle profile menu"
                                            aria-expanded={showProfileMenu}
                                        >
                                            <svg className="w-4 h-4 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </button>
                                        {/* Profile dropdown menu */}
                                        {showProfileMenu && (
                                            <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg z-50 py-1 border border-gray-200 transition-all duration-200 ease-in-out">
                                                <button
                                                    onClick={handleSwitchAccounts}
                                                    className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150 flex items-center"
                                                >
                                                    <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m-8 6H4m0 0l4 4m-4-4l4-4" />
                                                    </svg>
                                                    Switch Accounts
                                                </button>
                                                <button
                                                    onClick={handleSettings}
                                                    className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150 flex items-center"
                                                >
                                                    <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    </svg>
                                                    Settings
                                                </button>
                                                <button
                                                    onClick={handleLogout}
                                                    className="w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150 flex items-center border-t border-gray-100"
                                                >
                                                    <svg className="w-4 h-4 mr-2 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                                    </svg>
                                                    Logout
                                                </button>
                                            </div>
                                        )}
                                    </div>

                                    {/* Action Icons */}
                                    <div className="flex items-center space-x-4 ml-8">
                                        {isOwnProfile && (
                                            <button
                                                className="focus:outline-none"
                                                onClick={() => setIsEditProfileModalOpen(true)}
                                                title="Edit Profile"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="#000000" strokeWidth="2">
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                </svg>
                                            </button>
                                        )}
                                        <button
                                            className="focus:outline-none"
                                            onClick={() => setCalendarModalOpen(true)}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button className="focus:outline-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                            </svg>
                                        </button>
                                        <button className="focus:outline-none">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 6h16M4 12h16M4 18h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* Main Profile Content - Responsive layout: profile pic, user info, chapters */}
                            <div className="flex flex-col sm:flex-row mb-6 px-1 sm:px-0 justify-center">
                                {/* First Column - Profile Picture */}
                                <div className="w-full sm:w-1/4 flex flex-col items-center sm:pr-4 mb-4 sm:mb-0">
                                    {/* Profile Picture */}
                                    <div className="relative mb-4 sm:mb-6 w-full flex flex-col items-center">
                                        <div className="w-28 h-28 sm:w-40 sm:h-40 rounded-full border-2 border-red-500 overflow-hidden">
                                            <img
                                                src={profileData.profileImage || "/pics/placeholder.svg"}
                                                alt={profileData.displayName}
                                                className="w-full h-full object-cover"
                                                onError={(e) => {
                                                    const target = e.target as HTMLImageElement;
                                                    target.onerror = null; // Prevent infinite loop
                                                    target.src = '/pics/placeholder.svg'; // Fallback image
                                                }}
                                            />
                                        </div>
                                        <div className="absolute bottom-0 right-1/3 bg-blue-500 rounded-full p-1">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                                {/* Second Column - User Info */}
                                <div className="w-full sm:w-2/5 px-0 sm:px-4 flex flex-col">
                                    {/* First Row - Stats */}
                                    <div className="flex justify-between sm:justify-start text-black mb-4 sm:mb-6 sm:space-x-10">
                                        <div className="text-center">
                                            <div className="font-bold text-base sm:text-lg">{profileData.posts_count || totalPostsCount || 0}</div>
                                            <div className="text-xs sm:text-sm font-medium text-gray-600">posts</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="font-bold text-base sm:text-lg">{profileData.followers_count || 0}</div>
                                            <div className="text-xs sm:text-sm font-medium text-gray-600">Admire</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="font-bold text-base sm:text-lg">{profileData.following_count || 0}</div>
                                            <div className="text-xs sm:text-sm font-medium text-gray-600">Admiring</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="font-bold text-base sm:text-lg">{profileData.views_count || 0}</div>
                                            <div className="text-xs sm:text-sm font-medium text-gray-600">Views</div>
                                        </div>
                                    </div>

                                    {/* Second Row - User Info */}
                                    <div className="text-left mb-6 max-w-full">
                                        {/* Follow/Unfollow button for other user profiles */}
                                        {!isOwnProfile && (
                                            <div className="mb-3">
                                                <button
                                                    onClick={handleFollowToggle}
                                                    className={`px-4 py-1.5 rounded-md text-sm font-medium ${isFollowing ? 'bg-gray-200 text-gray-800' : 'bg-red-500 text-white'}`}
                                                >
                                                    {isFollowing ? 'Admiring' : 'Admire'}
                                                </button>
                                            </div>
                                        )}
                                        <div className="font-bold text-black text-base sm:text-lg">{profileData.displayName}</div>
                                        <div className="text-gray-600 text-xs sm:text-sm font-medium mb-1">{profileData.category}</div>
                                        <div className="flex flex-wrap items-center text-black mb-1">
                                            <span className="font-medium text-xs sm:text-sm md:text-base break-words w-full sm:w-auto">{profileData.description}</span>
                                            <span className="ml-0 sm:ml-1 text-blue-500">🌎</span>
                                        </div>

                                        {/* Display location if available */}
                                        {profileData.place && (
                                            <div className="text-gray-600 text-xs sm:text-sm font-medium mb-1">
                                                <span className="font-medium">Location:</span> {profileData.place}
                                            </div>
                                        )}

                                        {/* Display verification badge if applicable */}
                                        {profileData.faceVerified && (
                                            <div className="text-green-600 text-xs sm:text-sm font-medium mb-1 flex items-center">
                                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                </svg>
                                                <span>Verified Account</span>
                                            </div>
                                        )}

                                        <div className="flex flex-wrap items-center">
                                            <a href="#" className="text-blue-600 font-medium hover:underline text-xs sm:text-sm truncate max-w-[150px] sm:max-w-[200px] md:max-w-none">
                                                www.{userName.replace(/\s+/g, '')}.com
                                            </a>
                                            <span className="text-blue-600 font-medium ml-1 text-xs sm:text-sm">more..</span>
                                        </div>
                                    </div>

                                    {/* Third Row - Hear Short Story */}
                                    {(profileData.userShortAudio || userProfileData?.user_short_audio) && (
                                        <div className="mb-4 w-full">
                                            {/* Hear Short Story Button */}
                                            <div className="flex items-center mb-3">
                                                <button
                                                    className="flex items-center text-black font-medium"
                                                    onClick={() => {
                                                        const audioUrl = profileData.userShortAudio || userProfileData?.user_short_audio;
                                                        if (audioUrl) {
                                                            // Create an audio element to play the short audio
                                                            const audio = new Audio(audioUrl);
                                                            audio.play();
                                                        }
                                                    }}
                                                >
                                                    <span className="mr-2 font-semibold text-xs sm:text-sm md:text-base">Hear Short Story</span>
                                                    <div className="bg-red-500 rounded-full p-1">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-4 sm:w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                                        </svg>
                                                    </div>
                                                </button>
                                            </div>

                                            {/* Audio Waveform */}
                                            <div className="h-8 sm:h-10 bg-white border border-gray-200 rounded-full flex items-center px-2 sm:px-3 w-full shadow-sm overflow-hidden">
                                                <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-red-500 flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-4 sm:w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                                    </svg>
                                                </div>
                                                <div className="flex-1 flex items-center space-x-0.5 overflow-hidden">
                                                    {/* Desktop waveform */}
                                                    <div className="hidden sm:flex items-center space-x-0.5 flex-1">
                                                        {Array(30).fill(0).map((_, i) => (
                                                            <div
                                                                key={`waveform-desktop-${i}`}
                                                                className="bg-red-500"
                                                                style={{
                                                                    height: `${Math.sin(i / 3) * 8 + 8}px`,
                                                                    width: '2px'
                                                                }}
                                                            ></div>
                                                        ))}
                                                    </div>
                                                    {/* Mobile waveform */}
                                                    <div className="flex sm:hidden items-center space-x-0.5 flex-1">
                                                        {Array(20).fill(0).map((_, i) => (
                                                            <div
                                                                key={`waveform-mobile-${i}`}
                                                                className="bg-red-500"
                                                                style={{
                                                                    height: `${Math.sin(i / 3) * 6 + 6}px`,
                                                                    width: '1px'
                                                                }}
                                                            ></div>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* Third Column - Chapters of Love */}
                                <div className="w-full sm:w-1/3 pl-0 sm:pl-4 flex flex-col mt-4 sm:mt-0 pr-0">
                                    <div className="flex flex-col h-full">
                                        {/* Title Row */}
                                        <div className="flex items-center mb-3 sm:mb-6">
                                            <div className="mr-2 sm:mr-3">
                                                <svg className="h-4 w-4 sm:h-6 sm:w-6 text-red-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                                    <path d="M9 7L15 12L9 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                            </div>
                                            <h3 className="text-sm sm:text-xl font-bold text-black tracking-wide">CHAPTERS OF LOVE</h3>
                                        </div>

                                        {/* Stories/Highlights Row - Horizontal Scrollable */}
                                        <div className="relative w-full mt-2 overflow-hidden">
                                            <div
                                                className="overflow-x-auto scrollbar-hide w-full"
                                                style={{
                                                    scrollBehavior: 'smooth',
                                                    WebkitOverflowScrolling: 'touch',
                                                    minHeight: userStories.length === 0 ? '120px' : 'auto',
                                                    overflowY: 'hidden' // Prevent vertical scrolling
                                                }}
                                            >
                                                <div className="flex gap-3 pb-4 flex-nowrap pl-1 pr-1">
                                                    {/* Add Story button with preview image - only for own profile */}
                                                    {isOwnProfile && (
                                                        <button
                                                            className="flex flex-col items-center space-y-2 flex-shrink-0 focus:outline-none"
                                                            onClick={() => alert('Create a new story!')}
                                                        >
                                                            <div className="w-[70px] h-[70px] sm:w-[100px] sm:h-[100px] max-w-[100px] max-h-[100px] rounded-full overflow-hidden border-2 border-gray-300">
                                                                <img
                                                                    src="/pics/placeholder.svg"
                                                                    alt="Add Story"
                                                                    className="w-full h-full object-cover"
                                                                />
                                                            </div>
                                                            <span className="text-[10px] sm:text-xs font-medium text-center text-black">Add Story</span>
                                                        </button>
                                                    )}

                                                    {/* User Stories with updated visual style */}
                                                    {/* Ensure each story has a unique key by using a stable ID generation approach */}
                                                    {userStories.map((story, storyIndex) => {
                                                        // Create a stable, unique key for this story
                                                        const storyKey = `story-${story.content_id || 'unknown'}-${storyIndex}-${story.content_type || 'unknown'}`;

                                                        return (
                                                            <button
                                                                key={storyKey}
                                                                className="flex flex-col items-center space-y-2 flex-shrink-0 focus:outline-none"
                                                                onClick={() => {
                                                                    if (story.content_type === 'video' && story.content_url) {
                                                                        window.open(story.content_url, '_blank');
                                                                    } else if (story.content_type === 'photo' && story.content_url) {
                                                                        window.open(story.content_url, '_blank');
                                                                    }
                                                                }}
                                                            >
                                                                <div className="w-[70px] h-[70px] sm:w-[100px] sm:h-[100px] max-w-[100px] max-h-[100px] rounded-full overflow-hidden border-2 border-red-500">
                                                                    <img
                                                                        src={processImageUrl(story)}
                                                                        alt={story.content_name}
                                                                        className="w-full h-full object-cover"
                                                                        onError={(e) => {
                                                                            const imgElement = e.target as HTMLImageElement;
                                                                            if (imgElement) {
                                                                                imgElement.src = '/pics/placeholder.svg';
                                                                            }
                                                                        }}
                                                                    />
                                                                </div>
                                                                <span className="text-[10px] sm:text-xs font-medium text-center text-black w-[70px] sm:w-[100px] truncate">{story.content_name}</span>
                                                            </button>
                                                        );
                                                    })}

                                                    {/* Show message if no stories or chapters */}
                                                    {userStories.length === 0 && profileData.chaptersOfLove.length === 0 && (
                                                        <div className="flex items-center justify-center text-gray-500 text-sm ml-4">
                                                            No stories yet. Create your first story!
                                                        </div>
                                                    )}

                                                    {/* Display chapters of love */}
                                                    {profileData.chaptersOfLove.map((chapter: string, index: number) => (
                                                        <button
                                                            key={`chapter-${index}`}
                                                            className="flex flex-col items-center space-y-2 flex-shrink-0 focus:outline-none"
                                                        >
                                                            <div className="w-[70px] h-[70px] sm:w-[100px] sm:h-[100px] max-w-[100px] max-h-[100px] rounded-full overflow-hidden border-2 border-red-500">
                                                                <img
                                                                    src={typeof chapter === 'string' ? chapter : '/pics/placeholder.svg'}
                                                                    alt={`Chapter ${index + 1}`}
                                                                    className="w-full h-full object-cover"
                                                                    onError={(e) => {
                                                                        const target = e.target as HTMLImageElement;
                                                                        target.onerror = null; // Prevent infinite loop
                                                                        target.src = '/pics/placeholder.svg'; // Fallback image
                                                                    }}
                                                                />
                                                            </div>
                                                            <span className="text-[10px] sm:text-xs font-medium text-center text-black w-[70px] sm:w-[100px] truncate">
                                                                Chapter {index + 1}
                                                            </span>
                                                        </button>
                                                    ))}
                                                </div>

                                                {/* CSS to hide scrollbar across browsers */}
                                                <style jsx>{`
                                                /* Hide scrollbar for Chrome, Safari and Opera */
                                                .scrollbar-hide::-webkit-scrollbar {
                                                    display: none;
                                                }
                                                /* Hide scrollbar for IE, Edge and Firefox */
                                                .scrollbar-hide {
                                                    -ms-overflow-style: none; /* IE and Edge */
                                                    scrollbar-width: none; /* Firefox */
                                                }
                                            `}</style>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                            {/* Tabs - Interactive */}
                            <div className="mt-4 mb-4 w-full overflow-x-auto px-1 sm:pl-1 sm:pr-0">
                                <div className={`flex justify-start sm:justify-center space-x-2 sm:space-x-4 pb-1 whitespace-nowrap`}
                                    style={{
                                        transition: "padding 150ms ease-in-out"
                                    }}>
                                    <div className="relative" ref={contentTypeDropdownRef}>
                                        <select
                                            className="py-1 sm:py-2 px-3 sm:px-6 text-xs sm:text-sm font-medium rounded-md bg-red-500 text-white appearance-none pr-8"
                                            value={contentType}
                                            onChange={(e) => {
                                                const newContentType = e.target.value;
                                                setContentType(newContentType);
                                                setActiveTab('posts');
                                                console.log('Selected content type:', newContentType);
                                                // Reset pagination
                                                setCurrentPage(1);

                                                // Fetch content for the selected type if it's not all-posts
                                                // and we don't already have content for this type
                                                fetchContentForType(newContentType);
                                            }}
                                        >
                                            <option value="all-posts">ALL POSTS</option>
                                            <option value="flashes">FLASHES</option>
                                            <option value="glimpses">GLIMPSES</option>
                                            <option value="movies">MOVIES</option>
                                            <option value="photos">PHOTOS</option>
                                        </select>
                                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                    <button
                                        className={`py-1 sm:py-2 px-3 sm:px-6 text-xs sm:text-sm font-medium rounded-md ${activeTab === 'flashes' ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-500'}`}
                                        onClick={async () => {
                                            setActiveTab('flashes');
                                            // Always load flashes when this tab is clicked
                                            setLoading(true);
                                            try {
                                                const token = localStorage.getItem('token') ||
                                                    localStorage.getItem('jwt_token') ||
                                                    localStorage.getItem('wedzat_token');

                                                if (!token) {
                                                    console.warn('No authentication token found');
                                                    return;
                                                }

                                                // Create user ID parameter for API calls - only include for other users
                                                const userIdParam = externalUserId ? `user_id=${externalUserId}` : '';

                                                // Directly fetch flashes
                                                const flashesResponse = await axios.get<FlashesResponse>(
                                                    `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-flashes?page=1&limit=50${userIdParam ? `&${userIdParam}` : ''}`,
                                                    { headers: { Authorization: `Bearer ${token}` } }
                                                ).catch(err => {
                                                    console.error('Error fetching flashes:', err);
                                                    return { data: { flashes: [], total_count: 0, next_page: false, current_page: 1 } };
                                                });

                                                const flashesContent = flashesResponse.data.flashes || [];
                                                console.log(`Loaded ${flashesContent.length} flashes directly`);

                                                // Process flashes with proper IDs and content type
                                                const timestamp = Date.now();
                                                const processedFlashes = flashesContent.map((flash, index) => ({
                                                    ...flash,
                                                    content_type: 'flash',
                                                    unique_id: `flash-${flash.video_id || index}-${timestamp}`
                                                }));

                                                // Update state with fetched flashes
                                                setUserFlashes(processedFlashes);
                                                setVisibleFlashes(processedFlashes);
                                                setHasMoreFlashes(flashesResponse.data.next_page || false);
                                                setFlashesPage(1);
                                            } catch (error) {
                                                console.error('Error loading flashes:', error);
                                            } finally {
                                                setLoading(false);
                                            }
                                        }}
                                    >
                                        <div className="flex items-center">
                                            <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                            </svg>
                                            <span className="whitespace-nowrap">FLASHES</span>
                                        </div>
                                    </button>
                                    <button
                                        className={`py-1 sm:py-2 px-3 sm:px-6 text-xs sm:text-sm font-medium rounded-md ${activeTab === 'saved' ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-500'}`}
                                        onClick={() => setActiveTab('saved')}
                                    >
                                        <div className="flex items-center">
                                            <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                            </svg>
                                            <span className="whitespace-nowrap">SAVED</span>
                                        </div>
                                    </button>
                                    <button
                                        className={`py-1 sm:py-2 px-3 sm:px-6 text-xs sm:text-sm font-medium rounded-md ${activeTab === 'tagged' ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-500'}`}
                                        onClick={() => setActiveTab('tagged')}
                                    >
                                        <div className="flex items-center">
                                            <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                            </svg>
                                            <span className="whitespace-nowrap">TAGGED</span>
                                        </div>
                                    </button>
                                </div>
                            </div>

                            {/* Content based on active tab */}
                            {activeTab === 'posts' && (
                                <div>
                                    {loading ? (
                                        <div className="py-10 text-center max-w-4xl mx-auto">
                                            <div className="inline-block w-8 h-8 border-4 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
                                            <p className="mt-2 text-gray-600">Loading your posts...</p>
                                        </div>
                                    ) : error ? (
                                        <div className="py-10 text-center text-red-500 max-w-4xl mx-auto">
                                            <p>{error}</p>
                                            <button
                                                onClick={fetchUserContent}
                                                className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md"
                                            >
                                                Try Again
                                            </button>
                                        </div>
                                    ) : allUserContent.length === 0 ? (
                                        <div className="py-10 text-center text-gray-500 max-w-4xl mx-auto">
                                            <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            <p className="text-lg font-medium">No posts yet</p>
                                            <p className="mt-1">Share your first post to get started</p>
                                        </div>
                                    ) : (
                                        <div
                                            className="grid grid-cols-3 gap-1 sm:gap-2 mt-2 sm:mt-4 mx-auto px-1 sm:px-0 max-w-4xl"
                                            ref={postsContainerRef}
                                        >
                                            {visiblePosts.map((content, index) => {
                                                // Determine content type and get appropriate properties
                                                const contentType = 'content_type' in content ? content.content_type : 'photo';

                                                // Use the unique_id we generated when creating the content array
                                                // or generate a new one if it doesn't exist
                                                let contentId;
                                                if ('unique_id' in content && content.unique_id) {
                                                    contentId = content.unique_id;
                                                } else if ('video_id' in content && content.video_id) {
                                                    contentId = `vid-${content.video_id}`;
                                                } else if ('photo_id' in content && content.photo_id) {
                                                    contentId = `photo-${content.photo_id}`;
                                                } else {
                                                    // Fallback to a unique ID based on index and a random string
                                                    // Avoid using Date.now() here as it can cause issues with React's reconciliation
                                                    contentId = `content-${index}-${Math.random().toString(36).substring(2, 15)}`;
                                                }

                                                // Determine the thumbnail URL based on content type
                                                let thumbnailUrl = '/pics/placeholder.svg';

                                                // Based on the API response format:
                                                // For photos: use content_url
                                                // For videos (flashes, glimpses, movies): use thumbnail_url

                                                if (contentType === 'photo') {
                                                    // For photos, prioritize content_url as per API response
                                                    if ('content_url' in content && content.content_url && typeof content.content_url === 'string') {
                                                        thumbnailUrl = content.content_url;
                                                    } else if ('photo_url' in content && content.photo_url && typeof content.photo_url === 'string') {
                                                        thumbnailUrl = content.photo_url;
                                                    } else if ('thumbnail_url' in content && content.thumbnail_url && typeof content.thumbnail_url === 'string') {
                                                        thumbnailUrl = content.thumbnail_url;
                                                    }
                                                }
                                                // For videos (flashes, glimpses, movies)
                                                else if (contentType === 'flash' || contentType === 'glimpse' || contentType === 'movie') {
                                                    // For videos, prioritize thumbnail_url as per API response
                                                    if ('thumbnail_url' in content && content.thumbnail_url && typeof content.thumbnail_url === 'string') {
                                                        thumbnailUrl = content.thumbnail_url;
                                                    } else if ('video_thumbnail' in content && content.video_thumbnail && typeof content.video_thumbnail === 'string') {
                                                        thumbnailUrl = content.video_thumbnail;
                                                    } else if ('content_url' in content && content.content_url && typeof content.content_url === 'string') {
                                                        // Use content_url as fallback only if no thumbnail available
                                                        thumbnailUrl = content.content_url;
                                                    }
                                                }
                                                // Generic fallback for any content type
                                                else {
                                                    if ('thumbnail_url' in content && content.thumbnail_url && typeof content.thumbnail_url === 'string') {
                                                        thumbnailUrl = content.thumbnail_url;
                                                    } else if ('content_url' in content && content.content_url && typeof content.content_url === 'string') {
                                                        thumbnailUrl = content.content_url;
                                                    }
                                                }

                                                // Log for debugging
                                                console.log(`Content ${index} (${contentType}):`, {
                                                    contentType,
                                                    thumbnailUrl,
                                                    hasVideoThumbnail: 'video_thumbnail' in content,
                                                    hasPhotoThumbnail: 'photo_thumbnail' in content,
                                                    hasPhotoUrl: 'photo_url' in content,
                                                    hasVideoUrl: 'video_url' in content,
                                                    content: JSON.stringify(content).substring(0, 100) + '...'
                                                });
                                                const likes = 'video_likes' in content ? content.video_likes :
                                                    'photo_likes' in content ? content.photo_likes : 0;
                                                const comments = 'video_comments' in content ? content.video_comments :
                                                    'photo_comments' in content ? content.photo_comments : 0;

                                                // Determine border color based on content type
                                                let borderColor = 'blue';
                                                if (contentType === 'flash') borderColor = 'red';
                                                else if (contentType === 'glimpse') borderColor = 'purple';

                                                // Create a truly unique key combining type, id, and index
                                                const uniqueKey = `${contentType}-${contentId}-${index}`;

                                                return (
                                                    <div
                                                        key={uniqueKey}
                                                        className="aspect-square relative overflow-hidden rounded-md cursor-pointer transform transition hover:scale-[0.99]"
                                                        style={{
                                                            padding: '2px',
                                                            width: '100%',
                                                            height: '90%',
                                                            background: borderColor === 'red' ? 'linear-gradient(45deg, #ff6b6b, #ff8e8e)' :
                                                                borderColor === 'purple' ? 'linear-gradient(45deg, #da77f2, #e599f7)' :
                                                                    'linear-gradient(45deg, #74c0fc, #a5d8ff)'
                                                        }}
                                                        onClick={() => {
                                                            // Handle click based on content type
                                                            if (contentType === 'flash') {
                                                                navigateWithAuth(`/home/<USER>/shorts?index=${index}`);
                                                            } else if (contentType === 'glimpse') {
                                                                navigateWithAuth(`/home/<USER>/${contentId}`);
                                                            } else {
                                                                // For photos or other content
                                                                alert(`Viewing ${contentType} ${contentId}`);
                                                            }
                                                        }}
                                                    >
                                                        <img
                                                            src={thumbnailUrl || '/pics/placeholder.svg'}
                                                            alt={`${contentType} ${contentId}`}
                                                            className="w-full h-full object-cover rounded-md"
                                                            onError={(e) => {
                                                                console.error(`Failed to load image: ${thumbnailUrl}`);
                                                                const imgElement = e.target as HTMLImageElement;
                                                                if (imgElement) {
                                                                    imgElement.src = '/pics/placeholder.svg';
                                                                }
                                                            }}
                                                        />
                                                        {/* Debug info removed */}
                                                        {/* Hover overlay with likes and comments */}
                                                        <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 flex items-center justify-center transition-opacity duration-200 rounded-md">
                                                            <div className="flex items-center text-white mx-1 sm:mx-2">
                                                                <svg className="w-3 h-3 sm:w-5 sm:h-5 mr-1" fill="white" viewBox="0 0 24 24">
                                                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                                                                </svg>
                                                                <span className="text-xs sm:text-sm mr-2 sm:mr-3">{likes || 0}</span>
                                                                <svg className="w-3 h-3 sm:w-5 sm:h-5 mr-1" fill="white" viewBox="0 0 24 24">
                                                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10z" />
                                                                </svg>
                                                                <span className="text-xs sm:text-sm">{comments || 0}</span>
                                                            </div>
                                                        </div>

                                                        {/* Content type indicator */}
                                                        <div className="absolute top-1 sm:top-2 right-1 sm:right-2 bg-black bg-opacity-50 text-white text-[8px] sm:text-xs px-1 sm:px-2 py-0.5 sm:py-1 rounded-full">
                                                            {contentType === 'flash' ? 'Flash' :
                                                                contentType === 'glimpse' ? 'Glimpse' :
                                                                    contentType === 'movie' ? 'Movie' : 'Photo'}
                                                        </div>
                                                    </div>
                                                );
                                            })}

                                            {/* Load more section */}
                                            <div className="col-span-full py-4 flex flex-col items-center justify-center">
                                                {loadingMore && (
                                                    <div className="flex flex-col items-center">
                                                        <div className="w-8 h-8 border-4 border-gray-300 border-t-red-500 rounded-full animate-spin mb-2"></div>
                                                        <p className="text-gray-500 text-sm">Loading more posts...</p>
                                                    </div>
                                                )}

                                                {/* End of content message */}
                                                {!hasMore && visiblePosts.length > 0 && (
                                                    <div className="flex flex-col items-center">
                                                        <p className="text-gray-500">You've reached the end of your {contentType === 'all-posts' ? 'posts' : contentType}</p>
                                                        <p className="text-gray-400 text-sm mt-1">Total {contentType === 'all-posts' ? 'posts' : contentType}: {
                                                            contentType === 'all-posts' ? totalPostsCount || visiblePosts.length :
                                                                contentType === 'flashes' ? userFlashes.length :
                                                                    contentType === 'glimpses' ? userGlimpses.length :
                                                                        contentType === 'movies' ? userMovies.length :
                                                                            contentType === 'photos' ? userPhotos.length :
                                                                                visiblePosts.length
                                                        }</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}

                            {activeTab === 'flashes' && (
                                <div>
                                    {loading ? (
                                        <div className="py-10 text-center max-w-4xl mx-auto">
                                            <div className="inline-block w-8 h-8 border-4 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
                                            <p className="mt-2 text-gray-600">Loading your flashes...</p>
                                        </div>
                                    ) : error ? (
                                        <div className="py-10 text-center text-red-500 max-w-4xl mx-auto">
                                            <p>{error}</p>
                                            <button
                                                onClick={fetchUserContent}
                                                className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md"
                                            >
                                                Try Again
                                            </button>
                                        </div>
                                    ) : loading ? (
                                        <div className="py-10 text-center max-w-4xl mx-auto">
                                            <div className="inline-block w-8 h-8 border-4 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
                                            <p className="mt-2 text-gray-600">Loading your flashes...</p>
                                        </div>
                                    ) : userFlashes.length === 0 ? (
                                        <div className="py-10 text-center text-gray-500 max-w-4xl mx-auto">
                                            <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                            </svg>
                                            <p className="text-lg font-medium">No flashes yet</p>
                                            <p className="mt-1">Create your first flash to share quick updates</p>
                                            <button
                                                className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md"
                                                onClick={() => router.push('/create-flash')}
                                            >
                                                Create Flash
                                            </button>
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-3 gap-1 sm:gap-2 mt-2 sm:mt-4 mx-auto px-1 sm:px-0 max-w-4xl">
                                            {visibleFlashes.map((flash, index) => {
                                                // Create a truly unique key for each flash
                                                // Use unique_id if available, otherwise generate one
                                                // Add a random string to ensure uniqueness
                                                const flashId = flash.unique_id || flash.video_id ||
                                                    `flash-${index}-${Math.random().toString(36).substring(2, 15)}`;
                                                const uniqueKey = `flash-${flashId}-${index}`;

                                                return (
                                                    <div
                                                        key={uniqueKey}
                                                        className="aspect-square relative overflow-hidden rounded-md cursor-pointer transform transition hover:scale-[0.99]"
                                                        style={{
                                                            padding: '2px',
                                                            width: '100%',
                                                            height: '90%',
                                                            background: 'linear-gradient(45deg, #ff6b6b, #ff8e8e)'
                                                        }}
                                                        onClick={() => {
                                                            navigateWithAuth(`/home/<USER>/shorts?index=${index}`);
                                                        }}
                                                    >
                                                        <img
                                                            src={flash.video_thumbnail || '/pics/placeholder.svg'}
                                                            alt={`Flash ${flash.video_id}`}
                                                            className="w-full h-full object-cover rounded-md"
                                                        />
                                                        {/* Hover overlay with smaller icons */}
                                                        <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 flex items-center justify-center transition-opacity duration-200 rounded-md">
                                                            <div className="flex items-center text-white mx-1 sm:mx-2">
                                                                <svg className="w-3 h-3 sm:w-5 sm:h-5 mr-1" fill="white" viewBox="0 0 24 24">
                                                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                                                                </svg>
                                                                <span className="text-[10px] sm:text-sm mr-2 sm:mr-3">{flash.video_likes || 0}</span>
                                                                <svg className="w-3 h-3 sm:w-5 sm:h-5 mr-1" fill="white" viewBox="0 0 24 24">
                                                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10z" />
                                                                </svg>
                                                                <span className="text-[10px] sm:text-sm">{flash.video_comments || 0}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            })}

                                            {/* Load more section */}
                                            <div className="col-span-full py-4 flex flex-col items-center justify-center">
                                                {loadingMore && (
                                                    <div className="w-8 h-8 border-4 border-gray-300 border-t-red-500 rounded-full animate-spin mb-2"></div>
                                                )}

                                                {/* End of content message */}
                                                {!hasMore && visibleFlashes.length > 0 && (
                                                    <div className="flex flex-col items-center">
                                                        <p className="text-gray-500">You've reached the end of your flashes</p>
                                                        <p className="text-gray-400 text-sm mt-1">Total flashes: {userFlashes.length}</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}

                            {activeTab === 'saved' && (
                                <div className="py-10 text-center text-gray-500 max-w-4xl mx-auto">
                                    <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                    </svg>
                                    <p className="text-lg font-medium">No saved items</p>
                                    <p className="mt-1">Save photos and videos to revisit them later</p>
                                </div>
                            )}

                            {activeTab === 'tagged' && (
                                <div className="py-10 text-center text-gray-500 max-w-4xl mx-auto">
                                    <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                    </svg>
                                    <p className="text-lg font-medium">No tagged posts</p>
                                    <p className="mt-1">Posts you're tagged in will appear here</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Mobile Navigation - only visible on small screens */}
                <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
                    <MobileNavigation />
                </div>
            </div>

            {/* Edit Profile Modal */}
            <EditProfileModal
                isOpen={isEditProfileModalOpen}
                onClose={() => setIsEditProfileModalOpen(false)}
                userProfile={userProfileData}
                onProfileUpdate={() => {
                    // Refresh user profile data after update
                    if (token) {
                        fetchUserProfile(token, externalUserId).then(profileData => {
                            if (profileData && !externalUserId) {
                                // Only update the AuthContext if this is the current user's profile
                                updateProfile(profileData);
                                console.log('Updated user profile in AuthContext:', profileData);
                            }
                        });
                    }
                }}
            />

            {/* Calendar Modal */}
            {calendarModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-[#FFF3D8] rounded-lg shadow-lg max-w-md w-full mx-4 overflow-hidden">
                        {/* Close button */}
                        <div className="flex justify-end p-2">
                            <button
                                onClick={() => setCalendarModalOpen(false)}
                                className="text-gray-700 hover:text-gray-900"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        {/* Calendar content */}
                        <div className="p-4">
                            <div className="bg-white rounded-lg p-4 shadow-sm">
                                {/* Month navigation */}
                                <div className="flex justify-between items-center mb-4">
                                    <h2 className="text-xl font-bold">
                                        {selectedDate.toLocaleString('default', { month: 'long' })} {selectedDate.getFullYear()}
                                    </h2>
                                    <div className="flex space-x-2">
                                        <button
                                            className="bg-gray-200 rounded-full p-1"
                                            onClick={() => {
                                                const newDate = new Date(selectedDate);
                                                newDate.setMonth(newDate.getMonth() - 1);
                                                setSelectedDate(newDate);
                                            }}
                                        >
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                                            </svg>
                                        </button>
                                        <button
                                            className="bg-gray-200 rounded-full p-1"
                                            onClick={() => {
                                                const newDate = new Date(selectedDate);
                                                newDate.setMonth(newDate.getMonth() + 1);
                                                setSelectedDate(newDate);
                                            }}
                                        >
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                {/* Days of week */}
                                <div className="grid grid-cols-7 gap-1 text-center mb-2">
                                    <div className="text-gray-600 font-medium">S</div>
                                    <div className="text-gray-600 font-medium">M</div>
                                    <div className="text-gray-600 font-medium">T</div>
                                    <div className="text-gray-600 font-medium">W</div>
                                    <div className="text-gray-600 font-medium">T</div>
                                    <div className="text-gray-600 font-medium">F</div>
                                    <div className="text-gray-600 font-medium">S</div>
                                </div>

                                {/* Calendar grid */}
                                <div className="grid grid-cols-7 gap-1 text-center">
                                    {(() => {
                                        // Get the first day of the month
                                        const firstDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);
                                        // Get the last day of the month
                                        const lastDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0);
                                        // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
                                        const firstDayOfWeek = firstDay.getDay();
                                        // Get the number of days in the month
                                        const daysInMonth = lastDay.getDate();

                                        // Get the last day of the previous month
                                        const prevMonthLastDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 0).getDate();

                                        // Create an array to hold all the day elements
                                        const days = [];

                                        // Add days from the previous month
                                        for (let i = 0; i < firstDayOfWeek; i++) {
                                            const prevMonthDay = prevMonthLastDay - firstDayOfWeek + i + 1;
                                            days.push(
                                                <div key={`prev-${i}`} className="py-2 text-gray-400">
                                                    {prevMonthDay}
                                                </div>
                                            );
                                        }

                                        // Add days from the current month
                                        for (let i = 1; i <= daysInMonth; i++) {
                                            const date = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), i);
                                            const isSelected =
                                                date.getDate() === selectedDate.getDate() &&
                                                date.getMonth() === selectedDate.getMonth() &&
                                                date.getFullYear() === selectedDate.getFullYear();

                                            days.push(
                                                <div
                                                    key={`current-${i}`}
                                                    className={`py-2 cursor-pointer hover:bg-gray-100 ${isSelected
                                                            ? 'bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto'
                                                            : ''
                                                        }`}
                                                    onClick={() => {
                                                        const newDate = new Date(selectedDate);
                                                        newDate.setDate(i);
                                                        setSelectedDate(newDate);
                                                    }}
                                                >
                                                    {i}
                                                </div>
                                            );
                                        }

                                        // Add days from the next month
                                        const totalDaysDisplayed = days.length;
                                        const daysToAdd = 42 - totalDaysDisplayed; // 6 rows of 7 days

                                        for (let i = 1; i <= daysToAdd; i++) {
                                            days.push(
                                                <div key={`next-${i}`} className="py-2 text-gray-400">
                                                    {i}
                                                </div>
                                            );
                                        }

                                        return days;
                                    })()}
                                </div>
                            </div>

                            {/* Event title input */}
                            <div className="mt-4">
                                <input
                                    type="text"
                                    placeholder="Add Title Here"
                                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                                    value={eventTitle}
                                    onChange={(e) => setEventTitle(e.target.value)}
                                />
                            </div>

                            {/* Save button */}
                            <div className="mt-4">
                                <button
                                    className="w-full bg-red-500 text-white py-2 px-4 rounded-md hover:bg-red-600 transition-colors flex items-center justify-center"
                                    onClick={() => {
                                        // Handle save event
                                        console.log('Saving event:', {
                                            date: selectedDate,
                                            title: eventTitle
                                        });
                                        setCalendarModalOpen(false);
                                    }}
                                >
                                    <span>Save</span>
                                    <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default UserProfile;