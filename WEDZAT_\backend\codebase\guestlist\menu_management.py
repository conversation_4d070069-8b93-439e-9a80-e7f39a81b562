import json
import uuid
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from .utils import get_db_connection, validate_token

def get_menu_options(event):
    """Get all menu options for a user"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute(
            '''SELECT * FROM wedding_menu_options
            WHERE user_id = %s OR user_id IS NULL
            ORDER BY name ASC''',
            (user_id,)
        )

        menu_options = cursor.fetchall()

        # Convert date objects to strings for JSON serialization
        serializable_options = []
        for option in menu_options:
            option_dict = dict(option)
            for key, value in option_dict.items():
                if isinstance(value, (datetime, date)):
                    option_dict[key] = value.isoformat()
            serializable_options.append(option_dict)

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'menu_options': serializable_options})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def add_menu_option(event):
    """Add a new menu option"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event['body'])
        name = data.get('name')
        description = data.get('description', '')

        if not name:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Menu option name is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            menu_id = str(uuid.uuid4())

            cursor.execute(
                '''INSERT INTO wedding_menu_options
                (menu_id, user_id, name, description, is_default, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (menu_id, user_id, name, description, False)
            )

            new_option = cursor.fetchone()

            # Convert to a regular Python dict
            option_dict = dict(new_option)

            # Convert date objects to strings
            for key, value in option_dict.items():
                if isinstance(value, (datetime, date)):
                    option_dict[key] = value.isoformat()

            conn.commit()

            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'menu_option': option_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def update_menu_option(event):
    """Update a menu option"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event['body'])
        menu_id = data.get('menu_id')
        name = data.get('name')
        description = data.get('description')

        if not menu_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Menu option ID is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Check if the menu option belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_menu_options
                WHERE menu_id = %s AND user_id = %s''',
                (menu_id, user_id)
            )

            existing_option = cursor.fetchone()
            if not existing_option:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Menu option not found or cannot be modified'})
                }

            # Update fields
            cursor.execute(
                '''UPDATE wedding_menu_options
                SET name = COALESCE(%s, name),
                    description = COALESCE(%s, description),
                    updated_at = NOW()
                WHERE menu_id = %s AND user_id = %s
                RETURNING *''',
                (name, description, menu_id, user_id)
            )

            updated_option = cursor.fetchone()

            # Convert to a regular Python dict
            option_dict = dict(updated_option)

            # Convert date objects to strings
            for key, value in option_dict.items():
                if isinstance(value, (datetime, date)):
                    option_dict[key] = value.isoformat()

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'menu_option': option_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def delete_menu_option(event):
    """Delete a menu option"""

    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        data = json.loads(event.get('body', '{}'))
        menu_id = data.get('menu_id')

        if not menu_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Menu option ID is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if the menu option belongs to the user and is not a default option
            cursor.execute(
                '''SELECT * FROM wedding_menu_options
                WHERE menu_id = %s AND user_id = %s AND is_default = FALSE''',
                (menu_id, user_id)
            )

            existing_option = cursor.fetchone()
            if not existing_option:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Menu option not found or cannot be deleted'})
                }

            # Delete the menu option
            cursor.execute(
                '''DELETE FROM wedding_menu_options
                WHERE menu_id = %s AND user_id = %s''',
                (menu_id, user_id)
            )

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'message': 'Menu option deleted successfully'})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
