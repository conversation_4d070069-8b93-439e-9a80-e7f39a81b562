/* Add to existing globals.css */

/* Safe area variables */
:root {
  --safe-area-top: env(safe-area-inset-top, 0px);
  --safe-area-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-left: env(safe-area-inset-left, 0px);
  --safe-area-right: env(safe-area-inset-right, 0px);
}

/* Safe area utilities */
.safe-area-top {
  padding-top: var(--safe-area-top);
}

.safe-area-bottom {
  padding-bottom: var(--safe-area-bottom);
}

.pb-safe {
  padding-bottom: var(--safe-area-bottom);
}

.pt-safe {
  padding-top: var(--safe-area-top);
}

.top-safe {
  top: var(--safe-area-top);
}

/* Fix for mobile viewport height issues */
@supports (-webkit-touch-callout: none) {
  .min-h-screen {
    min-height: -webkit-fill-available;
  }
}

/* Optimize scrolling on mobile */
.moments-page {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-y: contain;
}

/* Prevent content shift when loading images */
.aspect-square {
  aspect-ratio: 1/1;
} 