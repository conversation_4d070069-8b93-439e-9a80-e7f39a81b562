import os
import json
import jwt
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
from datetime import date, datetime

# Load environment variables
load_dotenv()

# Database and JWT configuration
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
JWT_SECRET = os.getenv('JWT_SECRET')

def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME,
        cursor_factory=RealDictCursor
    )

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        if token.startswith('Bearer '):
            token = token[7:]
        else:
            token = token.split()[1]
        
        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

def search_content(event):
    """
    Flexible search API for all uploaded content (videos and photos)
    Returns results organized by content type: stories[], flashes[], glimpses[], movies[], photos[]
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        
        # Search parameters
        search_query = query_params.get('q', '').strip()  # General search query
        username = query_params.get('username', '').strip()
        title = query_params.get('title', '').strip()
        tags = query_params.get('tags', '').strip()
        place = query_params.get('place', '').strip()
        wedding_style = query_params.get('wedding_style', '').strip()
        
        # Content type filters - support multiple types separated by comma
        content_types = query_params.get('types', 'photos,flashes,glimpses,movies').strip()
        content_types_list = [t.strip().lower() for t in content_types.split(',') if t.strip()]
        
        # Pagination per content type
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1
        
        limit_per_type = int(query_params.get('limit', 10))
        if limit_per_type > 50:
            limit_per_type = 50
        
        offset = (page - 1) * limit_per_type

        # Sort parameters
        sort_by = query_params.get('sort_by', 'created_at').strip()
        sort_order = query_params.get('sort_order', 'desc').strip().upper()
        
        if sort_order not in ['ASC', 'DESC']:
            sort_order = 'DESC'
            
        # Map sort fields to correct table references
        def get_sort_field(sort_by, table_type):
            if table_type == 'video':
                if sort_by == 'views':
                    return 'vs.video_views'
                elif sort_by == 'likes':
                    return 'vs.video_likes'
                elif sort_by == 'name':
                    return 'v.video_name'
                else:
                    return f'v.{sort_by}'
            else:  # photo
                if sort_by == 'views':
                    return 'ps.photo_views'
                elif sort_by == 'likes':
                    return 'ps.photo_likes'
                elif sort_by == 'name':
                    return 'p.photo_name'
                else:
                    return f'p.{sort_by}'

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Initialize result structure
            results = {
                'flashes': [],
                'glimpses': [],
                'movies': [],
                'photos': []
            }

            # Build common WHERE conditions for search
            def build_search_conditions(table_prefix, name_field, desc_field, tag_field):
                conditions = []
                params = []
                
                if search_query:
                    conditions.append(f"""(
                        LOWER({table_prefix}.{name_field}) LIKE LOWER(%s) OR 
                        LOWER({table_prefix}.{desc_field}) LIKE LOWER(%s) OR 
                        LOWER(u.name) LIKE LOWER(%s) OR
                        EXISTS (
                            SELECT 1 FROM jsonb_array_elements_text({table_prefix}.{tag_field}) AS tag 
                            WHERE LOWER(tag) LIKE LOWER(%s)
                        )
                    )""")
                    search_pattern = f"%{search_query}%"
                    params.extend([search_pattern, search_pattern, search_pattern, search_pattern])
                
                if username:
                    conditions.append("LOWER(u.name) LIKE LOWER(%s)")
                    params.append(f"%{username}%")
                
                if title:
                    conditions.append(f"LOWER({table_prefix}.{name_field}) LIKE LOWER(%s)")
                    params.append(f"%{title}%")
                
                if tags:
                    conditions.append(f"""EXISTS (
                        SELECT 1 FROM jsonb_array_elements_text({table_prefix}.{tag_field}) AS tag 
                        WHERE LOWER(tag) LIKE LOWER(%s)
                    )""")
                    params.append(f"%{tags}%")
                
                if place:
                    conditions.append("LOWER(mpd.place) LIKE LOWER(%s)")
                    params.append(f"%{place}%")
                
                if wedding_style:
                    conditions.append("LOWER(mpd.wedding_style) LIKE LOWER(%s)")
                    params.append(f"%{wedding_style}%")
                
                return conditions, params

            # Search Photos (photos with subtype 'post')
            if 'photos' in content_types_list:
                photo_conditions, photo_params = build_search_conditions('p', 'photo_name', 'photo_description', 'photo_tags')
                photo_where = "WHERE p.photo_subtype = 'post'"
                if photo_conditions:
                    photo_where += " AND " + " AND ".join(photo_conditions)

                photos_query = f"""
                    SELECT
                        p.photo_id,
                        p.photo_name,
                        p.photo_url,
                        p.photo_description,
                        p.photo_tags,
                        p.photo_subtype,
                        p.created_at,
                        u.name AS user_name,
                        p.user_id,
                        CASE WHEN p.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        ps.photo_views,
                        ps.photo_likes,
                        ps.photo_comments
                    FROM photos p
                    JOIN users u ON p.user_id = u.user_id
                    LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
                    LEFT JOIN media_personal_details mpd ON p.photo_id = mpd.media_id AND mpd.media_type = 'photo'
                    {photo_where}
                    ORDER BY {get_sort_field(sort_by, 'photo')} {sort_order}
                    LIMIT %s OFFSET %s
                """
                
                params = [user_id] + photo_params + [limit_per_type, offset]
                cursor.execute(photos_query, params)
                results['photos'] = cursor.fetchall()

            # Search Flashes (videos with subtype 'flash')
            if 'flashes' in content_types_list:
                flash_conditions, flash_params = build_search_conditions('v', 'video_name', 'video_description', 'video_tags')
                flash_where = "WHERE v.video_subtype = 'flash'"
                if flash_conditions:
                    flash_where += " AND " + " AND ".join(flash_conditions)

                flashes_query = f"""
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    {flash_where}
                    ORDER BY {get_sort_field(sort_by, 'video')} {sort_order}
                    LIMIT %s OFFSET %s
                """
                
                params = [user_id] + flash_params + [limit_per_type, offset]
                cursor.execute(flashes_query, params)
                results['flashes'] = cursor.fetchall()

            # Search Glimpses (videos with subtype 'glimpse')
            if 'glimpses' in content_types_list:
                glimpse_conditions, glimpse_params = build_search_conditions('v', 'video_name', 'video_description', 'video_tags')
                glimpse_where = "WHERE v.video_subtype = 'glimpse'"
                if glimpse_conditions:
                    glimpse_where += " AND " + " AND ".join(glimpse_conditions)

                glimpses_query = f"""
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    {glimpse_where}
                    ORDER BY {get_sort_field(sort_by, 'video')} {sort_order}
                    LIMIT %s OFFSET %s
                """
                
                params = [user_id] + glimpse_params + [limit_per_type, offset]
                cursor.execute(glimpses_query, params)
                results['glimpses'] = cursor.fetchall()

            # Search Movies (videos with subtype 'movie')
            if 'movies' in content_types_list:
                movie_conditions, movie_params = build_search_conditions('v', 'video_name', 'video_description', 'video_tags')
                movie_where = "WHERE v.video_subtype = 'movie'"
                if movie_conditions:
                    movie_where += " AND " + " AND ".join(movie_conditions)

                movies_query = f"""
                    SELECT
                        v.video_id,
                        v.video_name,
                        v.video_url,
                        v.video_description,
                        v.video_tags,
                        v.video_thumbnail,
                        v.video_duration,
                        v.video_category,
                        v.created_at,
                        u.name AS user_name,
                        v.user_id,
                        CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        vs.video_views,
                        vs.video_likes,
                        vs.video_comments
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                    {movie_where}
                    ORDER BY {get_sort_field(sort_by, 'video')} {sort_order}
                    LIMIT %s OFFSET %s
                """
                
                params = [user_id] + movie_params + [limit_per_type, offset]
                cursor.execute(movies_query, params)
                results['movies'] = cursor.fetchall()

            # Calculate total counts for each content type
            total_counts = {}
            
            for content_type in content_types_list:
                if content_type == 'photos':
                    photo_conditions, photo_params = build_search_conditions('p', 'photo_name', 'photo_description', 'photo_tags')
                    photo_where = "WHERE p.photo_subtype = 'post'"
                    if photo_conditions:
                        photo_where += " AND " + " AND ".join(photo_conditions)
                    
                    count_query = f"""
                        SELECT COUNT(*) as count
                        FROM photos p
                        JOIN users u ON p.user_id = u.user_id
                        LEFT JOIN media_personal_details mpd ON p.photo_id = mpd.media_id AND mpd.media_type = 'photo'
                        {photo_where}
                    """
                    cursor.execute(count_query, photo_params)
                    total_counts['photos'] = cursor.fetchone()['count']
                    
                else:  # flashes, glimpses, movies
                    video_conditions, video_params = build_search_conditions('v', 'video_name', 'video_description', 'video_tags')
                    video_where = f"WHERE v.video_subtype = '{content_type[:-1]}'"  # Remove 's' from plural
                    if video_conditions:
                        video_where += " AND " + " AND ".join(video_conditions)
                    
                    count_query = f"""
                        SELECT COUNT(*) as count
                        FROM videos v
                        JOIN users u ON v.user_id = u.user_id
                        LEFT JOIN media_personal_details mpd ON v.video_id = mpd.media_id AND mpd.media_type = 'video'
                        {video_where}
                    """
                    cursor.execute(count_query, video_params)
                    total_counts[content_type] = cursor.fetchone()['count']

            # Calculate pagination info for each content type
            pagination_info = {}
            for content_type in content_types_list:
                total_count = total_counts.get(content_type, 0)
                has_next_page = (offset + len(results[content_type])) < total_count
                total_pages = (total_count + limit_per_type - 1) // limit_per_type
                
                pagination_info[content_type] = {
                    "current_page": page,
                    "total_pages": total_pages,
                    "total_count": total_count,
                    "has_next_page": has_next_page,
                    "has_prev_page": page > 1,
                    "limit": limit_per_type
                }

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    **results,  # stories[], flashes[], glimpses[], movies[], photos[]
                    "pagination": pagination_info,
                    "search_params": {
                        "query": search_query,
                        "username": username,
                        "title": title,
                        "tags": tags,
                        "place": place,
                        "wedding_style": wedding_style,
                        "content_types": content_types_list,
                        "sort_by": sort_by,
                        "sort_order": sort_order
                    }
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_search_suggestions(event):
    """
    Get search suggestions for autocomplete functionality
    Returns popular tags, usernames, places, and wedding styles
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        suggestion_type = query_params.get('type', 'all').strip().lower()
        query = query_params.get('q', '').strip()
        limit = int(query_params.get('limit', 10))

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        suggestions = {}

        try:
            # Get username suggestions
            if suggestion_type in ['all', 'usernames']:
                username_query = """
                    SELECT DISTINCT u.name
                    FROM users u
                    WHERE LOWER(u.name) LIKE LOWER(%s)
                    ORDER BY u.name
                    LIMIT %s
                """
                cursor.execute(username_query, (f"%{query}%", limit))
                suggestions['usernames'] = [row['name'] for row in cursor.fetchall()]

            # Get tag suggestions from both videos and photos
            if suggestion_type in ['all', 'tags']:
                tag_query = """
                    SELECT DISTINCT tag
                    FROM (
                        SELECT jsonb_array_elements_text(video_tags) AS tag FROM videos
                        UNION ALL
                        SELECT jsonb_array_elements_text(photo_tags) AS tag FROM photos
                    ) AS all_tags
                    WHERE LOWER(tag) LIKE LOWER(%s)
                    ORDER BY tag
                    LIMIT %s
                """
                cursor.execute(tag_query, (f"%{query}%", limit))
                suggestions['tags'] = [row['tag'] for row in cursor.fetchall()]

            # Get place suggestions
            if suggestion_type in ['all', 'places']:
                place_query = """
                    SELECT DISTINCT mpd.place
                    FROM media_personal_details mpd
                    WHERE mpd.place IS NOT NULL 
                    AND LOWER(mpd.place) LIKE LOWER(%s)
                    ORDER BY mpd.place
                    LIMIT %s
                """
                cursor.execute(place_query, (f"%{query}%", limit))
                suggestions['places'] = [row['place'] for row in cursor.fetchall()]

            # Get wedding style suggestions
            if suggestion_type in ['all', 'wedding_styles']:
                style_query = """
                    SELECT DISTINCT mpd.wedding_style
                    FROM media_personal_details mpd
                    WHERE mpd.wedding_style IS NOT NULL 
                    AND LOWER(mpd.wedding_style) LIKE LOWER(%s)
                    ORDER BY mpd.wedding_style
                    LIMIT %s
                """
                cursor.execute(style_query, (f"%{query}%", limit))
                suggestions['wedding_styles'] = [row['wedding_style'] for row in cursor.fetchall()]

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "suggestions": suggestions,
                    "query": query,
                    "type": suggestion_type
                })
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }