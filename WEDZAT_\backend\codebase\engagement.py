import os
import json
import uuid
import jwt
import psycopg2
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

def validate_token(headers):
    token = headers.get('Authorization')
    
    if not token:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Token is required"})
        }
        
    try:
        data = jwt.decode(token.split()[1], JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Invalid token"})
        }

def like_content(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Parse request body
        data = json.loads(event['body'])
        content_id = data.get('content_id')
        content_type = data.get('content_type', '').lower()
        
        if not content_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Content ID is required"})
            }
            
        if content_type not in ['video', 'photo']:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Content type must be 'video' or 'photo'"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check if content exists
        table_name = f"{content_type}s"
        cursor.execute(f"SELECT 1 FROM {table_name} WHERE {content_type}_id = %s", (content_id,))
        if not cursor.fetchone():
            return {
                'statusCode': 404,
                'body': json.dumps({"error": f"{content_type.capitalize()} not found"})
            }
            
        try:
            cursor.execute("BEGIN")
            
            # Check if already liked
            cursor.execute(
                "SELECT like_id FROM likes WHERE content_id = %s AND content_type = %s AND user_id = %s",
                (content_id, content_type, user_id)
            )
            existing_like = cursor.fetchone()
            
            if existing_like:
                return {
                    'statusCode': 400,
                    'body': json.dumps({"error": f"Already liked this {content_type}"})
                }
                
            # Add like
            like_id = str(uuid.uuid4())
            cursor.execute(
                "INSERT INTO likes (like_id, content_id, content_type, user_id) VALUES (%s, %s, %s, %s)",
                (like_id, content_id, content_type, user_id)
            )
            
            # Update like count in stats table
            stats_table = f"{content_type}_stats"
            cursor.execute(
                f"UPDATE {stats_table} SET {content_type}_likes = {content_type}_likes + 1 WHERE {content_type}_id = %s",
                (content_id,)
            )
            
            cursor.execute("COMMIT")
            
            return {
                'statusCode': 200,
                'body': json.dumps({"message": f"{content_type.capitalize()} liked successfully", "like_id": like_id})
            }
            
        except Exception as e:
            cursor.execute("ROLLBACK")
            return {
                'statusCode': 500,
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def unlike_content(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Parse request body
        data = json.loads(event['body'])
        content_id = data.get('content_id')
        content_type = data.get('content_type', '').lower()
        
        if not content_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Content ID is required"})
            }
            
        if content_type not in ['video', 'photo']:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Content type must be 'video' or 'photo'"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("BEGIN")
            
            # Check if like exists
            cursor.execute(
                "SELECT like_id FROM likes WHERE content_id = %s AND content_type = %s AND user_id = %s",
                (content_id, content_type, user_id)
            )
            existing_like = cursor.fetchone()
            
            if not existing_like:
                return {
                    'statusCode': 400,
                    'body': json.dumps({"error": f"Not liked this {content_type}"})
                }
                
            # Remove like
            cursor.execute(
                "DELETE FROM likes WHERE content_id = %s AND content_type = %s AND user_id = %s",
                (content_id, content_type, user_id)
            )
            
            # Update like count in stats table
            stats_table = f"{content_type}_stats"
            cursor.execute(
                f"UPDATE {stats_table} SET {content_type}_likes = GREATEST({content_type}_likes - 1, 0) WHERE {content_type}_id = %s",
                (content_id,)
            )
            
            cursor.execute("COMMIT")
            
            return {
                'statusCode': 200,
                'body': json.dumps({"message": f"{content_type.capitalize()} unliked successfully"})
            }
            
        except Exception as e:
            cursor.execute("ROLLBACK")
            return {
                'statusCode': 500,
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def comment_content(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Parse request body
        data = json.loads(event['body'])
        content_id = data.get('content_id')
        content_type = data.get('content_type', '').lower()
        comment_text = data.get('comment_text', '').strip()
        parent_comment_id = data.get('parent_comment_id')
        
        if not content_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Content ID is required"})
            }
            
        if content_type not in ['video', 'photo']:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Content type must be 'video' or 'photo'"})
            }
            
        if not comment_text:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Comment text is required"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check if content exists
        table_name = f"{content_type}s"
        cursor.execute(f"SELECT 1 FROM {table_name} WHERE {content_type}_id = %s", (content_id,))
        if not cursor.fetchone():
            return {
                'statusCode': 404,
                'body': json.dumps({"error": f"{content_type.capitalize()} not found"})
            }
            
        # Check if parent comment exists if provided
        if parent_comment_id:
            cursor.execute("SELECT 1 FROM comments WHERE comment_id = %s", (parent_comment_id,))
            if not cursor.fetchone():
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Parent comment not found"})
                }
        
        try:
            cursor.execute("BEGIN")
            
            # Add comment
            comment_id = str(uuid.uuid4())
            cursor.execute(
                """INSERT INTO comments (comment_id, content_id, content_type, user_id, comment_text, parent_comment_id)
                   VALUES (%s, %s, %s, %s, %s, %s) RETURNING created_at""",
                (comment_id, content_id, content_type, user_id, comment_text, parent_comment_id)
            )
            created_at = cursor.fetchone()[0]
            
            # Update comment count in stats table
            stats_table = f"{content_type}_stats"
            cursor.execute(
                f"UPDATE {stats_table} SET {content_type}_comments = {content_type}_comments + 1 WHERE {content_type}_id = %s",
                (content_id,)
            )
            
            # Get user info
            cursor.execute("SELECT name FROM users WHERE user_id = %s", (user_id,))
            username = cursor.fetchone()[0]
            
            cursor.execute("COMMIT")
            
            return {
                'statusCode': 201,
                'body': json.dumps({
                    "message": "Comment added successfully",
                    "comment_id": comment_id,
                    "created_at": created_at.isoformat(),
                    "username": username,
                    "user_id": user_id
                })
            }
            
        except Exception as e:
            cursor.execute("ROLLBACK")
            return {
                'statusCode': 500,
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def get_comments(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Get query parameters
        params = event.get('queryStringParameters', {}) or {}
        content_id = params.get('content_id')
        content_type = params.get('content_type', '').lower()
        parent_id = params.get('parent_id')
        page = int(params.get('page', 1))
        page_size = min(int(params.get('page_size', 20)), 50)  # Limit to max 50 per page
        
        if not content_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Content ID is required"})
            }
            
        if content_type not in ['video', 'photo']:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Content type must be 'video' or 'photo'"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Build query
        query = """
            SELECT c.comment_id, c.user_id, u.name AS username, c.comment_text, 
                   c.parent_comment_id, c.created_at,
                   (SELECT COUNT(*) FROM comments WHERE parent_comment_id = c.comment_id) AS replies_count
            FROM comments c
            JOIN users u ON c.user_id = u.user_id
            WHERE c.content_id = %s AND c.content_type = %s
        """
        query_params = [content_id, content_type]
        
        if parent_id:
            query += " AND c.parent_comment_id = %s"
            query_params.append(parent_id)
        else:
            query += " AND c.parent_comment_id IS NULL"
        
        query += " ORDER BY c.created_at DESC LIMIT %s OFFSET %s"
        query_params.extend([page_size, (page - 1) * page_size])
        
        cursor.execute(query, query_params)
        comments = cursor.fetchall()
        
        # Count total comments for pagination
        count_query = """
            SELECT COUNT(*)
            FROM comments
            WHERE content_id = %s AND content_type = %s
        """
        count_params = [content_id, content_type]
        
        if parent_id:
            count_query += " AND parent_comment_id = %s"
            count_params.append(parent_id)
        else:
            count_query += " AND parent_comment_id IS NULL"
            
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        # Format results
        results = []
        for comment in comments:
            results.append({
                "comment_id": comment[0],
                "user_id": comment[1],
                "username": comment[2],
                "comment_text": comment[3],
                "parent_comment_id": comment[4],
                "created_at": comment[5].isoformat() if comment[5] else None,
                "replies_count": comment[6],
                "is_owner": comment[1] == user_id
            })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                "comments": results,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            })
        }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def delete_comment(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Parse request body
        data = json.loads(event['body'])
        comment_id = data.get('comment_id')
        
        if not comment_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Comment ID is required"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check if comment exists and belongs to user
        cursor.execute(
            "SELECT content_id, content_type FROM comments WHERE comment_id = %s AND user_id = %s",
            (comment_id, user_id)
        )
        result = cursor.fetchone()
        
        if not result:
            # Check if user is admin or moderator
            cursor.execute("SELECT user_type FROM users WHERE user_id = %s", (user_id,))
            user_type = cursor.fetchone()[0]
            
            if user_type not in ['admin', 'moderator']:
                return {
                    'statusCode': 403,
                    'body': json.dumps({"error": "Not authorized to delete this comment"})
                }
                
            # Admin/moderator can delete any comment, so get content info
            cursor.execute(
                "SELECT content_id, content_type FROM comments WHERE comment_id = %s",
                (comment_id,)
            )
            result = cursor.fetchone()
            
            if not result:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Comment not found"})
                }
        
        content_id, content_type = result
        
        try:
            cursor.execute("BEGIN")
            
            # Count comments to be deleted (including replies)
            cursor.execute("""
                WITH RECURSIVE comment_tree AS (
                    SELECT comment_id FROM comments WHERE comment_id = %s
                    UNION ALL
                    SELECT c.comment_id FROM comments c
                    JOIN comment_tree ct ON c.parent_comment_id = ct.comment_id
                )
                SELECT COUNT(*) FROM comment_tree
            """, (comment_id,))
            
            delete_count = cursor.fetchone()[0]
            
            # Delete comment and all replies
            cursor.execute("""
                WITH RECURSIVE comment_tree AS (
                    SELECT comment_id FROM comments WHERE comment_id = %s
                    UNION ALL
                    SELECT c.comment_id FROM comments c
                    JOIN comment_tree ct ON c.parent_comment_id = ct.comment_id
                )
                DELETE FROM comments WHERE comment_id IN (SELECT comment_id FROM comment_tree)
            """, (comment_id,))
            
            # Update comment count in stats table
            stats_table = f"{content_type}_stats"
            cursor.execute(
                f"UPDATE {stats_table} SET {content_type}_comments = GREATEST({content_type}_comments - %s, 0) WHERE {content_type}_id = %s",
                (delete_count, content_id)
            )
            
            cursor.execute("COMMIT")
            
            return {
                'statusCode': 200,
                'body': json.dumps({"message": "Comment deleted successfully"})
            }
            
        except Exception as e:
            cursor.execute("ROLLBACK")
            return {
                'statusCode': 500,
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()