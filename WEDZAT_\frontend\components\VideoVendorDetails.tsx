import React, { useState, useEffect } from 'react';
import VendorDetails from './VendorDetails';

interface VideoVendorDetailsProps {
  videoId: string;
  isVerified?: boolean;
}

const VideoVendorDetails: React.FC<VideoVendorDetailsProps> = ({
  videoId,
  isVerified = false
}) => {
  const [token, setToken] = useState<string>('');
  const [isBlurred, setIsBlurred] = useState<boolean>(!isVerified);

  useEffect(() => {
    // Function to safely access localStorage (avoid errors in SSR)
    const getFromLocalStorage = (key) => {
      try {
        if (typeof window !== 'undefined') {
          return localStorage.getItem(key);
        }
        return null;
      } catch (e) {
        console.error(`Error accessing localStorage for key ${key}:`, e);
        return null;
      }
    };

    // Get token from localStorage - try multiple possible keys
    // The backend expects a JWT token from Clerk
    const tokenFromClerk = getFromLocalStorage('__clerk_client_jwt');
    const tokenFromToken = getFromLocalStorage('token');
    const tokenFromJwtToken = getFromLocalStorage('jwt_token');
    const tokenFromAuthToken = getFromLocalStorage('auth_token');

    console.log('Checking for authentication token');

    // Use the Clerk token first, then fall back to other tokens
    const authToken = tokenFromClerk || tokenFromToken || tokenFromJwtToken || tokenFromAuthToken;

    if (authToken) {
      console.log('Using token from localStorage');
      setToken(authToken);
    } else {
      console.warn('No authentication token found in localStorage');

      // No token available
      console.warn('Authentication required to view vendor details');
      // We'll return null in the component if no token is available
    }
  }, []);

  const handleUnlock = () => {
    // In a real implementation, this would verify the user's mobile number
    // For now, we'll just unblur the details
    setIsBlurred(false);
  };

  // We need a token to fetch vendor details
  if (!token) {
    console.warn('No token available for VideoVendorDetails');
    return (
      <div className="mt-4 border-t pt-4">
        <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="flex items-center text-sm text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <span>Please log in to view vendor details for this video</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-4 border-t pt-4">
      <VendorDetails
        videoId={videoId}
        token={token}
        isBlurred={isBlurred}
        onUnlock={handleUnlock}
      />
    </div>
  );
};

export default VideoVendorDetails;
