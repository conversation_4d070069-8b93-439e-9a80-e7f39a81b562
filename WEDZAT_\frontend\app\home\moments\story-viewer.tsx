"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { useRouter } from "next/navigation";

// Define interface for story items
interface Story {
  content_id: string;
  content_name: string;
  content_url: string;
  content_description?: string;
  thumbnail_url?: string;
  duration?: number;
  created_at: string;
  user_name: string;
  content_type: 'video' | 'photo';
  is_own_content: boolean;
  viewed?: boolean;
}

interface StoryViewerProps {
  stories: Story[];
  initialStoryIndex: number;
  onClose: () => void;
  onStoryViewed: (storyId: string) => void;
}

export default function StoryViewer({ stories, initialStoryIndex, onClose, onStoryViewed }: StoryViewerProps) {
  const [currentIndex, setCurrentIndex] = useState(initialStoryIndex);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isAdmiring, setIsAdmiring] = useState(false);
  
  const progressInterval = useRef<NodeJS.Timeout | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const router = useRouter();

  const currentStory = stories[currentIndex];
  const storyDuration = currentStory?.content_type === 'video' ? 0 : 5000; // 5 seconds for photos

  // Setup progress timer
  useEffect(() => {
    if (!currentStory) return;
    
    // Mark story as viewed
    onStoryViewed(currentStory.content_id);
    
    // Clear any existing interval
    if (progressInterval.current) {
      clearInterval(progressInterval.current);
    }
    
    setProgress(0);
    
    // For photos, start progress timer
    if (currentStory.content_type === 'photo') {
      const interval = setInterval(() => {
        if (!isPaused) {
          setProgress(prev => {
            const newProgress = prev + (100 / (storyDuration / 100));
            if (newProgress >= 100) {
              clearInterval(interval);
              // Move to next story after completion
              setTimeout(() => {
                goToNextStory();
              }, 100);
              return 100;
            }
            return newProgress;
          });
        }
      }, 100);
      
      progressInterval.current = interval;
      
      return () => {
        clearInterval(interval);
      };
    }
    
    // For videos, progress is handled by video timeupdate event
  }, [currentIndex, isPaused, currentStory]);

  // Handle video progress
  const handleVideoTimeUpdate = () => {
    if (videoRef.current && currentStory?.content_type === 'video') {
      const percentage = (videoRef.current.currentTime / videoRef.current.duration) * 100;
      setProgress(percentage);
      
      // Move to next story when video ends
      if (percentage >= 99.5) {
        setTimeout(() => {
          goToNextStory();
        }, 500);
      }
    }
  };

  const goToPreviousStory = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setProgress(0);
    } else {
      // If at the first story, close the viewer
      onClose();
    }
  };

  const goToNextStory = () => {
    if (currentIndex < stories.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setProgress(0);
    } else {
      // If at the last story, close the viewer
      onClose();
    }
  };

  const togglePause = () => {
    setIsPaused(!isPaused);
    
    if (currentStory?.content_type === 'video' && videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play();
      } else {
        videoRef.current.pause();
      }
    }
  };

  const toggleAdmire = () => {
    setIsAdmiring(!isAdmiring);
  };

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      {/* Close button */}
      <button 
        onClick={onClose}
        className="absolute top-4 right-4 z-50 text-white p-2 rounded-full bg-black/30 hover:bg-black/50"
      >
        <X size={24} />
      </button>
      
      {/* Progress bar */}
      <div className="absolute top-4 left-4 right-4 z-40 flex space-x-1">
        {stories.map((_, index) => (
          <div key={index} className="h-1 flex-1 bg-white/30 rounded-full overflow-hidden">
            {index === currentIndex && (
              <div 
                className="h-full bg-white rounded-full" 
                style={{ width: `${progress}%`, transition: isPaused ? 'none' : 'width 0.1s linear' }}
              />
            )}
            {index < currentIndex && (
              <div className="h-full bg-white rounded-full w-full" />
            )}
          </div>
        ))}
      </div>
      
      {/* Navigation buttons */}
      <button 
        className="absolute left-2 top-1/2 transform -translate-y-1/2 z-40 text-white p-2 rounded-full bg-black/30 hover:bg-black/50"
        onClick={(e) => {
          e.stopPropagation();
          goToPreviousStory();
        }}
      >
        <ChevronLeft size={24} />
      </button>
      
      <button 
        className="absolute right-2 top-1/2 transform -translate-y-1/2 z-40 text-white p-2 rounded-full bg-black/30 hover:bg-black/50"
        onClick={(e) => {
          e.stopPropagation();
          goToNextStory();
        }}
      >
        <ChevronRight size={24} />
      </button>
      
      {/* Story content */}
      <div 
        className="relative w-full h-full max-w-md mx-auto"
        onClick={togglePause}
      >
        {currentStory?.content_type === 'photo' ? (
          <div className="relative w-full h-full">
            <Image
              src={currentStory.content_url}
              alt={currentStory.content_name || "Story"}
              fill
              className="object-contain"
              priority
              unoptimized={true}
            />
          </div>
        ) : (
          <div className="relative w-full h-full flex items-center justify-center">
            <video
              ref={videoRef}
              src={currentStory?.content_url}
              className="max-h-full max-w-full"
              autoPlay
              playsInline
              muted={false}
              controls={false}
              onTimeUpdate={handleVideoTimeUpdate}
            />
          </div>
        )}
        
        {/* User info at top */}
        <div className="absolute top-12 left-4 right-4 z-40 flex items-center">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full overflow-hidden relative border-2 border-white">
              <Image
                src="/pics/user-profile.png"
                alt={currentStory?.user_name || "User"}
                fill
                className="object-cover"
              />
            </div>
            <div className="ml-2">
              <div className="text-sm font-medium text-white">{currentStory?.user_name}</div>
              <div className="text-xs text-white/80">1.2M Admiring</div>
            </div>
          </div>
          
          {/* Admire button */}
          <button 
            className={`ml-auto ${isAdmiring ? 'bg-transparent border border-white text-white' : 'bg-[#B31B1E] text-white'} text-xs font-medium px-3 py-1 rounded-full`}
            onClick={(e) => {
              e.stopPropagation();
              toggleAdmire();
            }}
          >
            {isAdmiring ? 'Admiring' : 'Admire +'}
          </button>
        </div>
        
        {/* Vertical interaction buttons on the right */}
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col space-y-6 z-40">
          {/* 3-dots menu */}
          <button className="text-white bg-black/30 p-2 rounded-full opacity-80 hover:opacity-100" onClick={(e) => e.stopPropagation()}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="1"></circle>
              <circle cx="12" cy="5" r="1"></circle>
              <circle cx="12" cy="19" r="1"></circle>
            </svg>
          </button>
          
          {/* Like */}
          <button className="text-white bg-black/30 p-2 rounded-full opacity-80 hover:opacity-100" onClick={(e) => e.stopPropagation()}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
            </svg>
          </button>
          
          {/* Comment */}
          <button className="text-white bg-black/30 p-2 rounded-full opacity-80 hover:opacity-100" onClick={(e) => e.stopPropagation()}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
            </svg>
          </button>
          
          {/* Share */}
          <button className="text-white bg-black/30 p-2 rounded-full opacity-80 hover:opacity-100" onClick={(e) => e.stopPropagation()}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="22" y1="2" x2="11" y2="13"></line>
              <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
