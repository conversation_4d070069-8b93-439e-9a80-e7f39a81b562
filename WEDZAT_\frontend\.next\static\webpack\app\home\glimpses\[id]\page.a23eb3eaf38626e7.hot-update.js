"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./components/VideoInteractionBar.tsx":
/*!********************************************!*\
  !*** ./components/VideoInteractionBar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,MoreVertical,Plus,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MdFavorite,MdFavoriteBorder!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst VideoInteractionBar = (param)=>{\n    let { username, uploadDate, viewCount, description, userId, videoId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isCreatingChat, setIsCreatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiring, setIsAdmiring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAdmiringLoading, setIsAdmiringLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiking, setIsLiking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Format view count\n    const formatViewCount = (count)=>{\n        if (count >= 1000000) {\n            return \"\".concat((count / 1000000).toFixed(1), \"M\");\n        } else if (count >= 1000) {\n            return \"\".concat((count / 1000).toFixed(1), \"K\");\n        }\n        return count.toString();\n    };\n    // Handle message button click\n    const handleMessageClick = async ()=>{\n        if (isCreatingChat) return;\n        try {\n            setIsCreatingChat(true);\n            // Get token from localStorage - use userToken as in the chat page\n            const token = localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n            if (!token) {\n                console.error(\"No authentication token found\");\n                alert(\"Please log in to send messages\");\n                setIsCreatingChat(false);\n                return;\n            }\n            // Use the hardcoded URL from the messages page\n            const apiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\";\n            // If userId is 'default' or missing, use a fallback ID for testing\n            // In a real app, you would handle this differently\n            const participantId = !userId || userId === \"default\" ? username.toLowerCase().replace(/\\s+/g, \"_\") + \"_id\" : userId;\n            console.log(\"Creating conversation with user ID: \".concat(participantId));\n            console.log(\"Using API URL: \".concat(apiUrl, \"/conversations\"));\n            console.log(\"Authorization token: \".concat(token.substring(0, 10), \"...\"));\n            try {\n                // Create or get existing conversation\n                console.log(\"Request payload:\", JSON.stringify({\n                    participants: [\n                        participantId\n                    ]\n                }));\n                const response = await fetch(\"\".concat(apiUrl, \"/conversations\"), {\n                    method: \"POST\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        participants: [\n                            participantId\n                        ]\n                    }),\n                    // Add these options to help with CORS issues\n                    mode: \"cors\",\n                    credentials: \"same-origin\"\n                });\n                console.log(\"Response status:\", response.status);\n                console.log(\"Response headers:\", [\n                    ...response.headers.entries()\n                ]);\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"Conversation created/retrieved:\", data);\n                // Handle different response formats\n                let conversationId = null;\n                // Check if the response has a direct conversation_id property\n                if (data.conversation_id) {\n                    conversationId = data.conversation_id;\n                } else if (data.body && typeof data.body === \"string\") {\n                    try {\n                        const bodyData = JSON.parse(data.body);\n                        if (bodyData.conversation_id) {\n                            conversationId = bodyData.conversation_id;\n                            console.log(\"Found conversation ID in body:\", conversationId);\n                        }\n                    } catch (e) {\n                        console.error(\"Error parsing body data:\", e);\n                    }\n                }\n                if (!conversationId) {\n                    console.error(\"No conversation ID found in any format\", data);\n                    throw new Error(\"Invalid response from server\");\n                }\n                // Navigate to the chat page\n                router.push(\"/messages/\".concat(conversationId));\n            } catch (innerError) {\n                console.error(\"Inner fetch error:\", innerError);\n                throw innerError;\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            // Provide more specific error messages based on the error\n            if (error instanceof TypeError && error.message.includes(\"Failed to fetch\")) {\n                alert(\"Network error: Please check your internet connection and try again.\");\n            } else if (error instanceof Error && error.message.includes(\"401\")) {\n                alert(\"Authentication error: Please log in again.\");\n            } else if (error instanceof Error && error.message.includes(\"403\")) {\n                alert(\"Permission denied: You don't have permission to message this user.\");\n            } else {\n                alert(\"Failed to start conversation. Please try again.\");\n            }\n        } finally{\n            setIsCreatingChat(false);\n        }\n    };\n    // Function to navigate to user profile\n    const navigateToUserProfile = ()=>{\n        if (userId) {\n            // Get the token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Navigate to the profile page with the userId\n            router.push(\"/profile/\".concat(userId));\n        }\n    };\n    // Function to handle like/unlike\n    const handleLikeToggle = async ()=>{\n        if (isLiking || !videoId) return;\n        try {\n            setIsLiking(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to like this video');\n                setIsLiking(false);\n                return;\n            }\n            // Optimistically update UI\n            const newLikedState = !isLiked;\n            setIsLiked(newLikedState);\n            // Make API call to Next.js API routes\n            const endpoint = isLiked ? '/api/unlike' : '/api/like';\n            console.log(\"Making API call to: \".concat(endpoint, \" for video: \").concat(videoId));\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: videoId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const responseData = await response.json();\n            console.log('API Success Response:', responseData);\n            console.log(\"\".concat(isLiked ? 'Unliked' : 'Liked', \" video: \").concat(videoId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setIsLiked(!isLiked);\n        } finally{\n            setIsLiking(false);\n        }\n    };\n    // Function to handle admire/unadmire\n    const handleAdmireToggle = async ()=>{\n        if (isAdmiringLoading || !userId) return;\n        try {\n            setIsAdmiringLoading(true);\n            // Get token from localStorage\n            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n            if (!token) {\n                console.warn('No authentication token found');\n                alert('Please log in to admire this user');\n                setIsAdmiringLoading(false);\n                return;\n            }\n            // Optimistically update UI state immediately for better user experience\n            const newAdmiringState = !isAdmiring;\n            setIsAdmiring(newAdmiringState);\n            // Make API call to follow/unfollow user\n            const endpoint = isAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n            console.log(\"Making request to \".concat(endpoint, \" with user ID: \").concat(userId));\n            try {\n                // Make the API call\n                const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(endpoint, {\n                    target_user_id: userId\n                }, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('API response:', response.data);\n                console.log(\"Successfully \".concat(isAdmiring ? 'unadmired' : 'admired', \" user\"));\n                // Update localStorage with the new state\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    let admiredUsers = JSON.parse(admiredUsersJson);\n                    if (newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                    console.log('Updated admired users in localStorage:', admiredUsers);\n                    // Force a refresh of the following list to ensure it's up to date\n                    const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        }\n                    });\n                    console.log('Updated following list:', followingResponse.data);\n                    // Double-check our state is correct\n                    if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                        const isActuallyFollowing = followingResponse.data.following.some((user)=>user.user_id === userId || user === userId);\n                        if (isActuallyFollowing !== newAdmiringState) {\n                            console.log('State mismatch detected, correcting...');\n                            setIsAdmiring(isActuallyFollowing);\n                            // Update localStorage again with the correct state\n                            admiredUsers = JSON.parse(localStorage.getItem('admiredUsers') || '{}');\n                            if (isActuallyFollowing) {\n                                admiredUsers[userId] = true;\n                            } else {\n                                delete admiredUsers[userId];\n                            }\n                            localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                        }\n                    }\n                } catch (storageError) {\n                    console.error('Error updating localStorage:', storageError);\n                }\n            } catch (apiError) {\n                console.error(\"Error \".concat(isAdmiring ? 'unadmiring' : 'admiring', \" user:\"), apiError);\n                if (apiError.response) {\n                    console.log('Error response data:', apiError.response.data);\n                    console.log('Error response status:', apiError.response.status);\n                    // If the error is that the user is already following/not following, the UI state is already correct\n                    if (apiError.response.status === 400 && apiError.response.data && (apiError.response.data.error === \"Already following this user\" || apiError.response.data.error === \"Not following this user\")) {\n                        console.log('Already in desired state, keeping UI updated');\n                        return;\n                    }\n                }\n                // If there was an error that wasn't just \"already in desired state\", revert the UI\n                setIsAdmiring(!newAdmiringState);\n                // Also update localStorage to match\n                try {\n                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (!newAdmiringState) {\n                        admiredUsers[userId] = true;\n                    } else {\n                        delete admiredUsers[userId];\n                    }\n                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                } catch (storageError) {\n                    console.error('Error updating localStorage after API error:', storageError);\n                }\n            }\n        } catch (error) {\n            console.error('Unexpected error in handleAdmireToggle:', error);\n            // Revert UI state on unexpected errors\n            setIsAdmiring(!isAdmiring);\n        } finally{\n            setIsAdmiringLoading(false);\n        }\n    };\n    // Initialize like status from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!videoId) return;\n            try {\n                // Check if this video is liked in localStorage\n                const likedGlimpsesData = localStorage.getItem('likedGlimpses');\n                const likedMoviesData = localStorage.getItem('likedMovies');\n                let isVideoLiked = false;\n                // Check glimpses\n                if (likedGlimpsesData) {\n                    const likedGlimpses = JSON.parse(likedGlimpsesData);\n                    if (Array.isArray(likedGlimpses) && likedGlimpses.includes(videoId)) {\n                        isVideoLiked = true;\n                    }\n                }\n                // Check movies\n                if (!isVideoLiked && likedMoviesData) {\n                    const likedMovies = JSON.parse(likedMoviesData);\n                    if (Array.isArray(likedMovies) && likedMovies.includes(videoId)) {\n                        isVideoLiked = true;\n                    }\n                }\n                setIsLiked(isVideoLiked);\n                console.log(\"\\uD83C\\uDFAC VideoInteractionBar: Video \".concat(videoId, \" like status:\"), isVideoLiked);\n            } catch (error) {\n                console.error('🎬 Error loading like status for VideoInteractionBar:', error);\n                setIsLiked(false);\n            }\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        videoId\n    ]);\n    // Check if user is already admiring on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            if (!userId) return;\n            // First check localStorage for cached admiring state\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers');\n                if (admiredUsersJson) {\n                    const admiredUsers = JSON.parse(admiredUsersJson);\n                    if (admiredUsers[userId]) {\n                        console.log('Found admiring status in localStorage:', true);\n                        setIsAdmiring(true);\n                    // Continue with API check to verify\n                    }\n                }\n            } catch (storageError) {\n                console.error('Error reading from localStorage:', storageError);\n            }\n            const checkAdmiringStatus = {\n                \"VideoInteractionBar.useEffect.checkAdmiringStatus\": async ()=>{\n                    console.log('Checking admiring status for user ID:', userId);\n                    // Get token from localStorage\n                    const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                    if (!token) {\n                        console.warn('No authentication token found');\n                        return;\n                    }\n                    // Check if this is the current user's content - no need to check admiring status\n                    try {\n                        const tokenParts = token.split('.');\n                        if (tokenParts.length === 3) {\n                            const payload = JSON.parse(atob(tokenParts[1]));\n                            if (payload.user_id === userId) {\n                                console.log('This is the current user\\'s content, skipping admiring status check');\n                                setIsAdmiring(false); // User can't admire themselves\n                                return;\n                            }\n                        }\n                    } catch (tokenError) {\n                        console.log('Could not decode token to check current user, proceeding with API calls');\n                    }\n                    try {\n                        // Direct API call to check following status - most reliable method\n                        const followingResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/following\", {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('Following response:', followingResponse.data);\n                        if (followingResponse.data && Array.isArray(followingResponse.data.following)) {\n                            // Check if userId is in the following array\n                            const isFollowing = followingResponse.data.following.some({\n                                \"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\": (user)=>user.user_id === userId || user === userId\n                            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus.isFollowing\"]);\n                            console.log(\"User \".concat(isFollowing ? 'is' : 'is not', \" in following list:\"), userId);\n                            setIsAdmiring(isFollowing);\n                            // Update localStorage for future reference\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (isFollowing) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                            return;\n                        }\n                    } catch (followingError) {\n                        console.log('Error fetching following list, trying alternative method');\n                    }\n                    // Fallback: Try to get user profile\n                    try {\n                        const profileResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-profile?user_id=\".concat(userId), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        console.log('User profile response:', profileResponse.data);\n                        if (profileResponse.data && profileResponse.data.is_following !== undefined) {\n                            console.log('Setting admiring status from profile:', profileResponse.data.is_following);\n                            setIsAdmiring(profileResponse.data.is_following);\n                            // Update localStorage\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                if (profileResponse.data.is_following) {\n                                    admiredUsers[userId] = true;\n                                } else {\n                                    delete admiredUsers[userId];\n                                }\n                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                            } catch (storageError) {\n                                console.error('Error updating localStorage:', storageError);\n                            }\n                        }\n                    } catch (profileError) {\n                        console.log('Error fetching user profile, trying another method');\n                        // Last resort: Try the user-follow-stats endpoint\n                        try {\n                            const statsResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user-follow-stats?target_user_id=\".concat(userId), {\n                                headers: {\n                                    Authorization: \"Bearer \".concat(token),\n                                    'Content-Type': 'application/json'\n                                }\n                            });\n                            console.log('User follow stats response:', statsResponse.data);\n                            if (statsResponse.data && statsResponse.data.is_following !== undefined) {\n                                setIsAdmiring(statsResponse.data.is_following);\n                                // Update localStorage\n                                try {\n                                    const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                    const admiredUsers = JSON.parse(admiredUsersJson);\n                                    if (statsResponse.data.is_following) {\n                                        admiredUsers[userId] = true;\n                                    } else {\n                                        delete admiredUsers[userId];\n                                    }\n                                    localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                } catch (storageError) {\n                                    console.error('Error updating localStorage:', storageError);\n                                }\n                            }\n                        } catch (statsError) {\n                            console.log('All API methods failed to check admiring status, using localStorage fallback');\n                            // Fallback to localStorage if all API calls fail\n                            try {\n                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                const isAdmiringFromStorage = admiredUsers[userId] === true;\n                                setIsAdmiring(isAdmiringFromStorage);\n                                console.log(\"Using localStorage fallback: \".concat(isAdmiringFromStorage ? 'admiring' : 'not admiring'));\n                            } catch (storageError) {\n                                console.log('localStorage fallback also failed, defaulting to not admiring');\n                                setIsAdmiring(false);\n                            }\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.checkAdmiringStatus\"];\n            // Call the API check function\n            checkAdmiringStatus();\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        userId\n    ]);\n    // Add click outside handler to close menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoInteractionBar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"VideoInteractionBar.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.menu-container')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"VideoInteractionBar.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"VideoInteractionBar.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"VideoInteractionBar.useEffect\"];\n        }\n    }[\"VideoInteractionBar.useEffect\"], [\n        showMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-white p-2 sm:p-3 md:p-4 rounded-b-xl border border-gray-200 border-t-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-3 md:mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 sm:space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLikeToggle,\n                                    disabled: isLiking,\n                                    className: \"flex items-center justify-center rounded-full p-1.5 sm:p-2 transition-colors \".concat(isLiked ? 'bg-red-100 hover:bg-red-200' : 'bg-gray-100 hover:bg-gray-200'),\n                                    title: isLiked ? 'Unlike' : 'Like',\n                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdFavorite, {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        color: \"#B31B1E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdFavorite_MdFavoriteBorder_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdFavoriteBorder, {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        color: \"#666\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    onClick: handleMessageClick,\n                                    disabled: isCreatingChat,\n                                    title: \"Send a message\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 623,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: formatViewCount(viewCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"views\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative menu-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMenu(!showMenu),\n                                        className: \"p-1.5 rounded-full hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 18,\n                                            className: \"text-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: \"Report content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 622,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"cursor-pointer\",\n                                        onClick: navigateToUserProfile,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            username: username || \"Anonymous\",\n                                            size: \"md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-black cursor-pointer hover:underline\",\n                                                onClick: navigateToUserProfile,\n                                                children: username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Uploaded \",\n                                                    uploadDate || 'recently'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMessageClick,\n                                        disabled: isCreatingChat,\n                                        className: \"flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-full text-sm transition-colors\",\n                                        title: \"Send a message\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isCreatingChat ? \"Opening...\" : \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAdmireToggle,\n                                        disabled: isAdmiringLoading,\n                                        className: \"flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors bg-[#B31B1E] text-white hover:bg-red-700\",\n                                        title: isAdmiring ? \"Admiring\" : \"Admire this user\",\n                                        children: isAdmiringLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse\",\n                                            children: \"...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                isAdmiring ? 'Admiring' : 'Admire',\n                                                !isAdmiring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_MoreVertical_Plus_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 35\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, undefined),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black mt-2 text-xs sm:text-sm line-clamp-3 sm:line-clamp-none\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n                lineNumber: 679,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\VideoInteractionBar.tsx\",\n        lineNumber: 621,\n        columnNumber: 5\n    }, undefined);\n};\n_s(VideoInteractionBar, \"Ub7IOFbhXSoy7hmqI9MF3vyUtUI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = VideoInteractionBar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VideoInteractionBar);\nvar _c;\n$RefreshReg$(_c, \"VideoInteractionBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvVmlkZW9JbnRlcmFjdGlvbkJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFHbUQ7QUFDNkI7QUFDbEI7QUFDcEM7QUFDMEI7QUFDUjtBQVc1QyxNQUFNWSxzQkFBMEQ7UUFBQyxFQUMvREMsUUFBUSxFQUNSQyxVQUFVLEVBQ1ZDLFNBQVMsRUFDVEMsV0FBVyxFQUNYQyxNQUFNLEVBQ05DLE9BQU8sRUFDUjs7SUFDQyxNQUFNQyxTQUFTUiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDUyxnQkFBZ0JDLGtCQUFrQixHQUFHcEIsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDcUIsWUFBWUMsY0FBYyxHQUFHdEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDdUIsbUJBQW1CQyxxQkFBcUIsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ3lCLFNBQVNDLFdBQVcsR0FBRzFCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzJCLFVBQVVDLFlBQVksR0FBRzVCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzZCLFVBQVVDLFlBQVksR0FBRzlCLCtDQUFRQSxDQUFDO0lBRXpDLG9CQUFvQjtJQUNwQixNQUFNK0Isa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUlBLFNBQVMsU0FBUztZQUNwQixPQUFPLEdBQWdDLE9BQTdCLENBQUNBLFFBQVEsT0FBTSxFQUFHQyxPQUFPLENBQUMsSUFBRztRQUN6QyxPQUFPLElBQUlELFNBQVMsTUFBTTtZQUN4QixPQUFPLEdBQTZCLE9BQTFCLENBQUNBLFFBQVEsSUFBRyxFQUFHQyxPQUFPLENBQUMsSUFBRztRQUN0QztRQUNBLE9BQU9ELE1BQU1FLFFBQVE7SUFDdkI7SUFFQSw4QkFBOEI7SUFDOUIsTUFBTUMscUJBQXFCO1FBQ3pCLElBQUloQixnQkFBZ0I7UUFFcEIsSUFBSTtZQUNGQyxrQkFBa0I7WUFFbEIsa0VBQWtFO1lBQ2xFLE1BQU1nQixRQUNKQyxhQUFhQyxPQUFPLENBQUMsWUFBWUMsZUFBZUQsT0FBTyxDQUFDO1lBRTFELElBQUksQ0FBQ0YsT0FBTztnQkFDVkksUUFBUUMsS0FBSyxDQUFDO2dCQUNkQyxNQUFNO2dCQUNOdEIsa0JBQWtCO2dCQUNsQjtZQUNGO1lBRUEsK0NBQStDO1lBQy9DLE1BQU11QixTQUFTQyxrRUFBK0I7WUFFOUMsbUVBQW1FO1lBQ25FLG1EQUFtRDtZQUNuRCxNQUFNRyxnQkFDSixDQUFDL0IsVUFBVUEsV0FBVyxZQUNsQkosU0FBU29DLFdBQVcsR0FBR0MsT0FBTyxDQUFDLFFBQVEsT0FBTyxRQUM5Q2pDO1lBRU53QixRQUFRVSxHQUFHLENBQUMsdUNBQXFELE9BQWRIO1lBQ25EUCxRQUFRVSxHQUFHLENBQUMsa0JBQXlCLE9BQVBQLFFBQU87WUFDckNILFFBQVFVLEdBQUcsQ0FBQyx3QkFBK0MsT0FBdkJkLE1BQU1lLFNBQVMsQ0FBQyxHQUFHLEtBQUk7WUFFM0QsSUFBSTtnQkFDRixzQ0FBc0M7Z0JBQ3RDWCxRQUFRVSxHQUFHLENBQ1Qsb0JBQ0FFLEtBQUtDLFNBQVMsQ0FBQztvQkFBRUMsY0FBYzt3QkFBQ1A7cUJBQWM7Z0JBQUM7Z0JBR2pELE1BQU1RLFdBQVcsTUFBTUMsTUFBTSxHQUFVLE9BQVBiLFFBQU8sbUJBQWlCO29CQUN0RGMsUUFBUTtvQkFDUkMsU0FBUzt3QkFDUEMsZUFBZSxVQUFnQixPQUFOdkI7d0JBQ3pCLGdCQUFnQjtvQkFDbEI7b0JBQ0F3QixNQUFNUixLQUFLQyxTQUFTLENBQUM7d0JBQ25CQyxjQUFjOzRCQUFDUDt5QkFBYztvQkFDL0I7b0JBQ0EsNkNBQTZDO29CQUM3Q2MsTUFBTTtvQkFDTkMsYUFBYTtnQkFDZjtnQkFFQXRCLFFBQVFVLEdBQUcsQ0FBQyxvQkFBb0JLLFNBQVNRLE1BQU07Z0JBQy9DdkIsUUFBUVUsR0FBRyxDQUFDLHFCQUFxQjt1QkFBSUssU0FBU0csT0FBTyxDQUFDTSxPQUFPO2lCQUFHO2dCQUVoRSxJQUFJLENBQUNULFNBQVNVLEVBQUUsRUFBRTtvQkFDaEIsTUFBTSxJQUFJQyxNQUFNLGtDQUFrRCxPQUFoQlgsU0FBU1EsTUFBTTtnQkFDbkU7Z0JBRUEsTUFBTUksT0FBTyxNQUFNWixTQUFTYSxJQUFJO2dCQUNoQzVCLFFBQVFVLEdBQUcsQ0FBQyxtQ0FBbUNpQjtnQkFFL0Msb0NBQW9DO2dCQUNwQyxJQUFJRSxpQkFBaUI7Z0JBRXJCLDhEQUE4RDtnQkFDOUQsSUFBSUYsS0FBS0csZUFBZSxFQUFFO29CQUN4QkQsaUJBQWlCRixLQUFLRyxlQUFlO2dCQUN2QyxPQUVLLElBQUlILEtBQUtQLElBQUksSUFBSSxPQUFPTyxLQUFLUCxJQUFJLEtBQUssVUFBVTtvQkFDbkQsSUFBSTt3QkFDRixNQUFNVyxXQUFXbkIsS0FBS29CLEtBQUssQ0FBQ0wsS0FBS1AsSUFBSTt3QkFDckMsSUFBSVcsU0FBU0QsZUFBZSxFQUFFOzRCQUM1QkQsaUJBQWlCRSxTQUFTRCxlQUFlOzRCQUN6QzlCLFFBQVFVLEdBQUcsQ0FBQyxrQ0FBa0NtQjt3QkFDaEQ7b0JBQ0YsRUFBRSxPQUFPSSxHQUFHO3dCQUNWakMsUUFBUUMsS0FBSyxDQUFDLDRCQUE0QmdDO29CQUM1QztnQkFDRjtnQkFFQSxJQUFJLENBQUNKLGdCQUFnQjtvQkFDbkI3QixRQUFRQyxLQUFLLENBQUMsMENBQTBDMEI7b0JBQ3hELE1BQU0sSUFBSUQsTUFBTTtnQkFDbEI7Z0JBRUEsNEJBQTRCO2dCQUM1QmhELE9BQU93RCxJQUFJLENBQUMsYUFBNEIsT0FBZkw7WUFDM0IsRUFBRSxPQUFPTSxZQUFZO2dCQUNuQm5DLFFBQVFDLEtBQUssQ0FBQyxzQkFBc0JrQztnQkFDcEMsTUFBTUE7WUFDUjtRQUNGLEVBQUUsT0FBT2xDLE9BQU87WUFDZEQsUUFBUUMsS0FBSyxDQUFDLGdDQUFnQ0E7WUFFOUMsMERBQTBEO1lBQzFELElBQ0VBLGlCQUFpQm1DLGFBQ2pCbkMsTUFBTW9DLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLG9CQUN2QjtnQkFDQXBDLE1BQ0U7WUFFSixPQUFPLElBQUlELGlCQUFpQnlCLFNBQVN6QixNQUFNb0MsT0FBTyxDQUFDQyxRQUFRLENBQUMsUUFBUTtnQkFDbEVwQyxNQUFNO1lBQ1IsT0FBTyxJQUFJRCxpQkFBaUJ5QixTQUFTekIsTUFBTW9DLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFFBQVE7Z0JBQ2xFcEMsTUFDRTtZQUVKLE9BQU87Z0JBQ0xBLE1BQU07WUFDUjtRQUNGLFNBQVU7WUFDUnRCLGtCQUFrQjtRQUNwQjtJQUNGO0lBRUEsdUNBQXVDO0lBQ3ZDLE1BQU0yRCx3QkFBd0I7UUFDNUIsSUFBSS9ELFFBQVE7WUFDVixrQ0FBa0M7WUFDbEMsTUFBTW9CLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQyxZQUNqQ0QsYUFBYUMsT0FBTyxDQUFDLGdCQUNyQkQsYUFBYUMsT0FBTyxDQUFDO1lBRXZCLElBQUksQ0FBQ0YsT0FBTztnQkFDVkksUUFBUXdDLElBQUksQ0FBQztnQkFDYjtZQUNGO1lBRUEsK0NBQStDO1lBQy9DOUQsT0FBT3dELElBQUksQ0FBQyxZQUFtQixPQUFQMUQ7UUFDMUI7SUFDRjtJQUVBLGlDQUFpQztJQUNqQyxNQUFNaUUsbUJBQW1CO1FBQ3ZCLElBQUl0RCxZQUFZLENBQUNWLFNBQVM7UUFFMUIsSUFBSTtZQUNGVyxZQUFZO1lBRVosOEJBQThCO1lBQzlCLE1BQU1RLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQyxZQUNqQ0QsYUFBYUMsT0FBTyxDQUFDLGdCQUNyQkQsYUFBYUMsT0FBTyxDQUFDO1lBRXZCLElBQUksQ0FBQ0YsT0FBTztnQkFDVkksUUFBUXdDLElBQUksQ0FBQztnQkFDYnRDLE1BQU07Z0JBQ05kLFlBQVk7Z0JBQ1o7WUFDRjtZQUVBLDJCQUEyQjtZQUMzQixNQUFNc0QsZ0JBQWdCLENBQUN6RDtZQUN2QkMsV0FBV3dEO1lBRVgsc0NBQXNDO1lBQ3RDLE1BQU1DLFdBQVcxRCxVQUFVLGdCQUFnQjtZQUMzQ2UsUUFBUVUsR0FBRyxDQUFDLHVCQUE4Q2pDLE9BQXZCa0UsVUFBUyxnQkFBc0IsT0FBUmxFO1lBRTFELE1BQU1zQyxXQUFXLE1BQU1DLE1BQU0yQixVQUFVO2dCQUNyQzFCLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU50QjtvQkFDM0IsZ0JBQWdCO2dCQUNsQjtnQkFDQXdCLE1BQU1SLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkIrQixZQUFZbkU7b0JBQ1pvRSxjQUFjO2dCQUNoQjtZQUNGO1lBRUEsSUFBSSxDQUFDOUIsU0FBU1UsRUFBRSxFQUFFO2dCQUNoQixNQUFNcUIsWUFBWSxNQUFNL0IsU0FBU2dDLElBQUk7Z0JBQ3JDL0MsUUFBUUMsS0FBSyxDQUFDLHVCQUF1QjZDO2dCQUNyQyxNQUFNLElBQUlwQixNQUFNLHVCQUF1QyxPQUFoQlgsU0FBU1EsTUFBTTtZQUN4RDtZQUVBLE1BQU15QixlQUFlLE1BQU1qQyxTQUFTYSxJQUFJO1lBQ3hDNUIsUUFBUVUsR0FBRyxDQUFDLHlCQUF5QnNDO1lBRXJDaEQsUUFBUVUsR0FBRyxDQUFDLEdBQTJDakMsT0FBeENRLFVBQVUsWUFBWSxTQUFRLFlBQWtCLE9BQVJSO1FBQ3pELEVBQUUsT0FBT3dCLE9BQU87WUFDZEQsUUFBUUMsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdEMsb0NBQW9DO1lBQ3BDZixXQUFXLENBQUNEO1FBQ2QsU0FBVTtZQUNSRyxZQUFZO1FBQ2Q7SUFDRjtJQUVBLHFDQUFxQztJQUNyQyxNQUFNNkQscUJBQXFCO1FBQ3pCLElBQUlsRSxxQkFBcUIsQ0FBQ1AsUUFBUTtRQUVsQyxJQUFJO1lBQ0ZRLHFCQUFxQjtZQUVyQiw4QkFBOEI7WUFDOUIsTUFBTVksUUFBUUMsYUFBYUMsT0FBTyxDQUFDLFlBQ2pDRCxhQUFhQyxPQUFPLENBQUMsZ0JBQ3JCRCxhQUFhQyxPQUFPLENBQUM7WUFFdkIsSUFBSSxDQUFDRixPQUFPO2dCQUNWSSxRQUFRd0MsSUFBSSxDQUFDO2dCQUNidEMsTUFBTTtnQkFDTmxCLHFCQUFxQjtnQkFDckI7WUFDRjtZQUVBLHdFQUF3RTtZQUN4RSxNQUFNa0UsbUJBQW1CLENBQUNyRTtZQUMxQkMsY0FBY29FO1lBRWQsd0NBQXdDO1lBQ3hDLE1BQU1QLFdBQVc5RCxhQUNiLDhFQUNBO1lBRUptQixRQUFRVSxHQUFHLENBQUMscUJBQStDbEMsT0FBMUJtRSxVQUFTLG1CQUF3QixPQUFQbkU7WUFFM0QsSUFBSTtnQkFDRixvQkFBb0I7Z0JBQ3BCLE1BQU11QyxXQUFXLE1BQU0vQyw2Q0FBS0EsQ0FBQ21GLElBQUksQ0FDL0JSLFVBQ0E7b0JBQUVTLGdCQUFnQjVFO2dCQUFPLEdBQ3pCO29CQUNFMEMsU0FBUzt3QkFDUEMsZUFBZSxVQUFnQixPQUFOdkI7d0JBQ3pCLGdCQUFnQjtvQkFDbEI7Z0JBQ0Y7Z0JBR0ZJLFFBQVFVLEdBQUcsQ0FBQyxpQkFBaUJLLFNBQVNZLElBQUk7Z0JBQzFDM0IsUUFBUVUsR0FBRyxDQUFDLGdCQUFxRCxPQUFyQzdCLGFBQWEsY0FBYyxXQUFVO2dCQUVqRSx5Q0FBeUM7Z0JBQ3pDLElBQUk7b0JBQ0YsTUFBTXdFLG1CQUFtQnhELGFBQWFDLE9BQU8sQ0FBQyxtQkFBbUI7b0JBQ2pFLElBQUl3RCxlQUFlMUMsS0FBS29CLEtBQUssQ0FBQ3FCO29CQUU5QixJQUFJSCxrQkFBa0I7d0JBQ3BCSSxZQUFZLENBQUM5RSxPQUFPLEdBQUc7b0JBQ3pCLE9BQU87d0JBQ0wsT0FBTzhFLFlBQVksQ0FBQzlFLE9BQU87b0JBQzdCO29CQUVBcUIsYUFBYTBELE9BQU8sQ0FBQyxnQkFBZ0IzQyxLQUFLQyxTQUFTLENBQUN5QztvQkFDcER0RCxRQUFRVSxHQUFHLENBQUMsMENBQTBDNEM7b0JBRXRELGtFQUFrRTtvQkFDbEUsTUFBTUUsb0JBQW9CLE1BQU14Riw2Q0FBS0EsQ0FBQ3lGLEdBQUcsQ0FDdEMsOEVBQ0Q7d0JBQ0V2QyxTQUFTOzRCQUNQQyxlQUFlLFVBQWdCLE9BQU52Qjs0QkFDekIsZ0JBQWdCO3dCQUNsQjtvQkFDRjtvQkFHRkksUUFBUVUsR0FBRyxDQUFDLDJCQUEyQjhDLGtCQUFrQjdCLElBQUk7b0JBRTdELG9DQUFvQztvQkFDcEMsSUFBSTZCLGtCQUFrQjdCLElBQUksSUFBSStCLE1BQU1DLE9BQU8sQ0FBQ0gsa0JBQWtCN0IsSUFBSSxDQUFDaUMsU0FBUyxHQUFHO3dCQUM3RSxNQUFNQyxzQkFBc0JMLGtCQUFrQjdCLElBQUksQ0FBQ2lDLFNBQVMsQ0FBQ0UsSUFBSSxDQUMvRCxDQUFDQyxPQUFjQSxLQUFLQyxPQUFPLEtBQUt4RixVQUFVdUYsU0FBU3ZGO3dCQUdyRCxJQUFJcUYsd0JBQXdCWCxrQkFBa0I7NEJBQzVDbEQsUUFBUVUsR0FBRyxDQUFDOzRCQUNaNUIsY0FBYytFOzRCQUVkLG1EQUFtRDs0QkFDbkRQLGVBQWUxQyxLQUFLb0IsS0FBSyxDQUFDbkMsYUFBYUMsT0FBTyxDQUFDLG1CQUFtQjs0QkFFbEUsSUFBSStELHFCQUFxQjtnQ0FDdkJQLFlBQVksQ0FBQzlFLE9BQU8sR0FBRzs0QkFDekIsT0FBTztnQ0FDTCxPQUFPOEUsWUFBWSxDQUFDOUUsT0FBTzs0QkFDN0I7NEJBRUFxQixhQUFhMEQsT0FBTyxDQUFDLGdCQUFnQjNDLEtBQUtDLFNBQVMsQ0FBQ3lDO3dCQUN0RDtvQkFDRjtnQkFDRixFQUFFLE9BQU9XLGNBQWM7b0JBQ3JCakUsUUFBUUMsS0FBSyxDQUFDLGdDQUFnQ2dFO2dCQUNoRDtZQUNGLEVBQUUsT0FBT0MsVUFBZTtnQkFDdEJsRSxRQUFRQyxLQUFLLENBQUMsU0FBZ0QsT0FBdkNwQixhQUFhLGVBQWUsWUFBVyxXQUFTcUY7Z0JBRXZFLElBQUlBLFNBQVNuRCxRQUFRLEVBQUU7b0JBQ3JCZixRQUFRVSxHQUFHLENBQUMsd0JBQXdCd0QsU0FBU25ELFFBQVEsQ0FBQ1ksSUFBSTtvQkFDMUQzQixRQUFRVSxHQUFHLENBQUMsMEJBQTBCd0QsU0FBU25ELFFBQVEsQ0FBQ1EsTUFBTTtvQkFFOUQsb0dBQW9HO29CQUNwRyxJQUFJMkMsU0FBU25ELFFBQVEsQ0FBQ1EsTUFBTSxLQUFLLE9BQy9CMkMsU0FBU25ELFFBQVEsQ0FBQ1ksSUFBSSxJQUNyQnVDLENBQUFBLFNBQVNuRCxRQUFRLENBQUNZLElBQUksQ0FBQzFCLEtBQUssS0FBSyxpQ0FDaENpRSxTQUFTbkQsUUFBUSxDQUFDWSxJQUFJLENBQUMxQixLQUFLLEtBQUsseUJBQXdCLEdBQUk7d0JBQy9ERCxRQUFRVSxHQUFHLENBQUM7d0JBQ1o7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsbUZBQW1GO2dCQUNuRjVCLGNBQWMsQ0FBQ29FO2dCQUVmLG9DQUFvQztnQkFDcEMsSUFBSTtvQkFDRixNQUFNRyxtQkFBbUJ4RCxhQUFhQyxPQUFPLENBQUMsbUJBQW1CO29CQUNqRSxNQUFNd0QsZUFBZTFDLEtBQUtvQixLQUFLLENBQUNxQjtvQkFFaEMsSUFBSSxDQUFDSCxrQkFBa0I7d0JBQ3JCSSxZQUFZLENBQUM5RSxPQUFPLEdBQUc7b0JBQ3pCLE9BQU87d0JBQ0wsT0FBTzhFLFlBQVksQ0FBQzlFLE9BQU87b0JBQzdCO29CQUVBcUIsYUFBYTBELE9BQU8sQ0FBQyxnQkFBZ0IzQyxLQUFLQyxTQUFTLENBQUN5QztnQkFDdEQsRUFBRSxPQUFPVyxjQUFjO29CQUNyQmpFLFFBQVFDLEtBQUssQ0FBQyxnREFBZ0RnRTtnQkFDaEU7WUFDRjtRQUNGLEVBQUUsT0FBT2hFLE9BQVk7WUFDbkJELFFBQVFDLEtBQUssQ0FBQywyQ0FBMkNBO1lBQ3pELHVDQUF1QztZQUN2Q25CLGNBQWMsQ0FBQ0Q7UUFDakIsU0FBVTtZQUNSRyxxQkFBcUI7UUFDdkI7SUFDRjtJQUVBLDJDQUEyQztJQUMzQ3ZCLGdEQUFTQTt5Q0FBQztZQUNSLElBQUksQ0FBQ2dCLFNBQVM7WUFFZCxJQUFJO2dCQUNGLCtDQUErQztnQkFDL0MsTUFBTTBGLG9CQUFvQnRFLGFBQWFDLE9BQU8sQ0FBQztnQkFDL0MsTUFBTXNFLGtCQUFrQnZFLGFBQWFDLE9BQU8sQ0FBQztnQkFFN0MsSUFBSXVFLGVBQWU7Z0JBRW5CLGlCQUFpQjtnQkFDakIsSUFBSUYsbUJBQW1CO29CQUNyQixNQUFNRyxnQkFBZ0IxRCxLQUFLb0IsS0FBSyxDQUFDbUM7b0JBQ2pDLElBQUlULE1BQU1DLE9BQU8sQ0FBQ1csa0JBQWtCQSxjQUFjaEMsUUFBUSxDQUFDN0QsVUFBVTt3QkFDbkU0RixlQUFlO29CQUNqQjtnQkFDRjtnQkFFQSxlQUFlO2dCQUNmLElBQUksQ0FBQ0EsZ0JBQWdCRCxpQkFBaUI7b0JBQ3BDLE1BQU1HLGNBQWMzRCxLQUFLb0IsS0FBSyxDQUFDb0M7b0JBQy9CLElBQUlWLE1BQU1DLE9BQU8sQ0FBQ1ksZ0JBQWdCQSxZQUFZakMsUUFBUSxDQUFDN0QsVUFBVTt3QkFDL0Q0RixlQUFlO29CQUNqQjtnQkFDRjtnQkFFQW5GLFdBQVdtRjtnQkFDWHJFLFFBQVFVLEdBQUcsQ0FBQywyQ0FBeUMsT0FBUmpDLFNBQVEsa0JBQWdCNEY7WUFDdkUsRUFBRSxPQUFPcEUsT0FBTztnQkFDZEQsUUFBUUMsS0FBSyxDQUFDLHlEQUF5REE7Z0JBQ3ZFZixXQUFXO1lBQ2I7UUFDRjt3Q0FBRztRQUFDVDtLQUFRO0lBRVosdURBQXVEO0lBQ3ZEaEIsZ0RBQVNBO3lDQUFDO1lBQ1IsSUFBSSxDQUFDZSxRQUFRO1lBRWIscURBQXFEO1lBQ3JELElBQUk7Z0JBQ0YsTUFBTTZFLG1CQUFtQnhELGFBQWFDLE9BQU8sQ0FBQztnQkFDOUMsSUFBSXVELGtCQUFrQjtvQkFDcEIsTUFBTUMsZUFBZTFDLEtBQUtvQixLQUFLLENBQUNxQjtvQkFDaEMsSUFBSUMsWUFBWSxDQUFDOUUsT0FBTyxFQUFFO3dCQUN4QndCLFFBQVFVLEdBQUcsQ0FBQywwQ0FBMEM7d0JBQ3RENUIsY0FBYztvQkFDZCxvQ0FBb0M7b0JBQ3RDO2dCQUNGO1lBQ0YsRUFBRSxPQUFPbUYsY0FBYztnQkFDckJqRSxRQUFRQyxLQUFLLENBQUMsb0NBQW9DZ0U7WUFDcEQ7WUFFQSxNQUFNTztxRUFBc0I7b0JBQzFCeEUsUUFBUVUsR0FBRyxDQUFDLHlDQUF5Q2xDO29CQUVyRCw4QkFBOEI7b0JBQzlCLE1BQU1vQixRQUFRQyxhQUFhQyxPQUFPLENBQUMsWUFDakNELGFBQWFDLE9BQU8sQ0FBQyxnQkFDckJELGFBQWFDLE9BQU8sQ0FBQztvQkFFdkIsSUFBSSxDQUFDRixPQUFPO3dCQUNWSSxRQUFRd0MsSUFBSSxDQUFDO3dCQUNiO29CQUNGO29CQUVBLGlGQUFpRjtvQkFDakYsSUFBSTt3QkFDRixNQUFNaUMsYUFBYTdFLE1BQU04RSxLQUFLLENBQUM7d0JBQy9CLElBQUlELFdBQVdFLE1BQU0sS0FBSyxHQUFHOzRCQUMzQixNQUFNQyxVQUFVaEUsS0FBS29CLEtBQUssQ0FBQzZDLEtBQUtKLFVBQVUsQ0FBQyxFQUFFOzRCQUM3QyxJQUFJRyxRQUFRWixPQUFPLEtBQUt4RixRQUFRO2dDQUM5QndCLFFBQVFVLEdBQUcsQ0FBQztnQ0FDWjVCLGNBQWMsUUFBUSwrQkFBK0I7Z0NBQ3JEOzRCQUNGO3dCQUNGO29CQUNGLEVBQUUsT0FBT2dHLFlBQVk7d0JBQ25COUUsUUFBUVUsR0FBRyxDQUFDO29CQUNkO29CQUVBLElBQUk7d0JBQ0YsbUVBQW1FO3dCQUNuRSxNQUFNOEMsb0JBQW9CLE1BQU14Riw2Q0FBS0EsQ0FBQ3lGLEdBQUcsQ0FDdEMsOEVBQ0Q7NEJBQ0V2QyxTQUFTO2dDQUNQQyxlQUFlLFVBQWdCLE9BQU52QjtnQ0FDekIsZ0JBQWdCOzRCQUNsQjt3QkFDRjt3QkFHRkksUUFBUVUsR0FBRyxDQUFDLHVCQUF1QjhDLGtCQUFrQjdCLElBQUk7d0JBRXpELElBQUk2QixrQkFBa0I3QixJQUFJLElBQUkrQixNQUFNQyxPQUFPLENBQUNILGtCQUFrQjdCLElBQUksQ0FBQ2lDLFNBQVMsR0FBRzs0QkFDN0UsNENBQTRDOzRCQUM1QyxNQUFNbUIsY0FBY3ZCLGtCQUFrQjdCLElBQUksQ0FBQ2lDLFNBQVMsQ0FBQ0UsSUFBSTtpR0FDdkQsQ0FBQ0MsT0FBY0EsS0FBS0MsT0FBTyxLQUFLeEYsVUFBVXVGLFNBQVN2Rjs7NEJBR3JEd0IsUUFBUVUsR0FBRyxDQUFDLFFBQXNDLE9BQTlCcUUsY0FBYyxPQUFPLFVBQVMsd0JBQXNCdkc7NEJBQ3hFTSxjQUFjaUc7NEJBRWQsMkNBQTJDOzRCQUMzQyxJQUFJO2dDQUNGLE1BQU0xQixtQkFBbUJ4RCxhQUFhQyxPQUFPLENBQUMsbUJBQW1CO2dDQUNqRSxNQUFNd0QsZUFBZTFDLEtBQUtvQixLQUFLLENBQUNxQjtnQ0FFaEMsSUFBSTBCLGFBQWE7b0NBQ2Z6QixZQUFZLENBQUM5RSxPQUFPLEdBQUc7Z0NBQ3pCLE9BQU87b0NBQ0wsT0FBTzhFLFlBQVksQ0FBQzlFLE9BQU87Z0NBQzdCO2dDQUVBcUIsYUFBYTBELE9BQU8sQ0FBQyxnQkFBZ0IzQyxLQUFLQyxTQUFTLENBQUN5Qzs0QkFDdEQsRUFBRSxPQUFPVyxjQUFjO2dDQUNyQmpFLFFBQVFDLEtBQUssQ0FBQyxnQ0FBZ0NnRTs0QkFDaEQ7NEJBRUE7d0JBQ0Y7b0JBQ0YsRUFBRSxPQUFPZSxnQkFBZ0I7d0JBQ3ZCaEYsUUFBUVUsR0FBRyxDQUFDO29CQUNkO29CQUVBLG9DQUFvQztvQkFDcEMsSUFBSTt3QkFDRixNQUFNdUUsa0JBQWtCLE1BQU1qSCw2Q0FBS0EsQ0FBQ3lGLEdBQUcsQ0FDckMseUZBQWdHLE9BQVBqRixTQUN6Rjs0QkFDRTBDLFNBQVM7Z0NBQ1BDLGVBQWUsVUFBZ0IsT0FBTnZCO2dDQUN6QixnQkFBZ0I7NEJBQ2xCO3dCQUNGO3dCQUdGSSxRQUFRVSxHQUFHLENBQUMsMEJBQTBCdUUsZ0JBQWdCdEQsSUFBSTt3QkFFMUQsSUFBSXNELGdCQUFnQnRELElBQUksSUFBSXNELGdCQUFnQnRELElBQUksQ0FBQ3VELFlBQVksS0FBS0MsV0FBVzs0QkFDM0VuRixRQUFRVSxHQUFHLENBQUMseUNBQXlDdUUsZ0JBQWdCdEQsSUFBSSxDQUFDdUQsWUFBWTs0QkFDdEZwRyxjQUFjbUcsZ0JBQWdCdEQsSUFBSSxDQUFDdUQsWUFBWTs0QkFFL0Msc0JBQXNCOzRCQUN0QixJQUFJO2dDQUNGLE1BQU03QixtQkFBbUJ4RCxhQUFhQyxPQUFPLENBQUMsbUJBQW1CO2dDQUNqRSxNQUFNd0QsZUFBZTFDLEtBQUtvQixLQUFLLENBQUNxQjtnQ0FFaEMsSUFBSTRCLGdCQUFnQnRELElBQUksQ0FBQ3VELFlBQVksRUFBRTtvQ0FDckM1QixZQUFZLENBQUM5RSxPQUFPLEdBQUc7Z0NBQ3pCLE9BQU87b0NBQ0wsT0FBTzhFLFlBQVksQ0FBQzlFLE9BQU87Z0NBQzdCO2dDQUVBcUIsYUFBYTBELE9BQU8sQ0FBQyxnQkFBZ0IzQyxLQUFLQyxTQUFTLENBQUN5Qzs0QkFDdEQsRUFBRSxPQUFPVyxjQUFjO2dDQUNyQmpFLFFBQVFDLEtBQUssQ0FBQyxnQ0FBZ0NnRTs0QkFDaEQ7d0JBQ0Y7b0JBQ0YsRUFBRSxPQUFPbUIsY0FBYzt3QkFDckJwRixRQUFRVSxHQUFHLENBQUM7d0JBRVosa0RBQWtEO3dCQUNsRCxJQUFJOzRCQUNGLE1BQU0yRSxnQkFBZ0IsTUFBTXJILDZDQUFLQSxDQUFDeUYsR0FBRyxDQUNuQyxxR0FBNEcsT0FBUGpGLFNBQ3JHO2dDQUNFMEMsU0FBUztvQ0FDUEMsZUFBZSxVQUFnQixPQUFOdkI7b0NBQ3pCLGdCQUFnQjtnQ0FDbEI7NEJBQ0Y7NEJBR0ZJLFFBQVFVLEdBQUcsQ0FBQywrQkFBK0IyRSxjQUFjMUQsSUFBSTs0QkFFN0QsSUFBSTBELGNBQWMxRCxJQUFJLElBQUkwRCxjQUFjMUQsSUFBSSxDQUFDdUQsWUFBWSxLQUFLQyxXQUFXO2dDQUN2RXJHLGNBQWN1RyxjQUFjMUQsSUFBSSxDQUFDdUQsWUFBWTtnQ0FFN0Msc0JBQXNCO2dDQUN0QixJQUFJO29DQUNGLE1BQU03QixtQkFBbUJ4RCxhQUFhQyxPQUFPLENBQUMsbUJBQW1CO29DQUNqRSxNQUFNd0QsZUFBZTFDLEtBQUtvQixLQUFLLENBQUNxQjtvQ0FFaEMsSUFBSWdDLGNBQWMxRCxJQUFJLENBQUN1RCxZQUFZLEVBQUU7d0NBQ25DNUIsWUFBWSxDQUFDOUUsT0FBTyxHQUFHO29DQUN6QixPQUFPO3dDQUNMLE9BQU84RSxZQUFZLENBQUM5RSxPQUFPO29DQUM3QjtvQ0FFQXFCLGFBQWEwRCxPQUFPLENBQUMsZ0JBQWdCM0MsS0FBS0MsU0FBUyxDQUFDeUM7Z0NBQ3RELEVBQUUsT0FBT1csY0FBYztvQ0FDckJqRSxRQUFRQyxLQUFLLENBQUMsZ0NBQWdDZ0U7Z0NBQ2hEOzRCQUNGO3dCQUNGLEVBQUUsT0FBT3FCLFlBQVk7NEJBQ25CdEYsUUFBUVUsR0FBRyxDQUFDOzRCQUVaLGlEQUFpRDs0QkFDakQsSUFBSTtnQ0FDRixNQUFNMkMsbUJBQW1CeEQsYUFBYUMsT0FBTyxDQUFDLG1CQUFtQjtnQ0FDakUsTUFBTXdELGVBQWUxQyxLQUFLb0IsS0FBSyxDQUFDcUI7Z0NBQ2hDLE1BQU1rQyx3QkFBd0JqQyxZQUFZLENBQUM5RSxPQUFPLEtBQUs7Z0NBQ3ZETSxjQUFjeUc7Z0NBQ2R2RixRQUFRVSxHQUFHLENBQUMsZ0NBQW9GLE9BQXBENkUsd0JBQXdCLGFBQWE7NEJBQ25GLEVBQUUsT0FBT3RCLGNBQWM7Z0NBQ3JCakUsUUFBUVUsR0FBRyxDQUFDO2dDQUNaNUIsY0FBYzs0QkFDaEI7d0JBQ0Y7b0JBQ0Y7Z0JBQ0Y7O1lBRUEsOEJBQThCO1lBQzlCMEY7UUFDRjt3Q0FBRztRQUFDaEc7S0FBTztJQUVYLDBDQUEwQztJQUMxQ2YsZ0RBQVNBO3lDQUFDO1lBQ1IsTUFBTStIO29FQUFxQixDQUFDQztvQkFDMUIsSUFBSXBHLFVBQVU7d0JBQ1osTUFBTXFHLFNBQVNELE1BQU1DLE1BQU07d0JBQzNCLElBQUksQ0FBQ0EsT0FBT0MsT0FBTyxDQUFDLG9CQUFvQjs0QkFDdENyRyxZQUFZO3dCQUNkO29CQUNGO2dCQUNGOztZQUVBc0csU0FBU0MsZ0JBQWdCLENBQUMsYUFBYUw7WUFDdkM7aURBQU87b0JBQ0xJLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFOO2dCQUM1Qzs7UUFDRjt3Q0FBRztRQUFDbkc7S0FBUztJQUViLHFCQUNFLDhEQUFDMEc7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQ0NDLFNBQVN6RDtvQ0FDVDBELFVBQVVoSDtvQ0FDVjZHLFdBQVcsZ0ZBSVYsT0FIQy9HLFVBQ0ksZ0NBQ0E7b0NBRU5tSCxPQUFPbkgsVUFBVSxXQUFXOzhDQUUzQkEsd0JBQ0MsOERBQUNuQix5R0FBVUE7d0NBQUNrSSxXQUFVO3dDQUF3QkssT0FBTTs7Ozs7a0VBRXBELDhEQUFDdEksK0dBQWdCQTt3Q0FBQ2lJLFdBQVU7d0NBQXdCSyxPQUFNOzs7Ozs7Ozs7Ozs4Q0FHOUQsOERBQUNKO29DQUFPRCxXQUFVO29DQUNoQkUsU0FBU3ZHO29DQUNUd0csVUFBVXhIO29DQUNWeUgsT0FBTTs4Q0FFTiw0RUFBQzFJLGtIQUFhQTt3Q0FBQ3NJLFdBQVU7Ozs7Ozs7Ozs7OzhDQUUzQiw4REFBQ0M7b0NBQU9ELFdBQVU7OENBQ2hCLDRFQUFDckksa0hBQU1BO3dDQUFDcUksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJeEIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDTTt3Q0FBS04sV0FBVTtrREFBaUJ6RyxnQkFBZ0JqQjs7Ozs7O29DQUFtQjtvQ0FBSTs7Ozs7OzswQ0FHMUUsOERBQUN5SDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUNDQyxTQUFTLElBQU01RyxZQUFZLENBQUNEO3dDQUM1QjJHLFdBQVU7a0RBRVYsNEVBQUNwSSxrSEFBWUE7NENBQUMySSxNQUFNOzRDQUFJUCxXQUFVOzs7Ozs7Ozs7OztvQ0FHbkMzRywwQkFDQyw4REFBQzBHO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0M7Z0RBQU9ELFdBQVU7MERBQXFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVVuRyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7d0NBQWlCRSxTQUFTM0Q7a0RBQ3ZDLDRFQUFDdEUsaUVBQVVBOzRDQUFDRyxVQUFVQSxZQUFZOzRDQUFhbUksTUFBSzs7Ozs7Ozs7Ozs7a0RBRXRELDhEQUFDUjs7MERBQ0MsOERBQUNBO2dEQUNDQyxXQUFVO2dEQUNWRSxTQUFTM0Q7MERBRVJuRTs7Ozs7OzBEQUVILDhEQUFDMkg7Z0RBQUlDLFdBQVU7O29EQUF3QjtvREFBVTNILGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSW5FLDhEQUFDMEg7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FDQ0MsU0FBU3ZHO3dDQUNUd0csVUFBVXhIO3dDQUNWcUgsV0FBVTt3Q0FDVkksT0FBTTs7MERBRU4sOERBQUMxSSxrSEFBYUE7Z0RBQUNzSSxXQUFVOzs7Ozs7MERBQ3pCLDhEQUFDTTswREFBTTNILGlCQUFpQixlQUFlOzs7Ozs7Ozs7Ozs7a0RBR3pDLDhEQUFDc0g7d0NBQ0NDLFNBQVNqRDt3Q0FDVGtELFVBQVVwSDt3Q0FDVmlILFdBQVU7d0NBQ1ZJLE9BQU92SCxhQUFhLGFBQWE7a0RBRWhDRSxrQ0FDQyw4REFBQ3VIOzRDQUFLTixXQUFVO3NEQUFnQjs7Ozs7c0VBRWhDOztnREFDR25ILGFBQWEsYUFBYTtnREFDMUIsQ0FBQ0EsNEJBQWMsOERBQUNoQixrSEFBSUE7b0RBQUMwSSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU9yQ2hJLDZCQUNDLDhEQUFDaUk7d0JBQUVSLFdBQVU7a0NBQ1Z6SDs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWI7R0F6c0JNSjs7UUFRV0Qsc0RBQVNBOzs7S0FScEJDO0FBMnNCTixpRUFBZUEsbUJBQW1CQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNpdmFzXFxPbmVEcml2ZVxcRGVza3RvcFxcZmluYWxcXFdFRFpBVF9cXGZyb250ZW5kXFxjb21wb25lbnRzXFxWaWRlb0ludGVyYWN0aW9uQmFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcclxuXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBIZWFydCwgTWVzc2FnZUNpcmNsZSwgU2hhcmUyLCBNb3JlVmVydGljYWwsIFBsdXMgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCB7IE1kRmF2b3JpdGUsIE1kRmF2b3JpdGVCb3JkZXIgfSBmcm9tIFwicmVhY3QtaWNvbnMvbWRcIjtcclxuaW1wb3J0IGF4aW9zIGZyb20gXCJheGlvc1wiO1xyXG5pbXBvcnQgVXNlckF2YXRhciBmcm9tIFwiLi9Ib21lRGFzaGJvYXJkL1VzZXJBdmF0YXJcIjtcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5cclxuaW50ZXJmYWNlIFZpZGVvSW50ZXJhY3Rpb25CYXJQcm9wcyB7XHJcbiAgdXNlcm5hbWU6IHN0cmluZztcclxuICB1cGxvYWREYXRlOiBzdHJpbmc7XHJcbiAgdmlld0NvdW50OiBudW1iZXI7XHJcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgdXNlcklkOiBzdHJpbmc7XHJcbiAgdmlkZW9JZD86IHN0cmluZzsgLy8gQWRkIHZpZGVvSWQgZm9yIGxpa2UgZnVuY3Rpb25hbGl0eVxyXG59XHJcblxyXG5jb25zdCBWaWRlb0ludGVyYWN0aW9uQmFyOiBSZWFjdC5GQzxWaWRlb0ludGVyYWN0aW9uQmFyUHJvcHM+ID0gKHtcclxuICB1c2VybmFtZSxcclxuICB1cGxvYWREYXRlLFxyXG4gIHZpZXdDb3VudCxcclxuICBkZXNjcmlwdGlvbixcclxuICB1c2VySWQsXHJcbiAgdmlkZW9JZCxcclxufSkgPT4ge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IFtpc0NyZWF0aW5nQ2hhdCwgc2V0SXNDcmVhdGluZ0NoYXRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc0FkbWlyaW5nLCBzZXRJc0FkbWlyaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNBZG1pcmluZ0xvYWRpbmcsIHNldElzQWRtaXJpbmdMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNMaWtlZCwgc2V0SXNMaWtlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzTGlraW5nLCBzZXRJc0xpa2luZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dNZW51LCBzZXRTaG93TWVudV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIEZvcm1hdCB2aWV3IGNvdW50XHJcbiAgY29uc3QgZm9ybWF0Vmlld0NvdW50ID0gKGNvdW50OiBudW1iZXIpOiBzdHJpbmcgPT4ge1xyXG4gICAgaWYgKGNvdW50ID49IDEwMDAwMDApIHtcclxuICAgICAgcmV0dXJuIGAkeyhjb3VudCAvIDEwMDAwMDApLnRvRml4ZWQoMSl9TWA7XHJcbiAgICB9IGVsc2UgaWYgKGNvdW50ID49IDEwMDApIHtcclxuICAgICAgcmV0dXJuIGAkeyhjb3VudCAvIDEwMDApLnRvRml4ZWQoMSl9S2A7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gY291bnQudG9TdHJpbmcoKTtcclxuICB9O1xyXG5cclxuICAvLyBIYW5kbGUgbWVzc2FnZSBidXR0b24gY2xpY2tcclxuICBjb25zdCBoYW5kbGVNZXNzYWdlQ2xpY2sgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoaXNDcmVhdGluZ0NoYXQpIHJldHVybjtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc0NyZWF0aW5nQ2hhdCh0cnVlKTtcclxuXHJcbiAgICAgIC8vIEdldCB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZSAtIHVzZSB1c2VyVG9rZW4gYXMgaW4gdGhlIGNoYXQgcGFnZVxyXG4gICAgICBjb25zdCB0b2tlbiA9XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJ0b2tlblwiKSB8fCBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKFwidG9rZW5cIik7XHJcblxyXG4gICAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIk5vIGF1dGhlbnRpY2F0aW9uIHRva2VuIGZvdW5kXCIpO1xyXG4gICAgICAgIGFsZXJ0KFwiUGxlYXNlIGxvZyBpbiB0byBzZW5kIG1lc3NhZ2VzXCIpO1xyXG4gICAgICAgIHNldElzQ3JlYXRpbmdDaGF0KGZhbHNlKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFVzZSB0aGUgaGFyZGNvZGVkIFVSTCBmcm9tIHRoZSBtZXNzYWdlcyBwYWdlXHJcbiAgICAgIGNvbnN0IGFwaVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkw7XHJcblxyXG4gICAgICAvLyBJZiB1c2VySWQgaXMgJ2RlZmF1bHQnIG9yIG1pc3NpbmcsIHVzZSBhIGZhbGxiYWNrIElEIGZvciB0ZXN0aW5nXHJcbiAgICAgIC8vIEluIGEgcmVhbCBhcHAsIHlvdSB3b3VsZCBoYW5kbGUgdGhpcyBkaWZmZXJlbnRseVxyXG4gICAgICBjb25zdCBwYXJ0aWNpcGFudElkID1cclxuICAgICAgICAhdXNlcklkIHx8IHVzZXJJZCA9PT0gXCJkZWZhdWx0XCJcclxuICAgICAgICAgID8gdXNlcm5hbWUudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9cXHMrL2csIFwiX1wiKSArIFwiX2lkXCJcclxuICAgICAgICAgIDogdXNlcklkO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coYENyZWF0aW5nIGNvbnZlcnNhdGlvbiB3aXRoIHVzZXIgSUQ6ICR7cGFydGljaXBhbnRJZH1gKTtcclxuICAgICAgY29uc29sZS5sb2coYFVzaW5nIEFQSSBVUkw6ICR7YXBpVXJsfS9jb252ZXJzYXRpb25zYCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBBdXRob3JpemF0aW9uIHRva2VuOiAke3Rva2VuLnN1YnN0cmluZygwLCAxMCl9Li4uYCk7XHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIC8vIENyZWF0ZSBvciBnZXQgZXhpc3RpbmcgY29udmVyc2F0aW9uXHJcbiAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICBcIlJlcXVlc3QgcGF5bG9hZDpcIixcclxuICAgICAgICAgIEpTT04uc3RyaW5naWZ5KHsgcGFydGljaXBhbnRzOiBbcGFydGljaXBhbnRJZF0gfSlcclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2FwaVVybH0vY29udmVyc2F0aW9uc2AsIHtcclxuICAgICAgICAgIG1ldGhvZDogXCJQT1NUXCIsXHJcbiAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICAgIHBhcnRpY2lwYW50czogW3BhcnRpY2lwYW50SWRdLFxyXG4gICAgICAgICAgfSksXHJcbiAgICAgICAgICAvLyBBZGQgdGhlc2Ugb3B0aW9ucyB0byBoZWxwIHdpdGggQ09SUyBpc3N1ZXNcclxuICAgICAgICAgIG1vZGU6IFwiY29yc1wiLFxyXG4gICAgICAgICAgY3JlZGVudGlhbHM6IFwic2FtZS1vcmlnaW5cIixcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coXCJSZXNwb25zZSBzdGF0dXM6XCIsIHJlc3BvbnNlLnN0YXR1cyk7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJSZXNwb25zZSBoZWFkZXJzOlwiLCBbLi4ucmVzcG9uc2UuaGVhZGVycy5lbnRyaWVzKCldKTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gY3JlYXRlIGNvbnZlcnNhdGlvbjogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiQ29udmVyc2F0aW9uIGNyZWF0ZWQvcmV0cmlldmVkOlwiLCBkYXRhKTtcclxuXHJcbiAgICAgICAgLy8gSGFuZGxlIGRpZmZlcmVudCByZXNwb25zZSBmb3JtYXRzXHJcbiAgICAgICAgbGV0IGNvbnZlcnNhdGlvbklkID0gbnVsbDtcclxuXHJcbiAgICAgICAgLy8gQ2hlY2sgaWYgdGhlIHJlc3BvbnNlIGhhcyBhIGRpcmVjdCBjb252ZXJzYXRpb25faWQgcHJvcGVydHlcclxuICAgICAgICBpZiAoZGF0YS5jb252ZXJzYXRpb25faWQpIHtcclxuICAgICAgICAgIGNvbnZlcnNhdGlvbklkID0gZGF0YS5jb252ZXJzYXRpb25faWQ7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIENoZWNrIGlmIHRoZSByZXNwb25zZSBoYXMgYSBib2R5IHByb3BlcnR5IHRoYXQgbWlnaHQgY29udGFpbiB0aGUgY29udmVyc2F0aW9uX2lkXHJcbiAgICAgICAgZWxzZSBpZiAoZGF0YS5ib2R5ICYmIHR5cGVvZiBkYXRhLmJvZHkgPT09IFwic3RyaW5nXCIpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGJvZHlEYXRhID0gSlNPTi5wYXJzZShkYXRhLmJvZHkpO1xyXG4gICAgICAgICAgICBpZiAoYm9keURhdGEuY29udmVyc2F0aW9uX2lkKSB7XHJcbiAgICAgICAgICAgICAgY29udmVyc2F0aW9uSWQgPSBib2R5RGF0YS5jb252ZXJzYXRpb25faWQ7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJGb3VuZCBjb252ZXJzYXRpb24gSUQgaW4gYm9keTpcIiwgY29udmVyc2F0aW9uSWQpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGNhdGNoIChlKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBwYXJzaW5nIGJvZHkgZGF0YTpcIiwgZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAoIWNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiTm8gY29udmVyc2F0aW9uIElEIGZvdW5kIGluIGFueSBmb3JtYXRcIiwgZGF0YSk7XHJcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIHJlc3BvbnNlIGZyb20gc2VydmVyXCIpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gTmF2aWdhdGUgdG8gdGhlIGNoYXQgcGFnZVxyXG4gICAgICAgIHJvdXRlci5wdXNoKGAvbWVzc2FnZXMvJHtjb252ZXJzYXRpb25JZH1gKTtcclxuICAgICAgfSBjYXRjaCAoaW5uZXJFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJJbm5lciBmZXRjaCBlcnJvcjpcIiwgaW5uZXJFcnJvcik7XHJcbiAgICAgICAgdGhyb3cgaW5uZXJFcnJvcjtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNyZWF0aW5nIGNvbnZlcnNhdGlvbjpcIiwgZXJyb3IpO1xyXG5cclxuICAgICAgLy8gUHJvdmlkZSBtb3JlIHNwZWNpZmljIGVycm9yIG1lc3NhZ2VzIGJhc2VkIG9uIHRoZSBlcnJvclxyXG4gICAgICBpZiAoXHJcbiAgICAgICAgZXJyb3IgaW5zdGFuY2VvZiBUeXBlRXJyb3IgJiZcclxuICAgICAgICBlcnJvci5tZXNzYWdlLmluY2x1ZGVzKFwiRmFpbGVkIHRvIGZldGNoXCIpXHJcbiAgICAgICkge1xyXG4gICAgICAgIGFsZXJ0KFxyXG4gICAgICAgICAgXCJOZXR3b3JrIGVycm9yOiBQbGVhc2UgY2hlY2sgeW91ciBpbnRlcm5ldCBjb25uZWN0aW9uIGFuZCB0cnkgYWdhaW4uXCJcclxuICAgICAgICApO1xyXG4gICAgICB9IGVsc2UgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IubWVzc2FnZS5pbmNsdWRlcyhcIjQwMVwiKSkge1xyXG4gICAgICAgIGFsZXJ0KFwiQXV0aGVudGljYXRpb24gZXJyb3I6IFBsZWFzZSBsb2cgaW4gYWdhaW4uXCIpO1xyXG4gICAgICB9IGVsc2UgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IubWVzc2FnZS5pbmNsdWRlcyhcIjQwM1wiKSkge1xyXG4gICAgICAgIGFsZXJ0KFxyXG4gICAgICAgICAgXCJQZXJtaXNzaW9uIGRlbmllZDogWW91IGRvbid0IGhhdmUgcGVybWlzc2lvbiB0byBtZXNzYWdlIHRoaXMgdXNlci5cIlxyXG4gICAgICAgICk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgYWxlcnQoXCJGYWlsZWQgdG8gc3RhcnQgY29udmVyc2F0aW9uLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcclxuICAgICAgfVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNDcmVhdGluZ0NoYXQoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIG5hdmlnYXRlIHRvIHVzZXIgcHJvZmlsZVxyXG4gIGNvbnN0IG5hdmlnYXRlVG9Vc2VyUHJvZmlsZSA9ICgpID0+IHtcclxuICAgIGlmICh1c2VySWQpIHtcclxuICAgICAgLy8gR2V0IHRoZSB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2p3dF90b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dlZHphdF90b2tlbicpO1xyXG5cclxuICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE5hdmlnYXRlIHRvIHRoZSBwcm9maWxlIHBhZ2Ugd2l0aCB0aGUgdXNlcklkXHJcbiAgICAgIHJvdXRlci5wdXNoKGAvcHJvZmlsZS8ke3VzZXJJZH1gKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byBoYW5kbGUgbGlrZS91bmxpa2VcclxuICBjb25zdCBoYW5kbGVMaWtlVG9nZ2xlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKGlzTGlraW5nIHx8ICF2aWRlb0lkKSByZXR1cm47XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgc2V0SXNMaWtpbmcodHJ1ZSk7XHJcblxyXG4gICAgICAvLyBHZXQgdG9rZW4gZnJvbSBsb2NhbFN0b3JhZ2VcclxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKSB8fFxyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdqd3RfdG9rZW4nKSB8fFxyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd3ZWR6YXRfdG9rZW4nKTtcclxuXHJcbiAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oJ05vIGF1dGhlbnRpY2F0aW9uIHRva2VuIGZvdW5kJyk7XHJcbiAgICAgICAgYWxlcnQoJ1BsZWFzZSBsb2cgaW4gdG8gbGlrZSB0aGlzIHZpZGVvJyk7XHJcbiAgICAgICAgc2V0SXNMaWtpbmcoZmFsc2UpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gT3B0aW1pc3RpY2FsbHkgdXBkYXRlIFVJXHJcbiAgICAgIGNvbnN0IG5ld0xpa2VkU3RhdGUgPSAhaXNMaWtlZDtcclxuICAgICAgc2V0SXNMaWtlZChuZXdMaWtlZFN0YXRlKTtcclxuXHJcbiAgICAgIC8vIE1ha2UgQVBJIGNhbGwgdG8gTmV4dC5qcyBBUEkgcm91dGVzXHJcbiAgICAgIGNvbnN0IGVuZHBvaW50ID0gaXNMaWtlZCA/ICcvYXBpL3VubGlrZScgOiAnL2FwaS9saWtlJztcclxuICAgICAgY29uc29sZS5sb2coYE1ha2luZyBBUEkgY2FsbCB0bzogJHtlbmRwb2ludH0gZm9yIHZpZGVvOiAke3ZpZGVvSWR9YCk7XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGVuZHBvaW50LCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICBjb250ZW50X2lkOiB2aWRlb0lkLFxyXG4gICAgICAgICAgY29udGVudF90eXBlOiAndmlkZW8nXHJcbiAgICAgICAgfSlcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSSBFcnJvciBSZXNwb25zZTonLCBlcnJvclRleHQpO1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgSFRUUCBlcnJvciEgc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2VEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICBjb25zb2xlLmxvZygnQVBJIFN1Y2Nlc3MgUmVzcG9uc2U6JywgcmVzcG9uc2VEYXRhKTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKGAke2lzTGlrZWQgPyAnVW5saWtlZCcgOiAnTGlrZWQnfSB2aWRlbzogJHt2aWRlb0lkfWApO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdG9nZ2xpbmcgbGlrZTonLCBlcnJvcik7XHJcbiAgICAgIC8vIFJldmVydCBvcHRpbWlzdGljIHVwZGF0ZSBvbiBlcnJvclxyXG4gICAgICBzZXRJc0xpa2VkKCFpc0xpa2VkKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTGlraW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byBoYW5kbGUgYWRtaXJlL3VuYWRtaXJlXHJcbiAgY29uc3QgaGFuZGxlQWRtaXJlVG9nZ2xlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKGlzQWRtaXJpbmdMb2FkaW5nIHx8ICF1c2VySWQpIHJldHVybjtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc0FkbWlyaW5nTG9hZGluZyh0cnVlKTtcclxuXHJcbiAgICAgIC8vIEdldCB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2p3dF90b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dlZHphdF90b2tlbicpO1xyXG5cclxuICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnKTtcclxuICAgICAgICBhbGVydCgnUGxlYXNlIGxvZyBpbiB0byBhZG1pcmUgdGhpcyB1c2VyJyk7XHJcbiAgICAgICAgc2V0SXNBZG1pcmluZ0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gT3B0aW1pc3RpY2FsbHkgdXBkYXRlIFVJIHN0YXRlIGltbWVkaWF0ZWx5IGZvciBiZXR0ZXIgdXNlciBleHBlcmllbmNlXHJcbiAgICAgIGNvbnN0IG5ld0FkbWlyaW5nU3RhdGUgPSAhaXNBZG1pcmluZztcclxuICAgICAgc2V0SXNBZG1pcmluZyhuZXdBZG1pcmluZ1N0YXRlKTtcclxuXHJcbiAgICAgIC8vIE1ha2UgQVBJIGNhbGwgdG8gZm9sbG93L3VuZm9sbG93IHVzZXJcclxuICAgICAgY29uc3QgZW5kcG9pbnQgPSBpc0FkbWlyaW5nXHJcbiAgICAgICAgPyAnaHR0cHM6Ly91bnk0Y2Rzd2thLmV4ZWN1dGUtYXBpLmFwLXNvdXRoLTEuYW1hem9uYXdzLmNvbS90ZXN0L2h1Yi91bmZvbGxvdydcclxuICAgICAgICA6ICdodHRwczovL3VueTRjZHN3a2EuZXhlY3V0ZS1hcGkuYXAtc291dGgtMS5hbWF6b25hd3MuY29tL3Rlc3QvaHViL2ZvbGxvdyc7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhgTWFraW5nIHJlcXVlc3QgdG8gJHtlbmRwb2ludH0gd2l0aCB1c2VyIElEOiAke3VzZXJJZH1gKTtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8gTWFrZSB0aGUgQVBJIGNhbGxcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoXHJcbiAgICAgICAgICBlbmRwb2ludCxcclxuICAgICAgICAgIHsgdGFyZ2V0X3VzZXJfaWQ6IHVzZXJJZCB9LFxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgKTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ0FQSSByZXNwb25zZTonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhgU3VjY2Vzc2Z1bGx5ICR7aXNBZG1pcmluZyA/ICd1bmFkbWlyZWQnIDogJ2FkbWlyZWQnfSB1c2VyYCk7XHJcblxyXG4gICAgICAgIC8vIFVwZGF0ZSBsb2NhbFN0b3JhZ2Ugd2l0aCB0aGUgbmV3IHN0YXRlXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IGFkbWlyZWRVc2Vyc0pzb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWRtaXJlZFVzZXJzJykgfHwgJ3t9JztcclxuICAgICAgICAgIGxldCBhZG1pcmVkVXNlcnMgPSBKU09OLnBhcnNlKGFkbWlyZWRVc2Vyc0pzb24pO1xyXG5cclxuICAgICAgICAgIGlmIChuZXdBZG1pcmluZ1N0YXRlKSB7XHJcbiAgICAgICAgICAgIGFkbWlyZWRVc2Vyc1t1c2VySWRdID0gdHJ1ZTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGRlbGV0ZSBhZG1pcmVkVXNlcnNbdXNlcklkXTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYWRtaXJlZFVzZXJzJywgSlNPTi5zdHJpbmdpZnkoYWRtaXJlZFVzZXJzKSk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnVXBkYXRlZCBhZG1pcmVkIHVzZXJzIGluIGxvY2FsU3RvcmFnZTonLCBhZG1pcmVkVXNlcnMpO1xyXG5cclxuICAgICAgICAgIC8vIEZvcmNlIGEgcmVmcmVzaCBvZiB0aGUgZm9sbG93aW5nIGxpc3QgdG8gZW5zdXJlIGl0J3MgdXAgdG8gZGF0ZVxyXG4gICAgICAgICAgY29uc3QgZm9sbG93aW5nUmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoXHJcbiAgICAgICAgICAgIGBodHRwczovL3VueTRjZHN3a2EuZXhlY3V0ZS1hcGkuYXAtc291dGgtMS5hbWF6b25hd3MuY29tL3Rlc3QvaHViL2ZvbGxvd2luZ2AsXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICk7XHJcblxyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1VwZGF0ZWQgZm9sbG93aW5nIGxpc3Q6JywgZm9sbG93aW5nUmVzcG9uc2UuZGF0YSk7XHJcblxyXG4gICAgICAgICAgLy8gRG91YmxlLWNoZWNrIG91ciBzdGF0ZSBpcyBjb3JyZWN0XHJcbiAgICAgICAgICBpZiAoZm9sbG93aW5nUmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KGZvbGxvd2luZ1Jlc3BvbnNlLmRhdGEuZm9sbG93aW5nKSkge1xyXG4gICAgICAgICAgICBjb25zdCBpc0FjdHVhbGx5Rm9sbG93aW5nID0gZm9sbG93aW5nUmVzcG9uc2UuZGF0YS5mb2xsb3dpbmcuc29tZShcclxuICAgICAgICAgICAgICAodXNlcjogYW55KSA9PiB1c2VyLnVzZXJfaWQgPT09IHVzZXJJZCB8fCB1c2VyID09PSB1c2VySWRcclxuICAgICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICAgIGlmIChpc0FjdHVhbGx5Rm9sbG93aW5nICE9PSBuZXdBZG1pcmluZ1N0YXRlKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1N0YXRlIG1pc21hdGNoIGRldGVjdGVkLCBjb3JyZWN0aW5nLi4uJyk7XHJcbiAgICAgICAgICAgICAgc2V0SXNBZG1pcmluZyhpc0FjdHVhbGx5Rm9sbG93aW5nKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gVXBkYXRlIGxvY2FsU3RvcmFnZSBhZ2FpbiB3aXRoIHRoZSBjb3JyZWN0IHN0YXRlXHJcbiAgICAgICAgICAgICAgYWRtaXJlZFVzZXJzID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWRtaXJlZFVzZXJzJykgfHwgJ3t9Jyk7XHJcblxyXG4gICAgICAgICAgICAgIGlmIChpc0FjdHVhbGx5Rm9sbG93aW5nKSB7XHJcbiAgICAgICAgICAgICAgICBhZG1pcmVkVXNlcnNbdXNlcklkXSA9IHRydWU7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSBhZG1pcmVkVXNlcnNbdXNlcklkXTtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhZG1pcmVkVXNlcnMnLCBKU09OLnN0cmluZ2lmeShhZG1pcmVkVXNlcnMpKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKHN0b3JhZ2VFcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgbG9jYWxTdG9yYWdlOicsIHN0b3JhZ2VFcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChhcGlFcnJvcjogYW55KSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgJHtpc0FkbWlyaW5nID8gJ3VuYWRtaXJpbmcnIDogJ2FkbWlyaW5nJ30gdXNlcjpgLCBhcGlFcnJvcik7XHJcblxyXG4gICAgICAgIGlmIChhcGlFcnJvci5yZXNwb25zZSkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0Vycm9yIHJlc3BvbnNlIGRhdGE6JywgYXBpRXJyb3IucmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnRXJyb3IgcmVzcG9uc2Ugc3RhdHVzOicsIGFwaUVycm9yLnJlc3BvbnNlLnN0YXR1cyk7XHJcblxyXG4gICAgICAgICAgLy8gSWYgdGhlIGVycm9yIGlzIHRoYXQgdGhlIHVzZXIgaXMgYWxyZWFkeSBmb2xsb3dpbmcvbm90IGZvbGxvd2luZywgdGhlIFVJIHN0YXRlIGlzIGFscmVhZHkgY29ycmVjdFxyXG4gICAgICAgICAgaWYgKGFwaUVycm9yLnJlc3BvbnNlLnN0YXR1cyA9PT0gNDAwICYmXHJcbiAgICAgICAgICAgIGFwaUVycm9yLnJlc3BvbnNlLmRhdGEgJiZcclxuICAgICAgICAgICAgKGFwaUVycm9yLnJlc3BvbnNlLmRhdGEuZXJyb3IgPT09IFwiQWxyZWFkeSBmb2xsb3dpbmcgdGhpcyB1c2VyXCIgfHxcclxuICAgICAgICAgICAgICBhcGlFcnJvci5yZXNwb25zZS5kYXRhLmVycm9yID09PSBcIk5vdCBmb2xsb3dpbmcgdGhpcyB1c2VyXCIpKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBbHJlYWR5IGluIGRlc2lyZWQgc3RhdGUsIGtlZXBpbmcgVUkgdXBkYXRlZCcpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBJZiB0aGVyZSB3YXMgYW4gZXJyb3IgdGhhdCB3YXNuJ3QganVzdCBcImFscmVhZHkgaW4gZGVzaXJlZCBzdGF0ZVwiLCByZXZlcnQgdGhlIFVJXHJcbiAgICAgICAgc2V0SXNBZG1pcmluZyghbmV3QWRtaXJpbmdTdGF0ZSk7XHJcblxyXG4gICAgICAgIC8vIEFsc28gdXBkYXRlIGxvY2FsU3RvcmFnZSB0byBtYXRjaFxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCBhZG1pcmVkVXNlcnNKc29uID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FkbWlyZWRVc2VycycpIHx8ICd7fSc7XHJcbiAgICAgICAgICBjb25zdCBhZG1pcmVkVXNlcnMgPSBKU09OLnBhcnNlKGFkbWlyZWRVc2Vyc0pzb24pO1xyXG5cclxuICAgICAgICAgIGlmICghbmV3QWRtaXJpbmdTdGF0ZSkge1xyXG4gICAgICAgICAgICBhZG1pcmVkVXNlcnNbdXNlcklkXSA9IHRydWU7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBkZWxldGUgYWRtaXJlZFVzZXJzW3VzZXJJZF07XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FkbWlyZWRVc2VycycsIEpTT04uc3RyaW5naWZ5KGFkbWlyZWRVc2VycykpO1xyXG4gICAgICAgIH0gY2F0Y2ggKHN0b3JhZ2VFcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgbG9jYWxTdG9yYWdlIGFmdGVyIEFQSSBlcnJvcjonLCBzdG9yYWdlRXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdVbmV4cGVjdGVkIGVycm9yIGluIGhhbmRsZUFkbWlyZVRvZ2dsZTonLCBlcnJvcik7XHJcbiAgICAgIC8vIFJldmVydCBVSSBzdGF0ZSBvbiB1bmV4cGVjdGVkIGVycm9yc1xyXG4gICAgICBzZXRJc0FkbWlyaW5nKCFpc0FkbWlyaW5nKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzQWRtaXJpbmdMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBJbml0aWFsaXplIGxpa2Ugc3RhdHVzIGZyb20gbG9jYWxTdG9yYWdlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghdmlkZW9JZCkgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIENoZWNrIGlmIHRoaXMgdmlkZW8gaXMgbGlrZWQgaW4gbG9jYWxTdG9yYWdlXHJcbiAgICAgIGNvbnN0IGxpa2VkR2xpbXBzZXNEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2xpa2VkR2xpbXBzZXMnKTtcclxuICAgICAgY29uc3QgbGlrZWRNb3ZpZXNEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2xpa2VkTW92aWVzJyk7XHJcblxyXG4gICAgICBsZXQgaXNWaWRlb0xpa2VkID0gZmFsc2U7XHJcblxyXG4gICAgICAvLyBDaGVjayBnbGltcHNlc1xyXG4gICAgICBpZiAobGlrZWRHbGltcHNlc0RhdGEpIHtcclxuICAgICAgICBjb25zdCBsaWtlZEdsaW1wc2VzID0gSlNPTi5wYXJzZShsaWtlZEdsaW1wc2VzRGF0YSk7XHJcbiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkobGlrZWRHbGltcHNlcykgJiYgbGlrZWRHbGltcHNlcy5pbmNsdWRlcyh2aWRlb0lkKSkge1xyXG4gICAgICAgICAgaXNWaWRlb0xpa2VkID0gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENoZWNrIG1vdmllc1xyXG4gICAgICBpZiAoIWlzVmlkZW9MaWtlZCAmJiBsaWtlZE1vdmllc0RhdGEpIHtcclxuICAgICAgICBjb25zdCBsaWtlZE1vdmllcyA9IEpTT04ucGFyc2UobGlrZWRNb3ZpZXNEYXRhKTtcclxuICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShsaWtlZE1vdmllcykgJiYgbGlrZWRNb3ZpZXMuaW5jbHVkZXModmlkZW9JZCkpIHtcclxuICAgICAgICAgIGlzVmlkZW9MaWtlZCA9IHRydWU7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICBzZXRJc0xpa2VkKGlzVmlkZW9MaWtlZCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKGDwn46sIFZpZGVvSW50ZXJhY3Rpb25CYXI6IFZpZGVvICR7dmlkZW9JZH0gbGlrZSBzdGF0dXM6YCwgaXNWaWRlb0xpa2VkKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/CfjqwgRXJyb3IgbG9hZGluZyBsaWtlIHN0YXR1cyBmb3IgVmlkZW9JbnRlcmFjdGlvbkJhcjonLCBlcnJvcik7XHJcbiAgICAgIHNldElzTGlrZWQoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH0sIFt2aWRlb0lkXSk7XHJcblxyXG4gIC8vIENoZWNrIGlmIHVzZXIgaXMgYWxyZWFkeSBhZG1pcmluZyBvbiBjb21wb25lbnQgbW91bnRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCF1c2VySWQpIHJldHVybjtcclxuXHJcbiAgICAvLyBGaXJzdCBjaGVjayBsb2NhbFN0b3JhZ2UgZm9yIGNhY2hlZCBhZG1pcmluZyBzdGF0ZVxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgYWRtaXJlZFVzZXJzSnNvbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZG1pcmVkVXNlcnMnKTtcclxuICAgICAgaWYgKGFkbWlyZWRVc2Vyc0pzb24pIHtcclxuICAgICAgICBjb25zdCBhZG1pcmVkVXNlcnMgPSBKU09OLnBhcnNlKGFkbWlyZWRVc2Vyc0pzb24pO1xyXG4gICAgICAgIGlmIChhZG1pcmVkVXNlcnNbdXNlcklkXSkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0ZvdW5kIGFkbWlyaW5nIHN0YXR1cyBpbiBsb2NhbFN0b3JhZ2U6JywgdHJ1ZSk7XHJcbiAgICAgICAgICBzZXRJc0FkbWlyaW5nKHRydWUpO1xyXG4gICAgICAgICAgLy8gQ29udGludWUgd2l0aCBBUEkgY2hlY2sgdG8gdmVyaWZ5XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChzdG9yYWdlRXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVhZGluZyBmcm9tIGxvY2FsU3RvcmFnZTonLCBzdG9yYWdlRXJyb3IpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNoZWNrQWRtaXJpbmdTdGF0dXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdDaGVja2luZyBhZG1pcmluZyBzdGF0dXMgZm9yIHVzZXIgSUQ6JywgdXNlcklkKTtcclxuXHJcbiAgICAgIC8vIEdldCB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2p3dF90b2tlbicpIHx8XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dlZHphdF90b2tlbicpO1xyXG5cclxuICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIHRoaXMgaXMgdGhlIGN1cnJlbnQgdXNlcidzIGNvbnRlbnQgLSBubyBuZWVkIHRvIGNoZWNrIGFkbWlyaW5nIHN0YXR1c1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHRva2VuUGFydHMgPSB0b2tlbi5zcGxpdCgnLicpO1xyXG4gICAgICAgIGlmICh0b2tlblBhcnRzLmxlbmd0aCA9PT0gMykge1xyXG4gICAgICAgICAgY29uc3QgcGF5bG9hZCA9IEpTT04ucGFyc2UoYXRvYih0b2tlblBhcnRzWzFdKSk7XHJcbiAgICAgICAgICBpZiAocGF5bG9hZC51c2VyX2lkID09PSB1c2VySWQpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1RoaXMgaXMgdGhlIGN1cnJlbnQgdXNlclxcJ3MgY29udGVudCwgc2tpcHBpbmcgYWRtaXJpbmcgc3RhdHVzIGNoZWNrJyk7XHJcbiAgICAgICAgICAgIHNldElzQWRtaXJpbmcoZmFsc2UpOyAvLyBVc2VyIGNhbid0IGFkbWlyZSB0aGVtc2VsdmVzXHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKHRva2VuRXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQ291bGQgbm90IGRlY29kZSB0b2tlbiB0byBjaGVjayBjdXJyZW50IHVzZXIsIHByb2NlZWRpbmcgd2l0aCBBUEkgY2FsbHMnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICAvLyBEaXJlY3QgQVBJIGNhbGwgdG8gY2hlY2sgZm9sbG93aW5nIHN0YXR1cyAtIG1vc3QgcmVsaWFibGUgbWV0aG9kXHJcbiAgICAgICAgY29uc3QgZm9sbG93aW5nUmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoXHJcbiAgICAgICAgICBgaHR0cHM6Ly91bnk0Y2Rzd2thLmV4ZWN1dGUtYXBpLmFwLXNvdXRoLTEuYW1hem9uYXdzLmNvbS90ZXN0L2h1Yi9mb2xsb3dpbmdgLFxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgKTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ0ZvbGxvd2luZyByZXNwb25zZTonLCBmb2xsb3dpbmdSZXNwb25zZS5kYXRhKTtcclxuXHJcbiAgICAgICAgaWYgKGZvbGxvd2luZ1Jlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShmb2xsb3dpbmdSZXNwb25zZS5kYXRhLmZvbGxvd2luZykpIHtcclxuICAgICAgICAgIC8vIENoZWNrIGlmIHVzZXJJZCBpcyBpbiB0aGUgZm9sbG93aW5nIGFycmF5XHJcbiAgICAgICAgICBjb25zdCBpc0ZvbGxvd2luZyA9IGZvbGxvd2luZ1Jlc3BvbnNlLmRhdGEuZm9sbG93aW5nLnNvbWUoXHJcbiAgICAgICAgICAgICh1c2VyOiBhbnkpID0+IHVzZXIudXNlcl9pZCA9PT0gdXNlcklkIHx8IHVzZXIgPT09IHVzZXJJZFxyXG4gICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhgVXNlciAke2lzRm9sbG93aW5nID8gJ2lzJyA6ICdpcyBub3QnfSBpbiBmb2xsb3dpbmcgbGlzdDpgLCB1c2VySWQpO1xyXG4gICAgICAgICAgc2V0SXNBZG1pcmluZyhpc0ZvbGxvd2luZyk7XHJcblxyXG4gICAgICAgICAgLy8gVXBkYXRlIGxvY2FsU3RvcmFnZSBmb3IgZnV0dXJlIHJlZmVyZW5jZVxyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgYWRtaXJlZFVzZXJzSnNvbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZG1pcmVkVXNlcnMnKSB8fCAne30nO1xyXG4gICAgICAgICAgICBjb25zdCBhZG1pcmVkVXNlcnMgPSBKU09OLnBhcnNlKGFkbWlyZWRVc2Vyc0pzb24pO1xyXG5cclxuICAgICAgICAgICAgaWYgKGlzRm9sbG93aW5nKSB7XHJcbiAgICAgICAgICAgICAgYWRtaXJlZFVzZXJzW3VzZXJJZF0gPSB0cnVlO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGRlbGV0ZSBhZG1pcmVkVXNlcnNbdXNlcklkXTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FkbWlyZWRVc2VycycsIEpTT04uc3RyaW5naWZ5KGFkbWlyZWRVc2VycykpO1xyXG4gICAgICAgICAgfSBjYXRjaCAoc3RvcmFnZUVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGxvY2FsU3RvcmFnZTonLCBzdG9yYWdlRXJyb3IpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGZvbGxvd2luZ0Vycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0Vycm9yIGZldGNoaW5nIGZvbGxvd2luZyBsaXN0LCB0cnlpbmcgYWx0ZXJuYXRpdmUgbWV0aG9kJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEZhbGxiYWNrOiBUcnkgdG8gZ2V0IHVzZXIgcHJvZmlsZVxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHByb2ZpbGVSZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChcclxuICAgICAgICAgIGBodHRwczovL3VueTRjZHN3a2EuZXhlY3V0ZS1hcGkuYXAtc291dGgtMS5hbWF6b25hd3MuY29tL3Rlc3QvaHViL3VzZXItcHJvZmlsZT91c2VyX2lkPSR7dXNlcklkfWAsXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygnVXNlciBwcm9maWxlIHJlc3BvbnNlOicsIHByb2ZpbGVSZXNwb25zZS5kYXRhKTtcclxuXHJcbiAgICAgICAgaWYgKHByb2ZpbGVSZXNwb25zZS5kYXRhICYmIHByb2ZpbGVSZXNwb25zZS5kYXRhLmlzX2ZvbGxvd2luZyAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnU2V0dGluZyBhZG1pcmluZyBzdGF0dXMgZnJvbSBwcm9maWxlOicsIHByb2ZpbGVSZXNwb25zZS5kYXRhLmlzX2ZvbGxvd2luZyk7XHJcbiAgICAgICAgICBzZXRJc0FkbWlyaW5nKHByb2ZpbGVSZXNwb25zZS5kYXRhLmlzX2ZvbGxvd2luZyk7XHJcblxyXG4gICAgICAgICAgLy8gVXBkYXRlIGxvY2FsU3RvcmFnZVxyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgYWRtaXJlZFVzZXJzSnNvbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZG1pcmVkVXNlcnMnKSB8fCAne30nO1xyXG4gICAgICAgICAgICBjb25zdCBhZG1pcmVkVXNlcnMgPSBKU09OLnBhcnNlKGFkbWlyZWRVc2Vyc0pzb24pO1xyXG5cclxuICAgICAgICAgICAgaWYgKHByb2ZpbGVSZXNwb25zZS5kYXRhLmlzX2ZvbGxvd2luZykge1xyXG4gICAgICAgICAgICAgIGFkbWlyZWRVc2Vyc1t1c2VySWRdID0gdHJ1ZTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBkZWxldGUgYWRtaXJlZFVzZXJzW3VzZXJJZF07XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhZG1pcmVkVXNlcnMnLCBKU09OLnN0cmluZ2lmeShhZG1pcmVkVXNlcnMpKTtcclxuICAgICAgICAgIH0gY2F0Y2ggKHN0b3JhZ2VFcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBsb2NhbFN0b3JhZ2U6Jywgc3RvcmFnZUVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKHByb2ZpbGVFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdFcnJvciBmZXRjaGluZyB1c2VyIHByb2ZpbGUsIHRyeWluZyBhbm90aGVyIG1ldGhvZCcpO1xyXG5cclxuICAgICAgICAvLyBMYXN0IHJlc29ydDogVHJ5IHRoZSB1c2VyLWZvbGxvdy1zdGF0cyBlbmRwb2ludFxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCBzdGF0c1Jlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KFxyXG4gICAgICAgICAgICBgaHR0cHM6Ly91bnk0Y2Rzd2thLmV4ZWN1dGUtYXBpLmFwLXNvdXRoLTEuYW1hem9uYXdzLmNvbS90ZXN0L2h1Yi91c2VyLWZvbGxvdy1zdGF0cz90YXJnZXRfdXNlcl9pZD0ke3VzZXJJZH1gLFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIGZvbGxvdyBzdGF0cyByZXNwb25zZTonLCBzdGF0c1Jlc3BvbnNlLmRhdGEpO1xyXG5cclxuICAgICAgICAgIGlmIChzdGF0c1Jlc3BvbnNlLmRhdGEgJiYgc3RhdHNSZXNwb25zZS5kYXRhLmlzX2ZvbGxvd2luZyAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgIHNldElzQWRtaXJpbmcoc3RhdHNSZXNwb25zZS5kYXRhLmlzX2ZvbGxvd2luZyk7XHJcblxyXG4gICAgICAgICAgICAvLyBVcGRhdGUgbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgYWRtaXJlZFVzZXJzSnNvbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZG1pcmVkVXNlcnMnKSB8fCAne30nO1xyXG4gICAgICAgICAgICAgIGNvbnN0IGFkbWlyZWRVc2VycyA9IEpTT04ucGFyc2UoYWRtaXJlZFVzZXJzSnNvbik7XHJcblxyXG4gICAgICAgICAgICAgIGlmIChzdGF0c1Jlc3BvbnNlLmRhdGEuaXNfZm9sbG93aW5nKSB7XHJcbiAgICAgICAgICAgICAgICBhZG1pcmVkVXNlcnNbdXNlcklkXSA9IHRydWU7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSBhZG1pcmVkVXNlcnNbdXNlcklkXTtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhZG1pcmVkVXNlcnMnLCBKU09OLnN0cmluZ2lmeShhZG1pcmVkVXNlcnMpKTtcclxuICAgICAgICAgICAgfSBjYXRjaCAoc3RvcmFnZUVycm9yKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgbG9jYWxTdG9yYWdlOicsIHN0b3JhZ2VFcnJvcik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChzdGF0c0Vycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQWxsIEFQSSBtZXRob2RzIGZhaWxlZCB0byBjaGVjayBhZG1pcmluZyBzdGF0dXMsIHVzaW5nIGxvY2FsU3RvcmFnZSBmYWxsYmFjaycpO1xyXG5cclxuICAgICAgICAgIC8vIEZhbGxiYWNrIHRvIGxvY2FsU3RvcmFnZSBpZiBhbGwgQVBJIGNhbGxzIGZhaWxcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGFkbWlyZWRVc2Vyc0pzb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWRtaXJlZFVzZXJzJykgfHwgJ3t9JztcclxuICAgICAgICAgICAgY29uc3QgYWRtaXJlZFVzZXJzID0gSlNPTi5wYXJzZShhZG1pcmVkVXNlcnNKc29uKTtcclxuICAgICAgICAgICAgY29uc3QgaXNBZG1pcmluZ0Zyb21TdG9yYWdlID0gYWRtaXJlZFVzZXJzW3VzZXJJZF0gPT09IHRydWU7XHJcbiAgICAgICAgICAgIHNldElzQWRtaXJpbmcoaXNBZG1pcmluZ0Zyb21TdG9yYWdlKTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFVzaW5nIGxvY2FsU3RvcmFnZSBmYWxsYmFjazogJHtpc0FkbWlyaW5nRnJvbVN0b3JhZ2UgPyAnYWRtaXJpbmcnIDogJ25vdCBhZG1pcmluZyd9YCk7XHJcbiAgICAgICAgICB9IGNhdGNoIChzdG9yYWdlRXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ2xvY2FsU3RvcmFnZSBmYWxsYmFjayBhbHNvIGZhaWxlZCwgZGVmYXVsdGluZyB0byBub3QgYWRtaXJpbmcnKTtcclxuICAgICAgICAgICAgc2V0SXNBZG1pcmluZyhmYWxzZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIC8vIENhbGwgdGhlIEFQSSBjaGVjayBmdW5jdGlvblxyXG4gICAgY2hlY2tBZG1pcmluZ1N0YXR1cygpO1xyXG4gIH0sIFt1c2VySWRdKTtcclxuXHJcbiAgLy8gQWRkIGNsaWNrIG91dHNpZGUgaGFuZGxlciB0byBjbG9zZSBtZW51XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGhhbmRsZUNsaWNrT3V0c2lkZSA9IChldmVudDogTW91c2VFdmVudCkgPT4ge1xyXG4gICAgICBpZiAoc2hvd01lbnUpIHtcclxuICAgICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQgYXMgSFRNTEVsZW1lbnQ7XHJcbiAgICAgICAgaWYgKCF0YXJnZXQuY2xvc2VzdCgnLm1lbnUtY29udGFpbmVyJykpIHtcclxuICAgICAgICAgIHNldFNob3dNZW51KGZhbHNlKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XHJcbiAgICB9O1xyXG4gIH0sIFtzaG93TWVudV0pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUgcC0yIHNtOnAtMyBtZDpwLTQgcm91bmRlZC1iLXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgYm9yZGVyLXQtMFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zIG1kOm1iLTRcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBzbTpzcGFjZS14LTIgbWQ6c3BhY2UteC0zXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xIHNtOnNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTGlrZVRvZ2dsZX1cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMaWtpbmd9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1mdWxsIHAtMS41IHNtOnAtMiB0cmFuc2l0aW9uLWNvbG9ycyAke1xyXG4gICAgICAgICAgICAgICAgaXNMaWtlZFxyXG4gICAgICAgICAgICAgICAgICA/ICdiZy1yZWQtMTAwIGhvdmVyOmJnLXJlZC0yMDAnXHJcbiAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktMTAwIGhvdmVyOmJnLWdyYXktMjAwJ1xyXG4gICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgIHRpdGxlPXtpc0xpa2VkID8gJ1VubGlrZScgOiAnTGlrZSd9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7aXNMaWtlZCA/IChcclxuICAgICAgICAgICAgICAgIDxNZEZhdm9yaXRlIGNsYXNzTmFtZT1cInctNCBoLTQgc206dy01IHNtOmgtNVwiIGNvbG9yPVwiI0IzMUIxRVwiIC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxNZEZhdm9yaXRlQm9yZGVyIGNsYXNzTmFtZT1cInctNCBoLTQgc206dy01IHNtOmgtNVwiIGNvbG9yPVwiIzY2NlwiIC8+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS0xMDAgaG92ZXI6YmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIHAtMS41IHNtOnAtMiB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTWVzc2FnZUNsaWNrfVxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc0NyZWF0aW5nQ2hhdH1cclxuICAgICAgICAgICAgICB0aXRsZT1cIlNlbmQgYSBtZXNzYWdlXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxNZXNzYWdlQ2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTQgc206dy01IHNtOmgtNSB0ZXh0LWdyYXktNzAwXCIgLz5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS0xMDAgaG92ZXI6YmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIHAtMS41IHNtOnAtMiB0cmFuc2l0aW9uLWNvbG9yc1wiPlxyXG4gICAgICAgICAgICAgIDxTaGFyZTIgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01IHRleHQtZ3JheS03MDBcIiAvPlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGRcIj57Zm9ybWF0Vmlld0NvdW50KHZpZXdDb3VudCl9PC9zcGFuPntcIiBcIn1cclxuICAgICAgICAgICAgdmlld3NcclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtZW51LWNvbnRhaW5lclwiPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01lbnUoIXNob3dNZW51KX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEuNSByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPE1vcmVWZXJ0aWNhbCBzaXplPXsxOH0gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMFwiIC8+XHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG5cclxuICAgICAgICAgICAge3Nob3dNZW51ICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgbXQtMSB3LTQ4IGJnLXdoaXRlIHJvdW5kZWQtbWQgc2hhZG93LWxnIHotMTAgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgUmVwb3J0IGNvbnRlbnRcclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHB0LTRcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyXCIgb25DbGljaz17bmF2aWdhdGVUb1VzZXJQcm9maWxlfT5cclxuICAgICAgICAgICAgICA8VXNlckF2YXRhciB1c2VybmFtZT17dXNlcm5hbWUgfHwgXCJBbm9ueW1vdXNcIn0gc2l6ZT1cIm1kXCIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibGFjayBjdXJzb3ItcG9pbnRlciBob3Zlcjp1bmRlcmxpbmVcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17bmF2aWdhdGVUb1VzZXJQcm9maWxlfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHt1c2VybmFtZX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlVwbG9hZGVkIHt1cGxvYWREYXRlIHx8ICdyZWNlbnRseSd9PC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTWVzc2FnZUNsaWNrfVxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc0NyZWF0aW5nQ2hhdH1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgYmctZ3JlZW4tNTAwIGhvdmVyOmJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHB4LTMgcHktMS41IHJvdW5kZWQtZnVsbCB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICB0aXRsZT1cIlNlbmQgYSBtZXNzYWdlXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxNZXNzYWdlQ2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuPntpc0NyZWF0aW5nQ2hhdCA/IFwiT3BlbmluZy4uLlwiIDogXCJNZXNzYWdlXCJ9PC9zcGFuPlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZG1pcmVUb2dnbGV9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzQWRtaXJpbmdMb2FkaW5nfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHB4LTMgcHktMS41IHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGJnLVsjQjMxQjFFXSB0ZXh0LXdoaXRlIGhvdmVyOmJnLXJlZC03MDBcIlxyXG4gICAgICAgICAgICAgIHRpdGxlPXtpc0FkbWlyaW5nID8gXCJBZG1pcmluZ1wiIDogXCJBZG1pcmUgdGhpcyB1c2VyXCJ9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7aXNBZG1pcmluZ0xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+Li4uPC9zcGFuPlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICB7aXNBZG1pcmluZyA/ICdBZG1pcmluZycgOiAnQWRtaXJlJ31cclxuICAgICAgICAgICAgICAgICAgeyFpc0FkbWlyaW5nICYmIDxQbHVzIHNpemU9ezE2fSAvPn1cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHtkZXNjcmlwdGlvbiAmJiAoXHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsYWNrIG10LTIgdGV4dC14cyBzbTp0ZXh0LXNtIGxpbmUtY2xhbXAtMyBzbTpsaW5lLWNsYW1wLW5vbmVcIj5cclxuICAgICAgICAgICAge2Rlc2NyaXB0aW9ufVxyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFZpZGVvSW50ZXJhY3Rpb25CYXI7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTWVzc2FnZUNpcmNsZSIsIlNoYXJlMiIsIk1vcmVWZXJ0aWNhbCIsIlBsdXMiLCJNZEZhdm9yaXRlIiwiTWRGYXZvcml0ZUJvcmRlciIsImF4aW9zIiwiVXNlckF2YXRhciIsInVzZVJvdXRlciIsIlZpZGVvSW50ZXJhY3Rpb25CYXIiLCJ1c2VybmFtZSIsInVwbG9hZERhdGUiLCJ2aWV3Q291bnQiLCJkZXNjcmlwdGlvbiIsInVzZXJJZCIsInZpZGVvSWQiLCJyb3V0ZXIiLCJpc0NyZWF0aW5nQ2hhdCIsInNldElzQ3JlYXRpbmdDaGF0IiwiaXNBZG1pcmluZyIsInNldElzQWRtaXJpbmciLCJpc0FkbWlyaW5nTG9hZGluZyIsInNldElzQWRtaXJpbmdMb2FkaW5nIiwiaXNMaWtlZCIsInNldElzTGlrZWQiLCJpc0xpa2luZyIsInNldElzTGlraW5nIiwic2hvd01lbnUiLCJzZXRTaG93TWVudSIsImZvcm1hdFZpZXdDb3VudCIsImNvdW50IiwidG9GaXhlZCIsInRvU3RyaW5nIiwiaGFuZGxlTWVzc2FnZUNsaWNrIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwic2Vzc2lvblN0b3JhZ2UiLCJjb25zb2xlIiwiZXJyb3IiLCJhbGVydCIsImFwaVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwicGFydGljaXBhbnRJZCIsInRvTG93ZXJDYXNlIiwicmVwbGFjZSIsImxvZyIsInN1YnN0cmluZyIsIkpTT04iLCJzdHJpbmdpZnkiLCJwYXJ0aWNpcGFudHMiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJib2R5IiwibW9kZSIsImNyZWRlbnRpYWxzIiwic3RhdHVzIiwiZW50cmllcyIsIm9rIiwiRXJyb3IiLCJkYXRhIiwianNvbiIsImNvbnZlcnNhdGlvbklkIiwiY29udmVyc2F0aW9uX2lkIiwiYm9keURhdGEiLCJwYXJzZSIsImUiLCJwdXNoIiwiaW5uZXJFcnJvciIsIlR5cGVFcnJvciIsIm1lc3NhZ2UiLCJpbmNsdWRlcyIsIm5hdmlnYXRlVG9Vc2VyUHJvZmlsZSIsIndhcm4iLCJoYW5kbGVMaWtlVG9nZ2xlIiwibmV3TGlrZWRTdGF0ZSIsImVuZHBvaW50IiwiY29udGVudF9pZCIsImNvbnRlbnRfdHlwZSIsImVycm9yVGV4dCIsInRleHQiLCJyZXNwb25zZURhdGEiLCJoYW5kbGVBZG1pcmVUb2dnbGUiLCJuZXdBZG1pcmluZ1N0YXRlIiwicG9zdCIsInRhcmdldF91c2VyX2lkIiwiYWRtaXJlZFVzZXJzSnNvbiIsImFkbWlyZWRVc2VycyIsInNldEl0ZW0iLCJmb2xsb3dpbmdSZXNwb25zZSIsImdldCIsIkFycmF5IiwiaXNBcnJheSIsImZvbGxvd2luZyIsImlzQWN0dWFsbHlGb2xsb3dpbmciLCJzb21lIiwidXNlciIsInVzZXJfaWQiLCJzdG9yYWdlRXJyb3IiLCJhcGlFcnJvciIsImxpa2VkR2xpbXBzZXNEYXRhIiwibGlrZWRNb3ZpZXNEYXRhIiwiaXNWaWRlb0xpa2VkIiwibGlrZWRHbGltcHNlcyIsImxpa2VkTW92aWVzIiwiY2hlY2tBZG1pcmluZ1N0YXR1cyIsInRva2VuUGFydHMiLCJzcGxpdCIsImxlbmd0aCIsInBheWxvYWQiLCJhdG9iIiwidG9rZW5FcnJvciIsImlzRm9sbG93aW5nIiwiZm9sbG93aW5nRXJyb3IiLCJwcm9maWxlUmVzcG9uc2UiLCJpc19mb2xsb3dpbmciLCJ1bmRlZmluZWQiLCJwcm9maWxlRXJyb3IiLCJzdGF0c1Jlc3BvbnNlIiwic3RhdHNFcnJvciIsImlzQWRtaXJpbmdGcm9tU3RvcmFnZSIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImV2ZW50IiwidGFyZ2V0IiwiY2xvc2VzdCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJ0aXRsZSIsImNvbG9yIiwic3BhbiIsInNpemUiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/VideoInteractionBar.tsx\n"));

/***/ })

});