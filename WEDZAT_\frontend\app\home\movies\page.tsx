"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";
import { MdOutlineModeComment, MdOutlineShare, MdFavorite, MdFavoriteBorder } from "react-icons/md";
import VideoVendorDetails from "../../../components/VideoVendorDetails";
import { useRouter } from "next/navigation";

// Define interface for movie video items
interface MovieVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string; // Added user_id field
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface ApiResponse {
  movies: MovieVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

export default function MoviesPage() {
  const router = useRouter();
  const [movies, setMovies] = useState<MovieVideo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  // Removed right sidebar state
  const [likedVideos, setLikedVideos] = useState<string[]>([]);
  const [newItemsCount, setNewItemsCount] = useState<number>(0); // Track newly loaded items

  useEffect(() => {
    setIsClient(true);

    // Initialize liked movies from localStorage
    console.log('🎥 Initializing movies like state from localStorage');

    try {
      const likedMoviesData = localStorage.getItem('likedMovies');
      if (likedMoviesData) {
        const likedArray = JSON.parse(likedMoviesData);
        if (Array.isArray(likedArray)) {
          setLikedVideos(likedArray);
          console.log('🎥 Loaded liked movies from localStorage:', likedArray);
        } else {
          console.log('🎥 Invalid liked movies data, starting fresh');
          setLikedVideos([]);
        }
      } else {
        console.log('🎥 No liked movies in localStorage, starting fresh');
        setLikedVideos([]);
      }
    } catch (error) {
      console.error('🎥 Error loading liked movies from localStorage:', error);
      setLikedVideos([]);
    }

    // Add debug functions to window
    if (typeof window !== 'undefined') {
      (window as any).clearMovieLikes = () => {
        const keysToRemove = [
          'likedGlimpses', 'likedMovies', 'likedFlashes', 'likedPhotos', 'likedStories',
          'liked_glimpses', 'liked_movies', 'liked_flashes', 'liked_photos', 'liked_stories'
        ];
        keysToRemove.forEach(key => localStorage.removeItem(key));
        console.log('🎥 Cleared all movie like states from localStorage');
        window.location.reload();
      };
    }
  }, []);

  // Fetch movies from the API
  useEffect(() => {
    const fetchMovies = async () => {
      try {
        setLoading(true);
        // Get token from localStorage - try multiple possible keys
        const token = typeof window !== 'undefined' ?
          (localStorage.getItem('token') ||
            localStorage.getItem('jwt_token') ||
            localStorage.getItem('auth_token')) : null;

        if (!token) {
          console.warn('No authentication token found in any storage key');
          setError('Authentication required');
          return;
        }

        const response = await axios.get<ApiResponse>(`/movies?page=${page}&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.movies) {
          console.log('Movies API response:', response.data);

          // Check if we have thumbnails in the response
          const hasValidThumbnails = response.data.movies.some(item => item.video_thumbnail);
          console.log('Has valid thumbnails:', hasValidThumbnails);

          if (response.data.movies.length > 0) {
            console.log('Sample movie item:', JSON.stringify(response.data.movies[0]));
          }

          // Process the movies to ensure thumbnails are properly formatted
          const processedMovies = response.data.movies.map((movie, index) => {
            // Add YouTube thumbnail if missing and it's a YouTube video
            if (!movie.video_thumbnail && movie.video_url && movie.video_url.includes('youtube')) {
              const videoId = getYoutubeId(movie.video_url);
              if (videoId) {
                movie.video_thumbnail = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
              }
            }
            
            // Ensure video_url is properly formatted
            if (movie.video_url) {
              // Make sure the URL has a protocol
              if (!movie.video_url.startsWith('http')) {
                movie.video_url = `https://${movie.video_url}`;
              }
            }

            return movie;
          });

          if (page === 1) {
            // For first page, just set the movies directly
            setMovies(processedMovies);
            setNewItemsCount(0); // Reset new items count for first page
            console.log(`Set initial ${processedMovies.length} movies`);

            // Fetch like status for initial movies
            fetchLikeStatus(processedMovies.map((m: MovieVideo) => m.video_id));
          } else {
            // For subsequent pages, append to existing movies
            setMovies(prev => {
              const newMovies = [...prev, ...processedMovies];
              console.log(`Added ${processedMovies.length} new movies, total now: ${newMovies.length}`);
              return newMovies;
            });
            // Set the count of newly loaded items
            setNewItemsCount(processedMovies.length);

            // Fetch like status for new movies
            fetchLikeStatus(processedMovies.map((m: MovieVideo) => m.video_id));
          }

          setHasMore(response.data.next_page);
          console.log(`Has more pages: ${response.data.next_page}`);
          console.log(`Current page: ${response.data.current_page}, Total items: ${response.data.total_count}`);
        } else {
          console.warn('Unexpected API response format:', response.data);
          setError('Failed to load movies');
        }
      } catch (err) {
        console.error('Error fetching movies:', err);
        setError('Failed to load movies');
      } finally {
        setLoading(false);
      }
    };

    fetchMovies();
  }, [page]);

  // Reference for the last item in the list
  const lastMovieRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Reset new items notification after a few seconds
  useEffect(() => {
    if (newItemsCount > 0) {
      const timer = setTimeout(() => {
        setNewItemsCount(0);
      }, 5000); // Reset after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [newItemsCount]);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last movie is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.1, rootMargin: '0px 0px 500px 0px' } // Load when item is just 10% visible or 500px before it comes into view
    );

    // Get the last item element
    const lastElement = lastMovieRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, movies.length]);

  // Get appropriate image source for a movie
  const getImageSource = (video: MovieVideo): string => {
    // If we have a thumbnail, use it and log it for debugging
    if (video.video_thumbnail) {
      // console.log(`Using thumbnail from API: ${video.video_thumbnail}`);
      return video.video_thumbnail;
    }

    // Try to extract YouTube thumbnail if it's a YouTube video
    if (video.video_url && video.video_url.includes('youtube')) {
      const videoId = getYoutubeId(video.video_url);
      if (videoId) {
        const youtubeThumbnail = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        // console.log(`Using YouTube thumbnail: ${youtubeThumbnail}`);
        return youtubeThumbnail;
      }
    }

    // Default fallback - use a local placeholder image
    console.log('Using fallback placeholder image for video:', video.video_name);
    return '/pics/placeholder.svg';
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Handle different YouTube URL formats
    if (url.includes('youtu.be/')) {
      // Short URL format: https://youtu.be/VIDEO_ID
      const id = url.split('youtu.be/')[1]?.split(/[?&]/)[0];
      return id || '';
    } else if (url.includes('youtube.com/watch')) {
      // Standard format: https://www.youtube.com/watch?v=VIDEO_ID
      const params = new URLSearchParams(url.split('?')[1] || '');
      return params.get('v') || '';
    } else if (url.includes('youtube.com/embed/')) {
      // Embed format: https://www.youtube.com/embed/VIDEO_ID
      return url.split('embed/')[1]?.split(/[?&]/)[0] || '';
    }

    // Fallback to regex for other formats
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2]?.length === 11) ? match[2] : '';
  };

  // Process image URL to handle cloudfront URLs properly
  const processImageUrl = (url: string): string => {
    if (!url) return '/pics/placeholder.svg';

    // If it's already a local URL, return as is
    if (url.startsWith('/')) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
      // console.log('Using CDN URL for movie thumbnail:', url);
      return url;
    }

    // For YouTube thumbnails
    if (url.includes('img.youtube.com')) {
      // console.log('Using YouTube thumbnail URL:', url);
      return url;
    }

    // Return the URL as is for other external URLs
    return url;
  };

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string) => {
    // If we have a userId, navigate to that specific user's profile using the dynamic route
    if (userId) {
      router.push(`/profile/${userId}`);
    } else if (username) {
      // If we only have a username but no ID, create a temporary ID based on the username
      const tempId = username.toLowerCase().replace(/\s+/g, '-');
      router.push(`/profile/${tempId}`);
    } else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  const toggleLike = async (videoId: string) => {
    console.log('🎥 Movie like button clicked for ID:', videoId);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.warn('🎥 No authentication token found');
        return;
      }

      // Optimistically update UI
      const isCurrentlyLiked = likedVideos.includes(videoId);
      console.log('🎥 Current like status:', isCurrentlyLiked);
      setLikedVideos((prev) =>
        isCurrentlyLiked
          ? prev.filter((id) => id !== videoId)
          : [...prev, videoId]
      );

      // Make API call to Next.js API routes
      const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';
      console.log('🎥 Making API call to:', endpoint);

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content_id: videoId,
          content_type: 'video'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🎥 API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('🎥 API Success Response:', responseData);

      // Update localStorage to persist like status
      const likedMoviesData = localStorage.getItem('likedMovies');
      const likedMoviesArray = likedMoviesData ? JSON.parse(likedMoviesData) : [];

      if (isCurrentlyLiked) {
        // Remove from liked movies
        const updatedLikedMovies = likedMoviesArray.filter((id: string) => id !== videoId);
        localStorage.setItem('likedMovies', JSON.stringify(updatedLikedMovies));
      } else {
        // Add to liked movies
        if (!likedMoviesArray.includes(videoId)) {
          likedMoviesArray.push(videoId);
          localStorage.setItem('likedMovies', JSON.stringify(likedMoviesArray));
        }
      }

      console.log(`${isCurrentlyLiked ? 'Unliked' : 'Liked'} movie: ${videoId}`);
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert optimistic update on error
      const wasLiked = likedVideos.includes(videoId);
      setLikedVideos((prev) =>
        wasLiked
          ? prev.filter((id) => id !== videoId)
          : [...prev, videoId]
      );
    }
  };

  // Fetch like status for movies from localStorage
  const fetchLikeStatus = async (movieIds: string[]) => {
    try {
      if (movieIds.length === 0) return;

      console.log('🎥 Loading like status for movies:', movieIds);

      // Load liked movies from localStorage
      const likedMoviesData = localStorage.getItem('likedMovies');
      if (likedMoviesData) {
        try {
          const likedArray = JSON.parse(likedMoviesData);
          if (Array.isArray(likedArray)) {
            setLikedVideos(likedArray);
            console.log('🎥 Loaded liked movies:', likedArray);
          } else {
            console.log('🎥 Invalid liked movies data format');
            setLikedVideos([]);
          }
        } catch (parseError) {
          console.error('🎥 Error parsing liked movies:', parseError);
          setLikedVideos([]);
        }
      } else {
        console.log('🎥 No liked movies found in localStorage');
        setLikedVideos([]);
      }
    } catch (error) {
      console.error('🎥 Error in fetchLikeStatus:', error);
      setLikedVideos([]);
    }
  };

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Wedding Videos</h1>
              <div className="text-sm text-gray-500">
                Showing {movies.length} items | Page {page}
              </div>
            </div>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading wedding videos</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {movies.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                {movies.map((video, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === movies.length - 1;

                  // Determine if this is a newly loaded item
                  const isNewlyLoaded = page > 1 && index >= movies.length - newItemsCount;

                  return (
                    <div
                      key={`${video.video_id}-${index}`}
                      // Apply ref to the last item for intersection observer
                      ref={isLastItem ? lastMovieRef : null}
                      className={`flex-shrink-0 border-b pb-8 ${isNewlyLoaded ? 'border-green-500 animate-pulse' : 'border-gray-200'}`}
                    >
                      {/* Section 1 - User Info */}
                      <div className="flex justify-between items-center p-2 mb-4">
                        <div className="flex items-center gap-3">
                          <div className="cursor-pointer" onClick={() => navigateToUserProfile(video.user_id, video.user_name)}>
                            <UserAvatar username={video.user_name || "user"} size="sm" />
                          </div>
                          <span
                            className="font-semibold cursor-pointer hover:underline"
                            onClick={() => navigateToUserProfile(video.user_id, video.user_name)}
                          >
                            {video.user_name || "user"}
                          </span>
                        </div>
                        <button>•••</button>
                      </div>

                      {/* Section 2 - Video */}
                      <div className="relative w-full aspect-video rounded-lg overflow-hidden border border-gray-200 mb-4">
                        {/* Video Thumbnail */}
                        <div className="relative w-full h-full rounded-[10px] overflow-hidden">
                          <Image
                            src={processImageUrl(getImageSource(video))}
                            alt={video.video_name || "Wedding Video"}
                            fill
                            sizes="(max-width: 768px) 100vw, 800px"
                            className="object-cover"
                            priority={index < 2} // Only prioritize the first two images
                            unoptimized={true} // Skip optimization for all images to avoid issues
                            placeholder="empty"
                            onLoadingComplete={(result) => {
                              if (result.naturalWidth === 0) {
                                // The image didn't load properly
                                const imgElement = result as unknown as HTMLImageElement;
                                if (imgElement && imgElement.src) {
                                  imgElement.src = '/pics/placeholder.svg';
                                }
                              }
                            }}
                            onError={(e) => {
                              console.error(`Failed to load image for video: ${video.video_name}`);
                              // Use placeholder as fallback
                              const imgElement = e.target as HTMLImageElement;
                              if (imgElement) {
                                imgElement.src = '/pics/placeholder.svg';
                              }
                            }}
                          />

                          {/* Like button on thumbnail */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleLike(video.video_id);
                            }}
                            className={`absolute top-4 right-4 z-10 flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 ${
                              likedVideos.includes(video.video_id)
                                ? 'bg-red-100 hover:bg-red-200'
                                : 'bg-black/20 hover:bg-black/40'
                            }`}
                            title={likedVideos.includes(video.video_id) ? 'Unlike' : 'Like'}
                          >
                            {likedVideos.includes(video.video_id) ? (
                              <MdFavorite size={24} color="#B31B1E" />
                            ) : (
                              <MdFavoriteBorder size={24} color="white" />
                            )}
                          </button>

                          {/* Play Button Overlay */}
                          <div
                            className="absolute inset-0 flex items-center justify-center bg-black/20 hover:bg-black/30 transition-colors cursor-pointer"
                            onClick={() => {
                              // Navigate to the movie detail page
                              if (video.video_id) {
                                window.location.href = `/home/<USER>/${video.video_id}`;
                              }
                            }}
                          >
                            <div className="w-14 h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="28"
                                height="28"
                                viewBox="0 0 24 24"
                                fill="white"
                              >
                                <path d="M8 5v14l11-7z" />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Section 3 - Interactions */}
                      <div className="flex flex-col gap-3">
                        <div className="flex gap-4 items-center">
                          <button
                            onClick={() => toggleLike(video.video_id)}
                            className={`flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 ${
                              likedVideos.includes(video.video_id)
                                ? 'bg-red-100 hover:bg-red-200'
                                : 'bg-gray-100 hover:bg-gray-200'
                            }`}
                            title={likedVideos.includes(video.video_id) ? 'Unlike' : 'Like'}
                          >
                            {likedVideos.includes(video.video_id) ? (
                              <MdFavorite size={24} color="#B31B1E" />
                            ) : (
                              <MdFavoriteBorder size={24} color="#666" />
                            )}
                          </button>
                          <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200">
                            <MdOutlineModeComment size={24} color="#666" />
                          </button>
                          <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200">
                            <MdOutlineShare size={24} color="#666" />
                          </button>
                        </div>

                        <div className="flex gap-4 text-sm text-gray-600">
                          <span className={likedVideos.includes(video.video_id) ? "text-red-600" : ""}>
                            {video.video_likes ? `${(video.video_likes / 1000).toFixed(1)}K` : '0'} likes
                          </span>
                          <span>{video.video_views ? `${(video.video_views / 1000).toFixed(1)}K` : '0'} views</span>
                        </div>

                        {/* Vendor Details */}
                        <VideoVendorDetails videoId={video.video_id} isVerified={false} />

                        <div className="text-sm">
                          <span>{video.video_description || video.video_name}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* New items loaded notification */}
            {!loading && newItemsCount > 0 && (
              <div className="py-3 text-center text-green-600 font-medium animate-pulse">
                {newItemsCount} new items loaded! Look for the green borders.
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && movies.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more videos to load</div>
            )}

            {/* No content state */}
            {!loading && movies.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No wedding videos available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
