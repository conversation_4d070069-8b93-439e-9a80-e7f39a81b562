import axios from 'axios';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || '';

function getAuthHeaders() {
  const token =
    typeof window !== 'undefined' &&
    (localStorage.getItem('token') || sessionStorage.getItem('token'));
  return token ? { Authorization: `Bearer ${token}` } : {};
}

export async function saveContentToMyWedding({ 
  user_id,
  content_id, 
  content_type,
  title = '' 
}: {
  user_id: string;
  content_id: string;
  content_type: string;
  title?: string;
}) {
  return axios.post(
    `${API_BASE}/save-to-my-wedding`,
    { user_id, content_id, content_type, title },
    { headers: { ...getAuthHeaders(), 'Content-Type': 'application/json' } }
  );
}

export async function removeContentFromMyWedding({ 
  user_id,
  content_id 
}: {
  user_id: string;
  content_id: string;
}) {
  return axios.delete(
    `${API_BASE}/remove-from-my-wedding`,
    {
      data: { user_id, content_id },
      headers: { ...getAuthHeaders(), 'Content-Type': 'application/json' }
    }
  );
}

export async function checkContentSavedStatus({ 
  user_id,
  content_id 
}: {
  user_id: string;
  content_id: string;
}) {
  return axios.get(`${API_BASE}/check-video-saved`, {
    params: { user_id, content_id },
    headers: getAuthHeaders(),
  });
}

export async function getMyWeddingContent({ 
  user_id,
  content_type, 
  page = 1, 
  limit = 20 
}: {
  user_id: string;
  content_type: string;
  page?: number;
  limit?: number;
}) {
  return axios.get(`${API_BASE}/my-wedding-videos`, {
    params: { user_id, content_type, page, limit },
    headers: getAuthHeaders(),
  });
} 