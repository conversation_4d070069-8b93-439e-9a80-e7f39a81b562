import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Image from 'next/image';

// Define API base URL - use environment variable or fallback to hardcoded value
// Make sure the URL doesn't have a trailing slash
const API_BASE_URL = (process.env.NEXT_PUBLIC_API_URL || 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub').replace(/\/$/, '');

interface VendorDetail {
  name: string;
  contact: string;
}

interface VendorDetails {
  venue: VendorDetail;
  photographer: <PERSON><PERSON>orDeta<PERSON>;
  makeup_artist: VendorDetail;
  decoration: VendorDetail;
  caterer: VendorDetail;
  additional_vendors: any;
}

interface VendorDetailsProps {
  videoId: string;
  token: string;
  isBlurred?: boolean;
  onUnlock?: () => void;
}

const VendorDetails: React.FC<VendorDetailsProps> = ({
  videoId,
  token,
  isBlurred = true,
  onUnlock
}) => {
  const [vendorDetails, setVendorDetails] = useState<VendorDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendorDetails = async () => {
      try {
        setLoading(true);
        console.log(`Fetching vendor details for video ID: ${videoId}`);
        console.log(`API URL: ${API_BASE_URL}/vendor-details/${videoId}`);

        const response = await axios.get(`${API_BASE_URL}/vendor-details/${videoId}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        console.log('API Response:', response.data);

        // The backend returns vendor_details in the response
        if (response.data && response.data.vendor_details) {
          console.log('Vendor details found:', response.data.vendor_details);
          setVendorDetails(response.data.vendor_details);
        } else {
          console.warn('No vendor details found in response:', response.data);
          setError('No vendor details available for this video');
        }
      } catch (err) {
        console.error('Error fetching vendor details:', err);

        // Log more detailed error information
        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error('Error response data:', err.response.data);
          console.error('Error response status:', err.response.status);
          console.error('Error response headers:', err.response.headers);
          setError(`Failed to load vendor details: ${err.response.status} ${err.response.statusText}`);
        } else if (err.request) {
          // The request was made but no response was received
          console.error('Error request:', err.request);
          setError('Failed to load vendor details: No response received from server');
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error('Error message:', err.message);
          setError(`Failed to load vendor details: ${err.message}`);
        }

        // Log the error for debugging
        console.error('Failed to fetch vendor details from API');
        // Keep the error message to show to the user
      } finally {
        setLoading(false);
      }
    };

    if (videoId && token) {
      fetchVendorDetails();
    } else {
      console.warn('Missing videoId or token for vendor details fetch');
      console.log('videoId:', videoId);
      console.log('token available:', !!token);
    }
  }, [videoId, token]);

  if (loading) {
    return (
      <div className="mt-4 animate-pulse">
        <div className="flex items-center mb-2">
          <div className="h-5 bg-gray-200 rounded w-24 mr-2"></div>
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded w-full"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-4 text-sm text-gray-500">
        <p>{error}</p>
      </div>
    );
  }

  if (!vendorDetails) {
    // If we're still loading or there's an error, show appropriate message
    // Otherwise, show a generic "no details" message
    return (
      <div className="mt-4 text-sm text-gray-500">
        <p>{loading ? 'Loading vendor details...' : error || 'No vendor details available'}</p>
      </div>
    );
  }

  // Check if any vendor details are actually available
  const hasVendorDetails = (
    (vendorDetails.venue && vendorDetails.venue.name) ||
    (vendorDetails.photographer && vendorDetails.photographer.name) ||
    (vendorDetails.makeup_artist && vendorDetails.makeup_artist.name) ||
    (vendorDetails.decoration && vendorDetails.decoration.name) ||
    (vendorDetails.caterer && vendorDetails.caterer.name) ||
    (vendorDetails.additional_vendors && Object.keys(vendorDetails.additional_vendors).length > 0)
  );

  if (!hasVendorDetails) {
    return (
      <div className="mt-4 text-sm text-gray-500">
        <p>No vendor details available for this video</p>
      </div>
    );
  }

  return (
    <div className="mt-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-base font-semibold text-gray-800">Vendor Details</h3>
        {isBlurred && onUnlock && (
          <button
            onClick={onUnlock}
            className="text-sm bg-red-600 hover:bg-red-700 text-white px-4 py-1.5 rounded-md flex items-center shadow-sm transition-all duration-200 hover:shadow-md"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            Unlock Details
          </button>
        )}
      </div>

      {isBlurred && (
        <div className="mb-3 p-2 bg-gray-50 border border-gray-200 rounded-md text-xs text-gray-600">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Vendor details are hidden. Click "Unlock Details" to view.</span>
          </div>
        </div>
      )}

      <div className="space-y-2 text-sm">
        {vendorDetails.venue && vendorDetails.venue.name && (
          <div className="flex items-start">
            <div className="w-24 font-medium text-gray-700">Venue:</div>
            <div className="flex-1">
              <div className={isBlurred ? 'blur-sm select-none' : ''}>{vendorDetails.venue.name}</div>
              <div className={`text-gray-500 ${isBlurred ? 'blur-sm select-none' : ''}`}>{vendorDetails.venue.contact}</div>
            </div>
          </div>
        )}

        {vendorDetails.photographer && vendorDetails.photographer.name && (
          <div className="flex items-start">
            <div className="w-24 font-medium text-gray-700">Photographer:</div>
            <div className="flex-1">
              <div className={isBlurred ? 'blur-sm select-none' : ''}>{vendorDetails.photographer.name}</div>
              <div className={`text-gray-500 ${isBlurred ? 'blur-sm select-none' : ''}`}>{vendorDetails.photographer.contact}</div>
            </div>
          </div>
        )}

        {vendorDetails.makeup_artist && vendorDetails.makeup_artist.name && (
          <div className="flex items-start">
            <div className="w-24 font-medium text-gray-700">Makeup Artist:</div>
            <div className="flex-1">
              <div className={isBlurred ? 'blur-sm select-none' : ''}>{vendorDetails.makeup_artist.name}</div>
              <div className={`text-gray-500 ${isBlurred ? 'blur-sm select-none' : ''}`}>{vendorDetails.makeup_artist.contact}</div>
            </div>
          </div>
        )}

        {vendorDetails.decoration && vendorDetails.decoration.name && (
          <div className="flex items-start">
            <div className="w-24 font-medium text-gray-700">Decoration:</div>
            <div className="flex-1">
              <div className={isBlurred ? 'blur-sm select-none' : ''}>{vendorDetails.decoration.name}</div>
              <div className={`text-gray-500 ${isBlurred ? 'blur-sm select-none' : ''}`}>{vendorDetails.decoration.contact}</div>
            </div>
          </div>
        )}

        {vendorDetails.caterer && vendorDetails.caterer.name && (
          <div className="flex items-start">
            <div className="w-24 font-medium text-gray-700">Caterer:</div>
            <div className="flex-1">
              <div className={isBlurred ? 'blur-sm select-none' : ''}>{vendorDetails.caterer.name}</div>
              <div className={`text-gray-500 ${isBlurred ? 'blur-sm select-none' : ''}`}>{vendorDetails.caterer.contact}</div>
            </div>
          </div>
        )}

        {vendorDetails.additional_vendors && Object.keys(vendorDetails.additional_vendors).length > 0 && (
          <div className="flex items-start">
            <div className="w-24 font-medium text-gray-700">Additional:</div>
            <div className="flex-1">
              <div className={isBlurred ? 'blur-sm select-none' : ''}>{JSON.stringify(vendorDetails.additional_vendors)}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VendorDetails;
