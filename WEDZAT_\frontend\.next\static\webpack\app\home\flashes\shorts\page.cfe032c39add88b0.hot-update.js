"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/shorts/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/shorts/page.tsx":
/*!******************************************!*\
  !*** ./app/home/<USER>/shorts/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashShortsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var _components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/HomeDashboard/UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/FlashVendorDetails */ \"(app-pages-browser)/./components/FlashVendorDetails.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../components/HomeDashboard/Navigation */ \"(app-pages-browser)/./components/HomeDashboard/Navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Create a Client component that uses useSearchParams\nfunction FlashShortsContent() {\n    var _flashes_currentIndex, _flashes_currentIndex1, _flashes_currentIndex2, _flashes_currentIndex3;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialIndex = parseInt((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"index\")) || \"0\");\n    const [flashes, setFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialIndex);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedFlashes, setLikedFlashes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [leftSidebarExpanded, setLeftSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightSidebarExpanded, setRightSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showVendorDetails, setShowVendorDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [admiringUsers, setAdmiringUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // Track admiring state\n    const [videoLoading, setVideoLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Refs for video elements\n    const videoRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const youtubeTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // User avatar placeholders - using placeholder.svg which exists\n    const userAvatarPlaceholders = [\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\",\n        \"/pics/placeholder.svg\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>setIsClient(true)\n    }[\"FlashShortsContent.useEffect\"], []);\n    // Load admiring state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!isClient) return;\n            try {\n                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                const admiredUsers = JSON.parse(admiredUsersJson);\n                setAdmiringUsers(admiredUsers);\n            } catch (error) {\n                console.error('Error loading admired users from localStorage:', error);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient\n    ]);\n    // Fetch flashes from the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const fetchFlashes = {\n                \"FlashShortsContent.useEffect.fetchFlashes\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Get token from localStorage\n                        const token = localStorage.getItem(\"token\");\n                        if (!token) {\n                            console.warn(\"No authentication token found\");\n                            setError(\"Authentication required\");\n                            return;\n                        }\n                        const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/flashes?page=\".concat(page, \"&limit=10\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.data && response.data.flashes) {\n                            console.log(\"Flashes API response:\", response.data);\n                            if (page === 1) {\n                                setFlashes(response.data.flashes);\n                                // Initialize loading state for all videos\n                                const initialLoadingState = {};\n                                response.data.flashes.forEach({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                        initialLoadingState[flash.video_id] = true;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                setVideoLoading(initialLoadingState);\n                                // Fetch like status for initial flashes\n                                fetchLikeStatus(response.data.flashes.map({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (f)=>f.video_id\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]), 'video');\n                            } else {\n                                setFlashes({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>[\n                                            ...prev,\n                                            ...response.data.flashes\n                                        ]\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Initialize loading state for new videos\n                                setVideoLoading({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (prev)=>{\n                                        const newState = {\n                                            ...prev\n                                        };\n                                        response.data.flashes.forEach({\n                                            \"FlashShortsContent.useEffect.fetchFlashes\": (flash)=>{\n                                                newState[flash.video_id] = true;\n                                            }\n                                        }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                        return newState;\n                                    }\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]);\n                                // Fetch like status for new flashes\n                                fetchLikeStatus(response.data.flashes.map({\n                                    \"FlashShortsContent.useEffect.fetchFlashes\": (f)=>f.video_id\n                                }[\"FlashShortsContent.useEffect.fetchFlashes\"]), 'video');\n                            }\n                            setHasMore(response.data.next_page);\n                        } else {\n                            console.warn(\"Unexpected API response format:\", response.data);\n                            setError(\"Failed to load flashes\");\n                        }\n                    } catch (err) {\n                        console.error(\"Error fetching flashes:\", err);\n                        setError(\"Failed to load flashes\");\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.fetchFlashes\"];\n            fetchFlashes();\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        page\n    ]);\n    // Load more flashes when reaching the end\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (currentIndex >= flashes.length - 2 && hasMore && !loading) {\n                setPage({\n                    \"FlashShortsContent.useEffect\": (prevPage)=>prevPage + 1\n                }[\"FlashShortsContent.useEffect\"]);\n            }\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length,\n        hasMore,\n        loading\n    ]);\n    // Handle video playback when current index changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (flashes.length === 0 || !isClient) return;\n            // Clear any existing YouTube timer\n            if (youtubeTimerRef.current) {\n                clearTimeout(youtubeTimerRef.current);\n                youtubeTimerRef.current = null;\n            }\n            // Pause all videos\n            Object.values(videoRefs.current).forEach({\n                \"FlashShortsContent.useEffect\": (videoEl)=>{\n                    if (videoEl && !videoEl.paused) {\n                        videoEl.pause();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect\"]);\n            const currentFlash = flashes[currentIndex];\n            if (!currentFlash) return;\n            // Handle YouTube videos with timer-based auto-advance\n            if (isYoutubeVideo(currentFlash) && !isPaused[currentFlash.video_id]) {\n                // Set a timer for YouTube videos (assuming average duration of 30 seconds)\n                // In a real implementation, you might want to use YouTube API to get actual duration\n                youtubeTimerRef.current = setTimeout({\n                    \"FlashShortsContent.useEffect\": ()=>{\n                        console.log(\"YouTube video timer ended: \".concat(currentFlash.video_id, \", auto-advancing to next flash\"));\n                        if (!isPaused[currentFlash.video_id]) {\n                            navigateToNext();\n                        }\n                    }\n                }[\"FlashShortsContent.useEffect\"], 30000); // 30 seconds default duration for YouTube videos\n            } else {\n                // Play current video if not manually paused (for non-YouTube videos)\n                const currentVideoId = currentFlash.video_id;\n                const currentVideo = videoRefs.current[currentVideoId];\n                if (currentVideo && !isPaused[currentVideoId]) {\n                    const playPromise = currentVideo.play();\n                    if (playPromise !== undefined) {\n                        playPromise.catch({\n                            \"FlashShortsContent.useEffect\": (error)=>{\n                                console.error(\"Error playing video:\", error);\n                            }\n                        }[\"FlashShortsContent.useEffect\"]);\n                    }\n                }\n            }\n            // Cleanup function\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes,\n        isClient,\n        isPaused\n    ]);\n    // Handle keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"FlashShortsContent.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") {\n                        navigateToPrevious();\n                    } else if (e.key === \"ArrowDown\" || e.key === \"ArrowRight\") {\n                        navigateToNext();\n                    } else if (e.key === \"Escape\") {\n                        router.push(\"/home/<USER>");\n                    } else if (e.key === \" \" || e.key === \"Spacebar\") {\n                        var _flashes_currentIndex;\n                        // Toggle play/pause on spacebar\n                        togglePlayPause((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleKeyDown\"];\n            window.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle touch events for swiping\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            let startY = 0;\n            // let startTime = 0; // Uncomment if needed for timing-based gestures\n            const handleTouchStart = {\n                \"FlashShortsContent.useEffect.handleTouchStart\": (e)=>{\n                    startY = e.touches[0].clientY;\n                // startTime = Date.now(); // Uncomment if needed for timing-based gestures\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchStart\"];\n            const handleTouchEnd = {\n                \"FlashShortsContent.useEffect.handleTouchEnd\": (e)=>{\n                    const deltaY = e.changedTouches[0].clientY - startY;\n                    // const deltaTime = Date.now() - startTime; // Uncomment if needed for timing-based gestures\n                    // Make touch more responsive by reducing the threshold\n                    if (Math.abs(deltaY) > 30) {\n                        if (deltaY > 0) {\n                            // Swipe down - go to previous\n                            navigateToPrevious();\n                        } else {\n                            // Swipe up - go to next\n                            navigateToNext();\n                        }\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchEnd\"];\n            // Add touchmove handler for more responsive scrolling\n            let lastY = 0;\n            let touchMoveThrottle = false;\n            const handleTouchMove = {\n                \"FlashShortsContent.useEffect.handleTouchMove\": (e)=>{\n                    const currentY = e.touches[0].clientY;\n                    // Only process every few pixels of movement to avoid too many updates\n                    if (!touchMoveThrottle && Math.abs(currentY - lastY) > 20) {\n                        lastY = currentY;\n                        touchMoveThrottle = true;\n                        // Schedule reset of throttle\n                        setTimeout({\n                            \"FlashShortsContent.useEffect.handleTouchMove\": ()=>{\n                                touchMoveThrottle = false;\n                            }\n                        }[\"FlashShortsContent.useEffect.handleTouchMove\"], 100);\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleTouchMove\"];\n            const container = containerRef.current;\n            container.addEventListener(\"touchstart\", handleTouchStart);\n            container.addEventListener(\"touchmove\", handleTouchMove, {\n                passive: true\n            });\n            container.addEventListener(\"touchend\", handleTouchEnd);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"touchstart\", handleTouchStart);\n                    container.removeEventListener(\"touchmove\", handleTouchMove);\n                    container.removeEventListener(\"touchend\", handleTouchEnd);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    // Handle wheel events for touchpad scrolling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            if (!containerRef.current || !isClient) return;\n            const handleWheel = {\n                \"FlashShortsContent.useEffect.handleWheel\": (e)=>{\n                    // Debounce the wheel event to prevent too many navigations\n                    if (e.deltaY > 50) {\n                        // Scroll down - go to next\n                        navigateToNext();\n                    } else if (e.deltaY < -50) {\n                        // Scroll up - go to previous\n                        navigateToPrevious();\n                    }\n                }\n            }[\"FlashShortsContent.useEffect.handleWheel\"];\n            const container = containerRef.current;\n            container.addEventListener(\"wheel\", handleWheel, {\n                passive: true\n            });\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    container.removeEventListener(\"wheel\", handleWheel);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], [\n        isClient,\n        currentIndex,\n        flashes.length\n    ]);\n    const navigateToNext = ()=>{\n        if (currentIndex < flashes.length - 1) {\n            setCurrentIndex((prevIndex)=>prevIndex + 1);\n        } else {\n            // When reaching the last video, loop back to the first one\n            setCurrentIndex(0);\n        }\n    };\n    const navigateToPrevious = ()=>{\n        if (currentIndex > 0) {\n            setCurrentIndex((prevIndex)=>prevIndex - 1);\n        }\n    };\n    // Auto-advance to next flash when current video ends\n    const handleVideoEnded = (videoId)=>{\n        console.log(\"Video ended: \".concat(videoId, \", auto-advancing to next flash\"));\n        // Only auto-advance if the video wasn't manually paused\n        if (!isPaused[videoId]) {\n            navigateToNext();\n        }\n    };\n    const toggleLike = async (flashId)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.warn('No authentication token found');\n                return;\n            }\n            // Optimistically update UI\n            const isCurrentlyLiked = likedFlashes.has(flashId);\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (isCurrentlyLiked) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n            // Make API call to Next.js API routes\n            const endpoint = isCurrentlyLiked ? '/api/unlike' : '/api/like';\n            const response = await fetch(endpoint, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content_id: flashId,\n                    content_type: 'video'\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            console.log(\"\".concat(isCurrentlyLiked ? 'Unliked' : 'Liked', \" flash: \").concat(flashId));\n        } catch (error) {\n            console.error('Error toggling like:', error);\n            // Revert optimistic update on error\n            setLikedFlashes((prev)=>{\n                const newLiked = new Set(prev);\n                if (likedFlashes.has(flashId)) {\n                    newLiked.delete(flashId);\n                } else {\n                    newLiked.add(flashId);\n                }\n                return newLiked;\n            });\n        }\n    };\n    // Fetch like status for content\n    const fetchLikeStatus = async (contentIds, contentType)=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token || contentIds.length === 0) return;\n            // For now, we'll check each content individually\n            // In a real app, you might want a batch API\n            const likedIds = new Set();\n            for (const contentId of contentIds){\n                try {\n                    const response = await fetch(\"/api/like-status\", {\n                        method: 'POST',\n                        headers: {\n                            'Authorization': \"Bearer \".concat(token),\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            content_id: contentId,\n                            content_type: contentType\n                        })\n                    });\n                    if (response.ok) {\n                        const data = await response.json();\n                        if (data.liked) {\n                            likedIds.add(contentId);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error checking like status for \".concat(contentId, \":\"), error);\n                }\n            }\n            setLikedFlashes(likedIds);\n        } catch (error) {\n            console.error('Error fetching like status:', error);\n        }\n    };\n    const togglePlayPause = (videoId)=>{\n        if (!videoId) return;\n        const currentFlash = flashes.find((flash)=>flash.video_id === videoId);\n        if (!currentFlash) return;\n        setIsPaused((prev)=>{\n            const newState = {\n                ...prev\n            };\n            newState[videoId] = !prev[videoId];\n            if (isYoutubeVideo(currentFlash)) {\n                // Handle YouTube video pause/play\n                if (newState[videoId]) {\n                    // Paused - clear the timer\n                    if (youtubeTimerRef.current) {\n                        clearTimeout(youtubeTimerRef.current);\n                        youtubeTimerRef.current = null;\n                    }\n                } else {\n                    // Resumed - restart the timer\n                    youtubeTimerRef.current = setTimeout(()=>{\n                        console.log(\"YouTube video timer ended: \".concat(videoId, \", auto-advancing to next flash\"));\n                        if (!newState[videoId]) {\n                            navigateToNext();\n                        }\n                    }, 30000); // 30 seconds default duration\n                }\n            } else {\n                // Handle regular video pause/play\n                const videoEl = videoRefs.current[videoId];\n                if (videoEl) {\n                    if (newState[videoId]) {\n                        videoEl.pause();\n                    } else {\n                        videoEl.play().catch((err)=>console.error(\"Error playing video:\", err));\n                    }\n                }\n            }\n            return newState;\n        });\n    };\n    // Extract YouTube video ID from URL\n    const getYoutubeId = (url)=>{\n        if (!url) return \"\";\n        // If it's already just an ID, return it\n        if (url.length < 20 && !url.includes(\"/\")) return url;\n        // Try to extract ID from YouTube URL\n        const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;\n        const match = url.match(regExp);\n        return match && match[2].length === 11 ? match[2] : \"\";\n    };\n    // Get appropriate image source for a flash\n    const getImageSource = (flash)=>{\n        // If we have a thumbnail, use it\n        if (flash.video_thumbnail) {\n            return flash.video_thumbnail;\n        }\n        // If it's a YouTube video, use the YouTube thumbnail\n        if (flash.video_url && flash.video_url.includes(\"youtube\")) {\n            const videoId = getYoutubeId(flash.video_url);\n            if (videoId) {\n                return \"https://img.youtube.com/vi/\".concat(videoId, \"/hqdefault.jpg\");\n            }\n        }\n        // Default fallback - use a local placeholder image\n        return \"/pics/placeholder.svg\";\n    };\n    // Check if the video is from YouTube\n    const isYoutubeVideo = (flash)=>{\n        return typeof flash.video_url === \"string\" && flash.video_url.includes(\"youtube\");\n    };\n    // Format numbers for display (e.g., 1.2K)\n    const formatNumber = (num)=>{\n        if (!num) return \"0\";\n        if (num >= 1000000) {\n            return \"\".concat((num / 1000000).toFixed(1), \"M\");\n        } else if (num >= 1000) {\n            return \"\".concat((num / 1000).toFixed(1), \"K\");\n        }\n        return num.toString();\n    };\n    // Add custom CSS for styling and responsiveness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashShortsContent.useEffect\": ()=>{\n            // Add a style tag for styling and responsiveness\n            const style = document.createElement(\"style\");\n            style.innerHTML = \"\\n      .shorts-page {\\n        background-color: #f8f8f8;\\n      }\\n\\n      .shorts-container {\\n        background-color: #000;\\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\\n        border-radius: 12px;\\n        overflow: hidden;\\n        position: relative;\\n      }\\n\\n      .shorts-video {\\n        width: 100%;\\n        height: 100%;\\n        object-fit: cover;\\n        background-color: #000;\\n      }\\n\\n      .shorts-controls {\\n        position: absolute;\\n        right: 8px;\\n        bottom: 80px;\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        gap: 16px;\\n        z-index: 20;\\n      }\\n\\n      .shorts-info {\\n        position: absolute;\\n        bottom: 0;\\n        left: 0;\\n        right: 0;\\n        padding: 16px;\\n        background: linear-gradient(transparent, rgba(0,0,0,0.8));\\n        z-index: 10;\\n      }\\n\\n      /* Fixed layout styles for proper centering */\\n      .layout-container {\\n        display: flex;\\n        width: 100%;\\n      }\\n\\n      .left-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .left-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .right-sidebar {\\n        width: 80px;\\n        min-width: 80px;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      .right-sidebar.expanded {\\n        width: 192px;\\n        min-width: 192px;\\n      }\\n\\n      .main-content {\\n        flex: 1;\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        transition: all 300ms ease-in-out;\\n      }\\n\\n      /* Mobile responsive styles */\\n      @media (max-width: 768px) {\\n        .shorts-page {\\n          background-color: #000;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n        }\\n\\n        .shorts-container {\\n          width: 100vw !important;\\n          max-width: none !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          max-height: none !important;\\n          border-radius: 0 !important;\\n          border: none !important;\\n          margin: 0 !important;\\n          padding-bottom: 20px !important;\\n        }\\n\\n        .mobile-video-item {\\n          margin-bottom: 10px !important;\\n          border-radius: 8px !important;\\n          overflow: hidden !important;\\n        }\\n\\n        .mobile-nav-buttons {\\n          position: fixed !important;\\n          left: 16px !important;\\n          top: 50% !important;\\n          transform: translateY(-50%) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-nav-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-nav-button:disabled {\\n          background: rgba(255, 255, 255, 0.3) !important;\\n          color: rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-interaction-buttons {\\n          position: fixed !important;\\n          right: 16px !important;\\n          bottom: calc(120px + env(safe-area-inset-bottom, 20px)) !important;\\n          z-index: 50 !important;\\n          display: flex !important;\\n          flex-direction: column !important;\\n          gap: 20px !important;\\n        }\\n\\n        .mobile-interaction-button {\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          color: white !important;\\n        }\\n\\n        .mobile-back-button {\\n          position: fixed !important;\\n          top: 20px !important;\\n          left: 16px !important;\\n          z-index: 50 !important;\\n          width: 48px !important;\\n          height: 48px !important;\\n          border-radius: 50% !important;\\n          background: rgba(255, 255, 255, 0.9) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border: none !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\\n        }\\n\\n        .mobile-user-info {\\n          position: absolute !important;\\n          top: 20px !important;\\n          left: 80px !important;\\n          right: 16px !important;\\n          z-index: 50 !important;\\n          background: rgba(0, 0, 0, 0.6) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 12px !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: space-between !important;\\n        }\\n\\n        .mobile-unlock-vendor {\\n          position: absolute !important;\\n          bottom: calc(20px + env(safe-area-inset-bottom, 10px)) !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n          background: #B31B1E !important;\\n          color: white !important;\\n          border: none !important;\\n          border-radius: 8px !important;\\n          padding: 12px 24px !important;\\n          font-weight: 600 !important;\\n          box-shadow: 0 4px 12px rgba(179, 27, 30, 0.4) !important;\\n          display: flex !important;\\n          align-items: center !important;\\n          justify-content: center !important;\\n        }\\n\\n        .mobile-vendor-details {\\n          position: absolute !important;\\n          bottom: 80px !important;\\n          left: 16px !important;\\n          right: 16px !important;\\n          z-index: 60 !important;\\n          background: rgba(255, 255, 255, 0.95) !important;\\n          backdrop-filter: blur(10px) !important;\\n          border-radius: 12px !important;\\n          padding: 16px !important;\\n          max-height: 50vh !important;\\n          overflow-y: auto !important;\\n          color: black !important;\\n        }\\n\\n        .mobile-progress-indicator {\\n          position: fixed !important;\\n          top: 80px !important;\\n          left: 50% !important;\\n          transform: translateX(-50%) !important;\\n          z-index: 50 !important;\\n        }\\n\\n        /* Hide desktop navigation buttons on mobile */\\n        .desktop-nav-buttons {\\n          display: none !important;\\n        }\\n\\n        .desktop-interaction-buttons {\\n          display: none !important;\\n        }\\n\\n        /* Ensure main content takes full space on mobile */\\n        .main-content-mobile {\\n          padding: 0 !important;\\n          padding-bottom: env(safe-area-inset-bottom, 20px) !important;\\n          margin: 0 !important;\\n          width: 100vw !important;\\n          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;\\n          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;\\n          position: relative !important;\\n        }\\n      }\\n\\n      /* Desktop styles */\\n      @media (min-width: 769px) {\\n        .mobile-nav-buttons,\\n        .mobile-interaction-buttons,\\n        .mobile-back-button,\\n        .mobile-user-info,\\n        .mobile-unlock-vendor,\\n        .mobile-progress-indicator {\\n          display: none !important;\\n        }\\n      }\\n    \";\n            document.head.appendChild(style);\n            return ({\n                \"FlashShortsContent.useEffect\": ()=>{\n                    document.head.removeChild(style);\n                }\n            })[\"FlashShortsContent.useEffect\"];\n        }\n    }[\"FlashShortsContent.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"opacity-0\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 809,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen w-full shorts-page\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.TopNavigation, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                    lineNumber: 816,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 815,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full h-[calc(100vh-80px)] md:mt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-sidebar \".concat(leftSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.SideNavigation, {\n                            expanded: leftSidebarExpanded,\n                            onExpand: ()=>setLeftSidebarExpanded(true),\n                            onCollapse: ()=>setLeftSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 821,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center px-4 md:px-4 px-0 relative main-content-mobile\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"absolute top-4 left-4 z-50 bg-white rounded-full p-3 text-black shadow-lg hover:bg-gray-200 transition-colors flex items-center justify-center hidden md:flex\",\n                                style: {\n                                    width: \"48px\",\n                                    height: \"48px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 836,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/home/<USER>"),\n                                className: \"mobile-back-button md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                    lineNumber: 849,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center w-full md:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-nav-buttons flex flex-col items-center justify-center space-y-4 mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === 0 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 856,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"p-3 rounded-full transition-all duration-200 \".concat(currentIndex === flashes.length - 1 ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl\"),\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-nav-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToPrevious,\n                                                disabled: currentIndex === 0,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Previous Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: navigateToNext,\n                                                disabled: currentIndex === flashes.length - 1,\n                                                className: \"mobile-nav-button\",\n                                                title: \"Next Flash\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: containerRef,\n                                        className: \"shorts-container relative w-full md:w-[400px] h-full md:h-[min(calc(100vh-100px),89vh)]\",\n                                        style: {\n                                            // Desktop styles\n                                            ... true && window.innerWidth >= 768 ? {\n                                                width: \"400px\",\n                                                height: \"min(calc(100vh - 100px), 89vh)\",\n                                                margin: \"0 auto\",\n                                                border: \"2px solid #B31B1E\",\n                                                borderRadius: \"12px\",\n                                                overflow: \"hidden\"\n                                            } : {\n                                                // Mobile styles - full screen with safe area\n                                                width: \"100vw\",\n                                                height: \"calc(100vh - env(safe-area-inset-bottom, 20px))\",\n                                                margin: \"0\",\n                                                border: \"none\",\n                                                borderRadius: \"0\",\n                                                overflow: \"hidden\",\n                                                paddingBottom: \"env(safe-area-inset-bottom, 20px)\"\n                                            }\n                                        },\n                                        children: [\n                                            loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-white\",\n                                                children: \"Loading flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 15\n                                            }, this),\n                                            error && !loading && flashes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center text-red-500\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 940,\n                                                columnNumber: 15\n                                            }, this),\n                                            flashes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full w-full transition-transform duration-300 ease-out\",\n                                                style: {\n                                                    transform: \"translateY(-\".concat(currentIndex * 102, \"%)\")\n                                                },\n                                                children: flashes.map((flash, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full w-full flex items-center justify-center relative bg-black \".concat( true && window.innerWidth < 768 ? 'mobile-video-item' : ''),\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(index * 102, \"%\"),\n                                                            left: 0,\n                                                            right: 0,\n                                                            bottom: 0,\n                                                            overflow: \"hidden\",\n                                                            borderRadius: \"8px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-full w-full relative overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 overflow-hidden\",\n                                                                    children: isYoutubeVideo(flash) ? // YouTube iframe for YouTube videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 979,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 980,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 978,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 977,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                                                src: \"https://www.youtube.com/embed/\".concat(getYoutubeId(flash.video_url), \"?autoplay=1&controls=0&rel=0&showinfo=0&mute=0\"),\n                                                                                title: flash.video_name,\n                                                                                className: \"shorts-video\",\n                                                                                allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                                                                allowFullScreen: true,\n                                                                                onLoad: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 984,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 974,\n                                                                        columnNumber: 27\n                                                                    }, this) : // Video player for Cloudfront videos\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full overflow-hidden\",\n                                                                        children: [\n                                                                            videoLoading[flash.video_id] !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center justify-center bg-black/50 z-20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1004,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-sm\",\n                                                                                            children: \"Loading video...\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1005,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1003,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1002,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                ref: (el)=>{\n                                                                                    videoRefs.current[flash.video_id] = el;\n                                                                                },\n                                                                                src: flash.video_url,\n                                                                                className: \"shorts-video\",\n                                                                                playsInline: true,\n                                                                                muted: false,\n                                                                                controls: false,\n                                                                                poster: getImageSource(flash),\n                                                                                onLoadStart: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: true\n                                                                                        }));\n                                                                                },\n                                                                                onCanPlay: ()=>{\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onError: ()=>{\n                                                                                    console.error(\"Failed to load video: \".concat(flash.video_name));\n                                                                                    setVideoLoading((prev)=>({\n                                                                                            ...prev,\n                                                                                            [flash.video_id]: false\n                                                                                        }));\n                                                                                },\n                                                                                onEnded: ()=>handleVideoEnded(flash.video_id)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1009,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    togglePlayPause(flash.video_id);\n                                                                                },\n                                                                                className: \"absolute inset-0 w-full h-full flex items-center justify-center z-10 group\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"\".concat(isPaused[flash.video_id] ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\", \" transition-opacity duration-200 bg-black/40 rounded-full p-4 shadow-lg\"),\n                                                                                    children: isPaused[flash.video_id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"white\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                                            points: \"5 3 19 12 5 21 5 3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                            lineNumber: 1058,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1047,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        width: \"32\",\n                                                                                        height: \"32\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"white\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"6\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1072,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                                x: \"14\",\n                                                                                                y: \"4\",\n                                                                                                width: \"4\",\n                                                                                                height: \"16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                                lineNumber: 1078,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1061,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                    lineNumber: 1039,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1032,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 999,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 971,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 970,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 left-4 right-4 z-20 flex items-center bg-black/20 backdrop-blur-sm rounded-lg p-2 hidden md:flex\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1096,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1107,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1108,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1106,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1095,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1172,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1113,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1094,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-user-info md:hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                username: flash.user_name || \"user\",\n                                                                                size: \"sm\",\n                                                                                isGradientBorder: true,\n                                                                                imageUrl: userAvatarPlaceholders[index % userAvatarPlaceholders.length]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1180,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-2 text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: flash.user_name || \"user\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1191,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs opacity-80\",\n                                                                                        children: [\n                                                                                            Math.floor(Math.random() * 2) + 0.1,\n                                                                                            \"M Admiring\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                        lineNumber: 1192,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1190,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1179,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            // Handle admire functionality\n                                                                            const userId = flash.user_id;\n                                                                            if (!userId) return;\n                                                                            // Check if already admiring this user\n                                                                            const isCurrentlyAdmiring = admiringUsers[userId] || false;\n                                                                            // Optimistically update UI state\n                                                                            setAdmiringUsers((prev)=>({\n                                                                                    ...prev,\n                                                                                    [userId]: !isCurrentlyAdmiring\n                                                                                }));\n                                                                            // Update localStorage\n                                                                            try {\n                                                                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';\n                                                                                const admiredUsers = JSON.parse(admiredUsersJson);\n                                                                                if (!isCurrentlyAdmiring) {\n                                                                                    admiredUsers[userId] = true;\n                                                                                } else {\n                                                                                    delete admiredUsers[userId];\n                                                                                }\n                                                                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));\n                                                                            } catch (error) {\n                                                                                console.error('Error updating localStorage:', error);\n                                                                            }\n                                                                            // Make API call in the background\n                                                                            const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token');\n                                                                            if (token) {\n                                                                                const endpoint = isCurrentlyAdmiring ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow' : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';\n                                                                                _services_axiosConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(endpoint, {\n                                                                                    target_user_id: userId\n                                                                                }, {\n                                                                                    headers: {\n                                                                                        Authorization: \"Bearer \".concat(token)\n                                                                                    }\n                                                                                }).catch((error)=>{\n                                                                                    console.error('Error with admire API call:', error);\n                                                                                    // Revert UI state on error\n                                                                                    setAdmiringUsers((prev)=>({\n                                                                                            ...prev,\n                                                                                            [userId]: isCurrentlyAdmiring\n                                                                                        }));\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            admiringUsers[flash.user_id || ''] ? 'Admiring' : 'Admire',\n                                                                            !admiringUsers[flash.user_id || ''] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-1\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1256,\n                                                                                columnNumber: 67\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1197,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-8 left-0 right-0 flex justify-center z-10 hidden md:flex\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-[#B31B1E] text-white text-sm font-medium px-4 py-2 rounded-md flex items-center\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowVendorDetails((prev)=>({\n                                                                                ...prev,\n                                                                                [flash.video_id]: !prev[flash.video_id]\n                                                                            }));\n                                                                    },\n                                                                    children: [\n                                                                        \"Unlock Vendor\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4 ml-1\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                                lineNumber: 1275,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1274,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1263,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1262,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            index === currentIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mobile-unlock-vendor md:hidden\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    setShowVendorDetails((prev)=>({\n                                                                            ...prev,\n                                                                            [flash.video_id]: !prev[flash.video_id]\n                                                                        }));\n                                                                },\n                                                                children: [\n                                                                    \"Unlock Vendor\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        className: \"h-4 w-4 ml-1\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                            lineNumber: 1294,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                        lineNumber: 1293,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1282,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-20 left-4 right-4 p-4 bg-white/90 rounded-lg text-black max-h-[40%] overflow-y-auto z-30 hidden md:block\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1302,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1301,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === currentIndex && showVendorDetails[flash.video_id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mobile-vendor-details md:hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashVendorDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    videoId: flash.video_id,\n                                                                    isVerified: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1311,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(flash.video_id, \"-\").concat(index), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 15\n                                            }, this),\n                                            loading && page > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-20 left-0 right-0 text-center text-white bg-black/50 py-2 mx-auto w-48 rounded-full backdrop-blur-sm z-20\",\n                                                children: \"Loading more flashes...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-14 left-0 right-0 px-4 z-20 hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1334,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1342,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1332,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1331,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mobile-progress-indicator md:hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 justify-center\",\n                                                    children: [\n                                                        flashes.slice(0, Math.min(flashes.length, 10)).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-1 rounded-full \".concat(i === currentIndex ? \"bg-white w-6\" : \"bg-white/40 w-3\", \" transition-all duration-200\")\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                                lineNumber: 1351,\n                                                                columnNumber: 19\n                                                            }, this)),\n                                                        flashes.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-1 rounded-full bg-white/40 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1359,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1349,\n                                                    columnNumber: 15\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"desktop-interaction-buttons flex flex-col items-center justify-center space-y-6 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100 mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1370,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1371,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1369,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1368,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex = flashes[currentIndex]) === null || _flashes_currentIndex === void 0 ? void 0 : _flashes_currentIndex.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex1 = flashes[currentIndex]) === null || _flashes_currentIndex1 === void 0 ? void 0 : _flashes_currentIndex1.video_id) || '') ? \"#B31B1E\" : \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1386,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1377,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1394,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1393,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 opacity-80 hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1401,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1402,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1400,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1399,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1366,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-interaction-buttons md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash === null || currentFlash === void 0 ? void 0 : currentFlash.user_id) {\n                                                        // Navigate to user profile - you can implement this navigation\n                                                        console.log('Navigate to profile:', currentFlash.user_id);\n                                                    // router.push(`/profile/${currentFlash.user_id}`);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1422,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"7\",\n                                                            r: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1423,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1410,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                onClick: ()=>{\n                                                    const currentFlash = flashes[currentIndex];\n                                                    if (currentFlash) {\n                                                        toggleLike(currentFlash.video_id);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: likedFlashes.has(((_flashes_currentIndex2 = flashes[currentIndex]) === null || _flashes_currentIndex2 === void 0 ? void 0 : _flashes_currentIndex2.video_id) || '') ? \"#B31B1E\" : \"none\",\n                                                    stroke: likedFlashes.has(((_flashes_currentIndex3 = flashes[currentIndex]) === null || _flashes_currentIndex3 === void 0 ? void 0 : _flashes_currentIndex3.video_id) || '') ? \"#B31B1E\" : \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1438,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1437,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1428,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                        lineNumber: 1445,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1444,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1443,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                            x1: \"22\",\n                                                            y1: \"2\",\n                                                            x2: \"11\",\n                                                            y2: \"13\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1452,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                            points: \"22 2 15 22 11 13 2 9 22 2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1453,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1451,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"mobile-interaction-button\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1460,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"5\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1461,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"19\",\n                                                            r: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                            lineNumber: 1462,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                    lineNumber: 1459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                                lineNumber: 1458,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                                lineNumber: 852,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 834,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-sidebar \".concat(rightSidebarExpanded ? \"expanded\" : \"\", \" hidden md:block\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_6__.RightSidebar, {\n                            expanded: rightSidebarExpanded,\n                            onExpand: ()=>setRightSidebarExpanded(true),\n                            onCollapse: ()=>setRightSidebarExpanded(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                            lineNumber: 1475,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                        lineNumber: 1470,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n                lineNumber: 819,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 813,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashShortsContent, \"aeQpwdVKjXsnvNtC+OoDi2XkRdk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = FlashShortsContent;\n// Loading fallback component\nfunction FlashShortsLoading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-screen w-full bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-white text-xl\",\n            children: \"Loading flashes...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1490,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1489,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FlashShortsLoading;\n// Main page component with Suspense\nfunction FlashShortsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsLoading, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1498,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashShortsContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n            lineNumber: 1499,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\flashes\\\\shorts\\\\page.tsx\",\n        lineNumber: 1498,\n        columnNumber: 5\n    }, this);\n}\n_c2 = FlashShortsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FlashShortsContent\");\n$RefreshReg$(_c1, \"FlashShortsLoading\");\n$RefreshReg$(_c2, \"FlashShortsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/shorts/page.tsx\n"));

/***/ })

});