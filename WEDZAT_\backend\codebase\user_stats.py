import os
import json
from datetime import datetime, date
import jwt
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME,
        cursor_factory=psycopg2.extras.DictCursor  # Return results as dictionaries
    )

# Custom JSON encoder to handle date objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        if token.startswith('Bearer '):
            token = token[7:]  # Remove 'Bearer ' prefix
        else:
            # Fallback to old method if format is different
            token = token.split()[1]

        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

def follow_user(event):
    conn = None
    cursor = None
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error

        # Parse request body
        data = json.loads(event['body'])
        target_user_id = data.get('target_user_id')

        if not target_user_id:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Target user ID is required"})
            }

        # Check if user is trying to follow themselves
        if user_id == target_user_id:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Cannot follow yourself"})
            }

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if target user exists
        cursor.execute("SELECT user_id FROM users WHERE user_id = %s", (target_user_id,))
        if not cursor.fetchone():
            return {
                'statusCode': 404,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Target user not found"})
            }

        try:
            cursor.execute("BEGIN")

            # Check if user_stats entries exist, create if they don't
            cursor.execute(
                "INSERT INTO user_stats (user_id, following, followers) VALUES (%s, '[]', '[]') ON CONFLICT DO NOTHING",
                (user_id,)
            )
            cursor.execute(
                "INSERT INTO user_stats (user_id, following, followers) VALUES (%s, '[]', '[]') ON CONFLICT DO NOTHING",
                (target_user_id,)
            )

            # Get current following for user and followers for target
            cursor.execute("SELECT following FROM user_stats WHERE user_id = %s", (user_id,))
            result = cursor.fetchone()

            # Handle both string and list formats for following
            if result and result['following']:
                if isinstance(result['following'], str):
                    following = json.loads(result['following'])
                else:
                    following = result['following']
            else:
                following = []

            cursor.execute("SELECT followers FROM user_stats WHERE user_id = %s", (target_user_id,))
            result = cursor.fetchone()

            # Handle both string and list formats for followers
            if result and result['followers']:
                if isinstance(result['followers'], str):
                    followers = json.loads(result['followers'])
                else:
                    followers = result['followers']
            else:
                followers = []

            # Check if already following
            if target_user_id in following:
                return {
                    'statusCode': 400,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Already following this user"})
                }

            # Update following and followers
            following.append(target_user_id)
            followers.append(user_id)

            cursor.execute(
                "UPDATE user_stats SET following = %s, updated_at = NOW() WHERE user_id = %s",
                (json.dumps(following), user_id)
            )
            cursor.execute(
                "UPDATE user_stats SET followers = %s, updated_at = NOW() WHERE user_id = %s",
                (json.dumps(followers), target_user_id)
            )

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"message": "Successfully followed user"})
            }

        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def unfollow_user(event):
    conn = None
    cursor = None
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error

        # Parse request body
        data = json.loads(event['body'])
        target_user_id = data.get('target_user_id')

        if not target_user_id:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Target user ID is required"})
            }

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("BEGIN")

            # Get current following for user and followers for target
            cursor.execute("SELECT following FROM user_stats WHERE user_id = %s", (user_id,))
            result = cursor.fetchone()
            if not result:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User stats not found"})
                }
            # Handle both string and list formats for following
            if result['following']:
                if isinstance(result['following'], str):
                    following = json.loads(result['following'])
                else:
                    following = result['following']
            else:
                following = []

            cursor.execute("SELECT followers FROM user_stats WHERE user_id = %s", (target_user_id,))
            result = cursor.fetchone()
            if not result:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Target user stats not found"})
                }

            # Handle both string and list formats for followers
            if result['followers']:
                if isinstance(result['followers'], str):
                    followers = json.loads(result['followers'])
                else:
                    followers = result['followers']
            else:
                followers = []

            # Check if actually following
            if target_user_id not in following:
                return {
                    'statusCode': 400,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Not following this user"})
                }

            # Update following and followers
            following.remove(target_user_id)
            if user_id in followers:
                followers.remove(user_id)

            cursor.execute(
                "UPDATE user_stats SET following = %s, updated_at = NOW() WHERE user_id = %s",
                (json.dumps(following), user_id)
            )
            cursor.execute(
                "UPDATE user_stats SET followers = %s, updated_at = NOW() WHERE user_id = %s",
                (json.dumps(followers), target_user_id)
            )

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"message": "Successfully unfollowed user"})
            }

        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_user_follow_stats(event):
    conn = None
    cursor = None
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Check if a specific user's stats are requested
        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', user_id)

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if user exists
        cursor.execute("SELECT user_id FROM users WHERE user_id = %s", (target_user_id,))
        if not cursor.fetchone():
            return {
                'statusCode': 404,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "User not found"})
            }

        # Get user stats
        cursor.execute("SELECT following, followers FROM user_stats WHERE user_id = %s", (target_user_id,))
        result = cursor.fetchone()

        if not result:
            # Create empty stats if they don't exist
            cursor.execute(
                "INSERT INTO user_stats (user_id, following, followers) VALUES (%s, '[]', '[]') RETURNING following, followers",
                (target_user_id,)
            )
            result = cursor.fetchone()

        # Handle both string and list formats for followers and following
        if result['following']:
            if isinstance(result['following'], str):
                following = json.loads(result['following'])
            else:
                following = result['following']
        else:
            following = []

        if result['followers']:
            if isinstance(result['followers'], str):
                followers = json.loads(result['followers'])
            else:
                followers = result['followers']
        else:
            followers = []

        # Get basic info for following and followers
        following_users = []
        if following:
            placeholders = ', '.join(['%s'] * len(following))
            query = f"SELECT user_id, name FROM users WHERE user_id IN ({placeholders})"
            cursor.execute(query, following)
            following_users = [{"user_id": str(row[0]), "name": row[1]} for row in cursor.fetchall()]

        follower_users = []
        if followers:
            placeholders = ', '.join(['%s'] * len(followers))
            query = f"SELECT user_id, name FROM users WHERE user_id IN ({placeholders})"
            cursor.execute(query, followers)
            follower_users = [{"user_id": str(row[0]), "name": row[1]} for row in cursor.fetchall()]

        # Check if the requesting user is following the target
        is_following = None
        if target_user_id != user_id:
            cursor.execute("SELECT following FROM user_stats WHERE user_id = %s", (user_id,))
            result = cursor.fetchone()
            if result and result['following']:
                user_following = json.loads(result['following'])
                is_following = target_user_id in user_following
            else:
                is_following = False

        return {
            'statusCode': 200,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "user_id": target_user_id,
                "following_count": len(following),
                "followers_count": len(followers),
                "following": following_users,
                "followers": follower_users,
                "is_following": is_following
            }, cls=CustomJSONEncoder)
        }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_user_profile(event):
    """
    Get a user's profile information including their stats and content
    """
    conn = None
    cursor = None
    try:
        # Validate token
        current_user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get target user_id from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', current_user_id)

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        try:
            # Get user details
            cursor.execute("""
                SELECT
                    user_id, name, email, mobile_number, dob, marital_status, place, user_type,
                    user_avatar, bio, user_short_audio, chapters_of_love, face_verified, face_image_url
                FROM users
                WHERE user_id = %s
            """, (target_user_id,))

            user = cursor.fetchone()

            if not user:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }

            # Convert user to dict
            user_dict = dict(user)

            # Parse JSON fields
            user_dict['chapters_of_love'] = json.loads(user_dict['chapters_of_love']) if user_dict['chapters_of_love'] else []

            # Get follower and following stats
            cursor.execute("""
                SELECT followers, following
                FROM user_stats
                WHERE user_id = %s
            """, (target_user_id,))

            stats = cursor.fetchone()

            if stats:
                # Handle both string and list formats for followers and following
                if stats['followers']:
                    if isinstance(stats['followers'], str):
                        followers = json.loads(stats['followers'])
                    else:
                        followers = stats['followers']
                else:
                    followers = []

                if stats['following']:
                    if isinstance(stats['following'], str):
                        following = json.loads(stats['following'])
                    else:
                        following = stats['following']
                else:
                    following = []

                user_dict['followers_count'] = len(followers)
                user_dict['following_count'] = len(following)
            else:
                user_dict['followers_count'] = 0
                user_dict['following_count'] = 0

            # Check if current user is following target user
            if current_user_id != target_user_id:
                cursor.execute("""
                    SELECT following
                    FROM user_stats
                    WHERE user_id = %s
                """, (current_user_id,))

                current_user_following = cursor.fetchone()

                if current_user_following and current_user_following['following']:
                    # Handle both string and list formats for following
                    if isinstance(current_user_following['following'], str):
                        following_list = json.loads(current_user_following['following'])
                    else:
                        following_list = current_user_following['following']
                    user_dict['is_following'] = target_user_id in following_list
                else:
                    user_dict['is_following'] = False

            # Count posts by type
            cursor.execute("""
                SELECT
                    (SELECT COUNT(*) FROM videos WHERE user_id = %s AND video_subtype = 'flash') as flash_count,
                    (SELECT COUNT(*) FROM videos WHERE user_id = %s AND video_subtype = 'glimpse') as glimpse_count,
                    (SELECT COUNT(*) FROM videos WHERE user_id = %s AND video_subtype = 'movie') as movie_count,
                    (SELECT COUNT(*) FROM photos WHERE user_id = %s AND photo_subtype = 'post') as photo_count
                """, (target_user_id, target_user_id, target_user_id, target_user_id))

            counts = cursor.fetchone()

            if counts:
                user_dict['flash_count'] = counts['flash_count'] or 0
                user_dict['glimpse_count'] = counts['glimpse_count'] or 0
                user_dict['movie_count'] = counts['movie_count'] or 0
                user_dict['photo_count'] = counts['photo_count'] or 0
                user_dict['posts_count'] = (counts['flash_count'] or 0) + (counts['glimpse_count'] or 0) + (counts['movie_count'] or 0) + (counts['photo_count'] or 0)
            else:
                user_dict['flash_count'] = 0
                user_dict['glimpse_count'] = 0
                user_dict['movie_count'] = 0
                user_dict['photo_count'] = 0
                user_dict['posts_count'] = 0

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(user_dict, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def update_user(event):
    conn = None
    cursor = None
    try:
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        try:
            update_data = json.loads(event['body'])
            name = update_data.get('name')
            email = update_data.get('email')
            mobile_number = update_data.get('mobile_number')
            dob = update_data.get('dob')
            marital_status = update_data.get('marital_status')
            place = update_data.get('place')

            # Get new fields
            user_avatar = update_data.get('user_avatar')
            bio = update_data.get('bio')
            user_short_audio = update_data.get('user_short_audio')
            chapters_of_love = update_data.get('chapters_of_love')

            # Convert chapters_of_love to JSON string if it's provided
            if chapters_of_love is not None:
                chapters_of_love = json.dumps(chapters_of_love)

            email = email.lower() if email else None

            if not (name and email):
                return {
                    'statusCode': 400,
                    "headers": {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Name and Email are required"})
                }

            # Check for existing email or mobile number in other accounts
            if mobile_number:
                cursor.execute(
                    '''SELECT user_id FROM users WHERE (LOWER(email) = LOWER(%s) OR mobile_number = %s) AND user_id != %s''',
                    (email, mobile_number, user_id)
                )
            else:
                cursor.execute(
                    '''SELECT user_id FROM users WHERE LOWER(email) = LOWER(%s) AND user_id != %s''',
                    (email, user_id)
                )

            existing_user = cursor.fetchone()

            if existing_user:
                return {
                    'statusCode': 400,
                    "headers": {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Email or phone number already exists"})
                }

            # Update user details including new fields
            cursor.execute(
                '''UPDATE users
                   SET name = %s, email = %s, mobile_number = %s, dob = %s, marital_status = %s, place = %s,
                       user_avatar = COALESCE(%s, user_avatar),
                       bio = COALESCE(%s, bio),
                       user_short_audio = COALESCE(%s, user_short_audio),
                       chapters_of_love = COALESCE(%s, chapters_of_love)
                   WHERE user_id = %s''',
                (name, email, mobile_number, dob, marital_status, place,
                 user_avatar, bio, user_short_audio, chapters_of_love, user_id)
            )
            conn.commit()

            return {
                'statusCode': 200,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"message": "User updated successfully"})
            }

        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
    except Exception as e:
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

